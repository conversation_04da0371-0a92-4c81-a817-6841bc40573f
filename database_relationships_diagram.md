# Database Relationships Diagram

## Tables and Their Relationships

```mermaid
erDiagram
    User {
        string id PK
        string email
    }

    Person {
        string id PK
        string user_id FK
        string first_name
        string last_name
    }

    Establishment {
        string id PK
    }

    Customer {
        string id PK
        string establishment_id FK
        string person_id FK
    }

    Participant {
        string id PK
        string booking_id FK
        string customer_id FK
    }

    Booking {
        string id PK
        string establishment_id FK
    }

    Rentable {
        string id PK
        string establishment_id FK
    }

    RentalAssignment {
        string id PK
        string participant_id FK
        string rentable_id FK
    }

    %% Relationships
    User ||--o{ Person : "has one"

    Establishment ||--o{ Customer : "has many"
    Establishment ||--o{ Booking : "has many"
    Establishment ||--o{ Rentable : "has many"

    Person ||--o{ Customer : "has many"

    Customer ||--o{ Participant : "has many"

    Booking ||--o{ Participant : "has many"

    Participant ||--o{ RentalAssignment : "has many"

    Rentable ||--o{ RentalAssignment : "has many"
```

## Key Relationships Explained

### 1. User → Person (1:1)

- **Relationship**: One user can have one person record
- **Foreign Key**: `Person.user_id` → `User.id`
- **Purpose**: Links user accounts to their personal information

### 2. Person → Customer (1:N)

- **Relationship**: One person can be a customer at multiple establishments
- **Foreign Key**: `Customer.person_id` → `Person.id`
- **Purpose**: Links customers to their personal information

### 3. Establishment → Customer (1:N)

- **Relationship**: One establishment can have many customers
- **Foreign Key**: `Customer.establishment_id` → `Establishment.id`
- **Purpose**: Links customers to the establishment they belong to

### 4. Establishment → Booking (1:N)

- **Relationship**: One establishment can have many bookings
- **Foreign Key**: `Booking.establishment_id` → `Establishment.id`
- **Purpose**: Links bookings to the establishment where they were made

### 5. Establishment → Rentable (1:N)

- **Relationship**: One establishment can have many rentable items
- **Foreign Key**: `Rentable.establishment_id` → `Establishment.id`
- **Purpose**: Links rentable equipment/items to the establishment that owns them

### 6. Customer → Participant (1:N)

- **Relationship**: One customer can have many participants
- **Foreign Key**: `Participant.customer_id` → `Customer.id`
- **Purpose**: Links participants to their customer record

### 7. Booking → Participant (1:N)

- **Relationship**: One booking can have many participants
- **Foreign Key**: `Participant.booking_id` → `Booking.id`
- **Purpose**: Links participants to the specific booking they're part of

### 8. Participant → RentalAssignment (1:N)

- **Relationship**: One participant can have many rental assignments
- **Foreign Key**: `RentalAssignment.participant_id` → `Participant.id`
- **Purpose**: Links rental assignments to the participant who rented the item

### 9. Rentable → RentalAssignment (1:N)

- **Relationship**: One rentable item can have many rental assignments
- **Foreign Key**: `RentalAssignment.rentable_id` → `Rentable.id`
- **Purpose**: Links rental assignments to the specific rentable item

## Business Logic Flow

1. **User** represents system users with email authentication
2. **Person** contains personal information (first_name, last_name) linked to a user account
3. **Establishment** is the central entity that owns customers, bookings, and rentable items
4. **Customer** links a person to an establishment, allowing the same person to be a customer at multiple establishments
5. **Bookings** are made at an establishment and can include multiple participants
6. **Participants** are linked to both a customer and a booking
7. **Rentable** items belong to an establishment and can be rented by participants
8. **RentalAssignment** connects participants to the specific rentable items they've rented on a specific date

This structure supports a diving/tourism business model where establishments offer activities, equipment rentals, and manage bookings with multiple participants. The User/Person separation allows for flexible customer management across multiple establishments.
