# Form-Based API Documentation

## Overview

This is a custom form-based API system that provides GraphQL-like functionality for database operations through HTML forms. It's designed to be type-safe, with strict typing based on the database schema, and supports complex nested operations in a single request.

## Architecture Overview

The system consists of several key components:

1. **Database Schema** (`db.ts`) - Defines all table structures and relationships
2. **Resource Inputs** (`RInput`) - Type-safe form components that generate the correct field names
3. **Form Processing** (`formdata-to-nested-json.ts`) - Converts form data to structured JSON
4. **Resource Server** (`resource.server.ts`) - Defines authorization and business logic for each table
5. **API Endpoint** (`_all._catch.resource.tsx`) - <PERSON><PERSON> form submissions and orchestrates operations

## How It Works

### 1. Form Data Structure

Forms submit data using a specific naming convention that allows for complex nested operations:

```
{table}.{index}.{field}.{subfield} = {value}
{table}.{index}.operation = {insert|update|delete|ignore}
```

**Examples:**

```
participant.0.data.first_name = "<PERSON>"
participant.0.data.last_name = "Doe"
participant.0.operation = "insert"
participant.1.id = "existing-uuid"
participant.1.data.phone = "+1234567890"
participant.1.operation = "update"
```

### 2. Field Naming Convention

The `fName()` helper function generates field names with the following structure:

```typescript
fName(table, field, index, ...additionalKeys);
```

**Examples:**

```typescript
fName("participant", "data.first_name", 0); // "00000-participant-0.participant.data.first_name"
fName("booking", "data.date", "booking-123"); // "00000-booking-booking-123.booking.data.date"
```

### 3. Data Transformation

The system supports various field types for data transformation:

- `__boolean__` - Converts string values to boolean
- `__pg_int_range__` - Creates PostgreSQL integer ranges
- `__pg_coordinates__` - Creates PostGIS Point objects
- `__pg_daterange__` - Creates PostgreSQL date ranges
- `__sum__` - Sums array values
- `__join__` - Joins array values into a string
- `__to_string__` - Converts objects to JSON strings

## API Reference

### Endpoint

**POST** `/resource`

### Request Format

The API accepts `application/x-www-form-urlencoded` data with the following structure:

#### Basic Fields

| Field                          | Type   | Description                                            |
| ------------------------------ | ------ | ------------------------------------------------------ |
| `{table}.{index}.id`           | string | Entity ID (for updates/deletes)                        |
| `{table}.{index}.operation`    | string | Operation type: `insert`, `update`, `delete`, `ignore` |
| `{table}.{index}.data.{field}` | any    | Field value                                            |
| `{table}.{index}.data.{field}` | string | Field type (e.g., `__boolean__`)                       |

#### Special Fields

| Field                 | Type   | Description                           |
| --------------------- | ------ | ------------------------------------- |
| `redirect`            | string | Success redirect URL                  |
| `redirect_error`      | string | Error redirect URL                    |
| `identifier`          | string | Form identifier                       |
| `response_identifier` | string | Response identifier                   |
| `captcha_token`       | string | Google reCAPTCHA token                |
| `start`               | string | Form start timestamp (bot prevention) |

### Response Format

#### Success Response

```json
{
  "results": [
    {
      "id": "uuid",
      "entity_name": "participant",
      "operation": "insert",
      "result": "uuid"
    }
  ],
  "init": {
    "session_id": "uuid"
  }
}
```

#### Error Response

```json
{
  "response_error": "error_message",
  "response_identifier": "form_id",
  "response_form_id": "form_id"
}
```

## Available Tables and Operations

### Core Tables

#### `participant`

- **Fields**: `first_name`, `last_name`, `phone`, `email`, `birth_date`, `country_code`, etc.
- **Operations**: `insert`, `update`, `delete`
- **Authorization**: User must be admin of the establishment

#### `booking`

- **Fields**: `date`, `establishment_id`, `currency_id`, `message`, etc.
- **Operations**: `insert`, `update`, `delete`
- **Authorization**: User must be admin of the establishment

#### `product`

- **Fields**: `name`, `description`, `price`, `category_id`, etc.
- **Operations**: `insert`, `update`, `delete`
- **Authorization**: User must be admin of the establishment

#### `establishment`

- **Fields**: `name`, `address`, `email`, `phone`, `website`, etc.
- **Operations**: `insert`, `update`, `delete`
- **Authorization**: User must be editor

### Relationship Tables

#### `product__tag`

- **Fields**: `product_id`, `tag_id`
- **Operations**: `insert`, `update`, `delete`
- **Authorization**: User must be admin of the establishment

#### `participant_waiver`

- **Fields**: `participant_id`, `waiver_id`, `signed_at`, etc.
- **Operations**: `insert`, `update`, `delete`
- **Authorization**: User must be admin of the establishment

## Usage Examples

### Creating a New Participant

```html
<form method="post" action="/resource">
  <input type="hidden" name="participant.0.operation" value="insert" />
  <input type="text" name="participant.0.data.first_name" required />
  <input type="text" name="participant.0.data.last_name" required />
  <input type="email" name="participant.0.data.email" required />
  <input type="hidden" name="redirect" value="/success" />
  <input type="hidden" name="redirect_error" value="/error" />
  <button type="submit">Create Participant</button>
</form>
```

### Updating Multiple Records

```html
<form method="post" action="/resource">
  <!-- Update existing participant -->
  <input type="hidden" name="participant.0.id" value="existing-uuid" />
  <input type="hidden" name="participant.0.operation" value="update" />
  <input type="text" name="participant.0.data.phone" value="+1234567890" />

  <!-- Create new booking -->
  <input type="hidden" name="booking.0.operation" value="insert" />
  <input type="date" name="booking.0.data.date" required />
  <input type="hidden" name="booking.0.data.establishment_id" value="establishment-uuid" />

  <button type="submit">Update</button>
</form>
```

### Complex Nested Operations

```html
<form method="post" action="/resource">
  <!-- Create participant with waiver -->
  <input type="hidden" name="participant.0.operation" value="insert" />
  <input type="text" name="participant.0.data.first_name" required />
  <input type="text" name="participant.0.data.last_name" required />

  <!-- Create waiver for participant -->
  <input type="hidden" name="participant_waiver.0.operation" value="insert" />
  <input type="hidden" name="participant_waiver.0.data.participant_id" value="ref-participant-0" />
  <input type="hidden" name="participant_waiver.0.data.waiver_id" value="waiver-uuid" />
  <input type="hidden" name="participant_waiver.0.data.signed_at" value="2024-01-01" />

  <button type="submit">Submit</button>
</form>
```

### Using Field Types

```html
<form method="post" action="/resource">
  <!-- Boolean field -->
  <input type="checkbox" name="participant.0.data.allow_contact" value="true" />
  <input type="hidden" name="participant.0.data.allow_contact" value="__boolean__" />

  <!-- Date range -->
  <input type="date" name="booking.0.data.date_range.from" />
  <input type="date" name="booking.0.data.date_range.to" />
  <input type="hidden" name="booking.0.data.date_range" value="__pg_daterange__" />

  <!-- Coordinates -->
  <input type="text" name="address.0.data.coordinates" value="40.7128,-74.0060" />
  <input type="hidden" name="address.0.data.coordinates" value="__pg_coordinates__" />

  <button type="submit">Submit</button>
</form>
```

## React Components

### RInput Component

The `RInput` component provides type-safe form inputs:

```tsx
import { RInput } from "~/components/ResourceInputs";

// Basic usage
<RInput
  table="participant"
  field="data.first_name"
  index={0}
  label="First Name"
  required
/>

// With field type
<RInput
  table="participant"
  field="data.allow_contact"
  index={0}
  type="checkbox"
  hiddenType="__boolean__"
/>

// With validation
<RInput
  table="participant"
  field="data.email"
  index={0}
  type="email"
  required
  pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
/>
```

### OperationInput Component

```tsx
import { OperationInput } from "~/components/form/DefaultInput";

<OperationInput table="participant" index={0} value="insert" />;
```

### DeleteInputs Component

```tsx
import { DeleteInputs } from "~/components/ResourceInputs";

<DeleteInputs table="participant" ids={["uuid1", "uuid2"]} />;
```

## Authorization

Each table has its own authorization rules defined in `resource.server.ts`:

### Common Authorization Patterns

1. **Editor Access**: User must have editor privileges
2. **Establishment Admin**: User must be admin of the specific establishment
3. **Owner Access**: User must own the resource
4. **Public Access**: No authorization required

### Custom Authorization

```typescript
const customResource: Args<"custom_table"> = {
  authorize: async (args) => {
    // Custom authorization logic
    const user = await getUser(args.ctx.session_id);
    return user.hasPermission("custom_permission");
  },
  insert: (args) => args.data,
  update: (args) => args.data,
  delete: () => true,
};
```

## Error Handling

### Common Error Types

1. **Authorization Errors**: User doesn't have permission
2. **Validation Errors**: Invalid data format
3. **Reference Errors**: Invalid foreign key references
4. **Business Logic Errors**: Custom business rule violations

### Error Response Format

```json
{
  "response_error": "unauthorized for insert on participant",
  "response_identifier": "form-123",
  "response_form_id": "form-123"
}
```

## Best Practices

### 1. Use Type-Safe Components

Always use `RInput` instead of regular HTML inputs to ensure type safety:

```tsx
// Good
<RInput table="participant" field="data.first_name" index={0} />

// Avoid
<input name="participant.0.data.first_name" />
```

### 2. Handle References Properly

Use the reference system for relationships:

```tsx
// Create participant and booking with relationship
<RInput table="participant" field="id" index={0} value="ref-participant-0" />
<RInput table="booking" field="data.participant_id" index={0} value="ref-participant-0" />
```

### 3. Use Appropriate Field Types

```tsx
// Boolean fields
<RInput table="participant" field="data.active" hiddenType="__boolean__" />

// Date ranges
<RInput table="booking" field="data.date_range" hiddenType="__pg_daterange__" />
```

### 4. Implement Proper Authorization

Always check if the current user has permission to perform the operation:

```typescript
const resource: Args<"table"> = {
  authorize: (args) => memberIsAdminQb(args).executeTakeFirst(),
  // ... other methods
};
```

### 5. Handle Form State

Use the form identifier system for proper state management:

```tsx
<ActionForm identifier="participant-form">{/* form content */}</ActionForm>
```

## Security Considerations

1. **CSRF Protection**: Forms include built-in CSRF protection
2. **Input Validation**: All inputs are validated against the database schema
3. **Authorization**: Every operation requires proper authorization
4. **Rate Limiting**: Consider implementing rate limiting for public endpoints
5. **Bot Prevention**: The system includes basic bot prevention mechanisms

## Performance Considerations

1. **Batch Operations**: Multiple operations in a single request are more efficient
2. **Transaction Safety**: All operations in a single request are wrapped in a transaction
3. **Audit Trail**: All operations are automatically audited
4. **Caching**: Consider caching frequently accessed data

## Troubleshooting

### Common Issues

1. **Type Errors**: Ensure field names match the database schema exactly
2. **Authorization Errors**: Check user permissions and establishment membership
3. **Reference Errors**: Verify that referenced entities exist and are accessible
4. **Validation Errors**: Check data format and required fields

### Debugging

1. Check browser network tab for request/response details
2. Review server logs for detailed error messages
3. Use the `console.log` statements in the code for debugging
4. Verify form data structure matches expected format

## Migration from REST APIs

If migrating from REST APIs:

1. **Identify Resources**: Map REST endpoints to database tables
2. **Define Authorization**: Implement appropriate authorization rules
3. **Update Forms**: Replace AJAX calls with form submissions
4. **Handle Responses**: Update client-side code to handle form responses
5. **Test Thoroughly**: Ensure all operations work as expected

## Conclusion

This form-based API system provides a powerful, type-safe way to perform database operations through HTML forms. It combines the simplicity of form submissions with the flexibility of complex nested operations, making it ideal for building rich web applications with minimal client-side JavaScript.
