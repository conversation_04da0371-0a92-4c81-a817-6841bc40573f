# Developer Quick Reference

## Quick Start

### 1. Basic Form Structure

```html
<form method="post" action="/resource">
  <!-- Operation -->
  <input type="hidden" name="participant.0.operation" value="insert" />

  <!-- Data fields -->
  <input type="text" name="participant.0.data.first_name" required />
  <input type="text" name="participant.0.data.last_name" required />

  <!-- Redirect URLs -->
  <input type="hidden" name="redirect" value="/success" />
  <input type="hidden" name="redirect_error" value="/error" />

  <button type="submit">Submit</button>
</form>
```

### 2. React Component Usage

```tsx
import { RInput, OperationInput } from "~/components/ResourceInputs";

<ActionForm identifier="my-form">
  <OperationInput table="participant" index={0} value="insert" />
  <RInput table="participant" field="data.first_name" index={0} required />
  <RInput table="participant" field="data.last_name" index={0} required />
  <SubmitButton>Create Participant</SubmitButton>
</ActionForm>;
```

## Field Naming Patterns

| Pattern                        | Example                         | Description             |
| ------------------------------ | ------------------------------- | ----------------------- |
| `{table}.{index}.operation`    | `participant.0.operation`       | Operation type          |
| `{table}.{index}.id`           | `participant.0.id`              | Entity ID (for updates) |
| `{table}.{index}.data.{field}` | `participant.0.data.first_name` | Field value             |
| `{table}.{index}.data.{field}` | `participant.0.data.active`     | Field type              |

## Operations

| Operation | Description            | When to Use            |
| --------- | ---------------------- | ---------------------- |
| `insert`  | Create new record      | New data               |
| `update`  | Modify existing record | Existing data with ID  |
| `delete`  | Remove record          | Existing data with ID  |
| `ignore`  | Skip operation         | Conditional operations |

## Field Types

| Type                 | Usage           | Example                                |
| -------------------- | --------------- | -------------------------------------- |
| `__boolean__`        | Checkboxes      | `<input type="checkbox" value="true">` |
| `__pg_int_range__`   | Number ranges   | `from: 5, to: 30`                      |
| `__pg_daterange__`   | Date ranges     | `from: 2024-01-01, to: 2024-01-07`     |
| `__pg_coordinates__` | GPS coordinates | `"40.7128,-74.0060"`                   |
| `__sum__`            | Sum arrays      | `[1, 2, 3]` → `6`                      |
| `__join__`           | Join arrays     | `["a", "b"]` → `"ab"`                  |
| `__to_string__`      | Object to JSON  | `{key: "value"}` → `'{"key":"value"}'` |
| `__empty_array__`    | Empty arrays    | `[]`                                   |

## Common Patterns

### 1. Create with References

```html
<!-- Create participant -->
<input type="hidden" name="participant.0.operation" value="insert" />
<input type="text" name="participant.0.data.first_name" value="John" />

<!-- Create booking referencing participant -->
<input type="hidden" name="booking.0.operation" value="insert" />
<input type="hidden" name="booking.0.data.participant_id" value="ref-participant-0" />
<input type="date" name="booking.0.data.date" value="2024-01-01" />
```

### 2. Update Existing Record

```html
<input type="hidden" name="participant.0.id" value="existing-uuid" />
<input type="hidden" name="participant.0.operation" value="update" />
<input type="text" name="participant.0.data.phone" value="+1234567890" />
```

### 3. Delete Records

```html
<input type="hidden" name="participant.0.id" value="uuid-to-delete" /> <input type="hidden" name="participant.0.operation" value="delete" />
```

### 4. Boolean Fields

```html
<input type="checkbox" name="participant.0.data.active" value="true" />
<input type="hidden" name="participant.0.data.active" value="__boolean__" />
```

### 5. Date Ranges

```html
<input type="date" name="booking.0.data.date_range.from" value="2024-01-01" />
<input type="date" name="booking.0.data.date_range.to" value="2024-01-07" />
<input type="hidden" name="booking.0.data.date_range" value="__pg_daterange__" />
```

## React Components Cheat Sheet

### RInput Component

```tsx
// Basic input
<RInput table="participant" field="data.first_name" index={0} />

// With label and validation
<RInput
  table="participant"
  field="data.email"
  index={0}
  label="Email"
  type="email"
  required
/>

// Hidden input
<RInput
  table="participant"
  field="id"
  index={0}
  type="hidden"
  value="existing-uuid"
/>

// With field type
<RInput
  table="participant"
  field="data.active"
  index={0}
  type="checkbox"
  hiddenType="__boolean__"
/>
```

### OperationInput Component

```tsx
// Insert operation
<OperationInput table="participant" index={0} value="insert" />

// Update operation
<OperationInput table="participant" index={0} value="update" />

// Delete operation
<OperationInput table="participant" index={0} value="delete" />
```

### DeleteInputs Component

```tsx
// Delete multiple records
<DeleteInputs table="participant" ids={["uuid1", "uuid2", "uuid3"]} />
```

## Authorization Quick Reference

### Common Authorization Patterns

```typescript
// Editor access
authorize: (args) => isEditorQb(args).executeTakeFirst();

// Establishment admin
authorize: (args) => memberIsAdminQb(args).executeTakeFirst();

// Public access
authorize: () => true;

// Not allowed
authorize: () => false;
```

### Table Access Matrix

| Access Level            | Tables                                                                                                                                                                                                                                                                        |
| ----------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Editor**              | `establishment`, `user`, `operator`, `region`, `spot`, `diving_location`, `diving_site`, `diving_course`                                                                                                                                                                      |
| **Establishment Admin** | `participant`, `booking`, `product`, `tag`, `category`, `price`, `addon`, `member`, `activity`, `person`, `customer`, `participation`, `waiver`, `signature`, `comment`, `file`, `payment_method`, `payment`, `trip`, `boat`, `schedule`, `rentable`, `view`, `form`, `field` |
| **Public**              | `review`, `inquiry`, `signup_submission`                                                                                                                                                                                                                                      |
| **System**              | `callback`, `mail`, `user_session`, `one_time_password`, `session_link`, `xendit_*`                                                                                                                                                                                           |
| **Not Allowed**         | `xendit_platform`, `cache`, `event`, `user_event`, `entity_action`, `currency`, `spatial_ref_sys`                                                                                                                                                                             |

## Error Handling

### Common Error Messages

| Error                                    | Cause                        | Solution                                     |
| ---------------------------------------- | ---------------------------- | -------------------------------------------- |
| `unauthorized for insert on participant` | User lacks permission        | Check user role and establishment membership |
| `no_changes`                             | No operations performed      | Verify form data and operations              |
| `could not parse {field}`                | Invalid field format         | Check field type and data format             |
| `reference replacement not supported`    | Multiple values for same key | Use unique field names                       |

### Debugging Tips

1. **Check Network Tab**: Look at the actual form data being sent
2. **Server Logs**: Review console output for detailed errors
3. **Form Structure**: Verify field naming follows the pattern
4. **Authorization**: Ensure user has proper permissions
5. **References**: Check that referenced entities exist

## Performance Tips

1. **Batch Operations**: Combine multiple operations in one request
2. **Minimize Fields**: Only send necessary data
3. **Use Indexes**: Use sequential indexes (0, 1, 2...) for better performance
4. **Avoid Large Arrays**: Break large arrays into smaller chunks

## Security Best Practices

1. **Always Validate**: Use proper field types and validation
2. **Check Authorization**: Verify user permissions for each operation
3. **Sanitize Input**: Use appropriate field types for data transformation
4. **Use HTTPS**: Ensure secure transmission
5. **Rate Limiting**: Implement rate limiting for public endpoints

## Common Gotchas

1. **Field Names**: Must match database schema exactly
2. **Indexes**: Use consistent indexing across related operations
3. **References**: Use `ref-{table}-{index}` format for relationships
4. **Field Types**: Include hidden type inputs for special transformations
5. **Operations**: Always specify the operation type
6. **Redirects**: Include both success and error redirect URLs

## Testing

### Manual Testing

```html
<!-- Test form -->
<form method="post" action="/resource">
  <input type="hidden" name="test.0.operation" value="insert" />
  <input type="text" name="test.0.data.name" value="Test Data" />
  <input type="hidden" name="redirect" value="/test-success" />
  <input type="hidden" name="redirect_error" value="/test-error" />
  <button type="submit">Test</button>
</form>
```

### Unit Testing

```typescript
import { formdataToNestedJson } from "~/misc/formdata-to-nested-json";

const formData = new FormData();
formData.set("participant.0.operation", "insert");
formData.set("participant.0.data.first_name", "John");

const result = formdataToNestedJson(formData);
expect(result).toEqual({
  participant: [{ operation: "insert", data: { first_name: "John" } }],
});
```

## Migration Checklist

When migrating from REST APIs:

- [ ] Map REST endpoints to database tables
- [ ] Define authorization rules for each table
- [ ] Update forms to use new field naming
- [ ] Test all CRUD operations
- [ ] Verify error handling
- [ ] Check authorization rules
- [ ] Test complex operations with references
- [ ] Validate field types and transformations
- [ ] Update client-side code for form responses
- [ ] Implement proper redirect handling
