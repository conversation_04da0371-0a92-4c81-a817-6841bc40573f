## Filetemplates
### kystely migration
filename
```
${YEAR}${MONTH}${DAY}${HOUR}${MINUTE}${SECOND}_migration
```
filecontent
```typescript
import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=sql
  await sql.raw(`select 'test'`).execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
```



### Ports (local)
Prefix for this project is 102

 - 10201 (webapp)
 - 10202 (api)
 - 10211 (postgres)
 - 10212 (hasura)
 - 10213 (pgadmin)


### Infrastructure

 - webapp en auth gaan via firebase
 - database bij aws rds
 - hasura en backendapi (die hasura gebruikt) bij kintohub

### current lines of code number
```
git ls-files | grep -vE '\.(jpg|jpeg|webp|sql|csv|png|gif)$|generated/|expo/' | xargs wc -l
```

### pg_dump
```
pg_dump "postgresql://<user>:<password>@<host>:<port>/<database>?sslmode=require" --no-owner | psql postgresql://localhost:5432/traveltruster_dev
```

### set cors
for test en development
```
gsutil cors set bucket-cors-config-dev.json  gs://traveltruster-singapore-test.appspot.com
```
for production
```
gsutil cors set bucket-cors-config.json  gs://traveltruster-singapore.appspot.com
```