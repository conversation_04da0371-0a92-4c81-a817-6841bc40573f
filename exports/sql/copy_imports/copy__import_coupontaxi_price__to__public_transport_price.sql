-- mutations
with imports as (
    select a.id as to_area, i.flatfee
    from imports.export_coupontaxi_prices i
             inner join final_lists.gadm_govph GadmGov on GadmGov.govph_psgc = i.psgc
             inner join public.area a on a.gadm_gid = GadmGov.gadm_gid
)
insert into public.transport_price (flat_fee, currency, from_area, to_area, transport_type)
select flatfee, 'PHP', 'a6237d58-fade-49b3-a3ac-8e6b3fa0bd40', to_area, 'CouponTaxi' from imports;

-- checks
select * from area where type = 'Custom';
-- custom: a6237d58-fade-49b3-a3ac-8e6b3fa0bd40

select count(*)
from imports.export_coupontaxi_prices;

select * from public.transport_price;

