insert into final_lists.gadm_areas (gadm_gid, geom, name_0, name_1, name_2, name_3, type)
select gid_3, geom, name_0, name_1, name_2, name_3, engtype_3 from gadm."gadm36_PHL_3" gadmLevel3
on conflict on constraint gadm_areas_pk do update set geom = excluded.geom, type = excluded.type;

insert into final_lists.gadm_areas (gadm_gid, geom, name_0, name_1, name_2, type)
select gid_2, geom, name_0, name_1, name_2, engtype_2 from gadm."gadm36_PHL_2" gadmLevel2
on conflict on constraint gadm_areas_pk do update set geom = excluded.geom, type = excluded.type;

select * from final_lists.gadm_areas;