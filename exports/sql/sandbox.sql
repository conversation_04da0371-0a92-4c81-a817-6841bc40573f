select id, name, count(name)
from public.area a
group by name
having count(name) = 1;


with psgcBarangays as (
    select distinct on (name) name, code
    from gov_ph.psgc_publication_march2020
    where geographic_level = 'Bgy'
      and code = '*********'
),
     distinctVillageAreas as (
         select distinct on (name) name, id
         from public.area
         where type = 'Village'
           and psgc is null
     )
update public.area a
set psgc = (select code from psgcBarangays where a.name = psgcBarangays.name)
where a.id in (select id from distinctVillageAreas);
select *
from psgcBarangays
where code = '*********';


select *
from public.area a
         inner join gov_ph.psgc_publication_march2020 level3 on a.psgc = level3.code
         inner join gov_ph.psgc_publication_march2020 level2 on substr(level3.code, 0, 7) || '000' = level2.code
         inner join gov_ph.psgc_publication_march2020 level1 on substr(level2.code, 0, 5) || '00000' = level1.code
         inner join philgis."Barangays" b on a.philgis_barangays_id = b.id
where a.psgc = '*********';

select st_area(geom), *
from public.area
limit 10;

select st_area(geom), *
from public.area a
where a."type" = 'Custom';

INSERT INTO public.area_info (id, info, area_id, created_at, updated_at)
VALUES (DEFAULT,
        'To take a coupon taxi, you simply approach a dispatch station (available at any of the airport terminals), state your destination and you’ll be given a coupon which marks the price to pay.',
        'a6237d58-fade-49b3-a3ac-8e6b3fa0bd40', DEFAULT, DEFAULT)
on conflict do nothing

INSERT INTO public.area_info (id, info, area_id, created_at, updated_at)
VALUES (DEFAULT,
        'In case of carrying large luggage, you can ask for a large Coupon taxi, up to eight passengers, without surcharge.',
        'a6237d58-fade-49b3-a3ac-8e6b3fa0bd40', DEFAULT, DEFAULT)
on conflict do nothing

INSERT INTO public.transport_area_info (transport_type, area_info_id, created_at, updated_at)
VALUES ('CouponTaxi', 'bd2fcb02-5387-4615-bb97-17014a2602ca', DEFAULT, DEFAULT)
on conflict do nothing

INSERT INTO public.transport_area_info (transport_type, area_info_id, created_at, updated_at)
VALUES ('CouponTaxi', '16be7be1-dfaf-4fed-987a-60f6060be89e', DEFAULT, DEFAULT)
on conflict do nothing

CREATE FUNCTION area_size(area_row area)
    RETURNS double precision AS
$$
SELECT st_area(area_row.geom)
$$ LANGUAGE sql STABLE;

select *
from gadm.gadm36gadm
where gid_2 is null;

select *
from gadm.gadm_level1
where gid_0 = 'PHL';

select *
from gadm.gadm_level0
where gid_0 = 'PHL';


select * from philgis."Barangays" b where b.name_3 = 'Barangay 268';
select st_setsrid(geom, 4326), * from area where id = '272986ec-c10b-4da6-9ea1-c101fc55e4e9'

