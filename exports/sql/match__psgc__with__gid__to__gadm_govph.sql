-- select * from public.transport_price tp
-- inner join

select name_2, name_3
from philgis."Barangays"
    except
select name_2, name_3
from gadm."gadm36_PHL_3"
    except
select name_0, name_1, name_2, name_3
from philgis."Barangays";

create or replace function trimGeoName(text) returns text
as
'
    select
        --            regexp_replace(
        replace(
                replace(
                        replace(
                                replace(
                                        replace(
                                                regexp_replace(
                                                        lower(trim($1.name)),
                                                        ''\(.*\)'', ''''),
                                                ''city of'', ''''),
                                        ''city'', ''''),
                                ''metropolitan'', ''''),
                        '' '', ''''),
                ''-'', '''')
    --                    ''[-,$&+,:;=?@#|<>.-^*()%!]'', '''')
'
    language sql
    returns null on null input;

with gadmBarangays as (
    select gid_3               as id,
           trimGeoName(name_1) as trimmed_name1,
           name_1,
           trimGeoName(name_2)    trimmed_name2,
           name_2,
           trimGeoName(name_3)    trimmed_name3,
           name_3
    from gadm."gadm36_PHL_3"
),
     gadmBarangaysDistinct as (
         select distinct on (trimmed_name1, trimmed_name2, trimmed_name3) * from gadmBarangays g
     ),
     gadmMunicities as (
         select gid_2               as id,
                trimGeoName(name_1) as trimmed_name1,
                name_1,
                trimGeoName(name_2)    trimmed_name2,
                name_2
         from gadm."gadm36_PHL_2"
     ),
     gadmMunicitiesDistinct as (
         select distinct on (trimmed_name1, trimmed_name2) *
         from gadmMunicities
     ),
     gov_phBgy as (
         select level3.code              as id,
                trimGeoName(level1.name) as trimmed_name1,
                level1.name              as name_1,
                trimGeoName(level2.name) as trimmed_name2,
                level2.name              as name_2,
                trimGeoName(level3.name) as trimmed_name3,
                level3.name              as name_3
         from gov_ph.psgc_publication_march2020 level3
                  inner join gov_ph.psgc_publication_march2020 level2
                             on substr(level3.code, 0, 7) || '000' = level2.code
                  inner join gov_ph.psgc_publication_march2020 level1
                             on substr(level3.code, 0, 5) || '00000' = level1.code
         where level3.geographic_level = 'Bgy'
     ),
     gov_phBgyDistinct as (
         select distinct on (trimmed_name1, trimmed_name2, trimmed_name3) * from gov_phBgy g
     ),
     gov_phMuniCities as (
         select level2.code              as id,
                trimGeoName(level1.name) as trimmed_name1,
                level1.name              as name_1,
                trimGeoName(level2.name) as trimmed_name2,
                level2.name              as name_2
         from gov_ph.psgc_publication_march2020 level2
                  inner join gov_ph.psgc_publication_march2020 level1
                             on substr(level2.code, 0, 5) || '00000' = level1.code
         where substr(level2.code, 7, 9) = '000'
     ),
     gov_phMuniCitiesDistinct as (
         select distinct on (trimmed_name1, trimmed_name2) * from gov_phMuniCities g
     ),
     gadmExceptGovPh as (
         select trimmed_name1, trimmed_name2, trimmed_name3
         from gadmBarangaysDistinct
             except
         select trimmed_name1, trimmed_name2, trimmed_name3
         from gov_phBgyDistinct
     ),
     GovPhExceptGadm as (
         select trimmed_name1, trimmed_name2, trimmed_name3
         from gov_phBgyDistinct
             except
         select trimmed_name1, trimmed_name2, trimmed_name3
         from gadmBarangaysDistinct
     ),
     gadmExceptGovPhMunicities as (
         select trimmed_name1, trimmed_name2
         from gadmMunicitiesDistinct
             except
         select trimmed_name1, trimmed_name2
         from gov_phMuniCitiesDistinct
     ),
     gadmJoinGovBgy as (
         select govh.id as psgc, gadm.id as gid, *
         from gadmBarangaysDistinct gadm
                  inner join gov_phBgyDistinct govh
                             on gadm.trimmed_name1 = govh.trimmed_name1
                                 and gadm.trimmed_name2 = govh.trimmed_name2
                                 and gadm.trimmed_name3 = govh.trimmed_name3
     ),
     gadmJoinGovMunicities as (
         select govh.id as psgc, gadm.id as gid, *
         from gadmMunicitiesDistinct gadm
                  inner join gov_phMuniCitiesDistinct govh
                             on gadm.trimmed_name1 = govh.trimmed_name1
                                 and gadm.trimmed_name2 = govh.trimmed_name2
     )
--          insert into final_lists.gadm_govph (govph_psgc, gadm_gid)
--              select psgc, gid from gadmJoinGovMunicities
--              returning *;

insert into final_lists.gadm_govph (govph_psgc, gadm_gid)
select g.psgc, g.gid
from gadmJoinGovBgy g
returning *;

select gid, count(gid)
from gadmJoinGovMunicities
group by gid having count(gid) > 1;

select gid, count(gid)
from gadmJoinGovBgy
group by gid
having count(gid) > 1;

select * from final_lists.gadm_govph;

select trimmed_name1 || trimmed_name2 || trimmed_name3,
       count(trimmed_name1 || trimmed_name2 || trimmed_name3),
       array_agg(trimmedList.id)     as ids,
       array_agg(trimmedList.name_1) as name1s,
       array_agg(trimmedList.name_2) as name2s,
       array_agg(trimmedList.name_3) as name3s
from gov_phBgyDistinct as trimmedList
group by trimmed_name1 || trimmed_name2 || trimmed_name3
having count(trimmed_name1 || trimmed_name2 || trimmed_name3) > 1;


select count(*)
from gadm."gadm36_PHL_3";
select geographic_level, count(geographic_level)
from gov_ph.psgc_publication_march2020
where substr(code, 7, 9) != '000'
group by geographic_level;

select count(*) from final_lists.gadm_govph;

select *
from gov_ph.psgc_publication_march2020
where name ilike '%quezon%';
select *
from gadm."gadm36_PHL_3"
where name_3 ilike '%quezon%'