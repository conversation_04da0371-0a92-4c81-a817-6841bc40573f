select
--a.geom,
a.id,
a.name,
a.gadm_gid                             as GADM_GID,
gadIdPsgcList.govph_psgc               as psgc,
psgcList.name                          as name<PERSON><PERSON><PERSON><PERSON>,
a."type",
gadmAreas.name_1,
gadmAreas.name_2,
gadmAreas.name_3,
a.total_polygons,
'https://scamgem.web.app/map/' || a.id as url
from (
         select inner_a.id,
                inner_a.name,
                inner_a.geom,
                inner_a.type,
                inner_a.gadm_gid,
                st_numgeometries(st_polygonize(inner_a.geom)) as total_polygons
         from public.area inner_a
         group by inner_a.id, inner_a.name, inner_a.geom, inner_a.type, inner_a.gadm_gid
--having st_numgeometries(st_polygonize(inner_a.geom)) > 1
     ) as a
         left outer join final_lists.gadm_areas gadmAreas on gadmAreas.gadm_gid = a.gadm_gid
         left outer join final_lists.gadm_govph gadIdPsgcList on gadIdPsgcList.gadm_gid = a.gadm_gid
         left outer join gov_ph.psgc_publication_march2020 psgcList on psgcList.code = gadIdPsgcList.govph_psgc
-- where a.type = 'Custom'
--where a.name = 'Lahay-lahay';