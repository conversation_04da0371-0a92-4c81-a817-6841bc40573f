-- Update psgc for barangays
with psgcVillage as (
    select level3.code psgc,
           b.id as     philgis_barangay_id
-- select *
    from gov_ph.psgc_publication_march2020 level3
             inner join gov_ph.psgc_publication_march2020 level2
                        on substr(level3.code, 0, 7) || '000' = level2.code
             inner join gov_ph.psgc_publication_march2020 level1
                        on substr(level3.code, 0, 5) || '00000' = level1.code
             inner join philgis."Barangays" b on lower(trim(b.name_3)) = lower(trim(level3.name))
        and lower(trim(b.name_2)) = lower(trim(level2.name))
        and lower(trim(b.name_1)) = lower(trim(level1.name))
    where level3.geographic_level = 'Bgy'
)
-- select psgcVillage.philgis_barangay_id, array_agg(psgcVillage.psgc), count(psgcVillage.philgis_barangay_id) from psgcVillage
-- group by psgcVillage.philgis_barangay_id
-- having count(psgcVillage.philgis_barangay_id) > 1;

update area a
set psgc = (select psgc from psgcVillage where psgcVillage.philgis_barangay_id = a.philgis_barangays_id)
where a.philgis_barangays_id in (select psgcVillage.philgis_barangay_id from psgcVillage)
;


--
select *
from public.area a
         inner join philgis."Barangays" b on a.philgis_barangays_id = b.id
where psgc is null
  and type = 'Village';


-- update psgc for municities
with psgcMuniCity as (
    select level2.code psgc,
            m.id as philgis_municity_id
--     select *
    from gov_ph.psgc_publication_march2020 level2
             inner join gov_ph.psgc_publication_march2020 level1
                        on substr(level2.code, 0, 5) || '00000' = level1.code and level2.code != level1.code
             inner join philgis."MuniCities" m on lower(trim(m.name_2)) = lower(trim(level2.name))
        and lower(trim(m.name_1)) = lower(trim(level1.name))
    where level2.geographic_level != 'Bgy'
      and (level2.geographic_level = 'Mun' or level2.geographic_level = 'City')
)
update area a
set psgc = (select psgcMuniCity.psgc from psgcMuniCity where psgcMuniCity.philgis_municity_id = a.philgis_muni_cities_id)
where a.philgis_muni_cities_id in (select psgcMuniCity.philgis_municity_id from psgcMuniCity)
-- select count(philgis_municity_id)
-- from psgcMuniCity
-- group by philgis_municity_id
-- having count(philgis_municity_id) > 1;


