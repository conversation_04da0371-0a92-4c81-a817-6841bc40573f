# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
*/.bit/*
*/dist/*
*/node_modules/**
*/.cache/**
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

/.firebase/**

firebase-debug.log
npm-debug.log*
yarn-debug.log*
yarn-error.log

.idea
.vscode

env.json
remix/sandbox.pgsql