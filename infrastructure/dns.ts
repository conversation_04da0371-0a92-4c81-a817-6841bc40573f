import * as digitalocean from "@pulumi/digitalocean";
import { DnsRecordArgs } from "@pulumi/digitalocean";

const diversdeskWhitelabels = [
  "seadivingbali",
  "bali-reef-divers",
  "divingindo",
  "silverreefdiveresort",
  "reeflexdivers",
  "balidiveresortandspa",
  "all4divingindonesia",
  "purpledivepenida",
  "sambodivers",
  "pondokbambu",
  "dragondivekomodo",
  "fastmanta",
  "jomadventure",
  "sun-and-fun",
  "oceans11",
  "thresher-shark-divers",
  "soberdiving",
  "purediveresort",
  "critterrepublic",
  "bahuradive",
  "freshfinsdiving",
  "divebequia",
  "mapleleafscuba",
  "thehighdiveindonesia",
  "dive-shack",
  "natureislanddive",
  "onlyblue-diving",
  "seacreaturesdiving",
  "divinglembongan",
  "balidiving",
  "haadyaodivers",
  "hgltours",
  "mantasticscuba",
  "tiomandivebuddy",
  "saltyfinnsdivingbali",
  "belizeunknown",
  "abyssoceanworld",
  "valmdiversbohol",
  "soulscubadivers",
  "livingseas",
  "chaniadiving",
  "tarzan-diving",
  "bataviacoastdive",
  "circledivers",
  "rumblefishmalaysia",
  "specialdive",
  "tauchwerkstatt-eu",
  "diveacademy",
  "aquanautsgrenada",
  "scubapublic",
  "medusasdive",
  "bestdivingingreece",
  "uniquediving",
  "dreamteamdivers-elgouna",
  "story-divers",
  "diveseanatives",
  "demo",
  "turtlebuddydivers",
  "malapascuambidive",
  "ceningandivers",
  "indooceanproject",
  "blowbubblesdivers",
  "karangdivers",
  "tropicaldivers-alona",
  "bongobongodivers",
  "manta-dive",
  "absolutescubabali",
  "mambodiveresort",
  "bluecornerfreedive",
  "aussiediversphuket",
  "fijidiving",
  "isledive",
  "diveindia",
  "blackturtledive",
  "smdiversbohol",
  "ecodivebali",
  "ocean-sun",
  "kohkooddivers",
  "cortodivers",
  "ganggadivers",
  "oceantribe",
  "scubaholicssubic",
  "vipdiving",

  "example",
  "trial",
  "trialmyr",

  "divingindo.test",
  "example.test",
];

const allDomains = ["traveltruster.com", "scamgem.com", "scamjam.com", "diverdesk.com", "diversdesk.com"];

type Domain = (typeof allDomains)[number];

const ttDomains = ["traveltruster.com", "scamgem.com", "scamjam.com"];

export const setupDns = () => {
  interface DnsArgs extends Omit<DnsRecordArgs, "domain"> {
    domain: Domain[] | Domain;
  }

  const whitelabelDnsRecords: DnsArgs[] = diversdeskWhitelabels.map((prefix) => ({
    domain: "diversdesk.com",
    type: "CNAME",
    name: prefix,
    value: "cname.vercel-dns.com.",
  }));

  const dnsRecords: DnsArgs[] = [
    ...whitelabelDnsRecords,
    { domain: allDomains, type: "A", name: "localhost", value: "127.0.0.1" },

    // vercel
    { domain: allDomains, type: "A", name: "@", value: "***********" },
    {
      domain: allDomains,
      type: "CNAME",
      name: "www",
      value: "cname.vercel-dns.com.",
    },
    {
      domain: ttDomains,
      type: "CNAME",
      name: "app",
      value: "cname.vercel-dns.com.",
    },
    {
      domain: ttDomains,
      type: "CNAME",
      name: "master",
      value: "cname.vercel-dns.com.",
    },

    {
      domain: ttDomains,
      type: "CNAME",
      name: "test",
      value: "cname.vercel-dns.com.",
    },
    {
      domain: "traveltruster.com",
      type: "CNAME",
      name: "blog",
      value: "cname.vercel-dns.com.",
    },
    {
      domain: "diversdesk.com",
      type: "CNAME",
      name: "app",
      value: "cname.vercel-dns.com.",
    },
    {
      domain: "diversdesk.com",
      type: "CNAME",
      name: "app.test",
      value: "cname.vercel-dns.com.",
    },
    {
      domain: "diversdesk.com",
      type: "TXT",
      name: "@",
      value: "google-site-verification=mbxsV6ngs-ajEB3VnQV8ywYYBSUE04r6wyQlbNJ4-9c",
    },
    {
      domain: allDomains,
      type: "TXT",
      name: "@",
      value: "v=spf1 include:_spf.mlsend.com include:zoho.eu include:amazonses.com include:eu.zcsend.net -all",
      ttl: 3600,
    },
    {
      domain: "traveltruster.com",
      type: "MX",
      name: "bounces",
      value: "feedback-smtp.sa-east-1.amazonses.com.",
      priority: 10,
    },
    {
      domain: "traveltruster.com",
      type: "TXT",
      name: "resend._domainkey",
      value:
        "p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDA5MAW6EmuU+C1pB1MPmNzyHh583gS4xnJebq5YP0oAPzWE8rYHjl+NHVBpRX8qbGOnhbDk6E7lklbwXsy+5p5aHVn5dZk993lS/56w7qHFbT6WvsWTrgdOBT0yYlhIdOT0lK6/MeY+aUnPfytc8Ny1UNAc5jdrKFsisVqjyiqpQIDAQAB",
    },
    {
      domain: ttDomains,
      type: "TXT",
      name: "zmail._domainkey",
      value:
        "v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC/Ra+W8EcttOrfMQF8TBHzrt+fyRh6k4runHvZr1TYmjGChAeH+/pb8AW7pLkzIXe6jMGTpElZBDzXOeHgxWiqzvINP55+BtETPGtngXyZQVxALRhPgm6ls1DN22Nd9A7MUvnDt9mZlpvlwhO96/KIwgGFjrMbt91KWQwIn90SswIDAQAB",
    },
    {
      domain: "diversdesk.com",
      type: "TXT",
      name: "zmail._domainkey",
      value:
        "v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCnD19EqwOe1hUXUzo6uDDFs4yy76ewSOwFK/1Mn/ntLe3kGgm7LWuKmzUWnu4EhCMEAo4cdCusCGGCqpqqYkAx9dGN82unrIRvnvnTOswf6qqg2FoAGHKnYvIyFOmTW+Dx0MRuo1TQWrA8Cp0SYs3g4JcDK3b8Tkiz3SaAaaaUEQIDAQAB",
    },
    {
      domain: "diversdesk.com",
      type: "TXT",
      name: "2124._domainkey",
      value:
        "k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC9Yjifv5dVwHyudor1mwfw9jyEDTU1aeLIdyznEIkSmOfda8QtDjDAD28TzggK2tElr4/xbg4FiOoUOJc2U3ei4M72I+R6DZbA1nLFkBDV77UiWJN9OJ+eZ4o28eajfxxCITstIClXMCMw+jHy6hxqXwv+VIEvbEBSWawL58Oj4wIDAQAB",
    },
    {
      domain: ttDomains,
      type: "TXT",
      name: "@",
      value: "firebase=scamgem",
      ttl: 3600,
    },
    {
      domain: ttDomains,
      type: "TXT",
      name: "@",
      value: "firebase=traveltruster-singapore-test",
      ttl: 3600,
    },
    {
      domain: ttDomains,
      type: "TXT",
      name: "@",
      value: "firebase=traveltruster-singapore",
      ttl: 3600,
    },
    {
      domain: ttDomains,
      type: "TXT",
      name: "default._domainkey",
      value:
        "v=DKIM1; k=rsa; p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAusMPL0TppkeCq9Csa4wPIDJrIiU5ZD/zZHi7lyLqlfL0pwkfMYgRvGhK5enw/7KPGrAtew1zAcdkDu/9FZo4oIyy/KEjdK5xBc47KfVVT8lXNzHYBpZ1yUmafpulXrgstBn5Yyirn5/2FvI07FOC8vC1o9Vj3/sPCbcemq9HzLdbu3W2B68M0qIjnQhGYgYWP GVnBu04QHNlom2E1wHrdEwt2EqlhjMjy3lVe+mma2fboqM+W1Kk5D82ad23/Jwvb7jpMQzkCKBU5scUqgFW57HG0Yb1Zl1BcEqsagwzh/V2jzTlsITytoB+x6e7H9BdWi/0Jq2DAexGWKt4DgTzkwIDAQAB;",
    },
    {
      domain: allDomains,
      type: "TXT",
      name: "_dmarc",
      value: "v=DMARC1; p=none",
      ttl: 3600,
    },

    {
      domain: allDomains,
      type: "MX",
      name: "@",
      value: "mx.zoho.eu.",
      ttl: 3600,
      priority: 10,
    },
    {
      domain: allDomains,
      type: "MX",
      name: "@",
      value: "mx2.zoho.eu.",
      ttl: 3600,
      priority: 20,
    },
    {
      domain: allDomains,
      type: "MX",
      name: "@",
      value: "mx3.zoho.eu.",
      ttl: 3600,
      priority: 50,
    },

    // {type: 'MX', name: '@', value: 'aspmx.l.google.com.', ttl: 3600, priority: 1},
    // {type: 'MX', name: '@', value: 'alt1.aspmx.l.google.com.', ttl: 3600, priority: 5},
    // {type: 'MX', name: '@', value: 'alt2.aspmx.l.google.com.', ttl: 3600, priority: 5},
    // {type: 'MX', name: '@', value: 'alt3.aspmx.l.google.com.', ttl: 3600, priority: 10},
    // {type: 'MX', name: '@', value: 'alt4.aspmx.l.google.com.', ttl: 3600, priority: 10},

    {
      domain: "traveltruster.com",
      type: "TXT",
      name: "@",
      value: "zoho-verification=zb58619164.zmverify.zoho.eu",
    },

    {
      domain: "traveltruster.com",
      type: "A",
      name: "auth",
      value: "199.36.158.100",
    },
    {
      domain: "traveltruster.com",
      type: "TXT",
      name: "@",
      value: "google-site-verification=m2HiBHRkf_AQyNWQ4TF0RmlU87DmypMFJ-sLWlTH2xs",
    },
    {
      domain: "traveltruster.com",
      type: "TXT",
      name: "@",
      value: "google-site-verification=B47M_UNClxOjBavQV1NYo2vFPblEO67FyOe-6F4HBu8",
    },
    {
      domain: "traveltruster.com",
      type: "CNAME",
      name: "litesrv._domainkey",
      value: "litesrv._domainkey.mlsend.com.",
    },
    {
      domain: "traveltruster.com",
      type: "CNAME",
      name: "firebase1._domainkey",
      value: "mail-traveltruster-com.dkim1._domainkey.firebasemail.com.",
    },
    {
      domain: "traveltruster.com",
      type: "CNAME",
      name: "firebase2._domainkey",
      value: "mail-traveltruster-com.dkim2._domainkey.firebasemail.com.",
    },

    {
      domain: "traveltruster.com",
      type: "CNAME",
      name: "57szsjgo7r6vtdxllwnllmhqt7dkqdtp._domainkey",
      value: "57szsjgo7r6vtdxllwnllmhqt7dkqdtp.dkim.amazonses.com.",
    },
    {
      domain: "traveltruster.com",
      type: "CNAME",
      name: "lu7c5fsvtk7mbagqphk6asyjhqmelknn._domainkey",
      value: "lu7c5fsvtk7mbagqphk6asyjhqmelknn.dkim.amazonses.com.",
    },
    {
      domain: "traveltruster.com",
      type: "CNAME",
      name: "xi6zl22eqnegngqpr655ugot3wk6jomj._domainkey",
      value: "xi6zl22eqnegngqpr655ugot3wk6jomj.dkim.amazonses.com.",
    },

    {
      domain: "traveltruster.com",
      type: "CNAME",
      name: "dpeveplikomwfigex2wprwm4kil575nl._domainkey",
      value: "dpeveplikomwfigex2wprwm4kil575nl.dkim.amazonses.com.",
    },
    {
      domain: "traveltruster.com",
      type: "CNAME",
      name: "ng3splcahuvogdiybrzvuyffga7azg46._domainkey",
      value: "ng3splcahuvogdiybrzvuyffga7azg46.dkim.amazonses.com.",
    },
    {
      domain: "traveltruster.com",
      type: "CNAME",
      name: "a2nbwznppcguddvnxzfmnd4prkcs3rti._domainkey",
      value: "a2nbwznppcguddvnxzfmnd4prkcs3rti.dkim.amazonses.com.",
    },
    {
      domain: "diversdesk.com",
      type: "CNAME",
      name: "66txwjak7pjrhzs2i6ksjtc6fsvjotic._domainkey",
      value: "66txwjak7pjrhzs2i6ksjtc6fsvjotic.dkim.amazonses.com.",
    },
    {
      domain: "diversdesk.com",
      type: "CNAME",
      name: "vltktdkojfopfir63pbezpttc27ppyj2._domainkey",
      value: "vltktdkojfopfir63pbezpttc27ppyj2.dkim.amazonses.com.",
    },
    {
      domain: "diversdesk.com",
      type: "CNAME",
      name: "w5gp7du34hdnrmccjkote3ggvaefy35d._domainkey",
      value: "w5gp7du34hdnrmccjkote3ggvaefy35d.dkim.amazonses.com.",
    },

    {
      domain: allDomains,
      type: "MX",
      name: "mail",
      priority: 10,
      value: "feedback-smtp.ap-southeast-1.amazonses.com.",
    },
    {
      domain: allDomains,
      type: "TXT",
      name: "mail",
      value: "v=spf1 include:amazonses.com ~all",
    },

    {
      domain: "scamjam.com",
      name: "@",
      type: "MX",
      value: "ycdmf6ebpioxqxsjpbiei2b44cg5nlen6svs5xjq6bsgfyhitvvq.mx-verification.google.com.",
      ttl: 3600,
      priority: 15,
    },

    {
      domain: "scamjam.com",
      type: "TXT",
      name: "@",
      value: "google-site-verification=wIbC-IF6HXheSXhQRGg84I3WrI30qy7dMPBkYuDonWs",
    },
    {
      domain: "scamjam.com",
      type: "CNAME",
      name: "firebase1._domainkey",
      value: "mail-scamjam-com.dkim1._domainkey.firebasemail.com.",
    },
    {
      domain: "scamjam.com",
      type: "CNAME",
      name: "firebase2._domainkey",
      value: "mail-scamjam-com.dkim2._domainkey.firebasemail.com.",
    },

    {
      domain: "scamgem.com",
      type: "TXT",
      name: "@",
      value: "zoho-verification=zb97580171.zmverify.zoho.eu",
    },
    {
      domain: "scamgem.com",
      type: "TXT",
      name: "@",
      value: "google-site-verification=L_LiPTsrifnp9RY-9vemlYIf73MSEXOXKfDDqRBhuxY",
    },
    {
      domain: "scamgem.com",
      type: "CNAME",
      name: "firebase1._domainkey",
      value: "mail-scamgem-com.dkim1._domainkey.firebasemail.com.",
    },
    {
      domain: "scamgem.com",
      type: "CNAME",
      name: "firebase2._domainkey",
      value: "mail-scamgem-com.dkim2._domainkey.firebasemail.com.",
    },
  ];

  return dnsRecords.map((record) => {
    const domains = record.domain instanceof Array ? record.domain : [record.domain];

    return domains.map((domain) => {
      const getPulumiName = () => {
        const name = typeof record.name === "string" ? record.name : "";
        const value = typeof record.value === "string" ? record.value : "";
        return domain + name + value;
      };
      return new digitalocean.DnsRecord(getPulumiName(), {
        ...record,
        domain: domain,
      });
    });
  });
};
