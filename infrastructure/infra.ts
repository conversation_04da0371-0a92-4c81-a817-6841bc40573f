import * as digitalocean from "@pulumi/digitalocean";
import * as pulumi from "@pulumi/pulumi";
import { AppSpecDomainName } from "@pulumi/digitalocean/types/input";

const stack = pulumi.getStack();
const config = new pulumi.Config();

const name = `traveltruster-${stack}`;
const toplevelDomain = "traveltruster.com";
const dbClusterName = `db-scamgem-${stack}`;

const domains: AppSpecDomainName[] =
  stack === "master"
    ? [
        {
          name: "www." + toplevelDomain,
          type: "PRIMARY",
          zone: toplevelDomain,
        },
        {
          name: toplevelDomain,
          type: "ALIAS",
          zone: toplevelDomain,
        },
        {
          name: "master." + toplevelDomain,
          type: "ALIAS",
          zone: toplevelDomain,
        },
        {
          name: "app." + toplevelDomain,
          type: "ALIAS",
          zone: toplevelDomain,
        },
      ]
    : [
        {
          name: `${stack}.${toplevelDomain}`,
          type: "PRIMARY",
          zone: toplevelDomain,
        },
      ];

export const setupInfra = () => {
  const AWS_SECRET_ACCESS_KEY = config.requireSecret("aws.secretKey");
  const AWS_ACCESS_KEY_ID = config.requireSecret("aws.accessKey");
  const logtailToken = config.requireSecret("logtail_token");

  const app = new digitalocean.App("app", {
    spec: {
      name: name,
      region: "sgp",
      databases: [
        {
          clusterName: dbClusterName,
          dbName: "master",
          dbUser: "doadmin",
          engine: "PG",
          name: "database",
          production: true,
          version: "14",
        },
      ],
      domainNames: domains,
      services: [
        {
          name: "remix",
          sourceDir: "remix",
          instanceCount: 1,
          instanceSizeSlug: "basic-xxs",
          httpPort: 3000,
          buildCommand: "yarn build",
          runCommand: "yarn start",
          routes: [{ path: "/" }],
          logDestinations: [
            {
              name: "logtail",
              logtail: { token: logtailToken },
            },
          ],
          environmentSlug: "node-js",
          github: {
            branch: stack,
            repo: "dinkelburg/scamgem",
            deployOnPush: true,
          },
        },
      ],
      alerts: [{ rule: "DEPLOYMENT_FAILED" }, { rule: "DEPLOYMENT_LIVE" }],
      envs: [
        {
          key: "PRIMARY_DOMAIN",
          scope: "RUN_AND_BUILD_TIME",
          value: "${APP_DOMAIN}",
        },
        {
          key: "OVERWRITE",
          scope: "RUN_AND_BUILD_TIME",
          value: `{
"PRIMARY_DOMAIN": "\${APP_DOMAIN}",
"DB": {
  "user": "\${database.USERNAME}",
  "password": "\${database.PASSWORD}",
  "host": "\${database.HOSTNAME}",
  "port": \${database.PORT},
  "database": "\${database.DATABASE}"
}
}`,
        },
        {
          key: "AWS_ACCESS_KEY_ID",
          scope: "RUN_AND_BUILD_TIME",
          value: AWS_ACCESS_KEY_ID,
        },
        {
          key: "AWS_SECRET_ACCESS_KEY",
          scope: "RUN_AND_BUILD_TIME",
          type: "SECRET",
          value: AWS_SECRET_ACCESS_KEY,
        },
      ],
    },
  });

  // const dbInboundRules = new digitalocean.DatabaseFirewall("inbound", {
  //   clusterId: dbCluster.then(result => result.id),
  //   rules: [{ type: "app", value: app.id }],
  // });
  //
  // const projectResource = new digitalocean.ProjectResources(
  //   "project-resources",
  //   {
  //     project: digitalOceanProject.then((proj) => proj.id),
  //     resources: [app.urn],
  //   }
  // );
};
