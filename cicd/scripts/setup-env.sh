#!/bin/bash
set -e

if [ ! $GITHUB_REF ]; then
  echo "GITHUB_REF not set, defaulting to refs/heads/test"
  GITHUB_REF=refs/heads/test
fi

if [ ! $GITHUB_WORKSPACE ]; then
  echo GITHUB_WORKSPACE not set, defaulting to current directory $(pwd)
  GITHUB_WORKSPACE=$(pwd)
fi

if [ ! $GITHUB_ENV ]; then
  GITHUB_ENV="${GITHUB_WORKSPACE}/GITHUB_ENV.txt"
  touch $GITHUB_ENV
  echo created ${GITHUB_ENV} for testing purposes
fi

BRANCH=$(echo ${GITHUB_REF#refs/heads/})
ENVIRONMENT='test'

if [ $BRANCH == 'test' ] || [ $BRANCH == 'master' ] || [ $BRANCH == 'dev' ]; then
  ENVIRONMENT=$BRANCH
else
  echo "defaulting to test environment"
  ENVIRONMENT='test'
fi

# set env variables
echo "BRANCH=${BRANCH}" >>"${GITHUB_ENV}"
echo "ENVIRONMENT=${ENVIRONMENT}" >>"${GITHUB_ENV}"

mask_and_extract_secrets_from_file() {
  FILE=$1
  while read p; do
    ENV_KEY=$(echo "$p" | sed 's/=.*//g')
    if [[ $ENV_KEY != "sops"* ]]; then
      echo "::add-mask::$(sops -d --extract "[\"$ENV_KEY\"]" $FILE)"
      echo "$ENV_KEY=$(sops -d --extract "[\"$ENV_KEY\"]" $FILE)" >>$GITHUB_ENV
    fi
  done <$FILE
}

mask_and_extract_secrets_from_file "${GITHUB_WORKSPACE}/cicd/env/${ENVIRONMENT}.env"
