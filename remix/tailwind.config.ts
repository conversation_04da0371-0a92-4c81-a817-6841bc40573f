import type { Config } from "tailwindcss";
import defaultTheme from "tailwindcss/defaultTheme";
import containerQueries from "@tailwindcss/container-queries";
import typography from "@tailwindcss/typography";
import forms from "@tailwindcss/forms";
import aspectRatio from "@tailwindcss/aspect-ratio";
// import headlessUI from "@headlessui/tailwindcss";
import tailwindcssAnimate from "tailwindcss-animate";

export default {
  content: ["./app/**/*.{js,ts,jsx,tsx}"],
  theme: {
    // screens: {
    //   sm: "640px",
    //   "max-sm": { max: "640px" },
    //   md: "768px",
    //   "max-md": { max: "768px" },
    //   lg: "1024px",
    //   "max-lg": { max: "1024px" },
    //   xl: "1280px",
    //   "max-xl": { max: "1280px" },
    //   "2xl": "1536px",
    //   "max-2xl": { max: "1536px" },
    // },
    extend: {
      aria: {
        ...defaultTheme.aria,
        busy: 'busy="true"',
        current: 'current="true"',
      },
      data: {
        success: 'success="true"',
        loading: 'loading="true"',
        equal: 'equal="true"',
        ...defaultTheme.data,
      },
      transitionProperty: {
        width: "width",
      },
      container: {
        center: true,
        padding: "0.75rem",
      },
      fontFamily: {
        sans: ['"Inter"', ...defaultTheme.fontFamily.sans],
        serif: ['"Inter"', ...defaultTheme.fontFamily.serif],
        mono: [...defaultTheme.fontFamily.mono],
      },
      colors: {
        primary: {
          light: "#FF9780",
          DEFAULT: "#FF7557",
          100: "#ffe0db",
          200: "#fcd5cf",
          300: "#ffb9b0",
          400: "#ffa091",
          600: "#FF471F",
          700: "#ec360b",
          800: "#E62900",
          dark: "#E62900",
        },
        secondary: {
          50: "#E8F2F6",
          100: "#BCDCFB",
          200: "#b4d9fc",
          300: "#9ecaef",
          500: "#8BB7C9",
          600: "#77b3cb",
          700: "#5ca9c7",
          stroke: "#CCD6EA",
          tag: "#8BB7C9",
          DEFAULT: "#8BB7C9",
        },
      },
    },
  },
  plugins: [containerQueries, typography, forms, aspectRatio, tailwindcssAnimate],
} satisfies Config;
