import { vitePlugin as remix } from "@remix-run/dev";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import commonjs from "vite-plugin-commonjs";
import vitePluginRequire from "vite-plugin-require";
// @ts-ignore
import { cjsInterop } from "vite-plugin-cjs-interop";
import { vercelPreset } from "@vercel/remix/vite";
import { lingui } from "@lingui/vite-plugin";
import macrosPlugin from "vite-plugin-babel-macros";

declare module "@remix-run/node" {
  // or cloudflare, deno, etc.
  interface Future {
    v3_singleFetch: true;
  }
}

export default defineConfig({
  server: {
    port: 3000,
    allowedHosts: ["local-tt.dinkel.works"],
  },
  ssr: {
    noExternal: ["qrcode.react", "mapbox-gl", "@formkit/auto-animate", "remeda", "@markdoc/markdoc"],
  },
  optimizeDeps: {
    exclude: ["@mapbox/node-pre-gyp"],
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: { mapbox: ["mapbox-gl"] },
      },
    },
  },
  plugins: [
    tsconfigPaths(),
    // @ts-ignore
    vitePluginRequire.default(),
    cjsInterop({
      dependencies: ["pg", "@googlemaps/js-api-loader"],
    }),
    commonjs(),
    lingui(),
    remix({
      presets: [vercelPreset()],
      future: {
        v3_fetcherPersist: true,
        unstable_optimizeDeps: true,
        v3_throwAbortReason: true,
        v3_lazyRouteDiscovery: true,
        v3_singleFetch: true,
        v3_relativeSplatPath: true,
        v3_routeConfig: true,
      },
      serverModuleFormat: "esm",
      ignoredRouteFiles: ["**/.*"],
    }),
    macrosPlugin(),
  ],
});
