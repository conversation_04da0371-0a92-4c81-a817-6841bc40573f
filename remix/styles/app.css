@import "./../app/components/field/rating/rating-input.css";
/*@import "react-international-phone/build/index.css";*/

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
    .input {
        @apply w-full rounded-lg border border-slate-300 p-2.5 text-sm text-gray-900 disabled:opacity-80 transition-colors min-h-12
    }

    .validated input:invalid, input.touched:invalid {
        @apply border-red-500
    }

    @media print {
        input:invalid {
            @apply border-slate-500
        }
    }

    .validated .select:invalid, .select.touched:invalid {
        @apply border-red-500
    }

    input.input, input.input-clean {
        line-height: 24px;
    }

    .input:hover:not(:disabled,:read-only) {
        @apply border-slate-500
    }

    .input:focus {
        @apply border-blue-500 ring-blue-500
    }

    .input:focus:read-only {
        @apply ring-0 border-slate-300
    }

    .btn {
        @apply flex items-center font-semibold justify-center gap-3 rounded p-2 px-3 disabled:opacity-60 transition-colors peer-checked:text-transparent;
    }

    .btn-basic {
        @apply bg-slate-100 text-slate-700 hover:bg-slate-200 active:bg-slate-300
    }

    .btn-primary {
        @apply bg-primary text-white hover:bg-primary-600 active:bg-primary-dark
    }

    .btn-secondary {
        @apply bg-secondary-500 text-white hover:bg-secondary-600 active:bg-secondary-700
    }

    .btn-red {
        @apply bg-red-500 text-white hover:bg-red-600 active:bg-red-700
    }


    .btn-green {
        @apply bg-green-600 text-white hover:bg-green-700 active:bg-green-800
    }

    .app-container {
        @apply container max-w-6xl
    }

    .link {
        @apply hover:underline active:text-primary-700 text-primary aria-busy:animate-pulse aria-busy:cursor-progress
    }

    .link.active {
        @apply font-bold
    }

    .formcontrol {
        @apply rounded-md border border-slate-300 focus-within:ring focus-within:ring-opacity-40 shadow-sm invalid:ring-red-500
    }

    .input-clean {
        @apply border-none focus:ring-0 rounded-md
    }

    .formcontrol-input {
        @apply border-none focus:ring-0 focus:outline-none rounded-md focus:bg-slate-100
    }

    .checkbox {
        @apply rounded border-slate-500 text-orange-500 shadow-sm focus:border-indigo-300 focus:ring focus:ring-offset-0 focus:ring-indigo-200 focus:ring-opacity-50 disabled:opacity-50
    }

    .radio {
        @apply rounded-full border-slate-500 text-orange-500 shadow-sm focus:border-indigo-300 focus:ring focus:ring-offset-0 focus:ring-indigo-200 focus:ring-opacity-50 disabled:opacity-50
    }

    .select {
        @apply rounded-md focus:ring-slate-600 border-slate-300 text-slate-700  py-2.5 text-sm min-h-12
    }

    .dive-site-marker {
        @apply bg-red-500 w-4 h-4 rounded-full flex items-center justify-center
    }

    .dive-site-marker > div {
        @apply bg-white w-full h-[2px] rotate-[35deg]
    }

    .block-divider::after {
        content: ' ';
        @apply hidden h-3 bg-secondary-50 md:block
    }

    .required:after {
        content: ' *';
        @apply text-red-500;
    }

    .spinner {
        /*position: relative;*/
        @apply relative;
    }

    .spinner::after {
        /*@apply border-t-black;*/
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        border: 4px solid transparent;
        border-top-color: #ffffff;
        /*border-top-color: black;*/
        border-radius: 50%;
        animation: spinner-animation 1s ease infinite;
    }

    .spinner.spinner-xl::after {
        width: 32px;
        height: 32px;
    }

    .spinner.spinner-light::after {
        border-top-color: white;
    }

    .spinner.spinner-dark::after {
        border-top-color: #444444;
    }

    @keyframes spinner-animation {
        from {
            transform: rotate(0turn);
        }

        to {
            transform: rotate(1turn);
        }
    }


    /*https://codepen.io/lukaszkups/pen/NQjeVN*/
    .loading-dots:after {
        animation: loading-dots 2s linear infinite;
        content: ''
    }

    @keyframes loading-dots {
        0%, 20% {
            content: '.'
        }
        40% {
            content: '..'
        }
        60% {
            content: '...'
        }
        90%, 100% {
            content: ''
        }
    }

    .markdoc-editor {
        mark {
            background: yellow;
            /*color: transparent;*/
            display: inline;
            word-wrap: break-word;
        }
    }

}

.image-text {
    /*text-shadow: 1px 1px 2px black, 0 0 4em #000000, 0 0 0.1em #000000;*/
    text-shadow: 0 0 4em #000000;
    /*mix-blend-mode: overlay;*/
    /*color: black;*/
    /*color: transparent;*/
    /*color: black;*/
    /*filter: invert(80%);*/
    /*background-clip: text;*/
}

.hero-svg-shadow {
    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));
}

.overlay {
    transition: opacity 1s, z-index 0s 1s;
}

.peer:checked ~ .btn > * {
    visibility: hidden;
}

@keyframes slide {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.animate-slide {
    animation: slide 1500ms ease-in-out infinite;
    animation-delay: 500ms;
}


/* https://tailwindcomponents.com/component/animated-ellipsis */
.loader-dots div {
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.loader-dots div:nth-child(1) {
    left: 8px;
    animation: loader-dots1 0.6s infinite;
}

.loader-dots div:nth-child(2) {
    left: 8px;
    animation: loader-dots2 0.6s infinite;
}

.loader-dots div:nth-child(3) {
    left: 32px;
    animation: loader-dots2 0.6s infinite;
}

.loader-dots div:nth-child(4) {
    left: 56px;
    animation: loader-dots3 0.6s infinite;
}

@keyframes loader-dots1 {
    0% {
        transform: scale(0);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes loader-dots3 {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(0);
    }
}

@keyframes loader-dots2 {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(24px, 0);
    }
}

/* https://css-tricks.com/single-element-loaders-the-dots/ */
.dots-1 {
    width: 60px;
    aspect-ratio: 4;
    background: radial-gradient(circle closest-side, #000 90%, #0000) 0/calc(100% / 3) 100% space;
    clip-path: inset(0 100% 0 0);
    animation: d1 1s steps(4) infinite;
}

@keyframes d1 {
    to {
        clip-path: inset(0 -34% 0 0)
    }
}

.dots-2 {
    width: 60px;
    aspect-ratio: 4;
    background: radial-gradient(circle closest-side, #000 90%, #0000) 0/calc(100% / 3) 100% no-repeat;
    animation: d2 1s steps(3) infinite;
}

@keyframes d2 {
    to {
        background-position: 150%
    }
}

.dots-3 {
    width: 60px;
    aspect-ratio: 2;
    --_g: no-repeat radial-gradient(circle closest-side, #000 90%, #0000);
    background: var(--_g) 0% 50%,
    var(--_g) 50% 50%,
    var(--_g) 100% 50%;
    background-size: calc(100% / 3) 50%;
    animation: d3 1s infinite linear;
}

@keyframes d3 {
    20% {
        background-position: 0% 0%, 50% 50%, 100% 50%
    }
    40% {
        background-position: 0% 100%, 50% 0%, 100% 50%
    }
    60% {
        background-position: 0% 50%, 50% 100%, 100% 0%
    }
    80% {
        background-position: 0% 50%, 50% 50%, 100% 100%
    }
}

.dots-4 {
    width: 60px;
    aspect-ratio: 4;
    background: radial-gradient(circle closest-side at left 6px top 50%, currentColor 90%, #0000),
    radial-gradient(circle closest-side, currentColor 90%, #0000),
    radial-gradient(circle closest-side at right 6px top 50%, currentColor 90%, #0000);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    animation: d4 1s infinite alternate;
}

@keyframes d4 {
    to {
        width: 25px;
        aspect-ratio: 1
    }
}

.dots-5 {
    width: 15px;
    aspect-ratio: 1;
    border-radius: 50%;
    animation: d5 1s infinite linear alternate;
}

@keyframes d5 {
    0% {
        box-shadow: 20px 0 #000, -20px 0 #0002;
        background: #000
    }
    33% {
        box-shadow: 20px 0 #000, -20px 0 #0002;
        background: #0002
    }
    66% {
        box-shadow: 20px 0 #0002, -20px 0 #000;
        background: #0002
    }
    100% {
        box-shadow: 20px 0 #0002, -20px 0 #000;
        background: #000
    }
}

.dots-6 {
    width: 15px;
    aspect-ratio: 1;
    background: #000;
    border-radius: 50%;
    animation: d6 1s infinite linear alternate;
}

@keyframes d6 {
    0% {
        box-shadow: 15px 0, -25px 0
    }
    50% {
        box-shadow: 15px 0, -15px 0
    }
    100% {
        box-shadow: 25px 0, -15px 0
    }
}

.dots-7 {
    width: 60px;
    aspect-ratio: 4;
    --_g: no-repeat radial-gradient(circle closest-side, #000 90%, #0000);
    background: var(--_g) 0% 50%,
    var(--_g) 50% 50%,
    var(--_g) 100% 50%;
    background-size: calc(100% / 3) 100%;
    animation: d7 1s infinite linear;
}

@keyframes d7 {
    33% {
        background-size: calc(100% / 3) 0%, calc(100% / 3) 100%, calc(100% / 3) 100%
    }
    50% {
        background-size: calc(100% / 3) 100%, calc(100% / 3) 0%, calc(100% / 3) 100%
    }
    66% {
        background-size: calc(100% / 3) 100%, calc(100% / 3) 100%, calc(100% / 3) 0%
    }
}

.dots-8 {
    width: 15px;
    aspect-ratio: 1;
    position: relative;
}

.dots-8::before,
.dots-8::after {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 50%;
    background: #000;
}

.dots-8::before {
    box-shadow: -25px 0;
    animation: d8-1 1s infinite linear;
}

.dots-8::after {
    transform: rotate(0deg) translateX(25px);
    animation: d8-2 1s infinite linear;
}

@keyframes d8-1 {
    100% {
        transform: translateX(25px)
    }
}

@keyframes d8-2 {
    100% {
        transform: rotate(-180deg) translateX(25px)
    }
}

.dots-9 {
    width: 15px;
    aspect-ratio: 1;
    position: relative;
    animation: d9-0 1.5s infinite steps(2);
}

.dots-9::before,
.dots-9::after {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 50%;
    background: #000;
}

.dots-9::before {
    box-shadow: 26px 0;
    transform: translateX(-26px);
    animation: d9-1 .75s infinite linear alternate;
}

.dots-9::after {
    transform: translateX(13px) rotate(0deg) translateX(13px);
    animation: d9-2 .75s infinite linear alternate;
}

@keyframes d9-0 {
    0%, 49.9% {
        transform: scale(1)
    }
    50%, 100% {
        transform: scale(-1)
    }
}

@keyframes d9-1 {
    100% {
        box-shadow: 52px 0
    }
}

@keyframes d9-2 {
    100% {
        transform: translateX(13px) rotate(-180deg) translateX(13px)
    }
}

.dots-10 {
    width: 15px;
    aspect-ratio: 1;
    position: relative;
}

.dots-10::before,
.dots-10::after {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 50%;
    background: #000;
}

.dots-10::before {
    box-shadow: -26px 0;
    animation: d10-1 1.5s infinite linear;
}

.dots-10::after {
    transform: rotate(0deg) translateX(26px);
    animation: d10-2 1.5s infinite linear;
}

@keyframes d10-1 {
    50% {
        transform: translateX(26px)
    }
}

@keyframes d10-2 {
    100% {
        transform: rotate(-360deg) translateX(26px)
    }
}

/* needed for transittioning hamburger menu segment collapse */
:root {
    interpolate-size: allow-keywords;
}

/* .blabla-container:has([aria-current="true"]) > .blabla {
    @apply text-slate-800
} */


/*:where([autocomplete=one-time-code]) {*/
/*  --otp-digits: 6;*/
/*  --otp-ls: 2ch;*/
/*  --otp-gap: 1.25;*/

/*  !* private consts *!*/
/*  --_otp-bgsz: calc(var(--otp-ls) + 1ch);*/
/*  --_otp-digit: 0;*/

/*  all: unset;*/
/*  background:*/
/*  linear-gradient(90deg,*/
/*    var(--otp-bg, #BBB) calc(var(--otp-gap) * var(--otp-ls)),*/
/*    transparent 0),*/
/*    linear-gradient(90deg,*/
/*    var(--otp-bg, #EEE) calc(var(--otp-gap) * var(--otp-ls)),*/
/*    transparent 0*/
/*  );*/
/*  background-position: calc(var(--_otp-digit) * var(--_otp-bgsz)) 0, 0 0;*/
/*  background-repeat: no-repeat, repeat-x;*/
/*  background-size: var(--_otp-bgsz) 100%;*/
/*  caret-color: var(--otp-cc, #222);*/
/*  caret-shape: block;*/
/*  clip-path: inset(0% calc(var(--otp-ls) / 2) 0% 0%);*/
/*  font-family: ui-monospace, monospace;*/
/*  font-size: var(--otp-fz, 2.5em);*/
/*  inline-size: calc(var(--otp-digits) * var(--_otp-bgsz));*/
/*  letter-spacing: var(--otp-ls);*/
/*  padding-block: var(--otp-pb, 1ch);*/
/*  padding-inline-start: calc(((var(--otp-ls) - 1ch) / 2) * var(--otp-gap));*/
/*}*/

/*!* For this demo *!*/
/*label span {*/
/*  display: block;*/
/*  font-family: ui-sans-serif, system-ui, sans-serif;*/
/*  font-weight: 500;*/
/*  margin-block-end: 1ch;*/
/*}*/