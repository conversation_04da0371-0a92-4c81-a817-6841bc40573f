{"name": "traveltruster-remix", "private": true, "description": "", "license": "", "sideEffects": false, "type": "module", "scripts": {"test": "vitest", "start": "remix-serve build/server/index.js", "prepare_and_start": "npm run start:prepare && remix-serve build/server/index.js", "start_simple": "remix-serve build/server/index.js", "start:local:prod": "NODE_ENV=production remix vite:build && remix-serve build/server/index.js", "migration:create": "tsx  ./scripts/create-migration.ts", "start:prepare": "tsx -r dotenv/config ./prepare.ts", "start:prepare:prod": "DOTENV_CONFIG_PATH=.env.production tsx -r dotenv/config ./prepare.ts", "build_with_sentrysourcemaps": "NODE_ENV=production tsc -b && vitest run && remix vite:build --sourcemap && sentry-upload-sourcemaps --org traveltruster --project app --auth_token sntrys_eyJpYXQiOjE3MDY0NTA1MDIuNjMyODEzLCJ1cmwiOiJodHRwczovL3NlbnRyeS5pbyIsInJlZ2lvbl91cmwiOiJodHRwczovL3VzLnNlbnRyeS5pbyIsIm9yZyI6InRyYXZlbHRydXN0ZXIifQ", "build": "NODE_ENV=production tsc -b &&  vitest run && remix vite:build && npm run start:prepare", "build_with_prepare": "NODE_ENV=production tsc -b && vitest run && remix vite:build && npm run start:prepare", "build_simple": "NODE_ENV=production tsc -b && vitest run && remix vite:build", "build:remix": "NODE_ENV=production remix vite:build", "dev": "NODE_ENV=development remix vite:dev", "typecheck": "tsc -b", "lingui:extract": "NODE_ENV=development lingui extract", "lingui:compile": "lingui compile", "crowdin": "crowdin", "crowdin:sync": "npm run lingui:extract && crowdin push && crowdin pull", "crowdin:sync:sources": "crowdin push", "crowdin:sync:translations": "crowdin pull"}, "dependencies": {"@aws-sdk/client-sesv2": "^3.433.0", "@date-fns/tz": "^1.2.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@floating-ui/react": "^0.27.13", "@formkit/auto-animate": "^0.8.1", "@google-cloud/tasks": "4.0.1", "@googlemaps/google-maps-services-js": "^3.4.0", "@googlemaps/js-api-loader": "^1.16.6", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.0.18", "@lingui/macro": "^5.2.0", "@lingui/react": "^5.2.0", "@mapbox/mapbox-gl-draw": "^1.4.1", "@markdoc/markdoc": "^0.4.0", "@openreplay/tracker": "^3.5.12", "@react-email/components": "^0.0.32", "@react-email/render": "^1.0.4", "@remix-run/node": "^2.15.1", "@remix-run/react": "^2.15.1", "@remix-run/serve": "^2.15.1", "@sentry/browser": "^8.53.0", "@sentry/node": "^8.53.0", "@sentry/profiling-node": "^8.53.0", "@sentry/remix": "^8.53.0", "@sentry/tracing": "^7.120.3", "@turf/turf": "^6.5.0", "@use-gesture/react": "^10.3.0", "@vercel/remix": "^2.15.1", "bcrypt": "^5.0.1", "core-js": "^3.37.1", "crypto-js": "^4.1.1", "date-fns": "^4.1.0", "firebase": "^11.0.2", "firebase-admin": "^13.0.0", "isbot": "^3.7.0", "kysely": "^0.27.6", "lucide-react": "^0.479.0", "mapbox-gl": "^3.2.0", "mimetext": "3.0.24", "pg": "^8.13.1", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-reverse-portal": "^2.1.1", "recharts": "^2.15.1", "remeda": "^2.21.1", "remix": "^2.15.1", "signature_pad": "^5.0.4", "tailwind-merge": "^2.5.4", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.20.12", "@crowdin/cli": "^3.19.2", "@lingui/cli": "^5.2.0", "@lingui/loader": "^5.2.0", "@lingui/vite-plugin": "^5.2.0", "@remix-run/dev": "^2.15.1", "@remix-run/fs-routes": "^2.15.1", "@remix-run/route-config": "^2.15.1", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.8", "@tailwindcss/typography": "^0.5.15", "@types/bcrypt": "^5.0.0", "@types/core-js": "^2.5.8", "@types/crypto-js": "^4.1.1", "@types/google.maps": "^3.55.5", "@types/mapbox__mapbox-gl-draw": "^1.3.3", "@types/mapbox-gl": "^2.7.11", "@types/pg": "^8.11.10", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "autoprefixer": "^10.4.15", "babel-plugin-macros": "^3.1.0", "concurrently": "^8.2.1", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "eslint": "^8.52.0", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-jsx-a11y": "^6.10.2", "@typescript-eslint/eslint-plugin": "^8.18.2", "eslint-plugin-import": "^2.31.0", "jscodeshift": "^0.15.0", "nodemon": "^3.0.1", "postcss": "^8.4.31", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.6.6", "puppeteer": "^22.6.5", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "ts-reset": "^0.0.1", "tsx": "^4.7.1", "typescript": "5.2.2", "@lingui/babel-plugin-lingui-macro": "^5.2.0", "vite": "^6.2.1", "vite-plugin-babel-macros": "^1.0.6", "vite-plugin-cjs-interop": "^2.1.6", "vite-plugin-commonjs": "^0.10.4", "vite-plugin-node-polyfills": "^0.23.0", "vite-plugin-require": "^1.2.14", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.8"}, "overrides": {"vite-plugin-require": {"vite": "^6.0.0"}, "@lingui/vite-plugin": {"vite": "^6.2.1"}, "@remix-run/dev": {"vite": "^6.2.1"}}, "engines": {"node": "20.x"}}