begin;

select
    participant.id,
    count(distinct booking.id) as booking_count,
    array_agg (distinct booking.id) as booking_ids
from
    participant
    inner join participation on participant.id = participation.participant_id
    inner join activity on participation.activity_id = activity.id
    inner join booking on activity.booking_id = booking.id
group by
    participant.id,
    booking.id
having
    count(booking.id) > 1;

rollback;