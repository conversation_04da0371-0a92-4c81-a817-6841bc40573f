import type { PDFOptions } from "puppeteer-core";

export interface PdfScreenshotArgs {
  url: string;
  fileName?: string;
  inline?: boolean;
  options?: PDFOptions;
  useCustomStorage?: boolean;
  storage?: {
    method: "PUT";
    url: string;
    extraHTTPHeaders?: {};
  };
}

const baseUrl = "https://v2.api2pdf.com";
const apiKey = "263013e9-02bf-444c-b123-d64e62e9485f";

// export const pdfScreenshotDirectBuffer = (args: Args) => {
//   return fetch(`${baseUrl}/chrome/pdf/url?apikey=${apiKey}`, {
//     headers: { "Content-type": "application/json" },
//     body: JSON.stringify(args),
//     method: "POST",
//   });
// };

export const pdfScreenshot = (args: PdfScreenshotArgs) => {
  return fetch(`${baseUrl}/chrome/pdf/url?apikey=${apiKey}`, {
    headers: { "Content-type": "application/json" },
    body: JSON.stringify(args),
    method: "POST",
  });
};
