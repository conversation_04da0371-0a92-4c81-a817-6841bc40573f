export const renderStr = (str: string) => {
  let result = str.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#39;");

  // headings
  result = result.replace(/^(#{1,6}\s.*(?:\n|$))/gm, (match, p1, p2, p3) => {
    return `<mark style="background-color: transparent; color: blue;">${match}</mark>`;
  });

  // bold
  result = result.replace(/\*\*\s?([^\n]+?)\*\*/g, (match) => `<mark style="background-color: yellow; color: inherit">${match}</mark>`);

  // italic
  result = result.replace(
    /(?<!\*)\*(?!\*)[^*]+\*(?!\*)/g,
    (match) => `<mark style="background-color: orange; color: inherit">${match}</mark>`,
  );

  // tag
  result = result.replace(/\{%.*?%\}/g, (match) => `<mark style="background-color: #ddd; outline: 1px solid #ddd;">${match}</mark>`);

  // variable
  result = result.replace(/\$\w+/g, (match) => `<mark style="color: red; background-color: transparent;">${match}</mark>`);

  // image
  result = result.replace(
    /\!\[([^\]]+)\]\((\S+)\)/g,
    (match) => `<mark style="background-color: #ddd; outline: 1px solid #ddd;">${match}</mark>`,
  );
  return result;
};
