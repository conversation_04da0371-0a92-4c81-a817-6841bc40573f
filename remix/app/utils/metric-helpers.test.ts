import { describe, expect, test } from "vitest";
import { convertHeight, convertWeight } from "./metric-helpers";

describe("convertHeight", () => {
  test("should correctly convert from cm to ft", () => {
    const actual = convertHeight(1, "inch", "cm");
    const expected = 2.54; //roughly equal to 120 cm
    expect(actual).toBeCloseTo(expected, 4);
  });

  test("should correctly convert from ft to cm", () => {
    const actual = convertHeight(1, "ft", "cm");
    const expected = 30.48; //1 ft equal to 30.48 cm
    expect(actual).toBeCloseTo(expected, 0);
  });

  test("should correctly convert from cm to inches", () => {
    const actual = convertHeight(100, "cm", "inch");
    const expected = 39.3701; //roughly equal to 100 cm
    expect(actual).toBeCloseTo(expected, 0);
  });

  test("should correctly convert from inch to cm", () => {
    const actual = convertHeight(1, "inch", "cm");
    const expected = 2.54; //1 inch equal to 2.54 cm
    expect(actual).toBeCloseTo(expected, 4);
  });

  test("should correctly convert from inch to ft", () => {
    const actual = convertHeight(1, "ft", "inch");
    const expected = 12;
    expect(actual).toBe(expected);
    expect(actual).toBeCloseTo(expected, 4);
  });
});

describe("convertWeight", () => {
  test("should correctly convert from kg to lbs", () => {
    const actual = convertWeight(1, "kg", "lbs");
    const expected = 2.20462; //roughly equal to 1 kg
    expect(actual).toBeCloseTo(expected, 1);
  });

  test("should correctly convert from lbs to kg", () => {
    const actual = convertWeight(1, "lbs", "kg");
    const expected = 0.453592; //roughly equal to 1 lbs
    expect(actual).toBeCloseTo(expected, 2);
  });

  test("should correctly convert from st to kg", () => {
    const actual = convertWeight(1, "st", "kg");
    const expected = 6.35029; //roughly equal to 1 st
    expect(actual).toBeCloseTo(expected, 1);
  });

  test("should correctly convert from kg to st", () => {
    const actual = convertWeight(1, "kg", "st");
    const expected = 0.157473; //roughly equal to 1 kg
    expect(actual).toBeCloseTo(expected, 1);
  });

  test("should correctly convert from lbs to st", () => {
    const actual = convertWeight(14, "lbs", "st");
    const expected = 1; //roughly equal to 14 lbs
    expect(actual).toBeCloseTo(expected, 0);
  });
});
