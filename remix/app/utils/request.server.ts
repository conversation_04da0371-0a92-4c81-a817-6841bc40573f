import { activeLanguages } from "~/data/languages";
import { getClientLocales } from "~/misc/utils";
import { defaultLangauge } from "~/misc/vars";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";

export const getLanguage = (request: Request) => {
  const url = new URL(request.url);
  const record = paramsToRecord(url.searchParams);
  const urlLocale = activeLanguages.find((lang) => lang === record.lang);
  const clientLocale = (getClientLocales(request)?.find((locale) => activeLanguages.includes(locale)) ||
    "en") as (typeof activeLanguages)[number];
  return urlLocale || clientLocale || defaultLangauge;
};

// the webapp has some pages were it redirects in case of an error, for the mobile app that might not desirable
export const getAllowRedirect = (request: Request) => request.headers.get("X-Redirect")?.toLowerCase() !== "prevent";
