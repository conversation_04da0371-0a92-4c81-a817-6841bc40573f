const allowedHostEnds = [".diversdesk.com", ".diversdesk.localhost:3000", "-diversdesk.dinkel.works"];

export const getWhitelabelFromHost = (host: string | null) => {
  if (!host) return null;
  const prefix = allowedHostEnds
    .map((hostEnd) => (host.endsWith(hostEnd) ? host.replace(hostEnd, "").replace(".test", "") : null))
    .find((prefix) => prefix);

  return prefix || null;
};

export const isDiversdesk = (host: string | null) => !!host && !!allowedHostEnds.find((allowedHost) => host.endsWith(allowedHost));
