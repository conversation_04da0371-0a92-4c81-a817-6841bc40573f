import { countries, defaultCountry } from "~/data/countries";

export const getDefaultCountry = (headers: Headers) => {
  const languageHeader = headers.get("Accept-language");

  return (
    (languageHeader &&
      countries
        .map((country) => ({
          country: country,
          index: languageHeader.indexOf(country.country_code),
        }))
        .filter((item) => item.index >= 0)
        .sort((a, b) => a.index - b.index)[0]?.country) ||
    defaultCountry
  );
};
