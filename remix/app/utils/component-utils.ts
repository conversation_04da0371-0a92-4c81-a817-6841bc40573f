import React from "react";

export const disableScrollOnNumberInput = (event: React.WheelEvent<HTMLInputElement>) => {
  const target = event.currentTarget;
  target.blur();
  requestAnimationFrame(() => {
    target.focus();
  });
};

export const showSiblingSelectPicker = (e: React.MouseEvent<HTMLButtonElement>) => {
  const select = e.currentTarget.nextElementSibling;
  if (select instanceof HTMLSelectElement) {
    (select as any).showPicker();
  }
};
