interface User {
  email?: string | null;
  display_name?: string | null;
}

export const formatUsername = (user?: User | null): string => {
  return `${user?.email || "unkown"}(${user?.display_name})`;
};

export const capitalize = (str: string) => str.charAt(0).toUpperCase() + str.slice(1);

export const strictWhatsapp = (whatsapp: string): string => {
  const trimmed = whatsapp.trim();
  const start = trimmed.startsWith("0") ? trimmed.replace("0", "+") : trimmed;
  return start.replace(/\s|-/g, "");
};

export const prettyWhatsapp = (whatsapp: string): string => {
  const strictNr = strictWhatsapp(whatsapp);
  const prefix = strictNr.slice(0, 3);
  const nextSegments = [strictNr.slice(3, 6), strictNr.slice(6, 9), strictNr.slice(9)];
  const dashed = nextSegments.filter((segment) => !!segment).join("-");
  return `${prefix} ${dashed}`;
};
