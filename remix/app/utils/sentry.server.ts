import * as Sentry from "@sentry/remix";
import { environment } from "~/config/environment.server";

Sentry.init({
  enabled: environment === "production" || environment === "preview",
  // enabled: false,
  dsn: "https://<EMAIL>/4506127796207616",
  // integrations: [Sentry.browserTracingIntegration({}), Sentry.browserProfilingIntegration()],
  // integrations: [new ProfilingIntegration() as any, ...Sentry..autoDiscoverNodePerformanceMonitoringIntegrations()],
  environment: environment,
  tracesSampleRate: 1.0,
  profilesSampleRate: 1.0,
});

export const sendToSentry = async (request: Request, error: unknown) => {
  if (error instanceof Error) {
    await Sentry.captureRemixServerException(error, "remix.server", request);
  } else {
    // Optionally capture non-Error objects
    Sentry.captureException(error);
  }
};
