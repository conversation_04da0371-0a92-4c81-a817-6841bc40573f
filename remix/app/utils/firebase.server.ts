import admin from "firebase-admin";
import { initializeApp as initializeAdminApp } from "firebase-admin/app";
import { appConfig } from "~/config/config.server";
import { convertToFirestorePath } from "~/misc/paths";
import { notFoundOrUnauthorzied } from "~/misc/responses";

const initFirebaseApp = async () => {
  if (!admin.apps.length) {
    initializeAdminApp({
      storageBucket: appConfig.PUBLIC.firebase_singapore.storageBucket,
      credential: admin.credential.cert(appConfig.firebase_singapore),
    });
  }
};

export const getAdminStorageSingapore = async () => {
  await initFirebaseApp();
  return admin.storage();
};

export const getAdminMessaging = async () => {
  await initFirebaseApp();
  return admin.messaging();
};

export const createSignedWriteUrl = async (path: string) => {
  const fbStorage = await getAdminStorageSingapore();
  const bucket = await fbStorage.bucket();

  const fileRef = bucket.file(path);
  const signedUrl = await fileRef.getSignedUrl({
    action: "write",
    version: "v4",
    expires: Date.now() + 1000 * 20,
  });

  const finalSignedUrl = signedUrl && signedUrl[0];

  if (!finalSignedUrl) throw notFoundOrUnauthorzied("could not create signed write url");
  return finalSignedUrl;
};

export const validateGoogleToken = async (token: string) => {
  await initFirebaseApp();
  const adminAuth = admin.auth();
  return adminAuth.verifyIdToken(token);
};

export const notifySession = async (sessionId: string, clientId: string) => {
  await initFirebaseApp();
  const adminFirestore = admin.firestore();
  return adminFirestore
    .collection("session")
    .doc(sessionId)
    .collection("changes")
    .doc()
    .create({ timestamp: new Date(), client_id: clientId });
};

export const notifyPage = async (path: string, clientId?: string | null) => {
  await initFirebaseApp();
  const adminFirestore = admin.firestore();
  return adminFirestore
    .collection(convertToFirestorePath(path))
    .doc()
    .create({
      timestamp: new Date(),
      client_id: clientId || null,
    });
};
