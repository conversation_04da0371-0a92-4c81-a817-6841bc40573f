export const getParamKeysStartingWith = (params: URLSearchParams, start: string) => {
  const keys: string[] = [];
  params.forEach((value, key) => {
    if (key.startsWith(start)) {
      keys.push(key);
    }
  });
  const remove = () => {
    keys.forEach((key) => {
      params.delete(key);
    });
  };
  return {
    keys: keys,
    remove: remove,
  };
};

export const hasKeyValue = (params: URLSearchParams, key: string, value: string) => {
  return !!params.getAll(key).find((keyValue) => keyValue === value);
};

export const removeParamByKeyValue = (params: URLSearchParams, key: string, value: string) => {
  const values = params.getAll(key);
  params.delete(key);
  values.forEach((pValue) => {
    if (pValue !== value) {
      params.append(key, pValue);
    }
  });
};

export const toggleSingleParam = (params: URLSearchParams, key: string, value: string, enable: boolean) => {
  if (enable) {
    params.set(key, value);
  } else {
    params.delete(key);
  }
};

export const toggleParamByKeyValue = (params: URLSearchParams, key: string, value: string, enable: boolean) => {
  if (enable) {
    params.append(key, value);
  } else {
    removeParamByKeyValue(params, key, value);
  }
};
