import { useEffect, useMemo, useState } from "react";
import { useMatches } from "@remix-run/react";
import { defaultPageOverwriteVars } from "~/misc/consts";

export const useWindowLocation = () => {
  const [location, setLocation] = useState<{ origin: string; href: string }>({
    origin: "",
    href: "",
  });
  // const ref = useRef<{origin: string, href: string}>({origin: '', href: ''});
  useEffect(() => {
    setLocation(window.location);
  }, []);
  return location;
};

export const mergeMatchData = <T extends Record<string, any> = Record<string, any>>(matches: { data?: any }[]): T => {
  return matches.reduce((acc, match) => {
    const newData = typeof match.data === "object" ? match.data : {};
    return { ...acc, ...newData };
  }, {}) as T;
};

export const useMergedMatchData = <T extends Record<string, any> = Record<string, any>>(): T => {
  const matches = useMatches();
  return useMemo(() => mergeMatchData<T>(matches), [matches]);
};

export const usePageOverwrites = () => {
  const matches = useMatches();
  return useMemo(() => {
    return { ...defaultPageOverwriteVars, ...mergeMatchData<typeof defaultPageOverwriteVars>(matches) };
  }, [matches]);
};
