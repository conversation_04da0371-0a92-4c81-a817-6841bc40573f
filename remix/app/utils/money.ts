import { defaultLocale } from "~/misc/vars";
import { locales } from "~/data/languages";

const currencyFormatters: Record<string, Intl.NumberFormat> = {};

interface NumberFormatOptions {
  localeMatcher?: string | undefined;
  style?: string | undefined;
  currency?: string | undefined;
  currencySign?: string | undefined;
  useGrouping?: boolean | undefined;
  minimumIntegerDigits?: number | undefined;
  minimumFractionDigits?: number | undefined;
  maximumFractionDigits?: number | undefined;
  minimumSignificantDigits?: number | undefined;
  maximumSignificantDigits?: number | undefined;
}

const defaultFormat = new Intl.NumberFormat("nl-NL", {
  // style: "currency",
  notation: "standard",
  compactDisplay: "long",
  maximumFractionDigits: 0,
  minimumFractionDigits: 0,
  // maximumSignificantDigits: maxFraction,
  // currency: currency,
});

// const getNumberFormat = (options: NumberFormatOptions) => {
// }

// const getOrCreateFormatted = (currency: string)

const moneyLong = (currency: string, localeCode: string | null) => {
  const localeObj = locales.find((locale) => locale.code === localeCode);
  const finalLocale = localeObj?.code || defaultLocale;
  const formatter = currencyFormatters[currency + finalLocale];
  if (formatter) return formatter;
  const newFormatter = new Intl.NumberFormat(finalLocale, {
    style: "currency",
    notation: "standard",
    compactDisplay: "long",
    maximumFractionDigits: 2,
    minimumFractionDigits: 0,
    // maximumSignificantDigits: maxFraction,
    currency: currency,
  });
  currencyFormatters[currency] = newFormatter;
  return newFormatter;
};

export const formatPrice = (amount: number, currency: string, locale: string | null) => {
  const formatter = moneyLong(currency, locale);
  return formatter.format(amount);
};

export const moneyCompact = (currency: string) =>
  new Intl.NumberFormat("nl-NL", {
    style: "currency",
    notation: "compact",
    currency: currency,
  });

interface BareARgs {
  nativeAmount: number;
  nativeCurrency: string;
  toCurrency?: string | null;
}

export interface ArgsWithFormat extends BareARgs {
  locale: string | null;
}

export interface MoneyContext {
  currencies: { id: string; conversion_rate_usd: number }[];
}

export const convertMoney = (context: MoneyContext, args: BareARgs) => {
  const fromCurrency = context.currencies.find((currency) => currency.id === args.nativeCurrency);
  const toCurrency = context.currencies.find((currency) => currency.id === args.toCurrency);
  if (!fromCurrency || !toCurrency) throw new Error(`from ${fromCurrency} or to ${toCurrency} is invalid currency`);
  return args.toCurrency === args.nativeCurrency
    ? args.nativeAmount
    : (args.nativeAmount / Number(fromCurrency.conversion_rate_usd)) * Number(toCurrency.conversion_rate_usd);
};

export const formatMoney = (context: MoneyContext, args?: ArgsWithFormat) => {
  if (!args) return null;
  try {
    const convertedPrice = convertMoney(context, args);
    return formatPrice(convertedPrice, args.toCurrency || "", args.locale);
  } catch (e) {
    console.error(e);
    return null;
  }
};
