import { heightMetrics, shoeSizeUnits, weightMetrics } from "~/data/countries";
import { keys } from "~/misc/helpers";
import { shoeSizesConversions } from "~/data/shoesize-conversions";

export const roundToMaxDecimals = (value: number, maxDecimals: number): number => {
  const factor = Math.pow(10, maxDecimals);
  return Math.round(value * factor) / factor;
};

export const strToHeightUnit = (str: string | null | undefined) =>
  keys(heightMetrics).find((unit) => unit === str) || ("cm" satisfies keyof typeof heightMetrics);

export const convertHeight = (value: number, from: keyof typeof heightMetrics, to: keyof typeof heightMetrics) => {
  // if (!to || from === to) return value;
  const fromToCmMultiplier = heightMetrics[from].conversion_to_cm;
  const cmToToDivider = heightMetrics[to].conversion_to_cm;

  const fromValueInCm = value * fromToCmMultiplier;
  const convertedValue = fromValueInCm / cmToToDivider;
  const decimals = convertedValue < 3 ? 2 : convertedValue < 10 ? 1 : 0;
  return roundToMaxDecimals(convertedValue, decimals);
};

export const strToWeightUnit = (str: string | null | undefined) =>
  keys(weightMetrics).find((unit) => unit === str) || ("kg" satisfies keyof typeof weightMetrics);

export const convertWeight = (value: number, from: keyof typeof weightMetrics, to: keyof typeof weightMetrics) => {
  // if (!to || from === to) return value;
  const fromToKgMultiplier = weightMetrics[from].conversion_to_kg;
  const kgToToDivider = weightMetrics[to].conversion_to_kg;

  const fromValueInKg = value * fromToKgMultiplier;

  const convertedValue = fromValueInKg / kgToToDivider;
  const decimals = convertedValue < 3 ? 2 : convertedValue < 10 ? 1 : 0;
  return roundToMaxDecimals(convertedValue, decimals);
};

export const strToShoesizeUnit = (str: string | null | undefined) =>
  keys(shoeSizeUnits).find((unit) => unit === str) || ("EU" satisfies keyof typeof shoeSizeUnits);

export const convertShoesize = (value: number, from: keyof typeof shoeSizeUnits, to: keyof typeof shoeSizeUnits) => {
  const foundConversion = shoeSizesConversions.find((conversions) => conversions[from] === value);
  return foundConversion?.[to] || null;
  // const valueInCm = shoeSizeUnits[from].to_cm(value);
  // const valueInTo = shoeSizeUnits[to].from_cm(valueInCm);
  //
  // return valueInTo;
  // const decimals = value <= 10 ? 1 : 0;
  // const roundedValue = roundToMaxDecimals(valueInTo, decimals);
  // return value <= 10 ? Math.round(roundedValue * 2) / 2 : roundedValue;
};
