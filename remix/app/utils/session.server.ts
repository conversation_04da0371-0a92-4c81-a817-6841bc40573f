import type { SessionStorage } from "@remix-run/server-runtime";
import { createSessionStorage } from "@remix-run/node";
import { getDefaultCountry } from "~/utils/currency";
import { kysely } from "~/misc/database.server";
import { addToNow, arrayAgg, atInfinityLiteralFn, notNull, nowValue } from "~/kysely/kysely-helpers";
import { z } from "zod";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { defaultCurrency } from "~/misc/vars";
import { featureKeys } from "~/domain/feature/feature";
import { fileTargetsQb } from "~/domain/file/file-resource";

import { environment } from "~/config/environment.server";
import { appConfig } from "~/config/config.server";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { otpValidDurtionInMinutes } from "~/domain/otp/otp-vars";

const sessionOutputParser = z.object({
  anonymous: z.boolean().default(false),
  selected_user_id: z.string().nullish(),
  env_verified: z.boolean().default(false),
  currency_switched: z.boolean().default(false),
});

const sessionInputParser = sessionOutputParser.partial();

const dbSessionStorage: SessionStorage = createSessionStorage({
  cookie: {
    name: "__session",
    // normally you want this to be `secure: true`
    // but that doesn't work on localhost for Safari
    // https://web.dev/when-to-use-local-https/
    secure: environment !== "development",
    secrets: [appConfig.SESSION_SECRET + "reset"],
    sameSite: "lax",
    path: "/",
    maxAge: 60 * 60 * 24 * 30,
    httpOnly: true,
  },
  createData: async (data, expires) => {
    const parsed = sessionInputParser.parse(data);
    const session = await kysely
      .insertInto("session")
      .values({ ...parsed, expires: expires })
      .returning("id")
      .executeTakeFirstOrThrow();
    console.log("session created", session.id);
    return session.id;
  },
  readData: async (id) => {
    const result = await kysely
      .selectFrom("session")
      .where("id", "=", id)
      .where("expires", ">", () => nowValue)
      .where("destroyed_at", "=", atInfinityLiteralFn)
      .selectAll()
      .limit(1)
      .executeTakeFirst();
    const parsed = sessionOutputParser.safeParse(result);
    // console.log("session read", result?.id);
    return parsed.success && result ? parsed.data : { valid: false };
  },
  updateData: async (id, data, expires) => {
    const parsed = sessionInputParser.parse(data);
    await kysely
      .updateTable("session")
      .set({ ...parsed, expires: expires, updated_at: nowValue })
      .where("id", "=", id)
      .where("destroyed_at", "=", atInfinityLiteralFn)
      .executeTakeFirst();
  },
  deleteData: async (id) => {
    await kysely.updateTable("session").set({ destroyed_at: nowValue }).where("id", "=", id).executeTakeFirst();
  },
});

const createResponseInit = (...cookies: string[]) => {
  const headers: [string, string][] = cookies.map((cookie) => ["Set-Cookie", cookie]);
  return {
    headers: headers,
  };
};

const fullUserSessionsQb = (sessionId: string) =>
  kysely
    .selectFrom("session")
    .innerJoin("user_session", "user_session.session_id", "session.id")
    .leftJoin("user", "user.id", "user_session.user_id")
    .where("session.id", "=", sessionId)
    .where("user_session.destroyed_at", "=", atInfinityLiteralFn)
    .where((eb) => eb.or([eb("user.deleted_at", "=", atInfinityLiteralFn), eb("user_session.user_id", "is", null)]))
    .select((eb) => [
      "session.id",
      "session.id as session_id",
      // "user_session.currency_switched",
      "user_session.id as user_session_id",
      "user_session.user_id",
      "user_session.verified_at",
      "user_session.method",
      // eb
      //   .exists(
      //     eb
      //       .selectFrom("one_time_password")
      //       .where("one_time_password.user_session_id", "=", eb.ref("user_session.id"))
      //       .where("one_time_password.created_at", ">", addToNow(-5, "minutes")),
      //   )
      //   .as("otp_available"),
      "user.email",
      "user.admin",
      "user.editor",
      "user.currency_id",
      "user.display_name",
      "user_session.display_name as user_session_display_name",
      "user.features",
      // jsonObjectFrom(
      //   eb
      //     .selectFrom("user")
      //     .whereRef("user.id", "=", "user_session.user_id")
      //     .select(["user.id", "user.display_name", "user.currency_id", "user.features", '']),
      // ).as("user"),
      jsonArrayFrom(
        eb
          .selectFrom("member")
          .selectAll("member")
          .select((eb) => [
            notNull(
              jsonObjectFrom(
                eb
                  .selectFrom("establishment")
                  .innerJoin("operator", "operator.id", "establishment.operator_id")
                  .leftJoin("spot", "spot.id", "establishment.spot_id")
                  .leftJoin("region", "region.id", "spot.region_id")
                  .where("establishment.id", "=", eb.ref("member.establishment_id"))
                  .select((eb) => [
                    "establishment.id",
                    "establishment.operator_id",
                    "establishment.location_name",
                    "operator.slug as operator_slug",
                    "operator.name as operator_name",
                    "region.timezone",
                    "spot.name as spot_name",
                    "operator.enterprise",
                    "establishment.workflow",
                    jsonArrayFrom(fileTargetsQb(kysely, "establishment", eb.ref("establishment.id"))).as("files"),
                  ]),
              ),
            ).as("establishment"),
          ])
          .where("member.deleted_at", "=", at_infinity_value)
          .whereRef("member.user_id", "=", "user_session.user_id"),
      ).as("members"),
    ])
    .orderBy((eb) => eb("user_session.user_id", "is", null), "desc")
    .orderBy("user_session.created_at");

const getUserSessions = async (sessionId?: string | null) => (sessionId ? fullUserSessionsQb(sessionId).execute() : null);

const getActiveSession = async (sessionId?: string | null) => {
  if (sessionId) {
    const foundSession = await kysely
      .selectFrom("session")
      .where("session.id", "=", sessionId)
      .select((eb) => [
        "session.id",
        "session.id as session_id",
        "session.currency_switched",
        "session.main_menu_pinned",
        "session.selected_user_id",
        "session.env_verified",
        "session.features",
        "session.flash_success_message",
        jsonArrayFrom(
          eb
            .selectFrom("booking")
            .where("booking.cart_for_session_id", "=", eb.ref("session.id"))
            .selectAll("booking")
            .select((eb) => [
              jsonArrayFrom(
                eb
                  .selectFrom("sale_item")
                  .selectAll("sale_item")
                  .whereRef("sale_item.booking_id", "=", "booking.id")
                  .select((eb) => [
                    eb
                      .selectFrom("participation")
                      .where("participation.sale_item_id", "=", eb.ref("sale_item.id"))
                      .select((eb) => arrayAgg(eb.ref("participation.id"), "uuid").as("ids"))
                      .as("participation_ids"),
                    jsonObjectFrom(
                      eb
                        .selectFrom("product")
                        .innerJoin("item", "item.id", "product.item_id")
                        .selectAll("item")
                        .selectAll("product")
                        .select((eb) => [jsonArrayFrom(fileTargetsQb(kysely, "product", eb.ref("product.id"))).as("files")])
                        .whereRef("product.id", "=", "sale_item.product_id"),
                    ).as("product"),
                  ]),
              ).as("activities"),
            ]),
        ).as("cart_bookings"),
        jsonArrayFrom(
          eb
            .selectFrom("one_time_password")
            .where("one_time_password.session_id", "=", eb.ref("session.id"))
            .select((eb) => [
              "one_time_password.verified_at",
              "one_time_password.created_at",
              "one_time_password.id",
              "one_time_password.user_id",
              eb("one_time_password.created_at", ">", addToNow(-otpValidDurtionInMinutes, "minutes")).as("active"),
            ])
            .orderBy("one_time_password.created_at desc"),
        ).as("otp"),
      ])
      .limit(1)
      .executeTakeFirst();
    if (foundSession) return foundSession;
  }
  return {
    id: "",
    session_id: "",
    env_verified: false,
    currency_switched: false,
    selected_user_id: null,
    main_menu_pinned: false,
    features: null,
    anonymous: false,
    otp: null,
    cart_bookings: [],
    flash_success_message: null,
  };
};

export const getOrCreateSession = async (request: Request) => {
  const cookie = request.headers.get("Cookie");

  let dbSession = await dbSessionStorage.getSession(cookie);
  const isValidSession = dbSession.data?.valid !== false && !!dbSession.id;

  let session_id = isValidSession ? dbSession.id : "";
  let init = undefined;
  if (!isValidSession) {
    dbSession = await dbSessionStorage.getSession();
    const cookieHeader = await dbSessionStorage.commitSession(dbSession);
    const createdSession = await dbSessionStorage.getSession(cookieHeader);
    session_id = createdSession.id;
    init = createResponseInit(cookieHeader);
  }

  if (!session_id) throw new Error("Could not create session");

  return {
    session: dbSession,
    session_id: session_id,
    init: init,
  };
};

export const getSessionSimple = async (request: Request) => {
  const cookie = request.headers.get("Cookie");
  if (environment === "development" && false) {
    await new Promise((resolve) => setTimeout(resolve, 700));
  }

  const dbSession = await dbSessionStorage.getSession(cookie);
  return { session_id: dbSession.id || null, session: dbSession };
};

export const getSession = async (request: Request) => {
  const cookie = request.headers.get("Cookie");
  if (environment === "development" && false) {
    await new Promise((resolve) => setTimeout(resolve, 700));
  }

  const dbSession = await dbSessionStorage.getSession(cookie);

  const [userSessions, activeSession] = await Promise.all([getUserSessions(dbSession.id), getActiveSession(dbSession.id)]);
  const selectedUserSession = userSessions?.find((us) => us.user_id === activeSession.selected_user_id);
  const country = getDefaultCountry(request.headers);
  const browserCurrency = country.currency_code;
  const isVerified = !!selectedUserSession?.verified_at;
  const userCurrency = selectedUserSession?.currency_id || browserCurrency;
  const isCurrencySwitched = activeSession.currency_switched;

  const members = (isVerified && selectedUserSession?.members) || [];
  const myEstablishmentIds = members.filter((member) => member.owner).map((item) => item.establishment_id);

  const featuresRaw = (isVerified ? selectedUserSession.features : null) || activeSession.features || [];

  if (activeSession.flash_success_message) {
    await kysely
      .updateTable("session")
      .set({ flash_success_message: null })
      .where("session.id", "=", activeSession.session_id)
      .executeTakeFirstOrThrow();
  }

  return {
    // sesion: dbSession,
    context: {
      session_id: activeSession.session_id,
      main_menu_pinned: activeSession.main_menu_pinned,
      otp: activeSession.otp,
      flash_success_message: activeSession.flash_success_message,
      verified_at: selectedUserSession?.verified_at,
      features: featureKeys.filter((feature) => featuresRaw.includes(feature)),
      user_id: selectedUserSession?.user_id || "",
      user_session_id: selectedUserSession?.user_session_id || "",
      email: selectedUserSession?.email || "",
      cart_bookings: activeSession.cart_bookings,
      verification_method: selectedUserSession?.method,
      // otp_available: selectedUserSession?.otp_available,
      display_name: (isVerified ? selectedUserSession?.display_name : selectedUserSession?.user_session_display_name) || "",
      currency_id: isVerified ? selectedUserSession?.currency_id : undefined,
      // admin: isVerified && !!selectedUserSession?.admin,
      editor: isVerified && !!selectedUserSession?.editor,
      env_verified: activeSession.env_verified,
      user_sessions: userSessions || [],
      members: members,
      establishment_ids: myEstablishmentIds,
      currency: {
        user: userCurrency,
        switched: isCurrencySwitched,
        final: isCurrencySwitched ? userCurrency : defaultCurrency,
      },
      default_country: country,
    },
  };
};
