import { RemixBrowser } from "@remix-run/react";
import { startTransition, StrictMode } from "react";
import { hydrateRoot } from "react-dom/client";
// import { environment } from "./config/environment.server";
//
// Sentry.init({
//   dsn: "https://<EMAIL>/****************",
//   enabled: process.env.NODE_ENV === "production",
//   // environment:
//   integrations: [
//     new Sentry.BrowserTracing({
//       routingInstrumentation: Sentry.remixRouterInstrumentation(useEffect, useLocation, useMatches),
//     }),
//     new Sentry.BrowserProfilingIntegration(),
//     new Sentry.Replay(),
//   ],
//   tracesSampleRate: 1.0,
//   environment: "unkown",
//   tracePropagationTargets: ["localhost", /^https:\/\/traveltruster\.com\//],
//   replaysSessionSampleRate: 0.1,
//   replaysOnErrorSampleRate: 1.0,
// });

startTransition(() => {
  hydrateRoot(
    document,
    <StrictMode>
      <RemixBrowser />
    </StrictMode>,
  );
});
