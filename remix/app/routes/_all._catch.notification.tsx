import { Outlet } from "@remix-run/react";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _establishment_paths, _notification, _notification_sent, _notification_settings } from "~/misc/paths";
import { EstablishmentLayout } from "../components/AllowedForEstablishment";
import { SettingsIcon } from "lucide-react";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { useAppContext } from "~/hooks/use-app-context";

export { action } from "~/routes/_all._catch.resource";

export default function NotificationLayout() {
  const ctx = useAppContext();

  return (
    <EstablishmentLayout title={<h1 className="text-xl font-semibold mb-4">Notifications</h1>}>
      <div className="flex gap-2 mb-6 border-b">
        <ParamLink
          path={_notification}
          end
          className={`px-4 py-2 border-b-2 transition-colors aria-current:border-secondary aria-current:text-secondary aria-current:font-medium border-transparent text-gray-600 hover:text-gray-800`}
        >
          Upcoming
        </ParamLink>
        <ParamLink
          path={_notification_sent}
          className={`px-4 py-2 border-b-2 transition-colors aria-current:border-secondary aria-current:text-secondary aria-current:font-medium border-transparent text-gray-600 hover:text-gray-800`}
        >
          Sent
        </ParamLink>
        <div className="flex-1"></div>
        {ctx.establishment && (
          <ParamLink className="text-slate-500 btn" path={"settings"}>
            <SettingsIcon className="w-4 h-4" />
          </ParamLink>
        )}
      </div>
      <Outlet />
    </EstablishmentLayout>
  );
}
