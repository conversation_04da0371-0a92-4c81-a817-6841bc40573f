import type { LoaderFunctionArgs } from "@remix-run/server-runtime";
import { useLoaderData } from "@remix-run/react";
import { Backbutton } from "~/components/base/base";
import { kysely } from "~/misc/database.server";
import { SubmitButton } from "~/components/base/Button";
import React, { Fragment } from "react";
import { _payment_method } from "~/misc/paths";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { RInput, RLabel, RSelect } from "~/components/ResourceInputs";
import { ActionForm } from "~/components/form/BaseFrom";
import { getSessionSimple } from "~/utils/session.server";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { notFound } from "~/misc/responses";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { at_infinity_value, at_now_value } from "~/kysely/db-static-vars";
import { ParamLink } from "~/components/meta/CustomComponents";
import { Checker } from "~/components/Checker";
import { activeUserSessionQb } from "~/domain/member/member-queries.server";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const ctx = await getSessionSimple(request);
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  // const userSessionIds = kysely
  //   .selectFrom("member")
  //   .where("member.owner", "=", true)
  //   .innerJoin("user", "user.id", "member.user_id")
  //   .innerJoin("user_session", "user_session.user_id", "user.id")
  //   .select("user_session.id");

  const establishmentQb = simpleEstablishmentQb.select((eb) => [
    "establishment.xendit_account_id",
    // jsonArrayFrom(
    //   eb
    //     .selectFrom("member")
    //     .where("member.establishment_id", "=", eb.ref("establishment.id"))
    //     .where("member.owner", "=", true)
    //     .innerJoin("user", "user.id", "member.user_id")
    //     .innerJoin("user_session", "user_session.user_id", "user.id")
    //     .innerJoin("intuit_connection", "intuit_connection.created_by_user_session_id", "user_session.id")
    //     .select(["intuit_connection.id", "intuit_connection.intuit_company", "intuit_connection.intuit_user"]),
    // ).as("available_intuit_connections"),
  ]);

  const myUserSessionId = activeUserSessionQb({ trx: kysely, ctx }, true).select("_user_session.user_id");
  const paymentMethodByIdQb = kysely.selectFrom("payment_method").where("payment_method.id", "=", state.id);

  const establishmentId = state.persist_establishment_id;

  const establishmentInuitConnectionIds = kysely
    .selectFrom("payment_method")
    .where("payment_method.establishment_id", "=", establishmentId)
    .select("payment_method.intuit_connection_id");

  const result = await kysely
    .selectNoFrom([
      jsonArrayFrom(
        kysely
          .selectFrom("intuit_connection")
          .innerJoin("user_session", "user_session.id", "intuit_connection.created_by_user_session_id")
          .where("intuit_connection.deleted_at", "=", at_infinity_value)
          .where((eb) =>
            eb.or([
              eb("intuit_connection.id", "=", paymentMethodByIdQb.select("payment_method.intuit_connection_id")),
              eb("intuit_connection.id", "in", establishmentInuitConnectionIds),
              eb("user_session.user_id", "=", myUserSessionId),
            ]),
          )
          .select(["intuit_connection.id", "intuit_connection.intuit_company", "intuit_connection.intuit_user"]),
      ).as("available_intuit_connections"),
      jsonObjectFrom(establishmentQb.where("establishment.id", "=", establishmentId)).as("establishment"),
      jsonObjectFrom(
        paymentMethodByIdQb
          .selectAll("payment_method")
          .select((eb) => [
            jsonObjectFrom(establishmentQb.where("establishment.id", "=", eb.ref("payment_method.establishment_id"))).as("establishment"),
          ]),
      ).as("payment_method"),
    ])
    .executeTakeFirstOrThrow();

  const establishment = result.payment_method?.establishment || result.establishment;
  if (!establishment) throw notFound("establishment not found");
  return {
    ...result,
    establishment: establishment,
  };
};

export default function Page() {
  const response = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  return (
    <div className="app-container space-y-3">
      <div>
        <h1 className="text-xl font-bold first-letter:capitalize">
          {response.payment_method ? "Edit Payment Method" : "Create Payment Method"}
        </h1>
        <h2 className="text-slate-600">{getEstablishmentName(response.establishment)}</h2>
      </div>
      <div className="w-fit space-y-7">
        <ActionForm className="flex flex-wrap gap-3">
          <RedirectParamsInput path={_payment_method} paramState={{ persist_establishment_id: response.establishment.establishment_id }} />
          <RInput
            table={"payment_method"}
            field={"data.establishment_id"}
            value={response.establishment.establishment_id}
            type={"hidden"}
          />
          <div className="space-y-5">
            <div>
              <RInput
                table={"payment_method"}
                field={"data.name"}
                label={"Name"}
                defaultValue={response.payment_method?.name}
                required
                className="input"
                type="text"
              />
            </div>
            <div>
              <RLabel table={"payment_method"} field={"data.default_surcharge_percentage"}>
                Surcharge (optional)
              </RLabel>
              <br />
              <div className="flex flex-row gap-3 items-center">
                <RInput
                  table={"payment_method"}
                  field={"data.default_surcharge_percentage"}
                  placeholder={"Enter a number here"}
                  className="input"
                  defaultValue={response.payment_method?.default_surcharge_percentage}
                  type={"number"}
                  min={0}
                  max={100}
                  step={1}
                />
                <div className="rounded-md border border-slate-300 text-slate-600 py-2.5 text-sm min-h-12 px-2.5 items-center flex justify-center">
                  %
                </div>
                {/*<select className="select" disabled>*/}
                {/*  <option value="">%</option>*/}
                {/*</select>*/}
              </div>
            </div>
            {!!response.available_intuit_connections.length && (
              <div>
                <RLabel table={"payment_method"} field={"data.intuit_connection_id"}>
                  Quickbooks
                </RLabel>
                <br />
                <RSelect
                  table={"payment_method"}
                  field={"data.intuit_connection_id"}
                  defaultValue={response.payment_method?.intuit_connection_id || ""}
                  className="select w-full"
                >
                  <option value={""}>No quickbooks</option>
                  {response.available_intuit_connections.map((intuitConnection) => (
                    <option value={intuitConnection.id}>
                      {intuitConnection.intuit_company.CompanyName} ({intuitConnection.intuit_user.email})
                    </option>
                  ))}
                </RSelect>
              </div>
            )}
            <div className="space-y-10">
              <div className="space-y-1">
                <label className="flex from-row gap-2 items-center">
                  <RInput
                    defaultChecked={!!response.payment_method?.xendit}
                    type={"checkbox"}
                    className="checkbox peer"
                    hiddenType={"__boolean__"}
                    table={"payment_method"}
                    field={"data.xendit"}
                    disabled={!response.establishment.xendit_account_id}
                  />
                  <span className="peer-disabled:text-slate-400">
                    <span className={"font-semibold "}>Xendit</span> Payment Gateway
                  </span>
                </label>
                {!response.establishment.xendit_account_id && (
                  <p className="p-3 rounded-md bg-secondary-50 text-sm">
                    <span className="font-semibold">Note</span>: There is currently no Xendit account connected or set up for you. Please{" "}
                    <a href={"https://forms.clickup.com/2633992/f/2gc88-4895/1EASXWV4VNEJ2B6N37"} target={"_blank"} className="link">
                      contact us
                    </a>{" "}
                    to set up your payment gateway.{" "}
                  </p>
                )}
              </div>
              {response.payment_method && (
                <div className="space-y-3">
                  <RInput
                    type="hidden"
                    table={"payment_method"}
                    field={"id"}
                    value={response.payment_method.id}
                    index={search.state.delete_and_create ? "-1" : 0}
                  />
                  <p className="">Do you want to apply Payment Name changes to existing bookings as well?</p>
                  <div className="flex flex-row gap-3">
                    <ParamLink
                      className="group flex flex-row gap-2"
                      aria-selected={search.state.delete_and_create}
                      paramState={{ delete_and_create: true }}
                    >
                      <Checker className="mt-1 border border-slate-500 rounded w-4 h-4 group-aria-selected:border-primary group-aria-selected:outline-4 group-aria-selected:outline-primary-100" />
                      <div>
                        <p className="font-semibold">No,</p>
                        <p className="text-sm">Keep the current names for all existing bookings.</p>
                      </div>
                    </ParamLink>
                    <ParamLink
                      className="group flex flex-row gap-2 "
                      aria-selected={!search.state.delete_and_create}
                      paramState={{ delete_and_create: false }}
                    >
                      <Checker className="mt-1 border border-slate-500 rounded w-4 h-4 group-aria-selected:border-primary group-aria-selected:outline-4 group-aria-selected:outline-primary-100" />
                      <div>
                        <p className="font-semibold">Yes,</p>
                        <p className="text-sm">Apply name change to all existing bookings</p>
                      </div>
                    </ParamLink>
                  </div>
                  <p className="p-3 rounded-md bg-secondary-50 text-sm">
                    <span className="font-semibold">Note</span>: Surcharge and other changes here will not affect existing bookings,
                    regardless of your selection.
                  </p>
                  {search.state.delete_and_create && (
                    <RInput table={"payment_method"} field={"data.deleted_at"} index={"-1"} value={at_now_value} type={"hidden"} />
                  )}
                </div>
              )}
              <div className="flex flex-wrap items-end gap-3 py-1 float-end">
                <Backbutton className="btn hover:underline">Cancel</Backbutton>
                <SubmitButton type="submit" className="btn btn-primary">
                  {response.payment_method ? "Save" : "Create"}
                </SubmitButton>
              </div>
            </div>
          </div>
        </ActionForm>
        {response.payment_method && (
          <Fragment>
            <hr className="mb-5 mt-3" />
            <ActionForm
              className="rounded-md border border-red-300 p-3 flex-wrap flex justify-between group hover:border-red-500"
              confirmMessage={`Are you sure you want to delete Payment Method "${response.payment_method.name}"?`}
            >
              <p className="text-slate-500 group-hover:text-slate-700">Delete Payment Method "{response.payment_method.name}"</p>
              <RedirectParamsInput
                path={_payment_method}
                paramState={{ persist_establishment_id: response.establishment.establishment_id }}
              />
              <RInput table={"payment_method"} field={"id"} value={response.payment_method.id} />
              <RInput table={"payment_method"} field={"data.deleted_at"} type={"hidden"} value={at_now_value} />
              <div className="flex flex-row items-end">
                <SubmitButton type="submit" className="link text-red-300 group-hover:text-red-500">
                  Delete
                </SubmitButton>
              </div>
            </ActionForm>
          </Fragment>
        )}
      </div>
    </div>
  );
}
