import { LoaderFunctionArgs } from "@remix-run/router";
import { kysely } from "~/misc/database.server";
import { redirect } from "@remix-run/server-runtime";
import { _booking_detail } from "~/misc/paths";
import { notFound } from "~/misc/responses";

export const loader = async (args: LoaderFunctionArgs) => {
  const paymentId = args.params.payment_id!;

  const payment = await kysely.selectFrom("payment").where("payment.id", "=", paymentId).selectAll("payment").executeTakeFirst();

  if (!payment) throw notFound();

  throw redirect(_booking_detail(payment.booking_id), 307);
};
