import React from "react";
import { EmailMarkdocComp } from "~/domain/email/email-components";
import { baseEmailMarkdocComps } from "~/domain/email/email-markdoc";
import { ActivityDetails, ParticipantInfo, BookingLink, EstablishmentContact } from "~/domain/email/email-template-components";
import { defaultPostActivityEmailTemplate } from "~/domain/email/post-activity-email-template";

export default function EmailTestPage() {
  const testVars = {
    participant_name: "John Doe",
    participant_email: "<EMAIL>",
    activity_name: "Scuba Diving Adventure",
    activity_date: "2025-01-15",
    establishment_name: "Dive Center",
    establishment_email: "<EMAIL>",
    booking_id: "12345",
    booking_url: "https://example.com/booking/12345",
    operator_name: "Dive Center",
  };

  return (
    <div className="app-container p-6">
      <h1 className="text-2xl font-bold mb-6">Email Template Test</h1>

      <div className="space-y-6">
        <div>
          <h2 className="text-xl font-semibold mb-3">Default Post-Activity Email Template</h2>
          <div className="border border-slate-200 rounded-md p-4">
            <EmailMarkdocComp
              content={defaultPostActivityEmailTemplate}
              comps={{
                ...baseEmailMarkdocComps,
                ActivityDetails: () => <ActivityDetails vars={testVars} />,
                ParticipantInfo: () => <ParticipantInfo vars={testVars} />,
                BookingLink: () => <BookingLink vars={testVars} />,
                EstablishmentContact: () => <EstablishmentContact vars={testVars} />,
              }}
              vars={testVars}
            />
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-3">Template Variables</h2>
          <pre className="bg-slate-100 p-4 rounded-md text-sm overflow-auto">{JSON.stringify(testVars, null, 2)}</pre>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-3">Raw Template</h2>
          <pre className="bg-slate-100 p-4 rounded-md text-sm overflow-auto whitespace-pre-wrap">{defaultPostActivityEmailTemplate}</pre>
        </div>
      </div>
    </div>
  );
}
