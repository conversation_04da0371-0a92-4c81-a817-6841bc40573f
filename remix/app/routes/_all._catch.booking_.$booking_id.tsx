import type { MetaFunction } from "@remix-run/react";
import { useLoaderData, useNavigation, useSubmit } from "@remix-run/react";
import { AnimatingDiv, BaseLink } from "~/components/base/base";
import {
  _activity_mutate,
  _booking_detail,
  _booking_lock,
  _booking_mutate,
  _booking_quickbooks,
  _fileTarget,
  _item_detail,
  _participant_detail,
  _participant_mutate,
  _planning,
  _retail,
} from "~/misc/paths";
import { ParamLink } from "~/components/meta/CustomComponents";
import { formatPrice } from "~/utils/money";
import { getFullProductTitle, getProductTitle, ProductItem } from "~/domain/product/ProductItem";
import { activities } from "~/domain/activity/activity";
import {
  CheckCircleIcon,
  CheckIcon,
  ChevronRightIcon,
  DocumentDuplicateIcon,
  ExclamationTriangleIcon,
  LockClosedIcon,
  MinusIcon,
  PlusCircleIcon,
  PlusIcon,
  QrCodeIcon,
  ShareIcon,
  UserIcon,
} from "@heroicons/react/20/solid";
import { actionValues, defaultCountryCode, defaultLocale, fixedResponseIdentifierValue, identifierKey } from "~/misc/vars";
import { toast } from "~/misc/toast";
import { getMeetingType } from "~/domain/planning/plannings-consts";
import { Trans } from "~/components/Trans";
import { DeleteButtonForm, RInput, RLabel, RSelect, toInputId } from "~/components/ResourceInputs";
import React, { Fragment, Suspense, useState } from "react";
import { DeleteButton, SubmitButton } from "~/components/base/Button";
import { OperationInput, RedirectParamsInput, ResponseIdentifierInput } from "~/components/form/DefaultInput";
import { ActionAlert } from "~/components/ActionAlert";
import { ActionForm, defaultEqualCheck } from "~/components/form/BaseFrom";
import { fName, getFullUrl } from "~/misc/helpers";
import { twMerge } from "tailwind-merge";
import { DividerWithText } from "~/components/Divider";
import { useIsInterative } from "~/hooks/hooks";
import { getDefaultSurcharges } from "~/domain/payment/payment";
import { flat, unique } from "remeda";
import { MoneyValue } from "~/components/field/MoneyValue";
import { Tooltip } from "~/components/base/tooltip";
import { IoInformationCircleOutline } from "react-icons/io5";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import type { SerializeFrom } from "@remix-run/server-runtime";
import { getSessionSimple } from "~/utils/session.server";
import { isEditorQb, memberIsAdminOrOwnerQb, memberQb, QbArgs } from "~/domain/member/member-queries.server";
import { kysely } from "~/misc/database.server";
import { bookingQbWithTotalDuration } from "~/domain/booking/booking-queries";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { createPageOverwrites, traveltrusterName } from "~/misc/consts";
import { establishmentQb } from "~/domain/establishment/queries";
import { notFound } from "~/misc/responses";
import { CallbackInput } from "~/domain/callback/callback.components";
import type { loader as rootLoader } from "~/root";
import { createImageUrl, IkImageSimple } from "~/components/IkImage";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { useAppContext } from "~/hooks/use-app-context";
import { saleItemWithProductQb, registrationFormWithFormattedDate } from "~/domain/activity/activity-queries";
import { participantQb, participatingParticipantIdQb, participationQb } from "~/domain/participant/participant.queries.server";
import { sql } from "kysely";
import { arrayAgg, dateRange, descNullsFirst, formatDatetime, notNull, round } from "~/kysely/kysely-helpers";
import { Alert } from "~/components/base/alert";
import { formatDuration } from "~/domain/activity/activity.helpers";
import type { LoaderFunctionArgs } from "@remix-run/router";
import { QRCodeSVG } from "qrcode.react";
import { removeFromArray, toggleArray } from "~/misc/parsers/global-state-parsers";
import { BookingPaymentBadge, getPaymentState, MeetingInput } from "~/domain/booking/booking-components";
import { at_infinity_value, at_now_value } from "~/kysely/db-static-vars";
import { baseProductWithSelect } from "~/domain/product/product-queries.server";
import { ParticipantStatusBadges } from "~/domain/participant/participant.components";
import { CountryCode, getCountry } from "~/data/countries";
import { defaultSurchargeLineText, getVatSentence } from "~/domain/invoice/invoice-vars";
import { CallbackName } from "~/domain/callback/callback";
import { bookingPriceSelects } from "~/domain/pricing/booking-pricing-queries";
import { activityTotalPriceRaw } from "~/domain/pricing/activity-pricing-queries";
import { activityAddonQuantityQb, activityAddonTotalRawPriceSelect } from "~/domain/pricing/activity-addon-pricing-queries";
import { summedPaymentAmount } from "~/domain/pricing/payment-pricing";
import { asPaymentSurcharcheAmount, asPaymentTotalAmount } from "~/domain/payment/payment-queries.server";
import { CircleOffIcon, CopyIcon, DollarSign } from "lucide-react";
import { CDialog, DialogCloseButton } from "~/components/base/Dialog";
import { CheckDoneIcon, NotDoneIcon } from "~/components/Icons";
import { myRegisteredParticipants } from "~/domain/participant/participant-auth-queries.server";
import { MarkDocLink, MarkdocParagraph } from "~/domain/waiver/waiver-markdoc";
import { MarkdocComp, replacePlainUrlWithMarkdownUrl } from "~/domain/waiver/waiver-components";
import { getAdminLevelIndex } from "~/domain/member/member-vars";
import { DB } from "../kysely/db";
import { disableScrollOnNumberInput } from "~/utils/component-utils";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const bookingId = params.booking_id!;

  const ctx = await getSessionSimple(request);

  const args: QbArgs = {
    trx: kysely,
    ctx: { session_id: ctx.session_id },
  };

  const timerId = "#" + Math.random().toPrecision(3).toString().replace("0.", "");
  console.time(`bookingQb ${timerId}`);
  const bookingQb = bookingQbWithTotalDuration
    .where("booking.id", "=", bookingId)
    .select(bookingPriceSelects)
    .select((eb) => {
      const participatingParticipantIdsQb = participatingParticipantIdQb
        .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
        .where("sale_item.booking_id", "=", eb.ref("booking.id"));
      return [
        "establishment.locale as establishment_locale",
        jsonObjectFrom(
          eb
            .selectFrom("invoice")
            .selectAll("invoice")
            .select((eb) => [
              jsonObjectFrom(
                eb
                  .selectFrom("intuit_connection")
                  .where("intuit_connection.id", "=", eb.ref("invoice.intuit_connection_id"))
                  .select(["intuit_connection.intuit_company", "intuit_connection.intuit_user"]),
              ).as("intuit_connection"),
              jsonArrayFrom(
                eb
                  .selectFrom("callback")
                  .selectAll("callback")
                  .select((eb) => [
                    formatDatetime(eb.ref("callback.created_at"), "YYYY.MM.DD HH24:MI:SS", eb.ref("region.timezone")).as(
                      "created_at_formatted",
                    ),
                    formatDatetime(eb.ref("callback.handled_at"), "YYYY.MM.DD HH24:MI:SS", eb.ref("region.timezone")).as(
                      "handled_at_formatted",
                    ),
                    round(sql<number | null>`EXTRACT(EPOCH FROM (
                    ${eb.ref("callback.handled_at")}
                    -
                    ${eb.ref("callback.created_at")}
                    )
                    )`).as("duration_in_seconds"),
                    formatDatetime(eb.ref("callback.handled_at"), "YYYY.MM.DD HH24:MI:SS", eb.ref("region.timezone")).as(
                      "handled_at_formatted",
                    ),
                  ])
                  .where("callback.name", "=", "send_invoice_to_intuit" satisfies CallbackName)
                  .orderBy("callback.handled_at", descNullsFirst)
                  .orderBy("callback.created_at desc")
                  .where("callback.target_id", "=", eb.ref("invoice.id")),
              ).as("send_invoice_to_intuit_callbacks"),
            ])
            .where("invoice.booking_id", "=", eb.ref("booking.id"))
            .limit(1),
        ).as("invoice"),
        jsonArrayFrom(
          myRegisteredParticipants(args)
            .distinct()
            .leftJoin("participation", "participation.participant_id", "_participant.id")
            .leftJoin("sale_item", "sale_item.id", "participation.sale_item_id")
            .where((cmpr) =>
              cmpr.or([
                cmpr("sale_item.booking_id", "=", eb.ref("booking.id")),
                cmpr("_participant.booking_id", "=", eb.ref("booking.id")),
              ]),
            )
            .select("_participant.id"),
        ).as("my_registered_participants"),
        eb
          .selectFrom("participant")
          .leftJoin("participation", "participant.id", "participation.participant_id")
          .leftJoin("sale_item", "participation.sale_item_id", "sale_item.id")
          .select((eb) => arrayAgg(eb.ref("participant.stay")).as("stays"))
          .where("participant.stay", "is not", null)
          .where((eb) =>
            eb.or([eb("participant.booking_id", "=", eb.ref("booking.id")), eb("sale_item.booking_id", "=", eb.ref("booking.id"))]),
          )
          .as("participant_stays"),
        jsonArrayFrom(
          participantQb(kysely)
            .where("participant.booking_id", "is", null)
            .where("participant.id", "not in", participatingParticipantIdQb)
            .where("customer.establishment_id", "=", eb.ref("booking.establishment_id")),
        ).as("pending_participants"),
        jsonArrayFrom(
          participantQb(kysely)
            .leftJoin("participation", "participation.participant_id", "participant.id")
            .leftJoin("sale_item", "sale_item.id", "participation.sale_item_id")
            .distinctOn(["participant.created_at", "participant.id"])
            .select((eb) => [eb("participant.id", "in", participatingParticipantIdsQb).as("is_participating")])
            .where((cmpr) =>
              cmpr.or([cmpr("sale_item.booking_id", "=", eb.ref("booking.id")), cmpr("participant.booking_id", "=", eb.ref("booking.id"))]),
            ),
        ).as("participants"),
        notNull(
          jsonObjectFrom(
            establishmentQb.where("establishment.id", "=", eb.ref("booking.establishment_id")).select((eb) => [
              jsonArrayFrom(
                eb
                  .selectFrom("payment_method")
                  .selectAll("payment_method")
                  .where("payment_method.deleted_at", "=", at_infinity_value)
                  .where("payment_method.establishment_id", "=", eb.ref("establishment.id"))
                  .where((eb) => eb.or([eb("payment_method.xendit", "=", false), eb("establishment.xendit_account_id", "is not", null)])),
              ).as("payment_methods"),
              jsonArrayFrom(baseProductWithSelect.where("item.establishment_id", "=", eb.ref("establishment.id"))).as("products"),
            ]),
          ),
        ).as("establishment"),
        jsonArrayFrom(
          saleItemWithProductQb
            .leftJoin("product", "product.id", "sale_item.product_id")
            .leftJoin("item", "item.id", "product.item_id")
            .where("sale_item.booking_id", "=", eb.ref("booking.id"))
            .select((eb) => {
              return [
                jsonArrayFrom(
                  eb
                    .selectFrom("activity_addon")
                    .selectAll("activity_addon")
                    .select([activityAddonQuantityQb.as("total_quantity"), activityAddonTotalRawPriceSelect.as("total_price")])
                    .where("activity_addon.sale_item_id", "=", eb.ref("sale_item.id")),
                ).as("activity_addons"),
                jsonObjectFrom(
                  registrationFormWithFormattedDate
                    .where("form.root_id", "=", eb.ref("item.form_root_id"))
                    .where("form.deleted_at", "=", at_infinity_value),
                ).as("latest_form"),
                activityTotalPriceRaw.as("price_total"),
                jsonArrayFrom(
                  eb
                    .selectFrom("trip_assignment")
                    .innerJoin("trip", "trip.id", "trip_assignment.trip_id")
                    .innerJoin("participation", "participation.id", "trip_assignment.participation_id")
                    .selectAll("trip_assignment")
                    .select((eb) => [
                      "trip.date as trip_date",
                      sql<boolean>`(${dateRange(eb.ref("trip.date"), eb.ref("trip.date"))} && ${eb.ref("sale_item.duration")})`.as(
                        "is_within_duration",
                      ),
                    ])
                    .where("participation.sale_item_id", "=", eb.ref("sale_item.id")),
                ).as("assignments"),
                jsonArrayFrom(
                  participationQb
                    .select((eb) => [
                      eb
                        .selectFrom("comment")
                        .select((eb) => arrayAgg(eb.ref("comment.id"), "uuid").as("comment_ids"))
                        .where("comment.target", "=", "participation" satisfies keyof DB)
                        .where("comment.target_id", "=", eb.ref("participation.id"))
                        .as("comment_ids"),
                      jsonArrayFrom(
                        eb
                          .selectFrom("trip_assignment")
                          .selectAll("trip_assignment")
                          .where("trip_assignment.participation_id", "=", eb.ref("participation.id")),
                      ).as("assignments"),
                    ])
                    .where("participation.sale_item_id", "=", eb.ref("sale_item.id")),
                ).as("participations"),
              ];
            })
            .orderBy("sale_item.duration", "asc")
            .orderBy("sale_item.created_at", "asc"),
        ).as("activities"),
        eb
          .or([
            eb.exists(isEditorQb(args)),
            eb.exists(memberQb(args).where("_member.establishment_id", "=", eb.ref("booking.establishment_id"))),
          ])
          .as("normal_view"),
        eb
          .exists(memberIsAdminOrOwnerQb(args).where("_member.establishment_id", "=", eb.ref("booking.establishment_id")))
          .as("allowed_to_edit"),
        jsonArrayFrom(
          eb
            .selectFrom("payment")
            .selectAll("payment")
            .select((eb) => [
              summedPaymentAmount.as("summed_amount"),
              asPaymentSurcharcheAmount,
              asPaymentTotalAmount,
              formatDatetime(eb.ref("payment.payed_at"), "YYYY.MM.DD HH24:MI", eb.ref("region.timezone")).as("payed_at_formatted"),
              formatDatetime(eb.ref("payment.payed_at"), "YYYY-MM-DD HH24:MI", eb.ref("region.timezone")).as("payed_at_iso"),
              formatDatetime(eb.ref("payment.created_at"), "YYYY.MM.DD", eb.ref("region.timezone")).as("created_at_formatted"),
              notNull(
                jsonObjectFrom(
                  eb
                    .selectFrom("payment_method")
                    .selectAll("payment_method")
                    .where("payment_method.id", "=", eb.ref("payment.payment_method_id")),
                ),
              ).as("payment_method"),
            ])
            .whereRef("payment.booking_id", "=", "booking.id")
            .where("payment.deleted_at", "=", at_infinity_value)
            .orderBy("payment.created_at"),
        ).as("payments"),
      ];
    });
  // console.log("booking sql", bookingQb.compile().sql);
  const booking = await bookingQb.executeTakeFirstOrThrow();
  console.timeEnd(`bookingQb ${timerId}`);

  const establishment = booking.establishment;
  if (!establishment) throw notFound("Establishment belonging to booking was not found");

  return {
    booking: {
      ...booking,
      message_urls_replaced: booking.message && replacePlainUrlWithMarkdownUrl(booking.message),
      establishment: establishment,
    },
    ...createPageOverwrites({
      simple_view: !booking.normal_view,
      show_whatsapp: false,
      customer_toggle: true,
      establishment_id: establishment.id,
    }),
  };
};

export const meta: MetaFunction<typeof loader> = (args) => {
  const parentData = args.matches.find((match) => match.id === "root")?.data as SerializeFrom<typeof rootLoader> | undefined;
  const config = parentData?.env;
  const bucketUrl = config?.firebase_singapore.storageBucket;
  const data = args.data;
  const establishment = data?.booking.establishment;
  const imagePath = establishment?.files?.[0]?.filename;
  return [
    {
      title: `Booking at ` + (establishment ? getEstablishmentName(establishment) : traveltrusterName),
    },
    { description: establishment?.bio },
    { "og:description": establishment?.bio || establishment?.about },
    bucketUrl ? { "og:image": imagePath && createImageUrl(bucketUrl, imagePath, 300, 200) } : {},
  ];
};

const percentages = [10, 30, 50, 100];

const editKeyword = "-edit";

const PaymentDialogContent = () => {
  const response = useLoaderData<typeof loader>();
  const search = useSearchParams2();

  const payments = response.booking.payments;
  const payment = payments.find((payment) => search.state.modal_detail_name?.startsWith(payment.id));
  const editMode = search.state.modal_detail_name?.endsWith(editKeyword);

  if (!payment) return <div>Payment not found</div>;

  const paymentMethod = payment.payment_method;
  const canEdit = !paymentMethod.xendit;
  const invoice = response.booking.invoice;

  return (
    <div className="space-y-3 w-full sm:min-w-96">
      <div className="flex flex-row  items-center gap-3">
        Payment
        {canEdit && !editMode && (
          <ParamLink paramState={{ modal_detail_name: search.state.modal_detail_name + editKeyword }} className="link">
            Edit
          </ParamLink>
        )}
        <span className="flex-1"></span>
        <DialogCloseButton />
      </div>
      <ActionForm className="space-y-3" onCheckEqual={defaultEqualCheck} key={editMode + ""}>
        <RInput table={"payment"} field={"id"} value={payment.id} />
        <RedirectParamsInput path={"./"} paramState={{ modal_detail_name: search.state.modal_detail_name?.replace(editKeyword, "") }} />
        <div className="grid grid-cols-2 gap-x-3 gap-y-1 items-center">
          <div>Method</div>
          <div>{paymentMethod.name}</div>
          <div>Created</div>
          <div>{payment.created_at_formatted}</div>
          <div>Paid</div>
          <div>
            {editMode ? (
              <RInput
                className={"input"}
                table={"payment"}
                field={"data.payed_at"}
                type={"datetime-local"}
                defaultValue={payment.payed_at_iso || ""}
              />
            ) : (
              payment.payed_at_formatted
            )}
          </div>
        </div>
        {editMode && (
          <div className="flex flex-row gap-3 justify-end">
            <ParamLink paramState={{ modal_detail_name: search.state.modal_detail_name?.replace(editKeyword, "") }} className="btn">
              Cancel
            </ParamLink>
            <SubmitButton className="btn btn-primary">Save</SubmitButton>
          </div>
        )}
      </ActionForm>
      {/*<pre>{JSON.stringify(payment, null, 2)}</pre>*/}
      <ActionForm>
        <ResponseIdentifierInput value={fixedResponseIdentifierValue} />
        <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined }} />
        {invoice && (
          <Fragment>
            <CallbackInput callbackName={"generate_invoice_pdf"} target_id={invoice.id} />
            <CallbackInput callbackName={"send_invoice_to_intuit"} target_id={invoice.id} />
          </Fragment>
        )}
        <RInput table={"payment"} field={"id"} value={payment.id} />
        <RInput table={"payment"} field={"data.deleted_at"} value={"true"} type={"hidden"} />
        <DeleteButton>Delete</DeleteButton>
      </ActionForm>
      {!payment.payed_at && paymentMethod.xendit && (
        <ActionForm>
          <RedirectParamsInput path={"./"} paramState={{ toggle_panels: removeFromArray(search.state.toggle_panels, payment.id) }} />
          <RInput table={"payment"} field={"id"} value={payment.id} />
          {payment.xendit_invoice_id && (
            <RInput table={"payment"} field={"data.payed_at"} value={actionValues.payedAtXendit} type={"hidden"} />
          )}
          <SubmitButton className="link">Refresh</SubmitButton>
        </ActionForm>
      )}
    </div>
  );
};

const PaymentBlock = () => {
  const response = useLoaderData<typeof loader>();
  const ctx = useAppContext();
  const booking = response.booking;
  const bookingCurrency = ctx.currencies.find((currency) => currency.id === booking.currency_id);
  const currencyDecimals = bookingCurrency?.decimals ?? 2;
  const step = Math.pow(10, -currencyDecimals);

  const [paymentAmount, setPaymentAmount] = useState<number>(0);
  const [methodKey, setMethodKey] = useState("");

  // const remainingAmount = Math.max(booking.balance_remaining - paymentAmount, 0);
  const remainingAmount = booking.price_total_final - (booking.payment_amount_raw || 0);
  const remainingAmountAfter = Math.max(remainingAmount - paymentAmount, 0);
  const selectedPercentage = Math.round((paymentAmount / remainingAmount) * 100);

  const method = booking.establishment.payment_methods.find((method) => method.id === methodKey);
  const defaultSurchargeForMethod = method?.default_surcharge_percentage || 0;
  return (
    <div>
      <ActionForm className="space-y-6 [&_label]:text-xs [&_label]:text-slate-600">
        <ActionAlert />
        <RedirectParamsInput path={"./"} paramState={{ toggle_payment_form: false }} />
        <RInput table={"payment"} field={"data.booking_id"} value={booking.id} type={"hidden"} />
        <div className="flex flex-row gap-3">
          {percentages.map((percentage) => (
            <button
              key={percentage}
              disabled={remainingAmount <= 0}
              onClick={() => {
                setPaymentAmount(Math.round((remainingAmount / 100) * percentage * Math.pow(10, currencyDecimals)) / Math.pow(10, currencyDecimals));
              }}
              aria-selected={percentage === selectedPercentage}
              type={"button"}
              className="btn flex-1 bg-secondary-50 py-4 text-slate-700 transition-colors hover:bg-slate-200 active:bg-slate-300 aria-selected:bg-secondary-500 aria-selected:text-white"
            >
              {percentage}%
            </button>
          ))}
        </div>
        {booking.invoice && <CallbackInput callbackName={"send_invoice_to_intuit"} target_id={booking.invoice.id} />}
        <div className="flex flex-row gap-3">
          <div>
            <RLabel table={"payment"} field={"data.payment_method_id"}>
              Method
            </RLabel>
            <br />
            <RSelect
              table={"payment"}
              field={"data.payment_method_id"}
              className="select"
              required
              onChange={(e) => setMethodKey(e.target.value)}
            >
              <option value="">select</option>
              {booking.establishment.payment_methods.map((method) => (
                <option key={method.id} value={method.id}>
                  {method.name}
                </option>
              ))}
            </RSelect>
          </div>
          <div>
            <label htmlFor={toInputId(fName("payment", "data.surcharge_percentage"))}>{defaultSurchargeLineText}</label>
            <br />
            <RSelect
              table={"payment"}
              field={"data.surcharge_percentage"}
              className="select"
              key={methodKey}
              defaultValue={defaultSurchargeForMethod}
            >
              {getDefaultSurcharges(defaultSurchargeForMethod).map((surcharge) => (
                <option key={surcharge} value={surcharge}>
                  {surcharge}%
                </option>
              ))}
            </RSelect>
          </div>
        </div>
        <div>
          <RInput className="input" label={"Payment url (optional)"} table={"payment"} field={"data.url"} type={"url"} />
        </div>
        <div className="space-y-2">
          <div className="flex flex-row items-end gap-2">
            <div className="block">
              <RLabel table={"payment"} field="data.amount">
                Amount{" "}
                <MoneyValue
                  nativeAmount={paymentAmount}
                  nativeCurrency={booking.currency_id}
                  toCurrency={booking.currency_id}
                  locale={booking.establishment_locale}
                />
              </RLabel>
              <div className="flex flex-row items-center gap-2 rounded-md border pl-2">
                <span>{booking.currency_id}</span>
                <input
                  name={fName("payment", "data.amount")}
                  id={toInputId(fName("payment", "data.amount"))}
                  step={step}
                  // inputMode={"numeric"}
                  className="input-clean w-fit"
                  type="number"
                  required
                  onWheel={disableScrollOnNumberInput}
                  value={paymentAmount + ""}
                  // defaultValue={paymentAmount}
                  onChange={(e) => {
                    setPaymentAmount(Number(e.target.value));
                  }}
                />
              </div>
            </div>
            <SubmitButton className="btn btn-primary">Save</SubmitButton>
          </div>
          <p className="text-xs italic text-slate-500">
            Remaining{" "}
            <MoneyValue
              nativeAmount={remainingAmountAfter}
              nativeCurrency={booking.currency_id}
              toCurrency={booking.currency_id}
              locale={booking.establishment_locale}
            />
          </p>
        </div>
      </ActionForm>
    </div>
  );
};

const addItemPanelName = "item_panel";

const LineItemForm = (props: {
  item?: {
    id: string;
    description?: string | null;
    quantity?: number | null;
    product_id?: string | null;
    price_pp?: number | null;
  };
  currency: string;
}) => {
  const context = useAppContext();
  const data = useLoaderData<typeof loader>();
  const product = data.booking.establishment.products.find((product) => product.id === props.item?.product_id);
  const search = useSearchParams2();

  const foundCurrency = context.currencies.find((c) => c.id === props.currency);
  const currencyDecimals = foundCurrency?.decimals ?? 2;
  const step = Math.pow(10, -currencyDecimals);
  return (
    <div className="space-y-3">
      <div className="flex flex-col gap-3 md:flex-row flex-1 items-center">
        <div className="flex flex-row gap-3">
          <RInput
            table={"sale_item"}
            field={"data.quantity"}
            defaultValue={props.item?.quantity ?? 1}
            type={"number"}
            step={1}
            min={1}
            className="input w-20"
          />
          <RInput
            table={"sale_item"}
            field={"data.price_pp"}
            step={step}
            required
            onWheel={disableScrollOnNumberInput}
            type={"number"}
            className="input flex-1"
            defaultValue={props.item?.price_pp ?? ""}
            placeholder="Price"
          />
        </div>
        <div className="flex-1">
          {product ? (
            <ParamLink className="link" target={"_blank"} path={_item_detail(product.item_id)} paramState={{ product_id: product.id }}>
              {getProductTitle(product)}
            </ParamLink>
          ) : (
            <RInput
              table={"sale_item"}
              field={"data.description"}
              defaultValue={props.item?.description || ""}
              className="input flex-1 min-w-20"
              required
              placeholder="Enter a description"
            />
          )}
        </div>
      </div>
      <div className={"flex flex-row gap-3 items-center justify-end"}>
        <RedirectParamsInput
          path={"./"}
          paramState={{
            toggle_panels: removeFromArray(search.state.toggle_panels, props.item?.id || addItemPanelName),
            element_action: [],
          }}
        />
        <ParamLink
          className="btn link px-5"
          paramState={{
            toggle_panels: removeFromArray(search.state.toggle_panels, props.item?.id || addItemPanelName),
            element_action: [],
          }}
        >
          <span>Cancel</span>
        </ParamLink>
        <SubmitButton className="btn btn-primary px-20">Save</SubmitButton>
      </div>
    </div>
  );
};

const shareBookingATagId = "share-booking";

const ActivityModal = () => {
  const response = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const activity = response.booking.activities.find((activity) => activity.id === search.state.modal_detail_name);

  if (!activity) return <p>No Activity Selected</p>;

  const latestForm = activity.latest_form;

  if (activity.registration_form?.created_at === latestForm?.created_at && activity.registration_form?.id === latestForm?.id) {
    return <div key={activity.id}>Is latest onboarding journey</div>;
  }

  return (
    <ActionForm key={activity.id} className="space-y-3 group">
      <p>
        <strong>Current form:</strong>
        <br />
        {activity.registration_form?.name}
        <br />
        {activity.registration_form?.created_at_formatted}
      </p>
      <p>
        <strong>Latest form:</strong>
        <br />
        {latestForm?.name}
        <br />
        {latestForm?.created_at_formatted}
      </p>
      <RInput table={"sale_item"} field={"id"} value={activity.id} />
      <RInput table={"sale_item"} field={"data.form_id"} value={latestForm?.id} type={"hidden"} />
      <SubmitButton className="btn btn-primary">Update to latest form</SubmitButton>
    </ActionForm>
  );
};

export default function Page() {
  const context = useAppContext();
  const search = useSearchParams2();
  const response = useLoaderData<typeof loader>();
  if (search.state.persist_debug) {
    console.log("response", response);
  }
  const canStaffPlus =
    context.members.find(
      (member) => member.establishment_id === response.booking.establishment.id && member.admin >= getAdminLevelIndex("read"),
    ) && !search.state.customer_view;

  const canEdit =
    context.members.find(
      (member) => member.establishment_id === response.booking.establishment.id && member.admin >= getAdminLevelIndex("write"),
    ) && !search.state.customer_view;

  const navigation = useNavigation();
  const isInteractive = useIsInterative();
  const submit = useSubmit();
  const booking = response.booking;
  const establishment = booking.establishment;
  const allAssignments = flat(booking.activities.map((activity) => activity.assignments));

  const printView = search.state.print_friendly;
  const showPrice = !booking.hide_price_for_customer || canEdit || printView;
  const operatorLogo = establishment?.logo_file;
  const establishmentCountry = getCountry(establishment?.country_code as CountryCode);
  const invoice = booking.invoice;
  const payments = booking.payments.filter((payment) => canEdit || payment.payed_at);
  const openPayments = booking.payments.filter((payment) => !payment.payed_at && payment.url);
  const bookingPath = _booking_detail(booking.id);
  const allslots = flat(booking.activities.filter((activity) => activity.duration).map((activity) => activity.participations));
  const openSlots = allslots.filter((participation) => !participation.participant_id);
  const openActivityIds = unique(openSlots.map((slot) => slot.sale_item_id));

  const meetingType = getMeetingType(booking.meeting_type);

  const meetingAddress = booking.meeting_address || meetingType?.empty_text || response.booking.establishment.address;

  const myParticipants = booking.participants.filter((participant) =>
    booking.my_registered_participants.find((myParticipant) => myParticipant.id === participant.id),
  );

  const baseUrl = getFullUrl(context.host);
  const fullUrl = baseUrl + bookingPath;

  const durationInDays = booking.activity_agg?.duration_in_days || 0;
  const multipleDays = durationInDays > 1;

  const normalView = !printView;

  const outstandingBalanceRaw = booking.price_total_final - (booking.payed_amount_raw || 0);

  const bookingChecks = {
    participants: booking.participants.length,
    openSlots: openSlots.length,
    duration: durationInDays,
    meeting_type: booking.meeting_type,
  };

  const isBooking = !!Object.values(bookingChecks).find((value) => !!value);

  return (
    <div className="space-y-3">
      <section className={twMerge("space-y-5", normalView ? "app-container" : "p-10 py-10")}>
        {openSlots.length > 0 && normalView && (!canEdit || search.state.customer_view) && (
          <div className="pt-2 sticky top-0 z-10">
            <ParamLink
              className="btn w-full btn-primary md:w-fit py-4 px-3 md:px-20 text-lg text-center font-normal "
              path={_participant_mutate}
              paramState={{
                booking_id: booking.id,
                persist_establishment_id: booking.establishment_id,
                toggle_sale_item_ids: openActivityIds.length === 1 ? openActivityIds : [],
              }}
            >
              Please register to confirm your booking
            </ParamLink>
          </div>
        )}
        {printView && (
          <Fragment>
            <section className="grid " style={{ gridTemplateColumns: "1fr auto" }}>
              <div className="flex">
                <div>
                  {operatorLogo ? (
                    <IkImageSimple
                      className="h-[100px] bg-transparent max-w-[280px]"
                      ik={{ w: 280, h: 100, path: operatorLogo.filename }}
                    />
                  ) : (
                    <p>{getEstablishmentName(booking.establishment)}</p>
                  )}
                  <span className="text-xs text-slate-500">{booking.id}</span>
                </div>
                <span className="flex flex-1"></span>
              </div>
              <div className="row-span-2 max-w-[350px]">
                <div className="grid gap-x-3 gap-y-1" style={{ gridTemplateColumns: "auto auto " }}>
                  <span></span>
                  <span className="whitespace-nowrap">{getEstablishmentName(booking.establishment)}</span>
                  <span></span>
                  <span>{booking.establishment.address}</span>
                  <span></span>
                  <span className="pb-2">{establishmentCountry?.country_name}</span>
                  {booking.establishment.telephone && (
                    <Fragment>
                      <span className="font-bold">P</span>
                      <span>{booking.establishment.telephone}</span>
                    </Fragment>
                  )}
                  {booking.establishment.email && (
                    <Fragment>
                      <span className="font-bold">E</span>
                      <span>{booking.establishment.email}</span>
                    </Fragment>
                  )}
                </div>
              </div>
              <div className="text-4xl font-bold pt-3 flex flex-row gap-3 items-center">
                <span>Invoice</span>
                <BookingPaymentBadge status={getPaymentState(booking)} className={"py-1  px-4 text-xl rounded-md"} />
              </div>
            </section>
            <section className="flex flex-row justify-between">
              <div className="space-y-1">
                <p>{invoice?.customer_name}</p>
                <p className="max-w-[300px]">{invoice?.customer_address}</p>
                <p>{getCountry(invoice?.customer_country_code || "")?.country_name}</p>
              </div>
              <div className="">
                <div className="grid grid-cols-2 gap-y-1 gap-x-3" style={{ maxWidth: "350px", gridTemplateColumns: "auto auto" }}>
                  <div className="whitespace-nowrap">VAT number</div>
                  <div className="font-semibold whitespace-nowrap">{booking.establishment.vat_number}</div>
                  <div className="whitespace-nowrap">Invoice date</div>
                  <div className="font-semibold whitespace-nowrap">{booking.created_at_formatted_iso}</div>
                  <div className="whitespace-nowrap">Invoice number</div>
                  <div className="font-semibold whitespace-nowrap">{booking.sqid}</div>
                  {booking.booking_reference && (
                    <Fragment>
                      <div className="whitespace-nowrap">Booking reference</div>
                      <div className="font-semibold">{booking.booking_reference}</div>
                    </Fragment>
                  )}
                </div>
              </div>
            </section>
          </Fragment>
        )}
        {normalView && (
          <Fragment>
            {booking.establishment && (
              <h2 className="text-xl font-semibold text-slate-600">{getEstablishmentName(booking.establishment)}</h2>
            )}
            <div className="flex flex-wrap gap-2 items-center">
              <h1 className="text-2xl">{isBooking ? "Booking" : "Sale"}</h1>
              {!!response.booking.booking_reference && <span className="text-slate-600">{response.booking.booking_reference}</span>}
              {booking.hide_price_for_customer && canEdit && (
                <Tooltip
                  description={
                    <p>
                      Price info in this booking is hidden from your customers but visible to you.
                      <br />
                      Edit the booking to change this setting.
                    </p>
                  }
                >
                  <div className="text-xs rounded-md py-1 px-2 bg-slate-300 text-slate-600 flex flex-row gap-1">
                    <div className="relative flex items-center justify-center w-4 h-4">
                      <DollarSign className="w-2.5 h-2.5" />
                      <CircleOffIcon className="w-4 h-4 left-0 top-0 absolute" />
                    </div>
                    Prices Hidden
                  </div>
                </Tooltip>
              )}
              {!!booking.cancelled_at && (
                <ParamLink paramState={{ toggle_modal: "delete" }} className="text-xs rounded-md bg-slate-600 text-slate-100 py-1 px-3">
                  CANCELLED
                </ParamLink>
              )}
              {!!booking.direct_booking && canEdit && (
                <span className="text-xs rounded-md bg-slate-600 text-slate-100 py-1 px-3">DIRECT</span>
              )}
              {canEdit && normalView && !booking.cancelled_at && (
                <Fragment>
                  <ParamLink path={_booking_mutate} paramState={{ id: response.booking.id }} className="link">
                    edit
                  </ParamLink>
                </Fragment>
              )}
              <span className="flex-1"></span>
              <span className="text-slate-600">{booking.sqid}</span>
            </div>
            <CDialog dialogname={"delete"} className="space-y-3 max-w-screen-md">
              <div className="flex flex-row justify-between items-center">
                <p className="text-xl">Cancelled booking</p>
                <DialogCloseButton />
              </div>
              <ActionForm>
                <RInput table="booking" field="id" value={booking.id} />
                <RInput table="booking" field="data.cancelled_at" value={""} type="hidden" />
                <RedirectParamsInput path="./" paramState={{ toggle_modal: undefined }} />
                <SubmitButton className={"btn btn-basic"}>Restore</SubmitButton>
              </ActionForm>
              {false && (
                <ActionForm>
                  <RInput table="booking" field="id" value={booking.id} />
                  <RInput table="booking" field="data.cancelled_at" value={"null"} type="hidden" />
                  <OperationInput table="booking" value="delete" />
                  {booking.payments.map((payment) => (
                    <Fragment key={payment.id}>
                      <OperationInput table="payment" index={payment.id} value="delete" />
                      <RInput table={"payment"} field={"id"} index={payment.id} value={payment.id} />
                    </Fragment>
                  ))}
                  {booking.activities.map((activity) => (
                    <Fragment key={activity.id}>
                      <RInput table={"sale_item"} field={"id"} index={activity.id} value={activity.id} />
                      <OperationInput table={"sale_item"} value={"delete"} index={activity.id} />
                      {activity.assignments.map((assignment) => (
                        <Fragment key={assignment.id}>
                          <RInput index={assignment.id} table={"trip_assignment"} field={"id"} value={assignment.id} />
                          <OperationInput index={assignment.id} table={"trip_assignment"} value={"delete"} />
                        </Fragment>
                      ))}
                      {activity.participations.map((participation) => (
                        <Fragment key={participation.id}>
                          <RInput index={participation.id} table={"participation"} field={"id"} value={participation.id} />
                          <OperationInput index={participation.id} table={"participation"} value={"delete"} />
                          {participation.comment_ids?.map((commentId) => (
                            <Fragment key={commentId}>
                              <RInput index={commentId} table={"comment"} field={"id"} value={commentId} />
                              <OperationInput index={commentId} table={"comment"} value={"delete"} />
                            </Fragment>
                          ))}
                        </Fragment>
                      ))}
                    </Fragment>
                  ))}
                  {booking.participants.map((participant) => (
                    <Fragment key={participant.id}>
                      <OperationInput value="delete" table="participant" index={participant.id} />
                      <RInput table="participant" field="id" index={participant.id} />
                    </Fragment>
                  ))}
                  <SubmitButton className={"btn btn-red"}>Delete</SubmitButton>
                </ActionForm>
              )}
            </CDialog>
            {/*{canEdit && !booking.cancelled_at && booking.direct_booking && (*/}
            {/*  <div>*/}
            {/*    <p>What would you like to do with this direct booking?</p>*/}
            {/*    <ActionForm>*/}
            {/*      {mainParticipant && (*/}
            {/*        <Fragment>*/}
            {/*          <p>*/}
            {/*            Message to {mainParticipant.email} ({mainParticipant.first_name} {mainParticipant.last_name})*/}
            {/*          </p>*/}
            {/*          <RTextarea table={"booking"} field={"data.direct_booking"} className="input" />*/}
            {/*          <OperationInput table={"callback"} value={"ignore"} />*/}
            {/*          <label className={"flex flex-row gap-2 items-center"}>*/}
            {/*            Send email*/}
            {/*            <OperationInput table={"callback"} value={"insert"} type={"checkbox"} className="checkbox" />*/}
            {/*          </label>*/}
            {/*          <CallbackInput callbackName={"send_participant_registration_email"} target_id={mainParticipant.id} />*/}
            {/*        </Fragment>*/}
            {/*      )}*/}
            {/*      <div className="flex flex-row gap-3">*/}
            {/*        <SubmitButton className="btn btn-green">Approve</SubmitButton>*/}
            {/*        <SubmitButton className="btn btn-red">Decline</SubmitButton>*/}
            {/*      </div>*/}
            {/*    </ActionForm>*/}
            {/*  </div>*/}
            {/*)}*/}
            {(canEdit || myParticipants.length > 0 || true) && showPrice && normalView && (
              <Fragment>
                <hr />
                <div className="flex flex-wrap gap-3 items-center">
                  <h2 className="text-xl font-bold">Payments</h2>
                  {canEdit && (
                    <ParamLink
                      type={"button"}
                      aria-busy={false}
                      className="link"
                      paramState={{ toggle_payment_form: !search.state.toggle_payment_form }}
                    >
                      {search.state.toggle_payment_form ? "close" : "+add"}
                    </ParamLink>
                  )}
                  <div className="flex-1"></div>
                  {invoice && canEdit && <LockClosedIcon className="w-5 h-5 text-slate-500" />}
                  <BookingPaymentBadge status={getPaymentState(response.booking)} />
                </div>
                <AnimatingDiv>
                  {canEdit && search.state.toggle_payment_form && <PaymentBlock key={response.booking.payments.length} />}
                </AnimatingDiv>
                <p>
                  Outstanding balance:&nbsp;
                  <MoneyValue
                    nativeAmount={outstandingBalanceRaw}
                    nativeCurrency={booking.currency_id}
                    toCurrency={booking.currency_id}
                    locale={establishment?.locale}
                  />
                </p>
                {/*<p>nr of openpaymsne {openPayments.length}</p>*/}
                {openPayments.length > 0 && !canEdit && (
                  <div className="space-y-6">
                    <hr />
                    {openPayments.map((payment) => {
                      const surcharchePercentage = payment.surcharge_percentage / 100;
                      const surchargeAmount = payment.amount * surcharchePercentage;
                      const totalAmount = payment.amount + surchargeAmount;
                      const paymentMethod = payment.payment_method;
                      return (
                        <div key={payment.id} className="space-y-3">
                          <div>
                            <p>
                              Pay{" "}
                              <span className="font-semibold italic text-slate-800">
                                <MoneyValue
                                  nativeAmount={totalAmount}
                                  nativeCurrency={booking.currency_id}
                                  toCurrency={booking.currency_id}
                                  locale={establishment?.locale}
                                />
                              </span>{" "}
                              via {paymentMethod.name}
                            </p>
                            {!!payment.surcharge_percentage && (
                              <p className="text-xs italic text-slate-600">
                                Includes a{" "}
                                <span>
                                  <MoneyValue
                                    nativeAmount={surchargeAmount}
                                    nativeCurrency={booking.currency_id}
                                    toCurrency={booking.currency_id}
                                    locale={establishment?.locale}
                                  />
                                </span>{" "}
                                surcharge
                              </p>
                            )}
                          </div>
                          <div className="flex flex-row gap-3">
                            {!!payment.url && (
                              <a href={payment.url} rel="noreferrer" target={"_blank"} className="btn btn-primary w-fit p-4 px-10">
                                Pay {payment.summed_amount < booking.price_total_final ? "deposit" : "now"}
                              </a>
                            )}
                            {/*{canEdit && (*/}
                            {/*  <ActionForm>*/}
                            {/*    <ResponseIdentifierInput />*/}
                            {/*    <RInput table={"payment"} field={"id"} value={payment.id} />*/}
                            {/*    <RInput table={"payment"} field={"data.payed_at"} value={"true"} type={"hidden"} />*/}
                            {/*    <SubmitButton className="btn btn-secondary">Mark as paid</SubmitButton>*/}
                            {/*  </ActionForm>*/}
                            {/*)}*/}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
                <DividerWithText>Ledger</DividerWithText>
                {payments.length === 0 && <p className="text-xs italic text-slate-600">No payments made</p>}
                {payments.length > 0 && (
                  <div className="overflow-auto text-xs">
                    <table className="w-full">
                      <thead>
                        <tr className="bg-slate-50 text-left text-slate-700 [&_th]:p-2 [&_th]:font-normal">
                          <th>Type</th>
                          <th>Date</th>
                          <th>Method</th>
                          <th className="text-right">Amount</th>
                          {canEdit && <th className="text-right">Paid</th>}
                        </tr>
                      </thead>
                      <tbody>
                        {payments.map((payment) => {
                          const isLoading = navigation.formData?.get(identifierKey) === payment.id;
                          const isSelected = !!search.state.toggle_panels.find((panel) => panel === payment.id);
                          const surchargeAmount = payment.derived_amount_surcharge;
                          const totalAmount = payment.derived_amount_total;
                          const paymentMethod = payment.payment_method;
                          const paymentAmount = payment.payment_amount;
                          const paymentCurrency = payment.payment_currency || booking.currency_id;
                          const conversionRate = paymentAmount && paymentAmount / totalAmount;
                          const paymentUrl = payment.url;
                          const date = payment.payed_at_formatted ? (
                            <Fragment>{payment.payed_at_formatted.slice(0, 10)}</Fragment>
                          ) : (
                            payment.created_at_formatted
                          );
                          return (
                            <Fragment key={payment.id}>
                              <tr className={twMerge("text-slate-600 [&_td]:p-2", isLoading && "animate-pulse opacity-60")}>
                                <td>{payment.summed_amount < booking.price_total_final ? "Deposit" : "Balance"}</td>
                                <td>
                                  {canEdit ? (
                                    <div className="flex flex-row gap-2">
                                      <ParamLink
                                        type={"button"}
                                        paramState={{ toggle_modal: "payment", modal_detail_name: payment.id }}
                                        className={twMerge(isInteractive && "link", isSelected && "text-slate-600")}
                                      >
                                        {date}
                                      </ParamLink>
                                    </div>
                                  ) : (
                                    date
                                  )}
                                </td>
                                <td>
                                  <div className="flex flex-row w-fit gap-2 items-center">
                                    <Tooltip
                                      className="flex w-fit flex-row items-center gap-2"
                                      description={
                                        (!!payment.surcharge_percentage || !!payment.error) && (
                                          <div className="max-w-[320px] space-y-3 bg-white p-4 text-slate-800">
                                            {payment.error && (
                                              <div className="space-y-3">
                                                <p>
                                                  Could not create <span className="font-semibold">{paymentMethod.name}</span> payment.
                                                </p>
                                                <p>{payment.error}</p>
                                                {/*<ActionForm>*/}
                                                {/*  <RInput table={"payment"} field={"id"} value={payment.id} />*/}
                                                {/*  <RInput table={"payment"} field={"data.error"} type={"hidden"} />*/}
                                                {/*  <CallbackInput callbackName={"payment_created"} target_id={tableRef("payment")} />*/}
                                                {/*  <SubmitButton className="link">retry</SubmitButton>*/}
                                                {/*</ActionForm>*/}
                                              </div>
                                            )}
                                            <div className={twMerge("space-y-3", !!payment.error && "opacity-50")}>
                                              <p>
                                                Your <span className="font-semibold">{paymentMethod.name}</span> payment is subject to a{" "}
                                                <span className="font-semibold">{payment.surcharge_percentage}%</span> surcharge
                                              </p>
                                              <p className="flex flex-row justify-between">
                                                <span>Amount</span>
                                                <span>
                                                  <MoneyValue
                                                    nativeAmount={payment.amount}
                                                    nativeCurrency={booking.currency_id}
                                                    toCurrency={booking.currency_id}
                                                    locale={establishment?.locale}
                                                  />
                                                </span>
                                              </p>
                                              <p className="flex flex-row justify-between">
                                                <span>Surcharge</span>
                                                <span>
                                                  <MoneyValue
                                                    nativeAmount={surchargeAmount}
                                                    nativeCurrency={booking.currency_id}
                                                    toCurrency={booking.currency_id}
                                                    locale={establishment?.locale}
                                                  />
                                                </span>
                                              </p>
                                              <hr />
                                              <p className="flex flex-row justify-between font-semibold">
                                                <span>TOTAL</span>
                                                <span>
                                                  <MoneyValue
                                                    nativeAmount={totalAmount}
                                                    nativeCurrency={booking.currency_id}
                                                    toCurrency={booking.currency_id}
                                                    locale={establishment.locale}
                                                  />
                                                </span>
                                              </p>
                                              <p className="text-xs text-slate-600">
                                                The surcharge amount is solely for the use of your payment method and therefore added on top
                                                of your AMOUNT DUE
                                              </p>
                                            </div>
                                            {paymentAmount && paymentAmount !== totalAmount && (
                                              <Alert status={"warning"} className="space-y-3">
                                                <p>
                                                  <strong>Note</strong>: Xendit requires {payment.payment_currency} payments, the original
                                                  amount has therefore been converted using the exchange rate.
                                                </p>
                                                <div className="grid grid-cols-2 gap-x-6 gap-y-3">
                                                  <div className="whitespace-nowrap">
                                                    Exchange rate {booking.currency_id}/{paymentCurrency}
                                                  </div>
                                                  <div className="text-right">{!!conversionRate && conversionRate.toFixed(4)}</div>
                                                  <div>Initial amount</div>
                                                  <div className="text-right">
                                                    <MoneyValue
                                                      nativeAmount={totalAmount}
                                                      nativeCurrency={booking.currency_id}
                                                      toCurrency={booking.currency_id}
                                                      locale={establishment?.locale}
                                                    />
                                                  </div>
                                                  <div className="whitespace-nowrap">Converted amount</div>
                                                  <div className="text-right">
                                                    <MoneyValue
                                                      nativeAmount={paymentAmount}
                                                      nativeCurrency={paymentCurrency}
                                                      toCurrency={paymentCurrency}
                                                      locale={establishment?.locale}
                                                    />
                                                  </div>
                                                </div>
                                              </Alert>
                                            )}
                                          </div>
                                        )
                                      }
                                    >
                                      {paymentUrl ? (
                                        <a href={paymentUrl} rel="noreferrer" target={"_blank"} className="link">
                                          {paymentMethod.short || paymentMethod.name}
                                        </a>
                                      ) : (
                                        <span>{paymentMethod.short || paymentMethod.name}</span>
                                      )}
                                      {payment.payment_method.xendit && !paymentUrl && !payment.error && (
                                        <span className="inline-block spinner spinner-dark w-4 h-4" />
                                      )}
                                      {!!payment.error ? (
                                        <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                                      ) : (
                                        !!payment.surcharge_percentage && <IoInformationCircleOutline className="h-5 w-5 text-primary" />
                                      )}
                                    </Tooltip>
                                    {canEdit && paymentUrl && (
                                      <button
                                        type={"button"}
                                        onClick={() => {
                                          navigator.clipboard?.writeText(paymentUrl).then(() => {
                                            toast("Payment URL copied");
                                          });
                                        }}
                                        className="hover:text-slate-900"
                                      >
                                        <CopyIcon className="w-4 h-4" />
                                      </button>
                                    )}
                                  </div>
                                </td>
                                <td className="text-right">
                                  <span className={twMerge(paymentAmount && paymentAmount !== totalAmount && "line-through")}>
                                    <MoneyValue
                                      nativeAmount={totalAmount}
                                      nativeCurrency={booking.currency_id}
                                      toCurrency={booking.currency_id}
                                      locale={establishment?.locale}
                                    />
                                  </span>
                                  {paymentAmount && paymentAmount !== totalAmount && (
                                    <span>
                                      &nbsp;
                                      <MoneyValue
                                        nativeAmount={paymentAmount}
                                        nativeCurrency={paymentCurrency}
                                        toCurrency={paymentCurrency}
                                        locale={establishment.locale}
                                      />
                                    </span>
                                  )}
                                </td>
                                {canEdit && (
                                  <td className="text-right">
                                    <ActionForm identifier={payment.id} key={payment.id + payment.payed_at}>
                                      <RInput table={"payment"} field={"id"} value={payment.id} />
                                      {invoice && (
                                        <Fragment>
                                          <CallbackInput callbackName={"send_invoice_to_intuit"} target_id={invoice.id} />
                                          <CallbackInput callbackName={"generate_invoice_pdf"} target_id={invoice.id} />
                                        </Fragment>
                                      )}
                                      <RInput table={"payment"} field={"data.payed_at"} value={""} type={"hidden"} />
                                      <RInput
                                        disabled={payment.payment_method.xendit}
                                        table={"payment"}
                                        field={"data.payed_at"}
                                        type={"checkbox"}
                                        className="checkbox"
                                        value={at_now_value}
                                        defaultChecked={!!payment.payed_at}
                                        onChange={(e) => {
                                          submit(e.target.form);
                                        }}
                                      />
                                    </ActionForm>
                                  </td>
                                )}
                              </tr>
                            </Fragment>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                )}
              </Fragment>
            )}
            <Fragment>
              {invoice && showPrice && (canEdit || !!myParticipants.length) && (
                <div className="flex flex-row items-center gap-3">
                  <BaseLink
                    to={_fileTarget({ target_id: invoice.id, target: "invoice" })}
                    target={"_blank"}
                    className={twMerge("btn btn-primary flex-1")}
                  >
                    Download invoice
                  </BaseLink>
                  {canEdit && (
                    <Fragment>
                      {(invoice.intuit_invoice_id ||
                        (invoice.send_invoice_to_intuit_callbacks.length > 0 && invoice.intuit_connection_id)) && (
                        <Fragment>
                          <ParamLink className="btn btn-green" paramState={{ toggle_modal: "content" }}>
                            Quickbooks
                          </ParamLink>
                          <CDialog dialogname={"content"} className="space-y-3 max-w-screen-md">
                            <div className="flex flex-row justify-between items-center">
                              <p className="text-xl">Quickbooks</p>
                              <DialogCloseButton />
                            </div>
                            <p>
                              {invoice?.intuit_connection?.intuit_company.CompanyName} ({invoice?.intuit_connection?.intuit_user.email})
                            </p>
                            <div className="grid grid-cols-[auto_auto_auto_auto] gap-x-4 gap-y-1 pb-3 text-sm text-slate-700">
                              <div className="text-slate-900">Started at</div>
                              <div className="text-slate-900 col-span-3">Finished in</div>
                              {invoice.send_invoice_to_intuit_callbacks.map((callback) => (
                                <Fragment key={callback.id}>
                                  <div className={"whitespace-normal break-words"}>{callback.created_at_formatted}</div>
                                  <div className="whitespace-nowrap">
                                    {callback.duration_in_seconds && callback.duration_in_seconds + " Sec."}
                                  </div>
                                  <div>
                                    {!callback.handled_at ? (
                                      <span className="inline-block spinner spinner-dark w-4 h-4" />
                                    ) : callback.success ? (
                                      <CheckDoneIcon className="h-4 w-4 text-green-500" />
                                    ) : (
                                      <NotDoneIcon className="h-4 w-4 text-red-500" />
                                    )}
                                  </div>
                                  <div className="whitespace-pre-wrap break-all">{callback.msg}</div>
                                </Fragment>
                              ))}
                            </div>
                            {invoice.intuit_invoice_id && (
                              <BaseLink to={_booking_quickbooks(booking.id)} className="link" target={"_blank"}>
                                Open in Quickbooks
                              </BaseLink>
                            )}
                            {search.state.persist_debug && (
                              <ActionForm>
                                <CallbackInput callbackName={"send_invoice_to_intuit"} target_id={invoice.id} />
                                <SubmitButton className="btn btn-basic">Sync</SubmitButton>
                              </ActionForm>
                            )}
                          </CDialog>
                        </Fragment>
                      )}
                      <ActionForm>
                        <RInput table={"invoice"} field={"id"} value={invoice.id} />
                        <OperationInput table={"invoice"} value={"delete"} />
                        {invoice && <CallbackInput callbackName={"send_invoice_to_intuit"} target_id={invoice.id} />}
                        {/*<RInput table={"intuit_invoice"} field={"data.invoice_id"} value={invoice.id} type="hidden" />*/}
                        {/*<OperationInput table={"intuit_invoice"} value={"delete"} />*/}
                        <DeleteButton>Delete invoice</DeleteButton>
                      </ActionForm>
                    </Fragment>
                  )}
                </div>
              )}
              {!invoice && booking.activities.length > 0 && !booking.cancelled_at && booking.establishment.workflow > 1 && canEdit && (
                <ParamLink path={_booking_lock(booking.id)} className="btn btn-primary ">
                  Generate invoice
                </ParamLink>
              )}
              {canEdit && !!booking.internal_note && normalView && (
                <p className="p-2 whitespace-pre-wrap rounded-md bg-secondary-tag text-white">{booking.internal_note}</p>
              )}
              <hr />
              {/*{placeholders.length > 0 && !canEdit && (*/}
              {/*  <div className="app-container sticky top-0 px-0 py-2">*/}
              {/*    <div className="rounded-md bg-orange-200 p-3">Please register to confirm your booking</div>*/}
              {/*  </div>*/}
              {/*)}*/}
              <h2 className="text-xl font-bold">
                Participants {!!booking?.participants.length && false && <span>({booking?.participants.length})</span>}
              </h2>
              <div className="space-y-2">
                {booking.participants.map((participant) => {
                  const allowedForParticipant = myParticipants?.find((p) => p === participant) || canEdit || canStaffPlus;
                  return (
                    <ParamLink
                      // aria-disabled={!enabled}
                      key={participant.id}
                      className={twMerge(
                        "flex gap-2 items-center flex-row hover:bg-slate-50 p-1 rounded-md aria-busy:animate-pulse aria-busy:cursor-progress group aria-disabled:hover:bg-inherit group",
                      )}
                      paramState={{ customer_view: search.state.customer_view }}
                      path={_participant_detail(participant.id)}
                    >
                      <Tooltip
                        className="flex gap-2 items-center flex-row "
                        options={{ placement: "right" }}
                        description={
                          !allowedForParticipant ? (
                            <div className={"max-w-[260px]"}>
                              No access rights to this participant. If this is you, please use the URL in your registration email to gain
                              access.
                            </div>
                          ) : !participant.is_participating ? (
                            <div className={"max-w-[260px]"}>
                              {participant.first_name} is not connected to an activity. This can be done using the 'add/remove participant'
                              hyperlink below
                            </div>
                          ) : (
                            false
                          )
                        }
                      >
                        <div
                          className={twMerge(
                            "text-slate-800 bg-slate-200 rounded-full p-0.5",
                            !participant.is_participating && "outline outline-2 outline-primary",
                          )}
                        >
                          <UserIcon className="w-6 h-6" />
                        </div>
                        <div>
                          <p className="font-semibold group-aria-disabled:text-xl">
                            {participant.first_name} {participant.last_name}
                          </p>
                          {allowedForParticipant && (
                            <p className="text-xs text-slate-600 group-aria-disabled:hidden">{participant.email}</p>
                          )}
                        </div>
                      </Tooltip>
                      <span className="flex-1" />
                      <ParticipantStatusBadges {...participant} />
                      <ChevronRightIcon className="w-5 h-5 text-slate-800 group-aria-disabled:opacity-0" />
                    </ParamLink>
                  );
                })}
              </div>
              {booking.activities
                .filter((saleItem) => saleItem.duration)
                .map((saleItem) => {
                  const product = saleItem.product;
                  const participations = saleItem.participations;
                  const placeholders = participations.filter((participation) => !participation.participant_id);
                  const myParticipants = booking.participants.filter((participant) => participant?.user_id === context.user_id);
                  const lastPlaceholder = placeholders[placeholders.length - 1];
                  const registrations = participations.filter((participation) => participation.participant_id);

                  const addingParticipant = saleItem.id === search.state.sale_item_id;

                  const assignmentsOutsideActivityDuration = saleItem.assignments.filter((assignment) => !assignment.is_within_duration);
                  const datesOutsideActivityDuration = unique(assignmentsOutsideActivityDuration.map((item) => item.trip_date));

                  const daysInHours = (saleItem.duration_in_days || 0) * 24;
                  return (
                    <div key={saleItem.id} className="space-y-2">
                      {/*{product.duration_in_hours}*/}
                      <div className="flex flex-wrap items-center">
                        <p className="font-bold">
                          {product ? activities[product.activity_slug].name : "Custom"} ({saleItem.participations.length})
                        </p>
                        <div className="w-3" />
                        {canEdit && !invoice && (
                          <div className="flex flex-wrap gap-2">
                            <ParamLink className="link" path={_activity_mutate} paramState={{ id: saleItem.id }}>
                              edit
                            </ParamLink>
                            <ActionForm
                              confirmMessage={`Are you sure you want to delete this activity?
${registrations.length > 0 ? `This also deletes it's registrations.` : ""}
${saleItem.assignments.length > 0 ? `This also deletes it's assignments and associated comments` : ""}`}
                            >
                              {/*<InvertTableIndexInput />*/}
                              <RInput table={"sale_item"} field={"id"} value={saleItem.id} />
                              <OperationInput table={"sale_item"} value={"delete"} />
                              {saleItem.assignments.map((assignment) => (
                                <Fragment key={assignment.id}>
                                  <RInput index={assignment.id} table={"trip_assignment"} field={"id"} value={assignment.id} />
                                  <OperationInput index={assignment.id} table={"trip_assignment"} value={"delete"} />
                                </Fragment>
                              ))}
                              {saleItem.participations.map((participation) => (
                                <Fragment key={participation.id}>
                                  <RInput index={participation.id} table={"participation"} field={"id"} value={participation.id} />
                                  <OperationInput index={participation.id} table={"participation"} value={"delete"} />
                                  {participation.comment_ids?.map((commentId) => (
                                    <Fragment key={commentId}>
                                      <RInput index={commentId} table={"comment"} field={"id"} value={commentId} />
                                      <OperationInput index={commentId} table={"comment"} value={"delete"} />
                                    </Fragment>
                                  ))}
                                </Fragment>
                              ))}
                              <DeleteButton />
                            </ActionForm>
                          </div>
                        )}
                        <div className="flex-1" />
                        {saleItem.duration && <span className="text-slate-500 pl-1">{formatDuration(saleItem)}</span>}
                      </div>
                      {datesOutsideActivityDuration.length > 0 && canEdit && (
                        <Alert status={"error"}>
                          There are participant(s) scheduled outside the duration of the activity on&nbsp;
                          {datesOutsideActivityDuration.map((date, index) => (
                            <Fragment key={date}>
                              {index > 0 && <span>, </span>}
                              <ParamLink
                                className="text-slate-600 hover:underline"
                                paramState={{ persist_date: date, persist_establishment_id: booking.establishment_id }}
                                path={_planning}
                              >
                                {date}
                              </ParamLink>
                            </Fragment>
                          ))}
                          .
                          <br />
                          These are not unscheduled automatically, do you want to unschedule these?&nbsp;
                          <div className="inline-block">
                            <DeleteButtonForm
                              table={"trip_assignment"}
                              values={assignmentsOutsideActivityDuration.map((item) => item.id)}
                              className={"inline-block btn btn-red text-white"}
                            >
                              unschedule now
                            </DeleteButtonForm>
                          </div>
                        </Alert>
                      )}
                      <div className="space-y-1  bg-secondary-50 rounded-md pb-2">
                        <div className="space-y-3">
                          <ProductItem
                            item={{
                              title: saleItem.description,
                              ...product,
                              product_prices: showPrice
                                ? [
                                    {
                                      amount: saleItem.price_pp,
                                      currency_id: booking.currency_id,
                                    },
                                  ]
                                : [],
                              // price: showPrice ? activity.price_pp : null,
                              // price_currency: booking.currency_id,
                              duration_in_hours: daysInHours ? `[${daysInHours},${daysInHours}]` : null,
                            }}
                            locale={establishment.locale}
                            to_currency={booking.currency_id}
                          >
                            {canEdit && (saleItem.registration_form || saleItem.latest_form) && (
                              <ParamLink
                                className="bg-slate-50 text-xs py-1 px-3 rounded-md"
                                paramState={{
                                  toggle_modal: "detail",
                                  modal_detail_name: saleItem.id,
                                }}
                              >
                                {saleItem.registration_form?.name}
                              </ParamLink>
                            )}
                          </ProductItem>
                          {!!saleItem.duration && <hr />}
                          {!!saleItem.duration && (
                            <AnimatingDiv className="px-3">
                              {registrations.length === 0 && <p className="text-slate-400">No registrations yet</p>}
                              {(canEdit || myParticipants.length > 0 || true) &&
                                registrations.map((participation) => {
                                  const participant = booking.participants.find(
                                    (participant) => participant.id === participation.participant_id,
                                  );

                                  if (!participant) return <Fragment key={participation.id} />;

                                  const name = `${participant.first_name} ${participant.last_name}`;
                                  return (
                                    <p key={participation.id} className="py-1">
                                      {name}
                                    </p>
                                  );
                                })}
                            </AnimatingDiv>
                          )}
                        </div>
                      </div>
                      {saleItem.duration && placeholders.length > 0 && (
                        <div className="space-y-1 pt-1">
                          {placeholders.map((placeholder) => (
                            <ParamLink
                              key={placeholder.id}
                              className="link flex gap-3 flex-row items-center p-2 hover:bg-slate-50"
                              path={_participant_mutate}
                              paramState={{
                                booking_id: booking.id,
                                toggle_sale_item_ids: [saleItem.id],
                                persist_establishment_id: booking.establishment_id,
                                customer_view: search.state.customer_view,
                              }}
                            >
                              <div className="text-slate-800 bg-slate-200 rounded-full p-0.5">
                                <UserIcon className="w-6 h-6" />
                              </div>
                              <span>Click to register</span>
                              <span className="flex-1" />
                              <ChevronRightIcon className="w-5 h-5 text-slate-800" />
                            </ParamLink>
                          ))}
                        </div>
                      )}
                      {saleItem.duration && (
                        <AnimatingDiv>
                          {addingParticipant ? (
                            <div className="p-3 rounded-md border border-primary ">
                              <div className="space-y-3">
                                <p className="text-slate-700">
                                  Select participant(s) to add or remove from this activity.{" "}
                                  {booking.pending_participants.length > 0 && (
                                    <ParamLink
                                      paramState={{ toggle_pending_participants: !search.state.toggle_pending_participants }}
                                      className="link"
                                    >
                                      {search.state.toggle_pending_participants ? "hide pending" : `add pending`}
                                    </ParamLink>
                                  )}
                                </p>
                                <ActionForm className="space-y-3">
                                  <ActionAlert />
                                  {search.state.toggle_pending_participants && (
                                    <Fragment>
                                      {booking.pending_participants.map((participant) => {
                                        const fieldIndex = participant.id;
                                        const checked = search.state.participant_ids.includes(participant.id);
                                        return (
                                          <div key={participant.id}>
                                            <ParamLink
                                              className="group flex items-center gap-2"
                                              paramState={{ participant_ids: toggleArray(search.state.participant_ids, participant.id) }}
                                              aria-selected={checked}
                                            >
                                              <span className="rounded-[4px] border border-slate-600 group-aria-selected:border-primary w-4 h-4 group-aria-selected:bg-primary bg-white flex items-center justify-center transition-colors">
                                                <CheckIcon className="w-4 h-4 hidden group-aria-selected:block text-white" />
                                              </span>
                                              <div className="flex items-center gap-2 text-left hover:cursor-pointer">
                                                <div className="text-slate-800 bg-slate-200 rounded-full p-0.5">
                                                  <UserIcon className="w-6 h-6" />
                                                </div>
                                                <div>
                                                  <p className="font-semibold">
                                                    {participant.first_name} {participant.last_name}{" "}
                                                    <span className="text-slate-500 font-normal text-xs">(pending)</span>
                                                  </p>
                                                  <p className="text-xs text-slate-600">{participant.email}</p>
                                                </div>
                                                <span className="flex-1" />
                                              </div>
                                            </ParamLink>
                                            {checked && (
                                              <Fragment>
                                                <RInput table={"participant"} field={"id"} index={participant.id} value={participant.id} />
                                                <RInput
                                                  table={"participant"}
                                                  field={"data.booking_id"}
                                                  index={participant.id}
                                                  value={booking.id}
                                                  type={"hidden"}
                                                />
                                                <RInput
                                                  table={"participation"}
                                                  field={"data.participant_id"}
                                                  value={participant.id}
                                                  index={fieldIndex}
                                                  type={"hidden"}
                                                />
                                                <RInput
                                                  table={"participation"}
                                                  field={"data.sale_item_id"}
                                                  value={saleItem.id}
                                                  index={fieldIndex}
                                                  type={"hidden"}
                                                />
                                                <OperationInput table={"participation"} value={"update"} index={fieldIndex} />
                                              </Fragment>
                                            )}
                                          </div>
                                        );
                                      })}
                                      <hr />
                                    </Fragment>
                                  )}
                                  {booking.participants.map((participant) => {
                                    const alreadySelected = registrations.find(
                                      (registration) => registration.participant_id === participant.id,
                                    );
                                    const fieldIndex = alreadySelected ? "00000" + participant.id : participant.id;
                                    return (
                                      <div key={participant.id}>
                                        {alreadySelected && (
                                          <RInput table={"participation"} field={"id"} index={fieldIndex} value={alreadySelected.id} />
                                        )}
                                        <RInput
                                          table={"participation"}
                                          field={"data.participant_id"}
                                          value={alreadySelected ? "" : participant.id}
                                          index={fieldIndex}
                                          type={"hidden"}
                                        />
                                        <RInput
                                          table={"participation"}
                                          field={"data.sale_item_id"}
                                          value={saleItem.id}
                                          index={fieldIndex}
                                          type={"hidden"}
                                        />
                                        <OperationInput
                                          table={"participation"}
                                          value={alreadySelected ? "update" : "ignore"}
                                          index={fieldIndex}
                                        />
                                        <label className="flex items-center gap-2 text-left hover:cursor-pointer">
                                          <OperationInput
                                            table={"participation"}
                                            value={alreadySelected ? "ignore" : "update"}
                                            defaultChecked={!!alreadySelected}
                                            index={fieldIndex}
                                            type={"checkbox"}
                                            className="checkbox"
                                          />
                                          <div className="text-slate-800 bg-slate-200 rounded-full p-0.5">
                                            <UserIcon className="w-6 h-6" />
                                          </div>
                                          <div>
                                            <p className="font-semibold">
                                              {participant.first_name} {participant.last_name}
                                            </p>
                                            <p className="text-xs text-slate-600">{participant.email}</p>
                                          </div>
                                          <span className="flex-1" />
                                        </label>
                                      </div>
                                    );
                                  })}
                                  <RedirectParamsInput
                                    path={"./"}
                                    paramState={{
                                      sale_item_id: null,
                                      participant_ids: [],
                                      toggle_pending_participants: false,
                                    }}
                                  />
                                  <div className="flex flex-row gap-3 items-center">
                                    <ParamLink
                                      paramState={{
                                        sale_item_id: null,
                                        participant_ids: [],
                                        toggle_pending_participants: false,
                                      }}
                                      className="link flex gap-2 p-1 items-center hover:bg-slate-50 aria-busy:spinner spinner-dark"
                                    >
                                      <Trans>Cancel</Trans>
                                    </ParamLink>
                                    <SubmitButton className="btn btn-primary flex items-center gap-2 text-left">Done</SubmitButton>
                                  </div>
                                </ActionForm>
                              </div>
                            </div>
                          ) : (
                            <Fragment>
                              {canEdit && !invoice && (
                                <div className="flex items-center gap-3">
                                  <div className="px-2">
                                    <ActionForm>
                                      <RInput table={"sale_item"} field={"id"} value={saleItem.id} />
                                      <RInput table={"sale_item"} field={"data.quantity"} type={"hidden"} value={saleItem.quantity + 1} />
                                      {/*<RInput table={"participation"} field={"data.sale_item_id"} value={activity.id} type={"hidden"} />*/}
                                      <SubmitButton className="flex gap-2 p-1 items-center text-slate-400 hover:text-slate-800 hover:bg-slate-50 aria-busy:spinner spinner-dark">
                                        <div className=" bg-slate-200 rounded-full p-0.5">
                                          <PlusIcon className="w-4 h-4" />
                                        </div>
                                        <Trans>add slot</Trans>
                                      </SubmitButton>
                                    </ActionForm>
                                  </div>
                                  {lastPlaceholder && !invoice && (
                                    <div>
                                      <ActionForm>
                                        <RInput table={"sale_item"} field={"id"} value={saleItem.id} />
                                        <RInput table={"sale_item"} field={"data.quantity"} value={saleItem.quantity - 1} type={"hidden"} />
                                        <RInput table={"participation"} field={"id"} value={lastPlaceholder.id} />
                                        {lastPlaceholder.assignments.map((assignment) => (
                                          <Fragment key={assignment.id}>
                                            <RInput table={"trip_assignment"} field={"id"} index={assignment.id} value={assignment.id} />
                                            <OperationInput table={"trip_assignment"} index={assignment.id} value={"delete"} />
                                          </Fragment>
                                        ))}
                                        {lastPlaceholder.comment_ids?.map((commentId) => (
                                          <Fragment key={commentId}>
                                            <RInput index={commentId} table={"comment"} field={"id"} value={commentId} />
                                            <OperationInput index={commentId} table={"comment"} value={"delete"} />
                                          </Fragment>
                                        ))}
                                        {/* this will make sure on the backend that no one registered on this placeholder yet. if so it will return an error */}
                                        <RInput table={"participation"} field={"data.participant_id"} type={"hidden"} />

                                        <OperationInput table={"participation"} value={"delete"} />
                                        <SubmitButton className="flex p-2 gap-1 items-center text-red-500 hover:underline aria-busy:animate-pulse aria-busy:spinner spinner-dark">
                                          <div className=" bg-red-100 rounded-full p-0.5">
                                            <MinusIcon className="w-4 h-4" />
                                          </div>
                                          <Trans>remove slot</Trans>
                                        </SubmitButton>
                                      </ActionForm>
                                    </div>
                                  )}
                                  {(booking.participants.length > 0 || booking.pending_participants.length > 0) && (
                                    <div>
                                      <ParamLink
                                        paramState={{
                                          sale_item_id: saleItem.id,
                                          toggle_pending_participants: booking.participants.length === 0,
                                        }}
                                        className="flex gap-2 p-1 items-center text-slate-400 hover:text-slate-800 hover:bg-slate-50 aria-busy:spinner spinner-dark"
                                      >
                                        {/*<div className=" bg-slate-200 rounded-full p-0.5">*/}
                                        {/*  <PlusIcon className="w-4 h-4" />*/}
                                        {/*</div>*/}
                                        <Trans>add/remove participant</Trans>
                                      </ParamLink>
                                    </div>
                                  )}
                                </div>
                              )}
                            </Fragment>
                          )}
                        </AnimatingDiv>
                      )}
                    </div>
                  );
                })}
              {canEdit && !invoice && (
                <div className="flex justify-end gap-3">
                  <ParamLink className="link flex items-center gap-2" path={_activity_mutate} paramState={{ booking_id: booking.id }}>
                    <PlusCircleIcon className="w-6 h-6" />
                    <span>Add activity</span>
                  </ParamLink>
                </div>
              )}
            </Fragment>
            <hr />
          </Fragment>
        )}
        <div className={twMerge("space-y-3", normalView && "p-3 rounded-md border border-primary")}>
          {isBooking && (
            <div className="space-y-3">
              <h3 className="flex flex-row items-center gap-3 text-xl font-semibold">
                <CheckCircleIcon className="h-8 w-8 text-primary" /> <span>Trip details</span>
              </h3>
              <div className="space-y-2 p-3">
                {normalView && (
                  <p className="flex justify-between">
                    <span>Registered participants</span>
                    <span>
                      <strong>{booking.participants.length}</strong>
                      {openSlots.length > 0 && (
                        <span className="text-slate-600 text-xs">
                          &nbsp;({openSlots.length} open slot{openSlots.length > 1 && "s"})
                        </span>
                      )}
                    </span>
                  </p>
                )}
                {printView && (
                  <p className="flex justify-between">
                    <span className="whitespace-nowrap">Activity date{multipleDays && "s"}</span>
                    <span className="font-semibold col-span-2 flex-row gap-2 flex ">
                      {booking.activity_agg ? formatDuration(booking.activity_agg) : ""}
                    </span>
                  </p>
                )}
                <AnimatingDiv className="space-y-2">
                  {canStaffPlus && search.state.toggle_edit ? (
                    <ActionForm className="block rounded-md border-primary border p-3 space-y-3">
                      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
                        <MeetingInput
                          country_code={booking.establishment.country_code || defaultCountryCode}
                          default={booking}
                          operator_address={response.booking.establishment.address}
                        />
                      </div>
                      <div className="flex justify-end gap-2 items-center">
                        <RedirectParamsInput
                          path={"./"}
                          paramState={{
                            toggle_edit: false,
                          }}
                        />
                        <RInput table={"booking"} field={"id"} value={booking.id} />
                        <ParamLink
                          className="link btn px-5 capitalize"
                          paramState={{
                            toggle_edit: !search.state.toggle_edit,
                            rerender: search.state.rerender + 1,
                            element_action: [toInputId(fName("booking", "data.meeting_address"))],
                          }}
                        >
                          {search.state.toggle_edit ? <Trans>Cancel</Trans> : <Trans>Change</Trans>}
                        </ParamLink>
                        {search.state.toggle_edit && <SubmitButton className="btn btn-primary px-20">Save</SubmitButton>}
                      </div>
                    </ActionForm>
                  ) : (
                    <Fragment>
                      <p className="flex justify-between">
                        <span>Meeting time</span>
                        <span className="font-semibold">{booking.meeting_time?.slice(0, 5) || "-"}</span>
                      </p>
                      {meetingType && (
                        <p className="flex justify-between">
                          <span>Meeting location</span>
                          <span className="font-semibold">{meetingType.label}</span>
                        </p>
                      )}
                      {meetingAddress && (
                        <p className="flex flex-wrap items-center justify-end">
                          <button
                            type={"button"}
                            className="hover:text-slate-900 flex flw-wrap items-center pb-1"
                            onClick={() => {
                              navigator.clipboard.writeText(meetingAddress).then(() => {
                                toast("Address copied");
                              });
                            }}
                          >
                            {meetingAddress}&nbsp;
                            {normalView && meetingType?.empty_text !== meetingAddress && <CopyIcon className="w-4 h-4" />}
                          </button>
                        </p>
                      )}
                      {canStaffPlus && normalView && (
                        <div className="flex justify-end gap-2 items-center">
                          <ParamLink
                            className="link"
                            paramState={{
                              toggle_edit: !search.state.toggle_edit,
                              rerender: search.state.rerender + 1,
                              element_action: [toInputId(fName("booking", "data.meeting_address"))],
                            }}
                          >
                            <Trans>change</Trans>
                          </ParamLink>
                        </div>
                      )}
                    </Fragment>
                  )}
                </AnimatingDiv>
              </div>
            </div>
          )}
          {showPrice && (
            <h3 className="flex flex-row items-center gap-3 text-xl font-semibold">
              <CheckCircleIcon className="h-8 w-8 text-primary" /> <span>Price breakdown</span>
            </h3>
          )}
          <div className="space-y-2 p-3">
            {booking.activities.map((activity) => {
              const description = activity.product ? getFullProductTitle(activity.product) : activity.description || "unknown";
              const isOpened = !!search.state.toggle_panels.find((panel) => panel === activity.id);
              return (
                <div key={activity.id}>
                  {isOpened ? (
                    <div className="py-3">
                      <div className=" p-3 space-y-3 border border-primary rounded-md">
                        <div className="flex flex-row items-center justify-between">
                          <span className={"text-md font-semibold"}>Edit item</span>
                          <ActionForm confirmMessage={"Are you sure you want to delete this invoice line?"} className="h-full">
                            <RInput table={"sale_item"} field={"id"} value={activity.id} />
                            <OperationInput table={"sale_item"} value={"delete"} />
                            {activity.participations.map((participation) => (
                              <Fragment key={participation.id}>
                                <RInput table={"participation"} field={"id"} index={participation.id} value={participation.id} />
                                <OperationInput table={"participation"} index={participation.id} value={"delete"} />
                              </Fragment>
                            ))}
                            <DeleteButton>Delete</DeleteButton>
                          </ActionForm>
                        </div>
                        <ActionForm>
                          {activity.participations.map((participation) => {
                            return (
                              <Fragment key={participation.id}>
                                <RInput table={"participation"} field={"id"} index={participation.id} value={participation.id} />
                                <OperationInput table={"participation"} index={participation.id} value={"delete"} />
                              </Fragment>
                            );
                          })}
                          <RInput table={"sale_item"} field={"id"} value={activity.id} />
                          <LineItemForm currency={booking.currency_id} item={activity} />
                        </ActionForm>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-row gap-3 items-center">
                      <span className="font-semibold">{activity.quantity}x</span>
                      {activity.duration || printView || !canEdit ? (
                        <span>{description}</span>
                      ) : (
                        <ParamLink
                          className={twMerge("link", isOpened && "text-slate-600")}
                          paramState={{ toggle_panels: toggleArray(search.state.toggle_panels, activity.id) }}
                        >
                          {isOpened ? "cancel" : description}
                        </ParamLink>
                      )}
                      <span className="flex-1"></span>
                      {showPrice && (
                        <span className="font-semibold">
                          {formatPrice(activity.price_total, booking.currency_id, booking.establishment_locale)}
                        </span>
                      )}
                    </div>
                  )}
                  {activity.activity_addons
                    .filter((item) => item.total_quantity)
                    .map((addon) => {
                      return (
                        <div key={addon.addon_id} className="flex gap-3 flex-row justify-between">
                          <p>
                            <strong>{addon.total_quantity}x</strong> {addon.name}
                          </p>
                          {showPrice && (
                            <span>{formatPrice(Number(addon.total_price), booking.currency_id, booking.establishment_locale)}</span>
                          )}
                        </div>
                      );
                    })}
                </div>
              );
            })}
            {canEdit && !printView && !invoice && (
              <Fragment>
                {search.state.toggle_panels.find((panel) => panel === addItemPanelName) ? (
                  <div className="py-3">
                    <ActionForm className="p-3 rounded-md border border-primary space-y-3">
                      <p className="text-md font-semibold ">Add item</p>
                      <RInput table={"sale_item"} field={"data.booking_id"} type={"hidden"} value={booking.id} />
                      <LineItemForm currency={booking.currency_id} />
                    </ActionForm>
                  </div>
                ) : (
                  <div className="flex gap-3">
                    <ParamLink className="link flex items-center gap-2" path={_retail} paramState={{ booking_id: booking.id }}>
                      <PlusCircleIcon className="w-6 h-6" />
                      <span>Add Retail</span>
                    </ParamLink>
                    <ParamLink
                      className="link flex items-center gap-2"
                      paramState={{
                        toggle_panels: toggleArray(search.state.toggle_panels, addItemPanelName),
                        element_action: [toInputId(fName("sale_item", "data.price_pp"))],
                      }}
                    >
                      <PlusCircleIcon className="w-6 h-6" />
                      <span>Add Item</span>
                    </ParamLink>
                  </div>
                )}
              </Fragment>
            )}
            {showPrice && (
              <p className="flex justify-between gap-3 font-bold">
                <span>Subtotal</span>
                <span>{formatPrice(booking.price_total_raw, booking.currency_id, booking.establishment_locale)}</span>
              </p>
            )}
            {showPrice && booking.price_total_final !== booking.price_total_raw && (
              <p className="flex justify-between gap-3 text-green-500">
                <span className="flex flex-row items-center gap-1">
                  Discount
                  {false && booking.activities.find((activity) => activity.exclude_discount_for_addons) && (
                    <Tooltip description={"Discounts are not applicable to extra features or services, termed as 'add-ons'."}>
                      <IoInformationCircleOutline className="h-5 w-5 text-primary" />
                    </Tooltip>
                  )}
                </span>
                <span className="font-semibold">
                  {formatPrice(booking.price_total_raw - booking.price_total_final, booking.currency_id, booking.establishment_locale)}
                </span>
              </p>
            )}
            {showPrice && !!booking.payed_amount_surcharge && (
              <p className="flex justify-between gap-3">
                <span>{defaultSurchargeLineText}</span>
                <span>{formatPrice(booking.payed_amount_surcharge, booking.currency_id, booking.establishment_locale)}</span>
              </p>
            )}
          </div>
          {showPrice && (
            <p className="flex justify-between rounded-md bg-primary p-2 font-bold text-white">
              <span>Total amount:</span>
              <span>
                {formatPrice(booking.price_total_final + booking.payed_amount_surcharge, booking.currency_id, booking.establishment_locale)}
              </span>
            </p>
          )}
          {showPrice && !!outstandingBalanceRaw && (
            <p className={twMerge("flex justify-between rounded-md p-2 text-slate-500", outstandingBalanceRaw < 0 && "text-red-500")}>
              <span>{outstandingBalanceRaw < 0 ? "Overpaid" : "Outstanding Balance"}</span>
              <span>{formatPrice(outstandingBalanceRaw, booking.currency_id, booking.establishment_locale || defaultLocale)}</span>
            </p>
          )}
          {showPrice && !!booking.vat_rate && <p className="text-center text-slate-700">{getVatSentence(booking.vat_rate)}</p>}
          {printView && invoice && !!invoice.message && (
            <Fragment>
              <div className="pt-8">
                <Alert status={"warning"} className="whitespace-pre-wrap ">
                  {invoice.message}
                </Alert>
              </div>
            </Fragment>
          )}
          {normalView && (
            <section className="flex flex-col-reverse">
              <div className="peer empty:hidden p-3 space-y-3">
                {!!booking.message_urls_replaced && (
                  <div>
                    <Suspense fallback={<div>Could not render</div>}>
                      <MarkdocComp
                        content={booking.message_urls_replaced}
                        comps={{ Link: MarkDocLink, Paragraph: MarkdocParagraph }}
                        vars={{}}
                      />
                    </Suspense>
                  </div>
                )}
              </div>
              <h3 className="flex flex-row items-center gap-3 text-xl font-semibold peer-empty:hidden">
                <CheckCircleIcon className="h-8 w-8 text-primary" /> <span>Additional info</span>
              </h3>
            </section>
          )}
        </div>
        {normalView && (
          <div className={"space-y-10"}>
            <AnimatingDiv className="space-y-6">
              <h3 className="text-xl font-semibold" id={shareBookingATagId}>
                Share booking URL
              </h3>
              <div className="flex flex-row gap-6  text-slate-600">
                <a
                  rel={"noreferrer"}
                  href={fullUrl}
                  target={"_blank"}
                  className={" hover:opacity-80  flex flex-col justify-center items-center"}
                  onClick={(e) => {
                    try {
                      if (navigator.clipboard) {
                        e.preventDefault();
                        navigator.clipboard.writeText(fullUrl);
                        toast("URL copied");
                      } else {
                        console.log("both share and clipboard apis are not supported");
                      }
                    } catch (e) {
                      console.log("could not handle onclick copy", e);
                    }
                  }}
                >
                  <DocumentDuplicateIcon className="h-10 w-10" />
                  <span>Copy</span>
                </a>
                <ParamLink
                  className="hover:opacity-80 flex flex-col justify-center items-center"
                  paramState={{ toggle_qr: !search.state.toggle_qr }}
                >
                  <QrCodeIcon className="h-10 w-10" />
                  <span>{search.state.toggle_qr ? "Hide" : "Show"} QR</span>
                </ParamLink>
                <a
                  rel={"noreferrer"}
                  href={fullUrl}
                  target={"_blank"}
                  className={"flex flex-col justify-center items-center hover:opacity-80"}
                  onClick={(e) => {
                    try {
                      if (navigator.share) {
                        e.preventDefault();
                        navigator.share({ title: "Booking", text: "Booking", url: fullUrl });
                      } else if (navigator.clipboard) {
                        e.preventDefault();
                        navigator.clipboard.writeText(fullUrl);
                        toast("URL copied, share is not supported for this browser");
                      } else {
                        console.log("both share and clipboard apis are not supported");
                      }
                    } catch (e) {
                      console.log("could not handle onclick copy", e);
                    }
                  }}
                >
                  <ShareIcon className="h-10 w-10" />
                  <span>Share</span>
                </a>
                {!!establishment.whatsapp_message_template && canEdit && (
                  <a
                    rel={"noreferrer"}
                    href={fullUrl}
                    target={"_blank"}
                    className={" hover:opacity-80  flex flex-col justify-center items-center"}
                    onClick={(e) => {
                      try {
                        if (navigator.clipboard) {
                          e.preventDefault();
                          navigator.clipboard.writeText(`${establishment.whatsapp_message_template} 

${fullUrl}`);
                          toast("Message + URL copied");
                        } else {
                          console.log("both share and clipboard apis are not supported");
                        }
                      } catch (e) {
                        console.log("could not handle onclick copy", e);
                      }
                    }}
                  >
                    <DocumentDuplicateIcon className="h-10 w-10" />
                    <span>
                      Copy +<br />
                      Message
                    </span>
                  </a>
                )}
              </div>
              {search.state.toggle_qr && (
                <div>
                  <QRCodeSVG height={300} width={300} value={fullUrl} />
                </div>
              )}
            </AnimatingDiv>
            {canEdit && !booking.cancelled_at && (
              <div className="flex flex-row gap-3">
                <ActionForm
                  preventScrollReset={false}
                  confirmMessage={`Cancel booking ${
                    booking.booking_reference || booking.sqid
                  }? \n\n This will:\n - Remove all participants from any scheduled trips \n - Not influence any previous payments made or outstanding payments. \n\n Note: Handling refunds or managing outstanding payments must be arranged separately and are not automatically adjusted with the cancellation.`}
                >
                  {allAssignments.map((assignment) => (
                    <Fragment key={assignment.id}>
                      <RInput table={"trip_assignment"} field={"id"} value={assignment.id} />
                      <OperationInput table={"trip_assignment"} value={"delete"} />
                    </Fragment>
                  ))}
                  <RInput table={"booking"} field={"data.cancelled_at"} type={"hidden"} value={"true"} />
                  <RInput table={"booking"} field={"id"} value={booking.id} />
                  <SubmitButton className="btn border border-primary disabled:loading-dots text-red-500 disabled:opacity-60">
                    Cancel booking
                  </SubmitButton>
                </ActionForm>
                <ParamLink
                  className="btn btn-primary flex-1"
                  path={search.state.previous_path || search.state.persist_previous_path || _planning}
                  paramState={
                    search.state.persist_operator_id
                      ? {}
                      : {
                          persist_establishment_id: booking.establishment_id,
                          persist_operator_id: booking.establishment?.operator_id,
                        }
                  }
                >
                  Done
                </ParamLink>
              </div>
            )}
          </div>
        )}
      </section>
      <CDialog dialogname={"payment"}>
        <PaymentDialogContent />
      </CDialog>
      <CDialog dialogname={"detail"}>
        <ActivityModal />
      </CDialog>
    </div>
  );
}
