import { redirect } from "@remix-run/server-runtime";
import { getAdminStorageSingapore } from "~/utils/firebase.server";
import { notFound, notFoundOrUnauthorzied } from "~/misc/responses";
import { kysely } from "~/misc/database.server";
import { fileTargets } from "~/domain/file/file-resource";
import { getSessionSimple } from "~/utils/session.server";
import { LoaderFunctionArgs } from "@remix-run/router";

const minutes10InMillis = 10 * 60 * 1000;

// export const action = async (args: DataFunctionArgs) => {
//   const filename = args.params.filename!;
//   const { session_id } = await getSessionSimple(args.request);
//
//   const file = await kysely
//     .selectFrom("file")
//     .where("file.filename", "=", filename)
//     .where("file.created_by_session_id", "=", session_id)
//     .selectAll("file")
//     .executeTakeFirst();
//
//   if (!file) throw notFoundOrUnauthorzied();
//
//   const storage = await getAdminStorageSingapore();
//   const bucket = storage.bucket();
//
//   const fileRef = bucket.file(filename);
//   const exists = await fileRef.exists();
//
//   if (exists) throw new Error("already uploaded");
//
//   const url = await fileRef.getSignedUrl({
//     action: "write",
//     version: "v4",
//     expires: Date.now() + minutes10InMillis,
//   });
//   const finalUrl = url && url[0];
//
//   if (!finalUrl) throw notFoundOrUnauthorzied("could not create signed write url");
//   return url;
// };

export const loader = async (args: LoaderFunctionArgs) => {
  const filename = args.params.filename!;

  const { session_id } = await getSessionSimple(args.request);
  const file = await kysely
    .selectFrom("file")
    .leftJoin("file_target", "file_target.file_id", "file.id")
    .where("file.filename", "=", filename)
    .where("file.deleted_at", "is", null)
    .where((eb) => {
      const publicFileCmpr = eb("file.public", "=", true);
      const canIDownloadCmpr = eb.or(
        Object.entries(fileTargets).map(([target, ebFn]) => {
          const fileTargetCmpr = eb("file_target.target", "=", target);
          const downloadEb = ebFn.download;
          if (!downloadEb) return fileTargetCmpr;
          const ebFnResult = ebFn.download(eb, { trx: kysely, request: args.request, ctx: { session_id: session_id } });
          return eb.and([fileTargetCmpr, ebFnResult]);
        }),
      );

      if (session_id) {
        const uploadedByMeCmpr = eb("file.created_by_session_id", "=", session_id);
        return eb.or([publicFileCmpr, canIDownloadCmpr, uploadedByMeCmpr]);
      }

      return eb.or([publicFileCmpr, canIDownloadCmpr]);
    })
    .selectAll("file")
    .executeTakeFirst();

  if (!file) throw notFoundOrUnauthorzied();

  const storage = await getAdminStorageSingapore();
  const bucket = storage.bucket();

  const fileRef = bucket.file(filename);
  const exists = await fileRef.exists();

  if (!exists) throw notFound();
  const url = await fileRef.getSignedUrl({
    action: "read",
    version: "v4",
    expires: Date.now() + minutes10InMillis,
    // extensionHeaders: {
    //   "content-disposition": "inline",
    // },
  });
  const finalUrl = url && url[0];

  if (finalUrl) return redirect(finalUrl, 307);
  throw notFound();
};
