// import { getClientIPAddress, getClientLocales } from "remix-utils";
import { kysely } from "~/misc/database.server";
import { getOrCreateSession } from "~/utils/session.server";
import { eventInputParser } from "~/domain/event/event-parser";
import { roleValidator } from "~/misc/parsers/meta-parser";
import { z } from "zod";
import { activeUserSessionQb } from "~/domain/member/member-queries.server";
import { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/server-runtime";
// import { getClientIPAddress } from "remix-utils/get-client-ip-address";
// import { getClientLocales } from "remix-utils/";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { session_id, init } = await getOrCreateSession(request);
  const formdata = await request.formData();
  const input = eventInputParser.parse(Object.fromEntries(formdata.entries()));

  const urlObj = new URL(input.url);
  const segments = urlObj.pathname.split("/");

  const role = segments.find((segment, index) => {
    return roleValidator.safeParse(segment).success && index === 2;
  });

  const targetId = segments.find((segment) => {
    return z.string().uuid().safeParse(segment).success;
  });

  // const ipAddress = getClientIPAddress(request);
  // const clientLocales = getClientLocales(request);
  const result = await kysely
    .insertInto("event")
    .values({
      ...input,
      role: role,
      session_id: session_id,
      user_id: activeUserSessionQb({ ctx: { session_id: session_id }, trx: kysely }, false).select("_user.id"),
      target_id: targetId || null,
      request: {
        // ip_address: ipAddress,
        // locales: clientLocales,
      },
    })
    .returning("id")
    .executeTakeFirstOrThrow();

  return json({ success: true }, init);
};
