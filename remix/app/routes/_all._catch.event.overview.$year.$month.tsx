import type { DataFunctionArgs } from "@remix-run/server-runtime";
import type { AsyncReturnType } from "type-fest";
import { useLoaderData } from "@remix-run/react";
import { FaPhoneAlt, FaWhatsapp } from "react-icons/fa";
import { MdEmail } from "react-icons/md";
import React from "react";
import { kysely } from "~/misc/database.server";
import { sql } from "kysely";
import { getSessionSimple } from "~/utils/session.server";
import { TbWorld } from "react-icons/tb";
import { unauthorized } from "~/misc/responses";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _establishment_paths } from "~/misc/paths";
import { getDbCachedValue } from "~/server/cache/cache.server";
import { differenceInDays, format } from "date-fns";
import { extractMonth, extractYear } from "~/kysely/kysely-helpers";

export const loader = async ({ request, params }: DataFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);
  const user = await activeUserSessionSimple(kysely, session_id, true).select("_user.editor").executeTakeFirst();
  if (!user?.editor) throw unauthorized();

  const yearStr = params.year!;
  const monthStr = params.month!;

  const year = Number.parseInt(yearStr);
  const monthIndex = Number.parseInt(monthStr) - 1;
  const date = new Date(year, monthIndex);
  const today = new Date();

  const ttlInSeconds = differenceInDays(today, date) > 80 ? null : 60 * 60 * 24;

  const monhtFormatted = format(date, "MMMM yyyy");
  const cacheKey = ["events", yearStr, monthStr, "detail"].join(".");
  const cache = await getDbCachedValue({
    key: cacheKey,
    request: (trx) =>
      trx
        .selectFrom("event")
        .leftJoin("user", "event.user_id", "user.id")
        .where((eb) =>
          eb.or([
            eb("event.user_id", "is", null),
            eb.and([eb("user.editor", "is not", true), eb("event.user_id", "not in", eb.selectFrom("member").select("member.user_id"))]),
          ]),
        )
        .where((eb) => extractMonth(eb.ref("event.created_at")), "=", monthIndex + 1)
        .where((eb) => extractYear(eb.ref("event.created_at")), "=", year)
        .innerJoin("establishment as ol", (eb) =>
          eb.on((eb) =>
            eb("ol.id", "=", eb.ref("event.target_id")).or(
              eb.exists(
                eb
                  .selectFrom("product")
                  .innerJoin("item", "item.id", "product.item_id")
                  .whereRef("product.id", "=", "event.target_id")
                  .whereRef("item.establishment_id", "=", "ol.id"),
              ),
            ),
          ),
        )
        .innerJoin("operator as o", "o.id", "ol.operator_id")
        .innerJoin("spot", "spot.id", "ol.spot_id")
        .select([
          "ol.id",
          "o.name",
          "ol.location_name",
          "spot.name as spot_name",
          (eb) =>
            sql<number>`(count ( distinct ${eb.ref("event.session_id")}) filter (where ${eb.ref("event.tag")} = 'website'))`.as(
              "website_click_count",
            ),
          (eb) =>
            sql<number>`(count ( distinct ${eb.ref("event.session_id")}) filter (where ${eb.ref("event.tag")} = 'telephone'))`.as(
              "telephone_click_count",
            ),
          (eb) =>
            sql<number>`(count ( distinct ${eb.ref("event.session_id")}) filter (where ${eb.ref("event.tag")} = 'whatsapp'))`.as(
              "whatsapp_click_count",
            ),
          (eb) =>
            sql<number>`(count ( distinct ${eb.ref("event.session_id")}) filter (where ${eb.ref("event.tag")} = 'email'))`.as(
              "email_click_count",
            ),
          (eb) => sql<number>`(count ( distinct ${eb.ref("event.session_id")}))`.as("session_count"),
          (eb) =>
            sql<number>`(count ( distinct ${eb.ref("event.session_id")}) filter (where ${eb.ref("user.id")} is null))`.as(
              "anonymous_count",
            ),
          (eb) => sql<number>`(count ( distinct ${eb.ref("user.email")}))`.as("event_count"),
          (eb) => sql<string[]>`(array_agg( distinct ${eb.ref("user.email")}))`.as("event_emails"),
        ])
        .groupBy(["ol.id", "o.name", "ol.location_name", "spot.name"])
        .execute(),
    ttlInSeconds: ttlInSeconds,
  });

  return {
    formatted: monhtFormatted,
    year: Number.parseInt(yearStr),
    month: Number.parseInt(monthStr) - 1,
    events: cache,
  };
};

type LoaderResponse = AsyncReturnType<typeof loader>;

export default function Page() {
  const response = useLoaderData<LoaderResponse>();
  const date = new Date(response.year, response.month, 1);
  const monthAsText = date.toLocaleString("en-EN", {
    year: "numeric",
    month: "long",
  });

  return (
    <div className="app-container">
      <h2 className={"text-xl font-bold"}>Totals per operator in {monthAsText}</h2>
      <div className={"flex-col space-y-3 py-2"}>
        {response.events.response.map((item) => (
          <ParamLink
            path={_establishment_paths(item.id).insight}
            target={"_blank"}
            className={"flex flex-col space-y-2 rounded bg-gray-100 p-3"}
            key={item.id}
          >
            <div className={"flex flex-row space-y-1"}>
              <span>
                {item.name}
                {item.location_name ? " - " + item.location_name : ""} in {item.spot_name}
              </span>
              <div className={"flex-1"} />
              <div className={"flex flex-row flex-wrap space-x-3"}>
                <span className={"font-bold"}>{item.session_count} sessions</span>
                <span className={"font-bold"}>{item.anonymous_count} anonymous</span>
                <span className={"font-bold"}>{item.event_count} emails</span>
              </div>
            </div>
            <div className={"flex flex-col space-y-1"}>
              <div className={"flex flex-row flex-wrap space-x-3"}>
                {item.website_click_count > 0 && (
                  <div className="flex flex-row items-center space-x-1 rounded bg-primary p-2 text-white">
                    <TbWorld />
                    <span>website clicks: {item.website_click_count}</span>
                  </div>
                )}
                {item.telephone_click_count > 0 && (
                  <div className="flex flex-row items-center space-x-1 rounded bg-primary p-2 text-white">
                    <FaPhoneAlt />
                    <span>telephone clicks: {item.telephone_click_count}</span>
                  </div>
                )}
                {item.whatsapp_click_count > 0 && (
                  <div className="flex flex-row items-center space-x-1 rounded bg-primary p-2 text-white">
                    <FaWhatsapp />
                    <span>whatsapp clicks: {item.whatsapp_click_count}</span>
                  </div>
                )}
                {item.email_click_count > 0 && (
                  <div className="flex flex-row items-center space-x-1 rounded bg-primary p-2 text-white">
                    <MdEmail />
                    <span>email clicks: {item.email_click_count}</span>
                  </div>
                )}
              </div>
            </div>
            <span>{item.event_emails.join(", ")}</span>
          </ParamLink>
        ))}
      </div>
    </div>
  );
}
