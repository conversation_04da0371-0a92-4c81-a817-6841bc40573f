import { useLoaderD<PERSON>, useNavigation } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { getSessionSimple } from "~/utils/session.server";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { ParamLink } from "~/components/meta/CustomComponents";
import { advancedBookingQb } from "~/domain/booking/booking-queries";
import { Fragment, ReactNode } from "react";
import { unauthorized } from "~/misc/responses";
import { activeUserSessionSimple, memberIsAdminOrOwnerQb, QbArgs } from "~/domain/member/member-queries.server";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { CheckState, mergeStateToParams, paramsToRecord, SortObj, StateInputKey, toggleArray } from "~/misc/parsers/global-state-parsers";
import { operatorQb } from "~/domain/operator/operator-queries";
import { LoaderFunctionArgs } from "@remix-run/router";
import { BookingItem, BookingPaymentBadge, getPaymentState, getRegistrationStatus } from "~/domain/booking/booking-components";
import { _activity_mutate, _booking, _booking_detail } from "~/misc/paths";
import { ascNullsLast, dateRange, descNullsFirst, lower, sqid, tstzToDate } from "~/kysely/kysely-helpers";
import { InputSearchParamCopies } from "~/components/meta/input";
import { ActionForm } from "~/components/form/BaseFrom";
import { ExpressionBuilder, sql } from "kysely";
import { MagnifyingGlassIcon, PlusCircleIcon, XMarkIcon } from "@heroicons/react/20/solid";
import { DB } from "~/kysely/db";
import { redirect, type SerializeFrom } from "@remix-run/server-runtime";
import { Alert } from "~/components/base/alert";
import { AnimatingDiv } from "~/components/base/base";
import { ExportIcon } from "~/components/Icons";
import { escapeCSV } from "~/misc/csv-helpers";
import { twMerge } from "tailwind-merge";
import { paymentsStates, PaymentState } from "~/domain/booking/booking-payment";
import { CDialog } from "~/components/base/Dialog";
import { entries, keys } from "~/misc/helpers";
import { useAppContext } from "~/hooks/use-app-context";
import { pageLimits, Paging } from "~/components/Paging";
import { getProductTitle } from "~/domain/product/ProductItem";
import { ChevronDown, FilterIcon, SortAscIcon } from "lucide-react";
import { NavigatingSelect } from "~/components/NavigatingSelect";
import { at_infinity_value } from "~/kysely/db-static-vars";

export { action } from "~/routes/_all._catch.resource";

const tabs = ["active", "cancelled"] as const;
type Tab = (typeof tabs)[number];

const pageTypes = { activity_start_date: "Activity Date", booking_created_at: "Book Date" } as const;
type PageType = keyof typeof pageTypes;

const defaultSorts: SortObj[] = [{ key: "activity_start_date" satisfies PageType, direction: "asc" }];

const filters = ["custom_range", "search", "booking_type", "payment_status", "sort_direction", "created_by"] as const;
type Filter = (typeof filters)[number];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);
  const { session_id } = await getSessionSimple(request);

  const tab: Tab = tabs.find((tab) => tab === state.tab) || "active";

  const operator = await operatorQb(state).executeTakeFirstOrThrow();
  if (state.date_from === null && state.date_to === null) {
    const todayIso = operator.establishments[0]?.today_iso || "";
    mergeStateToParams(url.searchParams, {
      date_from: todayIso,
      date_to: todayIso,
    });
    throw redirect(url.toString(), 307);
  }

  const args: QbArgs = { trx: kysely, ctx: { session_id: session_id } };
  const allowedEstablishmentIdsQb = memberIsAdminOrOwnerQb(args, "read").select("_member.establishment_id");

  const pageLimit = pageLimits.find((limit) => limit === state.page_limit) || pageLimits[0];

  const checkDate = (date?: string | null) => {
    if (!date) return 0;
    try {
      return new Date(date).getTime();
    } catch (e) {
      console.log("error", e);
      mergeStateToParams(url.searchParams, {
        date_from: undefined,
        date_to: undefined,
        error_message: "invalid date format",
      });
      throw redirect(url.toString(), 307);
    }
  };
  if (checkDate(state.date_from) > checkDate(state.date_to)) {
    mergeStateToParams(url.searchParams, {
      date_from: undefined,
      date_to: undefined,
      error_message: "End date cannot be set before the start date.",
    });
    throw redirect(url.toString(), 307);
  }

  const pageType: PageType = keys(pageTypes).find((sort) => state.page_type === sort) || "activity_start_date";
  const fromDate = sql.lit(state.date_from || null);
  const toDate = sql.lit(state.date_to || null);
  const range = dateRange(fromDate, toDate);

  const allowedCtxQb = activeUserSessionSimple(kysely, session_id, true).select((eb) => {
    const getWhere = (eb: ExpressionBuilder<DB, "booking" | "user" | "establishment" | "region" | "invoice">, tab: Tab) => {
      const bookingCreatedAtDate = tstzToDate(eb.ref("booking.created_at"), eb.ref("region.timezone"));
      const cmprs = [
        eb("establishment.id", "in", allowedEstablishmentIdsQb),
        eb("establishment.operator_id", "=", state.persist_operator_id),
        pageType === "booking_created_at"
          ? eb.and([
              eb(bookingCreatedAtDate, ">=", sql.lit(state.date_from || "-" + at_infinity_value)),
              eb(bookingCreatedAtDate, "<=", sql.lit(state.date_to || at_infinity_value)),
            ])
          : eb("booking.cached_duration", "&&", range),
      ];

      if (state.toggle_invoice) {
        cmprs.push(eb("invoice.id", "is not", null));
      }
      if (state.direct_booking !== "mixed") {
        cmprs.push(eb("booking.direct_booking", "=", state.direct_booking === "true"));
      }

      if (state.created_by) {
        cmprs.push(eb("user.id", "=", state.created_by));
      }

      if (state.search) {
        cmprs.push(
          eb.or([eb("booking.booking_reference", "ilike", `%${state.search}%`), eb(sqid(eb.ref("booking.id_seq")), "=", state.search)]),
        );
      }

      const paymentStateCmpr = {
        open: eb.and([eb("booking.cached_final_paid", "=", 0), eb("booking.cached_final_amount", ">", 0)]),
        deposit: eb.and([
          eb("booking.cached_final_paid", ">", 0),
          eb("booking.cached_final_paid", "<", eb.ref("booking.cached_final_amount")),
        ]),
        overpaid: eb("booking.cached_final_paid", ">", eb.ref("booking.cached_final_amount")),
        paid: eb("booking.cached_final_paid", "=", eb.ref("booking.cached_final_amount")),
      } satisfies Record<PaymentState, unknown>;
      if (state.booking_payment_states.length) {
        const paymentStateCmprs = state.booking_payment_states.map((status) => paymentStateCmpr[status]);
        cmprs.push(eb.or(paymentStateCmprs));
      }

      if (state.persist_establishment_id) {
        cmprs.push(eb("establishment.id", "=", state.persist_establishment_id));
      }

      const tabCmprs = {
        active: [eb("booking.cancelled_at", "is", null)],
        // direct: [eb("booking.cancelled_at", "is", null), eb("booking.direct_booking", "=", true)],
        cancelled: [eb("booking.cancelled_at", "is not", null)],
      } satisfies Record<Tab, any>;

      const selectedTabCmprs = tabCmprs[tab];

      return eb.and([...cmprs, ...selectedTabCmprs]);
    };

    const countBaseQb2 = kysely
      .selectFrom("booking")
      .innerJoin("establishment", "establishment.id", "booking.establishment_id")
      .where("booking.cart_for_session_id", "is", null)
      .leftJoin("spot", "establishment.spot_id", "spot.id")
      .leftJoin("region", "region.id", "spot.region_id")
      .leftJoin("invoice", "invoice.booking_id", "booking.id")
      .leftJoin("user_session", "user_session.id", "booking.created_by_user_session_id")
      .leftJoin("user", "user.id", "user_session.user_id")
      .select((eb) => eb.fn.count("booking.id").as("count"));

    return [
      countBaseQb2.where((eb) => getWhere(eb as any, "active")).as("booking_count_active"),
      countBaseQb2.where((eb) => getWhere(eb as any, "cancelled")).as("booking_count_cancelled"),
      jsonArrayFrom(
        kysely
          .selectFrom("member")
          .innerJoin("user", "user.id", "member.user_id")
          .innerJoin("establishment", "establishment.id", "member.establishment_id")
          .where("member.deleted_at", "=", at_infinity_value)
          .where((eb) => {
            const operatorCmpr = eb("establishment.operator_id", "=", state.persist_operator_id);
            if (!state.persist_establishment_id) return operatorCmpr;
            const establishmentCmpr = eb("member.establishment_id", "=", state.persist_establishment_id);
            return eb.and([operatorCmpr, establishmentCmpr]);
          })
          .select(["member.id", "member.name", "user.email", "user.id as user_id"]),
      ).as("members"),
      jsonArrayFrom(
        advancedBookingQb
          .where("booking.cart_for_session_id", "is", null)
          .leftJoin("invoice", "invoice.booking_id", "booking.id")
          .leftJoin("user_session", "user_session.id", "booking.created_by_user_session_id")
          .leftJoin("user", "user.id", "user_session.user_id")
          .where((eb) => getWhere(eb as any, tab))
          .select((eb) => [
            eb
              .selectFrom("user_session")
              .where("user_session.id", "=", eb.ref("booking.created_by_user_session_id"))
              .innerJoin("user", "user.id", "user_session.user_id")
              .leftJoin("member", (join) =>
                join
                  .onRef("member.user_id", "=", "user.id")
                  .onRef("member.establishment_id", "=", "booking.establishment_id")
                  .on("member.deleted_at", "=", at_infinity_value),
              )
              .select((eb) => eb.fn.coalesce("member.name", "user.email").as("name"))
              .limit(1)
              .as("created_by"),
            jsonObjectFrom(
              eb.selectFrom("invoice").selectAll("invoice").where("invoice.booking_id", "=", eb.ref("booking.id")).limit(1),
            ).as("invoice"),
          ])
          .offset(state.page_nr * pageLimit)
          .orderBy(
            (eb) => (pageType === "booking_created_at" ? eb.ref("booking.created_at") : lower(eb.ref("booking.cached_duration"))),
            state.toggle_direction ? descNullsFirst : ascNullsLast,
          )
          // .$call((eb) => {
          //   let finalEb = eb;
          //
          //   state.sorts.forEach((sort) => {
          //     const direction = sort?.direction === "desc" ? descNullssFirst : ascNullsLast;
          //     const sortImpls = {
          //       activity_start_date: finalEb.orderBy((eb) => lower(eb.ref("booking.cached_duration")), direction),
          //       booking_created_at: finalEb.orderBy((eb) => eb.ref("booking.created_at"), direction),
          //     } satisfies Record<PageType, any>;
          //     const impl = sortImpls[sort.key as PageType];
          //     if (impl) {
          //       finalEb = impl;
          //     }
          //   });
          //
          //   return finalEb;
          // })
          .orderBy("booking.created_at", ascNullsLast)
          .limit(pageLimit),
      ).as("bookings"),
    ];
  });

  console.time("booking query");
  const allowedCtx = await allowedCtxQb.executeTakeFirst();
  console.timeEnd("booking query");

  if (!allowedCtx) throw unauthorized();

  return {
    operator: operator,
    page_type: pageType,
    tab: tab,
    ctx: allowedCtx,
    page_limit: pageLimit,
  };
};

type LoaderData = SerializeFrom<typeof loader>;

type Booking = LoaderData["ctx"]["bookings"][number];

interface ColumnCellDef {
  label: string;
  Text: (item: Booking) => string | number | null | undefined;
  Comp?: (item: Booking) => ReactNode;
}

const columns = [
  {
    label: "Booking Id",
    Text: (booking) => booking.sqid || booking.id,
    Comp: (booking) => <ParamLink path={_booking_detail(booking.id)}>{booking.sqid || booking.id}</ParamLink>,
  },
  {
    label: "Booking Ref",
    Text: (booking) => booking.booking_reference,
    Comp: (booking) => <ParamLink path={_booking_detail(booking.id)}>{booking.booking_reference}</ParamLink>,
  },
  {
    label: "Book Date",
    Text: (booking) => booking.created_at?.slice(0, 10),
  },
  {
    label: "Booked by",
    Text: (booking) => booking.created_by,
  },
  {
    label: "Booked Source",
    Text: (booking) => booking.booking_source,
  },
  {
    label: "Pax",
    Text: (booking) => booking.participants.length + "",
  },
  {
    label: "Payment status",
    Text: (booking) => {
      const paymentState = getPaymentState(booking);
      return paymentState && paymentsStates[paymentState];
    },
  },
  {
    label: "Registration",
    Text: (booking) => getRegistrationStatus(booking),
  },
  {
    label: "Currency",
    Text: (booking) => booking.currency_id,
  },
  {
    label: "Total amount",
    Text: (booking) => booking.cached_final_amount,
  },
  {
    label: "Outstanding Balance",
    Text: (booking) => (booking.cached_final_amount || 0) - (booking.cached_final_paid || 0),
  },
  {
    label: "Items",
    Text: (booking) =>
      booking.activities
        ?.map((activity) => {
          const product = activity.product;
          const itemTitle = product ? product.activity_slug + ", " + getProductTitle(product) : activity.description || "";
          return escapeCSV(itemTitle);
        })
        .filter((title) => !!title)
        .join("|"),
  },
  {
    label: "From",
    Text: (booking) => booking.activity_agg?.duration_start,
  },
  {
    label: "To",
    Text: (booking) => booking.activity_agg?.duration_end,
  },
  // {
  //   label: "Activity Date(s)",
  //   Text: (booking) =>
  //     uniq(
  //       booking.activities
  //         .filter((activity) => activity.date_start_formatted)
  //         .map((activity) => {
  //           if (activity.date_start_formatted === activity.date_end_formatted) return activity.date_start_formatted;
  //           return activity.date_start_formatted + "-" + activity.date_end_formatted;
  //         }),
  //     ).join("|"),
  // },
  // {
  //   label: "Customer",
  //   Text: (booking) => booking.invoice?.customer_name,
  // },
  // {
  //   label: "Invoice Date",
  //   Text: (booking) => booking.invoice?.created_at?.slice(0, 10),
  // },
  // {
  //   label: "Due Date",
  //   Text: (booking) => booking.invoice?.created_at?.slice(0, 10),
  // },
] satisfies ColumnCellDef[];

const DismissableErrorAlert = () => {
  const search = useSearchParams2();

  if (!search.state.error_message) return <Fragment />;
  return (
    <Alert status={"error"} className="flex flex-row items-center justify-between">
      {search.state.error_message}
      <ParamLink paramState={{ error_message: undefined }} className="btn btn-text font-bold">
        <XMarkIcon className="h-5 w-5" />
      </ParamLink>
    </Alert>
  );
};

const searchInputId = "input-search";
const filterPanelName = "filter";

type RangeButton = { label: string; from: string; to: string; toggle_direction?: boolean };

const directBookingStatesObj: Record<CheckState, string> = {
  mixed: "All",
  true: "Only Direct Bookings",
  false: "Exclude Direct Bookings",
};

export default function Page() {
  const response = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const app = useAppContext();
  const responseCtx = response.ctx;
  const navigation = useNavigation();
  const searchIsIdle = navigation.state === "idle";

  if (typeof responseCtx === "string") return <div>{responseCtx}</div>;

  const operatorEstablishments = response.operator?.establishments || [];
  const firstEstablishment = operatorEstablishments[0];
  const todayFormatted = firstEstablishment?.today_formatted || "";
  const todayIso = firstEstablishment?.today_iso || "";
  const yesterdayIso = firstEstablishment?.yesterday_iso || "";
  const tomorrowIso = firstEstablishment?.tomorrow_iso || "";
  const last30Days = firstEstablishment?.last30days_iso || "";
  const next30Days = firstEstablishment?.next30days_iso || "";
  const startYear = firstEstablishment?.start_year_iso || "";
  const endYear = firstEstablishment?.end_year_iso || "";

  const todayButton: RangeButton = { label: "Today", from: todayIso, to: todayIso };
  const rangeFilterButtons: RangeButton[] =
    response.page_type === "booking_created_at"
      ? [
          { label: "Yesterday", from: yesterdayIso, to: yesterdayIso },
          todayButton,
          { label: "Last 30 Days", from: last30Days, to: yesterdayIso, toggle_direction: true },
          { label: todayIso.slice(0, 4), from: startYear, to: endYear },
          { label: "All time", from: "", to: "" },
        ]
      : [
          todayButton,
          { label: "Tomorrow", from: tomorrowIso, to: tomorrowIso },
          { label: "Last 30 Days", from: last30Days, to: yesterdayIso, toggle_direction: true },
          { label: "Next 30 Days", from: todayIso, to: next30Days },
          { label: todayIso.slice(0, 4), from: startYear, to: endYear },
          { label: "All time", from: "", to: "" },
        ];

  const activeRangeFilterButton = rangeFilterButtons.find(
    (button) => search.state.date_from === button.from && search.state.date_to === button.to,
  );

  const createdByFilter = response.ctx.members.find((member) => member.user_id === search.state.created_by);

  const filtersObj: Record<Filter, { isActive: () => boolean; modal?: () => ReactNode }> = {
    custom_range: { isActive: () => !activeRangeFilterButton },
    search: { isActive: () => !!search.state.search },
    created_by: {
      isActive: () => !!search.state.created_by,
      modal: () => {
        return (
          <div className="space-y-3">
            <p>Created by</p>
            <ParamLink
              path={"./"}
              className="text-sm px-3 py-1 bg-slate-100 hover:bg-slate-100 transition-colors rounded-md block aria-selected:btn-secondary"
              paramState={{ created_by: undefined, toggle_modal: undefined }}
              aria-selected={!search.state.created_by}
            >
              All
            </ParamLink>
            {response.ctx.members.map((member) => (
              <ParamLink
                key={member.id}
                path={"./"}
                className="text-sm px-3 py-1 bg-slate-100 hover:bg-slate-100 transition-colors rounded-md block aria-selected:btn-secondary"
                paramState={{ created_by: member.user_id, toggle_modal: undefined }}
                aria-selected={search.state.created_by === member.user_id}
              >
                {member.name} ({member.email})
              </ParamLink>
            ))}
          </div>
        );
      },
    },
    payment_status: {
      isActive: () => !!search.state.booking_payment_states.length,
      modal: () => {
        return (
          <div className="space-y-3">
            <p>Payment Status</p>
            <div className="space-y-4">
              <ParamLink
                className="block"
                path={"./"}
                replace
                paramState={{
                  // booking_payment_status: null,
                  booking_payment_states: [],
                  toggle_modal: undefined,
                }}
              >
                <BookingPaymentBadge status={null} />
              </ParamLink>
              {keys(paymentsStates).map((state) => (
                <ParamLink
                  className="block group "
                  key={state}
                  path={"./"}
                  replace
                  aria-selected={search.state.booking_payment_states.includes(state)}
                  paramState={{
                    // booking_payment_status: state,
                    booking_payment_states: toggleArray(search.state.booking_payment_states, state),
                    toggle_modal: undefined,
                    toggle_panels: filterPanelToggledArr,
                  }}
                >
                  <BookingPaymentBadge status={state} />
                </ParamLink>
              ))}
            </div>
          </div>
        );
      },
    },
    booking_type: {
      isActive: () => search.state.direct_booking !== "mixed",
      modal: () => {
        return (
          <div className="space-y-3">
            <p>Booking Type</p>
            {entries(directBookingStatesObj).map(([key, label]) => (
              <ParamLink
                path="./"
                key={key}
                aria-selected={search.state.direct_booking === key}
                aria-disabled={!searchIsIdle}
                className="text-sm px-3 py-1 bg-slate-100 hover:bg-slate-100 aria-selected:bg-slate-200 transition-colors rounded-md block"
                paramState={{ direct_booking: key, toggle_panels: filterPanelToggledArr, toggle_modal: undefined }}
              >
                {label}
              </ParamLink>
            ))}
          </div>
        );
      },
    },
    sort_direction: { isActive: () => search.state.toggle_direction },
  };

  const activeFilters = keys(filtersObj).filter((filterKey) => filtersObj[filterKey].isActive());

  const itemsCount = responseCtx[`booking_count_${response.tab}`] as number;
  const nextPage = search.state.page_nr + 1;
  const headers = columns.map((column) => column.label).join(",");
  const rows = responseCtx.bookings
    .map((booking) => {
      return columns
        .map((column) => {
          const value = column.Text(booking);
          return escapeCSV(value ? value + "" : "-");
        })
        .join(",");
    })
    .join("\n");
  const exportFileName = `bookings-${app.date.todayParam.replace(/-/g, "")}-${responseCtx.bookings.length}-page-${nextPage}.csv`;
  const csvContent = headers + "\n" + rows;
  const isFilterPanelToggled = search.state.toggle_panels.includes(filterPanelName);
  const filterPanelToggledArr = toggleArray(search.state.toggle_panels, filterPanelName);
  return (
    <AnimatingDiv className="app-container space-y-6">
      <h1 className="flex flex-wrap items-center justify-between gap-3">
        <span className="text-slate-800 text-xl font-semibold whitespace-nowrap">{response.operator?.name}</span>
        <span className="text-slate-600 whitespace-nowrap">
          {operatorEstablishments.length > 1 ? "ENTERPRISE" : firstEstablishment?.spot_name}
        </span>
        <span className="flex-1" />
        <span className="text-slate-600 whitespace-nowrap">{todayFormatted}</span>
      </h1>
      <div className="flex flex-row gap-3 items-center">
        <h2 className="text-xl font-bold">Bookings</h2>
        <NavigatingSelect className="border-none" defaultValue={response.page_type} onChange={(value) => ({ page_type: value })}>
          {entries(pageTypes).map(([key, value]) => {
            return (
              <option key={key} value={key}>
                By {value}
              </option>
            );
          })}
        </NavigatingSelect>
      </div>
      <DismissableErrorAlert />
      <div className={"space-y-3"}>
        <div className="md:flex flex-wrap gap-3 grid grid-cols-12">
          {rangeFilterButtons.map((button, index) => (
            <ParamLink
              key={button.from + button.to}
              path={"./"}
              className={twMerge(
                "btn btn-basic aria-selected:bg-secondary-500 col-span-4 aria-busy:spinner spinner-dark flex-1 text-center",
                rangeFilterButtons.length === 6 && index < 4 && "col-span-3",
              )}
              aria-selected={button === activeRangeFilterButton}
              paramState={{
                date_from: button.from,
                date_to: button.to,
                rerender: search.state.rerender + 1,
                toggle_direction: button.toggle_direction || false,
                error_message: null,
                page_nr: 0,
              }}
            >
              {button.label}
            </ParamLink>
          ))}
          <ParamLink
            aria-selected={isFilterPanelToggled}
            className={twMerge(
              "col-span-4 btn md:hidden group bg-slate-100",
              rangeFilterButtons.length === 6 && "col-span-4",
              activeFilters.length && "bg-secondary text-white",
            )}
            paramState={{ toggle_panels: filterPanelToggledArr }}
          >
            {isFilterPanelToggled ? <span>Hide</span> : <span>More</span>}
            {!!activeFilters.length && (
              <span className="rounded-md px-3 py-1 text-xs bg-white/80 text-secondary">{activeFilters.length}</span>
            )}
            <ChevronDown className={"w-4 h-4 group-aria-selected:rotate-180 transition-transform"} />
          </ParamLink>
        </div>
        <div className={twMerge(!isFilterPanelToggled && "max-md:hidden")}>
          <ActionForm preventScrollReset method={"get"} autoAnimate={false}>
            <input type={"hidden"} name={"element_action" satisfies StateInputKey} value={""} />
            <input type={"hidden"} name={"element_clear" satisfies StateInputKey} value={""} />
            <input type={"hidden"} name={"rerender" satisfies StateInputKey} value={search.state.rerender + 1} />
            <input type={"hidden"} value={""} name={"page_nr" satisfies StateInputKey} />
            <input type={"hidden"} value={""} name={"error_message" satisfies StateInputKey} />
            <input type="hidden" name={"toggle_panels" satisfies StateInputKey} value={""} />
            <InputSearchParamCopies
              excludeKeys={[
                "search",
                "error_message",
                "date_from",
                "date_to",
                "element_action",
                "element_clear",
                "rerender",
                "toggle_panels",
              ]}
            />
            <div className="space-y-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <Fragment key={search.state.rerender}>
                  <input
                    name={"date_from" satisfies StateInputKey}
                    id={"date_from" satisfies StateInputKey}
                    type={"date"}
                    placeholder="Date from"
                    className="input w-full flex-1"
                    defaultValue={search.state.date_from ?? todayIso}
                    onChange={(e) => {
                      const dateFrom = e.target.value;
                      const dateToEl = e.target.form?.querySelector<HTMLInputElement>(`input[name="${"date_to" satisfies StateInputKey}"]`);
                      const dateTo = dateToEl?.value;

                      if (dateToEl) {
                        dateToEl.setCustomValidity("");
                      }
                      if (dateFrom && dateTo && new Date(dateFrom) > new Date(dateTo)) {
                        e.target.setCustomValidity("Start date cannot be after end date");
                      } else {
                        e.target.setCustomValidity("");
                      }
                    }}
                  />
                  <input
                    name={"date_to" satisfies StateInputKey}
                    type={"date"}
                    placeholder="Date to"
                    className="input w-full flex-1"
                    defaultValue={search.state.date_to ?? todayIso}
                    onChange={(e) => {
                      const dateTo = e.target.value;
                      const dateFromEl = e.target.form?.querySelector<HTMLInputElement>(
                        `input[name="${"date_from" satisfies StateInputKey}"]`,
                      );
                      const dateFrom = dateFromEl?.value;

                      if (dateFromEl) {
                        dateFromEl.setCustomValidity("");
                      }
                      if (dateFrom && dateTo && new Date(dateFrom) > new Date(dateTo)) {
                        e.target.setCustomValidity("End date cannot be before start date");
                      } else {
                        e.target.setCustomValidity("");
                      }
                    }}
                  />
                </Fragment>
                <fieldset className="col-span-2 rounded-md border border-slate-300 focus-within:ring-1 ring-slate-400 flex flex-row gap-2 items-center pr-0.5">
                  <input
                    name={"search" satisfies StateInputKey}
                    id={searchInputId}
                    className="input-clean flex-1 text-sm"
                    placeholder={"Search for Reference"}
                    defaultValue={search.state.search || ""}
                  />
                  {search.state.search && (
                    <ParamLink
                      path={"./"}
                      paramState={{
                        element_action: [searchInputId],
                        element_clear: [searchInputId],
                        rerender: search.state.rerender + 1,
                        page_nr: undefined,
                        search: undefined,
                      }}
                      className="text-slate-600 p-2 hover:text-black"
                    >
                      Clear
                    </ParamLink>
                  )}

                  <div className="py-0.5 h-full ">
                    <button
                      className="btn h-full btn-primary disabled:spinner spinner-light group flex flex-row gap-2 items-center"
                      disabled={!searchIsIdle}
                    >
                      <span className="max-md:hidden text-sm">Search</span>
                      <MagnifyingGlassIcon className="w-5 h-5 group-disabled:opacity-0" />
                    </button>
                  </div>
                </fieldset>
              </div>

              <div className={"flex flex-wrap gap-x-3 gap-y-3"}>
                <ParamLink
                  paramState={{
                    toggle_panels: filterPanelToggledArr,
                    toggle_direction: !search.state.toggle_direction,
                  }}
                  path={"./"}
                  className="flex gap-1 items-center whitespace-nowrap py-1 text-sm pl-3 pr-3 rounded-full border border-secondary aria-selected:bg-secondary aria-selected:text-white"
                  aria-selected={search.state.toggle_direction}
                >
                  <SortAscIcon className={twMerge("h-4 w-4 transition-transform", search.state.toggle_direction && "rotate-180")} />
                  {search.state.toggle_direction ? <span>Descending</span> : <span>Ascending</span>}
                </ParamLink>
                <ParamLink
                  className="flex gap-2 items-center whitespace-nowrap py-1 text-sm pl-3 pr-1 rounded-full border border-secondary aria-selected:bg-secondary aria-selected:text-white"
                  paramState={{ modal_detail_name: "payment_status" satisfies Filter, toggle_modal: "filter" }}
                  replace
                  aria-selected={!!search.state.booking_payment_states.length}
                >
                  <span>Payment Status:</span>
                  {search.state.booking_payment_states.map((state) => (
                    <BookingPaymentBadge key={state} status={state} className="rounded-full flex flex-row w-fit pl-2 pr-0">
                      <span>{paymentsStates[state]}</span>
                      <ParamLink
                        replace
                        className="px-1"
                        path={"./"}
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                        paramState={{
                          booking_payment_states: toggleArray(search.state.booking_payment_states, state),
                          toggle_panels: filterPanelToggledArr,
                        }}
                      >
                        <XMarkIcon className="w-4 h-4" />
                      </ParamLink>
                    </BookingPaymentBadge>
                  ))}
                  {search.state.booking_payment_states.length === 0 && <BookingPaymentBadge className="rounded-full" status={null} />}
                </ParamLink>
                <ParamLink
                  className="flex gap-1 items-center whitespace-nowrap py-1 text-sm px-3 rounded-full border border-secondary aria-selected:bg-secondary aria-selected:text-white"
                  paramState={{
                    modal_detail_name: "booking_type" satisfies Filter,
                    toggle_modal: "filter",
                  }}
                  aria-selected={search.state.direct_booking !== "mixed"}
                >
                  <FilterIcon className="w-3 h-3" /> Booking Type: {directBookingStatesObj[search.state.direct_booking]}
                  {search.state.direct_booking !== "mixed" && (
                    <ParamLink
                      path="./"
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                      paramState={{ direct_booking: undefined }}
                    >
                      <XMarkIcon className="w-4 h-4" />
                    </ParamLink>
                  )}
                </ParamLink>
                <ParamLink
                  className="flex gap-1 pl-2 pr-2 aria-selected:pr-0 items-center overflow-hidden whitespace-nowrap py-1 text-sm rounded-full border border-secondary aria-selected:bg-secondary aria-selected:text-white"
                  paramState={{
                    modal_detail_name: "created_by" satisfies Filter,
                    toggle_modal: "filter",
                  }}
                  aria-selected={!!search.state.created_by}
                >
                  <span>Created by:</span>
                  {createdByFilter ? (
                    <span
                      className="overflow-hidden text-ellipsis whitespace-nowrap"
                      title={`${createdByFilter.name} (${createdByFilter.email})`}
                    >
                      {createdByFilter.name} ({createdByFilter.email})
                    </span>
                  ) : (
                    <span>All</span>
                  )}
                  {createdByFilter && (
                    <ParamLink
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                      className="pl-1 pr-2 flex-shrink-0"
                      path={"./"}
                      paramState={{ created_by: undefined }}
                    >
                      <XMarkIcon className="w-4 h-4" />
                    </ParamLink>
                  )}
                </ParamLink>
              </div>
            </div>
          </ActionForm>
        </div>
      </div>
      <div className="flex flex-row gap-3 empty:hidden">
        {operatorEstablishments.length > 1 &&
          operatorEstablishments.map((establishment) => {
            const isToggled = search.state.persist_toggle_establishment_ids.includes(establishment.id);
            // const isLoading = navigation.location?.search.includes(`toggle_establishment_ids=${establishment.id}`)
            return (
              <ParamLink
                key={establishment.id}
                aria-selected={!isToggled}
                path={"./"}
                paramState={{
                  page_nr: 0,
                  persist_toggle_establishment_ids: toggleArray(search.state.persist_toggle_establishment_ids, establishment.id),
                }}
                className="p-3 rounded-md bg-secondary-50 flex flex-row gap-2 items-center aria-busy:spinner spinner-light"
              >
                <p>{establishment.spot_name || "no spot"}</p>
                <input type={"checkbox"} checked={!isToggled} className="checkbox" />
              </ParamLink>
            );
          })}
      </div>

      <CDialog dialogname={"filter"} className="space-y-3">
        {filtersObj[(search.state.modal_detail_name || "") as Filter]?.modal?.()}
      </CDialog>
      <div className="max-md:flex-col-reverse flex md:flex md:flex-wrap border-b border-slate-200">
        <div className="flex flex-wrap">
          {tabs.map((tab) => (
            <ParamLink
              key={tab || ""}
              path={"./"}
              paramState={{ tab: tab, page_nr: 0 }}
              className={
                "aria-current:border-primary h-10 aria-current:font-semibold md:min-w-40 text-slate-500 aria-current:text-slate-800 hover:text-slate-700 hover:border-slate-200 border-b-transparent transition-colors border-b-4 py-2 px-6 max-md:w-full max-md:flex-1 whitespace-nowrap text-center capitalize"
              }
              paramsActive={tab === response.tab}
            >
              {tab} ({responseCtx[`booking_count_${tab}`] + ""})
            </ParamLink>
          ))}
        </div>
        <div className="flex-1" />
        <div className="flex gap-3">
          {operatorEstablishments.map((establishment) => (
            <ParamLink
              key={establishment.id}
              path={_activity_mutate}
              paramState={{ establishment_id: establishment.id }}
              className="link font-semibold flex flex-row gap-1 items-center"
            >
              <PlusCircleIcon className="w-5 h-5" />
              <span>Create booking</span>
            </ParamLink>
          ))}
          <div className="flex-1" />
          <button
            className="btn gap-1 text-primary hover:text-primary-700 whitespace-nowrap"
            type={"button"}
            onClick={() => {
              try {
                const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8" });
                const a = document.createElement("a");
                const downloadUrl = URL.createObjectURL(blob);
                a.href = downloadUrl;
                a.download = exportFileName;
                a.style.display = "none";
                document.body.appendChild(a);

                a.click();

                document.body.removeChild(a);

                URL.revokeObjectURL(downloadUrl);
              } catch (e) {
                console.error("export error", e);
                window.alert("export error");
              }
            }}
          >
            <ExportIcon className="w-6 h-6" />
            Export
          </button>
        </div>
      </div>
      {search.state.toggle_invoice ? (
        <div>
          <table>
            <thead>
              <tr>
                {columns.map((column) => (
                  <td className="px-2 text-slate-500" key={column.label}>
                    {column.label}
                  </td>
                ))}
              </tr>
            </thead>
            {responseCtx.bookings.map((booking) => (
              <tr key={booking.id}>
                {columns.map((column) => (
                  <td key={column.label} className="px-2">
                    {column.Comp ? column.Comp(booking) : column.Text(booking)}
                  </td>
                ))}
              </tr>
            ))}
          </table>
        </div>
      ) : (
        <div className="grid grid-cols-[auto_1fr_auto] lg:grid-cols-[repeat(2,auto_1fr_auto)] gap-4">
          {!responseCtx.bookings.length && <div>No Results</div>}
          {responseCtx.bookings.map((booking) => (
            <ParamLink
              path={_booking_detail(booking.id)}
              paramState={{ persist_previous_path: _booking }}
              key={booking.id}
              className={twMerge(
                "grid overflow-hidden rounded-md border border-secondary-stroke grid-cols-subgrid col-span-3 hover:opacity-80 transition-all hover:border-secondary-600",
              )}
            >
              <BookingItem booking={booking} />
            </ParamLink>
          ))}
        </div>
      )}
      <Paging totalCount={itemsCount} pageLimit={response.page_limit} />
    </AnimatingDiv>
  );
}
