import { Outlet } from "@remix-run/react";
import { BoxLogoCase } from "~/components/BoxLogo";
import type { ReactNode } from "react";
import React from "react";
import { useBoolean } from "~/hooks/use-boolean";
import { DefaultErrorBoundary } from "~/components/RoutDefaults";

const Layout = (props: { children: ReactNode }) => {
  const triedClose = useBoolean();

  return (
    <div className="container mx-auto max-w-md px-3 py-6">
      <div className="space-y-3 rounded-xl border border-secondary-500 bg-secondary-50 p-6 shadow-md">
        <div>
          <BoxLogoCase />
        </div>
        <div className="flex flex-col space-y-3">
          {props.children}
          {triedClose.isOn ? (
            <p className="text-xl text-slate-800">The window could not be closed automatically, try closing it manually please.</p>
          ) : (
            <button
              className="btn w-full"
              onClick={() => {
                try {
                  window.close();
                } catch (e) {
                  console.error("could not close", e);
                }
                triedClose.on();
              }}
            >
              Close
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export function ErrorBoundary() {
  return (
    <Layout>
      <DefaultErrorBoundary />
    </Layout>
  );
}

export default function Page() {
  return (
    <Layout>
      <Outlet />
    </Layout>
  );
}
