import type { MetaFunction } from "@remix-run/react";
import { Link } from "@remix-run/react";
import React, { useState, useEffect } from "react";

export const meta: MetaFunction = () => {
  return [
    { title: "Read Operations - API Documentation" },
    { name: "description", content: "Documentation for data retrieval operations in ScamGem's API system" },
  ];
};

const sections = [
  { id: "overview", title: "Overview", icon: "📖" },
  { id: "authentication", title: "Authentication", icon: "🔐" },
  { id: "api-routes", title: "API Routes", icon: "🌐" },
];

const apiRoutes = [
  { id: "currency", title: "Currency Exchange", path: "/api/currency/{currency_code}" },
  { id: "planning-index", title: "Planning Index", path: "/api/planning" },
  { id: "planning-month", title: "Planning Month", path: "/api/planning/month" },
  { id: "planning-user", title: "Planning User", path: "/api/planning/u" },
  { id: "root-context", title: "Root Context", path: "/api/root" },
];

export default function ReadOperationsDocumentation() {
  const [activeSection, setActiveSection] = useState("overview");

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
      setActiveSection(sectionId);
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      const sections = document.querySelectorAll("section[id]");
      let current = "";
      sections.forEach((section) => {
        const sectionTop = (section as HTMLElement).offsetTop;
        const sectionHeight = (section as HTMLElement).clientHeight;
        if (window.scrollY >= sectionTop - 200) {
          current = section.getAttribute("id") || "";
        }
      });
      setActiveSection(current);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link to="/api-documentation" className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
            Back to API Documentation
          </Link>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Read Operations</h1>
          <p className="text-gray-600">Data retrieval and querying documentation</p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Navigation */}
          <div className="lg:w-64 lg:sticky lg:top-8 lg:self-start lg:h-fit lg:max-h-screen lg:overflow-y-auto">
            <h2 className="text-sm font-semibold text-gray-900 mb-3">Navigation</h2>
            <nav className="space-y-1">
              {sections.map((section) => (
                <div key={section.id}>
                  <button
                    onClick={() => scrollToSection(section.id)}
                    className={`w-full text-left px-2 py-1.5 rounded font-medium transition-colors ${
                      activeSection === section.id ? "bg-green-100 text-green-700" : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                    }`}
                  >
                    <span className="mr-1">{section.icon}</span>
                    {section.title}
                  </button>
                  {section.id === "api-routes" && (
                    <div className="ml-4 mt-1 space-y-1">
                      {apiRoutes.map((route) => (
                        <button
                          key={route.id}
                          onClick={() => scrollToSection(route.id)}
                          className={`w-full text-left px-2 py-1 text-sm transition-colors ${
                            activeSection === route.id ? "text-green-600 font-medium" : "text-gray-500 hover:text-gray-700"
                          }`}
                        >
                          {route.path}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 bg-white rounded-lg shadow-lg p-8">
            {/* Overview Section */}
            <section id="overview" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Overview</h2>
              <div className="prose max-w-none">
                <p className="text-gray-600 mb-4">
                  Read operations in ScamGem's API system are designed for data retrieval and querying. The system provides dedicated JSON
                  endpoints for external consumption and programmatic access.
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <h3 className="font-semibold text-blue-900 mb-2">API Routes</h3>
                  <p className="text-blue-800 text-sm">
                    Dedicated JSON endpoints prefixed with <code className="bg-blue-100 px-1 rounded">api.</code> for external consumption
                    and programmatic access. These routes provide clean, RESTful interfaces for data retrieval.
                  </p>
                </div>
              </div>
            </section>

            {/* Authentication Section */}
            <section id="authentication" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Authentication & Authorization</h2>
              <div className="prose max-w-none">
                <p className="text-gray-600 mb-4">
                  Read operations use session-based authorization.
                  {/* getSessionSimple() */}
                </p>

                <h3 className="text-xl font-semibold text-gray-900 mb-3">Access Levels</h3>
                <ul className="list-disc list-inside text-gray-600 mb-4">
                  <li>
                    <strong>Public</strong>: No authentication required (e.g., explore pages)
                  </li>
                  <li>
                    <strong>Customer</strong>: Basic user authentication
                  </li>
                  <li>
                    <strong>Member</strong>: Establishment member access
                  </li>
                  <li>
                    <strong>Admin</strong>: Administrative access
                  </li>
                  <li>
                    <strong>Editor</strong>: System-wide editor access
                  </li>
                </ul>

                <h3 className="text-xl font-semibold text-gray-900 mb-3">Session Management</h3>
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                  <div>// Check if user can perform operation on table</div>
                  <div>const canInsert = await canInsertQb(args, "participant");</div>
                  <div>const canUpdate = await canUpdateQb(args, "booking", bookingId);</div>
                  <div>const canDelete = await canDeleteQb(args, "establishment", establishmentId);</div>
                  {/* getSessionSimple() */}
                </div>
              </div>
            </section>

            {/* API Routes Section */}
            <section id="api-routes" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">API Routes</h2>
              <div className="prose max-w-none">
                <p className="text-gray-600 mb-4">
                  API routes are dedicated endpoints that return JSON data for external consumption. These routes are prefixed with{" "}
                  <code className="bg-gray-100 px-1 rounded">api.</code> and provide clean, RESTful interfaces.
                </p>

                {/* Currency Exchange Rates */}
                <section id="currency" className="mb-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Currency Exchange Rates</h3>
                  <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                    <div>GET /api/currency/{"{currency_code}"}</div>
                  </div>
                  <p className="text-gray-600 mb-4">
                    <strong>Path Parameters:</strong>
                  </p>
                  <ul className="list-disc list-inside text-gray-600 mb-4">
                    <li>
                      <code>currency_code</code> (required): ISO 4217 currency code (e.g., USD, EUR, GBP)
                    </li>
                  </ul>
                  <p className="text-gray-600 mb-4">
                    <strong>Response:</strong> Array of currency conversion rates
                  </p>
                  <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                    <div>{"["}</div>
                    <div> {"{"}</div>
                    <div> "currencyCode": "EUR",</div>
                    <div> "conversionRate": 0.85</div>
                    <div> {"}"},</div>
                    <div> {"{"}</div>
                    <div> "currencyCode": "GBP",</div>
                    <div> "conversionRate": 0.73</div>
                    <div> {"}"}</div>
                    <div>{"]"}</div>
                  </div>
                  <p className="text-gray-600 mb-4">
                    <strong>Features:</strong> Cached for 10 minutes using external exchange rate API
                  </p>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">Example Request</h4>
                  <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                    <div>GET /api/currency/USD</div>
                  </div>
                </section>

                {/* Planning Index */}
                <section id="planning-index" className="mb-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Planning Index</h3>
                  <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                    <div>GET /api/planning</div>
                  </div>
                  <p className="text-gray-600 mb-4">
                    <strong>Query Parameters:</strong>
                  </p>
                  <ul className="list-disc list-inside text-gray-600 mb-4">
                    <li>
                      <code>persist_operator_id</code> (optional): Operator ID for filtering
                    </li>
                    <li>
                      <code>persist_establishment_id</code> (optional): Establishment ID for filtering
                    </li>
                    <li>
                      <code>persist_date</code> (optional): Date in YYYY-MM-DD format (defaults to today)
                    </li>
                    <li>
                      <code>persist_timezone</code> (optional): Timezone for date calculations
                    </li>
                  </ul>
                  <p className="text-gray-600 mb-4">
                    <strong>Response:</strong> Planning data for the specified date
                  </p>
                  <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                    <div>{"{"}</div>
                    <div> "date": "2024-01-15",</div>
                    <div> "timezone": "UTC",</div>
                    <div> "bookings": [</div>
                    <div> {"{"}</div>
                    <div> "id": "booking-123",</div>
                    <div> "activity": "Diving Trip",</div>
                    <div> "startTime": "09:00",</div>
                    <div> "endTime": "17:00",</div>
                    <div> "participants": 4,</div>
                    <div> "status": "confirmed"</div>
                    <div> {"}"}</div>
                    <div> ],</div>
                    <div> "totalBookings": 1,</div>
                    <div> "totalParticipants": 4</div>
                    <div>{"}"}</div>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">Example Request</h4>
                  <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                    <div>GET /api/planning?persist_date=2024-01-15&persist_operator_id=123&persist_timezone=UTC</div>
                  </div>
                </section>

                {/* Planning Month */}
                <section id="planning-month" className="mb-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Planning Month</h3>
                  <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                    <div>GET /api/planning/month</div>
                  </div>
                  <p className="text-gray-600 mb-4">
                    <strong>Query Parameters:</strong>
                  </p>
                  <ul className="list-disc list-inside text-gray-600 mb-4">
                    <li>
                      <code>persist_operator_id</code> (optional): Operator ID for filtering
                    </li>
                    <li>
                      <code>persist_establishment_id</code> (optional): Establishment ID for filtering
                    </li>
                    <li>
                      <code>persist_date</code> (optional): Date in YYYY-MM-DD format (defaults to today)
                    </li>
                    <li>
                      <code>persist_month</code> (optional): Month in YYYY-MM format
                    </li>
                    <li>
                      <code>persist_timezone</code> (optional): Timezone for date calculations
                    </li>
                    <li>
                      <code>persist_toggle_establishment_ids[]</code> (optional): Array of establishment IDs to exclude
                    </li>
                  </ul>
                  <p className="text-gray-600 mb-4">
                    <strong>Response:</strong> Monthly planning overview
                  </p>
                  <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                    <div>{"{"}</div>
                    <div> "month": "2024-01",</div>
                    <div> "timezone": "UTC",</div>
                    <div> "dailyStats": [</div>
                    <div> {"{"}</div>
                    <div> "date": "2024-01-15",</div>
                    <div> "totalBookings": 5,</div>
                    <div> "totalParticipants": 18,</div>
                    <div> "revenue": 1250.00</div>
                    <div> {"}"},</div>
                    <div> {"{"}</div>
                    <div> "date": "2024-01-16",</div>
                    <div> "totalBookings": 3,</div>
                    <div> "totalParticipants": 12,</div>
                    <div> "revenue": 890.00</div>
                    <div> {"}"}</div>
                    <div> ],</div>
                    <div> "monthlyTotal": {"{"}</div>
                    <div> "bookings": 45,</div>
                    <div> "participants": 156,</div>
                    <div> "revenue": 12500.00</div>
                    <div> {"}"}</div>
                    <div>{"}"}</div>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">Example Request</h4>
                  <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                    <div>GET /api/planning/month?persist_month=2024-01&persist_operator_id=123</div>
                  </div>
                </section>

                {/* Planning User */}
                <section id="planning-user" className="mb-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Planning User</h3>
                  <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                    <div>GET /api/planning/u</div>
                  </div>
                  <p className="text-gray-600 mb-4">
                    <strong>Query Parameters:</strong>
                  </p>
                  <ul className="list-disc list-inside text-gray-600 mb-4">
                    <li>
                      <code>persist_date</code> (optional): Date in YYYY-MM-DD format (defaults to today)
                    </li>
                    <li>
                      <code>persist_timezone</code> (optional): Timezone for date calculations
                    </li>
                  </ul>
                  <p className="text-gray-600 mb-4">
                    <strong>Response:</strong> User's personal planning data
                  </p>
                  <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                    <div>{"{"}</div>
                    <div> "date": "2024-01-15",</div>
                    <div> "timezone": "UTC",</div>
                    <div> "userBookings": [</div>
                    <div> {"{"}</div>
                    <div> "id": "booking-456",</div>
                    <div> "activity": "Snorkeling Tour",</div>
                    <div> "startTime": "10:30",</div>
                    <div> "endTime": "14:30",</div>
                    <div> "location": "Coral Bay",</div>
                    <div> "status": "confirmed",</div>
                    <div> "price": 75.00</div>
                    <div> {"}"}</div>
                    <div> ],</div>
                    <div> "userStats": {"{"}</div>
                    <div> "totalBookings": 1,</div>
                    <div> "totalSpent": 75.00,</div>
                    <div> "favoriteActivity": "Snorkeling"</div>
                    <div> {"}"}</div>
                    <div>{"}"}</div>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">Example Request</h4>
                  <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                    <div>GET /api/planning/u?persist_date=2024-01-15&persist_timezone=UTC</div>
                  </div>
                </section>

                {/* Root Context */}
                <section id="root-context" className="mb-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Root Context</h3>
                  <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                    <div>GET /api/root</div>
                  </div>
                  <p className="text-gray-600 mb-4">
                    <strong>Query Parameters:</strong>
                  </p>
                  <ul className="list-disc list-inside text-gray-600 mb-4">
                    <li>
                      <code>establishment_id</code> (optional): Establishment ID for context
                    </li>
                    <li>
                      <code>persist_establishment_id</code> (optional): Alternative establishment ID parameter
                    </li>
                    <li>
                      <code>booking_id</code> (optional): Booking ID for context
                    </li>
                    <li>
                      <code>print_token</code> (optional): Token for print access
                    </li>
                    <li>
                      <code>lang</code> (optional): Language code for internationalization
                    </li>
                  </ul>
                  <p className="text-gray-600 mb-4">
                    <strong>Response:</strong> Application context and configuration
                  </p>
                  <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                    <div>{"{"}</div>
                    <div> "context": {"{"}</div>
                    <div> "establishmentId": "est-123",</div>
                    <div> "establishmentName": "Ocean Adventures",</div>
                    <div> "bookingId": "booking-789",</div>
                    <div> "language": "en"</div>
                    <div> {"}"},</div>
                    <div> "config": {"{"}</div>
                    <div> "currency": "USD",</div>
                    <div> "timezone": "UTC",</div>
                    <div> "dateFormat": "YYYY-MM-DD",</div>
                    <div> "features": ["booking", "payment", "reviews"]</div>
                    <div> {"}"},</div>
                    <div> "user": {"{"}</div>
                    <div> "id": "user-456",</div>
                    <div> "role": "customer",</div>
                    <div> "permissions": ["read", "book"]</div>
                    <div> {"}"}</div>
                    <div>{"}"}</div>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">Example Request</h4>
                  <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                    <div>GET /api/root?establishment_id=est-123&lang=en</div>
                  </div>
                </section>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}
