import type { MetaFunction } from "@remix-run/react";
import { Outlet, useLoaderData, useMatches } from "@remix-run/react";
import React, { Fragment } from "react";
import { capitalize } from "~/utils/formatters";
import { createPageOverwrites, traveltrusterName } from "~/misc/consts";
import { ImageGallery } from "~/components/field/ImageGallery";
import { createImageUrl } from "~/components/IkImage";
import { kysely } from "~/misc/database.server";
import { arrayAgg, stAsGeoJsonPoint } from "~/kysely/kysely-helpers";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { DeleteInputs, RInput } from "~/components/ResourceInputs";
import { _establishment_paths, _planning } from "~/misc/paths";
import { Trans } from "~/components/Trans";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { ParamLink } from "~/components/meta/CustomComponents";
import { useAppContext } from "~/hooks/use-app-context";
import { LoaderFunctionArgs } from "@remix-run/router";
import { defaultCurrency } from "~/misc/vars";
import { ActionForm } from "~/components/form/BaseFrom";
import { DeleteButton, SubmitButton } from "~/components/base/Button";
import { getAppContextInMeta } from "~/misc/route-helpers";
import { robotsMeta } from "~/misc/web-helpers";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { getAdminLevelIndex } from "~/domain/member/member-vars";
import { getTimezone } from "~/data/timezones";
import { OperationInput } from "~/components/form/DefaultInput";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ params }: LoaderFunctionArgs) => {
  const establishment_id = params.id!;

  const operatorLocation = await kysely
    .selectFrom("establishment")
    .innerJoin("operator", "operator.id", "establishment.operator_id")
    .leftJoin("spot", "spot.id", "establishment.spot_id")
    .leftJoin("region", "region.id", "spot.region_id")
    .selectAll("operator")
    .selectAll("establishment")
    .select((eb) => [
      "operator.name as name",
      "operator.name as operator_name",
      "establishment.address",
      "establishment.id as establishment_id",
      "establishment.location_name as establishment_name",
      "spot.name as spot_name",
      "region.timezone",
      eb
        .selectFrom("payment_method")
        .where("payment_method.establishment_id", "=", eb.ref("establishment.id"))
        .select((eb) => arrayAgg(eb.ref("payment_method.id"), "uuid").as("ids"))
        .as("payment_method_ids"),
      stAsGeoJsonPoint(eb.ref("establishment.geom")).as("geom"),
      jsonArrayFrom(fileTargetsQb(kysely, "establishment", eb.ref("establishment.id"))).as("files"),
    ])
    .where("establishment.id", "=", establishment_id)
    .executeTakeFirst();

  if (!operatorLocation) throw new Response("This operator does not exist or is not yet published on our platform", { status: 404 });

  return {
    establishment: operatorLocation,
    title: capitalize(operatorLocation.operator_name + " - " + operatorLocation.spot_name),
    ...createPageOverwrites({
      show_whatsapp: false,
      base_currency: operatorLocation.default_currency || defaultCurrency,
    }),
  };
};

export const meta: MetaFunction<typeof loader> = (args) => {
  const parentData = getAppContextInMeta(args);
  const config = parentData?.env;
  const bucketUrl = config?.firebase_singapore.storageBucket;
  const data = args.data;
  const imagePath = data?.establishment?.files?.[0]?.filename;
  return [
    robotsMeta(parentData?.operator ? "noindex" : "index"),
    { title: data?.title || traveltrusterName },
    { description: data?.establishment?.bio },
    { "og:description": data?.establishment?.bio || data?.establishment?.about },
    bucketUrl ? { "og:image": imagePath && createImageUrl(bucketUrl, imagePath, 300, 200) } : {},
  ];
};

export default function Page() {
  const context = useAppContext();
  const response = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const establishment = response.establishment;
  const matches = useMatches();
  const isShowPage = !!matches.find((match) => match.id.endsWith("$id._index"));
  const isPlanning = !!matches.find((match) => match.id.endsWith("$id.planning"));

  const isAdminOrOwner = !!context.members.find(
    (member) => member.establishment_id === establishment.id && (member.admin > 1 || member.owner),
  );

  const foundMember = context.members.find((member) => member.establishment_id === establishment.id);
  return (
    <div className="py-6">
      <div className="app-container space-y-2 pb-2">
        {isShowPage && <ImageGallery fileNames={establishment.files?.map((file) => file.filename)} />}
        <div className="flex flex-row items-center justify-between gap-2">
          <div className="flex flex-wrap items-center gap-x-3 gap-y-0">
            {!isPlanning && (
              <h1 className="text-2xl font-bold line-clamp-1">
                {establishment.name} {!!establishment.location_name && " - " + establishment.location_name}
              </h1>
            )}
            {isShowPage && !search.state.customer_view && (
              <Fragment>
                {(isAdminOrOwner || context.editor) && (
                  <Fragment>
                    <ParamLink path={_establishment_paths(establishment.id).mutate} className="link">
                      <Trans>edit</Trans>
                    </ParamLink>
                    {establishment.workflow > 1 && (
                      <ParamLink
                        path={_planning}
                        paramState={{
                          persist_establishment_id: establishment.id,
                          persist_operator_id: establishment.operator_id,
                          persist_timezone: getTimezone(establishment.timezone),
                        }}
                        className="link"
                      >
                        <Trans>planning</Trans>
                      </ParamLink>
                    )}
                  </Fragment>
                )}
                {context.editor && !search.state.customer_view && (
                  <Fragment>
                    <ActionForm>
                      <DeleteInputs table={"payment_method"} ids={establishment.payment_method_ids || []} />
                      <DeleteInputs table={"establishment"} ids={[establishment.id]} />
                      <DeleteButton />
                    </ActionForm>
                    {foundMember ? (
                      <ActionForm>
                        <OperationInput table={"member"} value={"delete"} />
                        <RInput table={"member"} field={"id"} value={foundMember.id} />
                        <SubmitButton className="text-red-500">Remove Me</SubmitButton>
                      </ActionForm>
                    ) : (
                      <ActionForm>
                        <RInput type={"hidden"} table={"member"} field={"data.establishment_id"} value={establishment.establishment_id} />
                        <RInput type={"hidden"} table={"member"} field={"data.user_id"} value={context.user_id} />
                        <RInput
                          type={"hidden"}
                          table={"member"}
                          field={"data.name"}
                          value={(context.display_name || context.email.split("@")[0]) + " (DD Support)"}
                        />
                        <RInput type={"hidden"} table={"member"} field={"data.admin"} value={getAdminLevelIndex("write")} />
                        <SubmitButton className="link">Add Me</SubmitButton>
                      </ActionForm>
                    )}
                  </Fragment>
                )}
              </Fragment>
            )}
          </div>
        </div>
      </div>
      <Outlet />
    </div>
  );
}
