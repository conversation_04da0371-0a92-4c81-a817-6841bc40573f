import type { AsyncReturnType } from "type-fest";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import React, { Fragment, useState } from "react";
import { SubmitButton } from "~/components/base/Button";
import { RInput, RSelect } from "~/components/ResourceInputs";
import { getSessionSimple } from "~/utils/session.server";
import { isEditorQb } from "~/domain/member/member-queries.server";
import { LoaderFunctionArgs } from "@remix-run/router";
import { ActionForm } from "~/components/form/BaseFrom";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { tableIdRef } from "~/misc/helpers";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);
  const operatorLocationId = params.id!;

  const estblishment = await kysely
    .selectFrom("establishment")
    .selectAll("establishment")
    .leftJoin("xendit_account", "xendit_account.id", "establishment.xendit_account_id")
    .select((eb) => [
      "xendit_account.xendit_user_id",
      "xendit_account.xendit_platform_id",
      jsonArrayFrom(eb.selectFrom("xendit_platform").select(["xendit_platform.id", "xendit_platform.name"])).as("xendit_platforms"),
      jsonArrayFrom(eb.selectFrom("xendit_account").selectAll("xendit_account")).as("xendit_accounts"),
    ])
    .where((eb) => eb.exists(isEditorQb({ trx: kysely, ctx: { session_id: session_id } })))
    .where("establishment.id", "=", operatorLocationId)
    .executeTakeFirstOrThrow();

  // const intuitOAuthClient = new OAuthCl

  return {
    establishment: estblishment,
  };
};

type LoaderResponse = AsyncReturnType<typeof loader>;

export default function Page() {
  const response = useLoaderData<LoaderResponse>();
  const [enterManually, setEnterManually] = useState(false);

  const xenditUserId = response.establishment.xendit_user_id;
  const xenditInternalAccountId = response.establishment.xendit_account_id;
  return (
    <div className="app-container space-y-6">
      <h2 className="text-xl font-bold">Integrations</h2>
      <ActionForm className="space-y-3">
        <div className="space-y-3">
          <RInput table={"establishment"} field={"id"} value={response.establishment.id} />
          <h3 className="text-xl">Xendit</h3>
          <button className="btn btn-primary" type={"button"} onClick={() => setEnterManually(!enterManually)}>
            toggle manual input
          </button>
          {enterManually ? (
            <div className={"flex flex-row gap-3 items-center"}>
              <RInput table={"establishment"} field={"data.xendit_account_id"} value={tableIdRef("xendit_account")} type={"hidden"} />
              <RSelect table={"xendit_account"} field={"data.xendit_platform_id"} className="select">
                {response.establishment.xendit_platforms.map((xenditPlatform) => (
                  <option key={xenditPlatform.id} value={xenditPlatform.id}>
                    {xenditPlatform.name}
                  </option>
                ))}
              </RSelect>
              <RInput
                table={"xendit_account"}
                field={"data.xendit_user_id"}
                defaultValue={xenditUserId || ""}
                placeholder={"xendit user/account id"}
                className="input"
              />
              <div className="inline-flex gap-1 items-center">
                <RInput
                  className="checkbox"
                  table={"xendit_account"}
                  field={"data.production"}
                  hiddenType={"__boolean__"}
                  label={"production"}
                  type={"checkbox"}
                />
              </div>
            </div>
          ) : (
            <Fragment>
              <RSelect
                table={"establishment"}
                field={"data.xendit_account_id"}
                defaultValue={xenditInternalAccountId || ""}
                className="select"
              >
                <option value={""}>no xendit</option>
                {xenditInternalAccountId &&
                  !response.establishment.xendit_accounts.find((account) => account.id === xenditInternalAccountId) && (
                    <option value={xenditInternalAccountId}>{xenditUserId}</option>
                  )}
                {response.establishment.xendit_accounts.map((account) => {
                  const xenditEnv = response.establishment.xendit_platforms.find((env) => env.id === account.xendit_platform_id);
                  return (
                    <option key={account.id || ""} value={account.id || ""}>
                      {xenditEnv?.name}
                      {!account?.production && " (Xendit Test Env)"} - {account.xendit_account_response?.public_profile?.business_name} (
                      {account.xendit_user_id})
                    </option>
                  );
                })}
              </RSelect>
            </Fragment>
          )}
        </div>
        {/*<div className="space-y-3">*/}
        {/*  <h3 className="text-xl">Quickbooks</h3>*/}
        {/*  <RSelect*/}
        {/*    table={"establishment"}*/}
        {/*    field={"data.intuit_connection_id"}*/}
        {/*    defaultValue={response.establishment.intuit_connection_id || ""}*/}
        {/*    className="select"*/}
        {/*  >*/}
        {/*    <option value={""}>no quickbooks</option>*/}
        {/*    {response.establishment.available_intuit_connections.map((account) => (*/}
        {/*      <option value={account.id}>*/}
        {/*        {account.intuit_company.CompanyName} ({account.intuit_user.email})*/}
        {/*      </option>*/}
        {/*    ))}*/}
        {/*  </RSelect>*/}
        {/*</div>*/}

        <SubmitButton className="btn btn-primary">Save</SubmitButton>
      </ActionForm>
    </div>
  );
}
