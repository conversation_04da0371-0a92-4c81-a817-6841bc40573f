import type { DataFunctionArgs } from "@remix-run/server-runtime";
import { getSessionSimple } from "~/utils/session.server";
import { kysely } from "~/misc/database.server";
import { useLoaderData, useNavigation } from "@remix-run/react";
import { AnimatingDiv, AnimatingTableBody } from "~/components/base/base";
import React, { Fragment } from "react";
import { DeleteButton, SubmitButton } from "~/components/base/Button";
import { _member_detail } from "~/misc/paths";
import { EditorRequired } from "~/components/account/AccountContainer";
import { entries, fName, tableIdRef } from "~/misc/helpers";
import { ParamLink } from "~/components/meta/CustomComponents";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { RInput, RLabel, RSelect, toInputId } from "~/components/ResourceInputs";
import { HiddenTypeInput, OperationInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { twMerge } from "tailwind-merge";
import { ActionForm } from "~/components/form/BaseFrom";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { establishmentQb } from "~/domain/establishment/queries";
import { notFound } from "~/misc/responses";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { checks } from "~/domain/member/member-helpers";
import { HiddenInfinateAvailableScheduleInputs } from "~/domain/schedule/HiddenRangeInput";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { getAdminLevelIndex } from "~/domain/member/member-vars";
import { SidePanel } from "~/components/SidePanel";
import { permissions } from "~/domain/permission/permission";
import { PlusIcon } from "@heroicons/react/20/solid";
import { ActionAlert } from "~/components/ActionAlert";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: DataFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);

  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  if (!state.persist_establishment_id) throw notFound();

  const verifiedCtx = await activeUserSessionSimple(kysely, session_id, true)
    .where((eb) => {
      const allowedMemberUserIds = eb
        .selectFrom("member")
        .where("member.establishment_id", "=", state.persist_establishment_id)
        .where("member.deleted_at", "=", at_infinity_value)
        .where((eb) => eb.or([eb("member.owner", "=", true), eb("member.admin", ">=", getAdminLevelIndex("read"))]))
        .select("member.user_id");

      return eb.or([eb("_user.editor", "=", true), eb("_user.id", "in", allowedMemberUserIds)]);
    })
    .select((eb) => [
      jsonObjectFrom(establishmentQb.where("establishment.id", "=", state.persist_establishment_id)).as("establishment"),
      jsonArrayFrom(
        eb
          .selectFrom("member")
          .leftJoin("user", (eb) => eb.onRef("member.user_id", "=", "user.id").on("user.deleted_at", "=", at_infinity_value))
          .where("member.establishment_id", "=", state.persist_establishment_id)
          .where("member.deleted_at", "=", at_infinity_value)
          .selectAll("member")
          .select((eb) => [
            "user.email",
            eb
              .selectFrom("trip_assignment")
              .select((eb) => eb.fn.count<number>("trip_assignment.id").as("count"))
              .where("trip_assignment.member_id", "=", eb.ref("member.id"))
              .as("trip_count"),
          ])
          .orderBy("user.email")
          .orderBy("member.name"),
      ).as("members"),
    ])
    .executeTakeFirst();

  const establishment = verifiedCtx?.establishment;
  if (!establishment) return null;

  return {
    operatorLocationId: state.persist_establishment_id,
    members: verifiedCtx.members,
    establishment: establishment,
  };
};

export default function Page() {
  const searh = useSearchParams2();
  const response = useLoaderData<typeof loader>();
  const navigation = useNavigation();

  if (!response) return <EditorRequired />;

  const { members } = response;

  const existingEmails = members.map((olUser) => olUser.email);

  const selectedMember = members.find((member) => member.id === searh.state.member_id);

  return (
    <div className="app-container">
      <h1 className="text-xl font-semibold text-slate-800">Members</h1>
      <h2 className="font-semibold text-slate-700 mb-3">{getEstablishmentName(response.establishment)}</h2>
      <div>
        <div className="overflow-auto">
          <table className="w-full [&_td]:p-2 [&_td]:py-3">
            <thead className="font-semibold">
              <tr className="border-b border-slate-b-200">
                <td className="min-w-[200px]">Email</td>
                <td className="min-w-[150px] whitespace-nowrap">Name</td>
                {Object.values(checks).map((check) => (
                  <td key={check.label}>{check.label}</td>
                ))}
                <td>Trips</td>
                <td></td>
              </tr>
            </thead>
            <AnimatingTableBody>
              {members.map((member, i) => {
                return (
                  <tr key={member.id} className="border-b border-b-slate-200">
                    <Fragment>
                      <td>
                        {!!member.email && (
                          <ParamLink
                            path={_member_detail(member.id)}
                            className={twMerge(
                              "link",
                              navigation.location?.pathname.includes(member.id) && "loading-dots animate-pulse opacity-80",
                            )}
                          >
                            {member.email}
                          </ParamLink>
                        )}
                      </td>
                      <td>
                        <ParamLink
                          path={_member_detail(member.id)}
                          className={twMerge(
                            "link",
                            navigation.location?.pathname.includes(member.id) && "loading-dots animate-pulse opacity-80",
                          )}
                        >
                          {member.name}
                        </ParamLink>
                      </td>
                    </Fragment>
                    {entries(checks).map(([key, check]) => {
                      const options = check.options;
                      const value = member[key];
                      return <td key={key}>{!!value && (typeof value === "number" ? options?.[value] : check.label)}</td>;
                    })}
                    <td align={"center"}>{member.trip_count}</td>

                    <td className="text-right">
                      <ParamLink className="link" paramState={{ toggle_modal: "side", member_id: member.id }}>
                        edit
                      </ParamLink>
                      &nbsp;
                      <ActionForm className="inline" confirmMessage={`This will delete member ${member.name}, are you sure?`}>
                        <RInput table={"member"} field={"id"} value={member.id} />
                        <OperationInput table={"member"} value={"delete"} />
                        <DeleteButton />
                      </ActionForm>
                    </td>
                  </tr>
                );
              })}
            </AnimatingTableBody>
          </table>
        </div>
        <div className="flex justify-end px-3 py-3">
          <ParamLink paramState={{ toggle_modal: "side", member_id: null }} className="inline-flex items-center gap-1 link">
            <PlusIcon className="w-4 h-4" />
            Add
          </ParamLink>
        </div>
        {/*<div className="flex justify-end p-3">*/}
        {/*  <SubmitButton*/}
        {/*    className="btn btn-primary"*/}
        {/*    disabled={searh.state.edit.length + searh.state.remove.length + (searh.state.create ? 1 : 0) === 0}*/}
        {/*  >*/}
        {/*    Save*/}
        {/*  </SubmitButton>*/}
        {/*</div>*/}
      </div>
      <SidePanel>
        <div className="max-md:w-full md:w-96 bg-white h-full overflow-auto">
          <ActionForm className="px-5 py-7 space-y-10">
            <div className="space-y-3">
              <AnimatingDiv className="space-y-3">
                <h2 className="text-2xl text-slate-500">{selectedMember ? "Edit " + selectedMember.name : "Create Member"}</h2>
                <ActionAlert scrollTo />
              </AnimatingDiv>
              <div>
                {selectedMember && <RInput table={"member"} field={"id"} value={selectedMember.id} />}
                <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined }} />
                {!selectedMember && (
                  <RInput type={"hidden"} table={"member"} field={"data.establishment_id"} value={response.operatorLocationId} />
                )}
                <RInput table={"member"} field={"data.user_id"} type={"hidden"} value={tableIdRef("user")} />
                <RInput
                  label={"Email (optional)"}
                  table={"user"}
                  field={"data.email"}
                  defaultValue={selectedMember?.email || ""}
                  datalist={members.map((user) =>
                    user.email ? (
                      <option key={user.id} value={user.email} disabled={existingEmails.includes(user.email)}>
                        {user.email}
                      </option>
                    ) : (
                      <Fragment key={user.id} />
                    ),
                  )}
                  placeholder="Fill email"
                  type="email"
                  className="input"
                />
              </div>
              <div>
                <RInput
                  className="input"
                  label={"Name"}
                  table={"member"}
                  field={"data.name"}
                  required
                  placeholder={"Fill name"}
                  defaultValue={selectedMember?.name}
                />
              </div>
              {!selectedMember && <HiddenInfinateAvailableScheduleInputs target_id={tableIdRef("member")} index={0} />}
            </div>
            <div className="space-y-3">
              <h3 className="text-xl text-slate-400">Permissions</h3>
              <hr />
              <div className="space-y-3">
                {/*<RLabel table={"member"} field={`data.admin`} className="flex-1">*/}
                {/*  Role*/}
                {/*</RLabel>*/}
                <RSelect
                  table={"member"}
                  field={`data.admin`}
                  defaultValue={selectedMember?.admin + ""}
                  className="select flex-1 p-1 pr-3 w-full"
                >
                  {checks.admin?.options?.map((label, index) => (
                    <option key={index} value={index + ""}>
                      {label}
                    </option>
                  ))}
                </RSelect>
                <hr />
                <div className="flex flex-wrap gap-3">
                  <HiddenTypeInput name={fName("member", "data.permissions")} value={"__empty_array__"} />
                  {entries(permissions).map(([key, permission], index) => {
                    if (permission.workflow && !response.establishment.workflow) return <Fragment key={key} />;
                    const name = fName("member", "data.permissions", 0, index);
                    const id = toInputId(name);
                    return (
                      <div className="inline-flex gap-1 items-center bg-slate-100 py-1.5 rounded-md px-3" key={key}>
                        <label htmlFor={id} className="capitalize">
                          {key}
                        </label>
                        <input
                          id={id}
                          name={name}
                          value={key}
                          type="checkbox"
                          className="checkbox"
                          defaultChecked={!!selectedMember?.permissions?.includes(key)}
                        />
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
            <div className="space-y-3">
              <h3 className="text-xl text-slate-400">Function</h3>
              <hr />
              <div className="flex flex-wrap gap-3 pt-2">
                <div className="flex flex-row gap-1 items-center bg-slate-100 py-1.5 rounded-md px-3">
                  <RLabel table={"member"} field={`data.owner`}>
                    Owner
                  </RLabel>
                  <RInput
                    hiddenType={"__boolean__"}
                    table={"member"}
                    field={`data.owner`}
                    type="checkbox"
                    className="checkbox"
                    defaultChecked={!!selectedMember?.owner}
                  />
                </div>
                <div className="inline-flex gap-1 items-center bg-slate-100 py-1.5 rounded-md px-3">
                  <RLabel table={"member"} field={`data.crew`}>
                    Crew
                  </RLabel>
                  <RInput
                    hiddenType={"__boolean__"}
                    table={"member"}
                    field={`data.crew`}
                    type="checkbox"
                    className="checkbox"
                    defaultChecked={!!selectedMember?.crew}
                  />
                </div>

                <div className="inline-flex gap-1 items-center  bg-slate-100 py-1.5 rounded-md px-3">
                  <RLabel table={"member"} field={`data.captain`}>
                    Boat captain
                  </RLabel>
                  <RInput
                    hiddenType={"__boolean__"}
                    table={"member"}
                    field={`data.captain`}
                    type="checkbox"
                    className="checkbox"
                    defaultChecked={!!selectedMember?.captain}
                  />
                </div>

                <div className="inline-flex gap-1 items-center bg-slate-100 py-1.5 rounded-md px-3">
                  <RLabel table={"member"} field={`data.freelancer`}>
                    Freelancer
                  </RLabel>
                  <RInput
                    hiddenType={"__boolean__"}
                    table={"member"}
                    field={`data.freelancer`}
                    type="checkbox"
                    className="checkbox"
                    defaultChecked={!!selectedMember?.freelancer}
                  />
                </div>
                <div className="flex">
                  <RSelect
                    table={"member"}
                    field={`data.diving_level`}
                    defaultValue={selectedMember?.diving_level + ""}
                    className="select w-auto"
                  >
                    {checks.diving_level?.options?.map((label, index) => (
                      <option key={index} value={index + ""}>
                        {index ? label : "No Diving Level"}
                      </option>
                    ))}
                  </RSelect>
                </div>
              </div>
            </div>
            <div className="flex flex-row gap-3 items-center justify-end pt-8">
              <ParamLink paramState={{ toggle_modal: undefined }} replace className="btn">
                Cancel
              </ParamLink>
              <SubmitButton className="btn btn-primary">{selectedMember ? "Save" : "Create"}</SubmitButton>
            </div>
          </ActionForm>
        </div>
      </SidePanel>
    </div>
  );
}
