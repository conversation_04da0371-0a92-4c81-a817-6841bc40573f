import { Form, useActionData, useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { getVirtualBankAccounts } from "~/domain/payment/xendit-client.server";
import { LoaderFunctionArgs } from "@remix-run/router";
import React from "react";
import { isEditorQb } from "~/domain/member/member-queries.server";
import { getSessionSimple } from "~/utils/session.server";
import { ActionFunctionArgs } from "@remix-run/node";
import { unauthorized } from "~/misc/responses";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const xenditEnvironmentId = params.environment_id;
  if (!xenditEnvironmentId) throw new Error("EnvironmentId is required");

  const ctx = await getSessionSimple(request);
  const editor = await isEditorQb({ trx: kysely, ctx: ctx })
    .select((eb) =>
      eb
        .selectFrom("xendit_platform")
        .innerJoin("xendit_account", "xendit_account.xendit_platform_id", "xendit_platform.id")
        .innerJoin("xendit_environment", "xendit_environment.xendit_platform_id", "xendit_platform.id")
        .where("xendit_account.main", "=", true)
        .whereRef("xendit_account.production", "=", "xendit_environment.production")
        .where("xendit_platform.id", "=", xenditEnvironmentId)
        .select("xendit_environment.xendit_api_key")
        .as("xendit_api_key"),
    )
    .executeTakeFirst();

  const xenditApiKey = editor?.xendit_api_key;
  if (!xenditApiKey) throw new Error("Not authorized or Xendit env not found");

  const getVirtualBankAccountsSave = () => {
    try {
      return getVirtualBankAccounts({ apiKey: xenditApiKey, headers: {} });
    } catch (e) {
      console.error("could not retrive bank account", e);
      return "could not retrieve bank account";
    }
  };

  return {
    banks: await getVirtualBankAccountsSave(),
    accounts: [],
  };
};

export const action = async (args: ActionFunctionArgs) => {
  const xenditPlatformId = args.params.environment_id;
  if (!xenditPlatformId) throw new Error("XenditEnvId is required");
  const ctx = await getSessionSimple(args.request);
  const fomrdata = await args.request.formData();
  const editor = await isEditorQb({ trx: kysely, ctx: ctx })
    .select((eb) => [
      "_user.editor",
      eb
        .selectFrom("xendit_platform")
        .innerJoin("xendit_account", "xendit_account.xendit_platform_id", "xendit_platform.id")
        .innerJoin("xendit_environment", "xendit_environment.xendit_platform_id", "xendit_platform.id")
        .where("xendit_account.main", "=", true)
        .whereRef("xendit_account.production", "=", "xendit_environment.production")
        .where("xendit_platform.id", "=", xenditPlatformId)
        .as("xendit_environment"),
    ])
    .executeTakeFirst();

  if (!editor) throw unauthorized();

  const xenditApiKey = editor?.xendit_environment;
  if (!xenditApiKey) throw new Error("Not authorized or Xendit env not found");
  const encodeApiKey = Buffer.from(xenditApiKey + ":").toString("base64");
  const inputJson = Object.fromEntries(fomrdata);
  console.log("inputjsoon", inputJson);
  const url = new URL("https://api.xendit.co/pool_virtual_accounts/simulate_payment");
  fomrdata.forEach((value, key) => {
    if (typeof value === "string") {
      url.searchParams.set(key, value);
    }
  });
  const result = await fetch(url.toString(), {
    headers: {
      Authorization: `Basic ${encodeApiKey}`,
      "Content-Type": "application/json",
    },
    method: "POST",
    body: JSON.stringify(inputJson),
  });
  console.log("xendit simulate payment result", result.ok, result.status, result.statusText);
  const response = await result.json();
  console.log("xendit simulate payment response", response);

  return { success: true, msg: response };
};

export default function Page() {
  const data = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const banks = data.banks;
  return (
    <div>
      {data && <pre>{JSON.stringify(data.accounts, null, 2)}</pre>}
      {actionData && <pre>{JSON.stringify(actionData, null, 2)}</pre>}
      {typeof banks === "string" ? (
        <p>{banks}</p>
      ) : (
        <Form method={"post"}>
          <input name={"transfer_amount"} placeholder={"transfer amount"} />
          <input name={"bank_account_number"} placeholder={"bank_account_number"} />
          <select name={"bank_code"}>
            {banks.map((bank) => (
              <option key={bank.code} value={bank.code}>
                {bank.name} ({bank.code})
              </option>
            ))}
          </select>
          <button>simulate payment</button>
        </Form>
      )}
    </div>
  );
}
