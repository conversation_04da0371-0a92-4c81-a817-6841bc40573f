import { featureKeys, features } from "~/domain/feature/feature";
import React from "react";
import { ActionAlert } from "~/components/ActionAlert";
import { fName } from "~/misc/helpers";
import { FInput, RInput } from "~/components/ResourceInputs";
import { ActionForm } from "~/components/form/BaseFrom";
import { SubmitButton } from "~/components/base/Button";
import { useAppContext } from "~/hooks/use-app-context";
import { HiddenTypeInput } from "~/components/form/DefaultInput";

export { action } from "~/routes/_all._catch.resource";

export default function Page() {
  const session = useAppContext();

  const table = session.user_id ? "user" : "session";
  const id = session.user_id || session.session_id;

  return (
    <div className="app-container space-y-3 py-6">
      <h1 className="text-xl font-bold">Features (preview)</h1>
      <p>These features are "work in progress".</p>
      <p>
        Enabled features for "<strong>{session.email}</strong>":
      </p>
      {/* key is needed to refresh default values when user switches account and has different features enabled*/}
      <ActionForm key={session.user_id} className="group">
        <ActionAlert />
        <RInput table={table} field={"id"} value={id} />
        <div className="space-y-4">
          <input
            type={"hidden"}
            name={fName("user", "data.features", 0, 0)}
            value={
              "will be filtered out on backend en does create an array when not feature is selected, otherwise it will try to delete the user"
            }
          />
          <HiddenTypeInput name={fName(table, "data.features")} value={"__empty_array__"} />
          {featureKeys.map((featureKey, index) => {
            const description = features[featureKey];
            return (
              <div key={featureKey}>
                <label>
                  <span className="capitalize">{featureKey}&nbsp;</span>
                  <FInput
                    type="checkbox"
                    className="checkbox"
                    name={fName(table, "data.features", 0, index)}
                    value={featureKey}
                    defaultChecked={session.features.includes(featureKey)}
                  />
                  &nbsp; &nbsp;
                </label>
                <p className="whitespace-pre-wrap text-slate-500">{description || "no description"}</p>
              </div>
            );
          })}
          <SubmitButton className="link disabled:text-gray-500 disabled:no-underline">
            <span className="hidden group-aria-busy:inline">Saving...</span>
            <span className="inline group-aria-busy:hidden">Save</span>
          </SubmitButton>
        </div>
        <span className={"hidden text-green-500 group-data-success:inline"}>Saved!</span>
      </ActionForm>
    </div>
  );
}
