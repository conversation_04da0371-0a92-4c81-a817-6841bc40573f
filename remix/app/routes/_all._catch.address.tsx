import { ActionForm } from "~/components/form/BaseFrom";
import { AddressInput } from "~/components/base/AddressInput";
import { fName } from "~/misc/helpers";
import { RInput } from "~/components/ResourceInputs";
import { BasicCountrySelect } from "~/components/CountrySelect";
import { SubmitButton } from "~/components/base/Button";
import { Fragment, useState } from "react";
import type { AddressType, GeocodingAddressComponentType } from "@googlemaps/google-maps-services-js";
import PlaceResult = google.maps.places.PlaceResult;

export { action } from "~/routes/_all._catch.resource";
//
// const placeFields = (props: {place: PlaceResult}) => {
//   const
//   return <Fragment>
//
//   </Fragment>
// }

export default function Page() {
  const [place, setPlace] = useState<PlaceResult | null>(null);

  const getComponent = (type: `${AddressType}` | `${GeocodingAddressComponentType}`) =>
    place?.address_components?.find((c) => c.types.includes(type)) || null;

  console.log("place", place);

  const countryCode = getComponent("country")?.short_name;

  console.log("coutrcoe", countryCode === "NL", countryCode);
  return (
    <div className="app-container">
      <ActionForm className="space-y-3">
        <div className="flex flex-row gap-3">
          <AddressInput name={fName("address", "data.formatted_address")} className="input" onPlaceChange={setPlace} />
          <button type={"button"} onClick={() => setPlace(null)}>
            clear
          </button>
        </div>
        <Fragment key={place?.place_id || ""}>
          <RInput
            table={"address"}
            field={"data.route"}
            placeholder={"Street"}
            className="input"
            defaultValue={getComponent("route")?.long_name}
            disabled={!!place}
          />
          <RInput table={"address"} field={"data.postal_code"} className="input" defaultValue={getComponent("postal_code")?.long_name} />
          <select defaultValue={countryCode || ""}>
            <option value="">slect</option>
            <option value="NL">nether</option>
          </select>
          <BasicCountrySelect name={fName("address", "data.country_code")} className="select" defaultValue={countryCode} />
        </Fragment>
        <SubmitButton className="btn btn-primary">Save</SubmitButton>
      </ActionForm>
    </div>
  );
}
