import { difficultyLevels, gradingDisclaimer } from "~/domain/diving-site/diving-site";
import { DifficultyLevel } from "~/domain/diving-site/components";
import React from "react";
import { ikUrl } from "~/components/IkImage";
import { LinksFunction } from "@remix-run/server-runtime";

const heroUrl = ikUrl("images/divesite-grade-top.jpeg", "tr:q-80,h-500,w-1980,c-at_least");
const bottomUrl = ikUrl("images/divesite-grade-bottom.jpeg", "tr:q-80,h-400,w-600,c-at_least");

export const links: LinksFunction = () => [
  {
    as: "image",
    href: heroUrl,
    rel: "preload",
  },
];

export default function Page() {
  return (
    <div>
      <div className="bg-cover bg-center object-fill" style={{ backgroundImage: `url("${heroUrl}")` }}>
        <div className="image-text flex flex h-full h-80 w-full content-center items-center ">
          <div className="container flex h-auto max-w-6xl flex-wrap items-center justify-between text-white">
            <div className="space-y-1">
              <h1 className="text-2xl font-semibold tracking-wide md:text-4xl">Dive site grading</h1>
            </div>
          </div>
        </div>
      </div>
      <div className="app-container py-8">
        <div className="space-y-6">
          <p>
            At traveltruster, we want to provide all relevant information for everyone. Our partner operators may accommodate a wide
            experience range with divers, from novice students to the most experienced instructors.
          </p>
          <p>
            Based on our research there are different dive sites in Bali that require different experience levels of divers. There are many
            factors that change the suggested grade of a dive site bas/ed on visibility, weather, tides and more.
          </p>
          <p className="font-bold">{gradingDisclaimer}</p>

          <div className="max-w-screen-md  space-y-7">
            {difficultyLevels.map((levelInfo, index) => {
              const level = index + 1;
              return (
                <p key={index} className="space-y-2">
                  <div className="flex flex-wrap gap-2 py-1">
                    <DifficultyLevel level={level} className="text-xs" />
                    {/*<strong>{"->"}</strong>*/}
                    <p className="text-xl font-semibold">{levelInfo.title}</p>
                  </div>
                  <ul className="list-disc space-y-1 pl-6">
                    {levelInfo.description.split("\n").map((item) => (
                      <li key={item}>{item}</li>
                    ))}
                  </ul>
                </p>
              );
            })}
          </div>
        </div>
      </div>
      <img alt="diver" src={bottomUrl} width={"100%"} className="h-64 object-cover md:hidden" />
    </div>
  );
}
