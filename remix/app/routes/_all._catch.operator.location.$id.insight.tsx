import type { AsyncReturnType } from "type-fest";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { getSessionSimple } from "~/utils/session.server";
import { EditorRequired } from "~/components/account/AccountContainer";
import { jsonBuildObject } from "kysely/helpers/postgres";
import { _product_detail } from "~/misc/paths";
import { CheckIcon, XMarkIcon } from "@heroicons/react/20/solid";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import { LoaderFunctionArgs } from "@remix-run/router";
import { ParamLink } from "~/components/meta/CustomComponents";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);
  const user = await activeUserSessionSimple(kysely, session_id, true).select("_user.editor").executeTakeFirst();
  if (!user?.editor) return null;
  const establishmentId = params.id!;

  return kysely
    .selectFrom("inquiry")
    .where("inquiry.establishment_id", "=", establishmentId)
    .innerJoin("user_session", "user_session.id", "inquiry.created_by_user_session_id")
    .innerJoin("user", "user.id", "user_session.user_id")
    .selectAll("inquiry")
    .select((eb) => [
      eb
        .selectFrom("product")
        .innerJoin("item", "item.id", "product.item_id")
        .where("product.id", "=", eb.ref("inquiry.product_id"))
        .select("product.root_id")
        .as("product_root_id"),
      jsonBuildObject({
        email: eb.ref("user.email"),
        display_name: eb.ref("user_session.display_name"),
        verified_at: eb.ref("user_session.verified_at"),
      }).as("user"),
      // jsonObjectFrom(eb.selectFrom('product').whereRef('product.id', '=', 'inquiry.product_id').selectAll())
      "inquiry.created_at",
    ])
    .orderBy("inquiry.created_at", "desc")
    .execute();
};

type LoaderResponse = AsyncReturnType<typeof loader>;

export default function Page() {
  const response = useLoaderData<LoaderResponse>();

  if (!response) return <EditorRequired />;

  return (
    <div className="app-container space-y-3 pt-3">
      <h3 className="text-lg">Inquiries</h3>
      <div className="overflow-auto">
        <table>
          <thead>
            <tr className="">
              <td className="p-2">Created</td>
              <td className="p-2">Method</td>
              <td className="p-2">Email</td>
              <td className="p-2">Name</td>
              <td className="p-2">Nr of participants</td>
              <td className="p-2">Product</td>
            </tr>
          </thead>
          {response.map((inquiry) => {
            return (
              <tr key={inquiry.id} className="border-t border-slate-200">
                <td className="p-2">{inquiry.created_at}</td>
                <td className="p-2">{inquiry.communication_method}</td>
                <td className="p-2">
                  {inquiry.user.email}{" "}
                  {inquiry.user.verified_at ? (
                    <CheckIcon className="inline-block h-5 w-5 text-green-500" />
                  ) : (
                    <XMarkIcon className="inline-block h-5 w-5 text-red-500" />
                  )}
                </td>
                <td className="p-2">{inquiry.user.display_name}</td>
                <td className="p-2">{inquiry.nr_of_participants}</td>
                {/*<td className="p-2">*/}
                {/*  {!!inquiry.product_id && (*/}
                {/*    <ParamLink path={_product_detail(inquiry.product_id)} paramState={{ exact: true }} className="link" target={"_blank"}>*/}
                {/*      product*/}
                {/*    </ParamLink>*/}
                {/*  )}*/}
                {/*</td>*/}
                <td className="p-2">
                  {!!inquiry.product_id && (
                    <ParamLink path={_product_detail(inquiry.product_id)} className="link" target={"_blank"}>
                      product
                    </ParamLink>
                  )}
                </td>
              </tr>
            );
          })}
        </table>
      </div>
    </div>
  );
}
