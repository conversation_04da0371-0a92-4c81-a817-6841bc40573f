import { use<PERSON><PERSON>der<PERSON><PERSON> } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import React from "react";
import { getSessionSimple } from "~/utils/session.server";
import { LoaderFunctionArgs } from "@remix-run/router";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _xendit_account_detail } from "~/misc/paths";
import { ActionForm } from "~/components/form/BaseFrom";
import { SubmitButton } from "~/components/base/Button";
import { RInput, RSelect } from "~/components/ResourceInputs";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { tableIdRef } from "~/misc/helpers";
import { isEditorQb, toArgs } from "~/domain/member/member-queries.server";
import { unauthorized } from "~/misc/responses";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);

  const editor = await isEditorQb(toArgs(kysely, session_id)).select("_user.editor").executeTakeFirst();

  if (!editor) throw unauthorized();

  const xenditEnvironments = await kysely
    .selectFrom("xendit_platform")
    .select(["xendit_platform.id", "xendit_platform.name", "xendit_platform.currency_id"])
    .execute();

  const xenditAccounts = await kysely
    .selectFrom("xendit_account")
    .selectAll("xendit_account")
    .select((eb) => [
      jsonArrayFrom(simpleEstablishmentQb.where("establishment.xendit_account_id", "=", eb.ref("xendit_account.id"))).as("establishments"),
    ])
    .execute();

  return {
    xendit_platforms: xenditEnvironments,
    accounts: xenditAccounts,
  };
};

export default function Page() {
  const response = useLoaderData<typeof loader>();

  return (
    <div className="app-container space-y-3">
      <h2 className="text-xl font-bold">Xendit accounts</h2>
      <div>
        <p>Add account</p>
        <ActionForm className="flex flex-row gap-3">
          <RSelect table={"xendit_account"} field={"data.xendit_platform_id"} className="select">
            {response.xendit_platforms.map((xenditEnv) => (
              <option value={xenditEnv.id} key={xenditEnv.id}>
                {xenditEnv.name}
              </option>
            ))}
          </RSelect>

          <RInput table={"xendit_account"} field={"data.xendit_user_id"} className="input" placeholder="Xendit account id" />
          <div className="inline-flex gap-1 items-center">
            <RInput
              className="checkbox"
              table={"xendit_account"}
              field={"data.production"}
              hiddenType={"__boolean__"}
              label={"production"}
              type={"checkbox"}
            />
          </div>
          <RedirectParamsInput path={_xendit_account_detail(tableIdRef("xendit_account"))} paramState={{}} />
          <SubmitButton className="btn btn-primary">add</SubmitButton>
        </ActionForm>
      </div>
      <div className="space-y-3">
        {response.accounts.map((account) => {
          const env = response.xendit_platforms.find((env) => env.id === account.xendit_platform_id);
          return (
            <div key={account.id}>
              <ParamLink className="link" path={_xendit_account_detail(account.id)}>
                {account.id.slice(-5)} - {env?.name} - {account.xendit_account_response?.public_profile?.business_name || "..."} -{" "}
                {account.production ? "prod" : "test"}
              </ParamLink>
              <details>
                <summary>Details ({account.establishments.map((establishment) => establishment.operator_name).join(", ")})</summary>
                <pre>{JSON.stringify(account, null, 2)}</pre>
              </details>
            </div>
          );
        })}
      </div>
    </div>
  );
}
