import type { MetaFunction } from "@remix-run/react";
import { Outlet } from "@remix-run/react";
import React from "react";

export const meta: MetaFunction = () => {
  return [
    { title: "API Documentation - ScamGem" },
    { name: "description", content: "Complete API documentation for ScamGem's form-based API system" },
  ];
};

export default function ApiDocumentationLayout() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Outlet />
      </div>
    </div>
  );
}
