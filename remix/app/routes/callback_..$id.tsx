import type { LoaderFunction } from "@remix-run/server-runtime";
import { kysely } from "~/misc/database.server";
import { nowValue } from "~/kysely/kysely-helpers";
import { callbacks, handleCallback } from "~/domain/callback/callback.server";
import { ActionFunctionArgs } from "@remix-run/node";

export const action = async ({ request, params }: ActionFunctionArgs) => {
  const id = params.id!;
  const [success, msg] = await (async () => {
    const callbackObj = await kysely
      .updateTable("callback")
      .set({ started_at: nowValue })
      .where("callback.id", "=", id)
      .where("callback.handled_at", "is", null)
      .where("callback.started_at", "is", null)
      .returningAll("callback")
      .executeTakeFirst();

    if (!callbackObj) {
      console.log("callback does not exist or was removed before executed", id);
      return [false, "callback does not exist or was removed before executed"];
    }

    const callback: (typeof callbacks)[keyof typeof callbacks] | undefined = callbacks[callbackObj.name as keyof typeof callbacks];

    const callbackResponse = await kysely.transaction().execute((trx) => handleCallback(request, trx, callback, callbackObj));

    await kysely
      .updateTable("callback")
      .where("callback.id", "=", id)
      .where("callback.handled_at", "is", null)
      .set({ handled_at: nowValue, success: callbackResponse[0], msg: callbackResponse[1] })
      .executeTakeFirstOrThrow();

    return callbackResponse;
  })();
  console.log("event google task queue", id);
  return { success: success, msg: msg };
};

export const loader: LoaderFunction = async ({ request, params }) => {
  console.log("getting event from google task queue", await request.text());
  return {};
};
