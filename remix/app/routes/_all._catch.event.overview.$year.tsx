import type { AsyncReturnType } from "type-fest";
import { kysely } from "~/misc/database.server";
import { Outlet, useLoaderData, useNavigation } from "@remix-run/react";
import React from "react";
import { sql } from "kysely";
import { getSessionSimple } from "~/utils/session.server";
import { EditorRequired } from "~/components/account/AccountContainer";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import { LoaderFunctionArgs } from "@remix-run/router";
import { unauthorized } from "~/misc/responses";
import { getDbCachedValue } from "~/server/cache/cache.server";
import { differenceInDays, format, isAfter, isSameMonth } from "date-fns";
import { extractMonth, extractYear } from "~/kysely/kysely-helpers";
import { ParamLink } from "~/components/meta/CustomComponents";
import { ChevronLeft } from "lucide-react";
import { twMerge } from "tailwind-merge";
import { _event_overview, _event_year, _event_year_month } from "~/misc/paths";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);
  const verifiedEditor = await activeUserSessionSimple(kysely, session_id, true).where("_user.editor", "=", true).executeTakeFirst();

  if (!verifiedEditor) throw unauthorized();

  const year = Number.parseInt(params.year!);
  const today = new Date();

  const months = Array.from({ length: 12 }, (_, monthIndex) => {
    const monthStr = (monthIndex + 1 + "").padStart(2, "0");
    const cacheKey = ["events", year, monthStr, "totals", 2].join(".");
    const date = new Date(year, monthIndex);

    const monthFormatted = format(date, "MMMM");
    return {
      index: monthIndex,
      daysBetweenNow: differenceInDays(today, date),
      isSame: isSameMonth(date, today),
      isAfter: isAfter(date, today),
      key: cacheKey,
      str: monthStr,
      formatted: monthFormatted,
    };
  });

  const promises = months
    .filter((month) => !month.isAfter)
    .map(async (month) => {
      const ttlInSeconds = month.daysBetweenNow > 80 ? null : 60 * 60 * 24;
      const cache = await getDbCachedValue({
        key: month.key,
        request: (trx) =>
          trx
            .selectFrom("event")
            .leftJoin("user", "event.user_id", "user.id")
            .where("event.user_id", "is", null)
            .where((eb) =>
              eb.and([
                eb(extractYear(eb.ref("event.created_at")), "=", year),
                eb(extractMonth(eb.ref("event.created_at")), "=", month.index + 1),
              ]),
            )
            .where((ex) =>
              ex("user.editor", "is not", true).or("event.user_id", "not in", ex.selectFrom("member").select("member.user_id")),
            )
            .innerJoin("establishment as ol", (eb) =>
              eb.on((eb) =>
                eb("ol.id", "=", eb.ref("event.target_id")).or(
                  eb.exists(
                    eb
                      .selectFrom("product")
                      .innerJoin("item", "item.id", "product.item_id")
                      .whereRef("product.id", "=", "event.target_id")
                      .whereRef("item.establishment_id", "=", "ol.id"),
                  ),
                ),
              ),
            )
            .innerJoin("operator as o", "o.id", "ol.operator_id")
            .innerJoin("spot", "spot.id", "ol.spot_id")
            .select((eb) => [
              // (eb) => extractYear(eb.ref("event.created_at")).as("year"),
              // (eb) => extractMonth(eb.ref("event.created_at")).as("month"),
              // (eb) => sql<string>`(to_char(date_trunc('month', ${eb.ref("event.created_at")}), 'month'))`.as("month_as_text"),
              eb.fn.count("event.id").as("total_count"),
              sql<number>`(count ( distinct ${eb.ref("event.session_id")}) filter (where ${eb.ref("event.user_id")} is null))`.as(
                "anonymous_count",
              ),
              sql<number>`(count (1) filter (where ${eb.ref("event.tag")} = 'whatsapp'))`.as("whatsapp_click_count"),
              sql<number>`(count (1) filter (where ${eb.ref("event.tag")} = 'telephone'))`.as("telephone_click_count"),
              sql<number>`(count (1) filter (where ${eb.ref("event.tag")} = 'email'))`.as("email_click_count"),
              sql<number>`(count (1) filter (where ${eb.ref("event.tag")} = 'website'))`.as("website_click_count"),
            ])
            .executeTakeFirstOrThrow(),
        ttlInSeconds: ttlInSeconds,
      });
      return { ...month, ...cache };
    });

  const moths = (await Promise.all(promises)).sort((a, b) => a.key.localeCompare(b.key));

  return {
    year: year,
    months: moths,
    // updated_at: events.updated_at,
  };
};

type LoaderResponse = AsyncReturnType<typeof loader>;

const NextYear = (props: { addYear: number }) => {
  const data = useLoaderData<LoaderResponse>();
  const nav = useNavigation();
  const newYear = data.year + props.addYear;
  return (
    <ParamLink path={_event_year(newYear)} aria-busy={nav.state !== "idle"} className="aria-busy:spinner spinner-dark">
      <ChevronLeft className={twMerge("w-5 h-5", props.addYear > 0 && "rotate-180")}></ChevronLeft>
    </ParamLink>
  );
};

export default function Page() {
  const data = useLoaderData<LoaderResponse>();
  if (!data) return <EditorRequired />;

  return (
    <div className={"app-container flex flex-col space-y-3"}>
      <h1 className="text-2xl font-bold flex flex-row gap-1 items-center">
        <ParamLink path={_event_overview}>Pageviews</ParamLink> <NextYear addYear={-1} />
        {data.year} <NextYear addYear={1} />
      </h1>
      <div className={"flex flex-col space-y-3"}>
        <div>
          <p className={"mt-1"}>These are the number of pageviews and clicks grouped by month.</p>
          <p className={"mt-1"}>Only unique users that have viewed either the operator- or product detail page are counted.</p>
        </div>
        <h2 className={"text-xl"}>Totals per month:</h2>
        <div className={"grid grid-cols-1 gap-3 pb-3 md:grid-cols-3 lg:grid-cols-4"}>
          {data.months.map((monthGroup) => {
            return (
              <ParamLink
                key={monthGroup.key}
                path={_event_year_month(data.year, monthGroup.str)}
                className={twMerge(
                  `block flex flex-col rounded bg-gray-100 p-3 hover:bg-gray-200 aria-current:bg-gray-200`,
                  !monthGroup.response.total_count && "opacity-50",
                )}
              >
                <span className={"first-letter:capitalize"}>{monthGroup.formatted}</span>
                <span>{monthGroup.response.anonymous_count} Anonymous</span>
                <span>{monthGroup.response.whatsapp_click_count} Whatsapp clicks</span>
                <span>{monthGroup.response.email_click_count} Email clicks</span>
                <span>{monthGroup.response.telephone_click_count} Telephone clicks</span>
                <span>{monthGroup.response.website_click_count} Website clicks</span>
              </ParamLink>
            );
          })}
        </div>
        <Outlet />
      </div>
    </div>
  );
}
