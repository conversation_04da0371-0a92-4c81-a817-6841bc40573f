import { useLoaderData } from "@remix-run/react";
import { AnimatingDiv, Backbutton } from "~/components/base/base";
import { kysely } from "~/misc/database.server";
import { SubmitButton } from "~/components/base/Button";
import { getGeneratedProductTitle, ProductItem, ProductItemModel } from "~/domain/product/ProductItem";
import { FieldInputDuration } from "~/components/field/duration_in_hours/FieldInputDuration";
import { getEstablishmentShort } from "~/domain/establishment/helpers";
import { Tooltip } from "~/components/base/tooltip";
import { MoneyValueFull } from "~/components/field/MoneyValue";
import { _product_detail } from "~/misc/paths";
import { AddonCheckbox } from "~/domain/addon/AddonCheckbox";
import { ActivitySlug, getActivity, getActivitySlug } from "~/domain/activity/activity";
import React, { Fragment, useId, useRef, useState } from "react";
import { InformationCircleIcon, XMarkIcon } from "@heroicons/react/20/solid";
import { fName, keys, removeKeyRecursively, tableIdRef } from "~/misc/helpers";
import { RInput, RLabel, RSelect, RTextarea } from "~/components/ResourceInputs";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { OperationInput, RedirectInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { ActionAlert } from "~/components/ActionAlert";
import { ActionForm, defaultEqualCheck, useFormCtx } from "~/components/form/BaseFrom";
import { LoaderFunctionArgs } from "@remix-run/router";
import { defaultCurrency } from "~/misc/vars";
import { paramsToRecord, removeFromArray, toggleArray } from "~/misc/parsers/global-state-parsers";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { notNull, unnestArray } from "~/kysely/kysely-helpers";
import {
  DivingCertificateLevelKey,
  divingLevelsz,
  divingOrganizationszz,
  getDivingCertificateOrganization,
} from "~/domain/diving-course/diving-courses.data";
import { createPageOverwrites } from "~/misc/consts";
import { setState, useSearchParams2 } from "~/hooks/use-search-params2";
import { CDialog } from "~/components/base/Dialog";
import { ParamLink } from "~/components/meta/CustomComponents";
import { Checker } from "~/components/Checker";
import { CgClose } from "react-icons/cg";
import { disableScrollOnNumberInput } from "~/utils/component-utils";
import { formdataToNestedJson } from "~/misc/formdata-to-nested-json";
import { InputFilesDefault } from "~/components/form/FilesInput";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { refreshFormdata } from "~/components/form/form-hooks";
import { getDataForTable, jsonToStructuredArray } from "~/misc/json-to-structured-array";
import { useBoolean } from "~/hooks/use-boolean";
import { useIsInterative } from "~/hooks/hooks";
import { pricesJsonEb } from "~/domain/product/product-queries.server";
import { ProductPriceForm } from "~/domain/product/ProductPriceForm";

export { action } from "~/routes/_all._catch.resource";

// const defaultItem : Selecta
const defaultProduct: ProductItemModel = {
  activity_slug: "other",
  diving_count: 0,
  gear_included: false,
  n_sessions: 0,
  published: true,
  pickup: false,
  stay: false,
  product_prices: [],
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  const isCopy = state.action_type === "copy";

  const establishment = await simpleEstablishmentQb
    .where(
      "establishment.id",
      "=",
      state.id
        ? kysely
            .selectFrom("product")
            .innerJoin("item", "item.id", "product.item_id")
            .where("product.id", "=", state.id)
            .select("item.establishment_id")
        : state.establishment_id,
    )
    .select((eb) => [
      "establishment.id",
      jsonArrayFrom(eb.selectFrom("tag").where("tag.establishment_id", "=", eb.ref("establishment.id")).selectAll("tag")).as("tags"),
      jsonArrayFrom(
        eb
          .selectFrom("addon")
          .whereRef("addon.establishment_id", "=", "establishment.id")
          .selectAll("addon")
          .select((eb) =>
            notNull(
              jsonObjectFrom(
                eb.selectFrom("price").select(["price.amount", "price.currency_id"]).whereRef("price.id", "=", "addon.price_id"),
              ),
            ).as("price"),
          ),
      ).as("addons"),
      jsonArrayFrom(
        eb
          .selectFrom("diving_location")
          .select(["diving_location.id", "diving_location.name"])
          .whereRef("diving_location.region_id", "=", "spot.region_id")
          .orderBy("name"),
      ).as("diving_locations"),
      jsonArrayFrom(
        eb.selectFrom("diving_site").select(["diving_site.id", "diving_site.name", "diving_site.diving_location_id"]).orderBy("name"),
      ).as("diving_sites"),
      jsonArrayFrom(
        kysely
          .selectFrom("diving_course")
          .selectAll("diving_course")
          .innerJoin(unnestArray(keys(divingLevelsz), "dcl"), "dcl.key", "diving_course.diving_certificate_level_key")
          .innerJoin(unnestArray(keys(divingOrganizationszz), "dco"), "dco.key", "diving_course.diving_certificate_organization_key")
          // .orderBy(eb => eb.o)
          // .orderBy((eb) =>
          //   sql.raw(
          //     `array_position(array[${divingCertificateIds
          //       .map((id) => `'${id}'`)
          //       .join(", ")}]::text[], diving_course.diving_certificate_level)`,
          //   ),
          // )
          .orderBy("dcl.pos")
          .orderBy("dco.pos")
          .orderBy("diving_course.sort_order")
          .orderBy("diving_course.name"),
      ).as("diving_courses"),
      jsonObjectFrom(
        kysely
          .selectFrom("product")
          .innerJoin("item", "item.id", "product.item_id")
          .selectAll(["item", "product"])
          .where("product.id", "=", state.id)
          .select((eb) => [
            notNull(jsonObjectFrom(eb.selectFrom("item").selectAll("item").whereRef("item.id", "=", "product.item_id"))).as("item"),
            "product.id as id",
            pricesJsonEb,
            jsonArrayFrom(
              eb
                .selectFrom("product__tag")
                .innerJoin("tag", "tag.id", "product__tag.tag_id")
                .whereRef("product__tag.product_id", "=", "product.id")
                .selectAll("product__tag"),
            ).as("tags"),
            jsonArrayFrom(fileTargetsQb(kysely, "product", eb.ref("product.id"))).as("files"),
            jsonArrayFrom(
              eb
                .selectFrom("product__diving_location")
                .whereRef("product__diving_location.product_id", "=", "product.id")
                .selectAll("product__diving_location"),
            ).as("diving_locations"),
            jsonArrayFrom(
              eb
                .selectFrom("product__diving_site")
                .whereRef("product__diving_site.product_id", "=", "product.id")
                .selectAll("product__diving_site"),
            ).as("diving_sites"),
            jsonArrayFrom(
              eb.selectFrom("item__diving_course").whereRef("item__diving_course.item_id", "=", "item.id").selectAll("item__diving_course"),
            ).as("diving_courses"),
          ]),
      ).as("product"),
    ])
    .executeTakeFirstOrThrow();

  const operation = state.id ? (isCopy ? "copy" : "edit") : "create";

  const retrievedProduct = establishment.product;

  const finalProduct = operation !== "edit" && retrievedProduct ? removeKeyRecursively(retrievedProduct, "id") : retrievedProduct;
  const activitySlug = getActivitySlug(finalProduct?.item?.activity_slug || state.activity_slug) || ("other" satisfies ActivitySlug);
  return {
    product: finalProduct,
    activity_slug: activitySlug,
    // default_product: defaultProduct,
    // product: product,
    operation: operation,
    establishment: establishment,
    addons: establishment.addons,
    diving_courses: establishment.diving_courses,
    diving_locations: establishment.diving_locations,
    diving_sites: establishment.diving_sites,
    ...createPageOverwrites({ base_currency: establishment.default_currency || defaultCurrency }),
  };
};

const CreateTagForm = (props: { establishment_id: string }) => {
  const inputId = useId();
  return (
    <ActionForm className="space-y-1" generateIdentifier replace>
      <ActionAlert />
      <RedirectParamsInput path={"./"} paramState={{ element_clear: [inputId], element_action: [inputId] }} />
      <input type={"hidden"} />
      <div className="flex flex-row gap-3">
        <RInput table={"tag"} field={"data.establishment_id"} type={"hidden"} value={props.establishment_id} />
        <RInput table={"tag"} field={"data.name"} id={inputId} placeholder="Tag name" className="input" />
        <SubmitButton className="link">Create</SubmitButton>
      </div>
    </ActionForm>
  );
};

export const useProductPreview = () => {
  const response = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const formCtx = useFormCtx();
  const finalFormdata = formCtx.formdata && jsonToStructuredArray(formdataToNestedJson(formCtx.formdata));

  const finalProductPlain = (finalFormdata && getDataForTable(finalFormdata, "product")[0]?.data) || response.product;
  const finalItemPlain = (finalFormdata && getDataForTable(finalFormdata, "item")[0]?.data) || response.product;

  const prices =
    (finalFormdata && getDataForTable(finalFormdata, "price").map((item) => item.data)) || response.product?.product_prices || [];

  const defaultDivingCourseIds = response.product?.diving_courses.map((item) => item.diving_course_id) || [];
  const divingCourseIds = finalFormdata
    ? getDataForTable(finalFormdata, "item__diving_course")
        .filter((item) => item.operation !== "delete")
        .map((item) => item.data?.diving_course_id)
    : defaultDivingCourseIds;
  const selectedDivingCourses = response.diving_courses.filter((dc) => divingCourseIds.includes(dc.id));

  const defaultDivingLocationIds = response.product?.diving_locations.map((item) => item.diving_location_id) || [];
  const divingLocationIds = finalFormdata
    ? getDataForTable(finalFormdata, "product__diving_location")
        .filter((item) => item.operation !== "delete")
        .map((item) => item.data?.diving_location_id)
    : defaultDivingLocationIds;

  const selectedDivingLocations = response.diving_locations.filter((item) => divingLocationIds.includes(item.id));

  const productPreview = {
    ...defaultProduct,
    ...finalItemPlain,
    ...finalProductPlain,
    product_prices: prices,
    files: response.product?.files,
    // price_currency: finalProductPlain.price_currency || defaultCurrency,
    activity_slug: response.activity_slug,
    diving_courses: selectedDivingCourses.map((dc) => ({
      ...dc,
      name: dc.name,
    })),
    diving_locations: selectedDivingLocations.map((item) => ({ id: item.id, name: item.name })),
  };

  return {
    preview: productPreview,
  };
};

export const ProductPreview = () => {
  const response = useLoaderData<typeof loader>();
  const productPreview = useProductPreview();
  const firstPrice = productPreview.preview.product_prices[0];
  const activity = getActivity(response.activity_slug);
  return (
    <div className="top-0 z-10 space-y-3 border-b border-slate-200 bg-white py-3 md:sticky">
      <div className="app-container space-y-3">
        <div className="flex flex-row justify-between">
          <div>
            <h1 className="text-xl font-bold first-letter:capitalize">
              {response.operation} {activity.name} product
            </h1>
            <p>{getEstablishmentShort(response.establishment)}</p>
          </div>
          <div className="flex flex-row items-center gap-3">
            <Backbutton>Cancel</Backbutton>
            <SubmitButton className="btn btn-primary">Save</SubmitButton>
          </div>
        </div>
        <div className="flex flex-wrap gap-3">
          <ProductItem
            className="w-96"
            to_currency={firstPrice?.currency_id}
            item={productPreview.preview}
            locale={response.establishment.locale}
          />
          {firstPrice && (
            <div>
              <MoneyValueFull amount={firstPrice.amount} currency={firstPrice.currency_id} locale={response.establishment.locale} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const ProductTitle = () => {
  const productPreview = useProductPreview();

  return (
    <div className="space-y-1">
      <div className="flex justify-between flex-row">
        <RLabel table={"item"} field={"data.title"} className="flex flex-row items-center">
          Title
        </RLabel>
        <span>
          <span className="text-slate-400">Default:</span> {getGeneratedProductTitle(productPreview.preview)}
        </span>
      </div>
      <RInput
        placeholder={"Enter here to replace the default generated title"}
        defaultValue={productPreview.preview.title || ""}
        className="formcontrol focus:ring-0 w-full"
        type="text"
        table={"item"}
        field={"data.title"}
      />
    </div>
  );
};

const createTagDialogKey = "create_tag";

const DivingCoursesSelectAdd = () => {
  const data = useLoaderData<typeof loader>();
  const divingCourses = data.diving_courses;
  const defaultProductDivingCourses = data.product?.diving_courses || [];
  const defaultDivingCourseIds = defaultProductDivingCourses.map((item) => item.diving_course_id);
  const hasBeenBlurred = useBoolean(false);
  const selectRef = useRef<HTMLSelectElement>(null);
  const [divinCourseIds, setDivingCourseIds] = useState(defaultDivingCourseIds);
  const [organizationFilter, setOrganizationFilter] = useState<string>("");
  const [levelFilter, setLevelFilter] = useState<string>("");

  const selectedLevel = divingLevelsz[levelFilter as DivingCertificateLevelKey];
  const selectedOrganization = getDivingCertificateOrganization(organizationFilter);
  const selectedDivingCourses = divingCourses.filter((dc) => divinCourseIds.includes(dc.id));
  const deletedDivingCourses = defaultProductDivingCourses.filter((dc) => !divinCourseIds.includes(dc.diving_course_id));

  const isJsloaded = useIsInterative();
  const invalid = hasBeenBlurred.isOn && selectedDivingCourses.length === 0;

  return (
    <div className="flex flex-col">
      <div
        className={`
     flex w-fit flex-wrap gap-3 overflow-hidden rounded-xl border border-slate-400 focus-within:ring-1 focus-within:ring-slate-500
           ${invalid ? "ring-1 ring-red-500" : ""}
      `}
      >
        <select
          className="w-44 border-none focus:bg-slate-100 focus:ring-0"
          value={organizationFilter}
          onChange={(e) => setOrganizationFilter(e.target.value)}
        >
          <option value="">all organizations</option>
          {Object.entries(divingOrganizationszz).map(([key, org]) => (
            <option key={key} value={key}>
              {org.name}
            </option>
          ))}
        </select>
        <select
          className="w-32 border-none focus:bg-slate-100 focus:ring-0"
          value={levelFilter}
          onChange={(e) => setLevelFilter(e.target.value)}
        >
          <option value="">all levels</option>
          {Object.entries(divingLevelsz).map(([key, level]) => (
            <option key={key} value={key}>
              {level}
            </option>
          ))}
        </select>
        <select
          className="border-none focus:bg-slate-100 focus:ring-0"
          ref={selectRef}
          required={selectedDivingCourses.length === 0}
          value={""}
          onBlur={hasBeenBlurred.on}
          onChange={(e) => {
            const value = e.target.value;
            if (value) {
              setDivingCourseIds([...divinCourseIds, value]);
              refreshFormdata();
            }
          }}
        >
          <option value="">
            Add {selectedOrganization ? selectedOrganization.name + " " : ""}
            {selectedLevel ? selectedLevel + " " : ""}course...
          </option>
          {Object.entries(divingLevelsz)
            .filter(([key]) => !levelFilter || key === levelFilter)
            .map(([dclKey, dcl]) => (
              <optgroup key={dclKey} label={dcl}>
                {Object.entries(divingOrganizationszz)
                  .filter(([dcoKey, dco]) => !organizationFilter || dcoKey === organizationFilter)
                  .map(([dcoKey, dco]) =>
                    divingCourses
                      .filter((dc) => dc.diving_certificate_organization_key === dcoKey && dc.diving_certificate_level_key === dclKey)
                      .map((dc) => (
                        <option key={dc.id} value={dc.id} disabled={!!selectedDivingCourses.find((selectedDc) => dc.id === selectedDc.id)}>
                          {dco.name} - {dc.name}
                        </option>
                      )),
                  )}
              </optgroup>
            ))}
        </select>
      </div>
      {deletedDivingCourses
        .filter((item) => item.id)
        .map((item) => (
          <Fragment key={item.id}>
            <RInput table={"item__diving_course"} field={"id"} value={item.id} index={item.id} />
            <OperationInput table={"item__diving_course"} value={"delete"} index={item.id} />
          </Fragment>
        ))}
      <AnimatingDiv className={"flex flex-wrap gap-3 py-3"}>
        {Object.entries(divingLevelsz).map(([levelKey, level]) =>
          Object.entries(divingOrganizationszz).map(([orgKey, org]) =>
            selectedDivingCourses
              .filter((item) => item.diving_certificate_level_key === levelKey && item.diving_certificate_organization_key === orgKey)
              .map((item) => {
                const defaultProductDivingCourseId = defaultProductDivingCourses.find(
                  (defaultItem) => defaultItem.diving_course_id === item.id,
                )?.id;
                return (
                  <div
                    className="flex flex-row items-center space-x-1 whitespace-nowrap rounded bg-slate-100 p-1 px-2 text-sm"
                    key={item.id}
                  >
                    {org.name} - {item.name}
                    <button
                      type={"button"}
                      disabled={!isJsloaded}
                      onClick={() => {
                        setDivingCourseIds(removeFromArray(divinCourseIds, item.id));
                        refreshFormdata();
                      }}
                    >
                      <CgClose />
                    </button>
                    {defaultProductDivingCourseId && (
                      <RInput table={"item__diving_course"} field={"id"} value={defaultProductDivingCourseId} index={item.id} />
                    )}
                    <RInput table={"item__diving_course"} field={"data.diving_course_id"} index={item.id} value={item.id} type={"hidden"} />
                    <RInput
                      table={"item__diving_course"}
                      field={"data.item_id"}
                      index={item.id}
                      value={tableIdRef("item")}
                      type={"hidden"}
                    />
                  </div>
                );
              }),
          ),
        )}
      </AnimatingDiv>
    </div>
  );
};

export default function Page() {
  const response = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const product = response.product;
  const tags = product?.tags || [];
  const [tagIds, setTagIds] = useState<string[]>(tags.map((productTag) => productTag.tag_id));
  const removedTags = tags.filter((tag) => tag.id && !tagIds.includes(tag.tag_id));

  const productDivingLocations = product?.diving_locations || [];
  const [divingLocationIds, setDivingLocationIds] = useState(productDivingLocations.map((item) => item.diving_location_id));

  const productDiveSites = product?.diving_sites || [];
  const [divingSiteIds, setDivingSiteIds] = useState(productDiveSites.map((item) => item.diving_site_id));

  const activity = getActivity(response.activity_slug);

  const selectedDivingLocations = response.diving_locations.filter((item) => divingLocationIds.includes(item.id));
  const selectedDivingSites = response.diving_sites.filter((divingSite) => divingSiteIds.includes(divingSite.id));

  const removedDivingLocations = productDivingLocations.filter((item) => item.id && !divingLocationIds.includes(item.diving_location_id));
  const removedDivingSites = productDiveSites.filter((item) => item.id && !divingSiteIds.includes(item.diving_site_id));

  const is = (...activitySlugs: ActivitySlug[]) => {
    return activitySlugs.includes(response.activity_slug);
  };

  const productId = product?.id;
  const itemId = response.product?.item.id;

  const defaultdCurrency = response.establishment?.default_currency || defaultCurrency;
  const defaultPrices = response.product?.product_prices || [];
  const prices = defaultPrices.length
    ? defaultPrices
    : [
        {
          product_id: tableIdRef("product"),
          amount: 0,
          currency_id: defaultdCurrency,
        },
      ];
  const firstPrice = prices[0];

  return (
    <Fragment>
      <ActionForm replace onCheckEqual={defaultEqualCheck}>
        <RedirectInput value={_product_detail(tableIdRef("product"))} />

        {itemId && <RInput table={"item"} field={"id"} value={itemId} />}
        {productId && <RInput table={"product"} field={"id"} value={productId} type={"hidden"} />}
        <RInput table={"product"} field={"data.item_id"} value={tableIdRef("item")} type={"hidden"} />

        <RInput table={"item"} field={"data.form_root_id"} value={response.product?.item.form_root_id || ""} type={"hidden"} />
        <RInput table={"item"} field={"data.establishment_id"} value={response.establishment.establishment_id} type={"hidden"} />

        <RInput table={"item"} field={"data.activity_slug"} value={response.activity_slug || ""} type={"hidden"} />
        {/*<p>{response.establishment?.default_currency}</p>*/}
        <ProductPreview />
        {/*<ProductPreview />*/}
        <div className="app-container space-y-6">
          <ActionAlert scrollTo />
          <div className="flex flex-wrap gap-3 py-3">
            <ProductPriceForm productPrice={firstPrice} />
            {is("fun-diving", "diving-course") && (
              <label className="formcontrol flex flex-row items-center gap-2 pl-2">
                <span className="font-semibold">No. of dives:</span>
                <RInput
                  className="input-clean w-20"
                  required
                  type="number"
                  table={"product"}
                  field={"data.diving_count"}
                  defaultValue={response.product?.diving_count || ""}
                  min={0}
                  minLength={1}
                />
              </label>
            )}
            {is("snorkeling", "freediving") && (
              <label className="formcontrol flex flex-row items-center gap-2 pl-2">
                <span className="font-semibold">No. of sessions:</span>
                <RInput
                  className="input-clean w-20"
                  required
                  defaultValue={response.product?.n_sessions || ""}
                  type="number"
                  min={0}
                  minLength={1}
                  table={"product"}
                  field={"data.n_sessions"}
                />
              </label>
            )}
            {!activity.retail && (
              <FieldInputDuration name={fName("product", "data.duration_in_hours")} defaultValue={response.product?.duration_in_hours} />
            )}
            {is("fun-diving") && (
              <div className="flex w-fit flex-row items-center gap-3 rounded-md border border-slate-300 p-3">
                <span className="font-semibold">Type:</span>
                <RLabel table={"product"} field={"data.boat_dive"} className={"flex items-center gap-1 capitalize"}>
                  Boat
                  <RInput
                    className="checkbox"
                    defaultChecked={response.product?.boat_dive || false}
                    table={"product"}
                    field={"data.boat_dive"}
                    hiddenType={"__boolean__"}
                    type={"checkbox"}
                  />
                </RLabel>
                <RLabel table={"product"} field={"data.shore_dive"} className={"flex items-center gap-1 capitalize"}>
                  Shore
                  <RInput
                    className="checkbox"
                    defaultChecked={response.product?.shore_dive || false}
                    table={"product"}
                    field={"data.shore_dive"}
                    hiddenType={"__boolean__"}
                    type={"checkbox"}
                  />
                </RLabel>
                <RLabel table={"product"} field={"data.night_dive"} className={"flex items-center gap-1 capitalize"}>
                  Night
                  <RInput
                    className="checkbox"
                    defaultChecked={response.product?.night_dive || false}
                    table={"product"}
                    field={"data.night_dive"}
                    hiddenType={"__boolean__"}
                    type={"checkbox"}
                  />
                </RLabel>
              </div>
            )}
          </div>
          {is("diving-course") && (
            <div className="flex flex-col gap-3">
              <h3 className="text-xl font-bold text-slate-600">Diving courses</h3>
              <p>Add the diving course(s) for this product. Multiple diving course(s) is marked as a package.</p>
              <DivingCoursesSelectAdd />
            </div>
          )}
          {!activity.retail && (
            <div className="flex flex-col gap-3">
              <div className="flex flex-wrap items-center gap-3">
                <h3 className="text-xl font-bold text-slate-600">Dive location(s)</h3>
                <CDialog dialogname={"dive_location"} className="space-y-4">
                  <div className="flex flex-row justify-between items-center gap-3">
                    <p className="text-xl">Select location(s)</p>
                    <ParamLink
                      paramState={{ toggle_modal: undefined, modal_detail_name: undefined }}
                      aria-label="close"
                      className={"inline-block rounded-xl p-2 text-right hover:bg-slate-100 active:bg-slate-100"}
                    >
                      <XMarkIcon className="h-5 w-5" />
                    </ParamLink>
                  </div>
                  <div className="space-y-2 ">
                    {response.diving_locations.map((item) => (
                      <button
                        type={"button"}
                        key={item.id}
                        aria-selected={selectedDivingLocations.includes(item)}
                        onClick={() => {
                          setDivingLocationIds(toggleArray(divingLocationIds, item.id));
                          refreshFormdata();
                        }}
                        className="flex flex-row group items-center gap-3"
                      >
                        <Checker className="border border-slate-200" />
                        {item.name}
                      </button>
                    ))}
                  </div>
                  <div className="flex ">
                    <ParamLink className="btn btn-primary" paramState={{ toggle_modal: undefined }}>
                      OK
                    </ParamLink>
                  </div>
                </CDialog>
                <ParamLink
                  paramState={{ toggle_modal: "dive_location" }}
                  className="btn font-normal border border-slate-100 hover:border-slate-300
         rounded-md"
                >
                  add location
                </ParamLink>
              </div>
              <p>
                {is("diving-course")
                  ? "Add the optional dive locations for this course."
                  : "Add the dive location(s) and optional dive sites for this product."}
              </p>
              <div
                className={`rounded p-3 transition-all ${selectedDivingLocations.length > 0 ? "bg-secondary-50" : "bg-secondary-white"}`}
              >
                <div className="space-y-3">
                  <AnimatingDiv className="flex flex-col gap-3">
                    {removedDivingLocations.map((item) => (
                      <Fragment key={item.id}>
                        <RInput table={"product__diving_location"} field={"id"} id={item.id} index={item.id} />
                        <OperationInput table={"product__diving_location"} value={"delete"} index={item.id} />
                      </Fragment>
                    ))}
                    {removedDivingSites.map((item) => (
                      <Fragment key={item.id}>
                        <RInput table={"product__diving_site"} field={"id"} id={item.id} index={item.id} />
                        <OperationInput table={"product__diving_site"} value={"delete"} index={item.id} />
                      </Fragment>
                    ))}
                    {selectedDivingLocations.map((divingLocation) => {
                      const productDivingLocationId = productDivingLocations.find(
                        (item) => item.diving_location_id === divingLocation.id,
                      )?.id;
                      const diveSites = response.diving_sites.filter((item) => item.diving_location_id === divingLocation.id);
                      return (
                        <div key={divingLocation.id}>
                          <div className="flex w-fit flex-row items-center">
                            <span className="font-bold">{divingLocation.name}</span>
                            <button
                              type={"button"}
                              onClick={() => {
                                setDivingLocationIds(removeFromArray(divingLocationIds, divingLocation.id));
                                refreshFormdata();
                              }}
                            >
                              <CgClose />
                            </button>
                            {productDivingLocationId && (
                              <RInput
                                table={"product__diving_location"}
                                field={"id"}
                                index={divingLocation.id}
                                value={productDivingLocationId}
                              />
                            )}
                            <RInput
                              table={"product__diving_location"}
                              field={"data.diving_location_id"}
                              index={divingLocation.id}
                              value={divingLocation.id}
                              type={"hidden"}
                            />
                            <RInput
                              table={"product__diving_location"}
                              field={"data.product_id"}
                              index={divingLocation.id}
                              value={tableIdRef("product")}
                              type={"hidden"}
                            />
                          </div>
                          {!is("diving-course") && (
                            <div className="flex flex-wrap gap-4  pl-2 pt-2 pb-4">
                              {diveSites.map((diveSite) => {
                                const productDiveSiteId = productDiveSites.find((item) => item.diving_site_id === diveSite.id)?.id;
                                return (
                                  <Fragment key={diveSite.id}>
                                    <button
                                      type={"button"}
                                      className="flex items-center gap-1 group"
                                      aria-selected={selectedDivingSites.includes(diveSite)}
                                      onClick={() => {
                                        setDivingSiteIds(toggleArray(divingSiteIds, diveSite.id));
                                        refreshFormdata();
                                      }}
                                    >
                                      <Checker />
                                      {diveSite.name}
                                    </button>
                                    {selectedDivingSites.includes(diveSite) && (
                                      <Fragment>
                                        {productDiveSiteId && (
                                          <RInput
                                            table={"product__diving_site"}
                                            field={"id"}
                                            index={diveSite.id}
                                            value={productDiveSiteId}
                                          />
                                        )}
                                        <RInput
                                          table={"product__diving_site"}
                                          field={"data.diving_site_id"}
                                          index={diveSite.id}
                                          value={diveSite.id}
                                          type={"hidden"}
                                        />
                                        <RInput
                                          table={"product__diving_site"}
                                          field={"data.product_id"}
                                          index={diveSite.id}
                                          value={tableIdRef("product")}
                                          type={"hidden"}
                                        />
                                      </Fragment>
                                    )}
                                  </Fragment>
                                );
                              })}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </AnimatingDiv>
                </div>
              </div>
            </div>
          )}
          <h3 className="text-xl font-bold text-slate-500">Info</h3>
          <ProductTitle />
          <label className="formcontrol flex flex-row items-center pl-3">
            Subtitle
            <RInput
              table={"item"}
              field={"data.subtitle"}
              defaultValue={response.product?.subtitle || ""}
              className="w-full rounded-md border-none focus:ring-0"
              type="text"
            />
          </label>
          {/*<label className="formcontrol flex flex-row items-center pl-3">*/}
          {/*  Title*/}
          {/*  <input className="w-full rounded-md border-none focus:ring-0" type="text" {...inputProps("subtitle")} />*/}
          {/*</label>*/}
          {!activity.retail && (
            <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
              <div className="formcontrol flex flex-row items-center gap-1 whitespace-nowrap pl-3">
                <Tooltip description={"Enter the maximum number of participents per guide if applicable"}>
                  <InformationCircleIcon className="h-5 w-5" />
                </Tooltip>
                <label className="flex flex-row items-center">
                  Max. pax per guide
                  <RInput
                    className="w-full min-w-[30px] border-none bg-transparent focus:ring-0 "
                    type="number"
                    table={"product"}
                    field={"data.guide_pax_max"}
                    defaultValue={response.product?.guide_pax_max || ""}
                    onWheel={disableScrollOnNumberInput}
                    min={1}
                  />
                </label>
              </div>
              <label className="formcontrol lex-row flex items-center whitespace-nowrap pl-3">
                Min. pax to proceed
                <RInput
                  className="w-full rounded-md border-none focus:ring-0"
                  min={1}
                  type="number"
                  table={"product"}
                  field={"data.guide_pax_min"}
                  defaultValue={response.product?.guide_pax_min || ""}
                  onWheel={disableScrollOnNumberInput}
                />
              </label>
              <label className="formcontrol flex flex-row items-center whitespace-nowrap pl-3">
                Min. age
                <RInput
                  className="w-full rounded-md border-none focus:ring-0"
                  min={1}
                  type="number"
                  table={"item"}
                  field={"data.minimum_age"}
                  defaultValue={response.product?.minimum_age || ""}
                  onWheel={disableScrollOnNumberInput}
                />
              </label>
            </div>
          )}
          {!activity.retail && (
            <Fragment>
              <h3 className="pt-3 text-xl font-bold text-slate-500">Included</h3>
              <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                <div className="formcontrol overflow-hidden">
                  <RLabel table={"product"} field={"data.stay"} className={"flex w-fit flex-row items-center gap-2 p-3 pb-0"}>
                    Accomodation
                    <RInput
                      className="checkbox"
                      defaultChecked={response.product?.stay || false}
                      table={"product"}
                      field={"data.stay"}
                      hiddenType={"__boolean__"}
                      type={"checkbox"}
                    />
                  </RLabel>
                  <RInput
                    type="text"
                    className="w-full border-none p-3 pt-2 focus:ring-0"
                    table={"product"}
                    field={"data.stay_info"}
                    defaultValue={response.product?.stay_info || ""}
                    placeholder="optional info about the stay"
                  />
                </div>
                <div className="formcontrol overflow-hidden">
                  <RLabel table={"product"} field={"data.pickup"} className="flex w-fit flex-row items-center gap-2 p-3 pb-0">
                    Hotel/hostel pickup
                    <RInput
                      className="checkbox"
                      defaultChecked={response.product?.pickup || false}
                      table={"product"}
                      field={"data.pickup"}
                      hiddenType={"__boolean__"}
                      type={"checkbox"}
                    />
                  </RLabel>
                  <RInput
                    type="text"
                    className="w-full border-none p-3 pt-2 focus:ring-0"
                    table="product"
                    field="data.pickup_info"
                    defaultValue={response.product?.pickup_info || ""}
                    placeholder="e.g. Pickup within Amed & Tulamben area"
                  />
                </div>
                <div>
                  <RLabel
                    table={"product"}
                    field={"data.gear_included"}
                    className={"formcontrol flex w-fit flex-row items-center gap-2 p-3"}
                  >
                    Gear
                    <RInput
                      className="checkbox"
                      defaultChecked={response.product?.gear_included || false}
                      table={"product"}
                      field={"data.gear_included"}
                      hiddenType={"__boolean__"}
                      type={"checkbox"}
                    />
                  </RLabel>
                </div>
              </div>
            </Fragment>
          )}
          {is("fun-diving", "diving-course") && (
            <fieldset className="my-6 flex flex-col gap-6 py-8">
              <h3 className="text-xl font-bold text-slate-500">Required experience</h3>
              <div className="flex flex-wrap gap-6">
                <label className="rounded-md border border-slate-400 py-1 px-3 ring-slate-700 focus-within:ring-1">
                  Min. required certificate: &nbsp;
                  <RInput
                    type="text"
                    className="border-none py-2 px-0 focus:ring-0"
                    table="product"
                    field="data.required_diving_certificate"
                    defaultValue={response.product?.required_diving_certificate || ""}
                  />
                </label>
                <label className="flex flex-row items-center gap-2 rounded-md border border-slate-400 py-1 px-3 ring-slate-700 focus-within:ring-1">
                  <span>Min. logged dives</span>
                  <RInput
                    type="number"
                    min={0}
                    minLength={0}
                    className="border-none py-2 px-0 focus:ring-0"
                    table={"item"}
                    field={"data.diving_minimum_logged_dives"}
                    defaultValue={response.product?.diving_minimum_logged_dives || ""}
                    onWheel={disableScrollOnNumberInput}
                  />
                </label>
              </div>
              <label className="flex flex-col gap-1 rounded-md border border-slate-400 py-2 px-3 ring-slate-700 focus-within:ring-1">
                <p>Additional info:</p>
                <RTextarea
                  className="w-full border-none p-0 focus:ring-0"
                  placeholder="e.g. Logged dives are required for dive site Blue Corner because..."
                  table="product"
                  field="data.required_experience"
                  defaultValue={response.product?.required_experience || ""}
                />
              </label>
            </fieldset>
          )}
          <h3 className="text-xl font-bold text-slate-500">Further info</h3>
          {!activity.retail && (
            <label className="formcontrol flex flex-col gap-1 py-2 px-3">
              <p>Itinerary:</p>
              <RTextarea
                className="w-full border-none p-0 focus:ring-0"
                placeholder={`e.g.
Day 1
8:00 Pickup at your hotel.
8:30 Drive towards dive site
11:00 Instructions
11:30 First dive
13:00 Lunch at xx
14:00 2nd dive
15:30 return to hotel`}
                table="product"
                field="data.itinerary"
                defaultValue={response.product?.itinerary || ""}
              />
            </label>
          )}
          <label className="formcontrol flex flex-col gap-1 py-2 px-3">
            <p>Inclusions:</p>
            <RTextarea
              className="w-full border-none p-0 focus:ring-0"
              table="product"
              field="data.inclusions"
              defaultValue={response.product?.inclusions || ""}
            />
          </label>
          <label className="formcontrol flex flex-col gap-1 py-2 px-3">
            <p>Exclusions:</p>
            <RTextarea
              className="w-full border-none p-0 focus:ring-0"
              table="product"
              field="data.exclusions"
              defaultValue={response.product?.exclusions || ""}
            />
          </label>
          <label className="formcontrol flex flex-col gap-1 py-2 px-3">
            <p>Add-ons:</p>
            <RTextarea
              className="w-full border-none p-0 focus:ring-0"
              table="item"
              field="data.addons_description"
              defaultValue={response.product?.addons_description || ""}
            />
          </label>
          <AddonCheckbox
            addons={response.addons}
            defaultValue={response.product?.addon_ids || null}
            locale={response.establishment.locale}
            establishment_id={response.establishment.establishment_id}
          />
          <label className="formcontrol flex flex-col gap-1 py-2 px-3">
            <p>Info:</p>
            <RTextarea
              className="w-full border-none p-0 focus:ring-0"
              table="item"
              field="data.info"
              defaultValue={response.product?.info || ""}
            />
          </label>
          <div className="flex w-fit flex-row items-center gap-3 rounded-md border border-slate-300 p-3">
            <p>
              <span>Cancellation policy:</span>
            </p>
            <RSelect
              table={"item"}
              field={"data.cancellation_policy_in_hours"}
              className="border-none"
              defaultValue={response.product?.cancellation_policy_in_hours || ""}
            >
              <option value="">-</option>
              {[24, 48].map((hours) => (
                <option key={hours} value={hours + ""} className="flex items-center">
                  {hours}h
                </option>
              ))}
            </RSelect>
          </div>
          <div>
            <RLabel table={"product"} field={"data.published"} className="formcontrol flex w-fit flex-row items-center gap-2 p-3">
              Published
              <RInput
                className="checkbox"
                defaultChecked={response.product ? response.product.published : true}
                table={"product"}
                field={"data.published"}
                hiddenType={"__boolean__"}
                type={"checkbox"}
              />
            </RLabel>
          </div>
          <div>
            <label className="formcontrol flex flex-row items-center gap-2 pl-2">
              <span className="font-semibold">SKU:</span>
              <RInput
                table="product"
                field="data.external_identifier"
                className="input-clean"
                placeholder="SKU"
                defaultValue={response.product?.external_identifier || ""}
              />
            </label>
          </div>
          <div className="space-y-3">
            <div className="flex flex-wrap gap-3 items-center">
              <RLabel table={"item"} field={"data.tag_id"}>
                Tag
              </RLabel>
              <ParamLink className="link" paramState={{ modal_detail_name: createTagDialogKey, toggle_modal: "tag" }}>
                Create
              </ParamLink>
            </div>
            <div className="flex flex-row gap-3">
              {/*<RSelect table={"item"} field={"data.tag_id"} className={"select"} defaultValue={response.product?.tag_id || ""}>*/}
              {/*  <option value="">Main Tag (category)</option>*/}
              {/*  {response.establishment.tags.map((tag) => (*/}
              {/*    <option key={tag.id} value={tag.id}>*/}
              {/*      {tag.name}*/}
              {/*    </option>*/}
              {/*  ))}*/}
              {/*</RSelect>*/}
              {removedTags.map((productTag) => (
                <Fragment key={productTag.id}>
                  <RInput table={"product__tag"} field={"id"} index={productTag.id} value={productTag.id} />
                  <OperationInput table={"product__tag"} value={"delete"} index={productTag.id} />
                </Fragment>
              ))}
              <ParamLink
                paramState={{ toggle_modal: "tag", modal_detail_name: undefined }}
                replace
                className="flex flex-wrap gap-3 p-2 border border-slate-200 group rounded-md items-center hover:border-slate-300 hover:text-slate-900 transition-all hover:opacity-80"
              >
                {response.establishment.tags
                  .filter((tag) => tagIds.includes(tag.id))
                  .map((tag) => {
                    const productTagId = tags?.find((productTag) => productTag.tag_id === tag.id)?.id;
                    return (
                      <span className="bg-slate-300 rounded-md p-1 px-3" key={tag.id}>
                        {tag.name}
                        {productTagId && <RInput table={"product__tag"} field={"id"} index={tag.id} value={productTagId} />}
                        <RInput type={"hidden"} table={"product__tag"} field={"data.tag_id"} index={tag.id} value={tag.id} />
                        <RInput
                          type={"hidden"}
                          table={"product__tag"}
                          field={"data.product_id"}
                          index={tag.id}
                          value={tableIdRef("product")}
                        />
                      </span>
                    );
                  })}
                <span>Select Tags...</span>
              </ParamLink>
            </div>
          </div>
          <div className="space-y-3">
            <p className="text-xl">Images</p>
            <InputFilesDefault defaultValue={product?.files} target_id={tableIdRef("product")} target={"product"} />
          </div>
        </div>
      </ActionForm>
      <CDialog dialogname={"tag"} className="space-y-6">
        <div className="flex justify-between items-center gap-3">
          {search.state.modal_detail_name === createTagDialogKey ? (
            <p className="text-lg">Create tag</p>
          ) : (
            <p className="text-lg">Select tag(s)</p>
          )}
          <ParamLink paramState={{ toggle_modal: undefined }} replace>
            <CgClose />
          </ParamLink>
        </div>
        <div>
          {search.state.modal_detail_name === createTagDialogKey || !response.establishment.tags ? (
            <CreateTagForm establishment_id={response.establishment.establishment_id} />
          ) : (
            <ActionForm
              className="space-y-3"
              onSubmit={(e) => {
                e.preventDefault();
                const formdata = new FormData(e.currentTarget);
                const tags = Array.from(formdata.values()) as string[];
                setTagIds(tags);
                setState(
                  (e) => ({
                    toggle_modal: undefined,
                    refresh_formdata: !e.refresh_formdata,
                  }),
                  { replaceRoute: true },
                );
              }}
            >
              <div className="space-y-1">
                {response.establishment.tags.map((tag) => (
                  <label key={tag.name} className="flex flex-wrap gap-2 items-center">
                    <input
                      className="checkbox"
                      name={"tag"}
                      type={"checkbox"}
                      value={tag.id}
                      defaultChecked={!!tagIds.find((selectedTag) => selectedTag === tag.id)}
                    />
                    {tag.name}
                  </label>
                ))}
              </div>
              <SubmitButton className="btn btn-primary w-full ">OK</SubmitButton>
            </ActionForm>
          )}
        </div>
      </CDialog>
    </Fragment>
  );
}
