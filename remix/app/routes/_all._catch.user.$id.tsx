import { kysely } from "~/misc/database.server";
import { useLoaderD<PERSON>, useNavigation } from "@remix-run/react";
import { getEstablishmentShort } from "~/domain/establishment/helpers";
import { getSessionSimple } from "~/utils/session.server";
import { _establishment_detail, _user } from "~/misc/paths";
import { Button, DefaultSaveInner, SubmitButton } from "~/components/base/Button";
import { RInput } from "~/components/ResourceInputs";
import { RedirectInput } from "~/components/form/DefaultInput";
import { notFoundOrUnauthorzied } from "~/misc/responses";
import { isEditorQb } from "~/domain/member/member-queries.server";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { LoaderFunctionArgs } from "@remix-run/router";
import { ActionForm } from "~/components/form/BaseFrom";
import { ParamLink } from "~/components/meta/CustomComponents";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const id = params.id!;
  const ctx = await getSessionSimple(request);
  const me = await isEditorQb({ trx: kysely, ctx: ctx })
    .select((eb) => [
      "_user.admin",
      jsonObjectFrom(
        kysely
          .selectFrom("user")
          .where("user.id", "=", id)
          .where("deleted_at", "=", at_infinity_value)
          .select(["user.id", "user.email", "user.editor", "user.deleted_at"])
          .select((eb) =>
            jsonArrayFrom(
              kysely
                .selectFrom("member")
                .innerJoin("establishment", "establishment.id", "member.establishment_id")
                .innerJoin("operator", "operator.id", "establishment.operator_id")
                .leftJoin("spot", "spot.id", "establishment.spot_id")
                .where("member.user_id", "=", eb.ref("user.id"))
                .where("member.owner", "=", true)
                .select([
                  "member.id",
                  "member.user_id",
                  "establishment_id as establishment_id",
                  "establishment.spot_id",
                  "spot.name as spot_name",
                  "establishment.location_name as establishment_name",
                  "operator.name as operator_name",
                ]),
            ).as("ownings"),
          ),
      ).as("user"),
    ])
    .executeTakeFirst();
  const user = me?.user;
  if (!me || !user) throw notFoundOrUnauthorzied();
  return { ...me, user: user };
};

export default function Page() {
  const response = useLoaderData<typeof loader>();
  const user = response.user;

  const navigation = useNavigation();

  return (
    <div className="app-container space-y-3 py-6">
      <ParamLink className="link" path={_user}>
        users
      </ParamLink>
      <h1 className="text-2xl font-bold">
        User: {user.email} {user.deleted_at !== at_infinity_value && "(deleted)"}
      </h1>
      <h2 className="text-xl font-bold">Owns</h2>
      <div className="flex flex-wrap gap-3 pb-3">
        {user.ownings.map((owning) => (
          <ParamLink
            path={_establishment_detail(owning.establishment_id)}
            className="rounded-md bg-slate-50 p-2 hover:bg-slate-100 hover:underline"
            key={owning.id}
          >
            {getEstablishmentShort(owning)}
          </ParamLink>
        ))}
      </div>
      <ActionForm className="group flex flex-wrap gap-3 items-center p-2 rounded-md border border-primary w-fit">
        <RInput table={"user"} field={"id"} value={user.id} />
        <label className="gap-2 flex items-center">
          Editor
          <RInput
            defaultChecked={user.editor}
            className="checkbox"
            hiddenType={"__boolean__"}
            table={"user"}
            field={"data.editor"}
            type={"checkbox"}
          />
        </label>
        <SubmitButton className={"btn btn-primary group-data-success:bg-slate-200"}>
          <DefaultSaveInner />
        </SubmitButton>
      </ActionForm>
      <ActionForm identifier={"delete"} confirmMessage={`User ${user.email} will be deleted, are you sure?`}>
        <RedirectInput value={_user} />
        <RInput table={"user"} field={"id"} value={user.id} />
        <RInput table={"user"} field={"data.deleted_at"} value={"yes"} type={"hidden"} />
        <Button loading={!!navigation.formData} className="btn btn-red">
          Delete
        </Button>
      </ActionForm>
    </div>
  );
}
