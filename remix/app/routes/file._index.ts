import type { SerializeFrom } from "@remix-run/server-runtime";
import { getAdminStorageSingapore } from "~/utils/firebase.server";
import { notFoundOrUnauthorzied } from "~/misc/responses";
import { kysely } from "~/misc/database.server";
import { getOrCreateSession } from "~/utils/session.server";
import { userSessionId } from "~/domain/member/member-queries.server";
import { v4 } from "uuid";
import { nowValue } from "~/kysely/kysely-helpers";
import { z } from "zod";
import { ActionFunctionArgs } from "@remix-run/node";
import { data } from "@remix-run/router";

const minutes10InMillis = 10 * 60 * 1000;

const fileParser = z.object({ file: z.string(), public: z.boolean().default(false), description: z.string().optional() });

export const action = async (args: ActionFunctionArgs) => {
  const { session_id, init } = await getOrCreateSession(args.request);
  const body = await args.request.json();
  const input = z.object({ files: fileParser.array() }).parse(body);
  if (input.files.length == 0) return data({ files: [] }, init);

  const uploadId = v4();

  const storage = await getAdminStorageSingapore();
  const bucket = storage.bucket();

  const result = await kysely.transaction().execute(async (trx) => {
    const filePromises = input.files.map(async (file) => {
      try {
        const fileholder = await trx
          .insertInto("file")
          .values({
            id: v4(),
            filename: uploadId + "/" + file.file,
            public: file.public,
            description: file.description,
            created_at: nowValue,
            created_by_session_id: session_id,
            created_by_user_session_id: userSessionId({ trx: trx, ctx: { session_id: session_id } }),
          })
          .returningAll()
          .executeTakeFirstOrThrow();

        const fileRef = bucket.file(fileholder.filename);

        const url = await fileRef.getSignedUrl({
          action: "write",
          version: "v4",
          expires: Date.now() + minutes10InMillis,
        });
        const finalUrl = url && url[0];
        if (!finalUrl) throw notFoundOrUnauthorzied("could not create signed write url");

        return {
          ...fileholder,
          writeurl: finalUrl,
        };
      } catch (e) {
        console.error(e);
        throw e;
      }
    });
    return Promise.all(filePromises);
  });
  return data({ files: result }, init);
};

export type FilesAction = SerializeFrom<typeof action>;
