import type { AsyncReturnType } from "type-fest";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import React, { useId } from "react";
import { getUnit } from "~/domain/addon/addon";
import { getSessionSimple } from "~/utils/session.server";
import { unauthorized } from "~/misc/responses";
import { memberIsAdminQb } from "~/domain/member/member-queries.server";
import { ActionForm, defaultEqualCheck } from "~/components/form/BaseFrom";
import { useAppContext } from "~/hooks/use-app-context";
import { LoaderFunctionArgs } from "@remix-run/router";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { AddonForm } from "~/domain/addon/addon-components";
import { CheckIcon } from "@heroicons/react/20/solid";
import { formatMoney } from "~/utils/money";
import { RInput } from "~/components/ResourceInputs";
import { defaultCurrency, defaultLocale } from "~/misc/vars";
import { notNull } from "~/kysely/kysely-helpers";
import { jsonObjectFrom } from "kysely/helpers/postgres";
import { ParamLink } from "~/components/meta/CustomComponents";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);
  const establishmentId = params.id!;
  const establishment = await memberIsAdminQb({ trx: kysely, ctx: { session_id: session_id } }, "read")
    .innerJoin("establishment", "establishment.id", "_member.establishment_id")
    .where("_member.establishment_id", "=", establishmentId)
    .select(["_member.id", "establishment.default_currency", "establishment.id", "establishment.locale"])
    .executeTakeFirst();

  if (!establishment) {
    throw unauthorized("Not found or unauthorized, You need to be manager for this page");
  }

  return {
    establishment: establishment,
    addons: await kysely
      .selectFrom("addon")
      .where("establishment_id", "=", establishmentId)
      .selectAll("addon")
      .select((eb) =>
        notNull(
          jsonObjectFrom(eb.selectFrom("price").select(["price.amount", "price.currency_id"]).whereRef("price.id", "=", "addon.price_id")),
        ).as("price"),
      )
      .execute(),
  };
};

type LoaderResponse = AsyncReturnType<typeof loader>;

export default function Page() {
  const context = useAppContext();
  const search = useSearchParams2();
  const response = useLoaderData<LoaderResponse>();
  const elementId = useId();

  return (
    <div className="app-container space-y-3">
      <h2 className="text-xl font-bold">Add-ons</h2>
      <div className="p-3 rounded-md border-primary border">
        <h2 className="text-md font-bold">Create</h2>
        <ActionForm key={search.state.rerender} onCheckEqual={defaultEqualCheck} className="space-y-3">
          <RedirectParamsInput
            path={"./"}
            paramState={{
              element_action: [elementId],
              element_clear: [elementId],
              rerender: search.state.rerender + 1,
            }}
          />
          <RInput table={"addon"} field={"data.establishment_id"} value={response.establishment.id} type={"hidden"} />
          <AddonForm addon={{ price: { currency_id: response.establishment.default_currency || defaultCurrency } }} />
        </ActionForm>
      </div>
      <h3 className="text-md font-bold">List ({response.addons.length})</h3>
      <div className="overflow-auto">
        <table className="[&_td]:py-1 [&_td]:pr-2">
          <thead>
            <tr>
              <td>Add-on name</td>
              <td>Price</td>
              <td>Basis</td>
              <td align={"center"}>Default Quantity</td>
              <td align={"center"}>Allow Participant Choice</td>
            </tr>
          </thead>
          <tbody>
            {response.addons.map((addon) => (
              <tr key={addon.id}>
                <td>
                  <ParamLink className="link" path={addon.id}>
                    {addon.name}
                  </ParamLink>
                </td>
                <td>
                  {formatMoney(context, {
                    nativeAmount: addon.price.amount,
                    nativeCurrency: addon.price.currency_id,
                    toCurrency: addon.price.currency_id,
                    locale: response.establishment?.locale || defaultLocale,
                  })}
                </td>
                <td>{getUnit(addon.unit)?.select_label}</td>
                <td align={"center"}>{addon.quantity}</td>
                <td align={"center"}>{addon.allow_change && <CheckIcon className="w-5 h-5" />}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
