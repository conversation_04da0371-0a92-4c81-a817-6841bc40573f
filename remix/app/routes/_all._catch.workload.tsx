import { kysely } from "~/misc/database.server";
import { useLoaderData } from "@remix-run/react";
import { ParamLink } from "~/components/meta/CustomComponents";
import { getSessionSimple } from "~/utils/session.server";
import { getDateFromParams } from "~/domain/planning/planning.helpers.server";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { activeActivitySlugs, activities, ActivitySlug } from "~/domain/activity/activity";
import { twMerge } from "tailwind-merge";
import { _workload_detail } from "~/misc/paths";
import { uniqueBy } from "remeda";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { memberIsAdminOrOwnerQb } from "~/domain/member/member-queries.server";
import { getMonthObj } from "~/domain/planning/plannings-helpers";
import { MonthSwitch } from "~/domain/meta/datetime";
import { workloadAssignmentsQb } from "~/domain/workload/queries.server";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { notFoundOrUnauthorzied } from "~/misc/responses";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { LoaderFunctionArgs } from "@remix-run/router";
import { keys } from "~/misc/helpers";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const session = await getSessionSimple(request);
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);
  const date = getDateFromParams(request, true);
  const month = getMonthObj(date.dateParam);

  const establishment = await simpleEstablishmentQb
    .where("establishment.id", "=", state.persist_establishment_id)
    .where("establishment.id", "in", memberIsAdminOrOwnerQb({ ctx: session, trx: kysely }, "read").select("_member.establishment_id"))
    .select((eb) => [
      jsonArrayFrom(
        eb
          .selectFrom("member")
          .where("member.establishment_id", "=", eb.ref("establishment.id"))
          .select((eb) => [
            "member.id",
            "member.name",
            jsonArrayFrom(
              workloadAssignmentsQb
                .where("trip_assignment.member_id", "=", eb.ref("member.id"))
                .where("trip.date", ">=", month.firstDate)
                .where("trip.date", "<=", month.lastDate),
            ).as("assignments"),
          ]),
      ).as("members"),
    ])
    .executeTakeFirst();

  if (!establishment) throw notFoundOrUnauthorzied();

  return establishment;
};

const activityTotalKeys = keys(activities).filter((activityKey) => activities[activityKey].totals_name);
const dividerRow = (
  <tr>
    <td colSpan={1 + activityTotalKeys.length} className="py-2">
      <hr />
    </td>
  </tr>
);

export default function Page() {
  const data = useLoaderData<typeof loader>();
  console.log("daa", data);

  const totals: Partial<Record<ActivitySlug, number>> = {};
  return (
    <div className="app-container space-y-6">
      <p className="text-xl font-semibold text-slate-600">{getEstablishmentName(data)}</p>
      <h1 className="text-xl font-semibold">Workload</h1>
      <MonthSwitch />
      <div className="overflow-auto">
        <table className="w-full">
          <thead className="font-semibold">
            <tr>
              <td className="py-2">Name</td>
              {activityTotalKeys.map((slug) => (
                <td className="p-2" key={slug}>
                  {activities[slug].totals_name}
                </td>
              ))}
            </tr>
          </thead>
          <tbody>
            {dividerRow}
            {data.members
              .filter((member) => member.assignments.length > 0)
              .map((member) => {
                return (
                  <tr key={member.id}>
                    <td className="py-1">
                      <ParamLink className="link" path={_workload_detail(member.id)}>
                        {member.name}
                      </ParamLink>
                    </td>
                    {activityTotalKeys.map((slug) => {
                      const assignmentsFilterBySlug = member.assignments.filter((assignment) => assignment.activity_slug === slug);
                      const finalAssignments = uniqueBy(assignmentsFilterBySlug, (member) => member.trip_id);
                      const total =
                        slug === "diving-course"
                          ? uniqueBy(
                              finalAssignments.filter((assignment) => assignment.started_this_month),
                              (assignment) => assignment.booking_id,
                            ).length
                          : finalAssignments.length;

                      if (!totals[slug]) {
                        totals[slug] = 0;
                      }
                      totals[slug]! += total;

                      return (
                        <td className={twMerge("px-2 py-1", total ? "font-semibold" : "text-slate-300")} key={slug}>
                          {total}
                        </td>
                      );
                    })}
                  </tr>
                );
              })}
            {dividerRow}
            <tr className="font-semibold">
              <td>Total</td>
              {activeActivitySlugs.map((slug) => {
                const total = totals[slug] || 0;
                return (
                  <td className={twMerge("px-2 py-2", total ? "font-semibold" : "text-slate-300")} key={slug}>
                    {total}
                  </td>
                );
              })}
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}
