import React, { Fragment, useCallback, useState } from "react";
import { Form, useLoaderData, useNavigation } from "@remix-run/react";

import { AnimatingDiv, BlueBox, SectionHeading } from "~/components/base/base";
import { getSessionSimple } from "~/utils/session.server";
import {
  baseProductQb,
  divingCoursesJsonEb,
  divingLevelsJsonEb,
  divingLocationsJsonEb,
  orderProduct,
  pricesJsonEb,
  productFilterPerKey,
  validProductIds,
} from "~/domain/product/product-queries.server";
import { kysely } from "~/misc/database.server";
import { arrayAgg, stAsGeoJsonPoint, unnestArray } from "~/kysely/kysely-helpers";
import { capitalize, strictWhatsapp } from "~/utils/formatters";
import { fName, keys } from "~/misc/helpers";
import { languages } from "~/data/languages";
import { _product_detail, _product_mutate, _review_create_relative, _review_detail } from "~/misc/paths";
import { ProductItem } from "~/domain/product/ProductItem";
import { CgChevronDown } from "react-icons/cg";
import { t } from "~/misc/trans";
import { ReadmoreBox } from "~/components/ReadmoreBox";
import { BlueTag } from "~/components/base/Tag";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { ImageGallery } from "~/components/field/ImageGallery";
import { FeedbackChipCounts, FeedbackSingleChip } from "~/domain/review/review-components";
import { ratingsToAvg, stars, starsBackwards, StarsOrangeSmall, StarsWithText } from "~/components/field/rating/rating-input";
import { reviewQb, reviewsRatingQb } from "~/domain/review/review-queries";
import { NEGATIVES, POSITIVES } from "~/domain/review/review-data";
import { IoLocationSharp } from "react-icons/io5";
import { activities, ActivitySlug, activitySlugs, getFilters } from "~/domain/activity/activity";
import { ContactMetaKey, contactMetas, getContactMeta } from "~/domain/establishment/EstablishmentContact";
import { bbox, feature, featureCollection } from "@turf/turf";
import type mapboxgl from "mapbox-gl";
import buffer from "@turf/buffer";
import { useMap } from "~/hooks/use-map-context";
import { twMerge } from "tailwind-merge";
import { MapPortal } from "~/components/MapPortal";
import { MapMarker } from "~/components/map/Marker";
import { ParamLink } from "~/components/meta/CustomComponents";
import { postEvent } from "~/domain/event/event-fetcher";
import { Button } from "~/components/base/Button";
import { IkImage } from "~/components/IkImage";
import { RInput, RLabel, RTextarea } from "~/components/ResourceInputs";
import { PolicyLink, TermsLink } from "~/components/shared";
import { useWindowLocation } from "~/utils/remix";
import { UserRegisterFieldsForInquery } from "~/domain/user/UserRegisterFields";
import { createPageOverwrites } from "~/misc/consts";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { useAppContext } from "~/hooks/use-app-context";
import { LoaderFunctionArgs } from "@remix-run/router";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { paramsToRecord, removeFromArray, toggleArray } from "~/misc/parsers/global-state-parsers";
import { ChevronUpIcon } from "@heroicons/react/20/solid";
import { CDialog, DialogCloseButton } from "~/components/base/Dialog";
import { RedirectParamsInput } from "~/components/form/DefaultInput";

export { action } from "~/routes/_all._catch.resource";

const datetimeFormat = new Intl.DateTimeFormat("en-US");

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);
  const ctx = await getSessionSimple(request);
  const activitySlug = activitySlugs.find((type) => type === state.activity_slug);

  const establishment_id = params.id!;

  const activeFilters = getFilters(activitySlug);

  const args = { trx: kysely, ctx: ctx };

  const baseFilteredProductQb = baseProductQb.where("product.deleted_at", "=", at_infinity_value);

  const filteredProductQb = activeFilters
    .filter((key) => url.searchParams.getAll(key).length > 0)
    .reduce((accumulatedQueryBuilder, key) => {
      const filterValue = url.searchParams.getAll(key);
      return productFilterPerKey[key](accumulatedQueryBuilder, filterValue);
    }, baseFilteredProductQb)
    .where("item.activity_slug", "=", (activitySlug as ActivitySlug) || "none");

  const productQb = baseFilteredProductQb
    .where("item.establishment_id", "=", establishment_id)
    .selectAll(["item", "product"])
    .select((eb) => [
      jsonArrayFrom(fileTargetsQb(kysely, "product", eb.ref("product.id"))).as("files"),
      eb
        .and([eb("product.id", "in", validProductIds), eb("item.activity_slug", "in", activitySlugs), eb("product.published", "=", true)])
        .as("is_valid"),
      divingCoursesJsonEb,
      divingLocationsJsonEb,
      divingLevelsJsonEb,
      pricesJsonEb,
    ]);

  const olBuilder = kysely
    .selectFrom("establishment")
    .innerJoin("operator", "operator.id", "establishment.operator_id")
    .leftJoin("spot", "spot.id", "establishment.spot_id")
    .leftJoin("region", "region.id", "spot.region_id")
    .selectAll("operator")
    .selectAll("establishment")
    .select((eb) => {
      const reviewsQb = reviewQb(kysely)
        .whereRef("review.establishment_id", "=", eb.ref("establishment.id"))
        .orderBy("review.created_at", "desc");

      return [
        "operator.name as operator_name",
        "establishment.id as establishment_id",
        "establishment.location_name as establishment_name",
        "spot.name as spot_name",
        reviewsRatingQb.where("review.establishment_id", "=", eb.ref("establishment.id")).as("ratings"),
        eb
          .selectFrom("establishment__language")
          .where("establishment__language.establishment_id", "=", eb.ref("establishment.id"))
          .select((eb) => arrayAgg(eb.ref("establishment__language.language_code")).as("languages_codes"))
          .as("language_codes"),
        filteredProductQb
          .where("item.establishment_id", "=", eb.ref("establishment.id"))
          .select((eb) => arrayAgg(eb.ref("product.id"), "uuid").as("product_ids"))
          .as("filtered_product_ids"),
        jsonArrayFrom(reviewsQb).as("reviews"),
        stAsGeoJsonPoint(eb.ref("establishment.geom")).as("geom"),
        jsonArrayFrom(fileTargetsQb(kysely, "establishment", eb.ref("establishment.id"))).as("files"),
        jsonArrayFrom(
          eb
            .selectFrom(
              unnestArray(
                activitySlugs.filter((slug) => !activities[slug].retail),
                "activity",
              ),
            )
            .select((eb) => [
              "activity.key as activity_slug",
              jsonArrayFrom(productQb.where("item.activity_slug", "=", eb.ref("activity.key")).$call((eb) => orderProduct(eb))).as(
                "products",
              ),
            ]),
        ).as("product_groups"),
      ];
    })
    .where("establishment.id", "=", establishment_id);

  const olResult = await olBuilder.executeTakeFirst();

  if (!olResult) throw new Response("This operator does not exist or is not yet published on our platform", { status: 404 });

  return {
    ...olResult,
    ...createPageOverwrites({ show_currency_swap: true, show_whatsapp: true, customer_toggle: true }),
    activity_slug: activitySlug,
    title: capitalize(olResult.operator_name + " - " + olResult.spot_name),
  };
};

const buttons = keys(contactMetas);

const ContactDialog = () => {
  const context = useAppContext();
  const { state } = useSearchParams2();
  const location = useWindowLocation();
  const item = useLoaderData<typeof loader>();

  const navigation = useNavigation();
  const finalShortId = item.id;
  const shortUrl = location.origin + "/s/" + finalShortId;

  const whatsappNr = item.whatsapp ? strictWhatsapp(item.whatsapp) : "";

  const contactMeta = getContactMeta(state.modal_detail_name);

  return (
    <CDialog
      dialogname={"contact"}
      className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all"
    >
      <div className={"flex flex-row items-center justify-between"}>
        <h3 className="text-lg font-medium leading-6 text-gray-900">Contact {item.title}</h3>
        <DialogCloseButton />
      </div>
      <div className={"flex flex-col mt-2 space-y-3 pt-3"}>
        <div>
          <IkImage path={item.files?.[0]?.filename} w={540} h={150} className="h-[150px] w-full rounded-md object-cover" />
        </div>
        <div className={"pt-1"}>
          {contactMeta ? (
            <Form
              method="POST"
              onSubmit={(e) => {
                if (contactMeta?.key) {
                  const formdata = new FormData(e.currentTarget);
                  const email = context.email || formdata.get(fName("user", "data.email"));
                  const displayName = context.display_name || formdata.get(fName("user_session", "data.display_name"));
                  const question = formdata.get(fName("inquiry", "data.question"));

                  /****** Whatsapp template *******/
                  const whatsappMsg = `Inquiry through:
${shortUrl},

Name: ${displayName}
Email: ${email}

${question || ""}`;
                  const whatsappLink = encodeURI(`https://api.whatsapp.com/send?phone=${whatsappNr}&text=${whatsappMsg}`);

                  /****** End Whatsapp template *******/

                  /****** Start Email template *******/
                  const emailMsg = `${question || ""}

**Details via traveltruster**
${shortUrl}
Name: ${displayName}
Email: ${email}`;
                  const emailSubject = `Inquiry via traveltruster`;
                  const emailLink = `mailto:${item.email}?subject=${emailSubject}&body=${encodeURIComponent(emailMsg)}`;
                  /****** End email template *******/

                  const link = contactMeta.key === "whatsapp" ? whatsappLink : emailLink;
                  window.open(link)?.focus();
                }
              }}
            >
              <RedirectParamsInput path={"./"} paramState={{ modal_detail_name: undefined }} />
              <RInput table={"inquiry"} field={"data.communication_method"} value={contactMeta?.key || ""} type={"hidden"} />
              <RInput table={"inquiry"} field={"data.establishment_id"} value={item.establishment_id || ""} type={"hidden"} />
              <div className="space-y-5">
                <div>
                  <RLabel table={"inquiry"} field={"data.question"}>
                    Ask your questions here
                  </RLabel>
                  <br />
                  <RTextarea disabled={!!navigation.formData} className="input" table={"inquiry"} field={"data.question"} />
                </div>
                <UserRegisterFieldsForInquery />
                <p className="text-xs text-slate-600">
                  By sending this inquiry, you agree with our <TermsLink /> and <PolicyLink />
                </p>
                <hr />
                {contactMeta && (
                  <div className="space-y-2">
                    <Button loading={!!navigation.formData} className="btn btn-primary w-full">
                      <contactMeta.value.Icon className="h-7 w-7" />
                    </Button>
                    <p className="text-center italic text-slate-800">Send inquiry via {contactMeta.value.label}</p>
                  </div>
                )}
              </div>
            </Form>
          ) : (
            <div>Thanks for contacting!</div>
          )}
        </div>
      </div>
    </CDialog>
  );
};

const OperatorContact = () => {
  const item = useLoaderData<typeof loader>();

  const operatorName = item.location_name ? item.operator_name + " - " + item.location_name : item.operator_name;
  const features = item.geom ? [feature(item.geom, { name: operatorName })] : [];

  const collection = featureCollection(features);

  // usePointsLayer(collection);

  const fitBound = useCallback(
    (map: mapboxgl.Map) => {
      if (features.length > 0) {
        const bufferedCollection = buffer(collection, 1, {
          units: "kilometers",
        });
        const [minLng, minLat, maxLng, maxLat] = bbox(bufferedCollection);
        map.fitBounds(
          [
            [minLng, minLat],
            [maxLng, maxLat],
          ],
          { padding: 40, duration: 20 },
        );
      }
    },
    [collection, features.length],
  );

  useMap({
    static: true,
    callback: fitBound,
  });

  const filteredContactItems = buttons.filter((btnKey) => !!item[btnKey]);

  const getSharedProps = (key: ContactMetaKey) => ({
    className: "btn btn-secondary w-full",
    onClick: () => {
      postEvent({
        tag: key,
        type: "click",
        url: window.location.href,
      });
    },
  });

  return (
    <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
      <div>
        <div
          className={twMerge(
            "grid gap-3",
            filteredContactItems.length < 2 ? "grid-cols-1 md:grid-cols-1" : "md:grid-cols-2",
            filteredContactItems.length === 2 && "grid-cols-2",
            filteredContactItems.length === 3 && "grid-cols-3",
            filteredContactItems.length > 3 && "grid-cols-4",
          )}
        >
          {!!item.whatsapp && (
            <div>
              <ParamLink
                {...getSharedProps("whatsapp")}
                paramState={{ toggle_modal: "contact", modal_detail_name: "whatsapp" satisfies ContactMetaKey }}
              >
                <contactMetas.whatsapp.Icon className="h-7 w-7" />
                <span className="max-md:hidden">{contactMetas.whatsapp.label}</span>
              </ParamLink>
            </div>
          )}
          {!!item.telephone && (
            <div>
              <a {...getSharedProps("telephone")} href={`tel:${item.telephone}`}>
                <contactMetas.telephone.Icon className="h-7 w-7" />
                <span className="max-md:hidden">{contactMetas.telephone.label}</span>
              </a>
            </div>
          )}
          {!!item.email && (
            <div>
              <ParamLink
                {...getSharedProps("email")}
                paramState={{ toggle_modal: "contact", modal_detail_name: "email" satisfies ContactMetaKey }}
              >
                <contactMetas.email.Icon className="h-7 w-7" />
                <span className="max-md:hidden">{contactMetas.email.label}</span>
              </ParamLink>
            </div>
          )}
          {!!item.website && (
            <div>
              <a {...getSharedProps("website")} href={item.website} target={"_blank"}>
                <contactMetas.website.Icon className="h-7 w-7" />
                <span className="max-md:hidden">{contactMetas.website.label}</span>
              </a>
            </div>
          )}
        </div>
        <div className="hidden whitespace-pre-wrap pt-3 text-center md:block">{item.address}</div>
      </div>
      <div className="flex flex-col">
        <div className="flex-1 overflow-hidden rounded-md bg-gray-500">
          <div
            style={{
              position: "relative",
              height: "100%",
              minHeight: "150px",
              width: "100%",
            }}
          >
            <MapPortal>
              {item.geom && (
                <MapMarker coordinates={item.geom.coordinates} className="relative">
                  <div className="absolute -top-2 -left-2 h-4 w-4 rounded-full bg-primary" />
                  <p className="absolute top-2 -translate-x-1/2 whitespace-nowrap font-bold text-orange-500">{item.operator_name}</p>
                </MapMarker>
              )}
            </MapPortal>
          </div>
        </div>
        <div className="whitespace-pre-wrap pt-3 text-center md:hidden">{item.address}</div>
      </div>
    </div>
  );
};

export default function Page() {
  const app = useAppContext();
  const search = useSearchParams2();
  const response = useLoaderData<typeof loader>();
  const language_codes = response.language_codes || [];
  const product_groups = response.product_groups;
  const [expandedIds, setExpandedIds] = useState<string[]>(response.activity_slug ? [response.activity_slug] : []);

  const finalReviews = response.reviews.filter((review) => {
    if (app?.editor || review.published_at || review.verified_at) return true;
    return review.user_session_id === app.user_session_id;
  });

  const ratings = finalReviews.map((review) => review.experience_rating);
  const avgStars = ratingsToAvg(ratings);

  const member = app.members.find((member) => member.establishment_id === response.id);
  const isEditorOrOwner = app.editor || !!member?.owner;
  const isManager = (member?.admin || 0) > 1;

  const filteredProductIds = response.filtered_product_ids || [];
  const finalProductGroups = product_groups
    .map((productGroup) => ({
      activity_slug: productGroup.activity_slug,
      products: productGroup.products.filter(
        (product) => product.is_valid || ((isEditorOrOwner || isManager) && !search.state.customer_view),
      ),
    }))
    .filter((group) => group.products.length || ((isEditorOrOwner || isManager) && !search.state.customer_view));

  const minAmountShowingProducts = response.activity_slug ? 0 : 2;

  return (
    <div className="app-container space-y-1">
      <div className="flex flex-wrap justify-between gap-3">
        {!response.review_enabled ? (
          <div></div>
        ) : !!ratings && ratings.length > 0 ? (
          <a href="#reviews" className="flex flex-row text-sm text-slate-500">
            <StarsOrangeSmall rating={avgStars} />
            &nbsp;({response.ratings?.length || 0})
          </a>
        ) : (
          <ParamLink prefetch={"intent"} path={_review_create_relative} className="link">
            Write a review
          </ParamLink>
        )}
        <div className="flex flex-row items-center gap-3">
          <div className="flex flex-row items-center">
            <IoLocationSharp />
            <span className={"font-semibold line-clamp-1"}>{response.spot_name || "unspecified"}</span>
          </div>
        </div>
      </div>
      {response.bio && <p className="text-gray-700">{response.bio}</p>}
      <div className="flex flex-col gap-5 pt-6">
        {finalProductGroups.map((productGroup, index) => {
          const isOpen = !!expandedIds.find((expanedId) => expanedId === productGroup.activity_slug);
          const allProductsInGroup = productGroup.products;
          const otherProducts = allProductsInGroup
            .filter((product) => {
              return !filteredProductIds.find((id) => id === product.id);
            })
            .map((product) => ({ ...product, outlined: false }));
          const outlinedProducts = allProductsInGroup
            .filter((product) => {
              return filteredProductIds.find((id) => id === product.id);
            })
            .map((product) => ({
              ...product,
              outlined: otherProducts.length > 0,
            }));
          const products = [...outlinedProducts, ...otherProducts];
          const productsShortList = outlinedProducts.length > 1 ? outlinedProducts : products.slice(0, minAmountShowingProducts);
          const showingProducts = isOpen ? products : productsShortList;

          return (
            <div key={index} className="group" aria-selected={isOpen}>
              <AnimatingDiv className="flex  flex-row items-center gap-2">
                <p className="scroll-mt-[200px] text-xl font-bold" id={productGroup.activity_slug}>
                  {activities[productGroup.activity_slug].name}
                </p>
                <p className="text-gray-500">{allProductsInGroup.length}</p>
                {(isEditorOrOwner || isManager) && !search.state.customer_view && (
                  <ParamLink
                    path={_product_mutate}
                    paramState={{ establishment_id: response.id, activity_slug: productGroup.activity_slug }}
                    className="link"
                  >
                    Create
                  </ParamLink>
                )}
                <span className="flex-1"></span>
                {isOpen && !!products.length && products.length !== productsShortList.length && (
                  <button
                    type={"button"}
                    onClick={() => {
                      setExpandedIds(removeFromArray(expandedIds, productGroup.activity_slug));
                    }}
                    className={"flex items-center gap-1"}
                  >
                    Collapse <ChevronUpIcon className={"h-4 w-4"} />
                  </button>
                )}
              </AnimatingDiv>
              <AnimatingDiv className="div space-y-3 py-3">
                {!!showingProducts.length && (
                  <div className="grid flex-1 grid-cols-1 gap-3 md:grid-cols-2" key={showingProducts.length + "used_for_animated_div"}>
                    {showingProducts.map((product) => (
                      <ProductItem
                        key={product.id}
                        className={"h-full outline-2 outline-secondary-tag aria-selected:outline"}
                        item={product}
                        locale={response.locale}
                        link={{
                          "aria-selected": product.outlined,
                          prefetch: "intent",
                          path: _product_detail(product.id),
                          state: "canGoBack",
                        }}
                      />
                    ))}
                  </div>
                )}
                {!isOpen && !!products.length && products.length !== productsShortList.length && (
                  <button
                    onClick={() => setExpandedIds(toggleArray(expandedIds, productGroup.activity_slug))}
                    type="button"
                    className="flex w-full flex-row items-center justify-between rounded-md bg-secondary-50 p-2 hover:opacity-80 active:opacity-8 border border-secondary-stroke"
                  >
                    <span>
                      {isOpen ? "Hide " : "Show "}
                      {productsShortList.length === 0
                        ? products.length === 1
                          ? "1 activity"
                          : products.length + " activities"
                        : products.length - productsShortList.length + (isOpen ? "" : " more")}
                    </span>
                    <CgChevronDown className="transition-all group-aria-selected:rotate-180" />
                  </button>
                )}
              </AnimatingDiv>
            </div>
          );
        })}
        {!!response.about && (
          <div className="flex flex-col space-y-2">
            <SectionHeading>{t`About us`}</SectionHeading>
            <BlueBox>
              <ReadmoreBox>{response.about}</ReadmoreBox>
            </BlueBox>
          </div>
        )}
        {language_codes.length > 0 && (
          <div className="flex flex-col space-y-2">
            <SectionHeading>{t`Languages`}</SectionHeading>
            <BlueBox>
              <p>In our Dive Center we speak</p>
              <div className="flex flex-wrap gap-3 pt-2">
                {languages
                  .filter((language) => language_codes.find((code) => code === language.code))
                  .map((language) => (
                    <BlueTag className="text-md py-2 px-4 font-semibold" key={language.code}>
                      {language.name}
                    </BlueTag>
                  ))}
              </div>
            </BlueBox>
          </div>
        )}
        <SectionHeading>Contact details</SectionHeading>
        <BlueBox>
          <OperatorContact />
        </BlueBox>
        <ContactDialog />
        {response.review_enabled && (
          <section className="space-y-3">
            <SectionHeading id={"reviews"}>Reviews</SectionHeading>
            <div className="flex flex-col gap-6 lg:flex-row">
              {finalReviews.length > 0 ? (
                <div className="space-y-6 lg:w-2/5">
                  <div className="flex flex-row items-center">
                    <StarsWithText stars={avgStars} />
                    &nbsp;
                    <span>
                      ({finalReviews.length} {finalReviews.length === 1 ? "review" : "reviews"})
                    </span>
                  </div>
                  <div className="grid grid-cols-[auto_1fr_auto] items-center gap-y-3 gap-x-4">
                    {starsBackwards.map((nr) => {
                      const currentStarCount = finalReviews.filter((review) => review.experience_rating === nr).length;
                      const otherReviewsCount = finalReviews.length - currentStarCount;
                      return (
                        <Fragment key={nr}>
                          <div className="text-left">
                            {nr} {nr > 1 ? "stars" : "star"}
                          </div>
                          <div className="">
                            <div className="flex h-[8px] w-full flex-row rounded-full bg-gray-300">
                              <div style={{ flexGrow: currentStarCount }} className="rounded-full bg-primary" />
                              <div style={{ flexGrow: otherReviewsCount }} />
                            </div>
                          </div>
                          <div className="text-right">{currentStarCount} reviews</div>
                        </Fragment>
                      );
                    })}
                  </div>
                  <div className="flex flex-wrap gap-3">
                    <FeedbackChipCounts items={finalReviews} type={"plus"} />
                    <FeedbackChipCounts items={finalReviews} type={"min"} />
                  </div>
                  <ParamLink className="btn btn-primary" prefetch="render" path={_review_create_relative}>
                    Write a review
                  </ParamLink>
                </div>
              ) : (
                <div className="space-y-3">
                  <p>Be the first to write a review.</p>
                  <ParamLink className="btn btn-primary" prefetch="render" path={_review_create_relative}>
                    Write a review
                  </ParamLink>
                </div>
              )}
              <div className="lg:w-3/5">
                <div className="space-y-8 md:space-y-3">
                  {finalReviews.map((review, index) => (
                    <div
                      key={review.id}
                      className={twMerge(
                        `space-y-3 md:rounded-md md:border md:p-6`,
                        review.verified_at || review.published_at ? "border-secondary-500" : "border-gray-200 opacity-60 md:bg-gray-50",
                      )}
                    >
                      <hr className="md:hidden" />
                      <div className="flex flex-row gap-3">
                        <StarsWithText stars={review.experience_rating} />
                        {app.editor && (
                          <ParamLink
                            className="link"
                            path={_review_detail({
                              review_id: review.id,
                              establishment_id: review.establishment_id,
                            })}
                          >
                            View
                          </ParamLink>
                        )}
                      </div>
                      <div>{activities[review.activity_slug].name}</div>
                      <div className="flex flex-wrap gap-2">
                        {review.positives?.map((plus) => (
                          <FeedbackSingleChip key={plus} label={POSITIVES[plus as keyof typeof POSITIVES]} type={"plus"} />
                        ))}
                        {review.negatives?.map((min) => (
                          <FeedbackSingleChip key={min} label={NEGATIVES[min as keyof typeof NEGATIVES]} type={"min"} />
                        ))}
                      </div>
                      {review.experience && <p className="whitespace-pre-wrap">{review.experience}</p>}
                      {!!review.files && review.files.length > 0 && <ImageGallery fileNames={review.files?.map((file) => file.filename)} />}
                      <div className="text-slate-600">
                        {review.display_name || "unkown"} | {review.created_at && datetimeFormat.format(new Date(review.created_at))}
                        {/*{!review.verified_at && (*/}
                        {/*  <span>*/}
                        {/*    | <span className="text-red-500">unverified</span>*/}
                        {/*  </span>*/}
                        {/*)}*/}
                      </div>
                      {/*{index < finalReviews.length - 1 && <hr />}*/}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>
        )}
      </div>
    </div>
  );
}
