import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import React, { Fragment } from "react";
import { LoaderFunctionArgs } from "@remix-run/router";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { ParamLink } from "~/components/meta/CustomComponents";
import { AnimatingDiv } from "~/components/base/base";
import { _item_detail, _retail } from "~/misc/paths";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { useAppContext } from "~/hooks/use-app-context";
import { IkImage } from "~/components/IkImage";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { createPageOverwrites } from "~/misc/consts";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { ActivitySlug } from "~/domain/activity/activity";
import { SidePanel, SidePanelHeading } from "~/components/SidePanel";
import { ProductPriceForm } from "~/domain/product/ProductPriceForm";
import { attributes, ItemForm, SimpleProductForm } from "~/domain/product/SimpleProductForm";
import { DeleteButton, SubmitButton } from "~/components/base/Button";
import { ActionForm, defaultEqualCheck } from "~/components/form/BaseFrom";
import { fName, tableIdRef } from "~/misc/helpers";
import { OperationInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { InputFilesDefault } from "~/components/form/FilesInput";
import { defaultCurrency } from "~/misc/vars";
import { ConnectToBookingBanner } from "~/domain/booking/booking-components";
import { RInput, RLabel } from "~/components/ResourceInputs";
import { CategorySelect, RetailBreadCrumb } from "~/domain/category/CategoryComponents";
import { FaPlus } from "react-icons/fa";
import { unique } from "remeda";
import { getItemTitle } from "~/domain/product/ProductItem";
import { MoneyValue } from "~/components/field/MoneyValue";
import { pricesJsonEb } from "~/domain/product/product-queries.server";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const record = paramsToRecord(url.searchParams);

  const establishmentId = record.establishment_id || record.persist_establishment_id;

  if (!establishmentId) throw new Error("establishment_id param Required");

  const items = await kysely
    .selectFrom("item")
    .select(["item.id", "item.title", "item.description", "item.subtitle", "item.category_id", "item.brand"])
    .where("item.category_id", record.category_id ? "=" : "is", record.category_id)
    .where("item.establishment_id", "=", establishmentId)
    .where("item.activity_slug", "=", "retail" satisfies ActivitySlug)
    .select((productEb) => [
      jsonArrayFrom(
        productEb
          .selectFrom("product")
          .where("product.deleted_at", "=", at_infinity_value)
          .whereRef("product.item_id", "=", "item.id")
          .leftJoin("product_price", "product_price.price_id", "product.id")
          .leftJoin("price", "price.id", "product_price.price_id")
          .select((eb) => [
            "product.id",
            "product.size",
            "product.color",
            "product.sku",
            "product.stock",
            "product.item_id",
            pricesJsonEb,
            "price.amount",
            "price.currency_id",
            jsonArrayFrom(fileTargetsQb(kysely, "product", eb.ref("product.id"))).as("files"),
          ]),
      ).as("products"),
    ])
    .orderBy("item.title")
    .execute();

  // if (!result) throw notFoundOrUnauthorzied("establishment not found or you not authorized");
  return {
    items: items,
    // establishment: result,
    ...createPageOverwrites({ customer_toggle: true, establishment_id: establishmentId }),
  };
};

const modalTypes = ["product_create", "category_create", "category_edit"] as const;
const getModalType = (modalType: (typeof modalTypes)[number]) => modalType;

const CategoryPanelContent = () => {
  const ctx = useAppContext();
  const search = useSearchParams2();
  const establishment = ctx.establishment;

  if (!establishment) return <div>Establishment required</div>;
  const pageCategory = establishment?.categories.find((cat) => cat.id === search.state.category_id);
  const editCategory = search.state.modal_detail_name === getModalType("category_create") ? null : pageCategory;
  const parentCategoryId =
    search.state.modal_detail_name === getModalType("category_create") ? pageCategory?.id : pageCategory?.parent_category_id;

  return (
    <Fragment>
      <SidePanelHeading>
        <h3 className="text-xl">{editCategory ? "Edit Category" : pageCategory ? "Create Sub-Category" : "Create Category"}</h3>
      </SidePanelHeading>
      <CategorySelect
        name={fName("category", "data.parent_category_id")}
        defaultValue={parentCategoryId}
        parent
        disabledCategoryId={editCategory?.id}
      />
      {!editCategory && <RInput table="category" field="data.establishment_id" value={establishment.establishment_id} type="hidden" />}
      {editCategory && <RInput table="category" field="id" value={editCategory.id} type="hidden" />}
      <div className="form-control">
        <RLabel table="category" field="data.name">
          Category Name
        </RLabel>
        <RInput table="category" field="data.name" className="input w-full" defaultValue={editCategory?.name || ""} required />
      </div>

      {/*{!!parentCategoryId && <RInput table={"category"} field={"data.parent_category_id"} type={"hidden"} value={parentCategoryId} />}*/}
      <p>Images</p>
      <InputFilesDefault small target_id={tableIdRef("category")} target={"category"} defaultValue={editCategory?.files} />
    </Fragment>
  );
};

export default function Page() {
  const ctx = useAppContext();
  const search = useSearchParams2();
  const data = useLoaderData<typeof loader>();
  const establishment = ctx.establishment;
  if (!establishment) return <div>Establishment required</div>;
  const category = establishment.categories.find((cat) => cat.id === search.state.category_id);
  const member = ctx.members.find((member) => member.establishment_id === establishment?.id);
  const isManager = (member?.admin || 0) > 1;
  const showEdit = !search.state.customer_view && isManager;
  const activeItems = data.items.filter((item) => item.products.length);

  const brands = unique(activeItems.map((item) => item.brand).filter((brand): brand is string => !!brand));
  const selectedBrand = brands.find((brand) => brand === search.state.brand);

  const filteredItems = activeItems.filter((item) => !selectedBrand || item.brand === selectedBrand);
  const isUnder2LevelDeep = !category?.parent_category_id;
  const subcategories = establishment.categories.filter((cat) => cat.parent_category_id === search.state.category_id);
  return (
    <Fragment>
      <div className="app-container space-y-6">
        <ConnectToBookingBanner />
        {category && <RetailBreadCrumb category_id={category?.id} />}
        {category ? (
          <div className="flex flex-row gap-3 items-center">
            <h2 className="text-xl font-semibold">{category.name}</h2>
            {showEdit && (
              <Fragment>
                <ParamLink paramState={{ toggle_modal: "side", modal_detail_name: getModalType("category_edit") }} className="link">
                  Edit
                </ParamLink>
                <ActionForm
                  confirmMessage={activeItems.length ? "The products will move up by removing the Category, Are you sure" : "Are you sure?"}
                >
                  <RInput table={"category"} field={"id"} value={category.id} />
                  <OperationInput table={"category"} value={"delete"} />
                  <RedirectParamsInput path={_retail} paramState={{ category_id: category.parent_category_id }} />
                  {data.items.map((item) => (
                    <Fragment key={item.id}>
                      <RInput table={"item"} field={"id"} index={item.id} value={item.id} />
                      <RInput
                        table={"item"}
                        field={"data.category_id"}
                        index={item.id}
                        type={"hidden"}
                        value={category.parent_category_id || ""}
                      />
                    </Fragment>
                  ))}
                  <DeleteButton className="capitalize" />
                </ActionForm>
              </Fragment>
            )}
            {/*<ParamLink className="link" reload paramState={{ category_id: category.parent_category_id }}>*/}
            {/*  Up*/}
            {/*</ParamLink>*/}
          </div>
        ) : (
          <h2 className="text-xl font-semibold">Retail</h2>
        )}
        <div className="space-y-6">
          {(!!subcategories.length || (isUnder2LevelDeep && showEdit)) && (
            <div className="space-y-6">
              <div className="flex flex-row gap-3 items-center">
                <h3 className="text-lg font-medium">Categories</h3>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {subcategories.map((category) => {
                  const firstFile = category.files[0];
                  return (
                    <ParamLink
                      key={category.id}
                      reload
                      paramState={{ category_id: category.id }}
                      className="block rounded-md border-slate-200 border hover:shadow-md shadow hover:border-slate-300"
                    >
                      {firstFile && (
                        <div className="flex items-center justify-center">
                          <div className="rounded-md overflow-hidden" style={{ width: 120, height: 100 }}>
                            <IkImage w={120} h={100} className="object-cover w-full h-full" path={firstFile.filename} />
                          </div>
                        </div>
                      )}
                      <div className="p-3 text-center">
                        <p>{category.name}</p>
                      </div>
                    </ParamLink>
                  );
                })}
                {showEdit && (
                  <ParamLink
                    paramState={{ toggle_modal: "side", modal_detail_name: getModalType("category_create") }}
                    className="border-primary text-primary  whitespace-nowrap py-4 rounded-md text-xl gap-3 items-center flex flex-col justify-center hover:opacity-60  transition-opacity border px-5"
                  >
                    <FaPlus className="w-16 h-16" />
                    <span>{category ? "Create Sub-Category" : "Create Category"}</span>
                  </ParamLink>
                )}
              </div>
            </div>
          )}
          {!!brands.length && (
            <div className="flex flex-wrap gap-3">
              {brands.map((brand) => (
                <ParamLink
                  key={brand}
                  className="btn btn-basic aria-selected:btn-secondary"
                  paramState={{ brand: brand === selectedBrand ? null : brand }}
                  aria-selected={brand === selectedBrand}
                >
                  {brand}
                </ParamLink>
              ))}
            </div>
          )}
          <div className="rounded-md">
            {/*<h3 className="text-lg font-medium mb-4">{category ? `Products in ${category.name}` : "Products"}</h3>*/}
            <h3 className="text-lg font-medium mb-4">Products</h3>
            {/*{establishment.items.length === 0 && (*/}
            {/*  <div className="col-span-2 text-center py-8 text-gray-500">No products found in this category</div>*/}
            {/*)}*/}
            <AnimatingDiv className="grid grid-cols-1 gap-6 md:grid-cols-3">
              {filteredItems.map((item) => {
                const shownProduct = item.products.sort((a, b) => b.files.length - a.files.length)[0];
                const firstPrice = shownProduct?.product_prices[0];
                const firstFile = shownProduct?.files[0];
                const options = attributes.map((attribute) => {
                  const values = unique(item.products.map((product) => product[attribute]));
                  return [attribute, values] as const;
                });
                const filteredOptions = options.filter(([_, vals]) => vals.length > 1);
                return (
                  <ParamLink
                    key={item.id}
                    path={_item_detail(item.id)}
                    paramState={{
                      booking_id: search.state.booking_id,
                      establishment_id: search.state.establishment_id,
                    }}
                    className="overflow-hidden rounded-md border border-secondary-stroke"
                  >
                    {firstFile && (
                      <div className="justify-center items-center flex">
                        <div className="rounded-md overflow-hidden" style={{ width: 120, height: 100 }}>
                          <IkImage w={120} h={100} className="object-cover w-full h-full" path={firstFile.filename} />
                        </div>
                      </div>
                    )}
                    <div className="p-3 space-y-1.5">
                      <p className="text-center text-slate-700 font-semibold">{getItemTitle(item)}</p>
                      <p className="text-center line-clamp-2 text-slate-700 text-xs">{item.subtitle || item.description}</p>
                      {firstPrice && (
                        <p className="font-semibold text-lg text-center">
                          <MoneyValue
                            locale={null}
                            // locale={selectedProductPrice.currency_id}
                            toCurrency={firstPrice.currency_id}
                            nativeAmount={firstPrice.amount}
                            nativeCurrency={firstPrice.currency_id}
                          />
                        </p>
                      )}
                      {!!filteredOptions.length && (
                        <p className="text-slate-500 text-xs text-center">
                          Available in{" "}
                          {filteredOptions.map(([attr, values], index) => (
                            <Fragment key={attr}>
                              {!!index && <span>&nbsp;&&nbsp;</span>}
                              <span className="underline">
                                {values.length} {attr}s
                              </span>
                            </Fragment>
                          ))}
                        </p>
                      )}
                    </div>
                  </ParamLink>
                );
              })}
              {showEdit && (
                <ParamLink
                  paramState={{ toggle_modal: "side", modal_detail_name: getModalType("product_create") }}
                  className="border-primary text-primary  whitespace-nowrap py-4 rounded-md text-xl gap-3 items-center flex flex-col justify-center hover:opacity-60  transition-opacity border px-5"
                >
                  <FaPlus className="w-16 h-16" />
                  Create Product
                </ParamLink>
              )}
            </AnimatingDiv>
          </div>
        </div>
      </div>
      <SidePanel className="max-md:inset-x-0">
        <div className="h-full w-full md:min-w-96 md:max-w-96 space-y-3 bg-white p-3 overflow-auto">
          <ActionForm className="space-y-3" onCheckEqual={defaultEqualCheck}>
            <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined }} />
            {search.state.modal_detail_name === getModalType("product_create") ? (
              <div className="space-y-3">
                <SidePanelHeading>
                  <h3 className="text-xl">Create Product</h3>
                </SidePanelHeading>
                <ItemForm
                  item={{
                    activity_slug: "retail" satisfies ActivitySlug,
                    category_id: category?.id,
                    establishment_id: establishment.id,
                  }}
                  brands={brands}
                />
                <SimpleProductForm product={{ item_id: tableIdRef("item") }} />
                <ProductPriceForm productPrice={{ currency_id: establishment.default_currency || defaultCurrency }} />
                <InputFilesDefault target_id={tableIdRef("product")} target={"product"} small />
              </div>
            ) : (
              <CategoryPanelContent />
            )}

            <div className="flex flex-row gap-3 items-center justify-end">
              <ParamLink paramState={{ toggle_modal: undefined }} replace className="btn">
                Cancel
              </ParamLink>
              <SubmitButton className="btn btn-primary">Save</SubmitButton>
            </div>
          </ActionForm>
        </div>
      </SidePanel>
    </Fragment>
  );
}
