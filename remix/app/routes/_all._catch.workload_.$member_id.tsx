import { kysely } from "~/misc/database.server";
import { useLoaderData } from "@remix-run/react";
import { getSessionSimple } from "~/utils/session.server";
import { getDateFromParams } from "~/domain/planning/planning.helpers.server";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { sumBy, uniqueBy } from "remeda";
import { MonthSwitch } from "~/domain/meta/datetime";
import { activeUserSessionQb, memberIsAdminOrOwnerQb, QbArgs } from "~/domain/member/member-queries.server";
import { workloadAssignmentsQb } from "~/domain/workload/queries.server";
import { notFoundOrUnauthorzied } from "~/misc/responses";
import { activities, ActivitySlug } from "~/domain/activity/activity";
import { myGroupBy2 } from "~/misc/helpers";
import { divingCoursesJsonEb } from "~/domain/product/product-queries.server";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { getMonthObj } from "~/domain/planning/plannings-helpers";
import { LoaderFunctionArgs } from "@remix-run/router";
import { Fragment } from "react";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const session = await getSessionSimple(request);
  const date = getDateFromParams(request, true);
  const month = getMonthObj(date.dateParam);

  const memberId = params.member_id!;

  const ctx: QbArgs = { ctx: session, trx: kysely };

  const member = await kysely
    .selectFrom("member")
    .where("member.id", "=", memberId)
    .where((eb) =>
      eb.or([
        eb("member.user_id", "=", activeUserSessionQb(ctx, true).select("_user.id")),
        eb("member.establishment_id", "in", memberIsAdminOrOwnerQb(ctx, "read").select("_member.establishment_id")),
      ]),
    )
    .select((eb) => [
      "member.id",
      "member.name",
      jsonObjectFrom(simpleEstablishmentQb.where("establishment.id", "=", eb.ref("member.establishment_id"))).as("establishment"),
      jsonArrayFrom(
        workloadAssignmentsQb
          .select(["trip.activity_location", divingCoursesJsonEb, "trip.date as trip_date"])
          .where("trip_assignment.member_id", "=", eb.ref("member.id"))
          .where("trip.date", ">=", month.firstDate)
          .where("trip.date", "<=", month.lastDate),
      ).as("assignments"),
    ])
    .executeTakeFirst();

  if (!member) throw notFoundOrUnauthorzied();

  return {
    date: date,
    member: member,
  };
};

const TripTotals = (props: { slug: ActivitySlug; type?: "sessions" }) => {
  const data = useLoaderData<typeof loader>();
  const assignments = data.member.assignments.filter((assignment) => assignment.activity_slug === props.slug);
  const totals = {
    count: 0,
    pax: 0,
    tanks: 0,
  };
  return (
    <Fragment>
      <thead>
        <tr>
          <td colSpan={4} className="pt-6 pb-1">
            <h3 className="text-xl font-semibold">{activities[props.slug].name}</h3>
          </td>
        </tr>
      </thead>
      <thead className="border-b border-slate-200  bg-slate-50">
        <tr className="">
          <td className="py-2">Trip</td>
          <td className="p-2">Count</td>
          <td className="p-2">Pax</td>
          <td className="p-2">{!props.type && "Tanks"}</td>
        </tr>
      </thead>
      <tbody>
        {myGroupBy2(
          assignments,
          (assignment) => `${assignment.sites.length} ${props.type || "dives"}, ${assignment.activity_location}`,
        ).map((assignmentGroup) => {
          const tripCount = uniqueBy(assignmentGroup.items, (item) => item.trip_id).length;
          totals.count += tripCount;

          const pax = assignmentGroup.items.length;
          totals.pax += pax;

          const tanks = sumBy(assignmentGroup.items, (item) => item.sites.length);
          totals.tanks += tanks;

          return (
            <tr key={assignmentGroup.groupKey}>
              <td className="whitespace-nowrap py-2">{assignmentGroup.groupKey}</td>
              <td className="p-2">{tripCount}</td>
              <td className="p-2">{pax}</td>
              <td className="p-2">{!props.type && tanks}</td>
            </tr>
          );
        })}
        <tr className="border-t border-slate-200 font-semibold ">
          <td className="whitespace-nowrap py-2">Total</td>
          <td className="p-2">{totals.count}</td>
          <td className="p-2">{totals.pax}</td>
          <td className="p-2">{!props.type && totals.tanks}</td>
        </tr>
      </tbody>
    </Fragment>
  );
};

const CourseTotals = () => {
  const data = useLoaderData<typeof loader>();
  const assignments = data.member.assignments.filter((assignment) => assignment.activity_slug === ("diving-course" satisfies ActivitySlug));
  const totals = {
    count: 0,
    pax: 0,
    completed: 0,
  };
  return (
    <Fragment>
      <thead>
        <tr>
          <td colSpan={4} className="pt-7 pb-1">
            <h3 className="text-xl font-semibold">{activities["diving-course"].name}</h3>
          </td>
        </tr>
      </thead>
      <thead className="border-b border-slate-200  bg-slate-50">
        <tr>
          <td className="py-2">Course</td>
          <td className="p-2">Count</td>
          <td className="p-2">Pax</td>
          <td className="p-2">Completed</td>
        </tr>
      </thead>
      <tbody>
        {myGroupBy2(assignments, (assignment) => assignment.diving_courses.map((dc) => dc.name).join(", ")).map((assignmentGroup) => {
          const assignmentsUnigByBooking = uniqueBy(assignmentGroup.items, (item) => item.booking_id);
          const tripCount = assignmentsUnigByBooking.length;
          totals.count += tripCount;

          const paxList = uniqueBy(assignmentGroup.items, (item) => item.booking_id + item.participant_id);
          const paxCount = paxList.length;
          totals.pax += paxCount;

          const completed = paxList.filter((assignment) => assignment.completed_this_month).length;
          totals.completed += completed;

          return (
            <tr key={assignmentGroup.groupKey}>
              <td className="whitespace-nowrap py-2">{assignmentGroup.groupKey}</td>
              <td className="p-2">{tripCount}</td>
              <td className="p-2">{paxCount}</td>
              <td className="p-2">{completed}</td>
            </tr>
          );
        })}
        <tr className="border-t border-slate-200 font-semibold ">
          <td className="whitespace-nowrap py-2">Total</td>
          <td className="p-2">{totals.count}</td>
          <td className="p-2">{totals.pax}</td>
          <td className="p-2">{totals.completed}</td>
        </tr>
      </tbody>
    </Fragment>
  );
};

export default function Page() {
  const data = useLoaderData<typeof loader>();

  const establishment = data.member.establishment;
  const workingDays = uniqueBy(data.member.assignments, (assignment) => assignment.trip_date);
  // console.log("workingda", workingDays);
  return (
    <div className="app-container space-y-6">
      <h1 className="text-xl font-semibold text-slate-600">{establishment && getEstablishmentName(establishment)}</h1>
      <h2 className="text-xl font-semibold">Workload {data.member.name}</h2>
      <MonthSwitch />
      <div>
        <p className="p-2 bg-slate-100 font-semibold">Working days: {workingDays.length}</p>
        <table className="text-xs max-md:w-full md:min-w-[300px]">
          <TripTotals slug={"fun-diving"} />
          <CourseTotals />
          <TripTotals slug={"snorkeling"} type={"sessions"} />
          <TripTotals slug={"freediving"} type={"sessions"} />
          <TripTotals slug={"freediving-course"} type={"sessions"} />
        </table>
      </div>
    </div>
  );
}
