import type { DataFunctionArgs } from "@remix-run/server-runtime";
import { useLoaderData } from "@remix-run/react";
import { mockedEmails } from "~/server/mail/email.client.server";

export const loader = async ({ request, params }: DataFunctionArgs) => {
  const index = Number(params.index);
  console.log("emails", mockedEmails);
  const mail = mockedEmails[index];
  return {
    email: mail,
  };
};

export default function Page() {
  const data = useLoaderData<typeof loader>();

  if (!data.email) return <div>Not found</div>;

  // Define a regular expression pattern
  const pattern = /<!DOCTYPE(.*?)<\/html>/s;

  // Use the RegExp.exec() method to extract the matching part
  const match = pattern.exec(data.email);
  const htmlStr = match && match[0];
  // const htmlPart = data.email

  return (
    <div>
      {htmlStr && <iframe width={"100%"} height={"400px"} title={"email"} srcDoc={htmlStr} />}
      <div className="p-3">{data.email}</div>
    </div>
  );
}
