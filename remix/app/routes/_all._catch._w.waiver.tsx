import { LoaderFunctionArgs } from "@remix-run/router";
import { useLoaderData } from "@remix-run/react";
import React, { Fragment, useId } from "react";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _waiver_detail, _waiver_mutate } from "~/misc/paths";
import { DeleteButtonForm, RInput } from "~/components/ResourceInputs";
import { fName, myGroupBy2 } from "~/misc/helpers";
import { DividerWithText } from "~/components/Divider";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { getSessionSimple } from "~/utils/session.server";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { AnimatingDiv } from "~/components/base/base";
import { ActionForm, ResponseIdProvider } from "~/components/form/BaseFrom";
import { DeleteButton, SubmitButton } from "~/components/base/Button";
import { OperationInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { waiversEb } from "~/domain/waiver/waiver-queries.server";
import { ChevronUpIcon } from "@heroicons/react/20/solid";
import { twMerge } from "tailwind-merge";
import { ActionAlert } from "~/components/ActionAlert";
import { ascNullsLast } from "~/kysely/kysely-helpers";
import { memberIsAdminQb, toArgs } from "~/domain/member/member-queries.server";
import { kysely } from "~/misc/database.server";
import { useAppContext } from "~/hooks/use-app-context";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { getWaiverType } from "~/domain/waiver/waiver-vars";
import { EstablishmentLayout } from "~/components/AllowedForEstablishment";
import { SidePanel, SidePanelHeading } from "~/components/SidePanel";
import { WaiverForm } from "~/domain/waiver/WaiverForm";
import { formatValidityDuration, ValidityDurationField } from "~/components/duration";

export { action } from "~/routes/_all._catch.resource";

export const loader = async (args: LoaderFunctionArgs) => {
  const ctx = await getSessionSimple(args.request);
  const record = paramsToRecord(new URL(args.request.url).searchParams);

  const waivers = await waiversEb
    .leftJoin("waiver_establishment", (join) =>
      join
        .onRef("waiver_establishment.waiver_id", "=", "waiver.id")
        .on("waiver_establishment.establishment_id", "=", record.persist_establishment_id),
    )
    .where((eb) => {
      if (!record.persist_establishment_id) return eb("waiver.establishment_id", "is", null);

      return eb.or([
        eb("waiver.establishment_id", "is", null),
        eb("waiver.establishment_id", "=", record.persist_establishment_id),
        eb("waiver_establishment.id", "is not", null),
      ]);
    })
    .select((eb) => [
      eb
        .or([
          eb("waiver.diving_certificate_organization_key", "is", null),
          eb(
            "waiver.diving_certificate_organization_key",
            "in",
            eb
              .selectFrom("product")
              .innerJoin("item", "item.id", "product.item_id")
              .where("product.deleted_at", "=", at_infinity_value)
              .innerJoin("item__diving_course", "item__diving_course.item_id", "item.id")
              .innerJoin("diving_course", "diving_course.id", "item__diving_course.diving_course_id")
              .where("item.establishment_id", "=", record.persist_establishment_id)
              .select("diving_course.diving_certificate_organization_key"),
          ),
        ])
        .as("show_by_default"),
      // jsonObjectFrom(simpleEstablishmentQb.where("establishment.id", "=", eb.ref("waiver.establishment_id"))).as("establishment"),
      jsonArrayFrom(
        eb
          .selectFrom("waiver_translation")
          .where("waiver_translation.waiver_id", "=", eb.ref("waiver.id"))
          .orderBy("waiver_translation.sort_order", ascNullsLast)
          .selectAll("waiver_translation"),
        // .select(["waiver_translation.name", "waiver_translation.language_code"]),
      ).as("translations"),
    ])
    .selectAll("waiver")
    .select((eb) => ["waiver_establishment.validity_duration as establishment_validity_duration"])
    .execute();

  return {
    waivers: waivers,
  };
};

const valueOrNull = (str: string | null) => (str ? `'${str}'` : "null");

const SortButton = (props: { table: "waiver" | "waiver_translation"; ids: string[]; currentIndex: number; newIndex: number }) => {
  const id = useId();
  const currentValue = props.ids[props.currentIndex];
  const otherValue = props.ids[props.newIndex];
  console.log("id", id);
  if (!currentValue || !otherValue) return <Fragment />;

  const newIds = [...props.ids];
  newIds[props.currentIndex] = otherValue;
  newIds[props.newIndex] = currentValue;

  return (
    <ActionForm>
      {newIds.map((id, index) => (
        <Fragment key={id}>
          <RInput table={props.table} field={"id"} index={id} value={id} />
          <RInput table={props.table} field={"data.sort_order"} index={id} value={index} type={"hidden"} />
        </Fragment>
      ))}
      <SubmitButton className="aria-busy:spinner spinner-dark py-1 px-2">
        <ChevronUpIcon className={twMerge("w-4 h-4", props.newIndex > props.currentIndex && "rotate-180")} />
      </SubmitButton>
    </ActionForm>
  );
};

export default function Page() {
  const search = useSearchParams2();
  const ctx = useAppContext();
  const data = useLoaderData<typeof loader>();

  const selectedWaiver = data.waivers.find((waiver) => waiver.id === search.state.id);

  console.log("data", selectedWaiver);

  const insertStatement = data.waivers
    .map((waiver) => {
      // const cmprValueOrNull = (key: keyof typeof waiver) => {
      //   const val = waiver[key];
      //   const leftOperatr = `waiver."${key}"`;
      //   if (val === null) return `${leftOperatr} is null`;
      //   return `${leftOperatr} = '${val}'`;
      // };
      const translationsDeleteSql = waiver.translations
        .map((translation) => {
          return `
              delete
              from waiver_translation
              where waiver_translation.id = '${translation.id}';
          `;
        })
        .join("\n");

      const waiverSql = `
          delete
          from waiver
          where waiver.id = '${waiver.id}';

          insert
          into waiver (id,
                       slug,
                       establishment_id,
                       diving_certificate_organization_key)
          values ('${waiver.id}', '${waiver.slug}', ${valueOrNull(waiver.establishment_id)},
                  ${valueOrNull(waiver.diving_certificate_organization_key)});
      `;

      const translationInsertSql = waiver.translations
        .map(
          (translation) => `
              insert into waiver_translation (waiver_id,
                                              language_code,
                                              name,
                                              markdoc)
              values ('${waiver.id}',
                      '${translation.language_code}',
                      '${translation.name}',
                      '${translation.markdoc}');
          `,
        )
        .join("\n");

      return [translationsDeleteSql, waiverSql, translationInsertSql].join("\n");
    })
    .join("\n");

  return (
    <EstablishmentLayout
      title={
        <div className="flex flex-wrap gap-3 items-center">
          <h1 className="text-xl font-semibold">Waivers</h1>
          {/* <ParamLink className="link" path={_waiver_mutate} paramState={{ establishment_id: search.state.persist_establishment_id }}>
            create
          </ParamLink> */}
          <ParamLink className="link" paramState={{ toggle_modal: "side", id: null }}>
            create
          </ParamLink>
        </div>
      }
    >
      <div className="space-y-3">
        <ResponseIdProvider>
          <ActionAlert scrollTo />
          <AnimatingDiv className="grid w-fit grid-cols-[repeat(6,auto)] gap-3 gap-y-4 overflow-auto">
            {/*<div className="text-slate-500 uppercase col-span-2">#</div>*/}
            {/*<div className="text-slate-500 uppercase">Lang</div>*/}
            {/*<div className="text-slate-500 uppercase">Name</div>*/}
            {/*<div className="col-span-3"></div>*/}

            {myGroupBy2(data.waivers, (waiver) =>
              waiver.establishment_id ? "Custom waivers" : waiver.show_by_default ? "Default waivers" : "Hidden waivers",
            ).map((establishmentGroup) => {
              if (establishmentGroup.groupKey === "Hidden waivers" && !ctx.editor) return <Fragment key={establishmentGroup.groupKey} />;
              const waiverIds = establishmentGroup.items.map((item) => item.id);
              return (
                <Fragment key={establishmentGroup.groupKey}>
                  <div className="col-span-6 pt-6 break-words">
                    <DividerWithText>{establishmentGroup.groupKey}</DividerWithText>
                  </div>
                  {establishmentGroup.items.map((waiver, waiverIndex) => {
                    const waiverTranslationIds = waiver.translations.map((translation) => translation.id);
                    const canEdit =
                      waiver.establishment_id === search.state.persist_establishment_id ||
                      (ctx.editor && !search.state.persist_establishment_id && !waiver.establishment_id);
                    const waiverType = getWaiverType(waiver.type);
                    return (
                      <Fragment key={waiver.id}>
                        {!!waiverIndex && (
                          <div className="col-span-6">
                            <hr />
                            {/*<DividerWithText>{slugGroup.groupKey}</DividerWithText>*/}
                          </div>
                        )}
                        <div className="text-slate-400 text-xs items-center flex "></div>
                        <ParamLink
                          className="text-slate-600 font-semibold break-words line-clamp-1 flex flex-wrap gap-2 items-center"
                          paramState={{ toggle_modal: "side", id: waiver.id }}
                        >
                          <span className="text-slate-400 text-xs items-center flex bg-slate-200 rounded-md p-1 capitalize">
                            {getWaiverType(waiver.type).verb}
                          </span>
                          {!waiver.establishment_id && !!waiver.diving_certificate_organization_key && (
                            <span className="text-slate-400 text-xs items-center flex bg-slate-200 rounded-md p-1">
                              {waiver.diving_certificate_organization_key}
                            </span>
                          )}
                          <span className="break-words line-clamp-1">{waiver.slug}</span>
                          {waiver.validity_duration && (
                            <span
                              className={twMerge(
                                "text-xs font-normal text-slate-600",
                                waiver.establishment_validity_duration && "line-through",
                              )}
                            >
                              {formatValidityDuration(waiver.validity_duration)}
                            </span>
                          )}
                          {waiver.establishment_validity_duration && (
                            <span className="text-slate-600 text-xs items-center flex">
                              {formatValidityDuration(waiver.establishment_validity_duration)}
                            </span>
                          )}
                        </ParamLink>
                        {canEdit ? (
                          <Fragment>
                            <div className="flex justify-between gap-2 col-span-3">
                              <ParamLink className="link" path={_waiver_mutate} paramState={{ id: waiver.id }}>
                                {waiverType.key === "upload" ? "edit" : "create"}
                              </ParamLink>
                              <ActionForm confirmMessage={`Are you sure?`}>
                                <RInput table={"waiver"} field={"id"} value={waiver.id} />
                                <OperationInput table={"waiver"} value={"delete"} />
                                {waiver.translations.map((translation) => (
                                  <Fragment key={translation.id}>
                                    <RInput table={"waiver_translation"} field={"id"} index={translation.id} value={translation.id} />
                                    <OperationInput table={"waiver_translation"} index={translation.id} value={"delete"} />
                                  </Fragment>
                                ))}
                                <DeleteButton>delete</DeleteButton>
                              </ActionForm>
                            </div>
                            <div className="flex flex-row">
                              <SortButton ids={waiverIds} table={"waiver"} currentIndex={waiverIndex} newIndex={waiverIndex - 1} />
                              <SortButton ids={waiverIds} table={"waiver"} currentIndex={waiverIndex} newIndex={waiverIndex + 1} />
                            </div>
                          </Fragment>
                        ) : (
                          <div className="col-span-4"></div>
                        )}
                        {waiver.translations.map((waiverTranslation, waiverTranslationIndex) => (
                          <Fragment key={waiverTranslation.id}>
                            <div className="uppercase">{waiverTranslation.language_code}</div>
                            <div>
                              <ParamLink
                                className="link"
                                path={_waiver_detail(waiver.id)}
                                paramState={{ page_lang: waiverTranslation.language_code }}
                              >
                                {waiverTranslation.name}
                              </ParamLink>
                            </div>
                            {canEdit ? (
                              <Fragment>
                                <div className="justify-between flex flex-row col-span-3">
                                  <ParamLink
                                    className="link"
                                    path={_waiver_mutate}
                                    paramState={{ id: waiver.id, language_code: waiverTranslation.language_code }}
                                  >
                                    edit
                                  </ParamLink>
                                  {/*</div>*/}
                                  {/*<div>*/}
                                  {/*<ParamLink*/}
                                  {/*  className="link"*/}
                                  {/*  path={_waiver_mutate}*/}
                                  {/*  paramState={{*/}
                                  {/*    id: waiver.id,*/}
                                  {/*    language_code: waiverTranslation.language_code,*/}
                                  {/*    action_type: "copy",*/}
                                  {/*  }}*/}
                                  {/*>*/}
                                  {/*  copy*/}
                                  {/*</ParamLink>*/}
                                  {/*</div>*/}
                                  {/*<div className="text-right">*/}
                                  <DeleteButtonForm table={"waiver_translation"} values={[waiverTranslation.id]} />
                                </div>
                                <div className="flex flex-row">
                                  <SortButton
                                    ids={waiverTranslationIds}
                                    table={"waiver_translation"}
                                    currentIndex={waiverTranslationIndex}
                                    newIndex={waiverTranslationIndex - 1}
                                  />
                                  <SortButton
                                    ids={waiverTranslationIds}
                                    table={"waiver_translation"}
                                    currentIndex={waiverTranslationIndex}
                                    newIndex={waiverTranslationIndex + 1}
                                  />
                                </div>
                              </Fragment>
                            ) : (
                              <div className="col-span-4"></div>
                            )}
                          </Fragment>
                        ))}
                      </Fragment>
                    );
                  })}
                </Fragment>
              );
            })}
          </AnimatingDiv>
          {search.state.preview && <textarea className="w-full min-h-96" value={insertStatement}></textarea>}
        </ResponseIdProvider>
      </div>
      <SidePanel className="bg-white">
        <SidePanelHeading>{selectedWaiver ? "Waiver" : "Create waiver"}</SidePanelHeading>
        <div className="px-5">
          <div className="space-y-5">
            <h2 className="text-lg font-semibold">{selectedWaiver?.slug}</h2>
            <ActionForm className="space-y-5">
              <RedirectParamsInput path="./" paramState={{ toggle_modal: undefined, id: null }} />
              <WaiverForm
                waiver={selectedWaiver ? selectedWaiver : { establishment_id: search.state.persist_establishment_id }}
                disabled={!!selectedWaiver && selectedWaiver.establishment_id !== search.state.persist_establishment_id}
              />
              {selectedWaiver &&
                search.state.persist_establishment_id !== selectedWaiver.establishment_id &&
                !!search.state.persist_establishment_id && (
                  <Fragment>
                    <ValidityDurationField
                      name={fName("waiver_establishment", "data.validity_duration")}
                      label="Overwrite default validity term"
                      defaultValue={selectedWaiver?.establishment_validity_duration || ""}
                    />
                    <RInput
                      table="waiver_establishment"
                      type="hidden"
                      field="data.establishment_id"
                      value={search.state.persist_establishment_id}
                    />
                    <RInput table="waiver_establishment" type="hidden" field="data.waiver_id" value={selectedWaiver.id || ""} />
                  </Fragment>
                )}
              <SubmitButton className="btn btn-primary">Save</SubmitButton>
            </ActionForm>
          </div>
        </div>
      </SidePanel>
    </EstablishmentLayout>
  );
}
