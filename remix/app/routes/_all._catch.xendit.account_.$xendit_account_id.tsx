import { use<PERSON><PERSON>der<PERSON><PERSON> } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import React from "react";
import { SubmitButton } from "~/components/base/Button";
import { DeleteButtonForm, RInput } from "~/components/ResourceInputs";
import { getSessionSimple } from "~/utils/session.server";
import { LoaderFunctionArgs } from "@remix-run/router";
import { ActionForm } from "~/components/form/BaseFrom";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { fName, removeObjectKeys } from "~/misc/helpers";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _establishment_detail, _xendit_account } from "~/misc/paths";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { isEditorQb, toArgs } from "~/domain/member/member-queries.server";
import { unauthorized } from "~/misc/responses";
import { getVirtualBankAccounts } from "~/domain/payment/xendit-client.server";
import { DebugContainer } from "~/components/debug";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);

  const editor = await isEditorQb(toArgs(kysely, session_id)).select("_user.editor").executeTakeFirst();
  if (!editor) throw unauthorized();

  const xenditAccountId = params.xendit_account_id!;

  const xenditAccount = await kysely
    .selectFrom("xendit_account")
    .innerJoin("xendit_platform", "xendit_platform.id", "xendit_account.xendit_platform_id")
    .innerJoin("xendit_environment", "xendit_environment.xendit_platform_id", "xendit_platform.id")
    .whereRef("xendit_account.production", "=", "xendit_environment.production")
    .selectAll("xendit_account")
    .select((eb) => [
      "xendit_environment.xendit_api_key",
      jsonArrayFrom(simpleEstablishmentQb.where("establishment.xendit_account_id", "=", eb.ref("xendit_account.id"))).as("establishments"),
      jsonArrayFrom(
        eb
          .selectFrom("xendit_virtual_bank_account")
          .selectAll("xendit_virtual_bank_account")
          .where("xendit_virtual_bank_account.xendit_account_id", "=", eb.ref("xendit_account.id")),
      ).as("virtual_accounts"),
    ])
    .where("xendit_account.id", "=", xenditAccountId)
    .executeTakeFirstOrThrow();

  const getVirtualBankAccountsSave = () => {
    try {
      return getVirtualBankAccounts({
        apiKey: xenditAccount.xendit_api_key,
        headers: xenditAccount.main ? {} : { "for-user-id": xenditAccount.xendit_user_id },
      });
    } catch (e) {
      console.error("could not retrive bank account", e);
      return "could not retrieve bank account";
    }
  };

  return {
    banks: await getVirtualBankAccountsSave(),
    account: removeObjectKeys(xenditAccount, "xendit_api_key"),
  };
};

export default function Page() {
  const response = useLoaderData<typeof loader>();

  const banks = response.banks;

  return (
    <div className="app-container space-y-3">
      <ParamLink path={_xendit_account} className="link">
        Accounts
      </ParamLink>
      <div className="flex flex-row gap-3 items-center">
        <h2 className="text-xl font-bold">Xendit account</h2>
        <DeleteButtonForm table={"xendit_account"} values={[response.account.id]} redirect={_xendit_account} />
      </div>
      <div className="space-y-3">
        <div>
          <pre>{JSON.stringify(removeObjectKeys(response.account, "virtual_accounts"), null, 2)}</pre>
        </div>
      </div>
      <div>
        {response.account.establishments.map((establishment) => (
          <ParamLink key={establishment.establishment_id} path={_establishment_detail(establishment.establishment_id)} className="link">
            {getEstablishmentName(establishment)}
          </ParamLink>
        ))}
      </div>
      <ActionForm>
        <RInput table={"xendit_account"} field={"data.xendit_user_id"} value={response.account.xendit_user_id} type={"hidden"} />
        <RInput table={"xendit_account"} field={"data.xendit_platform_id"} value={response.account.xendit_platform_id} type={"hidden"} />
        <RInput
          table={"xendit_account"}
          field={"data.production"}
          value={response.account.production ? "true" : ""}
          type={"hidden"}
          hiddenType={"__boolean__"}
        />
        <SubmitButton className="btn btn-primary">Refresh</SubmitButton>
      </ActionForm>
      <ActionForm className="flex flex-row gap-3">
        <RInput table={"xendit_account"} field={"data.xendit_user_id"} value={response.account.xendit_user_id} type={"hidden"} />
        <RInput table={"xendit_account"} field={"data.xendit_platform_id"} value={response.account.xendit_platform_id} type={"hidden"} />
        <input
          className="input"
          placeholder={"response email"}
          name={fName("xendit_account", "data.xendit_account_response", 0, "email")}
          defaultValue={response.account.xendit_account_response?.email || ""}
        />
        <input
          className="input"
          placeholder={"bussiness name"}
          name={fName("xendit_account", "data.xendit_account_response", 0, "public_profile", "business_name")}
          defaultValue={response.account.xendit_account_response?.public_profile?.business_name || ""}
        />
        <SubmitButton className="btn btn-primary">Save</SubmitButton>
      </ActionForm>
      <DebugContainer>
        <h2 className="text-xl font-semibold">Virtual bank accounts</h2>
        <div>
          {response.account.virtual_accounts.map((virtualAccount) => (
            <div key={virtualAccount.id}>
              <div>
                <pre>{JSON.stringify(virtualAccount, null, 2)}</pre>
              </div>
            </div>
          ))}
        </div>
        {typeof banks === "string" ? (
          <p>{banks}</p>
        ) : (
          <ActionForm>
            <RInput table={"xendit_virtual_bank_account"} field={"data.name"} label={"Bank holder name"} />
            <RInput table={"xendit_virtual_bank_account"} field={"data.xendit_account_id"} type={"hidden"} value={response.account.id} />
            <select name={fName("xendit_virtual_bank_account", "data.bank_code")}>
              {banks.map((bank) => (
                <option key={bank.code} value={bank.code}>
                  {bank.name} ({bank.code})
                </option>
              ))}
            </select>
            <button>submit</button>
          </ActionForm>
        )}
      </DebugContainer>
    </div>
  );
}
