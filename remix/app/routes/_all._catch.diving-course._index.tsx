import { getSessionSimple } from "~/utils/session.server";
import { kysely } from "~/misc/database.server";
import { useLoaderData } from "@remix-run/react";
import { divingCourseTags } from "~/data/diving";
import { Fragment, useEffect, useId, useRef, useState } from "react";
import type { IdName } from "~/misc/models";
import { CSS } from "@dnd-kit/utilities";
import { closestCenter, DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { arrayMove, SortableContext, sortableKeyboardCoordinates, useSortable } from "@dnd-kit/sortable";
import { GrDrag } from "react-icons/gr";
import { v4 } from "uuid";
import { PlusIcon, TrashIcon } from "@heroicons/react/20/solid";
import { CDialog } from "~/components/base/Dialog";
import { Button, SubmitButton, useIsLoading } from "~/components/base/Button";
import type { DB } from "~/kysely/db";
import { AnimatingDiv } from "~/components/base/base";
import { EditorRequired } from "~/components/account/AccountContainer";
import { DeleteButtonForm, RInput, RSelect } from "~/components/ResourceInputs";
import { OperationInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { ActionAlert } from "~/components/ActionAlert";
import { fName } from "~/misc/helpers";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _diving_course } from "~/misc/paths";
import { ActionForm } from "~/components/form/BaseFrom";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import {
  defaultCertificateNames,
  divingCertificateIds,
  divingLevelsz,
  divingOrganizationszz,
} from "~/domain/diving-course/diving-courses.data";
import { unnestArray } from "~/kysely/kysely-helpers";
import { LoaderFunctionArgs } from "@remix-run/router";

export { action } from "~/routes/_all._catch.resource";

const allowedEntitities = ["diving_course"] satisfies ReadonlyArray<keyof DB>;

type AllowedEntity = (typeof allowedEntitities)[number];

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);

  const qb = kysely.selectFrom(unnestArray(divingCertificateIds)).select(["arr.key", "arr.pos"]);
  console.log(qb.compile().sql);
  console.log(await qb.execute());

  const [user, divingCourses] = await Promise.all([
    activeUserSessionSimple(kysely, session_id, true).select("_user.editor").executeTakeFirst(),
    kysely.selectFrom("diving_course").selectAll().orderBy("diving_course.sort_order").execute(),
  ]);

  if (!user?.editor) return null;

  return {
    divingCourses,
  };
};

const DraggableItem = (props: { loading?: boolean; item: IdName; onChange: (value: { name: string }) => void; onDelete?: () => void }) => {
  const sortable = useSortable({ id: props.item.id });
  const loading = useIsLoading();

  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (inputRef.current?.value === "") {
      inputRef.current.focus();
    }
  }, []);

  const style = {
    transform: CSS.Transform.toString(sortable.transform),
    transition: sortable.transition,
  };

  return (
    <div
      className={`flex flex-row items-center rounded-md border bg-slate-100 ${sortable.isDragging ? "z-10" : ""}`}
      ref={sortable.setNodeRef}
      style={style}
      {...sortable.attributes}
    >
      <button disabled={loading} {...sortable.listeners} className="p-3" type="button">
        <GrDrag />
      </button>
      <input
        required
        disabled={loading}
        ref={inputRef}
        className="w-full flex-1 rounded-md bg-transparent p-1 hover:outline-1 hover:ring-1 hover:ring-slate-500"
        value={props.item.name}
        onChange={(e) => {
          props.onChange({ name: e.target.value });
        }}
      />
      <button type="button" className="p-3" disabled={!props.onDelete || loading} onClick={props.onDelete}>
        <TrashIcon className="h-5 w-5" />
      </button>
    </div>
  );
};

const ReorderableForm = (props: { list: IdName[]; tableName: AllowedEntity }) => {
  const formId = useId();
  const [changes, setChanges] = useState<IdName[]>(props.list);
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const isEqual = JSON.stringify(props.list) === JSON.stringify(changes);
  const isLoading = useIsLoading(formId);

  const deletes = props.list.filter((item) => !changes?.map((change) => change.id).includes(item.id));
  return (
    <ActionForm identifier={formId}>
      {/*<IdentifierInput value={props.tableName} />*/}
      <ActionAlert />
      {deletes.map((item, index) => (
        <Fragment key={item.id}>
          <OperationInput table={props.tableName} index={index} value={"delete"} />
          <RInput table={props.tableName} field={"id"} index={index} value={item.id} />
        </Fragment>
      ))}
      {changes.map((change, baseIndex) => {
        const index = baseIndex + deletes.length;
        return (
          <Fragment key={change.id}>
            <OperationInput
              table={props.tableName}
              index={index}
              value={props.list.find((existing) => change.id === existing.id) ? "update" : "insert"}
            />
            <RInput table={props.tableName} field={"id"} index={index} value={change.id} />
            <RInput table={props.tableName} field={"data.sort_order"} index={index} value={index} type={"hidden"} />
            <RInput table={props.tableName} field={"data.name"} index={index} value={change.name} type={"hidden"} />
          </Fragment>
        );
      })}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={(e) => {
          const { active, over } = e;
          if (!over) return;
          if (active.id !== over.id) {
            const oldIndex = changes.findIndex((item) => item.id === active.id);
            const newIndex = changes.findIndex((item) => item.id === over.id);

            const newChanges = arrayMove(changes, oldIndex, newIndex);
            setChanges(newChanges);
          }
        }}
      >
        <SortableContext items={changes}>
          <AnimatingDiv className="grid gap-2 pt-3 pb-6 md:grid-cols-2 lg:grid-cols-4">
            {changes.map((item) => {
              const onDelete = () => setChanges(changes?.filter((change) => change.id !== item.id));
              return (
                <DraggableItem
                  key={item.id}
                  item={item}
                  onDelete={onDelete}
                  onChange={(newChange) => {
                    setChanges(
                      changes?.map((change) => {
                        if (change.id === item.id) return { ...change, ...newChange };
                        return change;
                      }),
                    );
                  }}
                />
              );
            })}
            <button
              disabled={isLoading}
              onClick={() => {
                setChanges([...changes, { id: v4(), name: "" }]);
              }}
              type={"button"}
              className="btn btn-basic flex flex-row items-center"
            >
              add <PlusIcon className="h-5 w-5" />
            </button>
            <div className="flex flex-row items-center">
              <button disabled={isEqual || isLoading} type="button" className="btn btn-basic" onClick={() => setChanges(props.list)}>
                cancel
              </button>
              <Button disabled={isEqual} loading={isLoading} className="btn btn-primary">
                save
              </Button>
            </div>
          </AnimatingDiv>
        </SortableContext>
      </DndContext>
    </ActionForm>
  );
};

export default function Page() {
  const search = useSearchParams2();
  const response = useLoaderData<typeof loader>();

  if (!response) return <EditorRequired />;

  const divingCourseIdOrCreate = search.state.diving_course_id;
  const selectedDivingCourse = response.divingCourses.find((item) => item.id === divingCourseIdOrCreate);

  return (
    <div className="app-container space-y-3 py-3">
      <div>
        <h1 className="text-xl font-bold">Diving certificate levels</h1>
        <p>{Object.values(divingLevelsz).join(", ")}</p>
        {/*<ReorderableForm list={response.divingLevels} tableName={"diving_certificate_level"} />*/}
      </div>
      <div>
        <h1 className="text-xl font-bold">Diving organizations</h1>
        <p>
          {Object.values(divingOrganizationszz)
            .map((org) => org.name)
            .join(", ")}
        </p>
        {/*<ReorderableForm list={response.divingOrganizations} tableName={"diving_certificate_organization"} />*/}
      </div>
      <div className="flex flex-row items-center gap-3">
        <h1 className="text-xl font-bold">Diving courses</h1>
        <ParamLink
          paramState={{
            diving_course_id: undefined,
            toggle_modal: "content",
          }}
          className="inline text-primary hover:underline"
        >
          create
        </ParamLink>
      </div>
      <ol className="list-inside list-decimal text-xs text-slate-600">
        {divingCourseTags.map((tag) => (
          <li key={tag.id}>{tag.name}</li>
        ))}
      </ol>
      {Object.entries(divingLevelsz).map(([levelKey, level]) => (
        <div className="flex flex-col gap-2 pt-2" key={levelKey}>
          <h2 className="font-bold">{level}</h2>
          <div className="grid grid-cols-2 md:grid-cols-4">
            {Object.entries(divingOrganizationszz).map(([orgKey, org]) => {
              const divingCourses = response.divingCourses.filter(
                (divingCourse) =>
                  divingCourse.diving_certificate_level_key === levelKey && divingCourse.diving_certificate_organization_key === orgKey,
              );

              if (divingCourses.length === 0) return <Fragment key={orgKey} />;

              return (
                <div key={orgKey}>
                  <p className="text-slate-700">{org.name}</p>
                  <div className="flex flex-col gap-1 pb-3 pt-1">
                    {divingCourses.map((divingCourse) => {
                      const tag = divingCourseTags.findIndex((tag) => tag.id === divingCourse.tag) + 1;
                      return (
                        <div key={divingCourse.id}>
                          <ParamLink
                            paramState={{
                              diving_course_id: divingCourse.id,
                              toggle_modal: "content",
                            }}
                            className="inline text-left text-primary hover:underline"
                          >
                            {divingCourse.name}
                          </ParamLink>
                          {!!tag && <dfn className="inline-block pl-1 text-xs text-slate-800">{tag}</dfn>}
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ))}
      <CDialog dialogname={"content"}>
        <div className="space-y-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900">
            <div className="flex flex-wrap">
              Diving course (
              {selectedDivingCourse ? <DeleteButtonForm table={"diving_course"} values={[selectedDivingCourse.id]} /> : "Create"})
            </div>
          </h3>
          <ActionForm className="flex flex-col gap-3" preventScrollReset>
            <ActionAlert />
            <RedirectParamsInput path={_diving_course} paramState={{ diving_course_id: null, toggle_modal: undefined }} />
            {selectedDivingCourse && <RInput table={"diving_course"} field={"id"} value={selectedDivingCourse.id} />}
            <RInput
              table={"diving_course"}
              field={"data.name"}
              defaultValue={selectedDivingCourse?.name || ""}
              className="input"
              required
              type="text"
              placeholder="Diving course name"
            />
            <select
              required
              className="input"
              placeholder="Diving certificate level"
              name={fName("diving_course", "data.diving_certificate_level_key")}
              defaultValue={selectedDivingCourse?.diving_certificate_level_key || ""}
            >
              <option value="">Select diving certificate level</option>
              {Object.entries(divingLevelsz).map(([dclKey, dcl]) => (
                <option value={dclKey} key={dclKey}>
                  {dcl}
                </option>
              ))}
            </select>
            <select
              className="input"
              required
              placeholder="Diving certificate organization"
              name={fName("diving_course", "data.diving_certificate_organization_key")}
              defaultValue={selectedDivingCourse?.diving_certificate_organization_key || ""}
            >
              <option value="">Select diving certificate organization</option>
              {Object.entries(divingOrganizationszz).map(([dcoKey, dco]) => (
                <option value={dcoKey} key={dcoKey}>
                  {dco.name}
                </option>
              ))}
            </select>
            <RSelect className="input" table="diving_course" field="data.tag" defaultValue={selectedDivingCourse?.tag || ""}>
              <option value="">-</option>
              {divingCourseTags.map((item) => (
                <option value={item.id} key={item.id}>
                  {item.name}
                </option>
              ))}
            </RSelect>
            <RInput
              table={"diving_course"}
              field={"data.sort_order"}
              className="input"
              type="number"
              placeholder="sort order"
              defaultValue={selectedDivingCourse?.sort_order || ""}
            />
            <div className=" flex flex-row items-center gap-3">
              <div className="flex-1" />
              <ParamLink paramState={{ toggle_modal: undefined }} type="button" className="btn">
                cancel
              </ParamLink>
              <SubmitButton className="btn btn-primary">Save</SubmitButton>
            </div>
          </ActionForm>
        </div>
      </CDialog>
      <div className="overflow-auto">
        <table className="[&_td]:px-3 [&_td]:py-0.5">
          <thead>
            <tr className="font-semibold">
              <td className="sticky left-0 bg-white">ID</td>
              <td>Default</td>
              {Object.entries(divingOrganizationszz).map(([key, divingOrg]) => (
                <td>{divingOrg.name}</td>
              ))}
            </tr>
          </thead>
          <tbody>
            {divingCertificateIds.map((id) => (
              <tr key={id} className="hover:bg-slate-100">
                <td className="sticky left-0 bg-white">{id}</td>
                <td>
                  <div className="whitespace-nowrap">{defaultCertificateNames[id]}</div>
                </td>
                {Object.entries(divingOrganizationszz).map(([key, divingOrg]) => (
                  <td className="whitespace-nowrap" key={key}>
                    {divingOrg.certificates?.[id] || "-"}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
