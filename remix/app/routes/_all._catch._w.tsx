import { createPageOverwrites } from "~/misc/consts";
import React from "react";
import { Outlet } from "@remix-run/react";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import { unauthenticated, unauthorized } from "~/misc/responses";
import { kysely } from "~/misc/database.server";
import { getSessionSimple } from "~/utils/session.server";
import { LoaderFunctionArgs } from "@remix-run/router";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);
  const user = await activeUserSessionSimple(kysely, session_id, false).select("_user_session.verified_at").executeTakeFirst();
  if (!user) throw unauthenticated();
  if (!user.verified_at) throw unauthorized("Email not verified yet");
  return createPageOverwrites({ show_whatsapp: false });
};

export default function Page() {
  return <Outlet />;
}
