import type { DataFunctionArgs } from "@remix-run/server-runtime";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import React from "react";
import { _addon, _product_detail } from "~/misc/paths";
import { ProductItem } from "~/domain/product/ProductItem";
import { baseProductWithSelect } from "~/domain/product/product-queries.server";
import { DeleteButtonForm } from "~/components/ResourceInputs";
import { RedirectInput } from "~/components/form/DefaultInput";
import { ActionForm, defaultEqualCheck } from "~/components/form/BaseFrom";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { AddonForm } from "~/domain/addon/addon-components";
import { DefaultInfoIcon, Tooltip } from "~/components/base/tooltip";
import { notNull } from "~/kysely/kysely-helpers";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: DataFunctionArgs) => {
  const addonId = params.addon_id!;
  return kysely
    .selectFrom("addon")
    .innerJoin("establishment", "establishment.id", "addon.establishment_id")
    .where("addon.id", "=", addonId)
    .selectAll("addon")
    .select((eb) => [
      "establishment.locale",
      notNull(
        jsonObjectFrom(eb.selectFrom("price").select(["price.amount", "price.currency_id"]).whereRef("price.id", "=", "addon.price_id")),
      ).as("price"),
      jsonArrayFrom(
        baseProductWithSelect
          .where("item.establishment_id", "=", eb.ref("addon.establishment_id"))
          .where("product.deleted_at", "=", at_infinity_value)
          .where(eb.ref("addon.id"), "=", (eb) => eb.fn.any("item.addon_ids")),
      ).as("products"),
    ])
    .executeTakeFirstOrThrow();
};

export default function Page() {
  const response = useLoaderData<typeof loader>();
  const redirectPath = _addon(response.establishment_id);

  return (
    <div className="app-container space-y-3">
      <div className="flex flex-row items-center gap-3">
        <h1 className="text-xl font-bold first-letter:capitalize">{response.name} (add-on)</h1>
        <DeleteButtonForm table={"addon"} values={[response.id]} redirect={redirectPath} />
      </div>
      <ActionForm onCheckEqual={defaultEqualCheck}>
        <RedirectInput value={redirectPath} />
        <AddonForm addon={response} />
      </ActionForm>
      <h2 className="text-md font-bold flex flex-row gap-1 items-center">
        Connected Products ({response.products.length}){" "}
        <Tooltip description={<span>Listed below are the activities or products that use this add-on.</span>}>
          <DefaultInfoIcon />
        </Tooltip>
      </h2>
      <div className="grid gap-3 md:grid-cols-2">
        {response.products.map((product) => (
          <ProductItem link={{ path: _product_detail(product.id) }} key={product.id} item={product} locale={response.locale} />
        ))}
      </div>
    </div>
  );
}
