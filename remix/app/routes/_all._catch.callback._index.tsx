import { kysely } from "~/misc/database.server";
import { getCloudTasks } from "~/server/utils/google-task.server";
import { useLoaderData } from "@remix-run/react";
import { getSessionSimple } from "~/utils/session.server";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import { unauthorized } from "~/misc/responses";
import { LoaderFunctionArgs } from "@remix-run/router";
import { ActionForm } from "~/components/form/BaseFrom";
import { SubmitButton } from "~/components/base/Button";
import { RInput } from "~/components/ResourceInputs";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { arrayAggDisinct } from "~/kysely/kysely-helpers";
import { Popover } from "~/components/base/Popover";
import { ParamLink } from "~/components/meta/CustomComponents";
import { paramsToRecord, toggleArray } from "~/misc/parsers/global-state-parsers";
import { CallbackName } from "~/domain/callback/callback";
import { twMerge } from "tailwind-merge";
import { Fragment } from "react";
import { OperationInput } from "~/components/form/DefaultInput";
import { CallbackInput } from "~/domain/callback/callback.components";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const record = paramsToRecord(url.searchParams);
  const { session_id } = await getSessionSimple(request);
  const adminUser = await activeUserSessionSimple(kysely, session_id, true)
    .select((eb) => {
      const allUpcomingTasks = eb.selectFrom("callback").where("callback.handled_at", "is", null);

      const filteredTaks = allUpcomingTasks.$if(record.filter_names.length > 0, (eb) =>
        eb.where("callback.name", "in", record.filter_names as CallbackName[]),
      );
      return [
        allUpcomingTasks.select((eb) => arrayAggDisinct(eb.ref("callback.name")).as("names")).as("task_names"),
        filteredTaks.select((eb) => eb.fn.countAll().as("count")).as("upcoming_tasks_count"),
        jsonArrayFrom(filteredTaks.selectAll("callback").limit(50)).as("tasks"),
      ];
    })
    .where("_user.admin", "=", true)
    .executeTakeFirst();
  if (!adminUser) throw unauthorized();

  const tasks = await getCloudTasks();

  return {
    // localTasks:
    cloudTasks: tasks,
    ...adminUser,
    // localTasks: upcomingTasks,
  };
  // kysely.selectFrom('callback').where('call.id')
};

export default function Page() {
  const search = useSearchParams2();
  const data = useLoaderData<typeof loader>();
  return (
    <div>
      <h2>Cloud tasks</h2>
      <table>
        {data.cloudTasks.map((cloudTask) => {
          //@ts-ignore
          const taskName = cloudTask.name;
          return (
            <tr key={taskName}>
              <td>{taskName}</td>
              <td>{JSON.stringify(cloudTask.scheduleTime)}</td>
              <td>{JSON.stringify(cloudTask.httpRequest)}</td>
            </tr>
          );
        })}
      </table>
      <ActionForm className="p-3 rounded-md border border-slate-300 space-y-3">
        <h2>Create new callback</h2>
        {/* <CallbackInput /> */}
        <RInput type="hidden" table="callback" field="data.name" value="send_notifications" />
        <SubmitButton className="btn btn-primary">Create</SubmitButton>
      </ActionForm>
      <h2>Pending/upcoming tasks ({data.upcoming_tasks_count + ""})</h2>
      <ActionForm>
        <SubmitButton className="btn btn-primary">Save</SubmitButton>
        <table className="text-left">
          <thead>
            <tr>
              <td>#</td>
              <td>Created</td>
              <td>Handled at</td>
              <td>Success</td>
              <td>
                <Popover
                  popoverId={"filter_names"}
                  content={
                    <div>
                      {data.task_names?.map((name) => {
                        return (
                          <ParamLink
                            className="btn btn-basic aria-selected:bg-slate-400"
                            key={name}
                            reload
                            paramState={{ filter_names: toggleArray(search.state.filter_names, name), popover_id: null }}
                            aria-selected={search.state.filter_names.includes(name)}
                          >
                            {name}
                          </ParamLink>
                        );
                      })}
                    </div>
                  }
                >
                  <ParamLink paramState={{ popover_id: "filter_names", filter_names: search.state.filter_names }}>
                    {"name" || search.state.filter_names}
                  </ParamLink>
                </Popover>
              </td>
            </tr>
          </thead>
          <tbody>
            {data.tasks.map((task) => {
              const toDelete = search.state.toggle_delete_ids.includes(task.id);
              return (
                <tr key={task.id}>
                  <td className={twMerge(toDelete && "line-through opacity-60")}>{task.id}</td>
                  <td className={twMerge(toDelete && "line-through opacity-60")}>{task.created_at}</td>
                  <td className={twMerge(toDelete && "line-through opacity-60")}>{task.handled_at}</td>
                  <td
                    className={twMerge(toDelete && "line-through opacity-60 font-bold", task.success ? "text-green-500" : "text-red-500")}
                  >
                    {task.success ? "yes" : "no"}
                  </td>
                  <td className={twMerge(toDelete && "line-through opacity-60")}>{task.name}</td>
                  <td className={twMerge(toDelete && "line-through opacity-60")}>{task.host}</td>
                  <td>
                    {toDelete && (
                      <Fragment>
                        <RInput table={"callback"} field={"id"} index={task.id} value={task.id} />
                        <OperationInput table={"callback"} index={task.id} value={"delete"} />
                      </Fragment>
                    )}
                    <ParamLink
                      className={twMerge("link", toDelete ? "text-slate-500" : "text-red-500")}
                      paramState={{ toggle_delete_ids: toggleArray(search.state.toggle_delete_ids, task.id) }}
                    >
                      {toDelete ? "undo" : "delete"}
                    </ParamLink>
                    {/*<DeleteButtonForm table={"callback"} values={[task.id]} />*/}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </ActionForm>
    </div>
  );
}
