import { useLoaderData } from "@remix-run/react";
import { _diving_location, _diving_location_detail, _diving_site_grade, _explore_base } from "~/misc/paths";
import { ImageGallery } from "~/components/field/ImageGallery";
import { currentRangeValueToText, difficultyLevels, gradingDisclaimer, ranges, tags, units } from "~/domain/diving-site/diving-site";
import React, { Fragment } from "react";
import { rangeValueToText } from "~/components/form/RangeInput";
import type { ActivitySlug } from "~/domain/activity/activity";
import { getActivityImage } from "~/domain/activity/activity";
import { ikUrl, useIkUrl } from "~/components/IkImage";
import { formatMoney } from "~/utils/money";
import { SiteCurrentIcon, SiteDepthIcon, SiteTemperatureIcon, SiteVisibilityIcon } from "~/components/Icons";
import { CheckIcon, InformationCircleIcon } from "@heroicons/react/20/solid";
import { Tooltip } from "~/components/base/tooltip";
import { DivingSitesMap } from "~/domain/diving-site/DivingSitesMap";
import { useDivingLocationAndSites } from "~/routes/_all._catch.diving-location.$diving_location_id";
import { DividerBig } from "~/components/Divider";
import { DifficultyLevel, DivingSiteAccessEntries, DivingSiteTypes } from "~/domain/diving-site/components";
import { entries } from "~/misc/helpers";
import { baseProductQbExplore } from "~/domain/product/product-queries.server";
import { RInput } from "~/components/ResourceInputs";
import { useAppContext } from "~/hooks/use-app-context";
import { useCurrency } from "~/domain/currency/use-currency";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { LoaderFunctionArgs } from "@remix-run/router";
import { kysely } from "~/misc/database.server";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { ActionForm } from "~/components/form/BaseFrom";
import { DeleteButton } from "~/components/base/Button";
import { OperationInput, RedirectInput } from "~/components/form/DefaultInput";
import { ParamLink } from "~/components/meta/CustomComponents";

export { ErrorBoundary } from "~/components/RoutDefaults";

export { action } from "~/routes/_all._catch.resource";

const activitySlugs = ["fun-diving", "snorkeling"] satisfies ReadonlyArray<ActivitySlug>;
const actvityLabels: Record<(typeof activitySlugs)[number], string> = {
  "fun-diving": "Dive trips",
  snorkeling: "Snorkel trips",
};

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const id = params.diving_site_id!;

  return kysely
    .selectFrom("diving_site")
    .where("diving_site.id", "=", id)
    .select((eb) => [
      jsonArrayFrom(
        eb
          .selectFrom("product__diving_site")
          .innerJoin("product", "product__diving_site.product_id", "product.id")
          .select(["product__diving_site.id", "product.deleted_at"])
          .where("product__diving_site.diving_site_id", "=", eb.ref("diving_site.id")),
      ).as("deletable_product_diving_site"),
      jsonArrayFrom(
        baseProductQbExplore
          .where("product.deleted_at", "=", at_infinity_value)
          .innerJoin("product__diving_site", "product.id", "product__diving_site.product_id")
          .where("product__diving_site.diving_site_id", "=", eb.ref("diving_site.id"))
          .where("item.activity_slug", "in", activitySlugs)
          .select((eb) => [
            eb.fn.count("product__diving_site.product_id").as("product_count"),
            eb.fn.min("price.amount_usd").as("lowest_price_in_usd"),
            "item.activity_slug",
          ])
          .groupBy("item.activity_slug"),
      ).as("totals"),
    ])
    .executeTakeFirstOrThrow();
};

export default function Page() {
  const context = useAppContext();
  const { totals, deletable_product_diving_site } = useLoaderData<typeof loader>();
  const totalActiveConnectedProductDivesites = deletable_product_diving_site.map((divesite) => divesite.deleted_at === at_infinity_value);
  const mergedResponses = useDivingLocationAndSites();
  const { finalSelected } = useCurrency();
  const { location, site } = mergedResponses;
  const { fromBucket } = useIkUrl();

  if (!site) return <Fragment />;

  const divingLocationFiles = location.files || [];
  const divingLocationHeroUrl = divingLocationFiles[0] && fromBucket(divingLocationFiles[0]?.filename, "tr:q-80,h-500,w-1980,c-at_least");

  const selectedTags = entries(tags)
    .filter(([tag]) => site[tag])
    .map(([key, label]) => label || key);

  const selectedDifficulty = difficultyLevels
    .map((level, index) => ({ ...level, level: index + 1 }))
    .find((level) => level.level === site.difficulty_level);

  const Difficulty = (props: { className: string }) => (
    <div className={props.className}>
      <DifficultyLevel level={site.difficulty_level} />
      {selectedDifficulty && (
        <div className="flex flex-row items-center justify-end gap-1 pt-1">
          <p>{selectedDifficulty.title}</p>
          <Tooltip
            description={
              <div className="p-2">
                <p className="pl-2">
                  {selectedDifficulty.level} {"->"} {selectedDifficulty.title}
                </p>
                <ul className="list-disc p-2 pl-6">
                  {selectedDifficulty.description.split("\n").map((item) => (
                    <li key={item}>{item}</li>
                  ))}
                </ul>
                <p className="pl-2 pt-2 text-slate-400">{gradingDisclaimer}</p>
                <p className="pt-2 pl-2">
                  <ParamLink className="underline" path={_diving_site_grade} prefetch={"intent"}>
                    Show all grades
                  </ParamLink>
                </p>
              </div>
            }
            className="z-10 inline-block leading-9"
            options={{ placement: "bottom" }}
          >
            <InformationCircleIcon className="h-5 w-5 text-slate-600" />
          </Tooltip>
        </div>
      )}
    </div>
  );
  const highlights = site.highlights ? site.highlights.split(/\r?\n/) : [];

  const files = site.files || [];

  return (
    <main className="space-y-10 py-10">
      {context.editor && (
        <section className="app-container flex flex-wrap gap-3">
          <ParamLink path="update" className="link">
            Edit
          </ParamLink>
          |
          <ActionForm
            confirmMessage={`Diving site ${site.name} will be deleted and disconnected from ${totalActiveConnectedProductDivesites.length} products, are you sure?`}
          >
            <RedirectInput value={_diving_location} />
            {deletable_product_diving_site?.map((productDiveSite) => (
              <Fragment key={productDiveSite.id}>
                <OperationInput table={"product__diving_site"} index={productDiveSite.id} value={"delete"} />
                <RInput table={"product__diving_site"} field={"id"} index={productDiveSite.id} value={productDiveSite.id} />
              </Fragment>
            ))}
            <OperationInput table={"diving_site"} value={"delete"} />
            <RInput table={"diving_site"} field={"id"} value={site.id} />
            <DeleteButton />
          </ActionForm>
        </section>
      )}
      <section className="app-container space-y-4">
        <Difficulty className="flex flex-wrap items-center gap-3 pb-2 md:hidden" />
        <div className="flex flex-wrap justify-between gap-x-10 gap-y-2">
          <div className="space-y-2">
            <DivingSiteAccessEntries divingsite={site} />
            <DivingSiteTypes divingsite={site} />
            <div>
              <div className="flex flex-row gap-3">
                <div className="h-7 w-10 rounded-md bg-secondary-600">
                  <SiteDepthIcon className="h-full w-full" />
                </div>
                <label>{rangeValueToText(ranges.depth_range_in_meters.unit, site.depth_range_in_meters)}</label>
              </div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex flex-row gap-3">
              <div className="h-7 w-10 rounded-md bg-secondary-600">
                <SiteCurrentIcon className="h-full w-full" />
              </div>
              <label>{currentRangeValueToText(site.current_range_in_level) || "unkown"}</label>
            </div>
            <div className="flex flex-row gap-3">
              <div className="h-7 w-10 rounded-md bg-secondary-600">
                <SiteTemperatureIcon className="h-full w-full" />
              </div>
              <label>{rangeValueToText(units[ranges.temperature_range_in_celsius.unit], site.temperature_range_in_celsius)}</label>
            </div>
            <div className="flex flex-row gap-3">
              <div className="h-7 w-10 rounded-md bg-secondary-600">
                <SiteVisibilityIcon className="h-full w-full" />
              </div>
              <label>{rangeValueToText(ranges.visibility_range_in_meters.unit, site.visibility_range_in_meters)}</label>
            </div>
          </div>
          <Difficulty className="hidden flex-col items-end gap-3 md:flex" />
        </div>
        {selectedTags.length > 0 && <p className="capitalize text-slate-500">Tags: {selectedTags.join(", ")}</p>}
        <ImageGallery fileNames={site.files.map((file) => file.filename)} />
      </section>
      <DividerBig />
      <section className="app-container">
        <h2 className="text-xl font-bold">About {site.name}</h2>
        {site.description && <p className="whitespace-pre-wrap">{site.description}</p>}
      </section>
      <DividerBig />
      <DivingSitesMap />
      <DividerBig />
      {highlights.length > 0 && (
        <section className="app-container space-y-3">
          <h2 className="space-y-3 pb-4 text-xl font-semibold">Marine life</h2>
          <div className="flex flex-wrap justify-between gap-x-10 gap-y-6">
            <div>
              <ul>
                {highlights.map((hightlight, index) => (
                  <li key={index} className="flex flex-row items-center leading-7">
                    <CheckIcon className="h-5 w-5 text-green-700" />
                    {hightlight}
                  </li>
                ))}
              </ul>
            </div>
            {!!files[4] && (
              <img
                src={fromBucket(files[4]?.filename, "tr:q-80,h-140,w-200,c-at_least")}
                alt=""
                className="h-40 w-64 rounded-md object-cover"
              />
            )}
          </div>
        </section>
      )}
      <section className="app-container space-y-3">
        <h2 className="text-xl font-bold">Activities at {site.name}</h2>
        <div className="flex flex-wrap gap-6">
          {totals.map((total) => {
            const lowerPrice = formatMoney(context, {
              nativeAmount: total.lowest_price_in_usd || 0,
              nativeCurrency: "USD",
              toCurrency: finalSelected,
              locale: null,
            });
            const activitySlug = total.activity_slug as keyof typeof actvityLabels;
            return (
              <ParamLink
                key={total.activity_slug}
                path={_explore_base}
                paramState={{
                  diving_locations: [site.diving_location_id],
                  diving_sites: [site.id],
                  activity_slug: activitySlug,
                }}
                className="w-full overflow-hidden rounded-md border border-secondary-500 md:w-80 lg:w-96"
              >
                <img
                  alt={total.activity_slug}
                  src={ikUrl(getActivityImage(total.activity_slug), `tr:w-540,h-400`)}
                  className="h-[100px] w-full object-cover md:h-[150px]"
                />
                <div className="p-3">
                  <p className="font-bold">
                    {actvityLabels[activitySlug]} to {site.name}
                  </p>
                  <div className="flex flex-wrap justify-between">
                    <div>
                      <p>{total.product_count === "1" ? "activity" : total.product_count + " activities"}</p>
                      <p>From {lowerPrice}</p>
                    </div>
                    <div className="btn btn-primary">view</div>
                  </div>
                </div>
              </ParamLink>
            );
          })}
        </div>
      </section>
      <section className="app-container">
        <div className="rounded-md bg-cover bg-center object-fill " style={{ backgroundImage: `url(${divingLocationHeroUrl})` }}>
          <div className="space-y-6  p-6">
            <h2 className="image-text text-xl font-bold text-white">Read about {location.name}</h2>
            <ParamLink path={_diving_location_detail(site.diving_location_id)} className="btn btn-basic w-fit">
              Read more
            </ParamLink>
          </div>
        </div>
      </section>
    </main>
  );
}
