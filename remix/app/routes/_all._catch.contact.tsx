import { MdEmail } from "react-icons/md";
import React from "react";
import { ikUrl } from "~/components/IkImage";
import type { LinksFunction } from "@remix-run/server-runtime";
import { traveltrusterName } from "~/misc/consts";

export { action } from "~/routes/_all._catch.resource";

const heroUrl = ikUrl("/images/women-on-a-rock-at-sea-typing-on-laptop-cropped.jpeg", "tr:q-80,h-500,w-1980,c-at_least");
const feedbackImgUrl = ikUrl("/images/some-people-at-desk-looking-at-art.jpeg", "tr:q-80,h-150,w-250");
const contactImgUrl = ikUrl("/images/woman-with-happy-face-balloon.jpeg", "tr:q-80,h-150,w-250");

export const links: LinksFunction = () => [
  {
    as: "image",
    href: heroUrl,
    rel: "preload",
  },
];

const MailButton = (props: { children: string }) => (
  <a {...props} target="_blank" href="mailto:<EMAIL>" className="btn btn-primary w-full">
    <MdEmail />
    <span>{props.children}</span>
  </a>
);

export default function Page() {
  return (
    <div className="text-slate-800">
      <div className="bg-cover bg-center object-fill" style={{ backgroundImage: `url(${heroUrl})` }}>
        <div className="flex h-80 w-full content-center items-center bg-black/30">
          <div className="app-container h-auto space-y-2">
            <h1 className="text-2xl font-semibold tracking-wide text-white drop-shadow-md md:text-5xl">Get in touch with our team</h1>
          </div>
        </div>
      </div>
      <section className="app-container flex flex-row gap-10 py-10">
        <div className="flex flex-1 flex-col space-y-6">
          <h2 className="text-xl font-bold">Feedback</h2>
          <p>
            Is there anything missing? Something you would like to see?
            <br />
            Let us know and help us improve out platform.
          </p>
          <MailButton>Leave feedback</MailButton>
        </div>
        <img className="hidden rounded md:block" src={feedbackImgUrl} />
      </section>
      <div className="bg-secondary-50">
        <section className="app-container flex flex-row gap-10 py-10">
          <div className="flex flex-1 flex-col gap-4">
            <h2 className="text-xl font-bold">Contact</h2>
            <p>
              Get in touch with the {traveltrusterName} team.
              <br />
              We usually respond within 2 hours.
            </p>
            <MailButton>Get in touch</MailButton>
          </div>
          <img className="hidden rounded md:block" src={contactImgUrl} />
        </section>
      </div>
    </div>
  );
}
