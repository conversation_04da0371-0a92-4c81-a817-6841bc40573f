import { LoaderFunctionArgs } from "@remix-run/router";
import { kysely } from "~/misc/database.server";
import { notFound } from "~/misc/responses";
import { getInvoice } from "~/domain/payment/xendit-client.server";
import { useLoaderData } from "@remix-run/react";

export const loader = async (args: LoaderFunctionArgs) => {
  const paymentId = args.params.payment_id!;

  const payment = await kysely
    .selectFrom("payment")
    .innerJoin("booking", "booking.id", "payment.booking_id")
    .innerJoin("establishment", "establishment.id", "booking.establishment_id")
    .innerJoin("xendit_account", "xendit_account.id", "establishment.xendit_account_id")
    .innerJoin("xendit_platform", "xendit_platform.id", "xendit_account.xendit_platform_id")
    .innerJoin("xendit_environment", "xendit_environment.xendit_platform_id", "xendit_platform.id")
    .whereRef("xendit_environment.production", "=", "xendit_account.production")
    .where("payment.id", "=", paymentId)
    .selectAll("payment")
    .select(["xendit_account.xendit_user_id", "xendit_environment.xendit_api_key"])
    .executeTakeFirst();

  if (!payment) throw notFound();

  const invoice = await getInvoice({
    apiKey: payment.xendit_api_key,
    invoiceId: payment.xendit_invoice_id!,
    headers: { "for-user-id": payment.xendit_user_id },
  });
  return invoice;
};

export default function Page() {
  const data = useLoaderData();
  return (
    <div>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  );
}
