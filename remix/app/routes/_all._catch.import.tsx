import { useMemo, useState } from "react";
import { ParamLink } from "~/components/meta/CustomComponents";
import { entries } from "~/misc/helpers";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { LoaderFunctionArgs } from "@remix-run/router";
import { createPageOverwrites } from "~/misc/consts";
import { Kysely } from "kysely";
import { DB } from "~/kysely/db";
import { RInput } from "~/components/ResourceInputs";
import { ActionForm } from "~/components/form/BaseFrom";
import { SubmitButton } from "~/components/base/Button";
import { kysely } from "~/misc/database.server";
import { useLoaderData } from "@remix-run/react";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const imports = await kysely
    .selectFrom("import")
    .select((eb) => [
      "import.id",
      "import.name",
      eb
        .selectFrom("import_row")
        .select((eb) => [eb.fn.count<number>("import_row.id").as("row_count")])
        .as("row_count"),
    ])
    .execute();
  return {
    ...createPageOverwrites({ fixed_width: true }),
    imports: imports,
  };
};

// Parse a CSV line with semicolon delimiter and quote escaping
const parseLine = (line: string): string[] => {
  const result: string[] = [];
  let current = "";
  let inQuotes = false;
  let i = 0;

  while (i < line.length) {
    const char = line[i];

    if (char === '"') {
      if (inQuotes && line[i + 1] === '"') {
        // Escaped quote (double quote)
        current += '"';
        i += 2;
      } else {
        // Toggle quote state
        inQuotes = !inQuotes;
        i++;
      }
    } else if (char === ";" && !inQuotes) {
      // Field separator
      result.push(current);
      current = "";
      i++;
    } else {
      current += char;
      i++;
    }
  }

  // Add the last field
  result.push(current);
  return result;
};

function csvToJson(csvText: string): Record<string, string>[] {
  const lines = csvText.trim().split("\n");
  if (lines.length < 2) return [];

  // Parse header row to get column names
  const headers = parseLine(lines[0] || '');

  // Parse data rows and create objects
  const data: Record<string, string | null>[] = [];
  for (let i = 1; i < lines.length; i++) {
    const values = parseLine(lines[i]);
    const obj: Record<string, string> = {};

    headers.forEach((header, index) => {
      obj[header] = values[index] || null;
    });

    data.push(obj);
  }

  return data;
}

const segmentKeys = ["csv", "json"] as const;
type SegmentKey = (typeof segmentKeys)[number];

const Uploaded = (props: { fileContent: string }) => {
  const search = useSearchParams2();
  const fileContent = props.fileContent;

  const converted = useMemo(() => {
    return csvToJson(fileContent);
  }, [fileContent]);

  const segments = {
    csv: {
      title: "CSV",
      body: <pre>{fileContent}</pre>,
    },
    json: {
      title: "JSON",
      body: <pre>{JSON.stringify(converted, null, 2)}</pre>,
    },
  } satisfies Record<SegmentKey, any>;
  // Convert CSV text to JSON array of objects
  const activeSegmentKey = segmentKeys.find((key) => key === search.state.tab) || ("csv" satisfies SegmentKey);
  const activeSegment = segments[activeSegmentKey];

  return (
    <div>
      <div className="flex flex-wrap gap-3 py-2">
        {entries(segments).map(([key, value]) => {
          return (
            <ParamLink
              key={key}
              paramState={{ tab: key }}
              aria-selected={activeSegmentKey === key}
              className="btn btn-basic aria-selected:btn-secondary"
            >
              {value.title}
            </ParamLink>
          );
        })}
      </div>
      <div className="overflow-auto">{activeSegment.body}</div>
    </div>
  );
};

const Body = () => {
  const data = useLoaderData<typeof loader>();
  const [file, setFile] = useState<[name: string, content: string] | null>(null);
  // const [jsonData, setJsonData] = useState<Record<string, string>[] | null>(null);

  if (!file)
    return (
      <div className="space-y-3">
        <input
          type={"file"}
          onChange={(e) => {
            console.log("fiel", e.target.files);
            //   get file contents
            const file = e.target.files?.[0];
            if (!file) return;
            const reader = new FileReader();
            reader.onload = (event) => {
              const text = event.target?.result;
              if (typeof text === "string") {
                setFile([file.name, text]);
              }
            };
            reader.readAsText(file);
          }}
        />
        <div>
          <h2 className="text-xl font-semibold">Imported: </h2>
          <div>
            {data.imports.map((item) => {
              return (
                <div key={item.id}>
                  {item.name} ({item.row_count} rows)
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );

  return (
    <div className="space-y-3">
      <ActionForm className="flex flex-row gap-3 py-3">
        <RInput table={"import"} field={"data.rows"} type={"hidden"} value={JSON.stringify(file[1])} />
        <RInput
          table={"import"}
          field={"data.name"}
          className={"input w-full md:w-96"}
          defaultValue={file[0]}
          placeholder={"Import name"}
        />
        <button type={"button"} className="btn btn-basic" onClick={() => setFile(null)}>Cancel</button>
        <SubmitButton className="btn btn-primary">Save</SubmitButton>
      </ActionForm>
      <Uploaded fileContent={file[1]} />;
    </div>
  );
};

export default function Page() {
  return (
    <div className="px-6">
      <h1 className="font-semibold text-xl">Import</h1>
      <Body />
    </div>
  );
}
