import { kysely } from "~/misc/database.server";
import { useLoaderData } from "@remix-run/react";
import { _form_mutate, _register } from "~/misc/paths";
import { ParamLink } from "~/components/meta/CustomComponents";
import { RInput } from "~/components/ResourceInputs";
import { Fragment } from "react";
import { DividerWithText } from "~/components/Divider";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { LoaderFunctionArgs } from "@remix-run/router";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { ascNullsLast } from "~/kysely/kysely-helpers";
import { myGroupBy2 } from "~/misc/helpers";
import { ActionForm } from "~/components/form/BaseFrom";
import { DeleteButton, SubmitButton } from "~/components/base/Button";
import { ChevronUpIcon } from "@heroicons/react/20/solid";
import { twMerge } from "tailwind-merge";
import { AnimatingDiv } from "~/components/base/base";
import { isEditorQb, memberIsAdminQb, QbArgs } from "~/domain/member/member-queries.server";
import { getSessionSimple } from "~/utils/session.server";
import { jsonArrayFrom } from "kysely/helpers/postgres";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const record = paramsToRecord(url.searchParams);
  const session = await getSessionSimple(request);

  const establishmentId = record.persist_establishment_id;

  const args: QbArgs = {
    trx: kysely,
    ctx: {
      session_id: session.session_id,
    },
  };

  const forms = await kysely
    .selectFrom("form")
    .where((eb) => {
      const defaultCmpr = eb("form.establishment_id", "is", null);
      if (!establishmentId) return defaultCmpr;
      return eb.or([defaultCmpr, eb("form.establishment_id", "=", establishmentId)]);
    })
    .where("form.deleted_at", "=", at_infinity_value)
    .selectAll("form")
    .select((eb) => [
      jsonArrayFrom(
        eb
          .selectFrom("waiver")
          .innerJoin("form_waiver", "form_waiver.waiver_id", "waiver.id")
          .select(["waiver.id", "waiver.slug"])
          .where("form_waiver.form_id", "=", eb.ref("form.id")),
      ).as("waivers"),
    ])
    .select((eb) =>
      eb
        .or([
          eb.and([eb("form.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"))]),
          eb.and([eb("form.establishment_id", "is", null), eb.exists(isEditorQb(args))]),
        ])
        .as("canEdit"),
    )
    .orderBy("form.establishment_id", ascNullsLast)
    .orderBy("form.sort_order", ascNullsLast)
    .orderBy("form.name")
    .execute();

  return {
    forms: forms,
  };
};

const SortButton = (props: { ids: string[]; currentIndex: number; newIndex: number }) => {
  const currentValue = props.ids[props.currentIndex];
  const otherValue = props.ids[props.newIndex];

  if (!currentValue || !otherValue) return <Fragment />;

  const newIds = [...props.ids];
  newIds[props.currentIndex] = otherValue;
  newIds[props.newIndex] = currentValue;

  return (
    <ActionForm>
      {newIds.map((id, index) => (
        <Fragment>
          <RInput table={"form"} field={"id"} index={id} value={id} />
          <RInput table={"form"} field={"data.sort_order"} index={id} value={index} type={"hidden"} />
        </Fragment>
      ))}
      <SubmitButton className="aria-busy:spinner spinner-dark py-1 px-2">
        <ChevronUpIcon className={twMerge("w-4 h-4", props.newIndex > props.currentIndex && "rotate-180")} />
      </SubmitButton>
    </ActionForm>
  );
};

export default function Page() {
  const search = useSearchParams2();
  const data = useLoaderData<typeof loader>();
  return (
    <div className="app-container space-y-6">
      <h2 className="text-xl font-semibold">Registration form(s)</h2>
      <AnimatingDiv className="grid w-fit grid-cols-[repeat(5,auto)] gap-6">
        {myGroupBy2(data.forms, (form) => (form.establishment_id ? "Custom forms" : "Default forms")).map((formGroup) => (
          <Fragment key={formGroup.groupKey}>
            <div className="col-span-5">
              <DividerWithText>{formGroup.groupKey}</DividerWithText>
            </div>
            {formGroup.items.map((form, index) => {
              const formIds = formGroup.items.map((item) => item.id);
              return (
                <Fragment key={form.id}>
                  <div>
                    <ParamLink
                      className="link"
                      path={_form_mutate}
                      paramState={{
                        id: form.id,
                        action_type: "view",
                        establishment_id: search.state.persist_establishment_id,
                      }}
                    >
                      {form.name}
                    </ParamLink>
                    {!!form.waivers.length && false && (
                      <ul>
                        {form.waivers.map((waiver) => (
                          <li key={waiver.id}>{waiver.slug}</li>
                        ))}
                      </ul>
                    )}
                  </div>
                  <div>
                    <ParamLink
                      className="link"
                      hidden={!form.canEdit}
                      path={_form_mutate}
                      paramState={{ id: form.id, establishment_id: search.state.persist_establishment_id }}
                    >
                      edit
                    </ParamLink>
                  </div>
                  <div>
                    <ParamLink
                      className="link"
                      path={_form_mutate}
                      paramState={{
                        id: form.id,
                        action_type: "copy",
                        establishment_id: search.state.persist_establishment_id,
                      }}
                    >
                      copy
                    </ParamLink>
                  </div>
                  <div>
                    {form.canEdit && (
                      <ActionForm>
                        <RInput table={"form"} field={"id"} value={form.id} />
                        <RInput table={"form"} field={"data.deleted_at"} value={"now"} type={"hidden"} />
                        <DeleteButton />
                      </ActionForm>
                    )}
                  </div>
                  <div>
                    {form.canEdit && (
                      <div className="flex flex-row">
                        <SortButton ids={formIds} currentIndex={index} newIndex={index - 1} />
                        <SortButton ids={formIds} currentIndex={index} newIndex={index + 1} />
                      </div>
                    )}
                  </div>
                </Fragment>
              );
            })}
          </Fragment>
        ))}
        {!!search.state.persist_establishment_id && (
          <ParamLink className={"btn btn-primary"} path={_register(search.state.persist_establishment_id)}>
            Generic customer registration page
          </ParamLink>
        )}
      </AnimatingDiv>
    </div>
  );
}
