import { kysely } from "~/misc/database.server";
import { useLoaderD<PERSON>, useNavigate, useNavigation } from "@remix-run/react";
import React, { Fragment, useEffect, useRef, useState } from "react";
import { InputFilesDefault } from "~/components/form/FilesInput";
import { MapPortal } from "~/components/MapPortal";
import { FaPhoneAlt, FaWhatsapp } from "react-icons/fa";
import { MdEmail } from "react-icons/md";
import { TbWorld } from "react-icons/tb";
import { _establishment_detail, _establishment_path } from "~/misc/paths";
import { PlusIcon } from "@heroicons/react/20/solid";
import autoAnimate from "@formkit/auto-animate";
import { arrayAgg, ascNullsLast, stAsGeoJsonPoint } from "~/kysely/kysely-helpers";
import { RInput, RLabel, RSelect, RTextarea } from "~/components/ResourceInputs";
import { fName, keys, tableIdRef } from "~/misc/helpers";
import { Button } from "~/components/base/Button";
import { DefaultFieldInputGeom } from "~/components/form/DefaultFieldInputGeom";
import { RedirectInput } from "~/components/form/DefaultInput";
import { ActionAlert } from "~/components/ActionAlert";
import { ActionForm } from "~/components/form/BaseFrom";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { useAppContext } from "~/hooks/use-app-context";
import { LoaderFunctionArgs } from "@remix-run/router";
import { countries, heightMetrics, shoeSizeUnits, weightMetrics } from "~/data/countries";
import { flat, unique } from "remeda";
import { LanguageSelectMultiSimple } from "~/domain/language/LanguageSelectMultiSimple";
import { PaymentInitFields } from "~/domain/payment/PaymentInitFields";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { workflowOptions } from "~/domain/establishment/helpers";
import { directBookingModes } from "~/domain/booking/booking-vars";
import { DefaultInfoIcon, Tooltip } from "~/components/base/tooltip";
import { locales } from "~/data/languages";
import { meetingTypes } from "~/domain/planning/plannings-consts";
import { ParamLink } from "~/components/meta/CustomComponents";
import { ValidityDurationField, ValidityDurationSelect } from "~/components/duration";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const id = params.id!;
  const operatorLocationQb = kysely
    .selectFrom("establishment")
    .innerJoin("operator", "operator.id", "establishment.operator_id")
    .leftJoin("spot", "spot.id", "establishment.spot_id")
    .leftJoin("region", "region.id", "spot.region_id")
    .selectAll("establishment")
    .select((eb) => [
      "operator.name as operator_name",
      "establishment.id as establishment_id",
      "establishment.location_name as establishment_name",
      "spot.name as spot_name",
      jsonArrayFrom(
        eb
          .selectFrom("form")
          .where("form.deleted_at", "=", at_infinity_value)
          .where((eb) => eb.or([eb("form.establishment_id", "is", null), eb("form.establishment_id", "=", eb.ref("establishment.id"))]))
          .orderBy("form.establishment_id", ascNullsLast)
          .orderBy("form.sort_order", ascNullsLast)
          .orderBy("form.name")
          .selectAll("form"),
      ).as("forms"),
      stAsGeoJsonPoint(eb.ref("establishment.geom")).as("geom"),
      jsonArrayFrom(fileTargetsQb(kysely, "establishment", eb.ref("establishment.id"))).as("files"),
      eb
        .selectFrom("establishment__language")
        .select((eb) => arrayAgg(eb.ref("establishment__language.language_code")).as("language_codes"))
        .where("establishment__language.establishment_id", "=", eb.ref("establishment.id"))
        .as("language_codes"),
    ])
    .where("establishment.id", "=", id);
  const regionsQb = kysely
    .selectFrom("region")
    .select((eb) => [
      "region.id",
      "region.name",
      "region.country_code",
      "region.published",
      jsonArrayFrom(eb.selectFrom("spot").selectAll("spot").where("spot.region_id", "=", eb.ref("region.id")).orderBy("spot.name")).as(
        "spots",
      ),
    ])
    .orderBy("region.name")
    .orderBy("region.published", "desc");
  const operatorsQb = kysely.selectFrom("operator").selectAll().orderBy("operator.name");

  const [operatorLocation, regions, operators] = await Promise.all([
    operatorLocationQb.executeTakeFirst(),
    regionsQb.execute(),
    operatorsQb.execute(),
  ]);

  return {
    item: operatorLocation,
    regions: regions,
    operators: operators,
  };
};

const newOperatorIdRef = tableIdRef("operator", -1);

const SpotSelect = () => {
  const response = useLoaderData<typeof loader>();
  const regions = response.regions;
  const defaultValue = response.item?.spot_id || "";
  const [selectedId, setSelectedId] = useState(defaultValue);
  const spot = flat(regions.map((region) => region.spots)).find((spot) => spot.id === selectedId);
  const region = regions.find((region) => region.id === (spot?.region_id || selectedId));
  const country = countries.find((country) => country.country_code === (region?.country_code || selectedId));

  const filteredRegions = country ? regions.filter((region) => region.country_code === country.country_code) : [];
  const filteredSpots = region ? region.spots : [];

  return (
    <div>
      <span>Spot</span>
      <div className="formcontrol flex flex-wrap items-center pl-2">
        <select
          className="formcontrol-input flex-1"
          required
          value={country?.country_code || ""}
          onChange={(e) => setSelectedId(e.target.value)}
        >
          <option value="" selected disabled>
            Country
          </option>
          {unique(regions.map((region) => region.country_code)).map((countryCode) => {
            const country = countries.find((country) => country.country_code === countryCode);
            return (
              <option key={countryCode} value={countryCode}>
                {country?.country_name || "unkown"}
              </option>
            );
          })}
        </select>
        <select className="formcontrol-input flex-1" required value={region?.id || ""} onChange={(e) => setSelectedId(e.target.value)}>
          <option value="" selected disabled>
            Region
          </option>
          {filteredRegions.map((regions) => (
            <option key={regions.id} value={regions.id}>
              {regions.name}
            </option>
          ))}
        </select>
        <select
          className="formcontrol-input flex-1"
          name={fName("establishment", "data.spot_id")}
          required
          placeholder="select spot"
          value={spot?.id || ""}
          onChange={(e) => setSelectedId(e.target.value)}
        >
          <option value="" disabled selected>
            Spot
          </option>
          {filteredSpots.map((spot) => (
            <option key={spot.id} value={spot.id}>
              {spot.name}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

// export const BookingMessages = () => {
//   const response = useLoaderData<typeof loader>();
//
//   return <div>
//     {response.item?.booking_messages?.map(((msg, index) => {
//       return <RTextarea key={index} table={"establishment"} field={"data.booking_messages"} defaultValue={msg} />;
//     })}
//     <RTextarea table={"establishment"} field={"data.booking_messages"} />
//     <ParamLink paramState={{}} replace>add</ParamLink>
//   </div>;
// };

export default function Page() {
  const app = useAppContext();
  const response = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const navigation = useNavigation();
  const isLoading = !!navigation.formData;

  const [newOperatorName, setNewOperatorName] = useState<string | null>(null);
  const [previousOperatorId, setPreviousOperatorId] = useState<string | null>(null);
  const [currentOperatorId, setCurrentOperatorId] = useState(response.item?.operator_id || "");
  const newOperatorNameInputRef = useRef<HTMLInputElement>(null);

  const animatingParentRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    animatingParentRef.current && autoAnimate(animatingParentRef.current);
  }, []);

  useEffect(() => {
    if (currentOperatorId === newOperatorIdRef) {
      newOperatorNameInputRef.current?.focus();
    }
  }, [currentOperatorId]);

  return (
    <ActionForm>
      <RedirectInput value={_establishment_detail(tableIdRef("establishment"))} />
      {!!response.item?.id ? (
        <RInput table={"establishment"} field={"id"} value={response.item.id} />
      ) : (
        <Fragment>
          <PaymentInitFields />
        </Fragment>
      )}
      <div className="app-container space-y-6 pb-6">
        <div className="sticky top-0 z-10 flex flex-row items-center gap-3 bg-white py-3">
          <h1 className="text-xl font-bold text-slate-800 line-clamp-2">{response.item ? "Edit" : "Create operator location"}</h1>
          <div className="flex-1" />
          <ParamLink
            className="hover:underline"
            type="button"
            path={_establishment_path}
            onClick={(e) => {
              e.preventDefault();
              navigate(-1);
            }}
          >
            cancel
          </ParamLink>
          <Button loading={isLoading} className="btn btn-primary">
            Save
          </Button>
        </div>
        <ActionAlert scrollTo />
        {app.editor && (
          <div className="formcontrol flex flex-wrap items-center gap-3 overflow-hidden" ref={animatingParentRef}>
            <label className="flex flex-1 flex-row items-center">
              <span className="pr-2 pl-3 font-semibold">Operator</span>
              <RSelect
                table={"establishment"}
                field={"data.operator_id"}
                value={currentOperatorId}
                onChange={(e) => {
                  setPreviousOperatorId(currentOperatorId);
                  setCurrentOperatorId(e.target.value);
                  if (e.target.value === newOperatorIdRef) {
                    setNewOperatorName("");
                  }
                }}
                required
                className="input-clean flex-1 p-0"
                disabled={isLoading}
              >
                <option value="">select operator</option>
                {typeof newOperatorName === "string" && (
                  <option value={newOperatorIdRef}>{newOperatorName} (will be created on save)</option>
                )}
                {response.operators.map((operator) => (
                  <option key={operator.id} value={operator.id}>
                    {operator.name}
                  </option>
                ))}
              </RSelect>
            </label>
            {typeof newOperatorName === "string" ? (
              <div className="flex flex-1 flex-row items-center">
                <RInput
                  table={"operator"}
                  field={"data.name"}
                  index={-1}
                  required
                  myref={newOperatorNameInputRef}
                  className="formcontrol-input flex-1 p-2"
                  placeholder="Operator name"
                  value={newOperatorName}
                  onChange={(e) => setNewOperatorName(e.target.value)}
                />
                {/*<InitDefaultViewsInputs operator_id={tableRef("operator")} />*/}
                <button
                  type={"button"}
                  className="p-2 text-sm"
                  onClick={() => {
                    setCurrentOperatorId(previousOperatorId || "");
                    setNewOperatorName(null);
                  }}
                >
                  cancel
                </button>
              </div>
            ) : (
              <div className="p-1">
                <button
                  className="btn btn-secondary flex flex-row items-center p-1 text-sm"
                  type={"button"}
                  onClick={() => {
                    setNewOperatorName("");
                    setPreviousOperatorId(currentOperatorId);
                    setCurrentOperatorId(newOperatorIdRef);
                  }}
                >
                  create <PlusIcon className="h-5 w-5" />
                </button>
              </div>
            )}
          </div>
        )}
        <InputFilesDefault target={"establishment"} target_id={tableIdRef("establishment")} defaultValue={response.item?.files} />
        {app.editor && <SpotSelect />}
        {app.editor && (
          <label className="flex flex-col">
            <div>
              <span>Location name</span>&nbsp;
              <span className="text-sm text-slate-600">
                (Only use this when there are multiple establishments of the same operator in one spot)
              </span>
            </div>
            <RInput
              table={"establishment"}
              field={"data.location_name"}
              defaultValue={response.item?.location_name || ""}
              className="input"
              disabled={isLoading}
            />
          </label>
        )}
        {app.editor && (
          <div className="flex h-[400px] flex-col md:h-[300px]">
            <MapPortal>
              <div className="absolute bottom-0 left-0 right-0 gap-3">
                <div className="relative z-10 flex w-full flex-col gap-3 p-3 md:flex-row">
                  <label className="formcontrol flex flex-1 flex-wrap items-center bg-white">
                    <span className="pl-2 font-semibold">Address</span>
                    <RInput
                      defaultValue={response.item?.address || ""}
                      table={"establishment"}
                      field={"data.address"}
                      disabled={isLoading}
                      className="input-clean min-w-[300px] flex-1"
                      required
                    />
                  </label>
                  <DefaultFieldInputGeom defaultValue={response.item?.geom || ""} name={fName("establishment", "data.geom")} required />
                </div>
              </div>
            </MapPortal>
          </div>
        )}
        <div className="grid gap-3 md:grid-cols-2">
          <label className="formcontrol flex flex-row items-center gap-2 pl-2">
            <FaWhatsapp />
            <span>Whatsapp: </span>
            <RInput
              table={"establishment"}
              defaultValue={response.item?.whatsapp || ""}
              field={"data.whatsapp"}
              disabled={isLoading}
              type="tel"
              className="input-clean flex-1"
            />
          </label>
          <label className="formcontrol flex flex-row items-center gap-2 pl-2">
            <FaPhoneAlt />
            <span>Telephone: </span>
            <RInput
              defaultValue={response.item?.telephone || ""}
              field={"data.telephone"}
              table={"establishment"}
              disabled={isLoading}
              type="tel"
              className="input-clean flex-1"
            />
          </label>
          <label className="formcontrol flex flex-row items-center gap-2 pl-2">
            <MdEmail />
            <span>Email: </span>
            <RInput
              defaultValue={response.item?.email || ""}
              field={"data.email"}
              table={"establishment"}
              disabled={isLoading}
              type="email"
              className="input-clean flex-1"
            />
          </label>
          <label className="formcontrol flex flex-row items-center gap-2 pl-2">
            <TbWorld />
            <span>Website: </span>
            <RInput
              pattern={"https?://.*"}
              table={"establishment"}
              field={"data.website"}
              title="Url starting with https:// or http://"
              placeholder="https://www.mywebsite.com"
              type="url"
              onInvalid={(e) => {
                e.currentTarget.setCustomValidity(
                  "Enter a valid url.\nIt should start with https://\nFor example: https://www.example.com.",
                );
              }}
              onInput={(e) => {
                e.currentTarget.setCustomValidity("");
              }}
              className="input-clean w-full"
              defaultValue={response.item?.website || ""}
              disabled={isLoading}
            />
          </label>
        </div>
        <label className="block">
          <span>Bio</span>
          <br />
          <RInput
            table={"establishment"}
            field={"data.bio"}
            defaultValue={response.item?.bio || ""}
            type="text"
            className="input"
            disabled={isLoading}
          />
        </label>
        <label className="block">
          <span>About us</span>
          <br />
          <RTextarea
            table={"establishment"}
            field={"data.about"}
            className="input min-h-[200px]"
            defaultValue={response.item?.about || ""}
            disabled={isLoading}
          />
        </label>
        <LanguageSelectMultiSimple label="In our Dive Center we speak" initialLanguageCodes={response.item?.language_codes || []} />
        {/*<LanguageSelectMulti label="In our Dive Center we speak" initialLanguageCodes={response.item?.language_codes || []} />*/}
        {app.editor && (
          <Fragment>
            <div>
              <RInput
                className="input"
                table={"establishment"}
                field={"data.vat_number"}
                label={"VAT number"}
                defaultValue={response.item?.vat_number || ""}
              />
            </div>
            <div>
              <RInput
                className="input"
                table={"establishment"}
                field={"data.short"}
                label={"Short"}
                defaultValue={response.item?.short || ""}
                maxLength={2}
              />
            </div>
            <div>
              <RLabel table={"establishment"} field={"data.locale"}>
                Locale
              </RLabel>
              <br />
              <RSelect table={"establishment"} field={"data.locale"} className={"select"} defaultValue={response.item?.locale || ""}>
                <option value="">Default</option>
                {locales.map((locale) => (
                  <option key={locale.code} value={locale.code}>
                    {locale.code} ({locale.language} - {locale.region})
                  </option>
                ))}
              </RSelect>
            </div>
            <label className="flex w-fit flex-row items-center gap-2 rounded-md border border-slate-300 p-3 shadow-sm">
              <span className="">Published</span>
              <RInput
                type={"checkbox"}
                hiddenType={"__boolean__"}
                table={"establishment"}
                field={"data.published"}
                className="checkbox"
                defaultChecked={response.item?.published}
                disabled={isLoading}
              />
            </label>
          </Fragment>
        )}
        <div>
          <RLabel
            table={"establishment"}
            field={"data.require_email_verification_for_signing"}
            className="flex w-fit flex-row items-center gap-2 rounded-md border border-slate-300 p-3 shadow-sm"
          >
            <span>Require OTP for signing</span>
            <RInput
              type={"checkbox"}
              field="data.require_email_verification_for_signing"
              table={"establishment"}
              hiddenType={"__boolean__"}
              className="checkbox"
              disabled={isLoading}
              defaultChecked={response.item ? response.item.require_email_verification_for_signing : true}
            />
          </RLabel>
        </div>
        {app.editor && (
          <Fragment>
            <h3 className="text-xl text-slate-600">Modules</h3>
            <div className="flex flex-wrap gap-3">
              <div>
                <RSelect
                  field="data.workflow"
                  table={"establishment"}
                  className="select"
                  disabled={isLoading}
                  defaultValue={response.item?.workflow || 0}
                >
                  {workflowOptions.map((label, index) => (
                    <option key={index} value={index}>
                      {label}
                    </option>
                  ))}
                </RSelect>
              </div>
              <div>
                <RSelect
                  field="data.direct_booking_mode"
                  table={"establishment"}
                  className="select"
                  disabled={isLoading}
                  defaultValue={response.item?.direct_booking_mode || 0}
                >
                  {directBookingModes.map((label, index) => (
                    <option key={index} value={index}>
                      Direct Booking {label}
                    </option>
                  ))}
                </RSelect>
              </div>
              <div>
                <RLabel
                  table={"establishment"}
                  field={"data.review_enabled"}
                  className="flex w-fit flex-row items-center gap-2 rounded-md border border-slate-300 p-3 shadow-sm"
                >
                  <span>Reviews</span>
                  <RInput
                    type={"checkbox"}
                    field="data.review_enabled"
                    table={"establishment"}
                    hiddenType={"__boolean__"}
                    className="checkbox"
                    disabled={isLoading}
                    defaultChecked={response.item ? response.item.review_enabled : true}
                  />
                </RLabel>
              </div>
            </div>
          </Fragment>
        )}
        <div className="space-y-3">
          <h3 className="text-xl text-slate-600">Defaults</h3>
          <div className="flex flex-wrap gap-3">
            {app.editor && (
              <div>
                <RLabel table={"establishment"} field={"data.direct_booking_form_root_id"}>
                  Direct booking form
                </RLabel>
                <br />
                <RSelect
                  field="data.direct_booking_form_root_id"
                  table={"establishment"}
                  className="select"
                  disabled={isLoading}
                  defaultValue={response.item?.direct_booking_form_root_id || ""}
                >
                  <option value="">Use product form (default)</option>
                  {response.item?.forms.map((form) => (
                    <option key={form.root_id} value={form.root_id}>
                      {!form.establishment_id && "default - "}
                      {form.name}
                    </option>
                  ))}
                </RSelect>
              </div>
            )}
            <div>
              <RLabel table={"establishment"} field={"data.default_currency"}>
                Currency
              </RLabel>
              <br />
              <RSelect
                table={"establishment"}
                field={"data.default_currency"}
                defaultValue={response.item?.default_currency || ""}
                className="select"
              >
                <option value={""}>-</option>
                {app.currencies.map((item) => (
                  <option key={item.id} value={item.id}>
                    {item.id}
                  </option>
                ))}
              </RSelect>
            </div>
            <div>
              <RLabel table={"establishment"} field={"data.default_weight_unit"}>
                Weight unit
              </RLabel>
              <br />
              <RSelect
                defaultValue={response.item?.default_weight_unit || ""}
                table={"establishment"}
                field={"data.default_weight_unit"}
                className="select"
              >
                <option value="">No default</option>
                {keys(weightMetrics).map((weight) => (
                  <option key={weight} value={weight}>
                    {weight}
                  </option>
                ))}
              </RSelect>
            </div>
            <div>
              <RLabel table={"establishment"} field={"data.default_height_unit"}>
                Height unit
              </RLabel>
              <br />
              <RSelect
                defaultValue={response.item?.default_height_unit || ""}
                table={"establishment"}
                field={"data.default_height_unit"}
                className="select"
              >
                <option value="">No default</option>
                {keys(heightMetrics).map((weight) => (
                  <option key={weight} value={weight}>
                    {weight}
                  </option>
                ))}
              </RSelect>
            </div>
            <div>
              <RLabel table={"establishment"} field={"data.default_shoe_size_unit"}>
                Shoesize unit
              </RLabel>
              <br />
              <RSelect
                defaultValue={response.item?.default_shoe_size_unit || ""}
                table={"establishment"}
                field={"data.default_shoe_size_unit"}
                className="select"
              >
                <option value="">No default</option>
                {keys(shoeSizeUnits).map((weight) => (
                  <option key={weight} value={weight}>
                    {weight}
                  </option>
                ))}
              </RSelect>
            </div>
            <div>
              <RLabel table={"establishment"} field={"data.default_booking_meeting_type"}>
                Meeting type
              </RLabel>
              <br />
              <RSelect
                table={"establishment"}
                field={"data.default_booking_meeting_type"}
                defaultValue={response.item?.default_booking_meeting_type || ""}
                className={"select"}
              >
                <option value="">No default</option>
                {Object.entries(meetingTypes).map(([key, type]) => {
                  return (
                    <option key={key} value={key}>
                      {type.label}
                    </option>
                  );
                })}
              </RSelect>
            </div>
            <div>
              <RInput
                table={"establishment"}
                field={"data.default_booking_meeting_time"}
                defaultValue={response.item?.default_booking_meeting_time || ""}
                type={"time"}
                label={"Meeting time"}
                className="input"
              />
            </div>
            <div>
              <RInput
                table={"establishment"}
                field={"data.default_trip_start_time"}
                defaultValue={response.item?.default_trip_start_time || ""}
                type={"time"}
                label={"Trip start time"}
                className="input"
              />
            </div>
            {/*<div>*/}
            {/*  <RLabel table={"establishment"} field={"data.default_weight_unit"}>*/}
            {/*    Weightbelt Unit*/}
            {/*  </RLabel>*/}
            {/*  <br />*/}
            {/*  <RSelect table={"establishment"} field={"data.default_weight_unit"} className="select">*/}
            {/*    <option value="">No default</option>*/}
            {/*    {weightMetrics.map((weight) => (*/}
            {/*      <option key={weight} value={weight}>*/}
            {/*        {weight}*/}
            {/*      </option>*/}
            {/*    ))}*/}
            {/*  </RSelect>*/}
            {/*</div>*/}
            {/*<RTextarea table={''} field={} />*/}
            {/*<BookingMessages />*/}
          </div>
          <div>
            <div className={"flex  gap-1 items-center"}>
              <RLabel table={"establishment"} field={"data.whatsapp_message_template"}>
                Whatsapp template for booking
              </RLabel>
              <Tooltip
                description={
                  <p className="max-w-96">
                    Setting a message here adds an additional copy button at the bottom of your booking pages, visible only to you as the
                    dive center. When used, the copied URL will automatically include this message.
                  </p>
                }
              >
                <DefaultInfoIcon />
              </Tooltip>
            </div>
            <RTextarea
              className="input min-h-40 placeholder:text-slate-400"
              table={"establishment"}
              field={"data.whatsapp_message_template"}
              defaultValue={response.item?.whatsapp_message_template || ""}
            />
          </div>
        </div>
        {app.editor && (
          <div>
            <h3 className="text-xl text-slate-600">Direct booking</h3>
            <div className="sm:max-w-40">
              <RLabel table={"establishment"} field={"data.direct_booking_window"}>
                Time window
              </RLabel>
              <br />
              <ValidityDurationField
                min={0}
                required
                name={fName("establishment", "data.direct_booking_window")}
                defaultValue={response.item?.direct_booking_window || "1 day"}
              />
            </div>
          </div>
        )}
      </div>
    </ActionForm>
  );
}
