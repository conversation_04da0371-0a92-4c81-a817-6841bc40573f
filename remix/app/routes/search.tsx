import { BiSearch } from "react-icons/bi";
import React, { useEffect, useState } from "react";
import { Combobox, Transition } from "@headlessui/react";
import { useFetcher, useNavigate, useNavigation } from "@remix-run/react";
import { HomeIcon } from "@heroicons/react/20/solid";
import { z } from "zod";
import { CloseButton } from "~/components/base/Button";
import { _establishment_detail } from "~/misc/paths";
import { searchOperatorQb } from "~/domain/establishment/queries";
import { getEstablishmentShort } from "~/domain/establishment/helpers";
import { getSessionSimple } from "~/utils/session.server";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import { kysely } from "~/misc/database.server";
import { data, LoaderFunctionArgs } from "@remix-run/router";

export const loader = async ({ request, context }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const search = url.searchParams.get("search");
  const { session_id } = await getSessionSimple(request);
  const user = await activeUserSessionSimple(kysely, session_id, true).select("_user.editor").executeTakeFirst();

  const operatorLocationsQb = searchOperatorQb(search)
    .where("region.published", "=", true)
    .$if(!user?.editor, (eb) => eb.where("establishment.published", "=", true).where("spot.id", "is not", null));
  const operatorLocations = await operatorLocationsQb.execute();

  return data(
    {
      search: search || "",
      operators: operatorLocations.map((item) => ({
        ...item,
        short: getEstablishmentShort(item),
        url: _establishment_detail(item.establishment_id),
      })),
    },
    user?.editor
      ? {}
      : {
          headers: {
            "Cache-Control": "public, max-age=20, s-maxage=20",
          },
        },
  );
};

const debounceInMs = 600;

export const SearchButton = () => {
  const navigtion = useNavigation();
  const isWorking = navigtion.state === "loading" || navigtion.state === "submitting";
  const navigate = useNavigate();
  const fetcher = useFetcher<typeof loader>();
  const [search, setSearch] = useState("");

  useEffect(() => {
    const handler = setTimeout(() => {
      const newParams = new URLSearchParams(search);
      newParams.set("search", search);
      fetcher.submit(newParams, { action: "/search", method: "get" });
    }, debounceInMs);

    return () => clearTimeout(handler);
  }, [search]);

  // const divingLocations = fetcher.data?.diving_locations || [];
  // const divingSites = fetcher.data?.diving_sites || [];
  const operators = fetcher.data?.operators || [];

  // console.log("found operators", operators);

  // const allResults = [...divingLocations, ...divingSites, ...operators].sort((a, b) => {
  //   return a.score - b.score;
  // });
  const allResults = operators.sort((a, b) => b.score - a.score);

  const isLoading = fetcher.state !== "idle" || fetcher.data?.search !== search;

  const inBetweenState = !!search && allResults.length === 0;
  const showLoading = inBetweenState && isLoading;
  const noResults = inBetweenState && !isLoading;

  return (
    <Combobox
      disabled={isWorking}
      onChange={(value) => {
        const parsed = z
          .object({
            url: z.string(),
          })
          .safeParse(value);
        if (parsed.success) {
          navigate(parsed.data.url);
        }
      }}
    >
      {(args) => (
        <div className="sticky top-0 h-16 max-w-screen-md">
          <Transition
            show={args.open}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 z-10 bg-black bg-opacity-20"></div>
          </Transition>
          <div className={"absolute flex w-full flex-col ui-open:z-20"}>
            <div
              className={`z-10 flex-1 rounded-xl bg-white p-1 ring-1 ring-slate-500 
            focus-within:ring-2 focus-within:ring-primary ui-open:drop-shadow-xl`}
            >
              <Combobox.Button as="div">
                <div className={"h-0 overflow-hidden"}>
                  <div className="flex flex-row items-center justify-between p-1 pb-2 ">
                    <p className="flex-1 text-center">Search</p>
                    <Combobox.Button as={CloseButton} />
                  </div>
                </div>
                <div className="flex flex-row items-center gap-2 overflow-hidden rounded-xl bg-white py-1 pl-2 pr-1">
                  <BiSearch className="h-6 w-6" />
                  <Combobox.Input
                    displayValue={(item: unknown) => {
                      if (typeof item === "string") return item;
                      if (item && typeof item === "object" && "short" in item && typeof item.short === "string") {
                        return item.short;
                      }
                      return "";
                    }}
                    onChange={(event) => {
                      setSearch(event.target.value);
                    }}
                    placeholder="search dive centers"
                    defaultValue={""}
                    className="w-full flex-1 border-none px-0 py-1 text-slate-700 focus:ring-0"
                  />
                  <Combobox.Button className="opacity-0 ui-open:opacity-100" as={CloseButton} />
                </div>
              </Combobox.Button>
              <Combobox.Options className="flex min-h-[0px] flex-1 flex-col">
                <div className="relative min-h-[0px] flex-1 overflow-y-auto md:max-h-96">
                  {showLoading && <div className="animate-pulse bg-slate-100 p-2">searching...</div>}
                  {noResults && <div className="p-2">No results</div>}
                  {/*{divingLocations.map((item) => (*/}
                  {/*  <Combobox.Option*/}
                  {/*    disabled={isLoading}*/}
                  {/*    as="button"*/}
                  {/*    type="button"*/}
                  {/*    className="block flex w-full flex-row gap-3 p-3 hover:bg-slate-100 active:bg-slate-100 disabled:opacity-60 ui-active:bg-slate-200 ui-disabled:opacity-60"*/}
                  {/*    key={item.id}*/}
                  {/*    value={item}*/}
                  {/*  >*/}
                  {/*    <MapPinIcon className="h-5 w-5" />*/}
                  {/*    <p className="text-left">{item.name}</p>*/}
                  {/*  </Combobox.Option>*/}
                  {/*))}*/}
                  {/*{divingSites.map((item) => (*/}
                  {/*  <Combobox.Option*/}
                  {/*    disabled={isLoading}*/}
                  {/*    as="button"*/}
                  {/*    type="button"*/}
                  {/*    className="block flex w-full flex-row gap-3 p-3 hover:bg-slate-100 active:bg-slate-100 disabled:opacity-60 ui-active:bg-slate-200 ui-disabled:opacity-60"*/}
                  {/*    key={item.id}*/}
                  {/*    value={item}*/}
                  {/*  >*/}
                  {/*    <FlagIcon className="h-5 w-5" />*/}
                  {/*    <p className="text-left">{item.short}</p>*/}
                  {/*  </Combobox.Option>*/}
                  {/*))}*/}
                  {operators.map((item) => (
                    <Combobox.Option
                      disabled={isLoading}
                      as="button"
                      type="button"
                      className="block flex w-full flex-row gap-3 p-3 hover:bg-slate-100 active:bg-slate-100 disabled:opacity-60 ui-active:bg-slate-200 ui-disabled:opacity-60"
                      key={item.establishment_id}
                      value={item}
                    >
                      <HomeIcon className="h-5 w-5" />
                      <p className="text-left">{item.short}</p>
                    </Combobox.Option>
                  ))}
                </div>
              </Combobox.Options>
            </div>
          </div>
        </div>
      )}
    </Combobox>
  );
};
