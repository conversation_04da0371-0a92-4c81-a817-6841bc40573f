import type { DataFunctionArgs } from "@remix-run/server-runtime";
import { Form, useActionData } from "@remix-run/react";
import * as process from "process";
import { kysely } from "~/misc/database.server";
import { updateBookingsQb } from "~/domain/pricing/booking-pricing-queries";
import { SubmitButton } from "~/components/base/Button";
import { updateParticipantsQb } from "~/domain/participant/participant.queries.server";
import { ActionFunctionArgs } from "@remix-run/node";
import { getSessionSimple } from "~/utils/session.server";
import { isEditorQb } from "~/domain/member/member-queries.server";

export const loader = async ({}: DataFunctionArgs) => {
  return {
    nodeversion: process.version,
  };
};

const cacheTables = ["booking", "participant"] as const;
type CacheTable = (typeof cacheTables)[number];

export const action = async (args: ActionFunctionArgs) => {
  const formdata = await args.request.formData();
  const session = await getSessionSimple(args.request);
  await isEditorQb({ ctx: session, trx: kysely }).executeTakeFirstOrThrow();

  const cacheTable = cacheTables.find((table) => formdata.get("type") === table);
  if (!cacheTable) return null;

  const updateHandlers = {
    booking: updateBookingsQb(kysely).returning("booking.id"),
    participant: updateParticipantsQb(kysely).returning("participant.id"),
  } satisfies Record<CacheTable, any>;

  const result = await updateHandlers[cacheTable].execute();

  return { updated: result.length, type: cacheTable };
};

export default function Page() {
  const result = useActionData<typeof action>();
  return (
    <div>
      {result && (
        <div>
          Updated {result.type}s: {result.updated}
        </div>
      )}
      <Form method={"POST"}>
        <select name={"type"}>
          {cacheTables.map((table) => (
            <option key={table} value={table}>
              {table}s
            </option>
          ))}
        </select>
        <SubmitButton className="btn btn-primary">Update</SubmitButton>
      </Form>
    </div>
  );
}
