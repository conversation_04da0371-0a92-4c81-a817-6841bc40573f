import { LoaderFunction } from "@remix-run/server-runtime";
import { getCachedValue } from "~/server/cache/cache.server";

const apiBaseUrl = "https://v6.exchangerate-api.com/v6";
const apiKey = "************************";

interface ExchangeRateApiResponse {
  result: string;
  base_code: string;
  conversion_rates: { [code: string]: number };
}

export const loader: LoaderFunction = async ({ request, params }) => {
  const apiPath = `latest/${params.currency}`;
  const apiUrl = apiBaseUrl + "/" + apiKey + "/" + apiPath;
  const cacheKey = apiBaseUrl + "/" + apiPath;
  const value = await getCachedValue<ExchangeRateApiResponse>({
    key: cacheKey,
    request: () => fetch(apiUrl).then((resp) => resp.json()),
    ttlInSeconds: 60 * 10,
  });
  return Object.entries(value.conversion_rates).map(([code, rate]) => ({
    currencyCode: code,
    conversionRate: rate,
  }));
};
