import type { DataFunctionArgs } from "@remix-run/server-runtime";
import { useLoaderData } from "@remix-run/react";
import { createCallbacksAndSendCloudTasks } from "~/server/utils/google-task.server";
import * as process from "process";
import { kysely } from "~/misc/database.server";

export const loader = async ({ request, params }: DataFunctionArgs) => {
  return {
    nodeversion: process.version,
  };
};

export const action = async () => {
  // const client = new CloudTasksClient({ key: appConfig.GOOGLE_TASKS_API_KEY, credentials: googleServiceAccount });
  // const parent = client.queuePath("scamgem", "europe-west1", "testqueue");

  // client.
  // client.getQueue(parent)
  const now = new Date();
  const nowpu5min = new Date(Date.now() + 1000 * 60 * 3);
  await createCallbacksAndSendCloudTasks(kysely);
  // await client.createTask({
  //   parent: parent,
  //   task: {
  //     scheduleTime: { seconds: Date.now() / 1000 + 60 * 3 },
  //     httpRequest: {
  //       httpMethod: "POST",
  //       url: "https://local-tt.dinkel.works/webhook/test",
  //     },
  //   },
  // });
  return {};
};

export default function Page() {
  const data = useLoaderData<typeof loader>();
  return <div>{data.nodeversion}</div>;
}
