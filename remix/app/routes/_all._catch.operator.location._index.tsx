import type { DataFunctionArgs } from "@remix-run/server-runtime";
import type { AsyncReturnType } from "type-fest";
import { getSessionSimple } from "~/utils/session.server";
import { kysely } from "~/misc/database.server";
import { useLoaderData } from "@remix-run/react";
import { EstablishmentItem } from "~/domain/establishment/EstablishmentItem";
import { _establishment_create, _operator } from "~/misc/paths";
import { EditorRequired } from "~/components/account/AccountContainer";
import React from "react";
import { ShowUnpublishedCheckbox } from "~/components/ShowUnpublishedCheckbox";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { ParamLink } from "~/components/meta/CustomComponents";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: DataFunctionArgs) => {
  const url = new URL(request.url);
  const showUnpublished = paramsToRecord(url.searchParams).show_unpublished;
  const { session_id } = await getSessionSimple(request);
  const user = await activeUserSessionSimple(kysely, session_id, true).select("_user.editor").executeTakeFirst();
  if (!user?.editor) return null;

  const baseQb = kysely
    .selectFrom("establishment")
    .leftJoin("spot", "spot.id", "establishment.spot_id")
    .leftJoin("region", "region.id", "spot.region_id")
    .innerJoin("operator", "operator.id", "establishment.operator_id")
    .$if(!showUnpublished, (eb) => eb.where("establishment.published", "=", true))
    .where("region.published", "=", true);

  const listQb = baseQb
    .orderBy("establishment.published")
    .orderBy("operator.name")
    .orderBy("establishment.location_name")
    .selectAll("establishment")
    .select(["spot.name as spot_name", "operator.name as operator_name"])
    .limit(200);

  const countQb = baseQb.select([(eb) => eb.fn.count("establishment.id").as("total")]);

  return Promise.all([
    listQb.execute(),
    countQb.executeTakeFirstOrThrow(),
    kysely
      .selectFrom("member")
      .innerJoin("user", "user.id", "member.user_id")
      .where("user.deleted_at", "=", at_infinity_value)
      .selectAll("member")
      .select("user.email as user_email")
      .execute(),
  ]);
};

type LoaderResponse = AsyncReturnType<typeof loader>;

export default function Page() {
  const response = useLoaderData<LoaderResponse>();
  if (!response) return <EditorRequired />;

  const [operators, count, owners] = response;

  return (
    <div className="app-container space-y-3 py-3">
      <ParamLink path={_operator} className="link">
        Operators
      </ParamLink>
      <div className="flex flex-row items-center gap-2">
        <h1 className="text-2xl font-bold">Operator locations</h1>
        <ParamLink path={_establishment_create} className="link">
          create
        </ParamLink>
      </div>
      <ShowUnpublishedCheckbox />
      <p>Count: {count.total + ""}</p>
      <div className="grid gap-3 md:grid-cols-2">
        {operators.map((operator) => (
          <ParamLink
            key={operator.id}
            path={operator.id}
            className="block w-full rounded-md border border-slate-100 hover:opacity-80 active:opacity-80"
          >
            <EstablishmentItem item={operator} />
            <div className="flex flex-wrap gap-3 p-3">
              {owners
                .filter((owner) => owner.establishment_id === operator.id)
                .map((owner) => (
                  <div className="rounded-md bg-slate-100 p-2" key={owner.id}>
                    {owner.user_email}
                  </div>
                ))}
            </div>
          </ParamLink>
        ))}
      </div>
    </div>
  );
}
