import React from "react";
import { getAccessToken, getCompanryInfo, getUserInfo, intuit_base_url, IntuitEnv } from "~/domain/quickbooks/quickbooks-client.server";
import { getSessionSimple } from "~/utils/session.server";
import { LoaderFunctionArgs } from "@remix-run/router";
import { kysely } from "~/misc/database.server";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import { notFound, unauthorized } from "~/misc/responses";
import { useLoaderData } from "@remix-run/react";
import { redirect } from "@remix-run/server-runtime";
import { _connect, _intuit_connection } from "~/misc/paths";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { ParamLink } from "~/components/meta/CustomComponents";
import { useIntuitAuthUrl } from "~/domain/quickbooks/quickbooks-auth-url";
import { getFullUrl, keys } from "~/misc/helpers";
import { at_infinity_value } from "~/kysely/db-static-vars";

export { action } from "~/routes/_all._catch.resource";

export const loader = async (args: LoaderFunctionArgs) => {
  const intuitEnvironment: IntuitEnv =
    keys(intuit_base_url).find((key) => key.toLowerCase() === args.params.env?.toLowerCase()) || "SANDBOX";

  const session = await getSessionSimple(args.request);
  const userSession = await activeUserSessionSimple(kysely, session.session_id, true)
    .select((eb) => [
      "_user_session.id",
      jsonArrayFrom(
        eb
          .selectFrom("intuit_connection")
          .innerJoin("user_session", "user_session.id", "intuit_connection.created_by_user_session_id")
          .where("user_session.user_id", "=", eb.ref("_user_session.user_id"))
          .where("intuit_connection.sandbox", "=", intuitEnvironment === "SANDBOX")
          .where("intuit_connection.deleted_at", "=", at_infinity_value)
          .select([
            "intuit_connection.id",
            "intuit_connection.realm_id",
            "intuit_connection.intuit_user",
            "intuit_connection.intuit_company",
          ]),
      ).as("intuit_connections"),
    ])
    .executeTakeFirst();

  if (!userSession) throw unauthorized();
  const url = new URL(args.request.url);
  const code = url.searchParams.get("code");
  const state = url.searchParams.get("state");
  const realmId = url.searchParams.get("realmId");

  if (!code) return { ...userSession, env: intuitEnvironment };

  if (state !== userSession.id) throw unauthorized("invalid state back from oauth");
  if (!realmId) throw notFound("realmId is required");

  const tokenResult = await getAccessToken(intuitEnvironment, {
    grant_type: "authorization_code",
    redirect_uri: getFullUrl(url.host) + _connect(intuitEnvironment),
    code: code,
  });

  const accessToken = tokenResult.access_token;
  const refreshToken = tokenResult.refresh_token;

  const userInfo = await getUserInfo({ access_token: accessToken, environment: intuitEnvironment });
  const companryInfo = await getCompanryInfo({
    access_token: accessToken,
    realm_id: realmId,
    environment: intuitEnvironment,
  });

  await kysely
    .insertInto("intuit_connection")
    .values({
      deleted_at: at_infinity_value,
      created_by_user_session_id: userSession.id,
      sandbox: intuitEnvironment === "SANDBOX",
      refresh_token: refreshToken,
      intuit_company: companryInfo.CompanyInfo,
      intuit_user: userInfo,
      realm_id: realmId,
    })
    .executeTakeFirstOrThrow();

  throw redirect(_connect(intuitEnvironment));
};

export default function Page() {
  const response = useLoaderData<typeof loader>();
  const qbAuthUrl = useIntuitAuthUrl(response.env);
  return (
    <div className="app-container py-6">
      <div className="space-y-3">
        <div className="flex flex-wrap gap-2 items-center">
          <h3 className="text-xl">Quickbooks {response.env === "SANDBOX" && response.env}</h3>
          <a href={qbAuthUrl} className="link">
            connect
          </a>
        </div>
        <p>Existing connections:</p>
        {response.intuit_connections.map((connection) => (
          <div key={connection.id} className="flex flex-wrap gap-3">
            <ParamLink path={_intuit_connection(connection.id)} className="link">
              {" "}
              - {connection.intuit_company.CompanyName} ({connection.intuit_user.email}) ({connection.realm_id})
            </ParamLink>
          </div>
        ))}
      </div>
    </div>
  );
}
