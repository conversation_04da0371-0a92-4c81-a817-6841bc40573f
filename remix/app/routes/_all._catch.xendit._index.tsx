import { use<PERSON><PERSON>derD<PERSON> } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import React from "react";
import { getSessionSimple } from "~/utils/session.server";
import { LoaderFunctionArgs } from "@remix-run/router";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { isEditorQb, toArgs } from "~/domain/member/member-queries.server";
import { unauthorized } from "~/misc/responses";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _xendit_account, _xendit_env_bank } from "~/misc/paths";
import { ActionForm } from "~/components/form/BaseFrom";
import { SubmitButton } from "~/components/base/Button";
import { RInput, RSelect } from "~/components/ResourceInputs";
import { AnimatingDiv } from "~/components/base/base";
import { useAppContext } from "~/hooks/use-app-context";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);

  const editor = await isEditorQb(toArgs(kysely, session_id)).select("_user.editor").executeTakeFirst();

  if (!editor) throw unauthorized();

  const xenditAccounts = await kysely
    .selectFrom("xendit_account")
    .selectAll("xendit_account")
    .select((eb) => [
      jsonArrayFrom(simpleEstablishmentQb.where("establishment.xendit_account_id", "=", eb.ref("xendit_account.id"))).as("establishments"),
    ])
    .execute();

  const xenditEnvs = await kysely
    .selectFrom("xendit_platform")
    .select((eb) => [
      "xendit_platform.id",
      "xendit_platform.name",
      "xendit_platform.currency_id",
      jsonArrayFrom(
        eb
          .selectFrom("xendit_environment")
          .select((eb) => [
            "xendit_environment.id",
            "xendit_environment.production",
            jsonArrayFrom(
              eb
                .selectFrom("xendit_split_rule")
                .selectAll("xendit_split_rule")
                .orderBy("xendit_split_rule.created_at asc")
                .whereRef("xendit_split_rule.xendit_environment_id", "=", "xendit_environment.id"),
            ).as("split_rules"),
          ])
          .whereRef("xendit_environment.xendit_platform_id", "=", "xendit_platform.id"),
      ).as("xendit_environments"),
    ])
    .execute();

  return {
    xendit_platforms: xenditEnvs,
    accounts: xenditAccounts,
    // splitRules: splitRules,
  };
};

export default function Page() {
  const app = useAppContext();
  const response = useLoaderData<typeof loader>();

  return (
    <div className="app-container space-y-6">
      <ParamLink path={_xendit_account} className={"link"}>
        Accounts
      </ParamLink>
      <AnimatingDiv className="space-y-3">
        <div className="flex flex-row gap-3 items-center">
          <h2 className="text-xl font-bold">Xendit Platforms</h2>
          {/*<ParamLink paramState={{ create: true }} className={"link"}>*/}
          {/*  Create*/}
          {/*</ParamLink>*/}
        </div>
        <div>
          <ActionForm className="flex flex-row gap-3">
            <RInput table={"xendit_platform"} field={"data.name"} className="input" placeholder={"Platform Name"} required />
            <RSelect table={"xendit_platform"} field={"data.currency_id"} className="select" required>
              <option value="">-- currency --</option>
              {app.currencies.map((currency) => (
                <option key={currency.id} value={currency.id}>
                  {currency.id}
                </option>
              ))}
            </RSelect>

            <SubmitButton className="btn btn-primary whitespace-nowrap">Add Platform</SubmitButton>
          </ActionForm>
        </div>
      </AnimatingDiv>
      <div className="space-y-9">
        {response.xendit_platforms.map((xenditEnv) => (
          <div key={xenditEnv.id} className="space-y-3">
            <div className="flex flex-wrap gap-3 text-xl">
              <ParamLink path={_xendit_env_bank(xenditEnv.id)} className="link">
                {xenditEnv.name} - {xenditEnv.currency_id}
              </ParamLink>
              {/*<ParamLink path={_xendit_env_bank(xenditEnv.id)} className="link">*/}
              {/*  {xenditEnv.name}*/}
              {/*</ParamLink>*/}
            </div>
            <div>
              <ActionForm className="flex flex-wrap gap-3">
                <RInput table={"xendit_environment"} field={"data.xendit_platform_id"} value={xenditEnv.id} type={"hidden"} />
                <RInput
                  table={"xendit_environment"}
                  field={"data.xendit_api_key"}
                  placeholder={"Xendit API Key"}
                  className="input w-fit "
                />
                <SubmitButton className="btn btn-primary">Add API Key</SubmitButton>
              </ActionForm>
            </div>
            <div>
              {xenditEnv.xendit_environments.map((xenditApiKey) => (
                <div key={xenditApiKey.id} className="space-y-3">
                  <div className="flex flex-row gap-3">
                    {xenditApiKey.production ? "Prodction" : "Test"}
                    <ActionForm>
                      <RInput table={"xendit_split_rule"} field={"data.xendit_environment_id"} value={xenditApiKey.id} type={"hidden"} />
                      <SubmitButton className="text-sm link">Add Split Rule</SubmitButton>
                    </ActionForm>
                  </div>
                  <div className="space-y-3">
                    {xenditApiKey.split_rules.map((splitRule) => (
                      <div key={splitRule.id}>
                        <div className="grid grid-cols-2 text-sm p-3 rounded-md border border-slate-300">
                          <div>Xendit Split Rule Id</div>
                          <div>{splitRule.xendit_split_rule_id}</div>
                          <div className="col-span-2">
                            <details>
                              <summary>Xendit response:</summary>
                              <pre>{JSON.stringify(splitRule.xendit_response, null, 2)}</pre>
                            </details>
                          </div>
                          <div>Active:</div>
                          <div className="flex flex-row gap-3 items-center">
                            {splitRule.active ? <span className="p-1 px-3 bg-green-600 text-white rounded-md">yes</span> : "no"}{" "}
                            <ActionForm>
                              <RInput table={"xendit_split_rule"} field={"id"} value={splitRule.id} />
                              <RInput
                                table={"xendit_split_rule"}
                                field={"data.active"}
                                value={!splitRule.active + ""}
                                type={"hidden"}
                                hiddenType={"__boolean__"}
                              />
                              <SubmitButton className="link">Toggle</SubmitButton>
                            </ActionForm>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
