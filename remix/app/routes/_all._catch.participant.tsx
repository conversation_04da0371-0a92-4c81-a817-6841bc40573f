import type { SerializeFrom } from "@remix-run/server-runtime";
import { kysely } from "~/misc/database.server";
import { useLoaderData } from "@remix-run/react";
import { getSessionSimple } from "~/utils/session.server";
import { getDateFromParams } from "~/domain/planning/planning.helpers.server";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { baseProductWithSelect } from "~/domain/product/product-queries.server";
import React, { Fragment, ReactNode, useState } from "react";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _booking_detail, _participant_detail, _planning } from "~/misc/paths";
import { OnFormSuccess, SubmitButton } from "~/components/base/Button";
import { ActionForm, defaultEqualCheck } from "~/components/form/BaseFrom";
import { formatExperienceinYears, getNrOfDivesOption } from "~/domain/participant/participant-data";
import { getProductTitle } from "~/domain/product/ProductItem";
import { getActivity } from "~/domain/activity/activity";
import { flat, intersection, sum, sumBy, unique, uniqueBy } from "remeda";
import { getMeetingType, getTripType, Role } from "~/domain/planning/plannings-consts";
import { twMerge } from "tailwind-merge";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { fName, isUuidRegex, logTime, myGroupBy2, symmetricDifference, tableIdRef } from "~/misc/helpers";
import { tripQb } from "~/domain/trip/trip.server";
import { getTripTotals } from "~/domain/trip/trip-components";
import { Selectable, sql } from "kysely";
import { memberIsAdminOrOwnerQb } from "~/domain/member/member-queries.server";
import { paramsToRecord, StateInputKey } from "~/misc/parsers/global-state-parsers";
import { EstablishmentSeperator } from "~/domain/establishment/EstablishmentSeperator";
import { InputSearchParamCopies } from "~/components/meta/input";
import { participantQb, participationQb } from "~/domain/participant/participant.queries.server";
import { toSitesStr } from "~/domain/trip/trip-helpers";
import { saleItemWithProductQb } from "~/domain/activity/activity-queries";
import { bookingQb } from "~/domain/booking/booking-queries";
import { formatDivingCertShort } from "~/domain/diving-course/diving-courses.data";
import { LoaderFunctionArgs } from "@remix-run/router";
import { RInput, RLabel, RSelect, RTextarea, toInputId } from "~/components/ResourceInputs";
import { useAppContext } from "~/hooks/use-app-context";
import { HiddenTypeInput, OperationInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { CheckDoneIcon, ExportIcon, NotDoneIcon, ProfileIcon } from "~/components/Icons";
import {
  ColumnKey,
  columnKeys,
  DetailModalName,
  Sort,
  sortableColumns,
  toValidOrderableColumns,
} from "~/domain/participant/participant-columns";
import { callOrderColumns } from "~/domain/participant/participant-columns.server";
import { defaultUnkownValue } from "~/misc/vars";
import { getLastDivedShort, safeFormat } from "~/domain/participant/participant-helpers";
import { formatMealPref } from "~/domain/participant/participant-fields";
import { defaultNotRegisteredValue } from "~/components/shared";
import { arrayAgg, ascNullsLast, contextRef, formatDatetime, lower, subtract } from "~/kysely/kysely-helpers";
import {
  ArrowsUpDownIcon,
  ChevronDownIcon,
  Cog6ToothIcon,
  ExclamationCircleIcon,
  LockClosedIcon,
  PencilIcon,
  PlusIcon,
  TrashIcon,
  XMarkIcon,
} from "@heroicons/react/20/solid";
import { BookingPaymentBadge, getPaymentState, MeetingInput } from "~/domain/booking/booking-components";
import { closestCenter, DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { arrayMove, SortableContext, sortableKeyboardCoordinates, useSortable } from "@dnd-kit/sortable";
import { GrDrag } from "react-icons/gr";
import { CSS } from "@dnd-kit/utilities";
import { ArrowRightLeft, EyeOffIcon, UndoIcon } from "lucide-react";
import { Tab } from "~/domain/view/view-components";
import { IdName } from "~/misc/models";
import { escapeCSV } from "~/misc/csv-helpers";
import { usePageRefresh } from "~/hooks/use-page-refresh";
import { HeightInfo, ShoeSizeInfo, WeightInfo } from "~/domain/participant/participant.components";
import { AdminLevel, getAdminLevelIndex } from "~/domain/member/member-vars";
import { createMeta } from "~/misc/route-helpers";
import { DaySwitch } from "~/domain/meta/datetime";
import { createPageOverwrites } from "~/misc/consts";
import { SidePanel, SidePanelHeading } from "~/components/SidePanel";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { useBoolean } from "~/hooks/use-boolean";
import { AnimatingDiv } from "~/components/base/base";
import { toggleArray } from "../misc/parsers/global-state-parsers";
import { GasOption, gasOptions, LiterOption, literOptions } from "~/domain/tank/tank-vars";
import { DB, TankAssignment } from "~/kysely/db";
import { refreshFormdata } from "~/components/form/form-hooks";
import { Checker } from "~/components/Checker";
import { GenderOption, genderOptions } from "~/domain/participant/GenderSelect";
import { addTripPanelTitleId } from "~/domain/trip/vars";
import { Tooltip } from "~/components/base/tooltip";
import { FiAlertCircle } from "react-icons/fi";
import { selectListWithFallback, uniqueTextArray } from "~/misc/postgres-helpers";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const ctx = await getSessionSimple(request);
  const date = getDateFromParams(request, true);
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  const operatorTabs = await kysely
    .selectFrom("view")
    .selectAll("view")
    .orderBy("view.sort_order")
    .where("view.operator_id", "=", state.persist_operator_id)
    .execute();

  const finalTabs = operatorTabs.map((tab) => {
    const columns = tab.columns.filter((column): column is ColumnKey => columnKeys.includes(column));
    return { ...tab, columns: columns, sorts: toValidOrderableColumns(tab.sorts) } as Tab;
  });

  const tab = finalTabs.find((tab) => tab.id === state.view_id) || finalTabs[0];

  const withinDuration = `[${date.dateParam},${date.dateParam}]`;

  const timer = logTime("targetEstablishments");
  const targetEstablishments = kysely
    .selectFrom("establishment")
    .where("establishment.operator_id", "=", state.persist_operator_id)
    .$if(!!state.persist_establishment_id, (eb) => eb.where("establishment.id", "=", state.persist_establishment_id))
    .$if(state.persist_toggle_establishment_ids.length > 0, (eb) =>
      eb.where("establishment.id", "not in", state.persist_toggle_establishment_ids),
    )
    .where(
      "establishment.id",
      "in",
      memberIsAdminOrOwnerQb(
        {
          trx: kysely,
          ctx: ctx,
        },
        "read",
      ).select("_member.establishment_id"),
    );
  const allowedBookings = targetEstablishments
    .innerJoin("booking", "booking.establishment_id", "establishment.id")
    .where("booking.cancelled_at", "is", null);

  const filteredTrips = kysely
    .selectFrom("trip")
    .where("trip.establishment_id", "in", targetEstablishments.select("establishment.id"))
    .where("trip.date", "=", date.dateParam);

  const establishments = await kysely
    .selectFrom("establishment")
    .leftJoin("spot", "spot.id", "establishment.spot_id")
    .leftJoin("region", "region.id", "spot.region_id")
    .selectAll("establishment")
    .where("establishment.id", "in", targetEstablishments.select("establishment.id"))
    .select((establishmentEb) => [
      "spot.name as spot_name",
      "region.country_code",
      jsonArrayFrom(
        kysely
          .selectFrom("rentable")
          .leftJoin("sortable_value", (join) => join.on("sortable_value.value", "=", lower(contextRef("rentable", "size"))))
          .where((eb) =>
            eb.or([
              eb("rentable.deleted_at", "=", at_infinity_value),
              eb(
                "rentable.id",
                "in",
                eb
                  .selectFrom("rental_assignment")
                  .select("rental_assignment.rentable_id")
                  .where("rental_assignment.date", "=", date.dateParam),
              ),
            ]),
          )
          .where("rentable.establishment_id", "=", establishmentEb.ref("establishment.id"))
          .orderBy("sortable_value.sort_order", ascNullsLast)
          .orderBy("rentable.size", "asc")
          .orderBy("rentable.reference_id asc")
          .selectAll("rentable"),
      ).as("rentables"),
      jsonArrayFrom(
        kysely
          .selectFrom("rental_assignment")
          .innerJoin("rentable", "rentable.id", "rental_assignment.rentable_id")
          .selectAll("rental_assignment")
          .where("rental_assignment.date", "=", date.dateParam)
          .where("rentable.establishment_id", "=", establishmentEb.ref("establishment.id")),
      ).as("rental_assignments"),
      jsonArrayFrom(
        tripQb.where("trip.establishment_id", "=", establishmentEb.ref("establishment.id")).where("trip.date", "=", date.dateParam),
      ).as("trips"),
      jsonArrayFrom(
        establishmentEb
          .selectFrom("participation")
          .selectAll("participation")
          .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
          .innerJoin("booking", "booking.id", "sale_item.booking_id")
          // needed for orderBy
          .leftJoin("product", "product.id", "sale_item.product_id")
          .leftJoin("product_price", "product_price.product_id", "product.id")
          .leftJoin("price", "price.id", "product_price.price_id")
          .leftJoin("trip_assignment", (join) =>
            join
              .onRef("trip_assignment.participation_id", "=", "participation.id")
              .on("trip_assignment.trip_id", "in", filteredTrips.select("trip.id")),
          )
          .leftJoin("trip", (join) => join.onRef("trip.id", "=", "trip_assignment.trip_id").on("trip.date", "=", date.dateParam))
          .where("booking.establishment_id", "=", establishmentEb.ref("establishment.id"))

          .where((eb) => {
            return eb.or([eb("trip.date", "=", date.dateParam), eb("sale_item.duration", "&&", withinDuration)]);
          })
          .where("sale_item.booking_id", "in", allowedBookings.select("booking.id"))
          .select((eb) => [
            "establishment.default_weight_unit",
            "establishment.default_height_unit",
            "establishment.default_shoe_size_unit",
            "trip_assignment.trip_id",
            "trip_assignment.id as trip_assignment_id",
            // jsonArrayFrom(
            //   eb
            //     .selectFrom("activity_addon")
            //     .selectAll('participation_addon')
            //     .innerJoin("participation_addon", (join) => join.onRef("participation_addon.addon_id", "=", "activity_addon.addon_id"))
            //     .where("activity_addon.allow_change", "=", true)
            //     .where("activity_addon.sale_item_id", "=", eb.ref("sale_item.id"))
            //     .where("participation_addon.participation_id", "=", eb.ref("participation.id")),
            // ).as("participation_addon"),
            jsonArrayFrom(
              establishmentEb
                .selectFrom("tank_assignment")
                .selectAll("tank_assignment")
                .orderBy("tank_assignment.sort_order", ascNullsLast)
                .where("tank_assignment.trip_assignment_id", "=", eb.ref("trip_assignment.id")),
            ).as("tank_assignments"),
            jsonArrayFrom(
              establishmentEb
                .selectFrom("comment")
                .where("comment.target", "=", "participation" satisfies keyof DB)
                .where("comment.target_id", "=", eb.ref("participation.id"))
                .orderBy("comment.created_at", "desc")
                .where("comment.date", "=", date.dateParam)
                .selectAll("comment")
                .select((eb) =>
                  formatDatetime(eb.ref("comment.created_at"), "DD Mon YYYY, HH24:MI", eb.ref("region.timezone")).as(
                    "created_at_formatted",
                  ),
                ),
            ).as("comments"),
            jsonObjectFrom(baseProductWithSelect.where("product.id", "=", eb.ref("sale_item.product_id"))).as("product"),
            jsonObjectFrom(
              participantQb(kysely)
                .select((eb) => [
                  jsonObjectFrom(
                    eb
                      .selectFrom("participant_day")
                      .selectAll("participant_day")
                      .where("participant_day.participant_id", "=", eb.ref("participant.id"))
                      .where("participant_day.date", "=", date.dateParam),
                  ).as("participant_day"),
                  jsonArrayFrom(
                    eb
                      .selectFrom("rental_assignment")
                      .selectAll("rental_assignment")
                      .where("rental_assignment.participant_id", "=", eb.ref("participant.id")),
                  ).as("all_rental_assignments"),
                ])
                .where("participant.id", "=", eb.ref("participation.participant_id")),
            ).as("participant"),
            jsonObjectFrom(
              eb.selectFrom("member").selectAll("member").where("member.id", "=", eb.ref("trip_assignment.member_id")).limit(1),
            ).as("member"),
            jsonObjectFrom(
              saleItemWithProductQb
                .select((eb) => [
                  subtract(sql.raw(`'${date.dateParam}'::date`), lower(contextRef("sale_item", "duration"))).as("duration_day_number"),
                  jsonArrayFrom(participationQb.where("participation.sale_item_id", "=", eb.ref("sale_item.id"))).as("participations"),
                  jsonObjectFrom(bookingQb(kysely).where("booking.id", "=", eb.ref("sale_item.booking_id"))).as("booking"),
                ])
                .where("sale_item.id", "=", eb.ref("participation.sale_item_id")),
            ).as("activity"),
            jsonObjectFrom(
              bookingQb(kysely)
                .where("booking.id", "=", eb.ref("sale_item.booking_id"))
                .select((eb) => {
                  return [
                    eb
                      .selectFrom("participant")
                      .innerJoin("participation", "participation.participant_id", "participant.id")
                      .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
                      .where("sale_item.booking_id", "=", eb.ref("booking.id"))
                      .select((eb) => arrayAgg(eb.ref("participant.stay")).as("stays"))
                      .where("participant.stay", "is not", null)
                      .distinct()
                      .as("participant_stays"),
                    eb
                      .selectFrom("invoice")
                      .select("invoice.id")
                      .where("invoice.booking_id", "=", eb.ref("booking.id"))
                      .limit(1)
                      .as("invoice_id"),
                    jsonArrayFrom(
                      saleItemWithProductQb
                        .where("sale_item.booking_id", "=", eb.ref("booking.id"))
                        .select((eb) =>
                          jsonArrayFrom(participationQb.where("participation.sale_item_id", "=", eb.ref("sale_item.id"))).as(
                            "participations",
                          ),
                        ),
                    ).as("activities"),
                  ];
                }),
            ).as("booking"),
          ])
          // .orderBy("trip.start_time", ascNullsLast)
          .$call((eb) => (tab ? callOrderColumns(eb, tab.sorts) : eb)),
      ).as("participations"),
    ])
    .execute();
  timer.end();
  return {
    date: date,
    tabs: finalTabs,
    establishments: establishments,
    ...createPageOverwrites({ customer_toggle: true, fixed_width: true }),
  };
};

type LoaderData = SerializeFrom<typeof loader>;

type Establishment = LoaderData["establishments"][number];
type Participation = Omit<Establishment["participations"][number], "signature">;

interface ColumnCellDef {
  Text: (item: Participation, response: LoaderData) => string | number | null | undefined;
  CellValue?: (item: Participation, response: LoaderData) => ReactNode;
}

interface ColumnDef {
  label: string;
  columns: ColumnCellDef[];
}

const participantDetailColumns: ColumnKey[] = ["booking_reference", "activity"];

const participantPanelKeys = ["gear_check", "trips"] as const;

type ParticipantPanelKey = (typeof participantPanelKeys)[number];

const columns: Record<ColumnKey, ColumnDef> = {
  name: {
    label: "Name",
    columns: [
      {
        Text: (item) => (item.participant ? item.participant.first_name + " " + item.participant.last_name : "-"),
        CellValue: (item) => {
          if (!item.participant) return defaultNotRegisteredValue;
          const name = item.participant.first_name + " " + item.participant.last_name;
          return (
            <ParamLink
              className="link"
              paramState={{
                toggle_modal: "detail",
                modal_detail_name: "participant" satisfies DetailModalName,
                modal_tab: undefined,
                participation_id: item.id,
              }}
            >
              {name}
            </ParamLink>
          );
        },
      },
    ],
  },
  booking_reference: {
    label: "Booking Ref.",
    columns: [
      {
        Text: (item) => item.booking?.booking_reference || item.booking?.sqid || "",
        CellValue: (item) => {
          if (!item.booking) return "";
          const value = item.booking.booking_reference || item.booking.sqid;
          return (
            <ParamLink className="link" path={_booking_detail(item.booking.id)}>
              {value}
            </ParamLink>
          );
        },
      },
    ],
  },
  booking_source: {
    label: "Booking Source",
    columns: [
      {
        Text: (item) => item.booking?.booking_source,
      },
    ],
  },
  country: {
    label: "Cntry",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.country_code),
      },
    ],
  },
  address: {
    label: "H/R Address",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.address),
      },
    ],
  },
  age: {
    label: "Age",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.age),
      },
    ],
  },
  birth_date: {
    label: "Birth Date",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.birth_date),
      },
    ],
  },
  gender: {
    label: "Gender",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.gender),
        CellValue: (item) => {
          const gender = item.participant?.gender;
          return (gender && genderOptions[gender as GenderOption]) || gender || "";
        },
      },
    ],
  },
  stay: {
    label: "Stay",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.stay),
        CellValue: (item) => {
          return (
            <div className="max-h-12 overflow-y-hidden">
              {item.participant?.stay || <span className="text-slate-300">{item.participant?.stay}</span>}
            </div>
          );
        },
      },
    ],
  },
  dives: {
    label: "Dives",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => getNrOfDivesOption(p.number_of_dives)[1]),
      },
    ],
  },
  level: {
    label: "Level",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => formatDivingCertShort(p.diving_certificate_level)),
      },
    ],
  },
  registration: {
    label: "Registration",
    columns: [
      {
        Text: (item) =>
          item.participant?.cached_incomplete_fields?.length === 0 && item.participant?.cached_read_waivers_valid !== false ? "Yes" : "No",
        CellValue: (item) => {
          if (item.participant?.cached_incomplete_fields?.length === 0 && item.participant?.cached_read_waivers_valid !== false)
            return <CheckDoneIcon className="w-5 h-5 text-green-500" />;
          return <NotDoneIcon className="w-5 h-5 text-red-500" />;
        },
      },
    ],
  },
  waivers: {
    label: "Waivers",
    columns: [
      {
        Text: (item) =>
          item.participant?.cached_signature_waivers_valid === true
            ? "Yes"
            : item.participant?.cached_signature_waivers_valid === false
              ? "No"
              : "",
        CellValue: (item) => {
          if (item.participant?.cached_signature_waivers_valid === true) return <CheckDoneIcon className="w-5 h-5 text-green-500" />;
          if (item.participant?.cached_signature_waivers_valid === false) return <NotDoneIcon className="w-5 h-5 text-red-500" />;
          return <span>-</span>;
        },
      },
    ],
  },
  activity: {
    label: "Activity",
    columns: [
      {
        Text: (item) => item?.product && getProductTitle(item.product),
      },
      {
        Text: (item) => item?.product && getActivity(item.product.activity_slug).short,
      },
      {
        Text: (item) => {
          const durationInDays = item.activity?.duration_in_days;
          const dayNumber = durationInDays && durationInDays > 1 ? `[day${(item.activity?.duration_day_number || 0) + 1}]` : "";
          return `${durationInDays + "d"} ${dayNumber}`;
        },
      },
    ],
  },
  comment: {
    label: "Comment",
    columns: [
      {
        Text: (item) => item?.comments?.[0]?.content,
        CellValue: (item) => {
          const search = useSearchParams2();
          const participationId = item.id;
          const comments = item?.comments || [];
          const firstComment = comments[0]?.content || "";
          return (
            <ParamLink
              className="w-full min-h-8 outline-slate-400 hover:outline outline-1 max-h-12 overflow-y-hidden rounded-md hover:cursor-default items-center flex"
              paramState={{
                participation_id: participationId,
                toggle_modal: "detail",
                modal_detail_name: "comment",
                element_action: [toInputId(fName("comment", "data.content", participationId))],
                rerender: search.state.rerender + 1,
              }}
            >
              <span>{firstComment}</span>
            </ParamLink>
          );
        },
      },
    ],
  },
  payment_status: {
    label: "Payment Status",
    columns: [
      {
        Text: (item) => item.booking && getPaymentState(item.booking),
        CellValue: (item) => {
          const booking = item.booking;
          if (!booking) return null;
          return (
            <div className="flex flex-row gap-1 items-center">
              {booking.invoice_id && <LockClosedIcon className="w-5 h-5 text-slate-500" />}
              <BookingPaymentBadge status={getPaymentState(booking)} />
            </div>
          );
        },
      },
    ],
  },
  contact_number: {
    label: "Contact Number",
    columns: [
      {
        Text: (item) => item.participant?.phone,
        CellValue: (item) => {
          const phone = item.participant?.phone;
          return (
            phone && (
              <a
                href={`tel:${phone}`}
                style={{
                  wordWrap: "break-word",
                  overflowWrap: "break-word",
                }}
                className="link"
              >
                {phone}
              </a>
            )
          );
        },
      },
    ],
  },
  instructor: {
    label: "Instructor",
    columns: [
      {
        CellValue: (item) => item.member?.name || <span className="opacity-50">Not scheduled</span>,
        Text: (item) => item.member?.name || defaultUnkownValue,
      },
    ],
  },
  meet: {
    label: "Meet",
    columns: [
      {
        Text: (item) => getMeetingType(item.booking?.meeting_type)?.short,
        CellValue: (item) => {
          const meetingType = getMeetingType(item.booking?.meeting_type);
          return (
            <ParamLink
              paramState={{
                modal_detail_name: "booking" satisfies DetailModalName,
                toggle_modal: "detail",
                participation_id: item.id,
              }}
              className={twMerge("block rounded-md p-2", meetingType?.key === "PICKUP" && "bg-orange-200")}
            >
              {meetingType?.short}
            </ParamLink>
          );
        },
      },
    ],
  },
  time: {
    label: "Time",
    columns: [
      {
        Text: (item) => item.booking?.meeting_time?.slice(0, 5) || "-",
        CellValue: (item) => {
          const startTime = item.booking?.meeting_time?.slice(0, 5) || "-";
          return (
            <div className="max-h-12 overflow-y-hidden">
              <ParamLink
                paramState={{
                  modal_detail_name: "booking" satisfies DetailModalName,
                  toggle_modal: "detail",
                  participation_id: item.id,
                }}
              >
                {startTime}
              </ParamLink>
            </div>
          );
        },
      },
    ],
  },
  location: {
    label: "Location",
    columns: [
      {
        Text: (item) => item.booking?.meeting_address,
        CellValue: (item) => {
          const meetingType = getMeetingType(item.booking?.meeting_type);
          return (
            <div className="max-h-12 overflow-y-hidden">
              <ParamLink
                paramState={{
                  modal_detail_name: "booking" satisfies DetailModalName,
                  toggle_modal: "detail",
                  participation_id: item.id,
                }}
              >
                {item.booking?.meeting_address || (
                  <span className="text-slate-300">{meetingType?.getPlaceholder(item.booking?.participant_stays)}</span>
                )}
              </ParamLink>
            </div>
          );
        },
      },
    ],
  },
  diet: {
    label: "Diet",
    columns: [
      {
        Text: (item) => item.participant?.diet,
        CellValue: (item) => safeFormat(item.participant, (p) => formatMealPref(p.diet)),
      },
    ],
  },
  allergies: {
    label: "Allergies",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.food_allergies),
      },
    ],
  },
  participant_comment: {
    label: "Cust. Remark",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.comment),
        CellValue: (item) => (
          <ParamLink
            paramState={{
              modal_detail_name: "participant_comment" satisfies DetailModalName,
              toggle_modal: "detail",
              participation_id: item.id,
            }}
            className="w-full min-h-8 outline-slate-400 hover:outline outline-1 max-h-12 overflow-y-hidden rounded-md hover:cursor-default items-center flex"
          >
            {safeFormat(item.participant, (p) => p.comment)}
          </ParamLink>
        ),
      },
    ],
  },
  booking_internal_note: {
    label: "Int. Note",
    columns: [
      {
        Text: (item) => item.booking?.internal_note,
        CellValue: (item) => (
          <ParamLink
            paramState={{
              modal_detail_name: "internal_booking_note" satisfies DetailModalName,
              toggle_modal: "detail",
              participation_id: item.id,
            }}
            className="w-full min-h-8 outline-slate-400 hover:outline outline-1 max-h-12 overflow-y-hidden rounded-md hover:cursor-default items-center flex"
          >
            {item.booking?.internal_note || ""}
          </ParamLink>
        ),
      },
    ],
  },
  height: {
    label: "Height",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.height_value && p.height_value + "" + p.height_unit),
        CellValue: (item) =>
          safeFormat(item.participant, (p) => (
            <HeightInfo value={p.height_value} unit={p.height_unit} default_unit={item.default_height_unit} />
          )),
      },
    ],
  },
  weight: {
    label: "Weight",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.weight_value && p.weight_value + "" + p.weight_unit),
        CellValue: (item) =>
          safeFormat(item.participant, (p) => (
            <WeightInfo value={p.weight_value} unit={p.weight_unit} default_unit={item.default_weight_unit} />
          )),
      },
    ],
  },
  wetsuit: {
    label: "Wetsuit",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.wetsuit_size),
      },
    ],
  },
  boots: {
    label: "Boots",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.shoe_size_value && p.shoe_size_value + " " + p.shoe_size_unit),
        CellValue: (item) =>
          safeFormat(item.participant, (p) => (
            <ShoeSizeInfo value={p.shoe_size_value} unit={p.shoe_size_unit} default_unit={item.default_shoe_size_unit} />
          )),
      },
    ],
  },
  bcd_size: {
    label: "BCD",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.bcd_size),
      },
    ],
  },
  passport_number: {
    label: "PPT No.",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.passport_number),
      },
    ],
  },
  room: {
    label: "Room",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.room),
      },
    ],
  },
  insurance: {
    label: "Insurance",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.insurance),
      },
    ],
  },
  emergency_contact_name: {
    label: "Emerg. Contact",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.emergency_contact_name),
      },
    ],
  },
  emergency_contact_phone: {
    label: "Emerg. Phone",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.emergency_contact_phone),
      },
    ],
  },
  emergency_contact_relationship: {
    label: "Emerg. Relation",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.emergency_contact_relationship),
      },
    ],
  },
  diving_certificate_organization: {
    label: "Cert. Org.",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.diving_certificate_organization),
      },
    ],
  },
  diving_certificate_number: {
    label: "Cert. No.",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.diving_certificate_number),
      },
    ],
  },
  years_of_experience: {
    label: "Yrs Exp.",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => formatExperienceinYears(p.years_of_experience)),
        CellValue: (item) => (
          <span className="whitespace-nowrap">{safeFormat(item.participant, (p) => formatExperienceinYears(p.years_of_experience))}</span>
        ),
      },
    ],
  },
  last_dive_within_months: {
    label: "Last Dive",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.last_dive_within_months),
        CellValue: (item) => {
          return safeFormat(item.participant, (p) => getLastDivedShort(p.last_dive_within_months));
        },
      },
    ],
  },
  my_gear: {
    label: "Own Gear",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.my_gear?.join(", ")),
        CellValue: (item) => safeFormat(item.participant, (p) => p.my_gear?.join(", ")),
      },
    ],
  },
  weightbelt: {
    label: "Dive Wts.",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.weightbelt_value && p.weightbelt_value + "" + p.weightbelt_unit),
        CellValue: (item) =>
          safeFormat(item.participant, (p) => (
            <WeightInfo value={p.weightbelt_value} unit={p.weightbelt_unit} default_unit={item.default_weight_unit} />
          )),
      },
    ],
  },
  referral_source: {
    label: "Referral Src.",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.referral_source),
      },
    ],
  },
  allow_contact_for_experience: {
    label: "Contact Permission",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => (p.allow_contact_for_experience ? "Yes" : "No")),
      },
    ],
  },
  instagram: {
    label: "Instagram",
    columns: [
      {
        Text: (item) => safeFormat(item.participant, (p) => p.instagram),
      },
    ],
  },
  direct_booking: {
    label: "Direct booking",
    columns: [
      {
        Text: (item) => (item.booking?.direct_booking ? "Yes" : ""),
        CellValue: (item) =>
          item.booking?.direct_booking && <span className="p-1 bg-slate-600 rounded-md px-2 text-xs text-white">Direct</span>,
      },
    ],
  },
  trip_time: {
    label: "Trip Time",
    columns: [
      {
        Text: (item, response) => {
          const trip = response.establishments.flatMap((e) => e.trips).find((trip) => trip.id === item.trip_id);
          return trip?.start_time?.slice(0, 5) || "-";
        },
        CellValue: (item, response) => {
          const trip = response.establishments.flatMap((e) => e.trips).find((trip) => trip.id === item.trip_id);
          const startTime = trip?.start_time?.slice(0, 5) || "-";
          return <div className="max-h-12 overflow-y-hidden">{startTime}</div>;
        },
      },
    ],
  },
  trip_type: {
    label: "Trip Type",
    columns: [
      {
        Text: (item, response) => {
          const trip = response.establishments.flatMap((e) => e.trips).find((trip) => trip.id === item.trip_id);
          return trip ? getTripType(trip.type)?.name || trip.type : "-";
        },
        CellValue: (item, response) => {
          const trip = response.establishments.flatMap((e) => e.trips).find((trip) => trip.id === item.trip_id);
          const tripType = trip ? getTripType(trip.type) : null;
          return <div className="max-h-12 overflow-y-hidden">{tripType?.name || trip?.type || "-"}</div>;
        },
      },
    ],
  },
  gear_check: {
    label: "Gear Check",
    columns: [
      {
        Text: (item) => (item.participant?.participant_day?.rental_checked ? "Yes" : "No"),
        CellValue: (item, data) => {
          const ctx = useAppContext();
          const participant = item.participant;
          if (!participant) return null;
          const isChecked = participant.participant_day?.rental_checked;
          const selectedRentablesForToday =
            participant.all_rental_assignments?.filter((rental) => rental.date === ctx.date.dateParam) || [];
          const rentablesByOtherParticipantsOnSameDay = flat(
            data.establishments.map((establishment) => {
              return flat(
                establishment.participations
                  .filter((item) => item.participant_id !== participant.id)
                  .map((item) => item.participant?.all_rental_assignments || []),
              ).filter((rental) => rental.date === ctx.date.dateParam);
            }),
          );
          const assignedByOtherParticipantTheSameDay = selectedRentablesForToday.filter((rentalAssignment) =>
            rentablesByOtherParticipantsOnSameDay.map((item) => item.rentable_id).includes(rentalAssignment.rentable_id),
          );
          return (
            <ParamLink
              className={twMerge(
                "btn btn-sm whitespace-nowrap flex flex-row items-center gap-2",
                isChecked ? "" : selectedRentablesForToday.length ? "btn-basic" : "btn-basic",
              )}
              paramState={{
                modal_detail_name: "participant" satisfies DetailModalName,
                modal_tab: "gear_check" satisfies ColumnKey,
                toggle_modal: "detail",
                participation_id: item.id,
              }}
            >
              {isChecked ? (
                <CheckDoneIcon
                  className={twMerge("w-4 h-4 text-green-600", assignedByOtherParticipantTheSameDay.length && "text-primary")}
                />
              ) : (
                !!assignedByOtherParticipantTheSameDay.length && <FiAlertCircle className="w-4 h-4 text-primary" />
              )}
              {selectedRentablesForToday.length + " items"}
            </ParamLink>
          );
        },
      },
    ],
  },
};

const createMapsUrl = (waypoints: string[], from: string | null, to: string | null) => {
  const mapsUrl = new URL("https://www.google.com/maps/dir/");
  if (from) {
    mapsUrl.searchParams.set("origin", from);
  }
  mapsUrl.searchParams.set("api", "1");
  mapsUrl.searchParams.set("travelmode", "driving");
  if (waypoints.length > 0) {
    mapsUrl.searchParams.set("waypoints", waypoints.join("|"));
  }
  if (to) {
    mapsUrl.searchParams.set("destination", to);
  }
  return mapsUrl;
};

export const ParticipationCelComp = (props: { column: ColumnCellDef; participation: Participation }) => {
  const data = useLoaderData<typeof loader>();
  const valueDef = props.column.CellValue || props.column.Text;
  return valueDef(props.participation, data);
};

export const ViewOrderInput = (props: { item: IdName }) => {
  const sortable = useSortable({ id: props.item.id });
  const style = {
    transform: CSS.Transform.toString(sortable.transform),
    transition: sortable.transition,
    opacity: sortable.isDragging ? 0.5 : 1,
  };
  return (
    <div
      className={twMerge("flex flex-row gap-1 items-center", sortable.isDragging && "z-10")}
      ref={sortable.setNodeRef}
      style={style}
      {...sortable.attributes}
    >
      <button {...sortable.listeners} className="pr-2 py-2 touch-none" type="button">
        <GrDrag />
      </button>
      <span>{props.item.name}</span>
    </div>
  );
};

export const ViewOrderForm = () => {
  const data = useLoaderData<typeof loader>();
  const [changes, setChanges] = useState(data.tabs);
  return (
    <div className="grid md:grid-cols-2 lg:grid-cols-3 items-center gap-3">
      <DndContext
        onDragEnd={(e) => {
          const { active, over } = e;
          if (!over) return;
          if (active.id === over.id) return;

          const oldIndex = changes.findIndex((item) => item.id === active.id);
          const newIndex = changes.findIndex((item) => item.id === over.id);

          const newChanges = arrayMove(changes, oldIndex, newIndex);
          setChanges(newChanges);
        }}
      >
        <SortableContext items={changes}>
          {changes.map((view, index) => {
            return (
              <div key={view.id}>
                <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined }} />
                <RInput table={"view"} field={"id"} index={view.id} value={view.id} />
                <RInput table={"view"} field={"data.sort_order"} index={view.id} value={index + ""} type={"hidden"} />
                <ViewOrderInput key={view.id} item={view} />
              </div>
            );
          })}
        </SortableContext>
      </DndContext>
    </div>
  );
};

export const OrderInput = (props: { item: { key: string; direction: string; id: number }; onChange: (value: Sort) => void }) => {
  const sortable = useSortable({ id: props.item.id, disabled: !props.item.key });
  const order = props.item;
  const style = {
    transform: CSS.Transform.toString(sortable.transform),
    transition: sortable.transition,
    opacity: sortable.isDragging ? 0.5 : 1,
  };
  return (
    <div
      className={twMerge("flex flex-row gap-1 items-center", sortable.isDragging && "z-10")}
      ref={sortable.setNodeRef}
      style={style}
      {...sortable.attributes}
    >
      <button {...sortable.listeners} disabled={!props.item.key} className="pr-2 py-2 touch-none" type="button">
        {props.item.key ? <GrDrag /> : <PlusIcon className="w-4 h-4" />}
      </button>
      <select
        className="select"
        name={fName("view", "data.sorts", 0, sortable.index, "key")}
        value={order.key}
        onChange={(e) => {
          props.onChange({ key: e.target.value || ("" as any), direction: order.direction as any });
        }}
      >
        <option value={""}>{order.key ? " -- remove --" : " -- select --"}</option>
        {Object.entries(sortableColumns).map(([sortKey, sort]) => {
          return (
            <option value={sortKey} key={sortKey} disabled={sortKey !== order.key && !!sortable.items.find((order) => order === sortKey)}>
              {sort.name}
            </option>
          );
        })}
      </select>
      <select
        name={fName("view", "data.sorts", 0, sortable.index, "direction")}
        disabled={!order.key}
        value={order.direction}
        onChange={(e) => {
          props.onChange({ key: order.key as any, direction: e.target.value as any });
        }}
        className="select"
      >
        <option value="asc">asc</option>
        <option value="desc">desc</option>
      </select>
    </div>
  );
};

export const OrderByForm = (props: { sorts: Sort[] }) => {
  console.log("props.sorrts", props.sorts);
  const [changes, setChanges] = useState(() => [
    ...props.sorts.map((sort, index) => ({
      ...sort,
      id: index + 1,
    })),
    // { id: props.sorts.length + 1, key: "", direction: "asc" },
  ]);
  const finalOrders = [
    ...changes,
    {
      id: changes.map((change) => change.id).reduce((acc, item) => (item > acc ? item : acc), 0) + 1,
      key: "",
      direction: "asc",
    },
  ];
  // const finalOrders = changes.at(-1)?.key === "" ? changes : [...orders, emptyOrderBy]; changes;
  // console.log("final orders", finalOrders, props.sorts);
  return (
    <div className="space-y-3">
      <HiddenTypeInput name={fName("view", "data.sorts", 0)} value={"__to_string__"} />
      <DndContext
        onDragEnd={(e) => {
          const { active, over } = e;
          if (!over) return;
          if (active.id === over.id) return;

          const oldIndex = finalOrders.findIndex((item) => item.id === active.id);
          const newIndex = finalOrders.findIndex((item) => item.id === over.id);

          const newChanges = arrayMove(changes, oldIndex, newIndex);
          setChanges(newChanges);
        }}
      >
        <SortableContext items={changes}>
          {finalOrders.map((order) => {
            return (
              <OrderInput
                key={order.id}
                item={order}
                onChange={(value) => {
                  const newOrders = finalOrders
                    .map((orderItem) => {
                      if (orderItem.id !== order.id) return orderItem;
                      return {
                        id: orderItem.id,
                        key: value.key as any,
                        direction: value.direction as any,
                      };
                    })
                    .filter((orderItem) => orderItem.key);
                  setChanges(newOrders);
                }}
              />
            );
          })}
        </SortableContext>
      </DndContext>
    </div>
  );
};

const ColumnInput = (props: { columnKey: string; newItems: string[] | null; onToggle: () => void }) => {
  const sortable = useSortable({ id: props.columnKey, animateLayoutChanges: () => false });
  const column = columns[props.columnKey as ColumnKey];
  const style = {
    transform: CSS.Transform.toString(sortable.transform),
    transition: sortable.transition,
    opacity: sortable.isDragging ? 0.5 : 1,
  };

  const hiddenIndex = (props.newItems || sortable.items).findIndex((column) => column === "_");
  const enabled = hiddenIndex > sortable.newIndex;

  if (!column)
    return (
      <div
        className="flex flex-row items-center gap-3 text-slate-400 touch-none"
        ref={sortable.setNodeRef}
        {...sortable.attributes}
        {...sortable.listeners}
        style={style}
      >
        <GrDrag />
        {/*<Bars2Icon className="w-4 h-4" />*/}
        <span className="flex flex-row gap-1 items-center">
          <EyeOffIcon className="h-4 w-4" />
          Hide
        </span>
      </div>
    );

  return (
    <div
      className={twMerge("flex flex-row gap-1 items-center", sortable.isDragging && "z-10")}
      ref={sortable.setNodeRef}
      style={style}
      {...sortable.attributes}
    >
      <button {...sortable.listeners} className="pr-2 py-2 touch-none" type="button">
        <GrDrag />
      </button>
      <label className="flex flex-row gap-2 items-center">
        {/*<input name={"toggle_columns" satisfies StateInputKey} type={"hidden"} value={""} />*/}
        <input
          name={fName("view", "data.columns", 0, sortable.index)}
          type={"checkbox"}
          className="checkbox"
          value={props.columnKey}
          checked={enabled}
          onChange={props.onToggle}
          // defaultChecked={props.selectedColumnKeys.includes(columnKey)}
        />
        <span className="whitespace-nowrap">{column.label}</span>
      </label>
    </div>
  );
};

const ColumnInputs = (props: { selectedColumnKeys: string[] }) => {
  const [changes, setChanges] = useState<string[]>(() => {
    const disabledColumsn = columnKeys.filter((columnKey) => !props.selectedColumnKeys.includes(columnKey));
    return [...props.selectedColumnKeys, "_", ...disabledColumsn];
  });
  const [moveChanges, setMoveChanges] = useState<string[] | null>(null);

  const hiddenIndex = changes.findIndex((column) => column === "_");

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );
  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragMove={(e) => {
        const { active, over } = e;
        if (!over) return;
        if (active.id === over.id) return;

        const oldIndex = changes.findIndex((item) => item === active.id);
        const newIndex = changes.findIndex((item) => item === over.id);

        const newChanges = arrayMove(changes, oldIndex, newIndex);
        setMoveChanges(newChanges);
      }}
      onDragEnd={(e) => {
        setMoveChanges(null);
        const { active, over } = e;
        if (!over) return;
        if (active.id === over.id) return;

        const oldIndex = changes.findIndex((item) => item === active.id);
        const newIndex = changes.findIndex((item) => item === over.id);

        const newChanges = arrayMove(changes, oldIndex, newIndex);
        setChanges(newChanges);
      }}
    >
      <SortableContext items={changes}>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-1 gap-x-3">
          {changes.map((column, index) => {
            return (
              <ColumnInput
                key={column}
                columnKey={column}
                newItems={moveChanges}
                onToggle={() => {
                  setChanges(arrayMove(changes, index, hiddenIndex));
                }}
              />
            );
          })}
        </div>
      </SortableContext>
    </DndContext>
  );
};

export const meta = createMeta({ title: "Participants" });

const TankAssignmentForm = (props: { trip_assignment_id: string }) => {
  const data = useLoaderData<typeof loader>();
  const ctx = useAppContext();
  const establishmentLiterOptions = uniqueTextArray(ctx.establishment?.tanks, literOptions);
  const establishmentGasOptions = uniqueTextArray(ctx.establishment?.blends, gasOptions);
  const tripAssignment = data.establishments
    .flatMap((establishment) => establishment.participations)
    .find((item) => item.trip_assignment_id === props.trip_assignment_id);
  const initialTankAssignments = (tripAssignment?.tank_assignments || []).map((tank) => ({ ...tank, delete: false }));

  const reset = useBoolean();
  const [tankAssignments, setTankAssignments] = useState<
    (Omit<Selectable<TankAssignment>, "trip_assignment_id" | "sort_order"> & {
      delete: boolean;
    })[]
  >(initialTankAssignments);

  if (!tripAssignment) {
    return <div>Trip assignment not found.</div>;
  }

  return (
    <div>
      <ActionForm key={reset.isOn + "reset"} onCheckEqual={defaultEqualCheck} className="group space-y-3">
        <OnFormSuccess
          onSuccess={() => {
            setTankAssignments(initialTankAssignments);
            reset.toggle();
          }}
        />
        {tankAssignments
          .filter((tank) => !tank.delete || initialTankAssignments.find((initialTank) => initialTank.id === tank.id))
          .map((tank, index) => {
            const isUuid = isUuidRegex.test(tank.id);

            const finalGasOptions = selectListWithFallback(establishmentGasOptions, tank.gas);
            const finalLiterOptions = selectListWithFallback(establishmentLiterOptions, tank.liters);
            return (
              <div key={tank.id} className="pl-2 text-xs text-slate-600">
                <div className="flex flex-row gap-3 items-center">
                  {tank.delete && <OperationInput table="tank_assignment" value="delete" index={tank.id} />}
                  {isUuid && <RInput table="tank_assignment" field="id" index={tank.id} value={tank.id} type="hidden" />}
                  <RInput table="tank_assignment" field="data.sort_order" index={tank.id} value={index} type="hidden" />
                  <RInput
                    table="tank_assignment"
                    field="data.trip_assignment_id"
                    index={tank.id}
                    value={tripAssignment.trip_assignment_id || ""}
                    type="hidden"
                  />
                  <div>
                    <RInput
                      label="Quantity"
                      table="tank_assignment"
                      field="data.quantity"
                      disabled={tank.delete}
                      index={tank.id}
                      defaultValue={tank.quantity}
                      type="number"
                      min={1}
                      max={100}
                      className="input disabled:line-through md:w-20"
                    />
                  </div>
                  <div>
                    <RLabel table="tank_assignment" field="data.liters" index={tank.id}>
                      Size
                    </RLabel>
                    <br />
                    <RSelect
                      table="tank_assignment"
                      field="data.liters"
                      className="md:w-32 select disabled:line-through"
                      disabled={tank.delete}
                      index={tank.id}
                      defaultValue={tank.liters}
                    >
                      {finalLiterOptions.map((liter) => (
                        <option key={liter} value={liter}>
                          {liter}
                        </option>
                      ))}
                    </RSelect>
                  </div>
                  <div className="flex-1">
                    <RLabel table="tank_assignment" field="data.gas" index={tank.id}>
                      Gas
                    </RLabel>
                    <br />
                    <RSelect
                      table="tank_assignment"
                      field="data.gas"
                      disabled={tank.delete}
                      index={tank.id}
                      defaultValue={tank.gas}
                      className="w-full select disabled:line-through"
                    >
                      {finalGasOptions.map((gas) => (
                        <option key={gas} value={gas}>
                          {gas}
                        </option>
                      ))}
                    </RSelect>
                  </div>
                  <div className="pt-4">
                    <button
                      type="button"
                      onClick={() => {
                        const newTankAssignments = tankAssignments.map((thisTank) => {
                          if (thisTank.id === tank.id)
                            return {
                              ...thisTank,
                              delete: !thisTank.delete,
                            };
                          return thisTank;
                        });
                        setTankAssignments(newTankAssignments);
                        refreshFormdata();
                      }}
                    >
                      {tank.delete ? <UndoIcon className="w-4 h-4" /> : <XMarkIcon className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
              </div>
            );
          })}

        <div className="flex flex-row gap-2">
          <button
            type="button"
            className="btn link gap-1 font-normal"
            onClick={() => {
              setTankAssignments([
                ...tankAssignments,
                {
                  id: tankAssignments.length + "",
                  quantity: 1,
                  gas: "AIR" satisfies GasOption,
                  liters: "12L" satisfies LiterOption,
                  delete: false,
                },
              ]);
              refreshFormdata();
            }}
          >
            <PlusIcon className="w-4 h-4" />
            Add Tank
          </button>
          <div className="flex-1"></div>
          <div className="flex flex-row gap-2 group-data-equal:hidden">
            <button
              type="button"
              className="btn btn-basic"
              onClick={() => {
                reset.toggle();
                setTankAssignments(initialTankAssignments);
              }}
            >
              Cancel
            </button>
            <SubmitButton className="btn btn-primary">Save</SubmitButton>
          </div>
        </div>
      </ActionForm>
    </div>
  );
};

function reorderList(list: string[], startIndex: number) {
  if (!list[startIndex]) return list;

  const partAfter = list.slice(startIndex + 1);
  const partBefore = list.slice(0, startIndex);
  return [...partAfter, ...partBefore];
}

const ParticipantDetailDialogContent = () => {
  const data = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const ctx = useAppContext();

  const allParticipatoins = flat(
    data.establishments.map((establishment) =>
      establishment.participations.map((item) => ({
        ...item,
        establishment: establishment,
      })),
    ),
  );

  const allTrips = flat(data.establishments.map((establishment) => establishment.trips));
  const selectedParticipation = allParticipatoins.find((participation) => participation.id === search.state.participation_id);
  const selectedParticipant = selectedParticipation?.participant;
  const myParticipations = allParticipatoins.filter((item) => item.participant_id && item.participant_id === selectedParticipant?.id);
  const myTrips = allTrips.filter((trip) => myParticipations.find((item) => item.trip_id === trip.id));
  const myTripTimes = unique(myTrips.map((trip) => trip.start_time)).filter((time) => !!time);
  const selectedTrip = allTrips.find((trip) => trip.id === selectedParticipation?.trip_id) || null;
  const selectedEstablishment = data.establishments.find((establishment) => establishment.id === selectedParticipant?.establishment_id);
  const establishmentRentables = uniqueBy(selectedEstablishment?.rentables || [], (rentable) => rentable.id);
  const rentableTypes = unique(establishmentRentables.map((rentable) => rentable.type)).sort((a, b) => a.localeCompare(b));
  const allRentalAssignments =
    selectedEstablishment?.participations.flatMap((participation) => participation.participant?.all_rental_assignments || []) || [];
  const participantRentalAssignmentsForToday = allRentalAssignments.filter(
    (rental) => rental.participant_id === selectedParticipant?.id && rental.date === ctx.date.dateParam,
  );
  const initialSelectedRentableIds = unique(participantRentalAssignmentsForToday.map((item) => item.rentable_id));

  const [selectedRentableIds, setSelectedRentableIds] = useState(initialSelectedRentableIds);
  const initialRentalCompleted = !!selectedParticipant?.participant_day?.rental_checked;
  const rentalCompleted = useBoolean(initialRentalCompleted);
  const openAssignedGear = useBoolean(initialRentalCompleted);

  const diff = symmetricDifference(selectedRentableIds, initialSelectedRentableIds);
  const hasChanges = diff.length > 0 || rentalCompleted.isOn !== initialRentalCompleted;
  const mapEstablishmentRentables = establishmentRentables.map((rentable) => {
    const isSelected = selectedRentableIds.includes(rentable.id);
    const initialParticipantRentals = participantRentalAssignmentsForToday.filter((rental) => rental.rentable_id === rentable.id);
    const selectedByOther = allRentalAssignments.filter(
      (rental) =>
        rental.rentable_id === rentable.id && rental.participant_id !== selectedParticipant?.id && rental.date === ctx.date.dateParam,
    );

    const tripIdsOfOtherParticipants = unique(
      selectedEstablishment?.participations
        .filter((item) => item.participant?.all_rental_assignments.find((rentalAssignmen) => selectedByOther.includes(rentalAssignmen)))
        .map((item) => item.trip_id) || [],
    );

    const tripsOfOtherParticipants = allTrips.filter((trip) => tripIdsOfOtherParticipants.includes(trip.id));

    const otherTripTimes = unique(tripsOfOtherParticipants.map((trip) => trip.start_time)).filter((time) => !!time);
    const overlappingTripsTimes = intersection(myTripTimes, otherTripTimes);

    const selectedOnOtherDay = allRentalAssignments.filter(
      (rental) =>
        rental.rentable_id === rentable.id && rental.participant_id === selectedParticipant?.id && rental.date !== ctx.date.dateParam,
    );
    const wasSelected = !isSelected && !!initialParticipantRentals.length;
    return {
      ...rentable,
      initialParticipantRentals: initialParticipantRentals,
      isSelected,
      wasSelected,
      selectedByOther,
      tripIdsOfOtherParticipants: tripIdsOfOtherParticipants,
      tripsOfOtherParticipants: tripsOfOtherParticipants,
      overlappingTripsTimes: overlappingTripsTimes,
      selectedOnOtherDay,
    };
  });
  const rentablesWithConflicts = mapEstablishmentRentables.filter(
    (rentable) => rentable.isSelected && rentable.tripsOfOtherParticipants.length,
  );
  // .sort((a, b) => (a.isSelected === b.isSelected ? 0 : a.isSelected ? -1 : 1));

  const toBeDeletedRentalAssignments = participantRentalAssignmentsForToday.filter(
    (rental) => !selectedRentableIds.includes(rental.rentable_id),
  );

  const toBeAddedRentalAssignments = mapEstablishmentRentables.filter(
    (rentable) => rentable.isSelected && !rentable.initialParticipantRentals.length,
  );

  const [rentalAssignmentSearch, setRentalAssignmentSearch] = useState<string>("");
  const selectTypeIndex = rentableTypes.indexOf(rentalAssignmentSearch);
  const nextUnassignedType = reorderList(rentableTypes, selectTypeIndex).find(
    (type) => !mapEstablishmentRentables.find((rentable) => rentable.type === type && rentable.isSelected),
  );

  if (!selectedEstablishment) {
    return <div>Establishment not found.</div>;
  }

  if (!selectedParticipation) {
    return <div>Participation not found.</div>;
  }

  if (!selectedParticipant) {
    return <div>Participant not found.</div>;
  }

  return (
    <div className="space-y-6 w-full md:w-[600px]">
      <div className="bg-white p-3 space-y-3">
        <div className="flex flex-wrap gap-2 items-center">
          <div className="p-4 rounded-full bg-slate-200 text-slate-700">
            <ProfileIcon className="w-7 h-7" />
          </div>
          <div>
            <p className="font-bold">{selectedParticipant?.full_name}</p>
            <p className="text-sm text-slate-600">{selectedParticipant?.email}</p>
          </div>
          <div className="flex-1 "></div>
          <div className="whitespace-nowrap text-slate-500">{ctx.date.dateParam}</div>
        </div>
        <hr />
        <div className="grid grid-cols-2 gap-x-2 gap-y-3">
          <div>Name</div>
          <div>
            <ParamLink path={_participant_detail(selectedParticipant?.id)} className="link">
              {selectedParticipant?.first_name} {selectedParticipant?.last_name}
            </ParamLink>
          </div>
          {participantDetailColumns.map((column) => {
            const columnDef = columns[column];
            if (!columnDef) return null;
            const cell = columnDef.columns[0];
            if (!cell) return null;

            return (
              <Fragment key={column}>
                <div>{columnDef.label}</div>
                <div className="flex flex-wrap gap-2">
                  {columnDef.columns.map((cell, index) => (
                    <div key={index}>{cell.CellValue?.(selectedParticipation, data) || cell.Text?.(selectedParticipation, data)}</div>
                  ))}
                </div>
              </Fragment>
            );
          })}
          <div>Experience</div>
          <div>
            {formatDivingCertShort(selectedParticipant.diving_certificate_level)}{" "}
            {getNrOfDivesOption(selectedParticipant.number_of_dives)[1]}
          </div>
        </div>
      </div>
      <AnimatingDiv className="bg-white">
        <ParamLink
          className="flex flex-row gap-2 items-center justify-between p-3"
          paramState={{
            modal_tab:
              search.state.modal_tab === ("trips" satisfies ParticipantPanelKey) ? undefined : ("trips" satisfies ParticipantPanelKey),
          }}
        >
          <p className="font-medium">Trips ({myTrips.length})</p>
          <ChevronDownIcon
            className={twMerge(
              "w-4 h-4 transition-transform",
              search.state.modal_tab === ("trips" satisfies ParticipantPanelKey) && "rotate-180",
            )}
          />
        </ParamLink>
        {search.state.modal_tab === ("trips" satisfies ParticipantPanelKey) && (
          <div className="p-2">
            {!allTrips.length && (
              <p className="px-2 text-slate-500">
                No Scheduled Trips. Add a trip on the{" "}
                <ParamLink
                  className={"link"}
                  path={_planning}
                  log
                  hash={addTripPanelTitleId}
                  paramState={{ toggle_trip_add_panel: selectedParticipant.establishment_id }}
                >
                  planning page
                </ParamLink>
              </p>
            )}
            {allTrips.map((trip) => {
              const sitesStr = toSitesStr(trip.sites);
              const tripType = getTripType(trip.type);
              const tripAssignments = allParticipatoins.filter(
                (participation) => participation.trip_id === trip.id && participation.participant_id === selectedParticipant?.id,
              );
              const sitesCount = trip.sites.length || 0;
              const totalTanks = sum(
                tripAssignments.flatMap((assignment) => assignment.tank_assignments || []).map((tank) => tank.quantity),
              );
              return (
                <div key={trip.id} className={twMerge("p-2 mb-1 border-b border-slate-200")}>
                  <div className="flex flex-col">
                    <div className="flex items-center pb-1 pt-2 gap-2">
                      <span>
                        {trip.start_time?.slice(0, 5)} {tripType?.name} {sitesStr && `| ${sitesStr}`}
                      </span>
                      |
                      <span
                        className={twMerge(
                          "text-sm text-slate-500 flex flex-row items-center whitespace-nowrap",
                          totalTanks > sitesCount && "text-orange-500",
                        )}
                      >
                        {totalTanks} Tanks {totalTanks > sitesCount && <ExclamationCircleIcon className="w-4 h-4" />}
                      </span>
                      <div className="flex-1" />
                      <ActionForm>
                        {tripAssignments.length ? (
                          <Fragment>
                            {tripAssignments.map((assignment) => {
                              const tripAssignmentId = assignment.trip_assignment_id;
                              if (!tripAssignmentId) return null;
                              return (
                                <Fragment key={assignment.id}>
                                  <RInput
                                    table="trip_assignment"
                                    field="id"
                                    index={tripAssignmentId}
                                    value={tripAssignmentId}
                                    type="hidden"
                                  />
                                  <OperationInput table="trip_assignment" value="delete" index={tripAssignmentId} />
                                  {assignment.tank_assignments?.map((tank) => (
                                    <Fragment key={tank.id}>
                                      <RInput table="tank_assignment" field="id" index={tank.id} value={tank.id} type="hidden" />
                                      <OperationInput table="tank_assignment" value="delete" index={tank.id} />
                                    </Fragment>
                                  ))}
                                </Fragment>
                              );
                            })}
                          </Fragment>
                        ) : (
                          <Fragment>
                            <RInput table="trip_assignment" field="data.trip_id" value={trip.id} type="hidden" />
                            <RInput table="trip_assignment" field="data.participation_id" value={selectedParticipation.id} type="hidden" />
                            <RInput table="trip_assignment" field="data.role" value={"instructor" satisfies Role} type="hidden" />
                          </Fragment>
                        )}
                        <SubmitButton className="group" aria-selected={!!tripAssignments.length}>
                          <Checker className="border border-slate-300" />
                        </SubmitButton>
                      </ActionForm>
                    </div>
                    <div className="pt-2">
                      {tripAssignments.map((tripAssignment) => {
                        if (!tripAssignment.trip_assignment_id) return null;
                        return (
                          <TankAssignmentForm
                            key={tripAssignment.trip_assignment_id}
                            trip_assignment_id={tripAssignment.trip_assignment_id}
                          />
                        );
                      })}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </AnimatingDiv>
      {/* {JSON.stringify(search.state, null, 2)} */}
      <AnimatingDiv className="space-y-3 bg-white">
        <ParamLink
          className="flex flex-row gap-2 items-center justify-between p-3"
          paramState={{
            modal_tab: search.state.modal_tab === ("gear_check" satisfies ColumnKey) ? undefined : ("gear_check" satisfies ColumnKey),
          }}
        >
          <p className="font-medium flex flex-row gap-2 items-center">
            Gear & Sizes{" "}
            <Tooltip
              description={
                rentablesWithConflicts.length
                  ? "Rental items for this participants are also assigned to other participants on this day."
                  : null
              }
            >
              {initialRentalCompleted ? (
                <CheckDoneIcon className={twMerge("w-4 h-4 text-green-600", rentablesWithConflicts.length && "text-primary")} />
              ) : (
                !!rentablesWithConflicts.length && <FiAlertCircle className="w-4 h-4 text-primary" />
              )}
            </Tooltip>{" "}
            {!!initialSelectedRentableIds.length && (
              <div className="text-sm text-slate-500">({initialSelectedRentableIds.length} items)</div>
            )}
          </p>
          <ChevronDownIcon
            className={twMerge(
              "w-4 h-4 transition-transform",
              search.state.modal_tab === ("gear_check" satisfies ColumnKey) && "rotate-180",
            )}
          />
        </ParamLink>
        {search.state.modal_tab === ("gear_check" satisfies ColumnKey) && (
          <div className="space-y-3 px-3">
            <div>
              <p className="font-medium mb-2 text-slate-500">Filled by Customer:</p>
              <div className="flex flex-wrap gap-x-5 gap-y-1 text-slate-500">
                {(["height", "weight", "wetsuit", "bcd_size", "boots", "weightbelt", "my_gear"] satisfies ColumnKey[]).map(
                  (measurement) => {
                    const column = columns[measurement];
                    if (!column) return null;
                    const cell = column.columns[0];
                    if (!cell) return null;
                    const label = column.label;
                    const value = cell.CellValue?.(selectedParticipation, data) || cell.Text?.(selectedParticipation, data);
                    if (!value) return null;
                    return (
                      <div key={measurement} className="flex flex-row gap-1 items-center">
                        {label}: {value || "-"}
                      </div>
                    );
                  },
                )}
              </div>
            </div>
            <hr />
            <button type="button" onClick={openAssignedGear.toggle} className="flex flex-row gap-2 items-center w-full text-left">
              <div className="flex-1 text-sm text-slate-500">Assigned gear</div>
              <div className="flex-1 text-sm text-slate-500">{selectedRentableIds.length} items</div>
            </button>
            <hr />
            {openAssignedGear.isOn && (
              <div
                className={`grid gap-x-3 gap-y-1 items-center w-full 
                                    max-sm:grid-cols-[min-content_minmax(0,auto)_minmax(0,auto)_minmax(0,auto)_min-content]
                    grid-cols-[min-content_minmax(0,auto)_minmax(0,auto)_minmax(0,auto)_minmax(0,auto)_min-content]
              `}
              >
                {mapEstablishmentRentables
                  .filter((rentable) => rentable.isSelected)
                  .sort((a, b) => {
                    const typeSort = a.type.localeCompare(b.type);
                    if (typeSort) return typeSort;
                    const referenceIdSort = a.reference_id.localeCompare(b.reference_id);
                    return referenceIdSort;
                  })
                  .map((rentable) => {
                    return (
                      <div key={rentable.id} className="contents text-sm md:text-base">
                        <div>
                          {rentable.selectedByOther.length ? (
                            <Tooltip
                              description={
                                <p>
                                  This item is assigned to multiple participants today.
                                  <br />
                                  Ensure there's no trip time overlap causing a conflict.
                                </p>
                              }
                            >
                              <FiAlertCircle className="w-4 h-4 text-primary" />
                            </Tooltip>
                          ) : (
                            <CheckDoneIcon className="w-4 h-4 text-green-600" />
                          )}
                        </div>
                        <div className="whitespace-nowrap text-left flex flex-row gap-1 items-center overflow-clip">
                          <span className="flex-1">{rentable.type}</span>
                        </div>
                        <div className="whitespace-nowrap max-sm:hidden overflow-clip">{rentable.brand}</div>
                        <div className="whitespace-nowrap">{rentable.size}</div>
                        <div className="whitespace-nowrap">{rentable.reference_id}</div>
                        <div>
                          <button
                            type="button"
                            className="whitespace-nowrap text-left flex flex-row gap-1 items-center"
                            onClick={() => {
                              setSelectedRentableIds(toggleArray(selectedRentableIds, rentable.id));
                            }}
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    );
                  })}
              </div>
            )}
            <ActionForm className="space-y-3" data-equal={!hasChanges} id={"gear_check" satisfies ColumnKey}>
              <div className="flex flex-row gap-2 items-center">
                <div className="flex-1">
                  <button
                    type="button"
                    onClick={rentalCompleted.toggle}
                    aria-selected={rentalCompleted.isOn}
                    className="flex flex-row gap-2 items-center group h-10"
                  >
                    <Checker className="border border-slate-300" />
                    <p className="text-left text-sm md:text-base">Gear Check Completed</p>
                  </button>

                  <RInput table="participant_day" field="data.participant_id" value={selectedParticipant.id || ""} type="hidden" />
                  <RInput table="participant_day" field="data.date" value={ctx.date.dateParam} type="hidden" />
                  <RInput
                    table="participant_day"
                    field="data.rental_checked"
                    type="hidden"
                    defaultValue={rentalCompleted.isOn ? "true" : "false"}
                    hiddenType="__boolean__"
                  />
                </div>
                {hasChanges && (
                  <div className="flex-1">
                    <div className="flex flex-row gap-2 sticky bottom-0">
                      <button
                        type="button"
                        className="btn btn-basic px-6"
                        onClick={() => {
                          setSelectedRentableIds(initialSelectedRentableIds);
                          rentalCompleted.set(initialRentalCompleted);
                          setRentalAssignmentSearch("");
                        }}
                      >
                        Cancel
                      </button>
                      <SubmitButton className="btn btn-primary flex-1">Save</SubmitButton>
                    </div>
                  </div>
                )}
              </div>
              <div className="flex flex-wrap gap-2 pb-2 justify-start sticky top-0 bg-white py-2 z-10">
                {rentableTypes.map((type) => {
                  const isSelected = mapEstablishmentRentables.find((rentable) => rentable.type === type && rentable.isSelected);
                  return (
                    <button
                      key={type}
                      type="button"
                      className={twMerge(
                        "btn font-normal flex-grow flex-shrink ",
                        isSelected && rentalAssignmentSearch === type
                          ? "bg-green-600 text-green-100"
                          : rentalAssignmentSearch === type
                            ? "btn-secondary"
                            : isSelected
                              ? "bg-green-100 text-green-600"
                              : "btn-basic ",
                      )}
                      onClick={() => {
                        setRentalAssignmentSearch(rentalAssignmentSearch === type ? "" : type);
                        setTimeout(() => {
                          document.getElementById("gear_check" satisfies ColumnKey)?.scrollIntoView({ behavior: "smooth" });
                        }, 10);
                      }}
                    >
                      <p>{type}</p>
                    </button>
                  );
                })}
              </div>
              <OnFormSuccess
                onSuccess={() => {
                  setRentalAssignmentSearch("");
                }}
              />
              {toBeDeletedRentalAssignments.map((rentalAssignment) => {
                return (
                  <Fragment key={rentalAssignment.id}>
                    <RInput
                      table={"rental_assignment"}
                      field={"id"}
                      index={rentalAssignment.id}
                      value={rentalAssignment.id}
                      type="hidden"
                    />
                    <OperationInput table={"rental_assignment"} value={"delete"} index={rentalAssignment.id} />
                  </Fragment>
                );
              })}
              {toBeAddedRentalAssignments.map((rentable) => {
                return (
                  <Fragment key={rentable.id}>
                    <RInput table={"rental_assignment"} field={"data.rentable_id"} index={rentable.id} value={rentable.id} type="hidden" />
                    <RInput table={"rental_assignment"} field={"data.date"} index={rentable.id} value={ctx.date.dateParam} type="hidden" />
                    <RInput
                      table={"rental_assignment"}
                      field={"data.participant_id"}
                      index={rentable.id}
                      value={selectedParticipant?.id}
                      type="hidden"
                    />
                  </Fragment>
                );
              })}
              <div>
                <AnimatingDiv className="overflow-auto">
                  <div
                    className={`grid gap-x-3 gap-y-3 justify-stretch items-center w-full md:w-[500px] 
                      max-sm:grid-cols-[min-content_minmax(0,auto)_minmax(0,auto)_minmax(0,auto)_minmax(0,auto)_minmax(0,auto)]
                    grid-cols-[min-content_minmax(0,auto)_minmax(0,auto)_minmax(0,auto)_minmax(0,auto)_minmax(0,auto)]`}
                  >
                    {myGroupBy2(
                      mapEstablishmentRentables.filter((rentable) => rentable.type === rentalAssignmentSearch),
                      (rentable) => {
                        const tripNames = rentable.tripsOfOtherParticipants.map((trip) => trip.start_time + " " + trip.activity_location);
                        const finalTripNames = rentable.tripIdsOfOtherParticipants.includes(null)
                          ? [...tripNames, "Already assigned (unscheduled)"]
                          : tripNames;
                        return finalTripNames.join(", ");
                      },
                    )
                      .sort((a, b) => a.groupKey.localeCompare(b.groupKey))
                      .map((rentableGroup) => {
                        return (
                          <Fragment key={rentableGroup.groupKey}>
                            {/* <div className="col-span-5">
                              <hr className="border-slate-100" />
                            </div> */}
                            {!!rentableGroup.groupKey && (
                              <div className="text-sm col-span-6 text-slate-500 pt-3">
                                <div className="pb-1">{rentableGroup.groupKey}</div>
                                <hr className="border-slate-100" />
                              </div>
                            )}
                            {rentableGroup.items.map((rentable) => {
                              const alreadySelected = selectedRentableIds.includes(rentable.id);
                              const onClick = (e: React.MouseEvent<HTMLButtonElement>) => {
                                if (
                                  !rentable.selectedByOther.length ||
                                  alreadySelected ||
                                  window.confirm(
                                    rentable.overlappingTripsTimes.length
                                      ? "This item is already assigned to a different participant within the same trip. Selecting this item will cause a conflict"
                                      : "This item is already assigned to another guest today. This could cause a conflict. Are you sure you want to continue?",
                                  )
                                ) {
                                  setSelectedRentableIds(toggleArray(selectedRentableIds, rentable.id));
                                }
                              };
                              return (
                                <Fragment key={rentable.id}>
                                  <div className={twMerge("contents", rentableGroup.groupKey && "text-slate-500")}>
                                    <button
                                      type="button"
                                      onClick={onClick}
                                      className="whitespace-nowrap text-left group overflow-clip text-ellipsis"
                                      aria-selected={alreadySelected}
                                    >
                                      <Checker className="border border-slate-300" />
                                    </button>
                                    <button
                                      type="button"
                                      onClick={onClick}
                                      className="whitespace-nowrap text-left overflow-clip text-ellipsis"
                                    >
                                      {rentable.type}
                                    </button>
                                    <button
                                      type="button"
                                      onClick={onClick}
                                      className="whitespace-nowrap text-left overflow-clip text-ellipsis"
                                    >
                                      {rentable.title}
                                    </button>
                                    <button
                                      type="button"
                                      onClick={onClick}
                                      className="whitespace-nowrap text-left overflow-clip text-ellipsis max-sm:hidden"
                                    >
                                      {rentable.brand}
                                    </button>
                                    <button type="button" onClick={onClick} className="whitespace-nowrap overflow-clip text-ellipsis">
                                      {rentable.size}
                                    </button>
                                    <button type="button" onClick={onClick} className="whitespace-nowrap text-ellipsis">
                                      {rentable.reference_id}
                                    </button>
                                    {/* <button
                                    type="button"
                                    onClick={() => {
                                      setSelectedRentableIds(toggleArray(selectedRentableIds, rentable.id));
                                      // setRentalAssignmentSearch(nextUnassignedType || "");
                                    }}
                                    className="whitespace-nowrap text-left overflow-clip text-ellipsis"
                                  >
                                    {!!rentable.selectedByOther.length && (
                                      <div className="text-sm w-fit px-3 text-orange-600 bg-orange-100 py-1 rounded-md">
                                        !{rentable.selectedByOther.length}
                                      </div>
                                    )}
                                  </button> */}
                                  </div>
                                  <div className="col-span-6">
                                    <hr className="border-slate-100" />
                                  </div>
                                </Fragment>
                              );
                            })}
                          </Fragment>
                        );
                      })}
                  </div>
                </AnimatingDiv>
              </div>
            </ActionForm>
            {/* <ActionForm>
              <RInput table="participant_day" field="data.participant_id" value={selectedParticipant.id || ""} type="hidden" />
              <RInput table="participant_day" field="data.date" value={ctx.date.dateParam} type="hidden" />
              <RInput
                table="participant_day"
                field="data.rental_checked"
                type="hidden"
                defaultValue={selectedParticipant.participant_day?.rental_checked ? "false" : "true"}
                hiddenType="__boolean__"
              />
              <button
                type="submit"
                className={twMerge("btn btn-sm", selectedParticipant.participant_day?.rental_checked ? "btn-green" : "btn-basic")}
              >
                {selectedParticipant.participant_day?.rental_checked ? "Checked" : "Check"}
              </button>
            </ActionForm> */}
          </div>
        )}
      </AnimatingDiv>
    </div>
  );
};

const CommentListDialogContent = () => {
  const data = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const ctx = useAppContext();

  const allParticipatoins = flat(
    data.establishments.map((establishment) =>
      establishment.participations.map((item) => ({
        ...item,
        establishment: establishment,
      })),
    ),
  );
  const selectedParticipation = allParticipatoins.find((participation) => participation.id === search.state.participation_id);
  const selectedParticipant = selectedParticipation?.participant;

  const [commentId, setCommentId] = useState("");
  const selectedComment = selectedParticipation?.comments?.find((comment) => comment.id === commentId);

  if (!selectedParticipation) {
    return <div>Participation not found.</div>;
  }

  if (!selectedParticipant) {
    return <div>Participant not found.</div>;
  }

  const comments = selectedParticipation.comments || [];

  const getHasRightOnAllEstablishments = (minLevel: AdminLevel) => {
    const adminEstablishments = data.establishments.filter((establishment) =>
      ctx.members.find((member) => member.establishment_id === establishment.id && member.admin >= getAdminLevelIndex(minLevel)),
    );
    return !!data.establishments.length && adminEstablishments.length === data.establishments.length;
  };

  const hasWriteOnAllEstablishments = getHasRightOnAllEstablishments("write");

  return (
    <div className="space-y-4">
      <div className="flex flex-row gap-2 items-center">
        <div className="p-4 rounded-full bg-slate-200 text-slate-700">
          <ProfileIcon className="w-8 h-8" />
        </div>
        <div>
          <p className="font-medium">
            {selectedParticipant?.first_name} {selectedParticipant?.last_name}
          </p>
          <p className="text-sm text-slate-500">Comments</p>
        </div>
      </div>

      {/* Comments List */}
      <div className="space-y-3">
        <h3 className="font-medium">Comments ({comments.length})</h3>
        {comments.length === 0 ? (
          <p className="text-slate-500 text-sm">No comments yet.</p>
        ) : (
          <div className="space-y-3 overflow-y-auto">
            {comments.map((comment, index) => (
              <div key={comment.id || index} className="bg-slate-50 p-3 rounded-md">
                <div className="flex justify-between items-start gap-2">
                  <div className="flex-1">
                    <p className="text-sm whitespace-pre-wrap">{comment.content}</p>
                    <div className="flex flex-row gap-2 items-center justify-between">
                      <p className="text-xs text-slate-500 mt-1">{comment.created_at_formatted}</p>
                      <button
                        type="button"
                        className="text-blue-500 hover:text-blue-700 text-sm"
                        onClick={() => {
                          setCommentId(comment.id);
                          setTimeout(() => {
                            document.getElementById(toInputId(fName("comment", "data.content")))?.focus();
                          }, 10);
                        }}
                      >
                        <PencilIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  {hasWriteOnAllEstablishments && (
                    <ActionForm confirmMessage="Delete this comment?">
                      <RInput table="comment" field="id" value={comment.id} type="hidden" />
                      <OperationInput table="comment" value="delete" />
                      <button type="submit" className="text-red-500 hover:text-red-700 text-sm">
                        <XMarkIcon className="w-4 h-4" />
                      </button>
                    </ActionForm>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Add New Comment */}
      {hasWriteOnAllEstablishments && (
        <ActionForm key={commentId || ""} onCheckEqual={defaultEqualCheck}>
          <OnFormSuccess
            onSuccess={() => {
              setCommentId(commentId ? "" : "new");
            }}
          />
          <div className="space-y-3">
            <h3 className="font-medium">{selectedComment ? "Edit Comment" : "Add Comment"}</h3>
            <div>
              {selectedComment && <RInput table="comment" field="id" value={selectedComment.id} type="hidden" />}
              <RInput table="comment" field="data.date" value={ctx.date.dateParam} type="hidden" />
              <RInput table="comment" field="data.target" value={"participation" satisfies keyof DB} type="hidden" />
              <RInput table="comment" field="data.target_id" value={selectedParticipation.id} type="hidden" />
              <RTextarea
                table="comment"
                field="data.content"
                placeholder="Enter your comment here..."
                className="input min-h-24 w-full"
                required
              >
                {selectedComment?.content}
              </RTextarea>
            </div>
            <div className="flex gap-2 justify-end">
              {selectedComment && (
                <button type="button" className="btn btn-basic" onClick={() => setCommentId("")}>
                  Cancel
                </button>
              )}
              <SubmitButton className="btn btn-primary">{selectedComment ? "Save" : "Add"}</SubmitButton>
            </div>
          </div>
        </ActionForm>
      )}
    </div>
  );
};

export default function Page() {
  const ctx = useAppContext();
  const data = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const activeTab = data.tabs.find((tab) => tab.id === search.state.view_id) || data.tabs[0];
  usePageRefresh(20, data);

  const getHasRightOnAllEstablishments = (minLevel: AdminLevel) => {
    const adminEstablishments = data.establishments.filter((establishment) =>
      ctx.members.find((member) => member.establishment_id === establishment.id && member.admin >= getAdminLevelIndex(minLevel)),
    );
    return !!data.establishments.length && adminEstablishments.length === data.establishments.length;
  };

  const hasWriteOnAllEstablishments = getHasRightOnAllEstablishments("write");
  const hasReadOnAllEstablishments = getHasRightOnAllEstablishments("read");

  const finalColumns = activeTab?.columns || [];

  const totalColumns = sumBy(finalColumns, (column) => columns[column].columns.length);

  const finalOrderColumns = activeTab?.sorts || [];

  const globalWaypoints: string[] = [];
  const pickups: string[] = [];

  if (search.state.persist_debug) {
    console.log("participant.tsx data", data);
  }

  data.establishments.forEach((establishment) => {
    globalWaypoints.push(establishment.address);
    establishment.participations
      .map((participations) => participations.booking?.meeting_address)
      .forEach((pickupAddress) => {
        if (pickupAddress && !globalWaypoints.includes(pickupAddress)) {
          globalWaypoints.push(pickupAddress);
          pickups.push(pickupAddress);
        }
      });
  });
  const mapsUrl = createMapsUrl(globalWaypoints, "", "");

  const allParticipatoins = flat(
    data.establishments.map((establishment) =>
      establishment.participations.map((item) => ({
        ...item,
        establishment: establishment,
      })),
    ),
  );
  const allTrips = flat(data.establishments.map((establishment) => establishment.trips));
  const selectedParticipation = allParticipatoins.find((participation) => participation.id === search.state.participation_id);
  const selectedTrip = allTrips.find((trip) => trip.id === search.state.trip_id);

  const bookingDialogTitle = "Booking " + (selectedParticipation?.booking?.booking_reference || selectedParticipation?.booking?.sqid || "");
  // const participantDialogTitle = selectedParticipation?.participant?.first_name + " " + selectedParticipation?.participant?.last_name;
  const participantDialogTitle = "Participant Details";
  const detailDialogs: Record<DetailModalName, { title: ReactNode; content: ReactNode }> = {
    reorder: {
      title: "Order Tabs",
      content: (
        <ActionForm className="space-y-3">
          <ViewOrderForm />
          <SubmitButton className="btn btn-primary">Save</SubmitButton>
        </ActionForm>
      ),
    },
    sorting: {
      title: "Sorting",
      content: (
        <ActionForm className="space-y-3 ">
          {/*<p className="text-slate-500">Order by:</p>*/}
          <RInput table={"view"} field={"id"} value={activeTab?.id} />
          <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined }} />
          <OrderByForm sorts={finalOrderColumns} key={JSON.stringify(finalOrderColumns)} />
          <SubmitButton className="btn btn-primary">Ok</SubmitButton>
        </ActionForm>
      ),
    },
    customise: {
      title: "Customize View",
      content: (
        <ActionForm className="space-y-3 ">
          <RInput
            table={"view"}
            field={"data.name"}
            required
            defaultValue={search.state.action_type === "copy" ? "" : activeTab?.name}
            className="input"
            label={"Name"}
          />
          <RedirectParamsInput
            path={"./"}
            paramState={{
              toggle_modal: undefined,
              action_type: undefined,
              view_id: tableIdRef("view"),
            }}
          />
          <p className="text-slate-500">Columns</p>
          {/*<InputSearchParamCopies excludeKeys={["toggle_columns", "toggle_modal"]} />*/}
          {/*<input name={"toggle_modal" satisfies StateInputKey} value={""} type={"hidden"} />*/}
          <ColumnInputs selectedColumnKeys={finalColumns} />
          {search.state.action_type === "copy" ? (
            <Fragment>
              <p className="text-slate-500">Sorting</p>
              <OrderByForm sorts={finalOrderColumns} key={JSON.stringify(finalOrderColumns)} />
              <RInput table={"view"} field={"data.operator_id"} value={search.state.persist_operator_id || ""} type={"hidden"} />
              <RInput table={"view"} field={"data.sort_order"} value={data.tabs.length} type={"hidden"} />
              {/*<RInput table={'view'} field={'data.so'}*/}
            </Fragment>
          ) : (
            <RInput table={"view"} field={"id"} value={activeTab?.id} />
          )}
          <SubmitButton className="btn btn-primary">Ok</SubmitButton>
        </ActionForm>
      ),
    },
    participant: {
      title: participantDialogTitle,
      content: <ParticipantDetailDialogContent />,
    },
    trip: {
      title: "Trip",
      content: (
        <div className="space-y-2">
          <p>{selectedTrip?.date}</p>
          <p>{selectedTrip?.type}</p>
          <p>{selectedTrip?.capacity}</p>
          <p>{selectedTrip?.activity_location}</p>
        </div>
      ),
    },
    participant_comment: {
      title: "Participant Note",
      content: (
        <div className="space-y-2">
          <p>{selectedParticipation?.participant?.comment}</p>
        </div>
      ),
    },
    internal_booking_note: {
      title: "Internal Booking Note",
      content: (
        <div className="space-y-2">
          {selectedParticipation?.booking ? (
            <ActionForm>
              <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined, participation_id: null }} />
              <div>
                <RInput table={"booking"} field={"id"} type={"hidden"} value={selectedParticipation.booking.id} />
                <RTextarea table={"booking"} field={"data.internal_note"} placeholder={"internal note"} className="input min-h-32 min-w-48">
                  {selectedParticipation?.booking?.internal_note || ""}
                </RTextarea>
              </div>
              <div className="flex flex-row gap-3 items-center justify-end">
                <ParamLink paramState={{ toggle_modal: undefined, participation_id: null }} className="link" tabIndex={-1}>
                  close
                </ParamLink>
                <SubmitButton className="btn btn-primary">Save</SubmitButton>
              </div>
            </ActionForm>
          ) : (
            <div>No Booking</div>
          )}
        </div>
      ),
    },
    booking: {
      title: bookingDialogTitle,
      content: selectedParticipation ? (
        <div className="w-full sm:min-w-96">
          <ActionForm className="space-y-5 w-full">
            <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined, participation_id: null }} />
            <RInput table={"booking"} field={"id"} value={selectedParticipation.booking?.id || ""} />
            <MeetingInput
              country_code={selectedParticipation.establishment.country_code}
              default={selectedParticipation.booking}
              operator_address={selectedParticipation.establishment.address}
            />
            <div className="flex flex-row gap-3 items-center justify-end">
              <ParamLink paramState={{ toggle_modal: undefined, participation_id: null }} className="link" tabIndex={-1}>
                Close
              </ParamLink>
              {hasReadOnAllEstablishments && <SubmitButton className="btn btn-primary">Save</SubmitButton>}
            </div>
          </ActionForm>
        </div>
      ) : (
        <Fragment />
      ),
    },
    comment: {
      title: participantDialogTitle,
      content: selectedParticipation ? <CommentListDialogContent /> : <Fragment />,
    },
  };

  const selectedDialog = detailDialogs[(search.state.modal_detail_name || "") as DetailModalName];
  return (
    <div className="space-y-6">
      <div className="app-container space-y-6">
        <div className="flex flex-row items-center gap-3">
          <h2 className="text-xl font-semibold">Participants</h2>
          {pickups.length > 0 && (
            <a rel={"noreferrer"} href={mapsUrl.toString()} target={"_blank"} className="link">
              pickups
            </a>
          )}
          <SidePanel dialogname={"detail"}>
            <div className="h-full bg-slate-100 max-w-full max-md:w-screen overflow-auto">
              <SidePanelHeading className="pt-3">{selectedDialog?.title}</SidePanelHeading>
              <div className="p-4">{selectedDialog?.content}</div>
            </div>
          </SidePanel>
        </div>
        <ActionForm
          method={"GET"}
          className="flex flex-row gap-3"
          onCheckEqual={(args) => args.finalFormData.get("persist_date" satisfies StateInputKey) === search.state.persist_date}
        >
          <InputSearchParamCopies excludeKeys={["persist_date"]} />
          <DaySwitch className="flex-1" />
        </ActionForm>
      </div>
      <div className="space-y-6">
        {data.establishments.map((establishment) => {
          const waypoints: string[] = [];

          establishment.participations
            .map((participations) => participations.booking?.meeting_address)
            .forEach((pickupAddress) => {
              if (pickupAddress && !waypoints.includes(pickupAddress)) {
                waypoints.push(pickupAddress);
              }
            });

          const trips = [...establishment.trips, null];

          const mapsUrl = createMapsUrl(waypoints, establishment.address, "");

          return (
            <div key={establishment.id} className="space-y-6">
              <div className="space-y-5">
                {data.establishments.length > 1 && (
                  <h2>
                    <EstablishmentSeperator establishment={establishment} />
                  </h2>
                )}
                <div className="flex flex-row gap-3 items-center overflow-x-auto">
                  <nav className="flex flex-row">
                    {data.tabs.map((tab) => {
                      return (
                        <ParamLink
                          key={tab.id}
                          paramState={{ view_id: tab.id, toggle_columns: [], sorts: [] }}
                          reload
                          aria-selected={activeTab === tab}
                          className={`px-6 border-b-4 border-b-gray-200 py-2 text-center transition-colors whitespace-nowrap
              hover:border-b-gray-300 hover:bg-gray-50 hover:text-slate-900
              aria-selected:border-b-primary aria-busy:animate-pulse aria-busy:cursor-progress`}
                        >
                          {tab.name}
                        </ParamLink>
                      );
                    })}
                    {hasWriteOnAllEstablishments && (
                      <Fragment>
                        <ParamLink
                          className="btn gap-2 text-slate-400 hover:bg-slate-50 hover:text-slate-800 whitespace-nowrap group"
                          paramState={{
                            toggle_modal: "detail",
                            modal_detail_name: "customise" satisfies DetailModalName,
                            action_type: "copy",
                          }}
                        >
                          <PlusIcon className="w-4 h-4 group-hover:rotate-180 transition-transform" />
                          Add
                        </ParamLink>
                        {data.tabs.length > 1 && (
                          <ParamLink
                            className="btn gap-2 text-slate-400 hover:bg-slate-50 hover:text-slate-800 whitespace-nowrap group"
                            paramState={{
                              toggle_modal: "detail",
                              modal_detail_name: "reorder" satisfies DetailModalName,
                            }}
                          >
                            <ArrowRightLeft className="w-4 h-4 group-hover:rotate-180 transition-transform" />
                            Order tabs
                          </ParamLink>
                        )}
                      </Fragment>
                    )}
                  </nav>
                  <div className="flex-1" />
                  {activeTab && hasWriteOnAllEstablishments && (
                    <div className="flex flex-row gap-2 items-center">
                      <ParamLink
                        className="btn gap-2 text-slate-500 hover:text-slate-800 whitespace-nowrap"
                        paramState={{
                          toggle_modal: "detail",
                          modal_detail_name: "customise" satisfies DetailModalName,
                          action_type: undefined,
                        }}
                      >
                        <Cog6ToothIcon className="w-4 h-4" />
                        Customize ({finalColumns.length})
                      </ParamLink>
                      <ParamLink
                        className="btn gap-1 text-slate-500 hover:text-slate-800 whitespace-nowrap"
                        paramState={{
                          toggle_modal: "detail",
                          modal_detail_name: "sorting" satisfies DetailModalName,
                          action_type: undefined,
                        }}
                      >
                        <ArrowsUpDownIcon className="w-4 h-4" />
                        Sorting ({finalOrderColumns.length})
                      </ParamLink>
                      <ActionForm confirmMessage={`Delete view "${activeTab.name}".\nAre you sure?`}>
                        <OperationInput table={"view"} value={"delete"}></OperationInput>
                        <RInput table={"view"} field={"id"} value={activeTab.id} />
                        <SubmitButton className="btn gap-1 text-slate-500 hover:text-red-500">
                          <TrashIcon className="w-4 h-4" />
                          Delete
                        </SubmitButton>
                      </ActionForm>
                      <button
                        className="btn gap-1 text-primary hover:text-primary-700 whitespace-nowrap"
                        type={"button"}
                        onClick={() => {
                          try {
                            const headers = flat(
                              finalColumns.map((columnKey) => {
                                const columnValues = columns[columnKey].columns;
                                return columnValues.map((_, index) =>
                                  columnValues.length > 1 ? columnKey + "_" + (index + 1) : columnKey,
                                );
                              }),
                            ).join(",");
                            const rows = establishment.participations
                              .map((participation) => {
                                return flat(
                                  finalColumns.map((columnKey) =>
                                    columns[columnKey].columns.map((column) => {
                                      const value = column.Text(participation, data);
                                      // return value || "-";
                                      return escapeCSV(value ? value + "" : "-");
                                    }),
                                  ),
                                ).join(",");
                              })
                              .join("\n");
                            const filename = `participants-${activeTab.name}-${data.date.dateParam}.csv`;
                            const csvContent = headers + "\n" + rows;

                            const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8" });
                            const a = document.createElement("a");
                            const downloadUrl = URL.createObjectURL(blob);
                            a.href = downloadUrl;
                            a.download = filename;
                            a.style.display = "none";
                            document.body.appendChild(a);

                            a.click();

                            document.body.removeChild(a);

                            URL.revokeObjectURL(downloadUrl);
                          } catch (e) {
                            console.error("export error", e);
                            window.alert("export error");
                          }
                        }}
                      >
                        <ExportIcon className="w-6 h-6" />
                        Export
                      </button>
                    </div>
                  )}
                </div>
              </div>
              <div className="overflow-auto text-xs px-3">
                {establishment.participations.length === 0 && <p className="app-container">No participants this day</p>}
                {establishment.participations.length > 0 && (
                  <div
                    className="items-center gap-1"
                    style={{
                      display: "grid",
                      // gridTemplateColumns: finalColumns
                      //   .map((column) => columns[column].columns.map((column) => `minmax(0, ${column.gridColumn})`).join(" "))
                      //   .join(" "),
                      // minWidth: 1228,
                      // maxWidth: 1128,
                    }}
                  >
                    {finalColumns.map((columnKey) => {
                      const column = columns[columnKey];
                      return (
                        <div
                          style={{ gridColumn: `span ${column.columns.length}` }}
                          className="py-2 text-left font-semibold break-words"
                          key={column.label}
                        >
                          {column.label}
                          {/*<Feature feature={"views"}>*/}
                          {/*  <ParamLink*/}
                          {/*    paramState={{ toggle_columns: toggleArray(search.state.toggle_columns, columnKey) }}*/}
                          {/*    className="p-1 border border-slate-600"*/}
                          {/*  >*/}
                          {/*    -*/}
                          {/*  </ParamLink>*/}
                          {/*</Feature>*/}
                        </div>
                      );
                    })}
                    {trips.map((trip) => {
                      // const trip = tripGroup.items[0]?.trip;
                      const waypoints: string[] = [];
                      //                      const participations = establishment.participations.filter(
                      //   (participation) => {
                      //     const foundTrip = establishment.trips.find((trip) => trip.id === participation.trip_id);
                      //     return (foundTrip?.id || null) === (trip?.id || null);
                      // });
                      const participations = establishment.participations.filter(
                        (participation) => (participation.trip_id || null) === (trip?.id || null),
                      );
                      participations
                        .map((particpant) => particpant.booking?.meeting_address)
                        .forEach((pickupAddress) => {
                          if (pickupAddress && !waypoints.includes(pickupAddress)) {
                            waypoints.push(pickupAddress);
                          }
                        });

                      // const mapsUrl = createMapsUrl(waypoints, mergedData.establishment?.address, trip.address);
                      const tripTotals = trip && getTripTotals(trip);
                      const tripType = getTripType(trip?.type);
                      const sitetext = toSitesStr(trip?.sites);

                      const tanks = myGroupBy2(
                        participations.flatMap((participation) => participation.tank_assignments || []),
                        (tank) => tank.liters + " " + tank.gas,
                      );

                      const isRest = getTripType(trip?.type)?.key === "rest";

                      if (!trip && !participations.length) return <Fragment key={"0-scheduled-hide"} />;

                      return (
                        <Fragment key={trip?.id || ""}>
                          <div className="bg-slate-100 text-slate-700" style={{ gridColumn: `span ${totalColumns}` }}>
                            <div className="p-2">
                              <div className="flex flex-row items-center gap-3">
                                <ParamLink
                                  path={_planning}
                                  className="flex flex-wrap items-center [&_span:first-child]:before:content-none [&_span]:before:px-3 [&_span]:before:content-['|']"
                                >
                                  {trip ? (
                                    <Fragment>
                                      <span className="h-7 w-10 text-secondary">{getTripType(trip?.type)?.icon}</span>
                                      <span>{trip.boat_name || tripType?.name}</span>
                                      {!isRest && (
                                        <Fragment>
                                          <span>{trip.activity_location}</span>
                                          <span>
                                            {trip.boat_name ? "Departure" : "Start time"}: {trip.start_time?.slice(0, 5)}, {trip.address}
                                          </span>
                                          {!!sitetext && tripType?.showSites && <span>{sitetext}</span>}
                                          {tanks.map((tank) => {
                                            const quantity = sum(tank.items.map((item) => item.quantity));
                                            return (
                                              <span key={tank.groupKey}>
                                                {quantity} x {tank.groupKey}
                                              </span>
                                            );
                                          })}
                                          {!!trip.capacity && (
                                            <span>
                                              {unique(participations.map((item) => item.participant_id || item.id)).length} participants,{" "}
                                              {tripTotals?.available} available
                                            </span>
                                          )}
                                        </Fragment>
                                      )}
                                    </Fragment>
                                  ) : (
                                    <span>{participations.length} unscheduled participants</span>
                                  )}
                                </ParamLink>
                                {!isRest && waypoints.length > 0 && (
                                  <a rel={"noreferrer"} href={mapsUrl.toString()} target={"_blank"} className="link">
                                    pickups
                                  </a>
                                )}
                              </div>
                            </div>
                          </div>
                          {participations.map((participation, index) => {
                            const trip = allTrips.find((trip) => trip.id === participation.trip_id) || null;
                            return (
                              <Fragment key={index}>
                                {index > 0 && <div style={{ gridColumn: `span ${totalColumns}` }} className="h-[1px] bg-slate-200" />}
                                {finalColumns.map((columnKey) => {
                                  const column = columns[columnKey];
                                  return column.columns.map((column, columnIndex) => (
                                    <div className="whitespace-pre-wrap px-2 py-3" style={{ maxHeight: 100 }} key={columnIndex}>
                                      {/*<div className="overflow-hidden">*/}
                                      {/*  <div className="overflow-hidden">{value(participant, false)}</div>*/}
                                      {/*</div>*/}
                                      <ParticipationCelComp column={column} participation={participation} />
                                      {/*{column.value(participation, false)}*/}
                                      {/*<div style={{ width: column.width && column.width + "px" }}>{value(participant, false)}</div>*/}
                                    </div>
                                  ));
                                })}
                              </Fragment>
                            );
                          })}
                          {trip && participations.length === 0 && (
                            <div style={{ gridColumn: `span ${totalColumns}` }} className="text-slate-500 py-3">
                              No participants
                            </div>
                          )}
                        </Fragment>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
