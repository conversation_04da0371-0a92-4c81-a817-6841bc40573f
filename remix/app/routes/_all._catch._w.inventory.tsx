import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import React, { Fragment } from "react";
import { LoaderFunctionArgs } from "@remix-run/router";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _item_detail } from "~/misc/paths";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { useAppContext } from "~/hooks/use-app-context";
import { IkImage } from "~/components/IkImage";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { createPageOverwrites } from "~/misc/consts";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { SidePanel, SidePanelHeading } from "~/components/SidePanel";
import { ProductPriceForm } from "~/domain/product/ProductPriceForm";
import { ItemForm, SimpleProductForm } from "~/domain/product/SimpleProductForm";
import { SubmitButton } from "~/components/base/Button";
import { ActionForm, defaultEqualCheck } from "~/components/form/BaseFrom";
import { fName, tableIdRef } from "~/misc/helpers";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { InputFilesDefault } from "~/components/form/FilesInput";
import { defaultCurrency } from "~/misc/vars";
import { RInput, RLabel } from "~/components/ResourceInputs";
import { CategorySelect, getCategorySegments } from "~/domain/category/CategoryComponents";
import { FaPlus } from "react-icons/fa";
import { unique } from "remeda";
import { twMerge } from "tailwind-merge";
import { formatPrice } from "~/utils/money";
import { TrashIcon } from "@heroicons/react/20/solid";
import { pricesJsonEb } from "~/domain/product/product-queries.server";
import { StockBadge } from "~/domain/product/StockBadge";
import { ActivitySlug } from "~/domain/activity/activity";
import { salesCountSelect } from "~/domain/activity/activity-queries";
import { EstablishmentLayout } from "~/components/AllowedForEstablishment";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const record = paramsToRecord(url.searchParams);

  const establishmentId = record.establishment_id || record.persist_establishment_id;

  if (!establishmentId) throw new Error("establishment_id param Required");

  const products = await kysely
    .selectFrom("product")
    .innerJoin("item", "item.id", "product.item_id")
    .where("product.deleted_at", "=", at_infinity_value)
    .where("item.establishment_id", "=", establishmentId)
    .where("item.activity_slug", "=", "retail" satisfies ActivitySlug)
    .whereRef("product.item_id", "=", "item.id")
    .leftJoin("product_price", "product_price.price_id", "product.id")
    .leftJoin("price", "price.id", "product_price.price_id")
    .select((eb) => [
      "item.title",
      "item.description",
      "item.subtitle",
      "item.category_id",
      "item.brand",
      "product.id",
      "product.size",
      "product.color",
      "product.sku",
      "product.stock",
      "product.item_id",
      pricesJsonEb,
      jsonArrayFrom(fileTargetsQb(kysely, "product", eb.ref("product.id"))).as("files"),
      salesCountSelect.where("sale_item.product_id", "=", eb.ref("product.id")).as("sales_count"),
    ])

    .orderBy(["item.title", "product.color"])
    .execute();

  // if (!result) throw notFoundOrUnauthorzied("establishment not found or you not authorized");
  return {
    products: products,
    // establishment: result,
    ...createPageOverwrites({ establishment_id: establishmentId, fixed_width:true }),
  };
};

const CategoryPanelContent = () => {
  const ctx = useAppContext();
  const search = useSearchParams2();
  const establishment = ctx.establishment;

  if (!establishment) return <div>Establishment required</div>;
  const editCategory = establishment?.categories.find((cat) => cat.id === search.state.modal_detail_id);

  return (
    <Fragment>
      <SidePanelHeading>
        <h3 className="text-xl">{editCategory ? "Edit Category" : "Create Category"}</h3>
      </SidePanelHeading>
      <CategorySelect name={fName("category", "data.parent_category_id")} defaultValue={""} parent disabledCategoryId={editCategory?.id} />
      {!editCategory && <RInput table="category" field="data.establishment_id" value={establishment.establishment_id} type="hidden" />}
      {editCategory && <RInput table="category" field="id" value={editCategory.id} type="hidden" />}
      <div className="form-control">
        <RLabel table="category" field="data.name">
          Category Name
        </RLabel>
        <RInput table="category" field="data.name" className="input w-full" defaultValue={editCategory?.name || ""} required />
      </div>

      {/*{!!parentCategoryId && <RInput table={"category"} field={"data.parent_category_id"} type={"hidden"} value={parentCategoryId} />}*/}
      <p>Images</p>
      <InputFilesDefault small target_id={tableIdRef("category")} target={"category"} defaultValue={editCategory?.files} />
    </Fragment>
  );
};

export default function Page() {
  const ctx = useAppContext();
  const search = useSearchParams2();
  const data = useLoaderData<typeof loader>();
  const establishment = ctx.establishment;
  if (!establishment) return <div>Establishment required</div>;
  const category = establishment.categories.find((cat) => cat.id === search.state.category_id);
  const member = ctx.members.find((member) => member.establishment_id === establishment?.id);
  const isManager = (member?.admin || 0) > 1;
  const showEdit = !search.state.customer_view && isManager;
  const products = data.products;
  const brands = unique(products.map((item) => item.brand).filter((brand): brand is string => !!brand));
  const selectedBrand = brands.find((brand) => brand === search.state.brand);

  const filteredItems = data.products.filter((item) => !selectedBrand || item.brand === selectedBrand);
  return (
    <EstablishmentLayout
      title={
        <div className="flex flex-row gap-3 items-center justify-between">
          <h2 className="text-xl font-semibold">Retail Products ({filteredItems.length})</h2>
          {showEdit && (
            <ParamLink paramState={{ toggle_modal: "side", modal_domain: "product" }} className="btn btn-primary">
              <FaPlus className="w-4 h-4" />
              Create Product
            </ParamLink>
          )}
        </div>
      }
    >
      <div className="space-y-6">
        <div className="space-y-6">
          {/*{!!brands.length && (*/}
          {/*  <div className="flex flex-wrap gap-3">*/}
          {/*    {brands.map((brand) => (*/}
          {/*      <ParamLink*/}
          {/*        key={brand}*/}
          {/*        className="btn btn-basic aria-selected:btn-secondary"*/}
          {/*        paramState={{ brand: brand === selectedBrand ? null : brand }}*/}
          {/*        aria-selected={brand === selectedBrand}*/}
          {/*      >*/}
          {/*        {brand}*/}
          {/*      </ParamLink>*/}
          {/*    ))}*/}
          {/*  </div>*/}
          {/*)}*/}
          <div className="overflow-auto">
            <div className="grid grid-cols-[repeat(10,minmax(auto,1fr))] gap-y-2 gap-x-2 text-slate-700">
              <div className="grid grid-cols-subgrid col-span-10 font-semibold pb-1">
                <div>Title</div>
                <div>Category</div>
                <div>Brand</div>
                <div>SKU</div>
                <div>Color</div>
                <div>Size</div>
                <div></div>
                <div>Stock</div>
                <div>Price</div>
                <div></div>
              </div>
              {filteredItems.map((product) => {
                const firstPrice = product.product_prices[0];
                const firstFile = product.files[0];
                const currentStock = (product.stock || 0) - (product.sales_count || 0);
                return (
                  <div
                    className={twMerge(
                      "grid grid-cols-subgrid p-1 col-span-10 gap-3 rounded-md border-t border-slate-200",
                      // product === activeProduct && "bg-slate-100",
                    )}
                    key={product.id}
                  >
                    <div>
                      <ParamLink path={_item_detail(product.item_id)} paramState={{ product_id: product.id }} className="link">
                        {product.title || product.sku || product.id}
                      </ParamLink>
                    </div>
                    <div>
                      {getCategorySegments({
                        category_id: product.category_id,
                        categories: establishment.categories,
                        acc_cat_names: [],
                      }).join(" > ")}
                    </div>
                    <div>{product.brand}</div>
                    <div className="flex flex-wrap gap-3">
                      <ParamLink
                        path={_item_detail(product.item_id)}
                        paramState={{
                          product_id: product.id,
                        }}
                        className="link whitespace-nowrap"
                      >
                        {product.sku || product.id}
                      </ParamLink>
                    </div>
                    <div>{product.color || "-"}</div>
                    <div>{product.size || "-"}</div>
                    <div>{firstFile && <IkImage w={30} h={30} path={firstFile.filename} className="bg-transparent" />}</div>
                    <div>
                      <StockBadge stock={currentStock} />
                    </div>
                    <div>{firstPrice && formatPrice(firstPrice.amount, firstPrice.currency_id, firstPrice.currency_id)}</div>
                    <div className="flex justify-end">
                      <ActionForm confirmMessage={"Are you sure?"}>
                        <RInput table={"product"} field={"id"} value={product.id} index={product.id} />
                        <RInput table={"product"} field={"data.deleted_at"} value={"now"} index={product.id} type={"hidden"} />
                        <SubmitButton>
                          <TrashIcon className="w-4 h-4" />
                        </SubmitButton>
                      </ActionForm>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
      <SidePanel className="max-md:inset-x-0">
        <div className="h-full w-full md:w-96 space-y-3 bg-white p-3 overflow-auto">
          <ActionForm className="space-y-3" onCheckEqual={defaultEqualCheck}>
            <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined }} />
            {search.state.modal_domain === "product" ? (
              <div className="space-y-3">
                <SidePanelHeading>
                  <h3 className="text-xl">Create Product</h3>
                </SidePanelHeading>
                <div className="px-3 space-y-6">
                  <ItemForm item={{ activity_slug: "retail", category_id: category?.id, establishment_id: establishment.id }} />
                  <SimpleProductForm product={{ item_id: tableIdRef("item") }} />
                  <ProductPriceForm productPrice={{ currency_id: establishment.default_currency || defaultCurrency }} />
                  <InputFilesDefault target_id={tableIdRef("product")} target={"product"} small />
                </div>
              </div>
            ) : (
              <CategoryPanelContent />
            )}

            <div className="flex flex-row gap-3 items-center justify-end">
              <ParamLink paramState={{ toggle_modal: undefined }} replace className="btn">
                Cancel
              </ParamLink>
              <SubmitButton className="btn btn-primary">Save</SubmitButton>
            </div>
          </ActionForm>
        </div>
      </SidePanel>
    </EstablishmentLayout>
  );
}
