import type { AsyncReturnType } from "type-fest";
import { kysely } from "~/misc/database.server";
import { useLoaderData, useNavigation } from "@remix-run/react";
import { SubmitButton } from "~/components/base/Button";
import { getSessionSimple } from "~/utils/session.server";
import React, { useRef } from "react";
import { _diving_site } from "~/misc/paths";
import { EditorRequired } from "~/components/account/AccountContainer";
import { RInput, RSelect } from "~/components/ResourceInputs";
import { fName, myGroupBy2, tableIdRef } from "~/misc/helpers";
import { ActionForm } from "~/components/form/BaseFrom";
import { ActionAlert } from "~/components/ActionAlert";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import { LoaderFunctionArgs } from "@remix-run/router";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { countries } from "~/data/countries";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { ParamLink } from "~/components/meta/CustomComponents";

export { ErrorBoundary } from "~/components/RoutDefaults";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);

  const ctx = await activeUserSessionSimple(kysely, session_id, true)
    .where("_user.editor", "=", true)
    .select((eb) => [
      jsonArrayFrom(
        eb
          .selectFrom("region")
          .orderBy(["region.country_code", "region.name"])
          .select(["region.id", "region.name", "region.country_code", "region.published"]),
      ).as("regions"),
      jsonArrayFrom(eb.selectFrom("diving_location").selectAll('diving_location').orderBy("name")).as("diving_locations"),
      jsonArrayFrom(eb.selectFrom("diving_site").selectAll('diving_site').orderBy("name")).as("diving_sites"),
    ])
    .executeTakeFirst();

  if (!ctx) return null;

  return ctx;
};

type LoaderResponse = AsyncReturnType<typeof loader>;

const DivingSiteAddForm = (props: { diving_location_id: string; response: Exclude<LoaderResponse, null> }) => {
  const { state } = useSearchParams2();
  const { diving_sites, diving_locations } = props.response;
  const inputRef = useRef<HTMLInputElement>(null);
  const navigation = useNavigation();
  const isAddingSite = !!navigation.formData?.get(fName("diving_site", "data.diving_location_id"));
  const filteredDivingSites = diving_sites.filter((divingSite) => divingSite.diving_location_id === props.diving_location_id);

  return (
    <ActionForm className="space-y-3" identifier={props.diving_location_id}>
      <div className="flex flex-wrap gap-3 pt-2">
        {filteredDivingSites.map((divingSite) => (
          <ParamLink key={divingSite.id} path={_diving_site(divingSite)} className="link">
            {divingSite.name}
          </ParamLink>
        ))}
      </div>
      <ActionAlert />
      <div className="flex flex-wrap items-center gap-3">
        <div className="flex-1">
          <RedirectParamsInput
            path={"./"}
            paramState={{
              element_action: [props.diving_location_id],
              element_clear: [props.diving_location_id],
              rerender: state.rerender + 1,
            }}
          />
          <RInput table={"diving_site"} field={"data.diving_location_id"} type="hidden" value={props.diving_location_id} />
          <RInput
            id={props.diving_location_id}
            myref={inputRef}
            table={"diving_site"}
            field={"data.name"}
            required
            disabled={isAddingSite}
            className="input"
            type="text"
            placeholder="Enter diving site name"
          />
        </div>
        <SubmitButton className="btn btn-primary">Create</SubmitButton>
      </div>
    </ActionForm>
  );
};

export default function Page() {
  const search = useSearchParams2();
  const inputRef = useRef<HTMLInputElement>(null);
  const transition = useNavigation();
  const response = useLoaderData<LoaderResponse>();

  const submissionData = transition?.formData || new FormData();
  const isAddingLocation = transition.formMethod === "POST" && submissionData.has(fName("diving_location", "data.name"));

  if (!response) return <EditorRequired />;

  const divingLocations = response.diving_locations;

  return (
    <div className="app-container space-y-3 py-3">
      <h1 className="pb-2 text-2xl font-bold">Diving locations and sites</h1>
      <ActionForm className="space-y-3" key={search.state.rerender}>
        <ActionAlert />
        <RedirectParamsInput
          path={"./"}
          paramState={{ element_action: [tableIdRef("diving_location")], rerender: search.state.rerender + 1 }}
        />
        <div className="flex flex-wrap items-center gap-3 pb-3">
          <div className="flex-1">
            <RInput
              myref={inputRef}
              disabled={isAddingLocation}
              // onChange={() => {
              //   validateLocationName();
              // }}
              className="input min-w-44"
              required
              type="text"
              table={"diving_location"}
              field={"data.name"}
              placeholder="Enter diving location name"
            />
          </div>
          <div>
            <RSelect table={"diving_location"} field={"data.region_id"} className="select" required>
              <option value={""}>select region</option>
              {response.regions.map((region) => (
                <option value={region.id}>
                  {region.country_code} - {region.name}
                </option>
              ))}
            </RSelect>
          </div>
          <SubmitButton className="btn btn-primary">Create</SubmitButton>
        </div>
      </ActionForm>
      {myGroupBy2(divingLocations, (dl) => dl.region_id).map((regionGroup) => {
        const region = response.regions.find((region) => region.id === regionGroup.groupKey);
        const country = countries.find((country) => country.country_code === region?.country_code);
        return (
          <div>
            <h3 className="text-xl font-bold text-slate-600">
              {country?.country_name || "unkown"} - {region?.name || "unkown"}
            </h3>
            {regionGroup.items.map((divingLocation) => (
              <div key={divingLocation.id} className="space-y-3 py-6">
                <div className="flex flex-row items-center gap-2">
                  <h2 id={divingLocation.name} className="text-xl font-bold">
                    {divingLocation.name}
                  </h2>
                  <ParamLink className="link" path={divingLocation.id}>
                    view
                  </ParamLink>
                </div>
                <DivingSiteAddForm key={divingLocation.id} diving_location_id={divingLocation.id} response={response} />
              </div>
            ))}
          </div>
        );
      })}
    </div>
  );
}
