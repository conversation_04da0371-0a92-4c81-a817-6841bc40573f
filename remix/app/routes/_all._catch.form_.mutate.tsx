import { useLoaderData } from "@remix-run/react";
import { OverwriteField, ParticipantFields } from "~/domain/participant/ParticipantFields";
import { ActionForm } from "~/components/form/BaseFrom";
import { RInput, RLabel, RSelect, RTextarea } from "~/components/ResourceInputs";
import { OperationInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { _booking_detail, _form, _participant_detail, _product_mutate } from "~/misc/paths";
import { ActionAlert } from "~/components/ActionAlert";
import { SubmitButton } from "~/components/base/Button";
import { InputFilesDefault } from "~/components/form/FilesInput";
import { removeObjectKeys, tableIdRef } from "~/misc/helpers";
import { filesQuery } from "~/domain/file/file-queries";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { ImageGallery } from "~/components/field/ImageGallery";
import { LoaderFunctionArgs } from "@remix-run/router";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import React, { Fragment, useState } from "react";
import { AnimatingDiv, Backbutton } from "~/components/base/base";
import { XMarkIcon } from "@heroicons/react/20/solid";

import { at_infinity_value } from "~/kysely/db-static-vars";

import { waiversEb } from "~/domain/waiver/waiver-queries.server";
import { useLingui } from "@lingui/react";
import { useAppContext } from "~/hooks/use-app-context";
import { ParamLink } from "~/components/meta/CustomComponents";
import { activitySlugs } from "~/domain/activity/activity";
import { getWaiverType } from "~/domain/waiver/waiver-vars";
import { DefaultInfoIcon, Tooltip } from "~/components/base/tooltip";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const record = paramsToRecord(url.searchParams);
  // const defaultTemplateName = record.template;
  const establishmentId = record.establishment_id;
  const formId = record.id;
  const view = record.action_type;

  const establishment = await simpleEstablishmentQb
    .where("establishment.id", "=", establishmentId)
    .select((establishmentEb) => [
      "establishment.id",
      jsonArrayFrom(
        waiversEb
          .selectAll("waiver")
          .where((eb) =>
            eb.and([
              eb.or([
                eb("waiver.diving_certificate_organization_key", "is", null),
                eb(
                  "waiver.diving_certificate_organization_key",
                  "in",
                  establishmentEb
                    .selectFrom("diving_course")
                    .innerJoin("item__diving_course", "item__diving_course.diving_course_id", "diving_course.id")
                    .innerJoin("item", "item.id", "item__diving_course.item_id")
                    .innerJoin("product", "product.item_id", "item.id")
                    .where("item.establishment_id", "=", establishmentEb.ref("establishment.id"))
                    .where("product.deleted_at", "=", at_infinity_value)
                    .select("diving_course.diving_certificate_organization_key"),
                ),
              ]),
              eb.or([
                eb("waiver.establishment_id", "is", null),
                eb("waiver.establishment_id", "=", establishmentEb.ref("establishment.id")),
              ]),
            ]),
          ),
      ).as("waivers"),
    ])
    .$if(!!formId, (eb) =>
      eb.select((eb) => [
        jsonObjectFrom(
          eb
            .selectFrom("form")
            .where((eb) => eb.or([eb("form.establishment_id", "=", eb.ref("establishment.id")), eb("form.establishment_id", "is", null)]))
            .where("form.id", "=", formId || null)
            .selectAll("form")
            .select((eb) => [
              jsonArrayFrom(eb.selectFrom("field").selectAll("field").whereRef("field.form_id", "=", "form.id")).as("fields"),
              jsonArrayFrom(filesQuery("form").where("file_target.target_id", "=", eb.ref("form.id"))).as("files"),
              jsonArrayFrom(
                eb.selectFrom("form_waiver").where("form_waiver.form_id", "=", eb.ref("form.id")).select("form_waiver.waiver_id"),
              ).as("waivers"),
              jsonArrayFrom(
                eb
                  .selectFrom("participant")
                  .select(["participant.id"])
                  .innerJoin("form as exactForm", "exactForm.id", "participant.form_id")
                  // .where("participant.establishment_id", "=", establishmentEb.ref("establishment.id"))
                  .where("exactForm.root_id", "=", eb.ref("form.root_id"))
                  .orderBy("participant.created_at desc")
                  .limit(20),
              ).as("participants"),
              jsonArrayFrom(
                eb
                  .selectFrom("booking")
                  .innerJoin("sale_item", "sale_item.booking_id", "booking.id")
                  .innerJoin("form as exactForm", "exactForm.id", "sale_item.form_id")
                  .select(["booking.id"])
                  // .where("booking.establishment_id", "=", establishmentEb.ref("establishment.id"))
                  .where("exactForm.root_id", "=", eb.ref("form.root_id"))
                  // .orderBy("booking.created_at desc")
                  .limit(20),
              ).as("bookings"),
              jsonArrayFrom(
                eb
                  .selectFrom("product")
                  .innerJoin("item", "item.id", "product.item_id")
                  .select(["product.id"])
                  // .where("item.establishment_id", "=", establishmentEb.ref("establishment.id"))
                  .where("item.form_root_id", "=", eb.ref("form.root_id"))
                  // .orderBy("booking.created_at desc")
                  .limit(20),
              ).as("products"),
            ]),
        ).as("form"),
      ]),
    )
    .executeTakeFirstOrThrow();

  return {
    establishment: establishment,
    view: view,
  };
};

const SelectForms = (props: { readOnly: boolean }) => {
  const data = useLoaderData<typeof loader>();
  const selectedWaiverIds = data.establishment.form?.waivers.map((waiver) => waiver.waiver_id) || [];
  const [state, setState] = useState(selectedWaiverIds);
  const waivers = data.establishment.waivers;
  // const filterOrgs = props.filterOrgs || [];

  const selectedForms = waivers.filter((waiver) => state.includes(waiver.id));

  return (
    <AnimatingDiv className="space-y-2">
      <p className="font-semibold text-slate-500">Additional forms: {selectedForms.length}</p>
      {selectedForms.map((waiver) => (
        <div key={waiver.id} className="flex flex-row justify-between items-center rounded-md border-slate-200 border px-2 p-1">
          <RInput table={"form_waiver"} field={"data.waiver_id"} index={waiver.id} value={waiver.id} type={"hidden"} />
          <RInput table={"form_waiver"} field={"data.form_id"} index={waiver.id} value={tableIdRef("form")} type={"hidden"} />
          <span>
            {waiver.diving_certificate_organization_key && waiver.diving_certificate_organization_key + " "}
            {waiver.slug} <span className="text-xs text-slate-500">({getWaiverType(waiver.type).description})</span>
          </span>
          {!props.readOnly && (
            <button
              onClick={() => setState(state.filter((waiverId) => waiverId !== waiver.id))}
              type={"button"}
              className="p-1 text-slate-800 hover:bg-slate-100 rounded-full"
            >
              <XMarkIcon className="w-4 h-4" />
            </button>
          )}
        </div>
      ))}
      {!props.readOnly && (
        <select
          value={""}
          onChange={(e) => {
            if (!e.target.value) return;
            setState([...state, e.target.value]);
          }}
          className="select w-full"
        >
          <option value="">include form</option>
          {waivers.map((waiver) => (
            <option key={waiver.id} value={waiver.id} disabled={state.includes(waiver.id)}>
              {waiver.diving_certificate_organization_key && waiver.diving_certificate_organization_key + " "}
              {waiver.slug} ({getWaiverType(waiver.type).verb})
            </option>
          ))}
        </select>
      )}
    </AnimatingDiv>
  );
};

export default function Page() {
  const ctx = useAppContext();
  const data = useLoaderData<typeof loader>();
  const establishment = data.establishment;
  const form = establishment.form;
  const [fields, setFields] = useState<OverwriteField[]>(form?.fields || []);
  const i18n = useLingui();
  return (
    <div>
      <div className="app-container space-y-3">
        <h1 className="text-xl text-slate-500">
          {form ? (data.view === "copy" ? "Copy" : data.view === "view" ? "View" : "Edit") : "Create"} registration form
        </h1>
      </div>
      <ActionForm>
        <div className="app-container">
          <ActionAlert scrollTo />
          <OperationInput table={"participant"} value={"ignore"} />
          <RedirectParamsInput path={_form} paramState={{}} />
          {form && data.view !== "copy" && (
            <Fragment>
              <RInput table={"form"} field={"id"} index={"-0"} value={form.id} />
              <RInput table={"form"} field={"data.deleted_at"} index={"-0"} value={"now"} type={"hidden"} />
              <RInput table={"form"} field={"data.root_id"} index={0} value={form.root_id} type={"hidden"} />
              <RInput table={"form"} field={"data.sort_order"} index={0} value={form.sort_order ?? ""} type={"hidden"} />
            </Fragment>
          )}
          {data.view !== "view" && (
            <RInput
              table={"form"}
              field={"data.establishment_id"}
              value={form && data.view === "copy" ? establishment.id : form?.establishment_id || ""}
              type={"hidden"}
            />
          )}
          <div className="space-y-3">
            <div>
              <RInput
                table={"form"}
                field={"data.name"}
                label={"Name"}
                className="input"
                required
                defaultValue={form?.name}
                readOnly={data.view === "view"}
              />
            </div>
            {ctx.editor && (
              <div className="flex flex-wrap gap-3 items-center ">
                <div className="flex flex-row gap-3 items-center">
                  <RLabel table={"form"} field={"data.filter_activity_slug"}>
                    Default for
                  </RLabel>
                  <RSelect
                    table={"form"}
                    field={"data.filter_activity_slug"}
                    defaultValue={form?.filter_activity_slug || ""}
                    className="select"
                  >
                    <option value="">none</option>
                    {activitySlugs.map((slug) => (
                      <option key={slug} value={slug}>
                        {slug}
                      </option>
                    ))}
                  </RSelect>
                </div>
                <div className="flex-row flex gap-2 items-center">
                  <RInput
                    className="checkbox"
                    table={"form"}
                    label={"Beginner Course"}
                    field={"data.filter_beginner_diver"}
                    hiddenType={"__boolean__"}
                    type={"checkbox"}
                    defaultChecked={form?.filter_beginner_diver}
                  />
                </div>
              </div>
            )}
            <div>
              {data.view === "view" ? (
                <ImageGallery fileNames={form?.files.map((file) => file.filename)} />
              ) : (
                <InputFilesDefault
                  target_id={tableIdRef("form")}
                  target={"form"}
                  defaultValue={form?.files.map((file) => removeObjectKeys(file, "id"))}
                />
              )}
            </div>
            <div className="flex flex-row items-center gap-1">
              <RInput
                className="checkbox"
                table={"form"}
                field={"data.selectable"}
                hiddenType={"__boolean__"}
                type={"checkbox"}
                label={"Selectable for participant"}
                defaultChecked={!!form?.selectable}
                disabled={data.view === "view"}
                readOnly={data.view === "view"}
              />
            </div>
            <div className="pt-3 pb-6">
              <SelectForms readOnly={data.view === "view"} />
            </div>
            <div>
              <div className="flex items-center gap-1">
                <RLabel table={"form"} field={"data.upload_description"}>
                  Generic Upload
                </RLabel>
                <Tooltip
                  description={
                    <p>
                      Add custom instructions for a file upload section on the customer registration page. <br />
                      This section will only appear if text is entered here
                    </p>
                  }
                >
                  <DefaultInfoIcon />
                </Tooltip>
              </div>
              <RTextarea
                table={"form"}
                field={"data.upload_description"}
                placeholder={"Enter a description to show the Generic Upload"}
                className="input"
                defaultValue={form?.upload_description || ""}
                readOnly={data.view === "view"}
              />
            </div>
          </div>
        </div>
        <ParticipantFields
          change={data.view !== "view"}
          fields={fields}
          onFieldsChange={(fields) => setFields(fields)}
          context={{ ...data, i18n: i18n }}
        />
        <div className="app-container">
          {data.view !== "view" && (
            <div className="flex justify-end gap-3 py-4">
              <Backbutton>Cancel</Backbutton>
              <SubmitButton className="btn btn-primary">Save</SubmitButton>
            </div>
          )}
        </div>
      </ActionForm>
      {ctx.environment === "development" && form && (
        <div className="app-container">
          <span>Participants: </span>
          {form.participants.map((participant) => (
            <ParamLink key={participant.id} path={_participant_detail(participant.id)} className="link">
              Participant {participant.id}
            </ParamLink>
          ))}
          <span>Bookings: </span>
          {form.bookings.map((booking) => (
            <ParamLink key={booking.id} path={_booking_detail(booking.id)} className="link">
              {booking.id}
            </ParamLink>
          ))}
          <span>Products: </span>
          {form.products.map((product) => (
            <ParamLink key={product.id} path={_product_mutate} paramState={{ id: product.id }} className="link">
              {product.id}
            </ParamLink>
          ))}
        </div>
      )}
    </div>
  );
}
