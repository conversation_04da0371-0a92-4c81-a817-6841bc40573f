import { getSessionSimple } from "~/utils/session.server";
import { kysely } from "~/misc/database.server";
import { useLoaderData, useRouteLoaderData } from "@remix-run/react";
import { AnimatingDiv } from "~/components/base/base";
import { divingCoursesJsonEb, divingLevelsJsonEb, divingLocationsJsonEb, pricesJsonEb } from "~/domain/product/product-queries.server";
import { fName, logTime, myGroupBy2, tableIdRef } from "~/misc/helpers";
import { sql } from "kysely";
import { jsonArrayFrom, jsonBuildObject, jsonObjectFrom } from "kysely/helpers/postgres";
import { PlanningTabs } from "~/domain/planning/PlanningTabs";
import type { Role, TripType } from "~/domain/planning/plannings-consts";
import { availabilities, getMeetingType, getTripType } from "~/domain/planning/plannings-consts";
import { getMonthObj } from "~/domain/planning/plannings-helpers";
import { DividerWithText } from "~/components/Divider";
import { getDateFromParams } from "~/domain/planning/planning.helpers.server";
import { RInput } from "~/components/ResourceInputs";
import React, { CSSProperties, Fragment, useState } from "react";
import { ActionAlert } from "~/components/ActionAlert";
import { OperationInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { Listbox, ListboxButton, ListboxOption, ListboxOptions } from "@headlessui/react";
import { ArrowUturnLeftIcon, ChevronDownIcon, PencilIcon, PlusCircleIcon, XMarkIcon } from "@heroicons/react/20/solid";
import { twMerge } from "tailwind-merge";
import { MemberSelect } from "~/domain/planning/MemberSelect";
import { SubmitButton } from "~/components/base/Button";
import { getProductTitle, ProductTags } from "~/domain/product/ProductItem";
import { _activity_mutate, _booking_detail, _participant_detail, _participant_mutate } from "~/misc/paths";
import { differenceInYears } from "date-fns";
import { ParamLink } from "~/components/meta/CustomComponents";
import { ActionForm } from "~/components/form/BaseFrom";
import { getNrOfDivesOption } from "~/domain/participant/participant-data";
import { SharedTripFields } from "~/domain/trip/TripForm";
import { TripCarousel } from "~/domain/trip/TripPanel";
import { tripQb } from "~/domain/trip/trip.server";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { flat, unique } from "remeda";
import { paramsToRecord, toggleArray } from "~/misc/parsers/global-state-parsers";
import { memberIsAdminOrOwnerQb, QbArgs } from "~/domain/member/member-queries.server";
import { RestDayIcon } from "~/components/Icons";
import { EstablishmentSeperator } from "~/domain/establishment/EstablishmentSeperator";
import { hadAdminEdit } from "~/domain/member/member-hooks";
import { getActivitySlug } from "~/domain/activity/activity";
import { usePageRefresh } from "~/hooks/use-page-refresh";
import { getTripTotals } from "~/domain/trip/trip-components";
import { useAppContext } from "~/hooks/use-app-context";
import { DivingCertificateId, divingCertificateIds, formatDivingCertShort } from "~/domain/diving-course/diving-courses.data";
import { bookingQb } from "~/domain/booking/booking-queries";
import { participantQb, participantSimpleQb, participatingParticipantIdQb } from "~/domain/participant/participant.queries.server";
import { saleItemSimpleQb, activityWithAddons } from "~/domain/activity/activity-queries";
import { ascNullsLast, dayNumberSelect } from "~/kysely/kysely-helpers";
import { unauthorized } from "~/misc/responses";
import { LoaderFunctionArgs } from "@remix-run/router";
import { getLastDivedShort, safeFormat } from "~/domain/participant/participant-helpers";
import { defaultNotFilledValue } from "~/misc/vars";
import { defaultNotRegisteredValue } from "~/components/shared";
import { HeightInfo, ShoeSizeInfo, WeightInfo } from "~/domain/participant/participant.components";
import { toUtc } from "~/misc/date-helpers";
import { getAdminLevelIndex } from "~/domain/member/member-vars";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { getDbCachedValue } from "~/server/cache/cache.server";
import { getCacheKey } from "~/server/cache/cache.planning.server";
import { useMeasure } from "~/hooks/use-measure";
import { BoatCreateDialog } from "~/domain/planning/BoatSelect";
import { refreshFormdata } from "~/components/form/form-hooks";
import { addTripPanelTitleId } from "~/domain/trip/vars";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  const timer = logTime("planning.index", false);
  const { session_id } = await getSessionSimple(request);
  timer.timeLog("session");

  const date = getDateFromParams(request);
  const month = getMonthObj(date.dateParam);

  const withinDuration = `[${date.dateParam},${date.dateParam}]`;

  const args: QbArgs = { trx: kysely, ctx: { session_id: session_id } };
  const memberQb = await memberIsAdminOrOwnerQb(args, "read")
    .innerJoin("establishment", "establishment.id", "_member.establishment_id")
    .where("establishment.operator_id", "=", state.persist_operator_id)
    .where((eb) => {
      const permissionCmpr = eb.or([eb("_member.owner", "=", true), eb("_member.admin", ">=", getAdminLevelIndex("read"))]);
      if (state.persist_establishment_id) return eb.and([eb("establishment.id", "=", state.persist_establishment_id), permissionCmpr]);
      return permissionCmpr;
    })
    .select("_member.establishment_id")
    .execute();

  const establishmentIds = unique(memberQb.map((member) => member.establishment_id));

  timer.timeLog(`allowed establishments ${establishmentIds}`);

  if (!establishmentIds.length) {
    throw unauthorized("Unauthorized for establishment(s)");
  }

  const divingLocations = await kysely
    .selectFrom("diving_location")
    .where(
      "diving_location.region_id",
      "in",
      kysely
        .selectFrom("establishment")
        .innerJoin("spot", "spot.id", "establishment.spot_id")
        .where("establishment.operator_id", "=", state.persist_operator_id)
        .select("spot.region_id"),
    )
    .selectAll("diving_location")
    .execute();

  timer.timeLog("diving locations");

  const establishments = await Promise.all(
    establishmentIds.map(async (establishmentId) => {
      const cachedEstablishment = await getDbCachedValue({
        key: getCacheKey({ establishmentId: establishmentId, date: date.dateParam, type: "planning-day" }),
        request: async (trx) => {
          const establishmentTimer = logTime("establishment-" + establishmentId);
          const filteredActivityQb = trx
            .selectFrom("sale_item")
            .innerJoin("booking", "booking.id", "sale_item.booking_id")
            .where("booking.cancelled_at", "is", null)
            .where("booking.establishment_id", "=", establishmentId)
            .where("sale_item.duration", "&&", withinDuration);

          const filteredTripsQb = trx
            .selectFrom("trip")
            .where("trip.establishment_id", "=", establishmentId)
            .where("trip.date", "=", date.dateParam);

          const filteredTripAssignmentsQb = filteredTripsQb.innerJoin("trip_assignment", "trip_assignment.trip_id", "trip.id");

          const participations = await trx
            .selectFrom("participation")
            .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
            .innerJoin("booking", "sale_item.booking_id", "booking.id")
            .where("booking.cancelled_at", "is", null)
            .where("booking.establishment_id", "=", establishmentId)
            .leftJoin("trip_assignment", (join) =>
              join
                .onRef("trip_assignment.participation_id", "=", "participation.id")
                .on("trip_assignment.trip_id", "in", filteredTripsQb.select("trip.id")),
            )
            .leftJoin("trip", "trip.id", "trip_assignment.trip_id")
            .where("sale_item.duration", "&&", withinDuration)
            .selectAll("participation")
            .select((eb) => [
              "sale_item.product_id",
              jsonObjectFrom(participantQb(kysely).where("participant.id", "=", eb.ref("participation.participant_id"))).as("participant"),
              jsonObjectFrom(
                saleItemSimpleQb
                  .where("sale_item.duration", "&&", withinDuration)
                  .where("sale_item.id", "=", eb.ref("participation.sale_item_id"))
                  .select((eb) => [dayNumberSelect(date.dateParam, eb.ref("sale_item.duration")).as("day_number")]),
              ).as("activity"),
              jsonObjectFrom(
                bookingQb(kysely)
                  .innerJoin("sale_item", "sale_item.booking_id", "booking.id")
                  .where("sale_item.id", "=", eb.ref("participation.sale_item_id"))
                  .select((eb) =>
                    jsonArrayFrom(activityWithAddons.where("sale_item.booking_id", "=", eb.ref("booking.id"))).as("activities"),
                  ),
              ).as("booking"),
              jsonObjectFrom(
                trx
                  .selectFrom("trip_assignment as ta")
                  .where("ta.id", "=", eb.ref("trip_assignment.id"))
                  .selectAll("ta")
                  .select((eb) => [
                    jsonObjectFrom(eb.selectFrom("member").whereRef("ta.member_id", "=", "member.id").selectAll("member")).as("member"),
                    jsonObjectFrom(tripQb.where("trip.id", "=", eb.ref("ta.trip_id"))).as("trip"),
                  ])
                  .limit(1),
              ).as("trip_assignment"),
            ])
            .orderBy("trip.date", ascNullsLast)
            .orderBy("trip.start_time")
            .orderBy("trip.created_at")
            .orderBy(
              (eb) => eb.selectFrom("member").whereRef("trip_assignment.member_id", "=", "member.id").select("member.name"),
              ascNullsLast,
            )
            .orderBy((eb) => eb(eb.ref("participation.participant_id"), "is", null))
            .orderBy("booking.id_seq")
            .orderBy(
              (eb) =>
                eb
                  .selectFrom("participant")
                  .innerJoin("customer", "customer.id", "participant.customer_id")
                  .innerJoin("person", "person.id", "customer.person_id")
                  .select("person.first_name")
                  .where("participant.id", "=", eb.ref("participation.participant_id")),
              "asc",
            )
            .orderBy("participation.id")
            .execute();
          establishmentTimer.timeLog("participations");

          const members = await trx
            .selectFrom("member")
            .orderBy("member.name")
            .where((eb) =>
              eb.or([
                eb("member.id", "in", filteredTripAssignmentsQb.select("trip_assignment.member_id")),
                eb.and([eb("member.establishment_id", "=", establishmentId), eb("member.deleted_at", "=", at_infinity_value)]),
              ]),
            )
            .selectAll("member")
            .select((eb) => [
              jsonArrayFrom(
                eb
                  .selectFrom("schedule")
                  .whereRef("member.id", "=", "schedule.target_id")
                  .where((eb) => eb("schedule.range", "&&", withinDuration).$castTo())
                  .where((eb) =>
                    eb.or([
                      eb("schedule.days_of_week", "is", null),
                      eb(
                        sql.raw(`extract(isodow from '${date.dateParam}'::timestamp) - 1`),
                        "=",
                        eb.fn("any", [eb.ref("schedule.days_of_week")]),
                      ),
                    ]),
                  )
                  .orderBy("schedule.created_at", sql`asc nulls first`)
                  .orderBy("schedule.available", "desc")
                  .orderBy("schedule.range", "asc")
                  .selectAll("schedule"),
              ).as("schedules"),
              jsonArrayFrom(
                eb
                  .selectFrom("trip_assignment")
                  .innerJoin("trip", "trip.id", "trip_assignment.trip_id")
                  .leftJoin("participation", "participation.id", "trip_assignment.participation_id")
                  .leftJoin("sale_item", "sale_item.id", "participation.sale_item_id")
                  .leftJoin("participant", "participant.id", "participation.participant_id")
                  .leftJoin("booking", "booking.id", "sale_item.booking_id")
                  .where("booking.cancelled_at", "is", null)
                  .where("trip.type", "!=", "rest" satisfies TripType)
                  .whereRef("trip_assignment.member_id", "=", "member.id")
                  .where((eb) => {
                    const datesEb = eb("trip.date", "=", date.dateParam);
                    const establishmentCmpr = eb("trip.establishment_id", "=", establishmentId);
                    return eb.and([establishmentCmpr, datesEb]);
                  })
                  .selectAll("trip_assignment")
                  .select((eb) => [jsonObjectFrom(tripQb.where("trip.id", "=", eb.ref("trip_assignment.trip_id"))).as("trip")]),
              ).as("assignments"),
            ])
            .execute();
          establishmentTimer.timeLog("members");

          const trips = await tripQb
            .where((eb) => eb.or([eb("trip.date", "=", date.dateParam), eb("trip.date", "is", null)]))
            .where("trip.establishment_id", "=", establishmentId)
            .orderBy("trip.date", ascNullsLast)
            .orderBy("trip.start_time")
            .orderBy("trip.created_at")
            .select((eb) => [
              jsonArrayFrom(
                eb
                  .selectFrom("trip_assignment")
                  .whereRef("trip_assignment.trip_id", "=", "trip.id")
                  .selectAll("trip_assignment")
                  .leftJoin("participation", "participation.id", "trip_assignment.participation_id")
                  .leftJoin("sale_item", "sale_item.id", "participation.sale_item_id")
                  .leftJoin("booking", "booking.id", "sale_item.booking_id")
                  .where("booking.cancelled_at", "is", null)
                  .select((eb) => [
                    jsonObjectFrom(
                      participantSimpleQb(kysely)
                        .innerJoin("participation", "participation.participant_id", "participant.id")
                        .where("participation.id", "=", eb.ref("trip_assignment.participation_id"))
                        .selectAll("participant")
                        .select(["participation.id as participation_id"]),
                    ).as("participant"),
                    jsonObjectFrom(eb.selectFrom("member").whereRef("member.id", "=", "trip_assignment.member_id").selectAll("member")).as(
                      "member",
                    ),
                  ]),
              ).as("assignments"),
              jsonObjectFrom(eb.selectFrom("boat").whereRef("boat.id", "=", "trip.boat_id").selectAll("boat")).as("boat"),
            ])
            .execute();
          establishmentTimer.timeLog("trips");

          const productIds = participations.map((participation) => participation.product_id).filter((productId) => !!productId);
          const products = productIds.length
            ? await trx
                .selectFrom("product")
                .innerJoin("item", "item.id", "product.item_id")
                .leftJoin("product_price", "product_price.product_id", "product.id")
                .leftJoin("price", "price.id", "product_price.price_id")
                .selectAll("item")
                .selectAll("product")
                .where("product.id", "in", productIds)
                // .where("product.deleted_at", "=", at_infinity_value)
                .select([divingCoursesJsonEb, divingLocationsJsonEb, divingLevelsJsonEb, pricesJsonEb])
                .where("item.establishment_id", "=", establishmentId)
                .execute()
            : [];
          console.log("products", products.length);
          establishmentTimer.timeLog("products");

          const theRestQuery = trx
            .selectFrom("establishment as _establishment")
            .selectAll("_establishment")
            .leftJoin("spot", "spot.id", "_establishment.spot_id")
            .leftJoin("region", "region.id", "spot.region_id")
            .select("region.country_code")
            .where("_establishment.id", "=", establishmentId)
            .select((establishmentEb) => {
              return [
                "spot.name as spot_name",
                jsonArrayFrom(
                  participantSimpleQb(kysely)
                    .where("participant.booking_id", "is", null)
                    .where("participant.id", "not in", participatingParticipantIdQb)
                    .where("customer.establishment_id", "=", establishmentEb.ref("_establishment.id")),
                ).as("pending_participants"),
                jsonBuildObject({
                  address: establishmentEb.ref("_establishment.address"),
                }).as("operator"),
                jsonArrayFrom(
                  filteredActivityQb
                    .innerJoin("user_session", "user_session.id", "booking.created_by_user_session_id")
                    .innerJoin("user", "user.id", "user_session.user_id")
                    .select((eb) => [
                      sql`(lower (${eb.ref("sale_item.duration")}))`.as("from"),
                      sql`(upper (${eb.ref("sale_item.duration")}))`.as("to"),
                    ]),
                ).as("activities"),
              ];
            });

          const establishment = await theRestQuery.executeTakeFirstOrThrow();
          establishmentTimer.end();

          // const establishmentTimerOld = logTime("establishment-" + establishmentId + "-previous");
          // const establishmentOld = await trx
          //   .selectFrom("establishment as _establishment")
          //   .selectAll("_establishment")
          //   .leftJoin("spot", "spot.id", "_establishment.spot_id")
          //   .leftJoin("region", "region.id", "spot.region_id")
          //   .select("region.country_code")
          //   .where("_establishment.id", "=", establishmentId)
          //   .select((establishmentEb) => {
          //     const filteredActivityQb = establishmentEb
          //       .selectFrom("activity")
          //       .innerJoin("booking", "booking.id", "activity.booking_id")
          //       .where("booking.cancelled_at", "is", null)
          //       .where("booking.establishment_id", "=", establishmentEb.ref("_establishment.id"))
          //       .where("activity.duration", "&&", withinDuration);
          //
          //     const filteredTripsQb = establishmentEb
          //       .selectFrom("trip")
          //       .innerJoin("establishment", "establishment.id", "trip.establishment_id")
          //       .where((eb) => {
          //         const datesCmpr = eb("trip.date", "=", date.dateParam);
          //         const establishmentCmpr = eb("establishment.operator_id", "=", establishmentEb.ref("_establishment.operator_id"));
          //         return eb.and([establishmentCmpr, datesCmpr]);
          //       });
          //
          //     const filteredTripAssignmentsQb = filteredTripsQb.innerJoin("trip_assignment", "trip_assignment.trip_id", "trip.id");
          //
          //     const participationsQb = establishmentEb
          //       .selectFrom("participation")
          //       .innerJoin("activity", "activity.id", "participation.sale_item_id")
          //       .innerJoin("booking", "activity.booking_id", "booking.id")
          //       .where("booking.cancelled_at", "is", null)
          //       .where("booking.establishment_id", "=", establishmentEb.ref("_establishment.id"))
          //       .leftJoin("trip_assignment", (join) =>
          //         join
          //           .onRef("trip_assignment.participation_id", "=", "participation.id")
          //           .on("trip_assignment.trip_id", "in", filteredTripsQb.select("trip.id")),
          //       )
          //       .leftJoin("trip", "trip.id", "trip_assignment.trip_id")
          //       .where((eb) =>
          //         eb.or([
          //           eb("activity.duration", "&&", withinDuration),
          //           eb("participation.id", "in", filteredTripAssignmentsQb.select("trip_assignment.participation_id")),
          //         ]),
          //       )
          //       .selectAll("participation")
          //       .select((eb) => [
          //         jsonObjectFrom(participantQb(kysely).where("participant.id", "=", eb.ref("participation.participant_id"))).as(
          //           "participant",
          //         ),
          //         jsonObjectFrom(
          //           activitySimpleQb
          //             .where("activity.duration", "&&", withinDuration)
          //             .where("activity.id", "=", eb.ref("participation.sale_item_id"))
          //             .select((eb) => [dayNumberSelect(date.dateParam, eb.ref("activity.duration")).as("day_number")]),
          //         ).as("activity"),
          //         jsonObjectFrom(
          //           bookingQb(kysely)
          //             .innerJoin("activity", "activity.booking_id", "booking.id")
          //             .where("activity.id", "=", eb.ref("participation.sale_item_id"))
          //             .select((eb) =>
          //               jsonArrayFrom(activityWithAddons.where("activity.booking_id", "=", eb.ref("booking.id"))).as("activities"),
          //             ),
          //         ).as("booking"),
          //         jsonObjectFrom(
          //           establishmentEb
          //             .selectFrom("trip_assignment as ta")
          //             .where("ta.id", "=", eb.ref("trip_assignment.id"))
          //             .selectAll("ta")
          //             .select((eb) => [
          //               jsonObjectFrom(eb.selectFrom("member").whereRef("ta.member_id", "=", "member.id").selectAll("member")).as("member"),
          //               jsonObjectFrom(tripQb.where("trip.id", "=", eb.ref("ta.trip_id"))).as("trip"),
          //             ])
          //             .limit(1),
          //         ).as("trip_assignment"),
          //       ])
          //       .orderBy("trip.date", ascNullsLast)
          //       .orderBy("trip.start_time")
          //       .orderBy("trip.created_at")
          //       .orderBy(
          //         (eb) => eb.selectFrom("member").whereRef("trip_assignment.member_id", "=", "member.id").select("member.name"),
          //         ascNullsLast,
          //       )
          //       .orderBy((eb) => eb(eb.ref("participation.participant_id"), "is", null))
          //       .orderBy("booking.id_seq")
          //       .orderBy(
          //         (eb) =>
          //           eb
          //             .selectFrom("participant")
          //             .innerJoin("customer", "customer.id", "participant.customer_id")
          //             .innerJoin("person", "person.id", "customer.person_id")
          //             .select("person.first_name")
          //             .where("participant.id", "=", eb.ref("participation.participant_id")),
          //         "asc",
          //       )
          //       .orderBy("participation.id");
          //
          //     return [
          //       "spot.name as spot_name",
          //       jsonArrayFrom(
          //         establishmentEb
          //           .selectFrom("product")
          //           .selectAll("product")
          //           .select([divingCoursesJsonEb, divingLocationsJsonEb, divingLevelsJsonEb])
          //           .where("item.establishment_id", "=", establishmentEb.ref("_establishment.id")),
          //       ).as("products"),
          //       jsonArrayFrom(
          //         participantSimpleQb(kysely)
          //           .where("participant.booking_id", "is", null)
          //           .where("participant.id", "not in", participatingParticipantIdQb)
          //           .where("customer.establishment_id", "=", establishmentEb.ref("_establishment.id")),
          //       ).as("pending_participants"),
          //       jsonArrayFrom(participationsQb).as("participations"),
          //       jsonBuildObject({
          //         address: establishmentEb.ref("_establishment.address"),
          //       }).as("operator"),
          //       jsonArrayFrom(
          //         tripQb
          //           .where((eb) => eb.or([eb("trip.date", "=", date.dateParam), eb("trip.date", "is", null)]))
          //           .where("trip.establishment_id", "=", establishmentEb.ref("_establishment.id"))
          //           .orderBy("trip.date", ascNullsLast)
          //           .orderBy("trip.start_time")
          //           .orderBy("trip.created_at")
          //           .select((eb) => [
          //             jsonArrayFrom(
          //               eb
          //                 .selectFrom("trip_assignment")
          //                 .whereRef("trip_assignment.trip_id", "=", "trip.id")
          //                 .selectAll("trip_assignment")
          //                 .leftJoin("participation", "participation.id", "trip_assignment.participation_id")
          //                 .leftJoin("activity", "activity.id", "participation.sale_item_id")
          //                 .leftJoin("booking", "booking.id", "activity.booking_id")
          //                 .where("booking.cancelled_at", "is", null)
          //                 .select((eb) => [
          //                   jsonObjectFrom(
          //                     participantSimpleQb(kysely)
          //                       .innerJoin("participation", "participation.participant_id", "participant.id")
          //                       .where("participation.id", "=", eb.ref("trip_assignment.participation_id"))
          //                       .selectAll("participant")
          //                       .select(["participation.id as participation_id"]),
          //                   ).as("participant"),
          //                   jsonObjectFrom(
          //                     eb.selectFrom("member").whereRef("member.id", "=", "trip_assignment.member_id").selectAll("member"),
          //                   ).as("member"),
          //                 ]),
          //             ).as("assignments"),
          //             jsonObjectFrom(eb.selectFrom("boat").whereRef("boat.id", "=", "trip.boat_id").selectAll("boat")).as("boat"),
          //           ]),
          //       ).as("trips"),
          //       jsonArrayFrom(
          //         filteredActivityQb
          //           .innerJoin("user_session", "user_session.id", "booking.created_by_user_session_id")
          //           .innerJoin("user", "user.id", "user_session.user_id")
          //           .select((eb) => [
          //             sql`(lower (${eb.ref("activity.duration")}))`.as("from"),
          //             sql`(upper (${eb.ref("activity.duration")}))`.as("to"),
          //           ]),
          //       ).as("activities"),
          //       jsonArrayFrom(
          //         establishmentEb
          //           .selectFrom("member")
          //           .orderBy("member.name")
          //           .whereRef("member.establishment_id", "=", "_establishment.id")
          //           .where((eb) =>
          //             eb.or([
          //               eb("member.id", "in", filteredTripAssignmentsQb.select("trip_assignment.member_id")),
          //               eb.and([
          //                 eb("member.establishment_id", "=", eb.ref("_establishment.id")),
          //                 eb("member.deleted_at", "=", at_infinity_value),
          //               ]),
          //             ]),
          //           )
          //           .selectAll("member")
          //           .select((eb) => [
          //             jsonArrayFrom(
          //               eb
          //                 .selectFrom("schedule")
          //                 .whereRef("member.id", "=", "schedule.target_id")
          //                 .where((eb) => eb("schedule.range", "&&", withinDuration).$castTo())
          //                 .where((eb) =>
          //                   eb.or([
          //                     eb("schedule.days_of_week", "is", null),
          //                     eb(
          //                       sql.raw(`extract(isodow from '${date.dateParam}'::timestamp) - 1`),
          //                       "=",
          //                       eb.fn("any", [eb.ref("schedule.days_of_week")]),
          //                     ),
          //                   ]),
          //                 )
          //                 .orderBy("schedule.created_at", sql`asc nulls first`)
          //                 .orderBy("schedule.available", "desc")
          //                 .orderBy("schedule.range", "asc")
          //                 .selectAll("schedule"),
          //             ).as("schedules"),
          //             jsonArrayFrom(
          //               eb
          //                 .selectFrom("trip_assignment")
          //                 .innerJoin("trip", "trip.id", "trip_assignment.trip_id")
          //                 .leftJoin("participation", "participation.id", "trip_assignment.participation_id")
          //                 .leftJoin("activity", "activity.id", "participation.sale_item_id")
          //                 .leftJoin("participant", "participant.id", "participation.participant_id")
          //                 .leftJoin("booking", "booking.id", "activity.booking_id")
          //                 .where("booking.cancelled_at", "is", null)
          //                 .where("trip.type", "!=", "rest" satisfies TripType)
          //                 .whereRef("trip_assignment.member_id", "=", "member.id")
          //                 .where((eb) => {
          //                   const datesEb = eb("trip.date", "=", date.dateParam);
          //                   const establishmentCmpr = eb("trip.establishment_id", "=", establishmentEb.ref("_establishment.id"));
          //                   return eb.and([establishmentCmpr, datesEb]);
          //                 })
          //                 .selectAll("trip_assignment")
          //                 .select((eb) => [jsonObjectFrom(tripQb.where("trip.id", "=", eb.ref("trip_assignment.trip_id"))).as("trip")]),
          //             ).as("assignments"),
          //           ]),
          //       ).as("members"),
          //     ];
          //   })
          //   .executeTakeFirst();
          // establishmentTimerOld.end();

          return {
            ...establishment,
            participations: participations,
            members: members,
            trips: trips,
            products: products,
          };
        },
      });

      const establishmentLatest = await kysely
        .selectFrom("establishment")
        .select((eb) => [
          jsonArrayFrom(eb.selectFrom("boat").whereRef("boat.establishment_id", "=", "establishment.id").selectAll("boat")).as("boats"),
        ])
        .where("establishment.id", "=", establishmentId)
        .executeTakeFirstOrThrow();

      const establishment = { ...cachedEstablishment.response, ...establishmentLatest };
      return establishment;
    }),
  );

  timer.end();

  if (establishments.length === 0) throw unauthorized("Not authorized for the establishment");
  const allProducts = flat(establishments.map((establishment) => establishment.products));
  return {
    establishments: establishments.map((item) => ({
      ...item,
      participations: item.participations.map((participation) => {
        const activity = participation.activity;
        const product = allProducts.find((product) => product.id === activity?.product_id);
        const durationStr =
          activity && !!activity.duration_in_days && activity.duration_in_days > 1 ? ", day " + ((activity.day_number || 0) + 1) : "";
        const productStr = product ? getProductTitle(product) : "custom";
        const activityCardTitle =
          (activity ? productStr : "Participants are scheduled on a different day than the activity itself") + durationStr;

        return {
          ...participation,
          activityTitle: activityCardTitle,
        };
      }),
      // products: [],
      // participations: [],
      members: item.members.map((member) => {
        const scheduledAvailable = member.schedules?.[member.schedules.length - 1]?.available;
        const availability = {
          captain: member.assignments.length > 0 ? 1 : member.captain ? (scheduledAvailable ? 0 : 2) : 3,
          instructor: member.assignments.length > 0 ? 1 : member.diving_level > 0 ? (scheduledAvailable ? 0 : 2) : 3,
          divemaster: member.assignments.length > 0 ? 1 : member.diving_level > 0 ? (scheduledAvailable ? 0 : 2) : 3,
          crew: member.assignments.length > 0 ? 1 : member.crew ? (scheduledAvailable ? 0 : 2) : 3,
        } satisfies Record<Role, number>;
        return { ...member, availability: availability };
      }),
      // participants: establishment.participants.map(participant => {
      //
      // })
    })),
    diving_locations: divingLocations,
    // operatorLocationId: establishmentIds,
    // bookings: establishment.bookings.map(mapBookingTotals),
    // members: members,
    date: date,
    month: month,
  };
};

export const useOptionalPlanningDayLoader = () => {
  const search = useSearchParams2();
  const data = useRouteLoaderData<typeof loader>("routes/_all._catch._w.planning._index");

  if (!data) return null;

  const establishments = data.establishments;
  const activityCardTitles = unique(
    flat(establishments.map((establishment) => establishment.participations.map((participation) => participation.activityTitle))),
  );

  const trips = flat(establishments.map((establishment) => establishment.trips)).filter((trip) => trip.date);
  const selectedTrip = trips.find((trip) => trip.id === search.state.filter_id);
  const selectedActivityTitle = activityCardTitles.find((title) => title === search.state.filter_id);
  const products = flat(establishments.map((establishment) => establishment.products));
  const participations = flat(
    establishments.map((establishment) =>
      establishment.participations.map((participation) => {
        const product = products.find((product) => product.id === participation.activity?.product_id);
        const trip = participation?.trip_assignment?.trip;
        const activity = participation?.activity;
        const getBaseGroup = () => {
          if (!trip) return "3.To be scheduled";
          if (trip.establishment_id !== establishment.id) return "0.Scheduled in different location";
          const tripType = getTripType(trip.type);
          if (tripType?.key === "rest") return `2.Rest day`;
          return `1.Scheduled activities`;
        };
        const baseGroup = getBaseGroup();
        const productGroupIdentifier =
          baseGroup + "" + trip?.id + "" + product?.id + "-" + activity?.duration_in_days + "-" + activity?.day_number;

        return {
          ...participation,
          product: product,
          establishment: establishment,
          product_panel_identifier: productGroupIdentifier,
          base_group: baseGroup,
        };
      }),
    ),
  ).map((participation) => ({
    ...participation,
    identifier: participation.id + participation?.establishment.id + participation.base_group + participation?.trip_assignment?.id,
  }));

  const filteredParticipations = participations.filter((participation) => {
    return (
      !search.state.filter_id ||
      participation.activityTitle === search.state.filter_id ||
      participation.trip_assignment?.trip?.id === search.state.filter_id ||
      participation.product?.activity_slug === search.state.filter_id
    );
  });

  return {
    ...data,
    allEstablishments: establishments,
    allProducts: products,
    allParticipations: participations,
    filteredParticipations: filteredParticipations,
    trips: trips,
    selectedTrip: selectedTrip,
    activeFilter: selectedTrip || selectedActivityTitle,
    activityTitles: activityCardTitles,
    selectedActivityTitle: selectedActivityTitle,
    establishments: establishments.filter((establishment) => !search.state.persist_toggle_establishment_ids.includes(establishment.id)),
  };
};

export const usePlanningDayLoader = () => {
  const data = useOptionalPlanningDayLoader();
  if (!data) throw new Error("day planning data is required");
  return data;
};

const tripUnselectEnabled = false;

const TripSelect = (props: { name: string; establishment_id: string; defaultValue?: string; defaultActivityLocation?: string }) => {
  const data = usePlanningDayLoader();
  const [tripId, setTripId] = useState<string | null>(props.defaultValue || "");
  const onChangeTripId = (value: string | null) => {
    setTripId(value);
    refreshFormdata();
  };
  const establishment = data.establishments.find((establishment) => establishment.id === props.establishment_id);
  const trips = flat(data.establishments.map((establishment) => establishment.trips));
  const selectedTrip = trips.find((trip) => trip.id === tripId);

  const tripEntry = getTripType(selectedTrip?.type);

  const addTripValue = tableIdRef("trip", props.name);

  const finalTripId = selectedTrip?.date === null ? addTripValue : tripId;

  const restDayIdentifier = props.name + "rest";
  const addRestdayValue = tableIdRef("trip", restDayIdentifier);
  const addingTrip = finalTripId === addTripValue;
  const addingRestday = finalTripId === addRestdayValue;

  // const establishmentId = data.establishments

  const existingRestDay = trips.find(
    (trip) => trip.type === ("rest" satisfies TripType) && props.establishment_id === trip.establishment_id,
  );

  return (
    <div className="space-y-2">
      <Listbox name={props.name} value={finalTripId || ""} onChange={onChangeTripId} as={AnimatingDiv} className="space-y-2">
        <ListboxButton className="flex w-full flex-row items-center gap-3 rounded-md border border-slate-300 p-2">
          {addingTrip ? (
            <Fragment>
              <PlusCircleIcon className="h-5 w-5" />{" "}
              <span>
                Add trip
                {selectedTrip && (
                  <span>
                    - {selectedTrip.activity_location} {selectedTrip.boat?.name} ({getTripType(selectedTrip.type)?.name})
                  </span>
                )}
              </span>
            </Fragment>
          ) : selectedTrip ? (
            <Fragment>
              {selectedTrip.type === ("rest" satisfies TripType) ? (
                <RestDayIcon className={"text-primary w-5 h-5"} />
              ) : (
                <div
                  className={twMerge(
                    "h-5 w-5 rounded-full bg-green-500",
                    getTripTotals(selectedTrip).seatsTaken > 0 && "bg-primary",
                    getTripTotals(selectedTrip).available <= 0 && "bg-red-500",
                  )}
                />
              )}
              <span>
                {selectedTrip.activity_location} {selectedTrip.boat?.name} ({tripEntry?.name})
              </span>
            </Fragment>
          ) : finalTripId === null ? (
            <span>Unselect</span>
          ) : addingRestday ? (
            <Fragment>
              <RestDayIcon className={"text-primary w-5 h-5"} />
              <span>Rest day</span>
            </Fragment>
          ) : (
            <Fragment>
              <span>Select/create trip</span>
            </Fragment>
          )}
          <span className="flex-1" />
          <ChevronDownIcon className="h-5 w-5 transition-transform ui-open:rotate-180" />
        </ListboxButton>
        <ListboxOptions modal={false} className={"rounded-md border border-slate-300"}>
          {data.establishments.map((establishment) => {
            return (
              <div key={establishment.id}>
                {data.allEstablishments.length > 1 && (
                  <div className="p-3">
                    <EstablishmentSeperator establishment={establishment} />
                  </div>
                )}
                {establishment.trips.map((trip) => {
                  const tripEntry = getTripType(trip.type);
                  const tripTotals = getTripTotals(trip);
                  return (
                    <ListboxOption
                      value={trip.id}
                      key={trip.id}
                      className="flex w-full whitespace-nowrap  min-w-0 cursor-default flex-row items-center gap-3 p-3 hover:bg-slate-200 ui-selected:bg-slate-100 ui-active:bg-slate-200"
                    >
                      {trip.date ? (
                        <Fragment>
                          {tripEntry?.key === "rest" ? (
                            <RestDayIcon className={"text-primary w-5 h-5"} />
                          ) : (
                            <div
                              className={twMerge(
                                "h-5 w-5 rounded-full bg-green-500 text-white text-xs text-center",
                                tripTotals.seatsTaken > 0 && "bg-primary",
                                tripTotals.available <= 0 && "bg-red-500",
                              )}
                            >
                              {/*{establishment?.short || ""}*/}
                            </div>
                          )}
                        </Fragment>
                      ) : (
                        <span className="flex gap-2 items-center ">
                          <PlusCircleIcon className="h-5 w-5 text-primary" /> <span className="text-primary">add trip</span> <span>-</span>
                        </span>
                      )}
                      <span className="truncate">
                        {trip.activity_location} {trip.boat?.name} ({tripEntry?.name})
                      </span>
                    </ListboxOption>
                  );
                })}
              </div>
            );
          })}
          {!existingRestDay && (
            <ListboxOption
              value={addRestdayValue}
              className="flex w-full cursor-default flex-wrap items-center gap-3 p-3 hover:bg-slate-200 ui-selected:bg-slate-100 ui-active:bg-slate-200"
            >
              <RestDayIcon className={"text-primary w-5 h-5"} />
              <span>Rest day</span>
            </ListboxOption>
          )}
          <ListboxOption
            value={addTripValue}
            className="flex w-full cursor-default flex-wrap items-center gap-3 p-3 text-primary hover:bg-slate-200 ui-selected:bg-slate-100 ui-active:bg-slate-200"
          >
            <PlusCircleIcon className="h-5 w-5" /> <span>add trip</span>
          </ListboxOption>
          {tripUnselectEnabled && (
            <ListboxOption
              value={""}
              className="flex w-full cursor-default flex-wrap items-center gap-3 p-3 text-slate-600 hover:text-slate-700 hover:bg-slate-200 ui-selected:bg-slate-100 ui-active:bg-slate-200"
            >
              <span>unselect</span>
            </ListboxOption>
          )}
        </ListboxOptions>
      </Listbox>
      {addingRestday && (
        <div>
          {/*{existingRestDay && <RInput table={"trip"} field={"id"} index={props.name} value={existingRestDay.id} />}*/}
          <RInput table={"trip"} field={"data.type"} index={restDayIdentifier} value={"rest" satisfies TripType} type={"hidden"} />
          <RInput table={"trip"} field={"data.establishment_id"} index={restDayIdentifier} value={props.establishment_id} type={"hidden"} />
          <RInput table={"trip"} field={"data.date"} index={restDayIdentifier} value={data.date.dateParam} type={"hidden"} />
        </div>
      )}
      {addingTrip &&
        (selectedTrip ? (
          <Fragment>
            <input type={"hidden"} name={fName("trip", "data.date", props.name)} value={data.date.dateParam} />
            {selectedTrip.sites.map((site, index) => (
              <input key={index} type={"hidden"} name={fName("trip", "data.sites", props.name, index)} value={site} />
            ))}
            <RInput
              type={"hidden"}
              table={"trip"}
              field={"data.establishment_id"}
              index={props.name}
              value={selectedTrip.establishment_id}
            />
            <RInput type={"hidden"} table={"trip"} field={"data.capacity"} index={props.name} value={selectedTrip.capacity ?? ""} />
            <RInput type={"hidden"} table={"trip"} field={"data.start_time"} index={props.name} value={selectedTrip.start_time ?? ""} />
            <RInput
              type={"hidden"}
              table={"trip"}
              field={"data.start_location"}
              index={props.name}
              value={selectedTrip.start_location ?? ""}
            />
            <RInput type={"hidden"} table={"trip"} field={"data.boat_id"} index={props.name} value={selectedTrip.boat_id ?? ""} />
            <RInput
              type={"hidden"}
              table={"trip"}
              field={"data.activity_location"}
              index={props.name}
              value={selectedTrip.activity_location ?? ""}
            />
            <RInput type={"hidden"} table={"trip"} field={"data.type"} index={props.name} value={selectedTrip.type ?? ""} />
          </Fragment>
        ) : (
          <div className="rounded-md border border-slate-300 p-3">
            <RInput table={"establishment"} field={"id"} index={0} value={props.establishment_id} />
            <OperationInput table={"establishment"} index={0} value={"ignore"} />
            <RInput type={"hidden"} table={"trip"} field={"data.establishment_id"} index={props.name} value={tableIdRef("establishment")} />
            <input type={"hidden"} name={fName("trip", "data.date", props.name)} value={data.date.dateParam} />
            <OperationInput table={"establishment"} index={0} value={"ignore"} />
            <SharedTripFields identifier={props.name} trip={{ start_time: establishment?.default_trip_start_time }} />
          </div>
        ))}
    </div>
  );
};

const participantColumns = [
  "Name",
  "Ref#",
  "Meet",
  "Time",
  "Cert",
  "Dives",
  "Last",
  "Age",
  "Weight",
  "Boots",
  "Height",
  "Wetsuit",
] as const;

const totalSpan = participantColumns.length + 2;

const spanCssProperties = (columnCounts: number): CSSProperties => ({
  gridColumn: `span ${columnCounts} / span ${columnCounts}`,
  gridColumnStart: `span ${columnCounts}`,
  gridColumnEnd: `span ${columnCounts}`,
});

const ParticipantRow = (props: { identifier: string; editMode: boolean; rest: boolean }) => {
  const data = usePlanningDayLoader();
  const participations = data.allParticipations;
  const [state, setState] = useState<"update" | "delete" | "nothing">("nothing");
  const participation = participations.find((participant) => participant.identifier === props.identifier);

  if (!participation) return <div style={spanCssProperties(totalSpan)}>could not find assignmentOrParticipantId: {props.identifier}</div>;

  const participant = participation.participant;
  const booking = participation.booking;
  const establishment = data.allEstablishments.find((establishment) => establishment.id === participant?.establishment_id);
  const tripAssignmentId = participation.trip_assignment?.id;

  const products = flat(data.establishments.map((establishment) => establishment.products));
  const product = products.find((product) => product.id === participation?.activity?.product_id);
  const activitySlug = getActivitySlug(product?.activity_slug);
  const meetingType = getMeetingType(participation?.booking?.meeting_type);

  const defaultDivingLocation = product?.diving_locations?.[0]?.name;

  // const cols = {
  //   Name: <Fragment />,
  //   "Ref#": <Fragment />,
  //   Meet: <Fragment />,
  //   Time: <Fragment />,
  //   Cert: <Fragment />,
  //   Dives: <Fragment />,
  //   Last: <Fragment />,
  //   Age: <Fragment />,
  //   Weight: <Fragment />,
  //   Boots: <Fragment />,
  //   Height: <Fragment />,
  //   Wetsuit: <Fragment />,
  // } satisfies Record<ParticipantColumn, ReactNode>;

  const fieldNameIndex = "zzz-last-" + participation.id + participation.trip_assignment?.id;
  return (
    <Fragment key={fieldNameIndex}>
      <Fragment>
        {tripAssignmentId && props.editMode ? (
          <div className="w-12">
            {state === "nothing" ? (
              <div className="flex flex-row gap-2">
                <button type={"button"} className="text-primary hover:opacity-60" onClick={() => setState("update")}>
                  <PencilIcon className="h-4 w-4" />
                </button>
                <button type={"button"} className="text-red-500 hover:opacity-60" onClick={() => setState("delete")}>
                  <XMarkIcon className="h-4 w-4" />
                </button>
              </div>
            ) : (
              <button type={"button"} className="text-slate-900 hover:opacity-60" onClick={() => setState("nothing")}>
                <ArrowUturnLeftIcon className="h-4 w-4" />
              </button>
            )}
          </div>
        ) : (
          <div></div>
        )}
        <div className={twMerge("transition-opacity", state === "delete" && "line-through opacity-60")}>
          {participant ? (
            <ParamLink
              path={participant.user_id ? _participant_detail(participant.id) : _participant_mutate}
              paramState={{ booking_id: booking?.id }}
              className="link"
            >
              <span>
                {participant.first_name} {participant.last_name}
              </span>
            </ParamLink>
          ) : (
            <span>{defaultNotRegisteredValue}</span>
          )}
        </div>
        <div className={twMerge("transition-opacity", state === "delete" && "line-through opacity-60")}>
          {!!booking && (
            <ParamLink className="link" path={_booking_detail(booking.id)}>
              {booking.booking_reference || booking.sqid}
            </ParamLink>
          )}
        </div>
        <div className={twMerge("transition-opacity", state === "delete" && "line-through opacity-60")}>{meetingType?.short}</div>
        <div className={twMerge("transition-opacity", state === "delete" && "line-through opacity-60")}>
          {participation?.booking?.meeting_time?.slice(0, 5) || defaultNotFilledValue}
        </div>
        <div className={twMerge("transition-opacity", state === "delete" && "line-through opacity-60")}>
          {participant &&
            ((participant.diving_certificate_level && formatDivingCertShort(participant.diving_certificate_level as DivingCertificateId)) ||
              defaultNotFilledValue)}
        </div>
        <div className={twMerge("transition-opacity", state === "delete" && "line-through opacity-60")}>
          {getNrOfDivesOption(participant?.number_of_dives)[1]}
        </div>
        {/*<td>{participant.last_dived_at && format(new Date(participant.last_dived_at), "LLL")}</td>*/}
        {/*<td>{participant.last_dived_at && format(new Date(participant.last_dived_at), "LLL")}</td>*/}
        <div className={twMerge("transition-opacity", state === "delete" && "line-through opacity-60")}>
          {participant && (getLastDivedShort(participant?.last_dive_within_months) || defaultNotFilledValue)}
        </div>
        <div className={twMerge("transition-opacity", state === "delete" && "line-through opacity-60")}>
          {participant &&
            (participant?.birth_date
              ? differenceInYears(toUtc(data.date.dateParam), toUtc(participant.birth_date))
              : defaultNotFilledValue)}
        </div>
        <div className={twMerge("transition-opacity", state === "delete" && "line-through opacity-60")}>
          {participant && (
            <WeightInfo value={participant.weight_value} unit={participant.weight_unit} default_unit={establishment?.default_weight_unit} />
          )}
        </div>
        <div className={twMerge("transition-opacity", state === "delete" && "line-through opacity-60")}>
          {participant && (
            <ShoeSizeInfo
              value={participant.shoe_size_value}
              unit={participant.shoe_size_unit}
              default_unit={establishment?.default_shoe_size_unit}
            />
          )}
        </div>
        <div className={twMerge("transition-opacity", state === "delete" && "line-through opacity-60")}>
          {participant && (
            <HeightInfo value={participant.height_value} unit={participant.height_unit} default_unit={establishment?.default_height_unit} />
          )}
        </div>
        <div className={twMerge("transition-opacity", state === "delete" && "line-through opacity-60")}>
          {safeFormat(participant, (p) => p.wetsuit_size)}
        </div>
        {/*<td>{participant ? participant.diet || "OK" : ""}</td>*/}
      </Fragment>
      <Fragment>
        <div />
        <div
          style={spanCssProperties(totalSpan)}
          className={twMerge("transition-opacity", state === "delete" && "line-through opacity-60")}
        >
          {state && (
            <AnimatingDiv>
              {tripAssignmentId && <RInput table={"trip_assignment"} field={"id"} index={fieldNameIndex} value={tripAssignmentId} />}
              {state === "delete" && <OperationInput table={"trip_assignment"} index={fieldNameIndex} value={"delete"} />}
              <RInput
                table={"trip_assignment"}
                field={"data.participation_id"}
                index={fieldNameIndex}
                value={participation.id}
                type={"hidden"}
              />
              {state === "update" ? (
                <div className="space-y-3 pb-4">
                  <MemberSelect
                    name={fName("trip_assignment", "data.member_id", fieldNameIndex)}
                    role={activitySlug === "diving-course" ? "instructor" : "divemaster"}
                    defaultValue={participation?.trip_assignment?.member?.id || ""}
                    establishment_id={participation.establishment.id}
                  />
                  <TripSelect
                    name={fName("trip_assignment", "data.trip_id", fieldNameIndex)}
                    establishment_id={participation.establishment.id}
                    defaultValue={participation?.trip_assignment?.trip?.id || ""}
                    defaultActivityLocation={defaultDivingLocation}
                  />
                  {/*<button type={"button"} className="btn btn-red" onClick={() => change("delete")}>*/}
                  {/*  Delete*/}
                  {/*</button>*/}
                </div>
              ) : (
                <Fragment>
                  <RInput
                    table={"trip_assignment"}
                    field={"data.member_id"}
                    index={fieldNameIndex}
                    value={tableIdRef("member", participation.trip_assignment?.member?.id || "none")}
                    type={"hidden"}
                  />
                  <RInput
                    table={"trip_assignment"}
                    field={"data.trip_id"}
                    index={fieldNameIndex}
                    value={tableIdRef("trip")}
                    type={"hidden"}
                  />
                </Fragment>
              )}
              <RInput
                table={"trip_assignment"}
                field={"data.role"}
                index={fieldNameIndex}
                type={"hidden"}
                value={"instructor" satisfies Role}
              />
            </AnimatingDiv>
          )}
        </div>
      </Fragment>
    </Fragment>
  );
};

const ProductGroupPanel = (props: { participantIdentifiers: string[] }) => {
  const [ref, { width }] = useMeasure();
  const context = useAppContext();
  const search = useSearchParams2();
  const data = usePlanningDayLoader();
  const participations = data.filteredParticipations.filter((participant) => props.participantIdentifiers.includes(participant.identifier));
  const tripAssignmintIds = participations
    .map((participation) => participation.trip_assignment?.id)
    .filter((tripAssignmentId): tripAssignmentId is string => !!tripAssignmentId);
  // console.log("particpans", participants);
  const firstParticipation = participations[0];
  const trip = firstParticipation?.trip_assignment?.trip;
  // if (!trip) return <div>Trip not found!</div>;
  const tripEntry = getTripType(trip?.type);
  const activity = firstParticipation?.activity;
  const booking = firstParticipation?.booking;
  const directBookings = participations.filter((participation) => participation.booking?.direct_booking);
  const canEdit = hadAdminEdit(context, booking?.establishment_id || trip?.establishment_id);
  const product = flat(data.establishments.map((establishment) => establishment.products)).find(
    (product) => product.id === activity?.product_id,
  );
  const activitySlug = getActivitySlug(product?.activity_slug);
  const identifier = firstParticipation?.product_panel_identifier || "";
  const editIdentifier = identifier + "-edit";
  const toggledPanelIds = search.state.persist_toggle_activity_panel_id || [];
  const toggledEditIds = toggleArray(toggledPanelIds, editIdentifier);

  const panelIsOn = !!trip === !!toggledPanelIds.find((id) => id.startsWith(identifier));
  const editIsOn = canEdit && !!trip === toggledPanelIds.includes(editIdentifier);

  const unscheduleFormId = "unschedule-form-" + tripAssignmintIds.join("-");

  const defaultDivingLocation = product?.diving_locations?.[0]?.name;

  const memberGroups = myGroupBy2(participations, (participant) => participant.trip_assignment?.member?.id || "");

  const isRestDay = tripEntry?.key === ("rest" satisfies TripType);

  const establishmentId = trip?.establishment_id || firstParticipation?.establishment.id || "";
  return (
    <AnimatingDiv className="space-y-2">
      <ParamLink
        type={"button"}
        paramState={{
          persist_toggle_activity_panel_id: toggledPanelIds.find((id) => id.startsWith(identifier))
            ? toggledPanelIds.filter((id) => !id.startsWith(identifier))
            : [...toggledPanelIds, identifier],
        }}
        className={twMerge(
          `block w-full space-y-2 rounded-md bg-secondary-50 p-2 text-left`,
          isRestDay && "bg-gray-100 opacity-70",
          !activity && "bg-red-200",
        )}
      >
        <div className="space-y-3">
          <div className="flex flex-row justify-between">
            <div className="space-y-1">
              <div>
                <p className="font-semibold">{firstParticipation?.activityTitle}</p>
                {product?.subtitle && <p className="text-xs text-slate-500">{product.subtitle}</p>}
              </div>
              {product && (
                <div className={"flex flex-wrap gap-2"}>
                  {!!directBookings.length && (
                    <span className="px-2 bg-slate-500 justify-center flex flex-row items-center text-xs rounded-md text-white">
                      {directBookings.length === 1 ? "Direct booking" : directBookings.length + " Direct bookings"}
                    </span>
                  )}
                  <ProductTags item={product} />
                </div>
              )}
            </div>
            {tripEntry ? (
              <div className="space-y-1">
                <div className="h-14 w-20 text-secondary-tag">{tripEntry.icon}</div>
                {trip?.boat && (
                  <div className="text-center text-secondary-tag line-clamp-1 max-w-24">
                    <span>{trip.boat.name}</span>
                  </div>
                )}
              </div>
            ) : (
              <div className="p-4">?</div>
            )}
          </div>
          {memberGroups.map((memberGroup) => {
            const member = memberGroup.items[0]?.trip_assignment?.member;
            const nrOfDivers = memberGroup.items.length;

            const certificateId = divingCertificateIds.find((levelId) =>
              memberGroup.items.find((item) => item.participant?.diving_certificate_level === levelId),
            );
            return (
              <div key={memberGroup.groupKey} className="flex flex-row gap-3">
                {member ? <span>{member.name}</span> : <span>?</span>}
                <span className="flex-1" />
                {certificateId && <span className="text-slate-400">{formatDivingCertShort(certificateId)}</span>}
                <span>{nrOfDivers === 1 ? "1 participant" : `${nrOfDivers} participants`}</span>
              </div>
            );
          })}
        </div>
      </ParamLink>
      {panelIsOn && (
        <Fragment>
          <ActionForm id={unscheduleFormId} confirmMessage={`This will unschedule ${tripAssignmintIds.length} participants, are you sure?`}>
            {tripAssignmintIds.map((tripAssignmentId) => {
              return (
                <Fragment key={tripAssignmentId}>
                  <RInput table={"trip_assignment"} field={"id"} index={tripAssignmentId} value={tripAssignmentId} />
                  <OperationInput table={"trip_assignment"} index={tripAssignmentId} value={"delete"} />
                </Fragment>
              );
            })}
          </ActionForm>
          <ActionForm
            className={twMerge("space-y-3 rounded-md border border-slate-800 p-2", editIsOn && "border-primary")}
            generateIdentifier
          >
            <RedirectParamsInput
              path={"./"}
              paramState={{
                persist_toggle_activity_panel_id: toggledPanelIds.filter((id) => id !== editIdentifier),
              }}
            />
            {editIsOn && <ActionAlert />}
            <div className="overflow-auto" ref={ref as any}>
              <div
                className="w-full grid text-slate-600 [&_div]:whitespace-nowrap gap-1 relative"
                style={{ gridTemplateColumns: `repeat(${totalSpan}, minmax(1fr, 1fr))` }}
              >
                {memberGroups.map((memberGroup) => {
                  const firstParticipant = memberGroup.items[0];
                  const member = firstParticipant?.trip_assignment?.member;
                  const members = flat(data.establishments.map((establishment) => establishment.members));
                  const selectedMember = members.find((item) => item.id === member?.id);
                  const availability = selectedMember && availabilities[selectedMember.availability.instructor];
                  // const totalColumns =
                  return (
                    <Fragment key={memberGroup.groupKey}>
                      {editIsOn ? (
                        <div style={spanCssProperties(totalSpan)}>
                          <div className="sticky left-0" style={width ? { width: width } : {}}>
                            <MemberSelect
                              role={activitySlug === "diving-course" ? "instructor" : "divemaster"}
                              name={fName("member", "id", memberGroup.groupKey || "none")}
                              establishment_id={establishmentId}
                              defaultValue={member?.id || ""}
                            />
                          </div>
                        </div>
                      ) : isRestDay ? (
                        <Fragment />
                      ) : (
                        <div className="flex flex-wrap items-center gap-2 font-semibold sticky left-0" style={spanCssProperties(totalSpan)}>
                          <div className="sticky left-0 flex flex-wrap gap-2 items-center w-fit">
                            <div className={twMerge("h-5 w-5 rounded-full bg-gray-200", availability && availability.className)} />
                            <span className="sticky">
                              {member ? `${member.name}` : <span className="opacity-40 font-normal">No IS/Guide scheduled</span>}
                            </span>
                          </div>
                        </div>
                      )}

                      <div></div>
                      {participantColumns.map((column) => (
                        <div className=" text-slate-700" key={column}>
                          {column}
                        </div>
                      ))}
                      <div></div>
                      {memberGroup.items.map((participant) => {
                        // return <Fragment key={participant.identifier} />;
                        return (
                          <ParticipantRow
                            key={participant.identifier + editIsOn}
                            editMode={editIsOn}
                            identifier={participant.identifier}
                            rest={isRestDay}
                          />
                        );
                      })}
                    </Fragment>
                  );
                })}
              </div>
            </div>
            {editIsOn && (
              <Fragment>
                <TripSelect
                  name={fName("trip", "id")}
                  establishment_id={establishmentId}
                  defaultValue={trip?.id}
                  defaultActivityLocation={defaultDivingLocation}
                />
                <OperationInput table={"trip"} value={"ignore"} />
              </Fragment>
            )}
            {canEdit && (
              <div className="flex flex-row items-center justify-between gap-3">
                <ParamLink
                  className="link"
                  path={_activity_mutate}
                  paramState={{
                    establishment_id: establishmentId,
                    product_id: product?.id,
                    trip_id: trip?.id,
                  }}
                  // params={(p) => p.set("product_id" satisfies keyof Booking, product?.id || "")}
                >
                  +add booking
                </ParamLink>
                <div className="flex flex-row items-center gap-3">
                  {!editIsOn && tripAssignmintIds.length > 0 && (
                    <SubmitButton className="link aria-busy:loading-dots" form={unscheduleFormId}>
                      unschedule
                    </SubmitButton>
                  )}
                  <ParamLink
                    paramState={{ persist_toggle_activity_panel_id: toggledEditIds }}
                    className={twMerge("link text-primary")}
                    type={"button"}
                  >
                    {editIsOn ? "cancel" : "edit"}
                  </ParamLink>
                  {editIsOn && <SubmitButton className="btn btn-primary">Done</SubmitButton>}
                </div>
              </div>
            )}
          </ActionForm>
        </Fragment>
      )}
    </AnimatingDiv>
  );
};

const Schedule = () => {
  const search = useSearchParams2();
  const context = useAppContext();
  const data = usePlanningDayLoader();

  const { state } = useSearchParams2();
  const participations = data.filteredParticipations;
  return (
    <section className="space-y-3">
      {/*<div>To be scheduled: {tobeScheudlesCount + "d"}</div>*/}
      <DividerWithText height={2} className="text-secondary-tag">
        {state.toggle_trip_templates_panel ? "Trip templates" : "Scheduled trips"}
      </DividerWithText>
      <AnimatingDiv className="space-y-6">
        {data.establishments.map((establishment) => {
          const trips = establishment.trips.filter((trip) => state.toggle_trip_templates_panel === (trip.date === null));
          const tripTemplates = establishment.trips.filter((trip) => trip.date === null);
          const selectedTemplate = tripTemplates.find((template) => template.id === state.toggle_trip_add_panel);
          const toggleTripAddPanel = state.toggle_trip_add_panel === establishment.id || selectedTemplate;
          return (
            <AnimatingDiv key={establishment.id} className="space-y-6">
              {data.allEstablishments.length > 1 && <EstablishmentSeperator establishment={establishment} />}
              {trips.length === 0 && state.toggle_trip_templates_panel && <span className="text-slate-400">No trips templates yet</span>}
              {trips
                .filter((trip) => !search.state.filter_id || search.state.filter_id === trip.id)
                .map((trip) => (
                  <TripCarousel key={trip.id} trip={trip} />
                ))}
              {hadAdminEdit(context, establishment.id) && (
                <Fragment>
                  {toggleTripAddPanel ? (
                    <ActionForm className="space-y-3 rounded-md border border-primary p-2">
                      <RedirectParamsInput path={"./"} paramState={{ toggle_trip_add_panel: null }} />
                      <ActionAlert />
                      <div className="flex flex-row gap-3 items-center">
                        <h1 className="text-xl" id={addTripPanelTitleId}>
                          Add trip {state.toggle_trip_templates_panel && "template"}
                        </h1>
                      </div>
                      {!state.toggle_trip_templates_panel && !!tripTemplates.length && (
                        <div className="flex flex-wrap gap-3 text-xs items-center">
                          {/*{selectedTemplate ? "Using" : "Prefill from template"}*/}
                          Prefill using template
                          {tripTemplates
                            // .filter((trip) => !selectedTemplate || selectedTemplate === trip)
                            .map((trip) => (
                              <ParamLink
                                key={trip.id}
                                paramState={{
                                  toggle_trip_add_panel: selectedTemplate === trip ? establishment.id : trip.id,
                                  boat_id: null,
                                }}
                                aria-selected={selectedTemplate === trip}
                                className={
                                  "p-1 bg-slate-200 text-slate-700 hover:bg-slate-300 hover:text-slate-800 transition-colors rounded-md text-xs aria-selected:bg-slate-600 aria-selected:text-slate-100"
                                }
                              >
                                {trip.activity_location} {trip.boat?.name} ({getTripType(trip.type)?.name})
                              </ParamLink>
                            ))}
                        </div>
                      )}
                      {!state.toggle_trip_templates_panel && (
                        <input type={"hidden"} name={fName("trip", "data.date", 0)} value={data.date.dateParam} />
                      )}
                      <RInput table={"establishment"} field={"id"} index={0} value={establishment.id} />
                      <OperationInput table={"establishment"} index={0} value={"ignore"} />
                      <RInput
                        type={"hidden"}
                        table={"trip"}
                        field={"data.establishment_id"}
                        index={0}
                        value={tableIdRef("establishment")}
                      />
                      <SharedTripFields
                        identifier={0}
                        trip={{ start_time: establishment.default_trip_start_time, ...selectedTemplate }}
                        key={selectedTemplate?.id + ""}
                      />
                      <div className={"flex justify-end gap-3 items-center"}>
                        <ParamLink paramState={{ toggle_trip_add_panel: null }} type={"button"} className=" link whitespace-nowrap">
                          cancel
                        </ParamLink>
                        <SubmitButton className="btn btn-primary">Done</SubmitButton>
                      </div>
                    </ActionForm>
                  ) : (
                    !search.state.filter_id && (
                      <div className="flex justify-end gap-3">
                        <ParamLink className="link" paramState={{ toggle_trip_templates_panel: !state.toggle_trip_templates_panel }}>
                          {state.toggle_trip_templates_panel ? "show day trips" : "templates"}
                        </ParamLink>
                        <ParamLink
                          className=" link whitespace-nowrap font-semibold underline"
                          paramState={{ toggle_trip_add_panel: establishment.id, boat_id: null }}
                        >
                          add trip {state.toggle_trip_templates_panel && "template"}
                        </ParamLink>
                      </div>
                    )
                  )}
                </Fragment>
              )}
            </AnimatingDiv>
          );
        })}
        {data.establishments
          .filter((establishment) => !participations.find((participant) => participant.establishment.id === establishment.id))
          .map((establishment) => {
            return (
              <div key={establishment.id}>
                {data.allEstablishments.length > 1 && <EstablishmentSeperator establishment={establishment} />}
                {hadAdminEdit(context, establishment.id) && (
                  <div className="flex justify-end">
                    <ParamLink
                      path={_activity_mutate}
                      paramState={{ establishment_id: establishment.id }}
                      className="link font-semibold underline flex flex-row gap-1 items-center"
                    >
                      <PlusCircleIcon className="w-5 h-5" />
                      <span>Create booking</span>
                    </ParamLink>
                  </div>
                )}
              </div>
            );
          })}
        {myGroupBy2(participations, (participations) => participations.base_group)
          .sort((a, b) => a.groupKey.localeCompare(b.groupKey))
          .map((group, groupIndex) => (
            <div key={group.groupKey} className="space-y-3" id={"divider" + group.groupKey.slice(0, 1)}>
              <DividerWithText height={2} className="text-secondary-tag">
                {group.groupKey.slice(2)}
              </DividerWithText>
              {myGroupBy2(group.items, (participant) => participant.establishment.id).map((establishmentGroup) => {
                const establishment = establishmentGroup?.items[0]?.establishment!;
                const trips = establishmentGroup.items;
                //   .map(item => {
                //   const trip = tripGroup.items[0]?.trip;
                //   const tripIndex = data.allEstablishments
                // });
                return (
                  <AnimatingDiv key={establishment.id} className="space-y-6">
                    {data.allEstablishments.length > 1 && <EstablishmentSeperator establishment={establishment} />}
                    {myGroupBy2(trips, (participant) => participant?.trip_assignment?.trip?.id || "").map((tripGroup) => {
                      const trip = tripGroup.items[0]?.trip_assignment?.trip;
                      return (
                        <AnimatingDiv className="space-y-2" key={tripGroup.groupKey}>
                          {trip && (
                            <div className="flex flex-row gap-3 items-center">
                              <span className="text-xl font-semibold">{trip.activity_location}</span>
                              <span>{trip.boat_name || getTripType(trip.type)?.name}</span>
                            </div>
                          )}
                          {myGroupBy2(tripGroup.items, (participation) => {
                            const activity = participation.activity;
                            return activity?.product_id + "" + activity?.duration_in_days + "-" + activity?.day_number;
                          }).map((productGroup) => (
                            <ProductGroupPanel
                              key={productGroup.groupKey}
                              participantIdentifiers={productGroup.items.map((participant) => participant.identifier)}
                            />
                          ))}
                        </AnimatingDiv>
                      );
                    })}
                    {hadAdminEdit(context, establishment.id) && groupIndex === 0 && (
                      <div className="flex justify-end">
                        <ParamLink
                          path={_activity_mutate}
                          paramState={{
                            establishment_id: establishment.id,
                          }}
                          className="link font-semibold underline flex flex-row gap-1 items-center"
                        >
                          <PlusCircleIcon className="w-5 h-5" />
                          <span>Create booking</span>
                        </ParamLink>
                      </div>
                    )}
                  </AnimatingDiv>
                );
              })}
            </div>
          ))}
      </AnimatingDiv>
    </section>
  );
};

export default function Page() {
  const search = useSearchParams2();
  const response = useLoaderData<typeof loader>();
  usePageRefresh(20, response);
  if (search.state.persist_debug) {
    console.log("planning._index.tsx data", response);
  }
  return (
    <div className="max-lg:px-3 space-y-5">
      <DividerWithText height={2} className="text-secondary-tag">
        Available today
      </DividerWithText>
      <PlanningTabs />
      <Schedule />
      <BoatCreateDialog />
    </div>
  );
}
