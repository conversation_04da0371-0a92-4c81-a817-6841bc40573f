import { RIn<PERSON>, <PERSON><PERSON><PERSON><PERSON>, RSelect } from "~/components/ResourceInputs";
import { ProductItem } from "~/domain/product/ProductItem";
import { ActionForm, defaultEqualCheck } from "~/components/form/BaseFrom";
import { DefaultSaveInner, SubmitButton } from "~/components/base/Button";
import { ActionAlert } from "~/components/ActionAlert";
import { _product_detail, _product_mutate } from "~/misc/paths";
import { activities } from "~/domain/activity/activity";
import React from "react";
import { ParamLink } from "~/components/meta/CustomComponents";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { AnimatingDiv } from "~/components/base/base";
import { paramsToRecord, toggleArray } from "~/misc/parsers/global-state-parsers";
import { LoaderFunctionArgs } from "@remix-run/router";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { at_infinity_value } from "~/kysely/db-static-vars";
import {
  divingCoursesJsonEb,
  divingLevelsJsonEb,
  divingLocationsJsonEb,
  orderProduct,
  pricesJsonEb,
  productsWithOnePriceQb,
  selectMaxDurationInHours,
} from "~/domain/product/product-queries.server";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { memberIsAdminOrOwnerQb } from "~/domain/member/member-queries.server";
import { getSessionSimple } from "~/utils/session.server";
import { notFoundOrUnauthorzied } from "~/misc/responses";
import { arrayAgg } from "~/kysely/kysely-helpers";
import { getCategoryLabel, useProductLists } from "~/domain/product/product-categories";
import { myGroupBy2 } from "~/misc/helpers";
import { useAppContext } from "~/hooks/use-app-context";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { fileTargetsQb } from "~/domain/file/file-resource";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const ctx = await getSessionSimple(request);
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  // const filteredActivitySlugs = activitySlugs.filter((slug) => activities[slug].retail === state.retail);

  const allowedMembersQb = memberIsAdminOrOwnerQb({ trx: kysely, ctx: ctx }, "read");

  const result = await simpleEstablishmentQb
    .where("establishment.id", "=", state.persist_establishment_id)
    .where("establishment.id", "in", allowedMembersQb.select("_member.establishment_id"))
    .select((eb) => [
      // jsonArrayFrom(eb.selectFrom("category").where("category.establishment_id", "=", eb.ref("establishment.id")).selectAll("category")).as(
      //   "categories",
      // ),
      jsonArrayFrom(
        eb
          .selectFrom("form")
          .where("form.deleted_at", "=", at_infinity_value)
          .where((eb) => eb.or([eb("form.establishment_id", "=", eb.ref("establishment.id")), eb("form.establishment_id", "is", null)]))
          .selectAll("form"),
      ).as("forms"),
      jsonArrayFrom(eb.selectFrom("tag").selectAll("tag").where("tag.establishment_id", "=", eb.ref("establishment.id"))).as("tags"),
      jsonArrayFrom(
        productsWithOnePriceQb
          .selectAll("item")
          .selectAll("product")
          .select((eb) => {
            return [
              divingCoursesJsonEb,
              divingLocationsJsonEb,
              divingLevelsJsonEb,
              selectMaxDurationInHours,
              pricesJsonEb,
              jsonArrayFrom(fileTargetsQb(kysely, "product", eb.ref("product.id"))).as("files"),
              eb
                .selectFrom("product__tag")
                .where("product__tag.product_id", "=", eb.ref("product.id"))
                .select((eb) => arrayAgg(eb.ref("product__tag.tag_id"), "uuid").as("tag_ids"))
                .as("tag_ids"),
            ];
          })
          .where("product.deleted_at", "=", at_infinity_value)
          .where("item.establishment_id", "=", eb.ref("establishment.id"))
          .$call((eb) => orderProduct(eb)),
      ).as("products"),
    ])
    .executeTakeFirst();

  if (!result) throw notFoundOrUnauthorzied("Not authorized or establishment was not found");

  return { establishment: result };
};

const productTypes = ["Activities", "Retail"];

export default function Page() {
  const ctx = useAppContext();
  const response = useLoaderData<typeof loader>();
  const search = useSearchParams2();

  const lists = useProductLists(response);
  // console.log("alugs", lists.activity_slugs);

  return (
    <div className="app-container space-y-6">
      <h2 className="text-xl font-semibold">Product list</h2>
      {ctx.editor && false && (
        <div className="flex flex-row items-center">
          {productTypes.map((productType, index) => (
            <ParamLink
              className="btn btn-basic aria-selected:bg-primary aria-selected:text-white text-center md:min-h-16 flex-1 rounded-none "
              key={productType}
              paramState={{ retail: !!index, activity_slug: null, categories: [] }}
              aria-selected={search.state.retail === !!index}
            >
              {productType}
            </ParamLink>
          ))}
        </div>
      )}
      {lists.filtered_product_types.length > 1 && (
        <div className="grid grid-cols-3 md:grid-cols-6 gap-3">
          {lists.filtered_product_types.map((slug) => (
            <ParamLink
              key={slug}
              paramState={{ activity_slug: search.state.activity_slug === slug ? undefined : slug, categories: [] }}
              aria-selected={slug === search.state.activity_slug}
              className="btn btn-basic aria-selected:bg-primary aria-selected:text-white text-center md:min-h-16"
            >
              {activities[slug].name}
            </ParamLink>
          ))}
        </div>
      )}
      {lists.mainTags.length > 0 && (
        <div className="flex flex-wrap gap-3">
          {lists.mainTags.map((tag) => (
            <ParamLink
              key={tag.id}
              paramState={{ main_tags: toggleArray(search.state.main_tags, tag.id) }}
              aria-selected={search.state.main_tags.includes(tag.id)}
              className="btn btn-basic aria-selected:bg-primary aria-selected:text-white text-center md:min-h-16"
            >
              {tag.name}
            </ParamLink>
          ))}
        </div>
      )}
      <div>
        <div className="">
          <div className="flex flex-wrap gap-3">
            {lists.filtered_product_type_categories
              .filter((cat) => getCategoryLabel(cat))
              .map((category) => (
                <ParamLink
                  key={category}
                  paramState={{ categories: toggleArray(search.state.categories, category) }}
                  aria-selected={search.state.categories.includes(category)}
                  className="btn btn-basic w-fit py-1 md:py-1.5 aria-selected:bg-primary aria-selected:text-white text-center capitalize text-sm font-normal"
                >
                  {getCategoryLabel(category)}
                </ParamLink>
              ))}
            {lists.tags.map((tag) => (
              <ParamLink
                key={tag.id}
                paramState={{ tags: toggleArray(search.state.tags, tag.id) }}
                aria-selected={search.state.tags.includes(tag.id)}
                className="btn btn-basic border py-1 md:py-1.5 border-slate-400 w-fit aria-selected:bg-secondary-tag aria-selected:text-white text-sm font-normal text-center "
              >
                {tag.name}
              </ParamLink>
            ))}
          </div>
        </div>
      </div>
      <ActionForm className="space-y-3 group" onCheckEqual={defaultEqualCheck} key={search.state.rerender + ""}>
        <div className="sticky z-10 top-0 justify-end flex flex-wrap items-center gap-3 bg-white py-3">
          <ParamLink paramState={{ rerender: search.state.rerender + 1, response_form_id: undefined }}>Cancel</ParamLink>
          <RedirectParamsInput path={"./"} paramState={{ rerender: search.state.rerender + 1 }} />
          <SubmitButton className="btn btn-primary">
            <DefaultSaveInner />
          </SubmitButton>
        </div>
        <ActionAlert scrollTo />
        <div className="space-y-4">
          {lists.filtered_product_types
            .filter((activitSlug) => !search.state.activity_slug || search.state.activity_slug === activitSlug)
            .map((activitySlug) => {
              const activity = activities[activitySlug];
              const products = lists.filtered.filter((product) => product.activity_slug === activitySlug);
              // if (products.length === 0) return <Fragment key={activitySlug} />;
              // const isToggled = !!search.state.persist_toggle_activity_slug.find((slug) => slug === activitySlug);
              // const newParams = isToggled
              //   ? search.state.persist_toggle_activity_slug.filter((slug) => slug !== activitySlug)
              //   : [...search.state.persist_toggle_activity_slug, activitySlug];
              return (
                <div key={activitySlug}>
                  <div className="py-3  flex flex-row gap-3 items-center text-slate-600 hover:text-slate-700 aria-current:text-slate-800">
                    <span className="font-bold text-xl">
                      {activity.name} ({products.length})
                    </span>
                    <ParamLink
                      path={_product_mutate}
                      className="link"
                      paramState={{
                        establishment_id: response.establishment.establishment_id,
                        activity_slug: activitySlug,
                      }}
                    >
                      Create
                    </ParamLink>
                    {/*<ChevronDownIcon className={twMerge("transition-transform w-7 h-7", !isToggled && "rotate-180")} />*/}
                  </div>
                  <AnimatingDiv className="grid grid-cols-1 gap-6 sm:grid-cols-[repeat(2,1fr)]">
                    {products.map((product) => {
                      const firstPrice = product.product_prices[0];
                      return (
                        <div key={product.id}>
                          <div className="overflow-hidden rounded-md border border-secondary-stroke">
                            {/*{product.duration_in_hours}*/}
                            {/*{product.duration_in_hours_max === null ? "null" : "nonull"}*/}
                            <ProductItem
                              item={product}
                              locale={response.establishment.locale}
                              link={{ path: _product_detail(product.id) }}
                              to_currency={firstPrice?.currency_id}
                            />
                            <div className="p-3">
                              <RInput table={"item"} field={"id"} value={product.item_id} index={product.id} />
                              <div className="space-y-3">
                                <div className="">
                                  <RLabel
                                    table={"item"}
                                    field={"data.form_root_id"}
                                    index={product.id}
                                    className="whitespace-nowrap text-xs"
                                  >
                                    Registration form
                                  </RLabel>
                                  <RSelect
                                    className="select w-full"
                                    table={"item"}
                                    field={"data.form_root_id"}
                                    index={product.id}
                                    defaultValue={product.form_root_id || ""}
                                  >
                                    <option value={""}>None</option>
                                    {myGroupBy2(response.establishment.forms, (form) => form.establishment_id || "").map((group) => (
                                      <optgroup key={group.groupKey} label={group.groupKey ? "Establishment" : "Default"}>
                                        {group.items.map((item) => (
                                          <option key={item.id} value={item.root_id}>
                                            {item.name}
                                          </option>
                                        ))}
                                      </optgroup>
                                    ))}
                                  </RSelect>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </AnimatingDiv>
                </div>
              );
            })}
        </div>
      </ActionForm>
    </div>
  );
}
