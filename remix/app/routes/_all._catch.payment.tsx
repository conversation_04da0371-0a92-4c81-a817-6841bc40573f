import { useLoaderData } from "@remix-run/react";
import { LoaderFunctionArgs } from "@remix-run/router";
import { ParamLink } from "~/components/meta/CustomComponents";
import { pageLimits, Paging } from "~/components/Paging";
import { useAppContext } from "~/hooks/use-app-context";
import { isEditorQb, memberHasPermission, memberIsAdminQb, toArgs } from "~/domain/member/member-queries.server";
import { kysely } from "~/misc/database.server";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { _booking_detail } from "~/misc/paths";
import { getSessionSimple } from "~/utils/session.server";
import { EstablishmentLayout } from "~/components/AllowedForEstablishment";
import { formatDatetime, sqid } from "~/kysely/kysely-helpers";
import { sql } from "kysely";
import { twMerge } from "tailwind-merge";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { MoneyValue } from "~/components/field/MoneyValue";
import { notFoundOrUnauthorzied } from "~/misc/responses";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);
  const { session_id } = await getSessionSimple(request);

  const pageLimit = pageLimits.find((limit) => limit === state.page_limit) || pageLimits[0];

  if (state.persist_establishment_id) {
    const targetEstablishments = await kysely
      .selectFrom("establishment")
      .where("establishment.id", "=", state.persist_establishment_id)
      .where("establishment.id", "in", memberHasPermission(toArgs(kysely, session_id), "payments").select("_member.establishment_id"))
      .executeTakeFirst();

    if (!targetEstablishments) {
      throw notFoundOrUnauthorzied();
    }
  } else {
    const isEditor = await isEditorQb(toArgs(kysely, session_id)).executeTakeFirst();
    if (!isEditor) {
      throw notFoundOrUnauthorzied();
    }
  }

  const paymentsQuery = kysely
    .selectFrom("payment")
    .innerJoin("booking", "booking.id", "payment.booking_id")
    .innerJoin("establishment", "establishment.id", "booking.establishment_id")
    .innerJoin("operator", "operator.id", "establishment.operator_id")
    .innerJoin("spot", "spot.id", "establishment.spot_id")
    .innerJoin("region", "region.id", "spot.region_id")
    .innerJoin("payment_method", "payment_method.id", "payment.payment_method_id")
    .leftJoin("currency", "currency.id", "payment.payment_currency")
    .where("payment.deleted_at", "=", at_infinity_value)
    .where("booking.cancelled_at", "is", null)
    .where("booking.cart_for_session_id", "is", null)
    .$if(!!state.persist_establishment_id, (eb) => eb.where("establishment.id", "=", state.persist_establishment_id))
    .select((eb) => [
      "payment.id",
      "payment.amount",
      sqid(eb.ref("booking.id_seq")).as("booking_sqid"),
      "payment.payment_amount",
      "payment.payment_currency",
      "payment.surcharge_percentage",
      "payment.payed_at",
      "payment.created_at",
      "payment.url",
      "operator.name as operator_name",
      "booking.booking_reference as booking_reference",
      "booking.id as booking_id",
      "booking.currency_id as booking_currency",
      "payment_method.name as payment_method_name",
      "payment_method.short as payment_method_short",
      formatDatetime(sql.ref("payment.created_at"), "YYYY-MM-DD HH24:MI", sql.ref("region.timezone")).as("created_at_formatted"),
      formatDatetime(sql.ref("payment.payed_at"), "YYYY-MM-DD HH24:MI", sql.ref("region.timezone")).as("payed_at_formatted"),
      "establishment.locale as establishment_locale",
    ])
    .orderBy("payment.created_at", "desc");

  const payments = await paymentsQuery
    .offset(state.page_nr * pageLimit)
    .limit(pageLimit)
    .execute();

  const totalResult = await paymentsQuery
    .clearSelect()
    .clearOrderBy()
    .select((eb) => eb.fn.count("payment.id").as("count"))
    .executeTakeFirstOrThrow();

  const totalCount = Number(totalResult.count);

  return {
    payments,
    totalCount,
    pageLimit,
  };
};

export default function PaymentsPage() {
  const { payments, totalCount, pageLimit } = useLoaderData<typeof loader>();
  const { establishment } = useAppContext();

  return (
    <EstablishmentLayout title={<h1 className="text-xl font-semibold mb-4">Payments</h1>}>
      <div className="space-y-4">
        <div className="overflow-auto">
          <div
            className="grid min-w-96 gap-4 text-sm"
            style={{ gridTemplateColumns: `repeat(${establishment ? 8 : 9}, minmax(auto, 1fr))` }}
          >
            <div className="font-semibold contents">
              {!establishment && <div>Establishment</div>}
              <div className="col-span-2 whitespace-nowrap">Booking/Sale</div>
              <div className="whitespace-nowrap">Payment Method</div>
              <div className="whitespace-nowrap">Amount</div>
              <div className="whitespace-nowrap">Surcharge</div>
              <div className="whitespace-nowrap">Total</div>
              <div className="whitespace-nowrap">Status</div>
              <div className="whitespace-nowrap">Date</div>
            </div>
            {payments.map((payment) => {
              const surchargeAmount = (payment.amount * payment.surcharge_percentage) / 100;
              const totalAmount = payment.amount + surchargeAmount;
              const isPaid = !!payment.payed_at;
              const displayAmount = payment.payment_amount || payment.amount;
              const displayCurrency = payment.payment_currency || payment.booking_currency;

              return (
                <div key={payment.id} className="contents">
                  {!establishment && <div>{payment.operator_name}</div>}
                  <div className="whitespace-nowrap">
                    <ParamLink path={_booking_detail(payment.booking_id)} className="link whitespace-nowrap">
                      {payment.booking_sqid}
                    </ParamLink>
                  </div>
                  <div>
                    <ParamLink path={_booking_detail(payment.booking_id)} className="link whitespace-nowrap">
                      {payment.booking_reference}
                    </ParamLink>
                  </div>
                  <div className="whitespace-nowrap">
                    <span>{payment.payment_method_short || payment.payment_method_name}</span>
                  </div>
                  <div className="whitespace-nowrap">
                    <MoneyValue
                      nativeAmount={payment.amount}
                      nativeCurrency={displayCurrency}
                      toCurrency={displayCurrency}
                      locale={payment.establishment_locale}
                    />
                  </div>
                  <div className="whitespace-nowrap">
                    <MoneyValue
                      nativeAmount={surchargeAmount}
                      nativeCurrency={displayCurrency}
                      toCurrency={displayCurrency}
                      locale={payment.establishment_locale}
                    />
                  </div>
                  <div className="font-semibold whitespace-nowrap">
                    <MoneyValue
                      nativeAmount={totalAmount}
                      nativeCurrency={displayCurrency}
                      toCurrency={displayCurrency}
                      locale={payment.establishment_locale}
                    />
                  </div>
                  <div className="whitespace-nowrap">
                    <span
                      className={twMerge(
                        "px-2 py-1 text-xs rounded-full",
                        isPaid ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800",
                      )}
                    >
                      {isPaid ? "Paid" : "Pending"}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 whitespace-nowrap">
                    {payment.payed_at_formatted || payment.created_at_formatted}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        <Paging totalCount={totalCount} pageLimit={pageLimit} />
      </div>
    </EstablishmentLayout>
  );
}
