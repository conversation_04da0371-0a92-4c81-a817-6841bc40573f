import type { DataFunctionArgs } from "@remix-run/server-runtime";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import React from "react";
import { _establishment_detail } from "~/misc/paths";
import { DeleteButtonForm, RInput } from "~/components/ResourceInputs";
import { ActionForm } from "~/components/form/BaseFrom";
import { SubmitButton } from "~/components/base/Button";
import { twMerge } from "tailwind-merge";
import { ParamLink } from "~/components/meta/CustomComponents";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: DataFunctionArgs) => {
  const reviewId = params.review_id!;
  const operatorLocationId = params.id!;
  return Promise.all([
    kysely
      .selectFrom("review")
      .innerJoin("establishment", "establishment.id", "review.establishment_id")
      .innerJoin("operator", "operator.id", "establishment.operator_id")
      .where("review.id", "=", reviewId)
      .where("review.establishment_id", "=", operatorLocationId)
      .selectAll("review")
      .select(["operator.name as operator_name"])
      .executeTakeFirstOrThrow(),
  ]);
};

export default function Page() {
  const [review] = useLoaderData<typeof loader>();

  return (
    <div className="app-container space-y-3">
      <div>
        Back to{" "}
        <ParamLink className="link" path={_establishment_detail(review.establishment_id)}>
          {review.operator_name}
        </ParamLink>
      </div>
      <div className="flex flex-wrap items-center gap-2">
        <h1 className="text-xl font-bold">Review {review.id}</h1>
        <ParamLink path={"edit"} className="link">
          edit
        </ParamLink>
      </div>
      <p>For operator {review.operator_name}</p>
      <DeleteButtonForm table={"review"} values={[review.id]} redirect={_establishment_detail(review.establishment_id)} />
      <ActionForm className="inline-block">
        <RInput table={"review"} field={"id"} value={review.id} type={"hidden"} />
        <RInput table={"review"} field={"data.published_at"} value={review.published_at ? "" : "true"} type={"hidden"} />
        <SubmitButton className={twMerge("aria-busy:loading-dots", review.published_at ? "text-red-500" : "text-green-600")}>
          {review.published_at ? "Unpublish" : "Publish"}
        </SubmitButton>
      </ActionForm>
    </div>
  );
}
