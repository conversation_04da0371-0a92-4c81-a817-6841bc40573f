import { Trans } from "~/components/Trans";
import type { MetaFunction } from "@remix-run/react";
import { useLoaderData } from "@remix-run/react";
import { t } from "~/misc/trans";
import { AccountContainer } from "~/components/account/AccountContainer";
import { kysely } from "~/misc/database.server";
import { getSessionSimple } from "~/utils/session.server";
import { IkImage } from "~/components/IkImage";
import { IoLocationSharp } from "react-icons/io5";
import React, { Fragment } from "react";
import { _establishment_detail } from "~/misc/paths";
import { traveltrusterDescription } from "~/misc/consts";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { memberQb } from "~/domain/member/member-queries.server";
import { useAppContext } from "~/hooks/use-app-context";
import { LoaderFunctionArgs } from "@remix-run/router";
import { ParamLink } from "~/components/meta/CustomComponents";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const ctx = await getSessionSimple(request);

  const establishments = await memberQb({ trx: kysely, ctx })
    .innerJoin("establishment", "establishment.id", "_member.establishment_id")
    .innerJoin("operator", "operator.id", "establishment.operator_id")
    .leftJoin("spot", "spot.id", "establishment.spot_id")
    .selectAll("establishment")
    .select((eb) => [
      "operator.name as operator_name",
      "spot.name as spot_name",
      jsonArrayFrom(fileTargetsQb(kysely, "establishment", eb.ref("establishment.id"))).as("files"),
    ])
    .execute();

  return {
    establishments: establishments,
  };
};

export const meta: MetaFunction = () => [
  {
    title: t`Manage`,
  },
  { description: traveltrusterDescription },
  { "og:description": traveltrusterDescription },
];

export default function Page() {
  const session = useAppContext();
  const response = useLoaderData<typeof loader>();
  const myEstablishmentAssociations = response.establishments || [];

  if (!session.verified_at)
    return (
      <div className="app-container py-3">
        <AccountContainer />
      </div>
    );

  return (
    <div className="app-container space-y-3 py-3">
      <h1 className={"text-3xl font-bold"}>
        <Trans>Manage</Trans>
      </h1>
      {myEstablishmentAssociations.length > 0 && (
        <Fragment>
          <div className="grid gap-6 md:grid-cols-2">
            {myEstablishmentAssociations.map((item) => {
              const locationName = item.location_name || item.spot_name;
              return (
                <ParamLink
                  path={_establishment_detail(item.id)}
                  key={item.id}
                  className="block w-full overflow-hidden rounded-xl shadow-md hover:opacity-80 active:opacity-80"
                >
                  <div className="h-[150px] overscroll-none">
                    <IkImage path={item.files?.[0]?.filename} w={540} h={150} className="h-[150px] w-full object-cover" />
                  </div>
                  <div className="flex-col p-3">
                    <div className="flex flex-row justify-between space-x-1">
                      <h4 className="text-xl font-bold line-clamp-1">
                        {item.operator_name}
                        {!!locationName && " - " + locationName}
                      </h4>
                      <div className="flex flex-row items-center">
                        <IoLocationSharp />
                        <span className={"max-w-[100px] line-clamp-1"}>{item.spot_name}</span>
                      </div>
                    </div>
                    {item.address && <span className={"block text-xs line-clamp-1"}>{item.address}</span>}
                  </div>
                </ParamLink>
              );
            })}
          </div>
          <div className="py-3">
            <hr />
          </div>
        </Fragment>
      )}
    </div>
  );
}
