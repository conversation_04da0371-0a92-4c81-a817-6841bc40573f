import type { DataFunctionArgs } from "@remix-run/server-runtime";
import { useLoaderData, useParams } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { RatingInput } from "~/components/field/rating/rating-input";
import { FeedbackPointsInput } from "~/domain/review/review-components";
import React from "react";
import { _establishment_detail } from "~/misc/paths";
import { activeActivitySlugs, activities } from "~/domain/activity/activity";
import { InputFilesDefault } from "~/components/form/FilesInput";
import { fName } from "~/misc/helpers";
import { RInput, RLabel, RSelect, RTextarea } from "~/components/ResourceInputs";
import { RedirectInput } from "~/components/form/DefaultInput";
import { SubmitButton } from "~/components/base/Button";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { ActionForm } from "~/components/form/BaseFrom";
import { ActionAlert } from "~/components/ActionAlert";
import { ParamLink } from "~/components/meta/CustomComponents";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ params }: DataFunctionArgs) => {
  const reviewId = params.review_id!;
  const operatorLocationId = params.id!;
  return kysely
    .selectFrom("review")
    .innerJoin("establishment", "establishment.id", "review.establishment_id")
    .innerJoin("operator", "operator.id", "establishment.operator_id")
    .where("review.id", "=", reviewId)
    .where("review.establishment_id", "=", operatorLocationId)
    .selectAll("review")
    .select((eb) => ["operator.name as operator_name", jsonArrayFrom(fileTargetsQb(kysely, "review", eb.ref("review.id"))).as("files")])
    .executeTakeFirstOrThrow();
};

export default function Page() {
  const params = useParams();
  const operatorLocationId = params.id!;
  const review = useLoaderData<typeof loader>();

  return (
    <div className="app-container space-y-3">
      <div>
        Back to <ParamLink path={_establishment_detail(review.establishment_id)}>{review.operator_name}</ParamLink>
      </div>
      <h1 className="text-xl font-bold">Edit review</h1>
      <p>For operator {review.operator_name}</p>
      <ActionForm className="space-y-5">
        <ActionAlert />
        <RInput table={"review"} field={"id"} value={review.id} />
        <RedirectInput value={_establishment_detail(operatorLocationId)} />
        <RSelect table={"review"} field={"data.activity_slug"} defaultValue={review.activity_slug} className="select">
          <option value="">Select activity</option>
          {activeActivitySlugs.map((slug) => {
            const activity = activities[slug];
            return (
              <option key={slug} value={slug}>
                {activity.name}
              </option>
            );
          })}
        </RSelect>
        <RatingInput name={fName("review", "data.experience_rating")} required defaultValue={review.experience_rating} />
        <div className="flex flex-col gap-6 md:flex-row">
          <div className="md:w-1/2">
            <FeedbackPointsInput type={"plus"} defaultValue={review.positives || []} />
          </div>
          <div className="md:w-1/2">
            <FeedbackPointsInput type={"min"} defaultValue={review.negatives || []} />
          </div>
        </div>
        <div>
          <div className="space-y-2">
            <RLabel table={"review"} field={"data.experience"}>
              Tell us more about your experience
            </RLabel>
            <RTextarea table={"review"} field={"data.experience"} className="input" defaultValue={review.experience || ""} />
          </div>
          <div className="space-y-2">
            <label>Photos</label>
            <InputFilesDefault target={"review"} target_id={review.id} multiple defaultValue={review.files} />
          </div>
        </div>
        <SubmitButton className="btn btn-primary">Save</SubmitButton>
      </ActionForm>
    </div>
  );
}
