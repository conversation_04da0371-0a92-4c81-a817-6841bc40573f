import type { AsyncReturnType } from "type-fest";
import { MetaFunction, Outlet, useLoaderData, useLocation, useNavigate } from "@remix-run/react";
import { AnimatingDiv, BlueBox, SectionHeading } from "~/components/base/base";
import type { ReactNode } from "react";
import React, { Fragment } from "react";
import { ParamLink } from "~/components/meta/CustomComponents";
import { t } from "~/misc/trans";
import { Trans } from "~/components/Trans";
import { ImageGallery } from "~/components/field/ImageGallery";
import { getProductTitle } from "~/domain/product/ProductItem";
import { divingCoursesJsonEb, pricesJsonEb } from "~/domain/product/product-queries.server";
import { AccomodationIcon, DiverIcon, DurationIcon, SnorkellerIcon, TransportIcon } from "~/components/Icons";
import { BlueTag } from "~/components/base/Tag";
import { formatRange, getRangeObject } from "~/components/field/duration_in_hours/duration_in_hours";
import { sql } from "kysely";
import { kysely } from "~/misc/database.server";
import { MoneyValue } from "~/components/field/MoneyValue";
import { EstablishmentItem } from "~/domain/establishment/EstablishmentItem";
import { ReadmoreBox } from "~/components/ReadmoreBox";
import { _activity_mutate, _establishment_detail, _product_detail, _product_history, _product_mutate } from "~/misc/paths";
import { GearIcon } from "~/domain/product/ProductTags";
import { formatAddonText } from "~/domain/addon/addon";
import { createPageOverwrites, traveltrusterName } from "~/misc/consts";
import { createImageUrl } from "~/components/IkImage";
import { capitalize } from "~/utils/formatters";
import { RInput } from "~/components/ResourceInputs";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { ActionForm } from "~/components/form/BaseFrom";
import { SubmitButton } from "~/components/base/Button";
import { RedirectInput } from "~/components/form/DefaultInput";
import { notFound } from "~/misc/responses";
import { useAppContext } from "~/hooks/use-app-context";
import { divingLevelsz, divingOrganizationszz } from "~/domain/diving-course/diving-courses.data";
import { LoaderFunctionArgs } from "@remix-run/router";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { defaultCurrency } from "~/misc/vars";
import { useCurrency } from "~/domain/currency/use-currency";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { BiChevronLeft } from "react-icons/bi";
import { getAppContextInMeta } from "~/misc/route-helpers";
import { robotsMeta } from "~/misc/web-helpers";
import { notNull } from "~/kysely/kysely-helpers";
import { twMerge } from "tailwind-merge";

export { action } from "~/routes/_all._catch.resource";

interface IdName {
  id: string;
  name: string;
}

interface NestedIdName extends IdName {
  items: IdName[];
}

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const id = params.id!;

  const product = await kysely
    .selectFrom("product")
    .innerJoin("item", "item.id", "product.item_id")
    .selectAll("item")
    .selectAll("product")
    .select((productEb) => [
      productEb
        .selectFrom("form")
        .where("form.root_id", "=", productEb.ref("item.form_root_id"))
        .where("form.deleted_at", "=", at_infinity_value)
        .select("form.id")
        .as("form_id"),
      divingCoursesJsonEb,
      pricesJsonEb,
      jsonArrayFrom(fileTargetsQb(kysely, "product", productEb.ref("product.id"))).as("files"),
      productEb
        .exists(productEb.selectFrom("sale_item").whereRef("sale_item.product_id", "=", "product.id").select("sale_item.id"))
        .as("has_activity"),
      productEb
        .exists(productEb.selectFrom("inquiry").whereRef("inquiry.product_id", "=", "product.id").select("inquiry.id"))
        .as("has_inquiry"),
      jsonArrayFrom(
        productEb
          .selectFrom("product__diving_location")
          .innerJoin("diving_location", "diving_location.id", "product__diving_location.diving_location_id")
          .where("product__diving_location.product_id", "=", productEb.ref("product.id"))
          .select((eb) => [
            "diving_location.id",
            "diving_location.name",
            jsonArrayFrom(
              eb
                .selectFrom("diving_site")
                .innerJoin("product__diving_site", "product__diving_site.diving_site_id", "diving_site.id")
                .where("product__diving_site.product_id", "=", productEb.ref("product.id"))
                .where("diving_site.diving_location_id", "=", eb.ref("diving_location.id"))
                .select(["diving_site.id", "diving_site.name"]),
            ).as("items"),
          ])
          .orderBy(["diving_location.name", "diving_location.id"]),
        // .orderBy("diving_location.name")
        // .orderBy("diving_location.id"),
      ).as("diving_locations"),
      jsonArrayFrom(
        productEb
          .selectFrom("addon")
          .where("addon.establishment_id", "=", productEb.ref("item.establishment_id"))
          .where("addon.id", "=", productEb.fn.any(productEb.ref("item.addon_ids")))
          .selectAll("addon")
          .select((productEb) =>
            notNull(
              jsonObjectFrom(
                productEb.selectFrom("price").select(["price.amount", "price.currency_id"]).whereRef("price.id", "=", "addon.price_id"),
              ),
            ).as("price"),
          ),
      ).as("addons"),
      jsonObjectFrom(
        simpleEstablishmentQb
          .where("establishment.id", "=", productEb.ref("item.establishment_id"))
          .select((eb) => [
            "establishment.id",
            "establishment.bio",
            "establishment.about",
            "establishment.workflow",
            "establishment.direct_booking_mode",
            eb
              .selectFrom("form")
              .where("form.root_id", "=", eb.ref("establishment.direct_booking_form_root_id"))
              .where("form.deleted_at", "=", at_infinity_value)
              .select("form.id")
              .as("direct_booking_form_id"),
            sql<any>`(ST_AsGeoJSON(${eb.ref("establishment.geom")})::jsonb)`.as("geom"),
            jsonArrayFrom(fileTargetsQb(kysely, "establishment", eb.ref("establishment.id"))).as("files"),
          ]),
      ).as("establishment"),
    ])
    // .where("product.id", "=", id)
    .where((eb) =>
      eb.or([eb("product.id", "=", id), eb.and([eb("product.deleted_at", "=", at_infinity_value), eb("product.root_id", "=", id)])]),
    )
    // .where((eb) =>
    //   record.exact ? eb("product.id", "=", id) : eb.and([eb("product.deleted_at", "=", at_infinity_value), eb("product.root_id", "=", id)]),
    // )
    .orderBy("product.created_at desc")
    .limit(1)
    .executeTakeFirst();

  if (!product) throw notFound("product not found");
  const establishment = product.establishment;
  if (!establishment) throw notFound("operator not found");

  product.diving_locations.map((dl) => dl.name);

  return {
    item: product,
    establishment: {
      ...establishment,
      title: capitalize(establishment.operator_name + " - " + (establishment?.spot_name || "")),
    },
    ...createPageOverwrites({
      base_currency: establishment.default_currency || defaultCurrency,
      show_currency_swap: true,
      show_whatsapp: true,
      whatsapp_fixed: false,
    }),
  };
};

export type LoaderResponse = AsyncReturnType<typeof loader>;

export const meta: MetaFunction<typeof loader> = (args) => {
  const parentData = getAppContextInMeta(args);
  const config = parentData?.env;
  const bucketUrl = config?.firebase_singapore.storageBucket;
  const data = args.data;
  const imagePath = data?.establishment.files?.[0]?.filename;
  return [
    robotsMeta(parentData?.operator ? "noindex" : "index"),
    { title: data?.establishment?.title || traveltrusterName },
    { description: data?.establishment.bio },
    { "og:description": data?.establishment.bio || data?.establishment.about },
    bucketUrl ? { "og:image": imagePath && createImageUrl(bucketUrl, imagePath, 300, 200) } : {},
  ];
};

const GridTable = (props: { children?: ReactNode }) => (
  <div>
    <div
      className="grid grid-cols-2 gap-3 rounded border border-secondary-stroke bg-secondary-50 p-3"
      style={{
        gridTemplateColumns: "auto 1fr",
      }}
      {...props}
    />
  </div>
);

const GridLabel = (props: { children?: ReactNode }) => <div className={"text-gray-600 first-letter:capitalize"} {...props} />;

const NestedTagsBlock = (props: { items: NestedIdName[] }) => (
  <div className={"flex flex-col space-y-1"}>
    {props.items.map((item) => (
      <div className={"flex flex-col space-y-2"} key={item.id}>
        <span className={"font-bold"}>{item.name}</span>
        {item.items.length > 0 && (
          <div className={"flex flex-wrap gap-2"}>
            {item.items.map((item) => (
              <BlueTag key={item.id}>{item.name}</BlueTag>
            ))}
          </div>
        )}
      </div>
    ))}
  </div>
);

export default function Page() {
  const search = useSearchParams2();
  const { item, establishment } = useLoaderData<typeof loader>();
  const firstPrice = item.product_prices[0];
  const appContext = useAppContext();
  const navigate = useNavigate();
  const { finalSelected } = useCurrency();
  const location = useLocation();

  const divingCertificateLevels = Object.entries(divingLevelsz).filter(([levelKey, level]) =>
    item.diving_courses.map((dc) => dc.diving_certificate_level_key).includes(levelKey),
  );
  const divingCertificateOrgs = Object.entries(divingOrganizationszz)
    .filter(([key]) => item.diving_courses.map((dc) => dc.diving_certificate_organization_key).includes(key))
    .map(([orgKey, org]) => ({
      id: orgKey,
      name: org.name,
    }));

  const or = (...fields: Array<keyof typeof item>) => {
    return !!fields.find((field) => !!item[field]);
  };
  const price = firstPrice && (
    <MoneyValue
      nativeAmount={firstPrice.amount}
      nativeCurrency={firstPrice.currency_id}
      toCurrency={finalSelected}
      locale={establishment.locale}
    />
  );
  const productFiles = item.files || [];
  const establishmentFiles = establishment.files || [];
  const finalFiles = productFiles.length ? productFiles : establishmentFiles;

  const isHistoryPage = location.pathname.endsWith("history");
  return (
    <div className="space-y-1 py-3">
      <div className="app-container space-y-1 pb-3">
        {item.deleted_at !== at_infinity_value && (
          <div className="bg-slate-200 p-3 rounded-md mt-3 mb-10">This product is not active anymore</div>
        )}
        <div className={twMerge(item.deleted_at !== at_infinity_value && "opacity-50")}>
          <ImageGallery fileNames={finalFiles.map((file) => file.filename)} />
        </div>
        <div className={"flex flex-col space-y-3"}>
          {appContext.members.find((member) => member.establishment_id === item.establishment_id && member.admin > 1) &&
            item.deleted_at === at_infinity_value && (
              <div className="flex flex-wrap gap-2 pt-1">
                <ParamLink
                  className="link inline-flex items-center"
                  onClick={(e) => {
                    if (window.history.length > 0) {
                      e.preventDefault();
                      navigate(-1);
                    }
                  }}
                  path={search.state.previous_path || search.state.persist_previous_path || _establishment_detail(item.establishment_id)}
                >
                  <BiChevronLeft />
                  Back
                </ParamLink>
                <span>|</span>
                <ParamLink path={_product_detail(item.id)} className="link" preventScrollReset end>
                  Show
                </ParamLink>
                {establishment.workflow > 1 && (
                  <Fragment>
                    <span>|</span>
                    <ParamLink
                      paramState={{
                        product_id: item.id,
                        establishment_id: item.establishment_id,
                      }}
                      path={_activity_mutate}
                      className="link"
                      preventScrollReset
                    >
                      Create activity
                    </ParamLink>
                  </Fragment>
                )}
                <span>|</span>
                {appContext.editor && (
                  <Fragment>
                    <ParamLink path={_product_history(item.id)} className="link" preventScrollReset>
                      History
                    </ParamLink>
                    <span>|</span>
                  </Fragment>
                )}
                <ParamLink className="link" path={_product_mutate} paramState={{ id: item.id }}>
                  <Trans>Edit</Trans>
                </ParamLink>
                <span>|</span>
                <ParamLink className="link" path={_product_mutate} paramState={{ id: item.id, action_type: "copy" }}>
                  <Trans>Copy</Trans>
                </ParamLink>
                <span>|</span>
                <ActionForm confirmMessage={"Are you sure?"}>
                  <RedirectInput value={_establishment_detail(item.establishment_id)} />
                  <RInput table={"product"} field={"id"} value={item.id} />
                  <RInput table={"product"} field={"data.deleted_at"} value={"delete"} type={"hidden"} />
                  <SubmitButton className="disabled:loading-dots text-red-500 hover:underline">delete</SubmitButton>
                </ActionForm>
                <div>has activity: {item.has_activity ? "yes" : "no"}</div>
              </div>
            )}
          <div className="flex flex-row gap-1">
            <h1 id={"product-title"} className="scroll-mt-[10px] text-2xl font-bold first-letter:capitalize">
              {getProductTitle(item)}
            </h1>
            <div className={"flex-1"} />
            <div className={"hidden text-xl font-bold lg:block"}>{price}</div>
          </div>
          {item.subtitle && <p className={"text-gray-500"}>{item.subtitle}</p>}
          {isHistoryPage && <Outlet />}
          <div className={"flex flex-col space-y-6"}>
            <div>
              <BlueBox
                className={"grid grid-cols-2 gap-3"}
                style={{
                  gridTemplateColumns: "auto 1fr",
                }}
              >
                {!!item.n_sessions && (
                  <Fragment>
                    <div className="flex content-center items-center justify-center rounded bg-secondary-tag p-1 text-center text-white">
                      <SnorkellerIcon className={"block h-[16px] w-auto"} />
                    </div>
                    <div>{item.n_sessions === 1 ? t`1 session` : item.n_sessions + " sessions"}</div>
                  </Fragment>
                )}
                {!!item.diving_count && (
                  <Fragment>
                    <div className="flex content-center items-center justify-center rounded bg-secondary-tag p-1 text-center text-white">
                      <DiverIcon className={"block h-[16px] w-auto"} />
                    </div>
                    <div>{item.diving_count === 1 ? t`1 dive` : item.diving_count + " dives"}</div>
                  </Fragment>
                )}
                {!!item.duration_in_hours && (
                  <Fragment>
                    <div className="flex content-center items-center justify-center rounded bg-secondary-tag p-1 text-center text-white">
                      <DurationIcon className={"block h-[16px] w-auto"} />
                    </div>
                    <div>{formatRange(getRangeObject(item.duration_in_hours))}</div>
                  </Fragment>
                )}
                {!!item.stay && (
                  <Fragment>
                    <div className="flex content-center items-center justify-center rounded bg-secondary-tag p-1 text-center text-white">
                      <AccomodationIcon className={"block h-[16px] w-auto"} />
                    </div>
                    <div>{item.stay_info || t`Stay`}</div>
                  </Fragment>
                )}
                {!!item.gear_included && (
                  <Fragment>
                    <div className="flex content-center items-center justify-center rounded bg-secondary-tag p-1 text-center text-white">
                      <GearIcon activity_slug={item.activity_slug} className={"block h-[16px] w-auto"} />
                    </div>
                    <div>{t`Gear included`}</div>
                  </Fragment>
                )}
                {!!item.pickup && (
                  <Fragment>
                    <div className="flex content-center items-center justify-center rounded bg-secondary-tag p-1 text-center text-white">
                      <TransportIcon className={"block h-[16px] w-auto"} />
                    </div>
                    <div>{item.pickup_info || t`Pickup`}</div>
                  </Fragment>
                )}
              </BlueBox>
            </div>
            {!!divingCertificateLevels.length && (
              <div className={"flex flex-col space-y-2"}>
                <SectionHeading>Course(s)</SectionHeading>
                <BlueBox>
                  <NestedTagsBlock items={divingCertificateOrgs.map((item) => ({ ...item, items: [] }))} />
                </BlueBox>
              </div>
            )}
            {!!item.diving_locations.length && (
              <div className={"flex flex-col space-y-2"}>
                <SectionHeading>{item.activity_slug === "snorkeling" ? "Snorkel location(s)" : "Diving location(s)"}</SectionHeading>
                <BlueBox className={"flex flex-col space-y-3"}>
                  {item.activity_slug !== "diving-course" && <p className={"text-gray-800"}>{t`Location(s) and optional dive sites:`}</p>}
                  <NestedTagsBlock items={item.diving_locations} />
                  <p className={"italic text-gray-800"}>
                    {t`Please note: Site availability is depending on weather & water conditions. Final discretion will be made by operator.`}
                  </p>
                </BlueBox>
              </div>
            )}
            {or("diving_minimum_logged_dives", "required_diving_certificate", "required_experience") && (
              <div className={"flex flex-col space-y-2"}>
                <SectionHeading>Experience</SectionHeading>
                <GridTable>
                  {item.required_diving_certificate && (
                    <Fragment>
                      <GridLabel>{t`Min. required certificate`}:</GridLabel>
                      <div className={"whitespace-pre-wrap font-bold"}>{item.required_diving_certificate}</div>
                    </Fragment>
                  )}
                  {item.diving_minimum_logged_dives && (
                    <Fragment>
                      <GridLabel>{t`Min. logged dives`}:</GridLabel>
                      <div className={"whitespace-pre-wrap font-bold"}>{item.diving_minimum_logged_dives}</div>
                    </Fragment>
                  )}
                  {item.required_experience && (
                    <div
                      className={"whitespace-pre-wrap"}
                      style={{
                        gridColumnStart: 1,
                        gridColumnEnd: 3,
                      }}
                    >
                      {item.required_experience}
                    </div>
                  )}
                </GridTable>
              </div>
            )}
            <div className={"flex flex-col space-y-2"}>
              <SectionHeading>Details</SectionHeading>
              <GridTable>
                {item.minimum_age && (
                  <Fragment>
                    <GridLabel>{t`Min. age`}:</GridLabel>
                    <div className={"whitespace-pre-wrap font-bold"}>{item.minimum_age}</div>
                  </Fragment>
                )}
                {item.guide_pax_max && (
                  <Fragment>
                    <GridLabel>{t`Max. pax per guide`}:</GridLabel>
                    <div className={"whitespace-pre-wrap font-bold"}>{item.guide_pax_max}</div>
                  </Fragment>
                )}
                {item.guide_pax_min && (
                  <Fragment>
                    <GridLabel>{t`Min. pax to proceed`}:</GridLabel>
                    <div className={"whitespace-pre-wrap font-bold"}>{item.guide_pax_min}</div>
                  </Fragment>
                )}
                {item.cancellation_policy_in_hours && (
                  <Fragment>
                    <GridLabel>{t`cancellation policy`}</GridLabel>
                    <div>{item.cancellation_policy_in_hours} hour free cancellation policy</div>
                  </Fragment>
                )}
              </GridTable>
            </div>
            {!!item.inclusions && (
              <div className={"flex flex-col space-y-2"}>
                <SectionHeading>{t`Inclusions`}</SectionHeading>
                <BlueBox>
                  <ReadmoreBox>{item.inclusions}</ReadmoreBox>
                </BlueBox>
              </div>
            )}
            {!!item.exclusions && (
              <div className={"flex flex-col space-y-2"}>
                <SectionHeading>{t`Exclusions`}</SectionHeading>
                <BlueBox>
                  <ReadmoreBox>{item.exclusions}</ReadmoreBox>
                </BlueBox>
              </div>
            )}
            {!!item.itinerary && (
              <div className={"flex flex-col space-y-2"}>
                <SectionHeading>{t`Itinerary`}</SectionHeading>
                <BlueBox>
                  <ReadmoreBox>{item.itinerary}</ReadmoreBox>
                </BlueBox>
              </div>
            )}
            {(!!item.addons_description || item.addons.length > 0) && (
              <div className={"flex flex-col space-y-2"}>
                <SectionHeading>{t`Add-ons`}</SectionHeading>
                <BlueBox>
                  <ReadmoreBox>
                    {item.addons_description}
                    {item.addons.length > 0 && (
                      <ul>
                        {item.addons.map((addon) => (
                          <li key={addon.id}>- {formatAddonText(appContext, finalSelected, establishment.locale, addon)}</li>
                        ))}
                      </ul>
                    )}
                  </ReadmoreBox>
                </BlueBox>
              </div>
            )}
            {!!item.info && (
              <div className={"flex flex-col space-y-2"}>
                <SectionHeading>{t`Info`}</SectionHeading>
                <BlueBox>
                  <ReadmoreBox>{item.info}</ReadmoreBox>
                </BlueBox>
              </div>
            )}
          </div>
          <div className="flex flex-col space-y-1">
            <SectionHeading>Operated by</SectionHeading>
            <ParamLink
              path={_establishment_detail(establishment.id)}
              persistAllParams
              className="block w-full overflow-hidden rounded bg-secondary-50 hover:opacity-80 active:opacity-80"
            >
              <EstablishmentItem item={establishment} />
            </ParamLink>
          </div>
        </div>
      </div>
      {!isHistoryPage && (
        <div className="sticky bottom-0 flex flex-col justify-center space-y-1 border-t-[1px] border-gray-500 bg-white py-3">
          <div className="app-container pb-2 text-center">{price}</div>
          <AnimatingDiv>
            <Outlet />
          </AnimatingDiv>
        </div>
      )}
    </div>
  );
}
