import { kysely } from "~/misc/database.server";
import { useLoaderData } from "@remix-run/react";
import { AnimatingDiv } from "~/components/base/base";
import { useIkUrl } from "~/components/IkImage";
import React, { Fragment } from "react";
import { countries } from "~/data/countries";
import { ParamLink } from "~/components/meta/CustomComponents";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { RInput, RSelect } from "~/components/ResourceInputs";
import { InputFilesDefault } from "~/components/form/FilesInput";
import { fName, myGroupBy, myGroupBy2, tableIdRef } from "~/misc/helpers";
import { SubmitButton } from "~/components/base/Button";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { ActionForm } from "~/components/form/BaseFrom";
import { LoaderFunctionArgs } from "@remix-run/router";
import { toggleArray } from "~/misc/parsers/global-state-parsers";
import { OperationInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { twMerge } from "tailwind-merge";
import { ActionAlert } from "~/components/ActionAlert";
import { timezones } from "~/data/timezones";
import { BasicCountrySelect } from "~/components/CountrySelect";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  return kysely
    .selectNoFrom((eb) => [
      jsonArrayFrom(
        kysely
          .selectFrom("region")
          .selectAll("region")
          .select((eb) => [
            jsonArrayFrom(fileTargetsQb(kysely, "region", eb.ref("region.id"))).as("files"),
            jsonArrayFrom(
              kysely
                .selectFrom("spot")
                .selectAll("spot")
                .where("spot.region_id", "=", eb.ref("region.id"))
                .select((eb) => jsonArrayFrom(fileTargetsQb(kysely, "spot", eb.ref("spot.id"))).as("files"))
                .orderBy("spot.name"),
            ).as("spots"),
          ])
          .orderBy(["region.published desc", "region.country_code", "region.name"]),
      ).as("regions"),
    ])
    .executeTakeFirstOrThrow();
};

const TimezoneSelect = (props: { name: string; defaultValue?: string; disabled?: boolean }) => {
  return (
    <select className="select max-sm:w-32" defaultValue={props.defaultValue} required name={props.name} disabled={props.disabled}>
      <option value="">Timezone</option>
      {timezones.map((timezone) => (
        <option key={timezone} value={timezone}>
          {timezone}
        </option>
      ))}
    </select>
  );
};

const addKeyword = "new";

export default function Page() {
  // const ctx
  const { state } = useSearchParams2();
  const { fromBucket } = useIkUrl();

  const { regions } = useLoaderData<typeof loader>();

  const addingRegion = state.region_ids.includes(addKeyword);
  return (
    <div className="app-container space-y-3 py-3">
      <h2 className="text-xl font-semibold text-slate-600">Regions</h2>
      <ActionForm className="space-y-3" key={state.rerender}>
        <ActionAlert />
        <RedirectParamsInput path={"./"} paramState={{ region_ids: [] }} />
        <AnimatingDiv style={{ gridTemplateColumns: "minmax(30px, auto) 1fr 1fr auto" }} className="grid items-center gap-2">
          <div>Country</div>
          <div>Timezone</div>
          <div>Name</div>
          <div></div>
          {regions.map((region) => {
            const toBeDeleted = state.region_ids.includes(region.id);
            return (
              <Fragment key={region.id}>
                <div className={twMerge(toBeDeleted && "opacity-60")}>
                  <RInput table={"region"} field={"id"} value={region.id} index={region.id} />
                  {toBeDeleted && <OperationInput table={"region"} index={region.id} value={"delete"} />}
                  <BasicCountrySelect
                    required
                    className="select max-sm:w-32"
                    name={fName("region", "data.country_code", region.id)}
                    defaultValue={region.country_code}
                  />
                </div>
                <div>
                  <TimezoneSelect name={fName("region", "data.timezone", region.id)} defaultValue={region.timezone || ""} />
                </div>
                <div className={twMerge("flex", toBeDeleted && "opacity-60")}>
                  <RInput
                    required
                    className="input flex-1"
                    table={"region"}
                    field={"data.name"}
                    index={region.id}
                    defaultValue={region.name}
                  />
                </div>
                <div className="text-right">
                  <ParamLink
                    className={twMerge("link text-red-500", toBeDeleted && "text-slate-700")}
                    paramState={{ region_ids: toggleArray(state.region_ids, region.id) }}
                  >
                    {toBeDeleted ? "undo" : "delete"}
                  </ParamLink>
                </div>
              </Fragment>
            );
          })}
          <Fragment>
            <div>
              {addingRegion && <BasicCountrySelect required className="select max-sm:w-32" name={fName("region", "data.country_code")} />}
            </div>
            <div>{addingRegion && <TimezoneSelect name={fName("region", "data.timezone")} />}</div>
            <div className="flex">{addingRegion && <RInput required className="input flex-1" table={"region"} field={"data.name"} />}</div>
            <div className="text-right">
              <ParamLink className="link" paramState={{ region_ids: toggleArray(state.region_ids, addKeyword) }}>
                {addingRegion ? "cancel" : "add"}
              </ParamLink>
            </div>
          </Fragment>
        </AnimatingDiv>
        <div className="flex flex-wrap gap-3 justify-end items-center">
          {state.region_ids.length > 0 && <ParamLink paramState={{ region_ids: [], rerender: state.rerender + 1 }}>clear</ParamLink>}
          <SubmitButton className="btn btn-primary">Save</SubmitButton>
        </div>
      </ActionForm>
      <h2 className="text-xl font-semibold text-slate-600">Spots</h2>
      <AnimatingDiv>
        <ParamLink paramState={{ persist_create: !state.persist_create }} className="link">
          {state.persist_create ? "cancel" : "Create spot"}
        </ParamLink>
        {state.persist_create && (
          <ActionForm className="space-y-6 py-3">
            <RedirectParamsInput path={"./"} paramState={{ persist_create: false }} />
            <ActionAlert />
            <InputFilesDefault target={"spot"} target_id={tableIdRef("spot")} />
            <RInput table={"spot"} field={"data.name"} className="input" required placeholder="name" />
            <label>
              <span>Region</span>
              <RSelect table={"spot"} field={"data.region_id"} className="input" required>
                <option value="">select region</option>
                {myGroupBy(regions, (region) => region.country_code).map((country) => (
                  <optgroup key={country.country_code} label={country.country_code}>
                    {country.items.map((region) => (
                      <option key={region.id} value={region.id}>
                        {region.name}
                      </option>
                    ))}
                  </optgroup>
                ))}
              </RSelect>
            </label>
            <div className="flex flex-row items-center">
              <SubmitButton className="btn btn-primary">Create</SubmitButton>
            </div>
          </ActionForm>
        )}
      </AnimatingDiv>
      {myGroupBy2(regions, (region) => region.country_code).map((countryGroup) => {
        const country = countries.find((country) => country.country_code === countryGroup.groupKey);

        return (
          <div key={country?.country_code || "unkown"} className="space-y-3">
            <h3 className="text-2xl font-bold text-slate-700">{country?.country_name || "unkown"}</h3>
            {countryGroup.items.map((region) => {
              return (
                <div key={region.id} className="space-y-3">
                  <h4 className="flex flex-row items-center gap-2 text-xl font-bold text-slate-500">
                    {region.name}{" "}
                    <span className={`inline-block h-2 w-2 rounded-full ${region.published ? "bg-green-500" : "bg-red-500"}`} />
                  </h4>
                  <div className="grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4">
                    {region.spots.map((spot) => (
                      <ParamLink
                        key={spot.id}
                        path={spot.id}
                        className="overflow-hidden rounded-xl bg-slate-100 ring-1 ring-slate-200 transition-opacity hover:opacity-80 active:opacity-80"
                      >
                        <object>
                          <img
                            alt={spot.name}
                            height={10}
                            className="h-16 w-full object-cover"
                            src={fromBucket(spot.files?.[0]?.filename || "", "tr:q-80,h-150,w-240")}
                          />
                        </object>
                        <p className="font- py-2 text-center text-sm">{spot.name}</p>
                      </ParamLink>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        );
      })}
    </div>
  );
}
