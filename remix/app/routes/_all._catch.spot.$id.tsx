import type { DataFunctionArgs } from "@remix-run/server-runtime";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { SubmitButton } from "~/components/base/Button";
import React from "react";
import { sql } from "kysely";
import { getSessionSimple } from "~/utils/session.server";
import { _spot } from "~/misc/paths";
import { RInput } from "~/components/ResourceInputs";
import { RedirectInput } from "~/components/form/DefaultInput";
import { unauthorized } from "~/misc/responses";
import { ActionForm } from "~/components/form/BaseFrom";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import { ParamLink } from "~/components/meta/CustomComponents";

export { ErrorBoundary } from "~/components/RoutDefaults";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: DataFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);
  const user = await activeUserSessionSimple(kysely, session_id, true).select("_user.editor").executeTakeFirst();
  if (!user?.editor) throw unauthorized();
  const spotId = params.id!;
  return Promise.all([
    kysely
      .selectFrom("spot")
      .innerJoin("region", "region.id", "spot.region_id")
      .selectAll("spot")
      .select(["region.name as region_name", "region.country_code", (eb) => sql<any>`(ST_AsGeoJSON(${eb.ref("spot.geom")}))`.as("geom")])
      .where("spot.id", "=", spotId)
      .executeTakeFirstOrThrow(),
    kysely
      .selectFrom("establishment")
      .innerJoin("operator", "operator.id", "establishment.operator_id")
      .where("establishment.spot_id", "=", spotId)
      .select(["establishment.id", "operator.name", "establishment.published"])
      .execute(),
  ]);
};

export default function Page() {
  const [spot, operatorLocations] = useLoaderData<typeof loader>();

  const canDelete = operatorLocations.length === 0;

  return (
    <div className="app-container space-y-3 py-3">
      <div className="flex flex-wrap items-center gap-2">
        <h1 className="text-xl font-bold">
          {spot.name}, {spot.region_name}, {spot.country_code}
        </h1>
        <ParamLink path={"edit"} className="link">
          Edit
        </ParamLink>
      </div>
      <p>
        Total operators: {operatorLocations.length}, published: {operatorLocations.filter((item) => item.published).length}
      </p>
      <ActionForm className="w-fit space-y-3 rounded-md border border-red-500 p-3">
        <RedirectInput value={_spot} />
        <RInput table={"spot"} field={"id"} value={spot.id} />
        {!canDelete && <p>Cannot delete spot as there are operator under it.</p>}
        <SubmitButton disabled={!canDelete} className="btn btn-red">
          Delete
        </SubmitButton>
      </ActionForm>
    </div>
  );
}
