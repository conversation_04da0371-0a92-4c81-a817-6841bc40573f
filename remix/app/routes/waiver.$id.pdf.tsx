import { LoaderFunctionArgs } from "@remix-run/router";
import { generateWaiverPdf } from "~/domain/participant-waiver/generate-waiver-pdf";
import { kysely } from "~/misc/database.server";
import { mergeStateToParams, paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { getSessionSimple } from "~/utils/session.server";
import { allowedParticipantIdsFor } from "~/domain/participant/participant-auth-queries.server";
import { unauthorized } from "~/misc/responses";
import { getFullUrl } from "~/misc/helpers";
import { _waiver_detail } from "~/misc/paths";

export const loader = async (args: LoaderFunctionArgs) => {
  const waiverId = args.params.id!;
  const url = new URL(args.request.url);
  const record = paramsToRecord(url.searchParams);

  if (record.participant_id) {
    const session = await getSessionSimple(args.request);
    const allowed = await allowedParticipantIdsFor(
      {
        trx: kysely,
        request: args.request,
        ctx: { session_id: session.session_id },
      },
      "read",
    )
      .select("_participant.id")
      .executeTakeFirst();
    if (!allowed) throw unauthorized();
  }

  const pdfUrl = new URL(getFullUrl(url.host) + _waiver_detail(waiverId));
  mergeStateToParams(pdfUrl.searchParams, record);

  const result = await generateWaiverPdf({ pdfUrl: pdfUrl });

  return new Response(result.arrayBuffer, {
    status: 200,
    headers: {
      "Content-Type": "application/pdf",
      "Content-Disposition": "inline; filename=customfile.pdf",
    },
  });
};
