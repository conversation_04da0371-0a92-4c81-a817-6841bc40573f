import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import React from "react";
import { LoaderFunctionArgs } from "@remix-run/router";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _retail } from "~/misc/paths";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { ActionForm } from "~/components/form/BaseFrom";
import { RInput, RLabel } from "~/components/ResourceInputs";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { SubmitButton } from "~/components/base/Button";
import { ActionAlert } from "~/components/ActionAlert";
import { Backbutton } from "~/components/base/base";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { InputFilesDefault } from "~/components/form/FilesInput";
import { fName, tableIdRef } from "~/misc/helpers";
import { useAppContext } from "~/hooks/use-app-context";
import { CategorySelect } from "~/domain/category/CategoryComponents";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  const establishment = await kysely
    .selectFrom("category")
    .selectAll("category")
    .select((eb) => jsonArrayFrom(fileTargetsQb(kysely, "category", eb.ref("category.id"))).as("files"))
    .where("category.id", state.id ? "=" : "is", state.id)
    .executeTakeFirst();

  return {
    category: establishment,
  };
};

export default function Page() {
  const appCtx = useAppContext();
  const { category } = useLoaderData<typeof loader>();

  return (
    <div className="app-container space-y-6">
      <div className="flex items-center gap-4">
        <Backbutton className="link">Back</Backbutton>
        <h2 className="text-xl font-semibold">{category ? "Edit Category" : "Create Category"}</h2>
      </div>

      <div className="max-w-2xl">
        <ActionForm className="space-y-4">
          <ActionAlert scrollTo />
          <RedirectParamsInput path={_retail} paramState={{}} />

          {/* Hidden establishment ID field */}
          {!category && (
            <RInput table="category" field="data.establishment_id" value={appCtx.establishment?.establishment_id} type="hidden" />
          )}

          {/* If editing, include the category ID */}
          {category && <RInput table="category" field="id" value={category.id} type="hidden" />}

          {/* Category name field */}
          <div className="form-control">
            <RLabel table="category" field="data.name">
              Category Name
            </RLabel>
            <RInput table="category" field="data.name" className="input w-full" defaultValue={category?.name || ""} required />
          </div>

          {/* Parent category selection */}
          {/*<div className="form-control">*/}
          {/*  <RLabel table="category" field="data.parent_category_id">*/}
          {/*    Parent Category*/}
          {/*  </RLabel>*/}
          {/*  <RSelect*/}
          {/*    table="category"*/}
          {/*    field="data.parent_category_id"*/}
          {/*    className="select w-full"*/}
          {/*    defaultValue={category?.parent_category_id || ""}*/}
          {/*  >*/}
          {/*    <option value="">-- No Parent (Root Category) --</option>*/}
          {/*    {establishment.categories*/}
          {/*      .filter((cat) => !cat.parent_category_id)*/}
          {/*      .map((cat) => (*/}
          {/*        <ParentCategoryOptions key={cat.id} category={cat} prefixNames={[]} />*/}
          {/*      ))}*/}
          {/*  </RSelect>*/}
          {/*  <p className="text-sm text-gray-500 mt-1">Select a parent category to create a hierarchical structure</p>*/}
          {/*</div>*/}
          <CategorySelect
            name={fName("category", "data.parent_category_id")}
            defaultValue={category?.parent_category_id}
            parent
            disabledCategoryId={category?.id}
          />
          <InputFilesDefault target_id={tableIdRef("category")} target={"category"} defaultValue={category?.files} />

          <div className="flex justify-end gap-3 pt-4">
            <ParamLink path={_retail} className="btn btn-outline">
              Cancel
            </ParamLink>
            <SubmitButton className="btn btn-primary">{category ? "Update" : "Create"}</SubmitButton>
          </div>
        </ActionForm>
      </div>
    </div>
  );
}
