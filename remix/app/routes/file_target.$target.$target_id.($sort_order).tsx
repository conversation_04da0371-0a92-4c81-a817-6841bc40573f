import { LoaderFunctionArgs } from "@remix-run/router";
import { kysely } from "~/misc/database.server";
import { _file_download } from "~/misc/paths";
import { redirect } from "@remix-run/server-runtime";
import { notFound } from "~/misc/responses";
import { paramsToRecord, StateInputKey } from "~/misc/parsers/global-state-parsers";

const maxRetryCount = 11;
const waitBeforeRetryInMs = 4000;

function delay(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export const loader = async (args: LoaderFunctionArgs) => {
  const target = args.params.target!;
  const targetId = args.params.target_id!;
  const sortOrder = args.params.sort_order;
  const record = paramsToRecord(new URL(args.request.url).searchParams);
  const token = record.persist_token;

  for (let retryCount = 0; retryCount < maxRetryCount; retryCount++) {
    console.log("try", retryCount, target);
    const fileTarget = await kysely
      .selectFrom("file_target")
      .innerJoin("file", "file.id", "file_target.file_id")
      .select("file.filename")
      .where("file_target.target", "=", target)
      .where("file_target.target_id", "=", targetId)
      // .where("file_target.sort_order", "=", (sortOrder && Number(sortOrder)) || 0)
      .orderBy("file_target.sort_order asc")
      .limit(1)
      .executeTakeFirst();

    if (fileTarget) {
      throw redirect(
        _file_download(fileTarget.filename) + (token ? "?" + ("persist_token" satisfies StateInputKey) + "=" + token : ""),
        303,
      );
    }
    await delay(waitBeforeRetryInMs);
  }

  throw notFound("File does not exist or is still generating");
};
