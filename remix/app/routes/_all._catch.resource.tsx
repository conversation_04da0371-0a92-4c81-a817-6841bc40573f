import { json, redirect } from "@remix-run/server-runtime";
import { kysely } from "~/misc/database.server";
import { executeActionAndCreateAuditInput } from "~/domain/table-action/table-action";
import { v4 } from "uuid";
import { formdataToNestedJson } from "~/misc/formdata-to-nested-json";
import { entries, keys, logTime } from "~/misc/helpers";
import { identifier<PERSON>ey, redirectError<PERSON>ey, redirect<PERSON>ey, responseIdentifier<PERSON>ey } from "~/misc/vars";
import type { Operation } from "~/components/form/DefaultInput";
import { Error<PERSON>ey, findErrorMessage, getErrorMsgFromCatch } from "~/misc/error-translations";
import { ResourceError } from "~/utils/error";
import { resources } from "~/server/resource/resource.server";
import { getOrCreateSession } from "~/utils/session.server";
import type { DB } from "~/kysely/db";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import type { InsertObject } from "kysely";
import { ActionFunctionArgs } from "@remix-run/node";
import { mergeStateToParams, paramsToRecord, StateInputKey } from "~/misc/parsers/global-state-parsers";
import { getAllowRedirect } from "~/utils/request.server";
import { appConfig } from "~/config/config.server";
import { getIpAddress } from "~/misc/route-helpers";
import { AfterMutations, AuthArgs } from "~/server/resource/resource-helpers.server";
import { jsonToStructuredArray } from "~/misc/json-to-structured-array";
import { AuditInsert } from "~/server/resource/resource.types";

type ResourceResult = {
  id: string;
  entity_name: keyof DB;
  operation: Operation;
  result: string | boolean;
  auditInsert?: AuditInsert | null;
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const formdata = await request.formData();
  const originUrl = new URL(request.url);

  const captcha_token = formdata.get("captcha_token");
  const formStartDateRaw = formdata.get("start");
  const honeyPotWebsite = formdata.get("website");

  let captchaScore = 0;
  if (typeof captcha_token === "string" && captcha_token && appConfig.GOOGLE_RECAPTCHA_SECRET_KEY) {
    const response = await fetch(`https://www.google.com/recaptcha/api/siteverify`, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        secret: appConfig.GOOGLE_RECAPTCHA_SECRET_KEY,
        response: captcha_token,
      }).toString(),
    });
    const body = await response.json();

    if (typeof body.score === "number") {
      captchaScore = body.score;
    } else {
      console.error("invalid captcha score", captcha_token, body);
    }
  }

  if (typeof formStartDateRaw === "string") {
    const formStartDate = new Date(formStartDateRaw);
    const now = new Date();
    if (now.getTime() - formStartDate.getTime() < 3000) {
      console.error("Bot prevention: Start date must be at least 3 seconds before the current time.");
      throw new Error("server error");
    }
  }
  if (honeyPotWebsite) {
    console.error("Bot prevention: website should not be filled");
    throw new Error("server error");
  }

  const afterMutations: AfterMutations = { insideTransaction: new Map(), outsideTransaction: new Map() };

  const allowRedirect = getAllowRedirect(request);

  const responseObj: Record<string, string | null> = {};
  Array.from(formdata.entries()).forEach(([key, value]) => {
    if (typeof value === "string") {
      responseObj[key] = value;
    }
  });
  const rawInput = formdataToNestedJson(formdata);

  const getFinalUrl = (path: FormDataEntryValue | null) => {
    if (typeof path !== "string") return new URL(originUrl);
    if (path.startsWith("http")) return new URL(path);
    return new URL(originUrl.protocol + "//" + (originUrl.host + "/" + path.replace("./", originUrl.pathname)).replace("//", "/"));
  };

  const errorUrl = getFinalUrl(formdata.get(redirectErrorKey));
  const successUrl = getFinalUrl(formdata.get(redirectKey));

  const formIdentifierValue = formdata.get(identifierKey);
  const responseIdentifierValue = formdata.get(responseIdentifierKey);
  const finalFormValue = formIdentifierValue || responseIdentifierValue;

  const refIds: Record<string, string | null> = {};

  const finalResponseIdentifier = typeof responseIdentifierValue === "string" ? responseIdentifierValue : null;

  const callbacks: { id: string; delay_in_seconds: number }[] = [];

  // if (environment === "dev") {
  //   await new Promise((resolve) => setTimeout(resolve, 2000));
  // }

  const resourceEntriesFromRawInput = jsonToStructuredArray(rawInput).map((args) => {
    const tableArgs = resources[args.tableName];
    if (!tableArgs) throw new ResourceError(`resource ${args.tableName} is not supported`);
    return {
      ...args,
      args: tableArgs,
    };
  });

  const { session_id, init } = await getOrCreateSession(request);
  const context = { session_id: session_id };

  try {
    const logger = logTime("resource");
    const result = await kysely.transaction().execute(async (trx) => {
      const resourceEntriesDeletesReversedIndex = resourceEntriesFromRawInput
        .filter((r) => r.operation === "delete")
        .sort((a, b) => b.refId.localeCompare(a.refId));

      const resourceEntriesOthers = resourceEntriesFromRawInput
        .filter((r) => r.operation !== "delete")
        .sort((a, b) => a.refId.localeCompare(b.refId));

      const resourceEntries = [...resourceEntriesOthers, ...resourceEntriesDeletesReversedIndex];

      let userSessionG: { id: string } | null = null;
      const getOrCreateUserSession = async () => {
        if (userSessionG) return userSessionG;
        const userSession = await activeUserSessionSimple(trx, context.session_id, false).select("_user_session.id").executeTakeFirst();
        if (userSession) {
          userSessionG = userSession;
          return userSession;
        }
        const newAnonymousUserSession = await trx
          .insertInto("user_session")
          .values({ session_id: session_id })
          .onConflict((oc) => oc.doNothing())
          .returning("user_session.id")
          .executeTakeFirstOrThrow();
        userSessionG = newAnonymousUserSession;
        return newAnonymousUserSession;
      };

      const getFinalValue = (rawValue: any) =>
        typeof rawValue === "string" && keys(refIds).includes(rawValue) ? refIds[rawValue] : rawValue;

      const resourcePromises: Array<{
        promise: () => Promise<ResourceResult | null>;
      }> = resourceEntries.map((resourceEntry) => {
        const tableName = resourceEntry.tableName;
        const input = resourceEntry.args;
        const baseMsg = `${resourceEntry.operation} on ${resourceEntry.tableName}`;
        const unauthorizedMsg = `unauthorized for ${baseMsg}`;

        return {
          ...resourceEntry,
          promise: async () => {
            // map refIds to ids for the datafields
            const data = resourceEntry.data as any;
            if (data && typeof data === "object") {
              Object.entries(data).forEach(([key, value]) => {
                data[key] = value instanceof Array ? value.map((arrayValue) => getFinalValue(arrayValue)) : getFinalValue(value);
              });
            }

            const beforeMutate = resourceEntry.args.beforeMutate;
            if (beforeMutate) {
              logger.timeLog("before mutate " + resourceEntry.tableName);
              const newIdAction = await beforeMutate({
                ...resourceEntry,
                getExistingOrCreateAnonymousUserSession: getOrCreateUserSession,
                ctx: context,
                captcha_score: captchaScore,
                trx: trx,
                formData: formdata,
                request: request,
              });
              logger.timeLog("finish");
              resourceEntry.id = newIdAction.id;
              resourceEntry.operation = newIdAction.operation;
              if (newIdAction.data) {
                resourceEntry.data = newIdAction.data;
              }
            } else if (resourceEntry.operation === "insert" && !resourceEntry.id) {
              resourceEntry.id = v4();
            }

            refIds[resourceEntry.refId] = resourceEntry.id || null;

            entries(refIds).forEach(([refId, id]) => {
              if (id && refIds[id]) {
                refIds[refId] = refIds[id] || null;
              }
            });

            if (!resourceEntry.id || resourceEntry.operation === "ignore") return null;

            const fnArgs: AuthArgs = {
              ctx: context,
              getExistingOrCreateAnonymousUserSession: getOrCreateUserSession,
              captcha_score: captchaScore,
              trx: trx,
              id: resourceEntry.id,
              trigger: "before",
              action: resourceEntry.operation,
              formData: formdata,
              request: request,
              after_mutations: afterMutations,
            };

            // before update or delete
            if (resourceEntry.operation === "update" || resourceEntry.operation === "delete") {
              const authorized = await input.authorize(fnArgs);
              if (!authorized) throw new ResourceError(unauthorizedMsg);
            }

            const getFinalData = async () => {
              if (resourceEntry.operation === "insert") {
                const data = await input.insert({ ...fnArgs, data: resourceEntry.data! });
                if (!data) throw new ResourceError(unauthorizedMsg);
                return data;
              }
              if (resourceEntry.operation === "update") {
                const data = await input.update({ ...fnArgs, data: resourceEntry.data! });
                if (!data) throw new ResourceError(unauthorizedMsg);
                const dataValues = Object.values(data);
                const undefinedValues = dataValues.filter((value) => value === undefined);
                if (dataValues.length === 0 || undefinedValues.length === dataValues.length) {
                  throw new ResourceError(`no values or unauthorized for ${baseMsg}`);
                }
                return data;
              }
              if (resourceEntry.operation === "delete") {
                const data = await input.delete({ ...fnArgs, data: resourceEntry.data });
                if (!data) throw new ResourceError(unauthorizedMsg);
                return null;
              }
              throw new ResourceError(`operation ${baseMsg} not supported`);
            };

            logger.timeLog("getData" + resourceEntry.tableName + " - " + resourceEntry.refId);
            const finalData = await getFinalData();
            logger.timeLog("finish");

            logger.timeLog("execute and audit " + resourceEntry.tableName);
            const auditInput = await executeActionAndCreateAuditInput(trx, {
              entity_name: tableName,
              entity_id: resourceEntry.id,
              data: finalData,
              operation: resourceEntry.operation,
            });
            logger.timeLog("finish");

            if (auditInput && input.onChanged) {
              const result = await input.onChanged({ ...fnArgs, diff: auditInput });
              if (result !== true) throw new ResourceError(result || unauthorizedMsg);
            }

            // after insert or update
            if (resourceEntry.operation === "insert" || resourceEntry.operation === "update") {
              const fnArgsAfter: AuthArgs = { ...fnArgs, trigger: "after" };
              const authorized = await input.authorize(fnArgsAfter);
              if (!authorized) throw new ResourceError(unauthorizedMsg);
            }

            if (tableName === "callback" && resourceEntry.operation === "insert") {
              callbacks.push({ id: resourceEntry.id, delay_in_seconds: Number(finalData?.delay_in_seconds || 5) });
            }

            const disableAudit = await resourceEntry.args.disableAudit?.(fnArgs);

            const auditInsert =
              !disableAudit && auditInput
                ? ({
                    data: auditInput.auditInput,
                    entity_name: tableName,
                    entity_id: resourceEntry.id,
                    action_name: resourceEntry.operation,
                  } satisfies Omit<InsertObject<DB, "entity_action">, "user_event_id">)
                : null;

            return {
              id: resourceEntry.id,
              entity_name: tableName,
              operation: resourceEntry.operation,
              result: auditInput ? resourceEntry.id : false,
              auditInsert: auditInsert,
            } as const;
          },
        };
      });

      const results: ResourceResult[] = [];
      for (const resourcePromise of resourcePromises) {
        const resourceResult = await resourcePromise.promise();
        if (resourceResult) {
          results.push(resourceResult);
        }
      }

      const auditInputs = results
        .map((result) => result.auditInsert)
        .filter((auditInsert): auditInsert is NonNullable<typeof auditInsert> => !!auditInsert);

      const insideTransactionEntries = Array.from(afterMutations.insideTransaction.entries()).sort(([aKey], [bKey]) =>
        aKey.localeCompare(bKey),
      );
      for (const [afterKey, afterFunc] of insideTransactionEntries) {
        const timer = logTime("after insideTransaction" + afterKey);
        const auditInserts = await afterFunc();
        if (auditInserts) {
          auditInputs.push(...auditInserts);
        }
        timer.end();
      }

      // create auditInput
      if (auditInputs.length > 0) {
        const userSession = await getOrCreateUserSession();
        const ipAddress = getIpAddress(request);
        const headerForwardedFor =
          request.headers
            .get("x-forwarded-for")
            ?.split(",")
            .map((str) => str.trim()) || null;

        const remoteAddress = (request as any).connection?.remoteAddress || null;

        const event = await trx
          .insertInto("user_event")
          .values({
            connection_remote_address: remoteAddress,
            header_x_forwarded_for: headerForwardedFor,
            ip_address: ipAddress,
            user_session_id: userSession.id,
          })
          .returningAll()
          .executeTakeFirstOrThrow();

        // create audit inputs for user event
        await trx
          .insertInto("entity_action")
          .values(
            auditInputs.map((auditInput) => ({
              ...auditInput as any,
              user_event_id: event.id,
            })),
          )
          .execute();
      }

      return {
        init: init,
        results: results,
        // callbacks: createdCallbacks,
      };
    });
    logger.end();
    const actionHasHappended = !!result.results.find((result) => result.result);

    if (!actionHasHappended) {
      throw new ResourceError("no_changes" satisfies ErrorKey);
    }

    for (const [afterKey, afterFunc] of Array.from(afterMutations.outsideTransaction.entries())) {
      const logger = logTime("after outsideTransaction " + afterKey);
      await afterFunc();
      logger.end();
    }

    const replaceFormdataValues = (value: string) => {
      let newValue = value;
      formdata.forEach((value, key) => {
        if (typeof value === "string") {
          newValue = newValue.replace(key, value);
        }
      });
      return newValue;
    };

    const replaceIdsRefs = (value: string) => {
      let newValue = value;
      entries(refIds).forEach(([key, value]) => {
        newValue = newValue.replace(key, value || "");
      });
      return newValue;
    };

    const record = paramsToRecord(successUrl.searchParams);
    // Object.entries(record).forEach(([key, value]) => {
    //   record[key as StateInputKey] =
    //     typeof value === "string"
    //       ? replaceIdsRefs(value)
    //       : value instanceof Array
    //       ? value.map((v) => (typeof v === "string" ? replaceIdsRefs(v) : v))
    //       : value;
    // });
    const elementActions = record.element_action.map(replaceFormdataValues).map(replaceIdsRefs);
    const elementClears = record.element_clear.map(replaceFormdataValues).map(replaceIdsRefs);
    const persistDate = record.persist_date;
    const replacedDate = persistDate && formdata.get(persistDate);
    const formSuccessId = formdata.get("form_success_id" satisfies StateInputKey);

    mergeStateToParams(successUrl.searchParams, {
      booking_id: record.booking_id ? replaceIdsRefs(record.booking_id) : record.booking_id,
      boat_id: record.boat_id ? replaceIdsRefs(record.boat_id) : record.boat_id,
      persist_date: typeof replacedDate === "string" ? replacedDate : persistDate,
      toggle_sale_item_ids: record.toggle_sale_item_ids.map(replaceIdsRefs),
      modal_detail_id: record.modal_detail_id && replaceIdsRefs(record.modal_detail_id),
      id: record.id && replaceIdsRefs(record.id),
      view_id: record.view_id ? replaceIdsRefs(record.view_id) : record.view_id,
      response_identifier: finalResponseIdentifier,
      response_form_id: typeof finalFormValue === "string" ? finalFormValue : null,
      response_error: null,
      form_success_id: typeof formSuccessId === "string" ? formSuccessId : null,
      element_action: elementActions,
      element_clear: elementClears,
    });

    successUrl.pathname = replaceIdsRefs(successUrl.pathname);
    console.log("finalurl", successUrl.toString());
    return allowRedirect ? redirect(successUrl.toString(), result.init) : json(paramsToRecord(successUrl.searchParams), result.init);
  } catch (e) {
    console.error("error on resource", e);
    const msg = getErrorMsgFromCatch(e);

    const errorMsg = e instanceof ResourceError ? e.message : typeof msg === "string" ? findErrorMessage(msg) && msg : null;
    if (errorMsg) {
      mergeStateToParams(errorUrl.searchParams, {
        response_identifier: finalResponseIdentifier,
        response_form_id: typeof finalFormValue === "string" ? finalFormValue : null,
        response_error: errorMsg,
        element_action: [],
      });

      return allowRedirect ? redirect(errorUrl.toString(), init) : json(paramsToRecord(errorUrl.searchParams), init);
    } else {
      throw e;
    }
  }
};
