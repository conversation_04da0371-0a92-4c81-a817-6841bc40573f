import { Outlet } from "@remix-run/react";
import { _diving_location_detail } from "~/misc/paths";
import React, { Fragment } from "react";
import { useIkUrl } from "~/components/IkImage";
import { useDivingLocationAndSites } from "~/routes/_all._catch.diving-location.$diving_location_id";
import { IoLocationSharp } from "react-icons/io5";
import { ParamLink } from "~/components/meta/CustomComponents";

export { action } from "~/routes/_all._catch.resource";

export default function Page() {
  const { fromBucket } = useIkUrl();
  const { site, location } = useDivingLocationAndSites();

  if (!site) return <Fragment />;

  const files = site.files || [];
  const heroUrl = files[0] && fromBucket(files[0]?.filename, "tr:q-80,h-500,w-1980,c-at_least");

  return (
    <div className="space-y-3">
      <div className="bg-cover bg-center object-fill" style={{ backgroundImage: `url(${heroUrl})` }}>
        <div className="image-text flex h-80 w-full content-center items-center ">
          <div className="container flex h-auto max-w-6xl flex-wrap items-center justify-between text-white">
            <div className="space-y-1">
              <h1 className="text-2xl font-semibold tracking-wide md:text-5xl">{site.name}</h1>
              <p className="">{site.headline}</p>
            </div>
            <ParamLink
              path={_diving_location_detail(site.diving_location_id)}
              className="text-md flex flex-row items-center gap-1 md:text-xl"
            >
              <IoLocationSharp className="hero-svg-shadow h-7 w-7 md:h-8" />
              <span>{location.name}</span>
            </ParamLink>
          </div>
        </div>
      </div>
      <Outlet />
    </div>
  );
}
