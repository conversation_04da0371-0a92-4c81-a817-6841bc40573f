import { kysely } from "~/misc/database.server";
import { unauthorized } from "~/misc/responses";
import { ActionFunctionArgs } from "@remix-run/node";
import { LoaderFunctionArgs } from "@remix-run/router";
import { executeAction } from "~/domain/table-action/table-action";

export const action = async (args: ActionFunctionArgs) => {
  const bankAccountResponse = await args.request.json();
  const ownerId = bankAccountResponse.owner_id;
  const callbackToken = args.request.headers.get("x-callback-token");
  const externalId = bankAccountResponse.external_id;

  if (!callbackToken || !externalId || !ownerId) throw unauthorized();

  return kysely.transaction().execute(async (trx) => {
    const xenditAccount = await trx
      .selectFrom("xendit_account")
      .where("xendit_account.xendit_user_id", "=", ownerId)
      .where("xendit_account.xendit_invoice_callback_token", "=", callbackToken)
      .selectAll("xendit_account")
      .executeTakeFirstOrThrow();

    const virtualAccount = await trx
      .selectFrom("xendit_virtual_bank_account")
      .where("xendit_virtual_bank_account.id", "=", externalId)
      .executeTakeFirst();

    await executeAction(trx, {
      entity_name: "xendit_virtual_bank_account",
      entity_id: externalId,
      data: {
        name: bankAccountResponse.name,
        xendit_account_id: xenditAccount.id,
        xendit_virtual_bank_account_id: bankAccountResponse.id,
        account_number: bankAccountResponse.account_number,
        bank_code: bankAccountResponse.bank_code,
        obj: bankAccountResponse,
      },
      operation: virtualAccount ? "update" : "insert",
    });
    return { success: true };
  });
};

export const loader = async (args: LoaderFunctionArgs) => {
  return { success: true };
};
