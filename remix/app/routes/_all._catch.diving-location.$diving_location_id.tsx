import type { AsyncReturnType } from "type-fest";
import { kysely } from "~/misc/database.server";
import { Outlet, useParams } from "@remix-run/react";
import React from "react";
import { isValidPoint, stAsGeoJsonPoint } from "~/kysely/kysely-helpers";
import { useMergedMatchData } from "~/utils/remix";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { LoaderFunctionArgs } from "@remix-run/router";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const divingLocationId = params.diving_location_id!;

  const divingLocation = await kysely
    .selectFrom("diving_location")
    .selectAll("diving_location")
    .select((eb) => [
      jsonArrayFrom(fileTargetsQb(kysely, "diving_location", eb.ref("diving_location.id"))).as("files"),
      jsonArrayFrom(
        eb
          .selectFrom("diving_site")
          .selectAll("diving_site")
          .select((eb) => [
            stAsGeoJsonPoint(eb.ref("diving_site.geom")).as("geom"),
            isValidPoint(eb.ref("diving_site.geom")).as("valid_point"),
            jsonArrayFrom(fileTargetsQb(kysely, "diving_site", eb.ref("diving_site.id"))).as("files"),
          ])
          .where("diving_location_id", "=", divingLocationId)
          .orderBy("name"),
      ).as("sites"),
    ])
    .where("id", "=", divingLocationId)
    .orderBy("name")
    .executeTakeFirstOrThrow();

  return { location: divingLocation, sites: divingLocation.sites };
};

type LoaderResponse = AsyncReturnType<typeof loader>;

export default function Page() {
  return <Outlet />;
}

export const useDivingLocationAndSites = () => {
  const { diving_site_id } = useParams();
  const data = useMergedMatchData<LoaderResponse>();
  const site = data.sites.find((site) => site.id === diving_site_id);
  return {
    ...data,
    site: site,
  };
};
