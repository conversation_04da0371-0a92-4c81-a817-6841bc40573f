import { Form, useLoaderData, useNavigation } from "@remix-run/react";
import { _diving_location, _diving_site } from "~/misc/paths";
import { Button } from "~/components/base/Button";
import React, { Fragment } from "react";
import { rangeValueToText } from "~/components/form/RangeInput";
import { InputFilesDefault } from "~/components/form/FilesInput";
import { useIkUrl } from "~/components/IkImage";
import { CheckIcon, ChevronRightIcon, ExclamationCircleIcon } from "@heroicons/react/20/solid";
import { DivingSitesMap } from "~/domain/diving-site/DivingSitesMap";
import { useDivingLocationAndSites } from "~/routes/_all._catch.diving-location.$diving_location_id";
import { DividerBig } from "~/components/Divider";
import { DifficultyLevel, DivingSiteAccessEntries, DivingSiteTypes } from "~/domain/diving-site/components";
import { ranges } from "~/domain/diving-site/diving-site";
import { SiteDepthIcon } from "~/components/Icons";
import { DeleteButtonForm, RInput, RLabel, RSelect, RTextarea } from "~/components/ResourceInputs";
import { fName } from "~/misc/helpers";
import { useAppContext } from "~/hooks/use-app-context";
import { kysely } from "~/misc/database.server";
import { ParamLink } from "~/components/meta/CustomComponents";

export { action } from "~/routes/_all._catch.resource";

export const loader = async () => {
  const regions = await kysely
    .selectFrom("region")
    .orderBy(["region.country_code", "region.name"])
    .select(["region.id", "region.name", "region.country_code", "region.published"])
    .execute();
  return {
    regions: regions,
  };
};

export default function Page() {
  const app = useAppContext();
  const { fromBucket } = useIkUrl();
  const loaderData = useLoaderData<typeof loader>();
  const parentData = useDivingLocationAndSites();
  const navigation = useNavigation();

  const isDeleting = !!navigation.formData && !navigation.formData.has(fName("diving_location", "data.name"));
  const isUpdating = !!navigation.formData?.has(fName("diving_location", "data.name"));

  const files = parentData.location.files || [];
  const heroUrl = files[0] && fromBucket(files[0]?.filename, "tr:q-80,h-500,w-1980,c-at_least");

  const highlights = parentData.location.highlights ? parentData.location.highlights.split(/\r?\n/) : [];

  return (
    <div className="space-y-5 pb-10 md:space-y-10">
      <div className="bg-cover bg-center object-fill" style={{ backgroundImage: `url(${heroUrl})` }}>
        <div className="image-text flex h-80 w-full content-center items-center">
          <div className="container h-auto max-w-6xl space-y-2">
            <h1 className="text-2xl font-semibold tracking-wide text-white md:text-5xl">{parentData.location.name}</h1>
          </div>
        </div>
      </div>
      {highlights.length > 0 && (
        <Fragment>
          <div className="app-container flex flex-wrap items-start gap-10">
            <div>
              <h2 className="space-y-3 pb-4 text-xl font-semibold">Highlights</h2>
              <ul>
                {highlights.map((hightlight, index) => (
                  <li key={index} className="flex flex-row items-center leading-7">
                    <CheckIcon className="h-5 w-5 text-green-700" />
                    {hightlight}
                  </li>
                ))}
              </ul>
            </div>
            {/*<div className="flex-1" />*/}
            {!!files[1] && (
              <img
                src={fromBucket(files[1]?.filename, "tr:q-80,h-140,w-200,c-at_least")}
                alt=""
                className="h-40 w-64 rounded-md object-cover"
              />
            )}
            {!!files[2] && (
              <img
                src={fromBucket(files[2]?.filename, "tr:q-80,h-140,w-200,c-at_least")}
                alt=""
                className="h-40 w-64 rounded-md object-cover"
              />
            )}
          </div>
          <DividerBig />
        </Fragment>
      )}
      <div className="app-container">
        <h2 className="pb-3 text-xl font-semibold">About {parentData.location.name}</h2>
        <p className="max-w-screen-md whitespace-pre-wrap">{parentData.location.description}</p>
      </div>
      <DividerBig />
      <DivingSitesMap />
      <DividerBig />
      <div>
        <div className="mx-auto max-w-6xl space-y-6">
          <div className="space-y-3">
            <h2 className="px-3 text-xl font-bold">{parentData.location.name} dive sites</h2>
            <div className="flex max-w-screen-md flex-col gap-5 text-slate-600 sm:px-3">
              {parentData.sites.map((site) => {
                const firstImage = site.files?.[0]?.filename;
                const imageUrl = firstImage && fromBucket(firstImage, "tr:q-80,w-400,h-250");
                return (
                  <div key={site.id}>
                    <ParamLink
                      path={_diving_site(site)}
                      className="border-slate-200 hover:opacity-80 active:opacity-80 sm:flex sm:flex-row sm:overflow-hidden sm:rounded-md sm:border"
                    >
                      <div>
                        <img alt="" className="h-36 w-full object-cover sm:h-28 sm:w-36" src={imageUrl} />
                      </div>
                      <div className="flex flex-1 flex-row items-center">
                        <div className="flex-1 sm:flex sm:flex-row">
                          <div className="flex-1 p-2">
                            <div className="flex flex-row justify-between py-1">
                              <h3 className="text-xl font-semibold text-slate-900">{site.name}</h3>
                              <DifficultyLevel level={site.difficulty_level} className="px-2 text-xs" />
                            </div>
                            {!site.valid_point && app.editor && (
                              <div>
                                <ExclamationCircleIcon className="inline h-5 w-5 text-red-500" />{" "}
                                {site.valid_point === null ? "Coordinates not set" : "Invalid coordinates"}
                              </div>
                            )}
                            <p className="pt-2 pb-1 text-sm italic text-slate-500 line-clamp-2">{site.summary}</p>
                          </div>
                          <div className="flex flex-wrap gap-y-1 gap-x-3 p-2 sm:w-52 sm:flex-col">
                            <DivingSiteAccessEntries divingsite={site} className="text-md" />
                            <DivingSiteTypes divingsite={site} />
                            <div className="flex flex-row gap-3">
                              <div className="h-7 w-10 rounded-md bg-secondary-600">
                                <SiteDepthIcon className="h-full w-full" />
                              </div>
                              <label>{rangeValueToText(ranges.depth_range_in_meters.unit, site.depth_range_in_meters)}</label>
                            </div>
                          </div>
                        </div>
                        <ChevronRightIcon className="h-10 w-10" />
                      </div>
                    </ParamLink>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
      {app.editor && (
        <Fragment>
          <DividerBig />
          <Form className="app-container space-y-6" method="POST">
            <h2 className="text-xl font-bold">Edit diving location</h2>
            <RInput table={"diving_location"} field={"id"} value={parentData.location.id} />
            <RInput
              table={"diving_location"}
              field={"data.name"}
              className="input"
              defaultValue={parentData.location.name || ""}
              disabled={isUpdating}
            />
            <RSelect
              table={"diving_location"}
              field={"data.region_id"}
              defaultValue={parentData.location.region_id || ""}
              className="select"
            >
              <option value={""}>select region</option>
              {loaderData.regions.map((region) => (
                <option value={region.id}>
                  {region.country_code} - {region.name}
                </option>
              ))}
            </RSelect>
            <InputFilesDefault
              target={"diving_location"}
              target_id={parentData.location.id}
              defaultValue={parentData.location.files || []}
            />
            <div>
              <RLabel table={"diving_location"} field={"data.description"} className="capitalize">
                Description
              </RLabel>
              <RTextarea
                table={"diving_location"}
                field={"data.description"}
                defaultValue={parentData.location.description || ""}
                disabled={isUpdating}
                className="input min-h-[200px]"
                placeholder={`a list of highlights, e.g.:
sites for all level divers
healthy corals
great wreck diving
      `}
              />
            </div>
            <div>
              <RLabel table={"diving_location"} field={"data.highlights"} className="capitalize" />
              <RTextarea
                defaultValue={parentData.location.highlights || ""}
                disabled={isUpdating}
                table={"diving_location"}
                field={"data.highlights"}
                className="input min-h-[200px]"
                placeholder={`a list of highlights, e.g.:
sites for all level divers
healthy corals
great wreck diving
      `}
              />
            </div>
            <div className="flex flex-row justify-end gap-3">
              <Button loading={isUpdating} className="btn btn-primary">
                Save
              </Button>
            </div>
          </Form>
          <DividerBig />
          <div className="app-container space-y-3">
            <h2 className="text-xl font-bold">Delete diving location</h2>
            {parentData.sites.length > 0 && <p>Cannot delete dive location as there are diving sites under it.</p>}
            <DeleteButtonForm
              disabled={parentData.sites.length > 0}
              table={"diving_location"}
              values={[parentData.location.id]}
              redirect={_diving_location}
            />
          </div>
        </Fragment>
      )}
    </div>
  );
}
