import { fName, keys } from "~/misc/helpers";
import { ActionForm } from "~/components/form/BaseFrom";
import { useAppContext } from "~/hooks/use-app-context";
import { SubmitButton } from "~/components/base/Button";
import React from "react";
import { fields } from "~/domain/signup_submission/signup-submission";

export { action } from "~/routes/_all._catch.resource";

export default function Page() {
  const ctx = useAppContext();

  return (
    <div>
      <ActionForm className="space-y-3 p-3 group">
        <div className="group-data-success:hidden">
          <code>{`fetch(https://${ctx.host}, {method: 'post'})`}</code>
          <br />
          <br />
          {keys(fields).map((field) => {
            const name = fName("signup_submission", `data.${field}`);
            return (
              <div key={field}>
                <code>{`<input name="${name}"/>`}</code>
                <br />
                <span className="text-slate-500">{field}</span>
                <br />
                <input name={name} />
              </div>
            );
          })}
          <SubmitButton className="btn btn-primary">submit</SubmitButton>
        </div>
        <span className={"hidden text-green-500 group-data-success:inline"}>Send!</span>
      </ActionForm>
    </div>
  );
}
