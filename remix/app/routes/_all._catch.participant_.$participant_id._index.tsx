import { Trans } from "@lingui/react/macro";
import { useLoaderData, useRevalidator } from "@remix-run/react";
import { getSessionSimple } from "~/utils/session.server";
import { participantQb, participationWaiversQb } from "~/domain/participant/participant.queries.server";
import { kysely } from "~/misc/database.server";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { divingCertificatesInItemArray } from "~/domain/product/product-queries.server";
import { notFound } from "~/misc/responses";
import { bookingQb } from "~/domain/booking/booking-queries";
import { createPageOverwrites } from "~/misc/consts";
import React, { ComponentProps, Fragment, ReactNode, Suspense, useId, useMemo } from "react";
import { establishmentQb } from "~/domain/establishment/queries";
import { DeleteButtonForm, RInput, RLabel } from "~/components/ResourceInputs";
import { memberQb } from "~/domain/member/member-queries.server";
import { allowedParticipantIdsFor, allowedParticipantsForMember } from "~/domain/participant/participant-auth-queries.server";
import { saleItemWithProductQb } from "~/domain/activity/activity-queries";
import type { LoaderFunctionArgs } from "@remix-run/router";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { useAppContext } from "~/hooks/use-app-context";
import { DeleteButton, SubmitButton } from "~/components/base/Button";
import { ParamLink } from "~/components/meta/CustomComponents";
import {
  _activity_mutate,
  _booking_detail,
  _customer,
  _customerDetail,
  _file_download,
  _fileTarget,
  _participant,
  _participant_detail,
  _participant_mutate,
  _waiver_detail,
  _waiver_pdf,
} from "~/misc/paths";
import {
  CheckCircleIcon,
  ChevronRightIcon,
  DocumentTextIcon,
  EllipsisVerticalIcon,
  ExclamationCircleIcon,
  InformationCircleIcon,
  TrashIcon,
  XMarkIcon,
} from "@heroicons/react/20/solid";
import { ActionForm } from "~/components/form/BaseFrom";
import { HiddenTypeInput, OperationInput, RedirectInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { twMerge } from "tailwind-merge";
import { fileTargetsQb, type FileTargetValue } from "~/domain/file/file-resource";
import { Popover, useSelectContext } from "~/components/base/Popover";
import { useListItem } from "@floating-ui/react";
import { formatDivingCertShort } from "~/domain/diving-course/diving-courses.data";
import { getNrOfDivesOption } from "~/domain/participant/participant-data";
import { getLastDivedShort } from "~/domain/participant/participant-helpers";
import { UploadButton } from "~/components/form/FilesInput";
import { AnimatingDiv, BaseLink } from "~/components/base/base";
import { fName, getFullUrl, myGroupBy2, tableIdRef } from "~/misc/helpers";
import { CheckDoneIcon, ExportIcon, NotDoneIcon, UploadIcon } from "~/components/Icons";
import {
  addToNow,
  addToTimestamp,
  arrayAgg,
  ascNullsLast,
  ascNullsFirst,
  formatDatetime,
  notNull,
  nowValue,
  tstzToDate,
  subtract,
  multiply,
  pInterval,
} from "~/kysely/kysely-helpers";
import { useBoolean } from "~/hooks/use-boolean";
import { Tooltip } from "~/components/base/tooltip";
import { toast } from "~/misc/toast";
import { defaultCurrency } from "~/misc/vars";
import { getTripType, meetingTypes } from "~/domain/planning/plannings-consts";
import { DebugContainer, IfDebug } from "~/components/debug";
import { flat, unique, uniqueBy } from "remeda";
import { CallbackName, callbacks } from "~/domain/callback/callback";
import { AsyncReturnType } from "~/misc/types";
import { OtpBlock } from "~/domain/otp/otp-components";
import { at_now_value } from "~/kysely/db-static-vars";
import { CDialog } from "~/components/base/Dialog";
import { AcceptDigitalSigning } from "~/domain/participant/participant.components";
import { mergeStateToParams, paramsToRecord, StateInput, StateInputKey } from "~/misc/parsers/global-state-parsers";
import { CallbackInput } from "~/domain/callback/callback.components";
import { getWaiverType, WaiverType } from "~/domain/waiver/waiver-vars";
import { MarkdocComp, replacePlainUrlWithMarkdownUrl } from "~/domain/waiver/waiver-components";
import { MarkDocLink, MarkdocParagraph } from "~/domain/waiver/waiver-markdoc";
import { ClockIcon, InfoIcon } from "lucide-react";
import { getAdminLevelIndex } from "~/domain/member/member-vars";
import { sql } from "kysely";
import { usePageRefresh, useRevalidatorWithPing } from "~/hooks/use-page-refresh";
import { Alert } from "~/components/base/alert";
import { getActivity } from "~/domain/activity/activity";
import { toSitesStr } from "~/domain/trip/trip-helpers";
import { activityUTCStartDateFixedTime, upcomingRemindersQuery } from "~/domain/email/participation-query.server";
import { SidePanel, SidePanelHeading } from "~/components/SidePanel";
import { formatDuration } from "~/domain/activity/activity.helpers";

import { ValidityDurationField } from "~/components/duration";
import { tripQb } from "~/domain/trip/trip.server";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const participantId = params.participant_id!;

  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);
  const token = state.persist_token;
  const session = await getSessionSimple(request);

  const qbArgs = {
    trx: kysely,
    request: request,
    ctx: { session_id: session.session_id },
  };

  const participantQb2 = participantQb(kysely)
    .innerJoin("establishment", "establishment.id", "customer.establishment_id")
    .leftJoin("spot", "spot.id", "establishment.spot_id")
    .leftJoin("region", "region.id", "spot.region_id")
    .select((rootEb) => [
      "region.timezone",
      jsonArrayFrom(
        rootEb
          .selectFrom("rental_assignment")
          .where("rental_assignment.participant_id", "=", rootEb.ref("participant.id"))
          .select(["rental_assignment.id"]),
      ).as("rental_assignments"),
      jsonArrayFrom(
        rootEb
          .selectFrom("sale_item")
          .innerJoin("participation", "participation.sale_item_id", "sale_item.id")
          .where("region.timezone", "is not", null)
          .where("establishment.activity_reminder_in_days_before_start", "is not", null)
          .where("participation.participant_id", "=", rootEb.ref("participant.id"))
          .where((eb) =>
            eb.not(
              eb.exists(
                eb
                  .selectFrom("mail")
                  .where("mail.participant_id", "=", eb.ref("participant.id"))
                  .where("mail.sale_item_id", "=", eb.ref("sale_item.id")),
              ),
            ),
          )
          .where(activityUTCStartDateFixedTime, ">", nowValue)
          .orderBy(activityUTCStartDateFixedTime, "asc")
          .selectAll("sale_item")
          .select((eb) => [
            "participation.id as participation_id",
            formatDatetime(
              subtract(
                activityUTCStartDateFixedTime,
                multiply(eb.ref("establishment.activity_reminder_in_days_before_start"), pInterval("days")),
              ),
              "DD Mon ''YY HH24:MI",
              rootEb.ref("region.timezone"),
            ).as("scheduled_local_datetime_formatted"),
          ]),
      ).as("upcoming_reminders"),
      jsonArrayFrom(
        rootEb
          .selectFrom("mail")
          .where("mail.participant_id", "=", rootEb.ref("participant.id"))
          .selectAll("mail")
          .select((eb) =>
            formatDatetime(eb.ref("mail.created_at"), "DD Mon ''YY HH24:MI", rootEb.ref("region.timezone")).as("created_at_formatted"),
          ),
      ).as("mails"),
      jsonArrayFrom(
        rootEb
          .selectFrom("callback")
          .where("callback.target_id", "=", rootEb.ref("participant.id"))
          .selectAll("callback")
          .select((eb) => [
            formatDatetime(eb.ref("callback.handled_at"), "DD Mon ''YY HH24:MI", rootEb.ref("region.timezone")).as("handled_at_formatted"),
          ]),
      ).as("callbacks"),
      jsonArrayFrom(
        rootEb
          .selectFrom("trip_assignment")
          .innerJoin("participation", "trip_assignment.participation_id", "participation.id")
          .innerJoin("trip", "trip.id", "trip_assignment.trip_id")
          .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
          .leftJoin("member", "member.id", "trip_assignment.member_id")
          .leftJoin("product", "product.id", "sale_item.product_id")
          .leftJoin("item", "item.id", "product.item_id")
          .where("participation.participant_id", "=", rootEb.ref("participant.id"))
          .orderBy("trip.date asc")
          .orderBy("trip.start_time asc")
          .selectAll("trip_assignment")
          .select((eb) => [
            "member.name as member_name",
            "item.activity_slug as activity_slug",
            notNull(jsonObjectFrom(tripQb.where("trip.id", "=", eb.ref("trip_assignment.trip_id")))).as("trip"),
            // "trip.date as trip_date",
            // "trip.start_time as trip_start_time",
            // "trip.sites as trip_sites",
            eb(tstzToDate(nowValue, eb.ref("region.timezone")), ">", eb.ref("trip.date")).as("is_past"),
          ]),
      ).as("trip_assignments"),
      formatDatetime(rootEb.ref("participant.created_at"), "DD Mon ''YY", rootEb.ref("region.timezone")).as("created_at_formatted"),
      jsonArrayFrom(
        rootEb
          .selectFrom("participant_waiver")
          .where("participant_waiver.participant_id", "=", rootEb.ref("participant.id"))
          .selectAll("participant_waiver")
          .select((eb) => [
            jsonObjectFrom(eb.selectFrom("waiver").selectAll("waiver").where("waiver.id", "=", eb.ref("participant_waiver.waiver_id"))).as(
              "waiver",
            ),
            jsonArrayFrom(
              eb
                .selectFrom("signature")
                .where("signature.participant_waiver_id", "=", eb.ref("participant_waiver.id"))
                .select((eb) => [
                  "signature.id",
                  formatDatetime(eb.ref("signature.signed_at"), "DD Mon ''YY", eb.ref("region.timezone")).as("signed_at_formatted"),
                ]),
            ).as("signatures"),
          ]),
      ).as("participant_waivers"),
      jsonArrayFrom(
        rootEb
          .selectFrom("form")
          .selectAll("form")
          .where((eb) => {
            const activityFormIdsQb = eb
              .selectFrom("participation")
              .where("participation.participant_id", "=", eb.ref("participant.id"))
              .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
              .select("sale_item.form_id");
            // return eb("form.id", "=", eb.ref("participant.form_id"));
            // return eb("form.id", "in", activityFormIdsQb);
            return eb.or([eb("form.id", "in", activityFormIdsQb), eb("form.id", "=", eb.ref("participant.form_id"))]);
          }),
      ).as("forms"),
      jsonArrayFrom(fileTargetsQb(kysely, "participant", rootEb.ref("participant.id"))).as("uploads"),
      jsonArrayFrom(
        participationWaiversQb
          .select((eb) => [
            "participation.sale_item_id",
            "waiver.description",
            jsonArrayFrom(
              eb
                .selectFrom("participant_waiver")
                .selectAll("participant_waiver")
                .select((eb) => [
                  formatDatetime(eb.ref("participant_waiver.created_at"), "DD Mon ''YY", rootEb.ref("region.timezone")).as(
                    "created_at_formatted",
                  ),
                  formatDatetime(eb.ref("participant_waiver.created_at"), "DD Mon ''YY HH24:MI", rootEb.ref("region.timezone")).as(
                    "created_at_datetime_formatted",
                  ),
                ])
                .where("participant_waiver.customer_id", "=", rootEb.ref("participant.customer_id"))
                .where("participant_waiver.waiver_id", "=", eb.ref("waiver.id"))
                .orderBy("participant_waiver.created_at desc"),
            ).as("available_participant_waivers"),
          ])
          .clearOrderBy()
          .orderBy("waiver.type asc")
          .orderBy("waiver.establishment_id", ascNullsLast)
          .orderBy("waiver.sort_order", ascNullsFirst)
          .orderBy("waiver.slug asc")
          .where("waiver.type", "!=", "read" satisfies WaiverType),
      ).as("waivers"),
      // incompleteFieldsQb(rootEb),
      rootEb
        .exists(
          memberQb({
            trx: kysely,
            ctx: session,
          }).where("_member.establishment_id", "=", rootEb.ref("customer.establishment_id")),
        )
        .as("normal_view"),
      notNull(
        jsonObjectFrom(
          establishmentQb
            .where("establishment.id", "=", rootEb.ref("customer.establishment_id"))
            .select((eb) => [
              "establishment.require_email_verification_for_signing",
              divingCertificatesInItemArray
                .innerJoin("item", "item.id", "item__diving_course.item_id")
                .where("item.establishment_id", "=", eb.ref("establishment.id"))
                .as("diving_certificate_organizations"),
            ]),
        ),
      ).as("establishment"),
      jsonArrayFrom(
        bookingQb(kysely)
          .leftJoin("sale_item", "sale_item.booking_id", "booking.id")
          .leftJoin("participation", "participant_id", "sale_item.id")
          .distinctOn("booking.id")
          .where((eb) =>
            eb.or([
              eb("participation.participant_id", "=", rootEb.ref("participant.id")),
              // eb.exists(
              //   eb
              //     .selectFrom("participation")
              //     .innerJoin("activity", "activity.id", "participation.sale_item_id")
              //     .where("participation.participant_id", "=", establishmentEb.ref("participant.id"))
              //     .where("activity.booking_id", "=", eb.ref("booking.id"))
              //     .select("activity.booking_id"),
              // ),
              eb("booking.id", "=", rootEb.ref("participant.booking_id")),
            ]),
          )
          .select((eb) => [
            jsonArrayFrom(
              eb
                .selectFrom("participation")
                .selectAll("participation")
                .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
                .where("sale_item.booking_id", "=", eb.ref("booking.id")),
            ).as("participations"),
            eb
              .selectFrom("participation")
              .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
              .where("participation.participant_id", "=", rootEb.ref("participant.id"))
              .where("sale_item.booking_id", "=", eb.ref("booking.id"))
              .select((eb) => arrayAgg(eb.ref("participation.id"), "uuid").as("ids"))
              .as("my_participating_ids"),
            jsonArrayFrom(
              saleItemWithProductQb
                .select((eb) => [
                  jsonArrayFrom(
                    eb
                      .selectFrom("waiver")
                      .selectAll("waiver")
                      .innerJoin("form_waiver", "waiver.id", "form_waiver.waiver_id")
                      .where("form_waiver.form_id", "=", eb.ref("sale_item.form_id")),
                  ).as("waivers"),
                ])
                .where("sale_item.booking_id", "=", eb.ref("booking.id")),
            ).as("activities"),
            // jsonObjectFrom(baseValidProductWithSelectQb.where("product.id", "=", eb.ref("booking.product_id"))).as("product"),
            jsonObjectFrom(establishmentQb.where("establishment.id", "=", eb.ref("booking.establishment_id"))).as("establishment"),
          ]),
      ).as("bookings"),
      jsonArrayFrom(
        kysely
          .selectFrom("participation")
          .selectAll("participation")
          .select((eb) =>
            jsonObjectFrom(saleItemWithProductQb.where("sale_item.id", "=", eb.ref("participation.sale_item_id"))).as("activity"),
          )
          .where("participation.participant_id", "=", rootEb.ref("participant.id")),
      ).as("participations"),
      jsonArrayFrom(
        rootEb
          .selectFrom("participant_token")
          .where((eb) => eb.exists(allowedParticipantsForMember(qbArgs).where("_participant.id", "=", rootEb.ref("participant.id"))))
          .where("participant_token.participant_id", "=", rootEb.ref("participant.id"))
          .select((eb) => [
            "participant_token.id",
            "participant_token.token",
            sql<number>`(floor( EXTRACT (EPOCH FROM (${eb.ref("participant_token.created_at")} - now())) / 60) + 30)`.as(
              "remaining_minutes",
            ),
            eb("participant_token.created_at", ">", addToNow(-30, "minutes")).as("valid"),
          ])
          .orderBy("participant_token.created_at desc"),
      ).as("participant_tokens"),
      rootEb
        .selectFrom("participant_token")
        .select("participant_token.created_at")
        .where("participant_token.participant_id", "=", rootEb.ref("participant.id"))
        .where("participant_token.token", "=", token)
        .where("participant_token.created_at", ">", addToNow(-30, "minutes"))
        .as("valid_token"),
      rootEb.exists(allowedParticipantIdsFor(qbArgs, "read").where("_participant.id", "=", rootEb.ref("participant.id"))).as("allowed"),
    ])
    .where("participant.id", "=", participantId);
  // console.log("particqb", participantQb2.compile().sql);
  console.time(`participantQb ${participantId}`);
  const participant = await participantQb2.executeTakeFirst();
  console.timeEnd(`participantQb ${participantId}`);

  if (!participant) throw notFound();

  return {
    ...createPageOverwrites({
      simple_view: !participant.normal_view,
      show_whatsapp: false,
      establishment_id: participant.establishment_id,
      customer_toggle: true,
    }),
    valid_token: participant.valid_token,
    participant: participant.allowed || participant.valid_token ? participant : { ...participant, email: "" },
  };
};

type ParticipantFromResponse = NonNullable<AsyncReturnType<typeof loader>["participant"]>;

const WaiverDetailPanel = () => {
  const data = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const { revalidate } = useRevalidatorWithPing();
  const allActivities = data.participant.bookings.flatMap((booking) => booking.activities);
  const waivers = data.participant.waivers.filter((waiver) => waiver.waiver_id === search.state.waiver_id);
  const firstWaiver = waivers[0];
  console.log("waiver", waivers);
  if (!firstWaiver) return <div>Waiver not found</div>;

  // const availableParticipantWaivers = firstWaiver.available_participant_waivers;
  const firstParticipantWaiver = firstWaiver.participant_waivers[0];
  const participant = data.participant;
  const isEstablishmentManager = !!data.participant.normal_view && !search.state.customer_view;
  const allFiles = firstParticipantWaiver?.files || [];
  const participantWaiverId = firstParticipantWaiver?.id;

  const UploadApproveForm = (props: { children: ReactNode }) => {
    return (
      ((firstParticipantWaiver?.upload_required && !!allFiles.filter((file) => file.sort_order !== 0).length && isEstablishmentManager) ||
        false) && (
        <ActionForm>
          <RInput table={"participant_waiver"} field={"id"} value={participantWaiverId} />
          {firstParticipantWaiver?.upload_approved ? (
            <Fragment>
              <RInput
                table={"participant_waiver"}
                field={"data.upload_approved"}
                value={"false"}
                hiddenType={"__boolean__"}
                type={"hidden"}
              />
            </Fragment>
          ) : (
            <Fragment>
              <HiddenTypeInput name={fName("participant_waiver", "data.upload_approved")} value={"__empty_array__"} />
              {firstParticipantWaiver?.files.map((file, index) => (
                <input
                  key={file.file_id}
                  type={"hidden"}
                  name={fName("participant_waiver", "data.upload_approved", 0, index)}
                  value={file.file_id}
                />
              ))}
            </Fragment>
          )}
          {props.children}
        </ActionForm>
      )
    );
  };

  return (
    <div className="h-full overflow-auto">
      <div className="px-5 space-y-3 pb-10">
        <div>
          <div className="text-xl text-slate-500">{firstWaiver.name || firstWaiver.slug}</div>
          <div>{firstWaiver.description}</div>
        </div>

        {/* Waiver Status Information */}
        {firstParticipantWaiver && (
          <div className="border-t pt-4 space-y-2">
            <div
              className={twMerge(
                "text-slate-400",
                (!firstWaiver.valid || !firstParticipantWaiver?.isValidWithinActivityDuration) && "text-red-500",
              )}
            >
              {getWaiverType(firstWaiver.type).key === "upload" ? "Uploaded on" : "Signed on"} {firstParticipantWaiver.created_at_formatted}
            </div>
            {!firstParticipantWaiver?.isSameName && <div className="text-amber-600">Registration name was changed after signing</div>}
          </div>
        )}

        {/* Waiver Management Actions */}
        <div className="border-t pt-4 mt-4">
          <h4 className="font-semibold mb-3">Waiver Management</h4>

          <div className="space-y-2">
            {/* {availableParticipantWaivers.map((participantWaiver) => {
              return (
                <div key={participantWaiver.id}>
                  <div>{participantWaiver.created_at_formatted}</div>

                  <WaiverDownoadAnchor
                    participant_waiver_id={participantWaiver.id}
                    className={twMerge(
                      `btn hover:bg-slate-100 focus:bg-slate-200 justify-start w-full flex flex-row items-center aria-selected:bg-black aria-busy:animate-pulse`,
                    )}
                  >
                    <DocumentTextIcon className="w-4 h-4" />
                    View
                  </WaiverDownoadAnchor>

                  <UploadApproveForm>
                    {firstParticipantWaiver?.upload_approved ? (
                      <SubmitButton className="btn hover:bg-slate-100 focus:bg-slate-200 justify-start w-full flex flex-row items-center">
                        <NotDoneIcon className="w-4 h-4 text-red-500" />
                        Disapprove Submission
                      </SubmitButton>
                    ) : (
                      <SubmitButton className="btn hover:bg-slate-100 focus:bg-slate-200 justify-start w-full flex flex-row items-center">
                        <CheckDoneIcon className="w-4 h-4 text-green-600" />
                        Approve Submission
                      </SubmitButton>
                    )}
                  </UploadApproveForm>
                </div>
              );
            })} */}

            {firstParticipantWaiver && !!allFiles.length && (
              <WaiverDownoadAnchor
                participant_waiver_id={firstParticipantWaiver.id}
                className={twMerge(
                  `btn hover:bg-slate-100 focus:bg-slate-200 justify-start w-full flex flex-row items-center aria-selected:bg-black aria-busy:animate-pulse`,
                )}
              >
                <DocumentTextIcon className="w-4 h-4" />
                View
              </WaiverDownoadAnchor>
            )}

            <UploadButton
              className="btn hover:bg-slate-100 focus:bg-slate-200 justify-start w-full flex flex-row items-center aria-selected:bg-black aria-busy:animate-pulse"
              description={`${firstWaiver.name} waiver document for ${participant.full_name}`}
              onUploaded={async (files) => {
                const formdata = new FormData();
                files.forEach((file, index) => {
                  formdata.set(fName("file_target", "data.target", index), "participant_waiver" satisfies FileTargetValue);
                  formdata.set(fName("file_target", "data.file_id", index), file.id);
                  formdata.set(fName("file_target", "data.target_id", index), tableIdRef("participant_waiver"));
                  formdata.set(fName("file_target", "data.sort_order", index), "1");
                });
                if (participantWaiverId) {
                  formdata.set(fName("participant_waiver", "id"), participantWaiverId);
                } else {
                  formdata.set(fName("participant_waiver", "data.waiver_id"), firstWaiver.waiver_id);
                  formdata.set(fName("participant_waiver", "data.participant_id"), participant.id);
                }
                await fetch(window.location.href, { method: "POST", body: formdata });
                revalidate();
              }}
            >
              <UploadIcon className="w-4 h-4" />
              <span>Upload</span>
            </UploadButton>

            {firstParticipantWaiver && (
              <DebugContainer>
                <ActionForm
                  className="w-full"
                  confirmMessage={`Are you sure you want to clear the ${
                    firstWaiver.name || firstWaiver.slug
                  } waiver? This action will delete all signatures and filled fields.`}
                >
                  <RedirectParamsInput path={"./"} paramState={{ popover_id: null }} />
                  <RInput table={"participant_waiver"} field={"id"} value={firstParticipantWaiver.id} />
                  <OperationInput table={"participant_waiver"} value={"delete"} />
                  {firstParticipantWaiver.signatures.map((signature) => (
                    <Fragment key={signature.id}>
                      <RInput table={"signature"} field={"id"} index={signature.id} value={signature.id} />
                      <OperationInput table={"signature"} index={signature.id} value={"delete"} />
                    </Fragment>
                  ))}
                  <SubmitButton className="btn hover:bg-slate-100 focus:bg-slate-200 justify-start w-full flex flex-row items-center">
                    <NotDoneIcon className="w-4 h-4 text-red-500" />
                    <span>Delete signed waiver</span>
                  </SubmitButton>
                </ActionForm>
              </DebugContainer>
            )}

            {firstWaiver.available_participant_waivers.map((availableParticipantWaiver) => (
              <ActionForm key={availableParticipantWaiver.id}>
                <RedirectParamsInput path={"./"} paramState={{ popover_id: null }} />
                {waivers.map((waiver) => {
                  const activityId = waiver.sale_item_id;
                  const index = (activityId || "pending") + availableParticipantWaiver.id;
                  return (
                    <Fragment key={index}>
                      <RInput
                        table={"participation_waiver"}
                        field={"data.sale_item_id"}
                        index={index}
                        value={activityId || ""}
                        type={"hidden"}
                      />
                      <RInput
                        table={"participation_waiver"}
                        field={"data.participant_id"}
                        index={index}
                        value={participant.id}
                        type={"hidden"}
                      />
                      <RInput
                        table={"participation_waiver"}
                        field={"data.participant_waiver_id"}
                        index={index}
                        value={availableParticipantWaiver.id}
                        type={"hidden"}
                      />
                      <RInput
                        table={"participation_waiver"}
                        field={"data.manually_approved"}
                        index={index}
                        value={"true"}
                        hiddenType={"__boolean__"}
                        type={"hidden"}
                      />
                    </Fragment>
                  );
                })}
                <SubmitButton className="btn hover:bg-slate-100 focus:bg-slate-200 justify-start w-full flex flex-row items-center">
                  Use {availableParticipantWaiver.created_at_formatted}
                </SubmitButton>
              </ActionForm>
            ))}
            {firstParticipantWaiver && (
              <div>
                <ActionForm className="space-y-3">
                  <RInput table={"participant_waiver"} field={"id"} value={firstParticipantWaiver.id} type={"hidden"} />
                  <ValidityDurationField
                    label={"Overwrite validity duration"}
                    name={fName("participant_waiver", "data.validity_duration")}
                    defaultValue={firstParticipantWaiver.validity_duration}
                  />
                  <SubmitButton className="btn btn-basic">Update validity duration</SubmitButton>
                </ActionForm>
              </div>
            )}
            {firstParticipantWaiver &&
              [false, true]
                .filter((approve) => !approve || !firstParticipantWaiver?.isValid)
                .map((approve) => (
                  <ActionForm
                    key={approve + ""}
                    confirmMessage={
                      approve
                        ? "You're approving a waiver that is invalid or signed after the activity start date. Do you want to continue?"
                        : undefined
                    }
                  >
                    <RedirectParamsInput path={"./"} paramState={{ popover_id: null }} />
                    {waivers.map((waiver) => {
                      const activityId = waiver.sale_item_id;
                      const participantWaivers = waiver.valid ? waiver.participant_waivers : [firstParticipantWaiver];

                      return participantWaivers.map((participantWaiver) => {
                        const index = (activityId || "pending") + participantWaiver.id;
                        return (
                          <Fragment key={index}>
                            <DebugContainer>
                              <label>
                                Go to default
                                <OperationInput type={"checkbox"} table={"participation_waiver"} index={index} value={"delete"} />
                              </label>
                            </DebugContainer>
                            <RInput
                              table={"participation_waiver"}
                              field={"data.sale_item_id"}
                              index={index}
                              value={activityId || ""}
                              type={"hidden"}
                            />
                            <RInput
                              table={"participation_waiver"}
                              field={"data.participant_id"}
                              index={index}
                              value={participant.id}
                              type={"hidden"}
                            />
                            <RInput
                              table={"participation_waiver"}
                              field={"data.participant_waiver_id"}
                              index={index}
                              value={participantWaiver.id}
                              type={"hidden"}
                            />
                            <RInput
                              table={"participation_waiver"}
                              field={"data.manually_approved"}
                              index={index}
                              value={approve + ""}
                              hiddenType={"__boolean__"}
                              type={"hidden"}
                            />
                          </Fragment>
                        );
                      });
                    })}
                    <SubmitButton className="btn hover:bg-slate-100 focus:bg-slate-200 justify-start w-full flex flex-row items-center">
                      {approve ? (
                        <Fragment>
                          <CheckDoneIcon className="text-green-600" />
                          Approve
                        </Fragment>
                      ) : (
                        <Fragment>
                          <NotDoneIcon className="w-4 h-4 text-red-500" />
                          Clear
                        </Fragment>
                      )}
                    </SubmitButton>
                  </ActionForm>
                ))}
          </div>
        </div>
        <hr />
        <p className="font-semibold">Applies for</p>
        <div>
          {waivers.map((waiver, index) => {
            const activity = allActivities.find((activity) => activity.id === waiver.sale_item_id);
            const activitySlug = activity?.product?.activity_slug;
            const firstParticipantWaiver = waiver.participant_waivers[0];
            console.log("activity", firstParticipantWaiver);
            return (
              <div key={index} className="border rounded-lg p-4 mb-3 bg-gray-50">
                <div className="flex flex-col gap-2">
                  <div className="flex items-center gap-2 justify-between">
                    <div className="flex items-center gap-2">
                      <Tooltip
                        description={
                          <div>
                            {waiver.valid ? (
                              firstParticipantWaiver?.upload_required ? (
                                <div>Action required</div>
                              ) : firstParticipantWaiver?.valid_to_formatted ? (
                                <div>Valid until {firstParticipantWaiver?.valid_to_formatted}</div>
                              ) : (
                                <div>Only valid for this booking</div>
                              )
                            ) : (
                              <div>
                                {!waiver.canSign ? (
                                  <div>Open for signing from {waiver.minSignDateFormatted}</div>
                                ) : firstParticipantWaiver?.isSignedAfterActivityStart ? (
                                  <div>Submitted after the activity start date</div>
                                ) : (
                                  <div>Not submitted or Expired</div>
                                )}
                              </div>
                            )}
                            {firstParticipantWaiver && !firstParticipantWaiver.isForThisParticipant && (
                              <p>This waiver was retrieved from a previous registration</p>
                            )}
                          </div>
                        }
                      >
                        <div className="min-w-[20px]">
                          {firstParticipantWaiver ? (
                            waiver.valid ? (
                              <CheckCircleIcon className={"h-6 w-6 text-green-600"} />
                            ) : (
                              <ExclamationCircleIcon className={"h-6 w-6 text-primary"} />
                            )
                          ) : (
                            <div className="h-6 w-6">
                              <div className="p-[2px] w-full h-full">
                                <div className={"bg-primary w-full h-full rounded-full"} />
                              </div>
                            </div>
                          )}
                        </div>
                      </Tooltip>
                      <span className="font-medium text-gray-700">{activitySlug ? getActivity(activitySlug)?.name : "Registration"}</span>
                    </div>
                    <span className="text-gray-600">
                      {activity && activity?.duration && <span className="text-slate-500 pl-1">{formatDuration(activity)}</span>}
                    </span>
                  </div>
                  {firstParticipantWaiver && (
                    <div className="ml-8 space-y-1 text-sm">
                      {!firstParticipantWaiver?.isValidWithinActivityDuration && (
                        <div className="text-red-500">
                          {firstParticipantWaiver?.isSignedAfterActivityStart
                            ? "Signed after the activity start date"
                            : "Waiver expired - New signature required"}
                        </div>
                      )}
                      {firstParticipantWaiver?.manually_approved && <div className="text-blue-600">Manually approved</div>}
                    </div>
                  )}

                  {/* Use Available Waivers for this Activity */}
                  {false &&
                    !firstParticipantWaiver &&
                    waiver.available_participant_waivers.map((availableParticipantWaiver) => (
                      <ActionForm key={availableParticipantWaiver.id}>
                        <RedirectParamsInput path={"./"} paramState={{ popover_id: null }} />
                        <RInput
                          table={"participation_waiver"}
                          field={"data.sale_item_id"}
                          value={waiver.sale_item_id || ""}
                          type={"hidden"}
                        />
                        <RInput table={"participation_waiver"} field={"data.participant_id"} value={participant.id} type={"hidden"} />
                        <RInput
                          table={"participation_waiver"}
                          field={"data.participant_waiver_id"}
                          value={availableParticipantWaiver.id}
                          type={"hidden"}
                        />
                        <RInput
                          table={"participation_waiver"}
                          field={"data.manually_approved"}
                          value={"true"}
                          hiddenType={"__boolean__"}
                          type={"hidden"}
                        />
                        <SubmitButton className="btn hover:bg-slate-100 focus:bg-slate-200 justify-start w-full flex flex-row items-center">
                          Use {availableParticipantWaiver.created_at_formatted}
                        </SubmitButton>
                      </ActionForm>
                    ))}
                  {false &&
                    firstParticipantWaiver &&
                    [false, true]
                      .filter((approve) => !approve || !firstParticipantWaiver?.isValid)
                      .map((approve) => (
                        <ActionForm
                          key={approve + ""}
                          confirmMessage={
                            approve
                              ? "You're approving a waiver that is invalid or signed after the activity start date. Do you want to continue?"
                              : undefined
                          }
                        >
                          <Fragment key={index}>
                            <DebugContainer>
                              <label>
                                Go to default
                                <OperationInput type={"checkbox"} table={"participation_waiver"} value={"delete"} />
                              </label>
                            </DebugContainer>
                            <RInput
                              table={"participation_waiver"}
                              field={"data.sale_item_id"}
                              value={waiver.sale_item_id || ""}
                              type={"hidden"}
                            />
                            <RInput table={"participation_waiver"} field={"data.participant_id"} value={participant.id} type={"hidden"} />
                            <RInput
                              table={"participation_waiver"}
                              field={"data.participant_waiver_id"}
                              value={firstParticipantWaiver?.id || ""}
                              type={"hidden"}
                            />
                            <RInput
                              table={"participation_waiver"}
                              field={"data.manually_approved"}
                              value={approve + ""}
                              hiddenType={"__boolean__"}
                              type={"hidden"}
                            />
                          </Fragment>

                          <SubmitButton className="btn hover:bg-slate-100 focus:bg-slate-200 justify-start w-full flex flex-row items-center">
                            {approve ? (
                              <Fragment>
                                <CheckDoneIcon className="text-green-600" />
                                Approve
                              </Fragment>
                            ) : (
                              <Fragment>
                                <NotDoneIcon className="w-4 h-4 text-red-500" />
                                Clear
                              </Fragment>
                            )}
                          </SubmitButton>
                        </ActionForm>
                      ))}
                </div>
              </div>
            );
          })}
        </div>
        <DebugContainer>
          <div>Waiver type: {firstWaiver.type}</div>
          <div>Waiver Final Validity duration: {firstWaiver?.validity_duration_final}</div>
          <div>Activity duration: {firstParticipantWaiver?.activity_duration}</div>
          <div>Signature Required: {firstParticipantWaiver?.signature_required ? "yes" : "no"}</div>
          <div>Upload Required: {firstParticipantWaiver?.upload_required ? "yes" : "no"}</div>
          <div>Upload approved: {firstParticipantWaiver?.upload_approved ? "yes" : "no"}</div>
          <div>Waiver Validity duration: {firstParticipantWaiver?.validity_duration}</div>
          <div>Manually approved: {firstParticipantWaiver?.manually_approved ? "yes" : "no"}</div>
          <div>Is valid: {firstWaiver.valid ? "yes" : "no"}</div>
        </DebugContainer>
      </div>
    </div>
  );
};

const WaiverDownoadAnchor = (props: ComponentProps<"a"> & { participant_waiver_id: string }) => {
  const search = useSearchParams2();
  const token = search.state.persist_token;
  return (
    <a
      rel={"noreferrer"}
      target={"_blank"}
      href={
        _fileTarget({
          target_id: props.participant_waiver_id,
          target: "participant_waiver",
        }) + (token ? "?" + ("persist_token" satisfies StateInputKey) + "=" + token : "")
      }
      {...props}
    >
      {props.children}
    </a>
  );
};

const ParticipantUploads = (props: { participant: ParticipantFromResponse }) => {
  const open = useBoolean();
  return (
    <AnimatingDiv>
      {!!props.participant.participant_waivers.length && (
        <button onClick={open.toggle} className="link whitespace-nowrap">
          {open.isOn ? "Hide" : "Show"} all uploads/signatures
        </button>
      )}
      {open.isOn && (
        <div>
          {props.participant.participant_waivers.map((participantWaiver) => {
            return (
              <div key={participantWaiver.id} className="flex flex-row gap-2">
                {participantWaiver.waiver?.slug}
                <WaiverDownoadAnchor
                  participant_waiver_id={participantWaiver.id}
                  className={twMerge("text-slate-400 max-md:text-xs hover:underline")}
                >
                  {participantWaiver.signatures[0]?.signed_at_formatted}
                </WaiverDownoadAnchor>
              </div>
            );
          })}
        </div>
      )}
    </AnimatingDiv>
  );
};

const PopoverSubmit = (props: { children: ReactNode }) => {
  const { activeIndex, selectedIndex } = useSelectContext();
  const { ref, index } = useListItem();
  const active = activeIndex === index;
  const selected = activeIndex === index;
  return (
    <SubmitButton
      className="btn hover:bg-slate-100 focus:bg-slate-200 justify-start w-full flex flex-row items-center aria-selected:bg-black"
      ref={ref}
      aria-selected={selected || active}
    >
      {props.children}
    </SubmitButton>
  );
};

const showDisapproveForTesting = false;

export default function ParticipantPage() {
  const search = useSearchParams2();
  const context = useAppContext();
  const data = useLoaderData<typeof loader>();
  const { revalidate } = useRevalidatorWithPing();
  usePageRefresh(60, data);
  const participant = data.participant;
  const resendMailFormId = useId();

  if (search.state.persist_debug) {
    console.log("participant", participant);
  }

  const bookings = participant.bookings;
  const firstBooking = bookings[0];

  const baseUrl = getFullUrl(context.host);

  const isEstablishmentManager =
    !!context.members.find(
      (member) => member.admin >= getAdminLevelIndex("write") && member.establishment_id === participant.establishment_id,
    ) && !search.state.customer_view;

  const registrationFormCompleted = !participant.cached_incomplete_fields?.length && participant.cached_read_waivers_valid !== false;
  // !participant.read_waivers.filter((waiver) => !waiver.valid).length;

  const fullName = participant.first_name + " " + participant.last_name;

  const infos = useMemo(() => {
    return [
      {
        label: <Trans>Room number</Trans>,
        value: participant.room ?? participant.room,
      },
      {
        label: <Trans>Certification</Trans>,
        value: formatDivingCertShort(participant.diving_certificate_level),
      },
      {
        label: <Trans>Dives</Trans>,
        value: getNrOfDivesOption(participant.number_of_dives)?.[1],
      },
      {
        label: <Trans>Last dive</Trans>,
        value: getLastDivedShort(participant.last_dive_within_months),
      },
      {
        label: <Trans>Country</Trans>,
        value: participant.country_code,
      },
      {
        label: <Trans>Phone</Trans>,
        value: participant.phone,
      },
    ].filter((obj) => obj.value);
  }, [data]);

  const emailVerified = !!context.verified_at && participant.email === context.email;
  const allowedToSign = !participant.establishment.require_email_verification_for_signing || emailVerified || !!data.valid_token;
  const agreedDigitalSign = !!participant.digital_signing_agreed_at;
  const canSign = allowedToSign && agreedDigitalSign;
  const waiverNavigateParams: Partial<StateInput> = {
    participant_id: participant.id,
    page_lang: search.state.persist_page_lang,
    toggle_modal: undefined,
  };

  const genericUploadNames = unique(participant.forms.map((form) => form.upload_description).filter((desc) => !!desc));
  const participantUrl = baseUrl + _participant_detail(participant.id || "");
  return (
    <Fragment>
      <CDialog dialogname={"content"}>
        <div>
          {emailVerified ? (
            <div>email already verified</div>
          ) : (
            <OtpBlock
              user_id={participant.user_id}
              requestOTPComp={
                <div className="space-y-3">
                  <p>
                    Your email is not verified.
                    <br />
                    Click 'Email OTP' to receive a verification code.
                  </p>
                </div>
              }
              verifyOTPComp={<RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined }} />}
            />
          )}
        </div>
      </CDialog>
      <CDialog dialogname={"pre_navigate"}>
        <div>
          {allowedToSign ? (
            <ActionForm preventScrollReset={false}>
              <AcceptDigitalSigning participant={participant} />
              {!!search.state.waiver_id && (
                <RedirectParamsInput path={_waiver_detail(search.state.waiver_id)} paramState={waiverNavigateParams} />
              )}
            </ActionForm>
          ) : (
            <OtpBlock
              user_id={participant.user_id}
              requestOTPComp={
                <div className="space-y-3">
                  <p>To sign waivers, please verify your email with a One-Time Password (OTP)</p>
                  <p>The OTP will be sent to the email address you provided in the registration form.</p>
                </div>
              }
              verifyOTPComp={
                search.state.waiver_id ? (
                  agreedDigitalSign && (
                    <RedirectParamsInput path={_waiver_detail(search.state.waiver_id)} paramState={waiverNavigateParams} />
                  )
                ) : (
                  // In case of email not verified tooltip
                  <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined }} />
                )
              }
            />
          )}
        </div>
      </CDialog>
      <div className="app-container py-6 space-y-3">
        {/*<ActionAlert />*/}
        <div className="text-right">
          {bookings.map((booking) => (
            <ParamLink
              key={booking.id}
              className="link"
              paramState={{ customer_view: search.state.customer_view }}
              path={_booking_detail(booking.id)}
            >
              Jump to Booking {booking.booking_reference || booking.sqid} <ChevronRightIcon className="mb-1 inline-block h-6 w-6" />
            </ParamLink>
          ))}
        </div>
        {!!search.state.persist_token && !data.valid_token && (
          <Alert status={"error"} className="flex flex-row gap-3 p-0 justify-between items-center">
            <p className="p-3">Invalid or Expired Token</p>
            <ParamLink className="btn btn-icon" paramState={{ persist_token: null }}>
              <XMarkIcon className="w-4 h-4" />
            </ParamLink>
          </Alert>
        )}
        <div className="flex flex-row items-center justify-between">
          <ParamLink path={_customerDetail(participant.customer_id)} aria-disabled={!search.state.persist_debug}>
            <h2 className="text-xl">
              {participant.first_name} {participant.last_name}
            </h2>
            {!!participant.email && <p className="text-slate-500">{participant.email}</p>}
          </ParamLink>
          {isEstablishmentManager ? (
            <ActionForm replace confirmMessage={`Are you sure you want to delete ${fullName}?`}>
              <RedirectInput value={firstBooking ? _booking_detail(firstBooking.id) : _participant} />
              <OperationInput table={"participant"} value={"delete"} />
              <RInput table={"participant"} field={"id"} value={participant.id} />
              {participant.rental_assignments.map((item) => (
                <Fragment key={item.id}>
                  <RInput table={"rental_assignment"} field={"id"} index={item.id} value={item.id} />
                  <OperationInput table={"rental_assignment"} index={item.id} value={"delete"} />
                </Fragment>
              ))}
              {/*{participant.mails.map((item) => (*/}
              {/*  <Fragment key={item.id}>*/}
              {/*    <RInput table={"mail"} field={"id"} index={item.id} value={item.id} />*/}
              {/*    <RInput table={"mail"} field={"data.participant_id"} type={"hidden"} index={item.id}/>*/}
              {/*  </Fragment>*/}
              {/*))}*/}
              {participant.participations.map((participation) => (
                <Fragment key={participation.id}>
                  <RInput table={"participation"} field={"id"} index={participation.id} value={participation.id} />
                  <RInput table={"participation"} field={"data.participant_id"} index={participation.id} type={"hidden"} />
                </Fragment>
              ))}
              <DeleteButton />
            </ActionForm>
          ) : (
            !!participant.email &&
            !context.email && (
              <ParamLink className="link flex flex-row items-center gap-1" paramState={{ toggle_modal: "content" }}>
                Email not verified <InformationCircleIcon className="w-4 h-4" />
              </ParamLink>
            )
          )}
        </div>
        {!!participant.email ? (
          <Fragment>
            <p className="rounded-md p-3 bg-orange-100 text-slate-700">
              Thank you for your registration, <span className="font-semibold">{participant.first_name}</span>. <br />
              {participant.bookings.length ? (
                <span>
                  Please take a moment to complete the required forms before your activity date. Your safety and fun are our main focus –
                  let's make this an incredible experience!
                </span>
              ) : (
                <span>
                  We are excited to have you join us! Our team will process your registration promptly, and you can expect us to contact you
                  at our earliest convenience.
                </span>
              )}
            </p>
            <div className="grid grid-cols-2 p-2 gap-3 rounded-md bg-secondary-50" style={{ gridTemplateColumns: "auto 1fr" }}>
              {infos.map((info, index) => (
                <Fragment key={index}>
                  <div>{info.label}</div>
                  <div>{info.value}</div>
                </Fragment>
              ))}
            </div>
            {isEstablishmentManager && (
              <div className="flex flex-wrap gap-3">
                {participant.forms.map((form) => (
                  <span key={form.id} className={"p-1 rounded-md bg-slate-200 text-xs"}>
                    {form.name}
                  </span>
                ))}
              </div>
            )}
            <div className="space-y-3">
              <div className="flex flex-row items-center justify-between gap-2">
                <div className="min-w-[20px]">
                  {registrationFormCompleted ? (
                    <Tooltip description={<div>Mandatory Fields Completed</div>}>
                      <CheckCircleIcon className={"h-6 w-6 text-green-600"} />
                    </Tooltip>
                  ) : (
                    <Tooltip
                      description={
                        <div>
                          Action required
                          {context.environment === "development" && (
                            <Fragment>
                              <br />
                              {participant.cached_incomplete_fields?.join(", ")}
                              {!participant.cached_read_waivers_valid && (
                                <span>
                                  <br />
                                  not agreed to al waivers
                                </span>
                              )}
                            </Fragment>
                          )}
                        </div>
                      }
                    >
                      <ExclamationCircleIcon className={"h-6 w-6 text-primary"} />
                    </Tooltip>
                  )}
                </div>
                <div className="flex-1">
                  <p className="md:text-lg font-bold">Registration form</p>
                </div>
                <p>
                  <Tooltip description={<div>Registered on {participant.created_at_formatted}</div>}>
                    <span className="text-slate-400 whitespace-nowrap max-md:text-xs">{participant.created_at_formatted}</span>
                  </Tooltip>
                </p>
                <div className="min-w-[80px]">
                  <ParamLink
                    path={_participant_mutate}
                    paramState={{
                      id: participant.id,
                      form_id: participant.participations.find((participation) => participation.activity?.registration_form)
                        ? null
                        : participant.form_id,
                    }}
                    className={twMerge(
                      ` btn group aria-busy:loading-dots w-24 h-10 md:w-36 border border-primary  bg-white text-primary transition-colors
              hover:bg-primary-100 aria-current:opacity-70 text-center`,
                      // registrationFormComplted && "text-green-600",
                    )}
                  >
                    {registrationFormCompleted ? (
                      <span>View</span>
                    ) : (
                      <span className="max-md:text-xs group-aria-busy:text-xs">Complete form</span>
                    )}
                  </ParamLink>
                </div>
              </div>
              {/*{ctx.email === "<EMAIL>" && (*/}
              {/*  <div>*/}
              {/*    <Form method="POST">*/}
              {/*      <IdentifierInput value={"sendemail"} />*/}
              {/*      <input type={"hidden"} name={"_action"} value={"send_email"} />*/}
              {/*      <Button loading={sendingEMail}>send mail</Button>*/}
              {/*    </Form>*/}
              {/*    <hr />*/}
              {/*    <CodepenChallengersEmail />*/}
              {/*  </div>*/}
              {/*)}*/}

              {/*<Link*/}
              {/*  to={_establishment_paths(participant.establishment_id).participant_medical(participant.booking_id, participant.id)}*/}
              {/*  className="link"*/}
              {/*>*/}
              {/*  medical*/}
              {/*</Link>*/}
              {/*<br />*/}
              {/*<Link*/}
              {/*  target={"_blank"}*/}
              {/*  to={_establishment_paths(participant.establishment_id).participant_medical(participant.booking_id, participant.id) + "/pdf"}*/}
              {/*  className="link"*/}
              {/*>*/}
              {/*  medical pdf*/}
              {/*</Link>*/}
              {/*<br />*/}
              {/*{myGroupBy2(participant.waivers, (waiver) => waiver.waiver_id)}*/}
              {myGroupBy2(participant.waivers, (waiver) => waiver.waiver_id + waiver.valid + waiver.participant_waivers?.[0]?.id).map(
                (waiverGroup, index) => {
                  const waiver = waiverGroup.items[0];
                  const availableParticipantWaivers = uniqueBy(
                    flat(waiverGroup.items.map((item) => item.available_participant_waivers)),
                    (item) => item.id,
                  );
                  const firstParticipantWaiver = waiver.participant_waivers?.[0];
                  const participantWaiverId = firstParticipantWaiver?.id;
                  const waiverType = getWaiverType(waiver.type);
                  const allFiles = firstParticipantWaiver?.files || [];
                  const coreFiles = allFiles.filter((file) => file.sort_order === 0);
                  const additionalFiles = allFiles.filter((file) => file.sort_order !== 0);
                  const popoverId = waiver.waiver_id + index;
                  const activityIds = waiverGroup.items.map((waiver) => waiver.sale_item_id);

                  const showViewButton = () => {
                    if (!firstParticipantWaiver) return false;
                    if (firstParticipantWaiver.isForThisParticipant || data.valid_token) {
                      return waiver.valid || firstParticipantWaiver.upload_required || !!allFiles.length;
                    }
                    return waiver.valid && !!allFiles.length;
                  };

                  const UploadApproveForm = (props: { children: ReactNode }) => {
                    return (
                      ((firstParticipantWaiver?.upload_required && !!additionalFiles.length && isEstablishmentManager) ||
                        showDisapproveForTesting) && (
                        <ActionForm>
                          <RInput table={"participant_waiver"} field={"id"} value={participantWaiverId} />
                          {firstParticipantWaiver?.upload_approved ? (
                            <Fragment>
                              <RInput
                                table={"participant_waiver"}
                                field={"data.upload_approved"}
                                value={"false"}
                                hiddenType={"__boolean__"}
                                type={"hidden"}
                              />
                            </Fragment>
                          ) : (
                            <Fragment>
                              <HiddenTypeInput name={fName("participant_waiver", "data.upload_approved")} value={"__empty_array__"} />
                              {firstParticipantWaiver?.files.map((file, index) => (
                                <input
                                  key={file.file_id}
                                  type={"hidden"}
                                  name={fName("participant_waiver", "data.upload_approved", 0, index)}
                                  value={file.file_id}
                                />
                              ))}
                            </Fragment>
                          )}
                          {props.children}
                        </ActionForm>
                      )
                    );
                  };

                  return (
                    <div key={waiver.waiver_id + waiver.participation_id} className="space-y-3">
                      <div className="flex flex-row items-center justify-between gap-2">
                        <Tooltip
                          description={
                            <div>
                              {waiver.valid ? (
                                firstParticipantWaiver?.upload_required ? (
                                  <div>Action required</div>
                                ) : firstParticipantWaiver?.valid_to_formatted ? (
                                  <div>Valid until {firstParticipantWaiver?.valid_to_formatted}</div>
                                ) : (
                                  <div>Only valid for this booking</div>
                                )
                              ) : (
                                <div>
                                  {!waiver.canSign ? (
                                    <div>Open for signing from {waiver.minSignDateFormatted}</div>
                                  ) : firstParticipantWaiver?.isSignedAfterActivityStart ? (
                                    <div>Submitted after the activity start date</div>
                                  ) : (
                                    <div>Not submitted or Expired</div>
                                  )}
                                </div>
                              )}
                              {firstParticipantWaiver && !firstParticipantWaiver.isForThisParticipant && (
                                <p>This waiver was retrieved from a previous registration</p>
                              )}
                            </div>
                          }
                        >
                          <div className="min-w-[20px]">
                            {firstParticipantWaiver ? (
                              waiver.valid ? (
                                <CheckCircleIcon className={"h-6 w-6 text-green-600"} />
                              ) : (
                                <ExclamationCircleIcon className={"h-6 w-6 text-primary"} />
                              )
                            ) : (
                              <div className="h-6 w-6">
                                <div className="p-[2px] w-full h-full">
                                  <div className={"bg-primary w-full h-full rounded-full"} />
                                </div>
                              </div>
                            )}
                          </div>
                        </Tooltip>
                        <p className="flex-1 md:text-lg font-bold break-words">
                          <ParamLink
                            aria-disabled={!search.state.persist_debug}
                            paramState={{
                              waiver_id: waiver.waiver_id,
                              toggle_modal: "side",
                            }}
                          >
                            {waiver.name || waiver.slug}
                          </ParamLink>
                        </p>
                        {waiver.description && (
                          <div>
                            <Tooltip
                              description={
                                <div className="bg-white  p-3 break-words whitespace-pre-wrap">
                                  <Suspense fallback={<div>Could not render</div>}>
                                    <MarkdocComp
                                      content={replacePlainUrlWithMarkdownUrl(waiver.description)}
                                      comps={{
                                        Link: MarkDocLink,
                                        Paragraph: MarkdocParagraph,
                                      }}
                                      vars={{}}
                                    />
                                  </Suspense>
                                </div>
                              }
                            >
                              <InfoIcon className="w-5 h-5 text-primary" />
                            </Tooltip>
                          </div>
                        )}
                        <div className="flex flex-row items-center">
                          {firstParticipantWaiver && (
                            <Tooltip
                              description={
                                <div className="space-y-3">
                                  <p>
                                    {waiverType.key === "upload" ? "" : "Signed on"} {firstParticipantWaiver.created_at_formatted}
                                  </p>
                                  {!firstParticipantWaiver?.isSameName && (
                                    <p className="leading-relaxed">
                                      Registration name was changed after signing
                                      {/*<span>Signing Name on the waiver differs from the registration.</span>*/}
                                      {/*<br />*/}
                                      {/*<span>Probably because the name was changed after signing the waiver</span>*/}
                                      {/*<br />*/}
                                      {/*<span>Approve or Clear the waiver to Accept or Reject it</span>*/}
                                    </p>
                                  )}
                                  {!firstParticipantWaiver?.isValidWithinActivityDuration && (
                                    <p>
                                      {firstParticipantWaiver?.isSignedAfterActivityStart ? (
                                        <span>Signed after the activity start date</span>
                                      ) : (
                                        <span>Waiver expired - New signature required.</span>
                                      )}
                                    </p>
                                  )}
                                  {firstParticipantWaiver?.manually_approved && (
                                    <p>
                                      <span>Manually approved</span>
                                    </p>
                                  )}
                                </div>
                              }
                            >
                              <span
                                // rel={"noreferrer"}
                                // target={"_blank"}
                                // href={_participant_waiver_pdf(firstParticipantWaiver.id)}
                                className={twMerge(
                                  "text-slate-400 max-md:text-xs",
                                  (!waiver.valid || !firstParticipantWaiver?.isValidWithinActivityDuration) && "text-red-500",
                                )}
                              >
                                {firstParticipantWaiver.created_at_formatted}
                              </span>
                            </Tooltip>
                          )}
                          {isEstablishmentManager ? (
                            <Popover
                              popoverId={popoverId}
                              options={{ placement: "bottom-end" }}
                              content={
                                <div className="whitespace-pre-wrap">
                                  {firstParticipantWaiver && !!allFiles.length && (
                                    <WaiverDownoadAnchor
                                      participant_waiver_id={firstParticipantWaiver.id}
                                      className={twMerge(
                                        `btn hover:bg-slate-100 focus:bg-slate-200 justify-start w-full flex flex-row items-center aria-selected:bg-black aria-busy:animate-pulse`,
                                      )}
                                    >
                                      <DocumentTextIcon className="w-4 h-4" />
                                      View
                                    </WaiverDownoadAnchor>
                                  )}
                                  <UploadApproveForm>
                                    {firstParticipantWaiver?.upload_approved ? (
                                      <PopoverSubmit>
                                        <NotDoneIcon className="w-4 h-4 text-red-500" />
                                        Disapprove Submission
                                      </PopoverSubmit>
                                    ) : (
                                      <PopoverSubmit>
                                        <CheckDoneIcon className="w-4 h-4 text-green-600" />
                                        Approve Submission
                                      </PopoverSubmit>
                                    )}
                                  </UploadApproveForm>
                                  <UploadButton
                                    className="btn hover:bg-slate-100 focus:bg-slate-200 justify-start w-full flex flex-row items-center aria-selected:bg-black aria-busy:animate-pulse "
                                    description={`${waiver.name} waiver document for ${participant.full_name}`}
                                    onUploaded={async (files) => {
                                      const formdata = new FormData();
                                      files.forEach((file, index) => {
                                        formdata.set(
                                          fName("file_target", "data.target", index),
                                          "participant_waiver" satisfies FileTargetValue,
                                        );
                                        formdata.set(fName("file_target", "data.file_id", index), file.id);
                                        formdata.set(fName("file_target", "data.target_id", index), tableIdRef("participant_waiver"));
                                        formdata.set(fName("file_target", "data.sort_order", index), "1");
                                      });
                                      if (participantWaiverId) {
                                        formdata.set(fName("participant_waiver", "id"), participantWaiverId);
                                      } else {
                                        formdata.set(fName("participant_waiver", "data.waiver_id"), waiver.waiver_id);
                                        formdata.set(fName("participant_waiver", "data.participant_id"), participant.id);
                                      }
                                      await fetch(window.location.href, {
                                        method: "POST",
                                        body: formdata,
                                      });
                                      revalidate();
                                    }}
                                  >
                                    <UploadIcon className="w-4 h-4" />
                                    <span>Upload</span>
                                  </UploadButton>
                                  {firstParticipantWaiver && (
                                    <DebugContainer>
                                      <ActionForm
                                        className="w-full"
                                        confirmMessage={`Are you sure you want to clear the ${
                                          waiver.name || waiver.slug
                                        } waiver? This action will delete all signatures and filled fields.`}
                                      >
                                        <RedirectParamsInput path={"./"} paramState={{ popover_id: null }} />
                                        <RInput table={"participant_waiver"} field={"id"} value={firstParticipantWaiver.id} />
                                        <OperationInput table={"participant_waiver"} value={"delete"} />
                                        {firstParticipantWaiver.signatures.map((signature) => (
                                          <Fragment key={signature.id}>
                                            <RInput table={"signature"} field={"id"} index={signature.id} value={signature.id} />
                                            <OperationInput table={"signature"} index={signature.id} value={"delete"} />
                                          </Fragment>
                                        ))}
                                        <PopoverSubmit>
                                          <NotDoneIcon className="w-4 h-4 text-red-500" />
                                          <span>Delete signed waiver</span>
                                        </PopoverSubmit>
                                      </ActionForm>
                                    </DebugContainer>
                                  )}
                                  {!firstParticipantWaiver &&
                                    availableParticipantWaivers.map((availableParticipantWaiver) => (
                                      <ActionForm key={availableParticipantWaiver.id}>
                                        <RedirectParamsInput path={"./"} paramState={{ popover_id: null }} />
                                        {waiverGroup.items.map((waiver) => {
                                          const activityId = waiver.sale_item_id;
                                          const index = (activityId || "pending") + availableParticipantWaiver.id;
                                          return (
                                            <Fragment key={index}>
                                              <RInput
                                                table={"participation_waiver"}
                                                field={"data.sale_item_id"}
                                                index={index}
                                                value={activityId || ""}
                                                type={"hidden"}
                                              />
                                              <RInput
                                                table={"participation_waiver"}
                                                field={"data.participant_id"}
                                                index={index}
                                                value={participant.id}
                                                type={"hidden"}
                                              />
                                              <RInput
                                                table={"participation_waiver"}
                                                field={"data.participant_waiver_id"}
                                                index={index}
                                                value={availableParticipantWaiver.id}
                                                type={"hidden"}
                                              />
                                              <RInput
                                                table={"participation_waiver"}
                                                field={"data.manually_approved"}
                                                index={index}
                                                value={"true"}
                                                hiddenType={"__boolean__"}
                                                type={"hidden"}
                                              />
                                            </Fragment>
                                          );
                                        })}
                                        <PopoverSubmit>Use {availableParticipantWaiver.created_at_formatted}</PopoverSubmit>
                                      </ActionForm>
                                    ))}
                                  {firstParticipantWaiver &&
                                    [false, true]
                                      .filter((approve) => !approve || !firstParticipantWaiver?.isValid)
                                      .map((approve) => (
                                        <ActionForm
                                          key={approve + ""}
                                          confirmMessage={
                                            approve
                                              ? "You're approving a waiver that is invalid or signed after the activity start date. Do you want to continue?"
                                              : undefined
                                          }
                                        >
                                          <RedirectParamsInput path={"./"} paramState={{ popover_id: null }} />
                                          {waiverGroup.items.map((waiver) => {
                                            const activityId = waiver.sale_item_id;
                                            const participantWaivers = waiver.valid ? waiver.participant_waivers : [firstParticipantWaiver];
                                            return participantWaivers.map((participantWaiver) => {
                                              const index = (activityId || "pending") + participantWaiver.id;
                                              return (
                                                <Fragment key={index}>
                                                  <DebugContainer>
                                                    <label>
                                                      Go to default
                                                      <OperationInput
                                                        type={"checkbox"}
                                                        table={"participation_waiver"}
                                                        index={index}
                                                        value={"delete"}
                                                      />
                                                    </label>
                                                  </DebugContainer>
                                                  <RInput
                                                    table={"participation_waiver"}
                                                    field={"data.sale_item_id"}
                                                    index={index}
                                                    value={activityId || ""}
                                                    type={"hidden"}
                                                  />
                                                  <RInput
                                                    table={"participation_waiver"}
                                                    field={"data.participant_id"}
                                                    index={index}
                                                    value={participant.id}
                                                    type={"hidden"}
                                                  />
                                                  <RInput
                                                    table={"participation_waiver"}
                                                    field={"data.participant_waiver_id"}
                                                    index={index}
                                                    value={participantWaiver.id}
                                                    type={"hidden"}
                                                  />
                                                  <RInput
                                                    table={"participation_waiver"}
                                                    field={"data.manually_approved"}
                                                    index={index}
                                                    value={approve + ""}
                                                    hiddenType={"__boolean__"}
                                                    type={"hidden"}
                                                  />
                                                </Fragment>
                                              );
                                            });
                                          })}
                                          <PopoverSubmit>
                                            {approve ? (
                                              <Fragment>
                                                <CheckDoneIcon className="text-green-600" />
                                                Approve
                                              </Fragment>
                                            ) : (
                                              <Fragment>
                                                <NotDoneIcon className="w-4 h-4 text-red-500" />
                                                Clear
                                              </Fragment>
                                            )}
                                          </PopoverSubmit>
                                        </ActionForm>
                                      ))}
                                  {false && (
                                    <Fragment>
                                      <ParamLink
                                        className="btn hover:bg-slate-100 focus:bg-slate-200 items-start"
                                        target={"_blank"}
                                        path={_waiver_detail(waiver.waiver_id)}
                                        paramState={{ participant_id: participant?.id }}
                                      >
                                        Waiver
                                      </ParamLink>
                                      <ParamLink
                                        className="btn hover:bg-slate-100 focus:bg-slate-200 items-start"
                                        target={"_blank"}
                                        path={_waiver_pdf(waiver.waiver_id)}
                                        paramState={{
                                          participant_waiver_id: firstParticipantWaiver?.id,
                                          participant_id: participant?.id,
                                        }}
                                      >
                                        PDF
                                      </ParamLink>
                                    </Fragment>
                                  )}
                                  {/*{firstParticipantWaiver?.isSigned && !coreFiles.length && !firstParticipantWaiver?.generating_pdf && (*/}
                                  {/*  <ActionForm className="w-full">*/}
                                  {/*    <RedirectParamsInput path={"./"} paramState={{ popover_id: null }} />*/}
                                  {/*    <CallbackInput callbackName={"participant_waiver_mutation"} target_id={firstParticipantWaiver.id} />*/}
                                  {/*    <PopoverSubmit>Generate immutable PDF</PopoverSubmit>*/}
                                  {/*  </ActionForm>*/}
                                  {/*)}*/}
                                  {/*<ParamLink*/}
                                  {/*  className="btn hover:bg-slate-100 focus:bg-slate-200 items-start"*/}
                                  {/*  target={"_blank"}*/}
                                  {/*  path={_waiver_pdf(form.id)}*/}
                                  {/*>*/}
                                  {/*  Pen and paper*/}
                                  {/*</ParamLink>*/}
                                </div>
                              }
                            >
                              <ParamLink
                                className="btn aria-busy:animate-pulse hover:bg-slate-100 transition-colors rounded-full p-2"
                                paramState={{ popover_id: search.state.popover_id ? null : popoverId }}
                                // paramState={{ toggle_modal: "side", waiver_id: waiver.waiver_id, }}
                                replace
                              >
                                <EllipsisVerticalIcon className="w-5 h-5" />
                              </ParamLink>
                            </Popover>
                          ) : (
                            <div className="w-2"></div>
                          )}
                          <div className="min-w-[80px]">
                            {firstParticipantWaiver && showViewButton() ? (
                              <WaiverDownoadAnchor
                                participant_waiver_id={firstParticipantWaiver.id}
                                className={twMerge(
                                  `btn aria-busy:loading-dots h-10 w-24 md:w-36 border transition-colors
              aria-current:opacity-70 outline outline-1 outline-primary hover:bg-primary-100 text-primary`,
                                )}
                              >
                                View
                              </WaiverDownoadAnchor>
                            ) : waiver.canSign ? (
                              waiverType.key === "upload" ? (
                                <UploadButton
                                  description={`${waiver.name} waiver document for ${participant.full_name}`}
                                  className="btn btn-primary text-center aria-busy:opacity-30 w-24 md:w-36 h-10  aria-busy:animate-pulse "
                                  onUploaded={async (files) => {
                                    const formdata = new FormData();
                                    files.forEach((file, index) => {
                                      formdata.set(
                                        fName("file_target", "data.target", index),
                                        "participant_waiver" satisfies FileTargetValue,
                                      );
                                      formdata.set(fName("file_target", "data.file_id", index), file.id);
                                      formdata.set(fName("file_target", "data.target_id", index), tableIdRef("participant_waiver"));
                                      formdata.set(fName("file_target", "data.sort_order", index), "1");
                                    });
                                    formdata.set(fName("participant_waiver", "data.waiver_id"), waiver.waiver_id);
                                    formdata.set(fName("participant_waiver", "data.participant_id"), participant.id);
                                    await fetch(window.location.href, {
                                      method: "POST",
                                      body: formdata,
                                    });
                                    revalidate();
                                  }}
                                >
                                  <span>Upload</span>
                                  {/*<UploadIcon className="w-7 h-7" />*/}
                                </UploadButton>
                              ) : (
                                <ParamLink
                                  path={canSign ? _waiver_detail(waiver.waiver_id) : undefined}
                                  paramState={
                                    canSign
                                      ? waiverNavigateParams
                                      : {
                                          toggle_modal: "pre_navigate",
                                          waiver_id: waiver.waiver_id,
                                        }
                                  }
                                  exact
                                  className={twMerge(
                                    `btn aria-busy:loading-dots w-24 md:w-36 h-10 border transition-colors 
              aria-current:opacity-70`,
                                    participantWaiverId
                                      ? "outline outline-1 outline-primary hover:bg-primary-100 text-primary"
                                      : "btn-primary",
                                  )}
                                >
                                  Start
                                </ParamLink>
                              )
                            ) : (
                              <Tooltip
                                description={<div>Open for signing from {waiver.minSignDateFormatted}</div>}
                                options={{ placement: "left" }}
                              >
                                <span className="w-24 md:w-36 h-10 border btn font-normal text-slate-400 max-md:text-xs">
                                  {waiver.minSignDateFormatted}
                                </span>
                              </Tooltip>
                            )}
                          </div>
                        </div>
                      </div>
                      {/*<div>*/}
                      {/*  {firstParticipantWaiver && (*/}
                      {/*    <span>*/}
                      {/*      <pre>{JSON.stringify(firstParticipantWaiver, null, 3)}</pre>*/}
                      {/*    </span>*/}
                      {/*  )}*/}
                      {/*</div>*/}
                      {participantWaiverId && (
                        <div className="border-secondary rounded border p-3 space-y-3 empty:hidden ">
                          {!waiver.valid &&
                            firstParticipantWaiver?.upload_required &&
                            !additionalFiles.length &&
                            (firstParticipantWaiver?.waiver_type === ("medical" satisfies WaiverType) ? (
                              <p className=" text-red-500">
                                <strong>Medical Examiner's evaluation required!</strong>
                                <br />
                                Participation in a diving course required your physician's approval
                              </p>
                            ) : (
                              <p className="text-red-500">Upload required</p>
                            ))}
                          {!waiver.valid &&
                            firstParticipantWaiver?.upload_required &&
                            !!additionalFiles.length &&
                            (firstParticipantWaiver?.waiver_type === ("medical" satisfies WaiverType) ? (
                              <p>Thank you. Your Examiner's Evaluation Form is under review.</p>
                            ) : (
                              <p>Thank you. Your upload is under review</p>
                            ))}
                          {waiver.valid && firstParticipantWaiver?.upload_required && !!additionalFiles.length && (
                            <p className="flex flex-wrap items-center gap-2">
                              <CheckDoneIcon className="text-green-600" />
                              {firstParticipantWaiver?.waiver_type === ("medical" satisfies WaiverType) ? (
                                <span>Examiner's Evaluation Form accepted</span>
                              ) : (
                                <span>Upload accepted</span>
                              )}
                            </p>
                          )}
                          {!firstParticipantWaiver.upload_approved && (
                            <UploadApproveForm>
                              <SubmitButton className="btn btn-green">
                                <CheckDoneIcon />
                                Approve submission
                              </SubmitButton>
                            </UploadApproveForm>
                          )}
                          {!!additionalFiles.length && (
                            <div className="space-y-1">
                              <p>Uploads:</p>
                              <div className="space-y-1">
                                {additionalFiles.map((fileTarget) => {
                                  const filenameSegements = fileTarget.filename.split("/");
                                  const displayFilename = filenameSegements.slice(filenameSegements.length > 1 ? 1 : 0).join("/");

                                  return (
                                    <div key={fileTarget.id} className="flex flex-wrap gap-3">
                                      <div className="break-all whitespace-pre-wrap">
                                        <BaseLink
                                          key={fileTarget.id}
                                          to={_file_download(fileTarget.filename)}
                                          target={"_blank"}
                                          className={twMerge("link")}
                                        >
                                          {displayFilename}
                                        </BaseLink>
                                      </div>
                                      {!waiver.valid && (
                                        <div>
                                          <DeleteButtonForm table={"file_target"} values={[fileTarget.id || ""]} />
                                        </div>
                                      )}
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          )}
                          {!!participantWaiverId &&
                            firstParticipantWaiver?.waiver_type === ("medical" satisfies WaiverType) &&
                            firstParticipantWaiver?.upload_required &&
                            !firstParticipantWaiver?.upload_approved &&
                            !additionalFiles.length && (
                              <div className="flex flex-row justify-between gap-3 items-center">
                                <span>1. Export Diver Medical PDF</span>
                                <WaiverDownoadAnchor
                                  participant_waiver_id={participantWaiverId}
                                  className={twMerge(
                                    `btn btn-secondary border justify-end transition-colors
              aria-current:opacity-70 text-white`,
                                    // form.generating_pdf && "animate-pulse",
                                  )}
                                >
                                  <span className="hidden md:inline">Download</span>
                                  <ExportIcon className="w-7 h-7" />
                                </WaiverDownoadAnchor>
                              </div>
                            )}
                          {((!!participantWaiverId &&
                            firstParticipantWaiver?.upload_required &&
                            !firstParticipantWaiver?.upload_approved) ||
                            (waiverType.upload && !firstParticipantWaiver?.upload_approved) ||
                            (false && !!additionalFiles.length)) && (
                            <div className="flex flex-row justify-between items-center gap-3">
                              <span>
                                {additionalFiles.length
                                  ? "Upload another file"
                                  : firstParticipantWaiver?.waiver_type === ("medical" satisfies WaiverType)
                                    ? `2. Upload Diver Medical + completed Examiner's evaluation form`
                                    : "Upload file"}
                              </span>
                              <div className="text-right flex flex-row items-end justify-end">
                                <UploadButton
                                  className="btn btn-secondary aria-busy:opacity-30 justify-end aria-busy:animate-pulse "
                                  description={`${waiver.name} waiver, ${additionalFiles.length ? "another file" : firstParticipantWaiver?.waiver_type === ("medical" satisfies WaiverType) ? "medical evaluation" : "file"} for ${participant.full_name}`}
                                  onUploaded={async (files) => {
                                    const formdata = new FormData();
                                    files.forEach((file, index) => {
                                      formdata.set(
                                        fName("file_target", "data.target", index),
                                        "participant_waiver" satisfies FileTargetValue,
                                      );
                                      formdata.set(fName("file_target", "data.file_id", index), file.id);
                                      formdata.set(fName("file_target", "data.target_id", index), participantWaiverId);
                                      formdata.set(fName("file_target", "data.sort_order", index), "1");
                                    });
                                    if (firstParticipantWaiver?.waiver_type === ("medical" satisfies WaiverType)) {
                                      formdata.set(fName("callback", "data.target_id"), participantWaiverId);
                                      formdata.set(
                                        fName("callback", "data.name"),
                                        "notify_establishment_for_medical_evaluation" satisfies CallbackName,
                                      );
                                    }
                                    await fetch(window.location.href, {
                                      method: "POST",
                                      body: formdata,
                                    });
                                    revalidate();
                                  }}
                                >
                                  <span className="hidden md:inline">Upload</span>
                                  <UploadIcon className="w-7 h-7" />
                                </UploadButton>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  );
                },
              )}
            </div>
            {false && <ParticipantUploads participant={participant} />}
            {!!genericUploadNames.length && (
              <div className="space-y-3">
                <h3 className="font-semibold text-xl">Uploads</h3>
                {genericUploadNames.map((desc) => {
                  const content = replacePlainUrlWithMarkdownUrl(desc || "");
                  return (
                    <div key={desc} className="break-words whitespace-pre-wrap">
                      <Suspense fallback={<div>Could not render</div>}>
                        <MarkdocComp content={content} comps={{ Link: MarkDocLink, Paragraph: MarkdocParagraph }} vars={{}} />
                      </Suspense>
                    </div>
                  );
                })}
                <div>
                  {participant.uploads.map((fileTarget) => {
                    const filenameSegements = fileTarget.filename.split("/");
                    const displayFilename = filenameSegements.slice(filenameSegements.length > 1 ? 1 : 0).join("/");

                    return (
                      <Fragment key={fileTarget.id}>
                        <div className="flex flex-wrap gap-3">
                          <BaseLink
                            key={fileTarget.id}
                            to={_file_download(fileTarget.filename)}
                            target={"_blank"}
                            className={twMerge("link")}
                          >
                            {displayFilename}
                          </BaseLink>
                          {isEstablishmentManager && (
                            <ActionForm confirmMessage={`Are you sure you want to delete this file?`}>
                              <RInput table={"file_target"} field={"id"} value={fileTarget.id} />
                              <OperationInput table={"file_target"} value={"delete"} />
                              <DeleteButton>delete</DeleteButton>
                            </ActionForm>
                          )}
                        </div>
                      </Fragment>
                    );
                  })}
                </div>
                <div className="text-right flex flex-row items-end justify-end">
                  <UploadButton
                    className="btn btn-secondary aria-busy:opacity-30 justify-end aria-busy:animate-pulse "
                    description={`Document for ${participant.full_name}`}
                    onUploaded={async (files) => {
                      const formdata = new FormData();
                      files.forEach((file, index) => {
                        formdata.set(fName("file_target", "data.target", index), "participant" satisfies FileTargetValue);
                        formdata.set(fName("file_target", "data.file_id", index), file.id);
                        formdata.set(fName("file_target", "data.target_id", index), participant.id);
                        formdata.set(fName("file_target", "data.sort_order", index), "1");
                      });
                      await fetch(window.location.href, { method: "POST", body: formdata });
                      revalidate();
                    }}
                  >
                    <span className="hidden md:inline">Upload</span> <UploadIcon className="w-7 h-7" />
                  </UploadButton>
                </div>
              </div>
            )}
            {isEstablishmentManager && !participant.bookings.length && (
              <div>
                {participant.establishment.workflow > 1 ? (
                  <ParamLink
                    className="btn btn-primary w-fit"
                    path={_activity_mutate}
                    paramState={{
                      participant_ids: [participant.id],
                      establishment_id:
                        search.state.establishment_id || search.state.persist_establishment_id || participant.establishment_id,
                    }}
                  >
                    Create activity
                  </ParamLink>
                ) : (
                  <ActionForm className="flex flex-wrap gap-3">
                    <RInput
                      type={"hidden"}
                      table={"booking"}
                      field={"data.currency_id"}
                      defaultValue={participant.establishment.default_currency || defaultCurrency}
                    />
                    <RInput type={"hidden"} table={"participation"} field={"data.sale_item_id"} value={tableIdRef("sale_item")} />
                    <RInput type={"hidden"} table={"participation"} field={"data.participant_id"} value={tableIdRef("participant")} />
                    <RInput type={"hidden"} table={"sale_item"} field={"data.booking_id"} value={tableIdRef("booking")} />
                    <RInput type={"hidden"} table={"sale_item"} field={"data.price_pp"} value={0} />
                    <RInput type={"hidden"} table={"sale_item"} field={"data.quantity"} value={1} />
                    <RInput
                      type={"hidden"}
                      table={"booking"}
                      field={"data.meeting_type"}
                      value={"DIVE_CENTER" satisfies keyof typeof meetingTypes}
                    />
                    <RInput type={"hidden"} table={"booking"} field={"data.establishment_id"} value={participant.establishment_id} />
                    <RInput
                      type={"hidden"}
                      table={"booking"}
                      field={"data.booking_reference"}
                      value={participant.first_name + " " + participant.last_name}
                    />
                    <RInput table={"participant"} field={"id"} value={participant.id} />
                    <RedirectParamsInput path={_customer} paramState={{ persist_establishment_id: participant.establishment_id }} />
                    <input type={"hidden"} name={fName("sale_item", "data.duration", 0, "duration")} value={1} />
                    <HiddenTypeInput name={fName("sale_item", "data.duration")} value={"__pg_daterange"} />
                    <input
                      type={"date"}
                      name={fName("sale_item", "data.duration", 0, "date")}
                      className="input w-fit"
                      defaultValue={context.date.todayParam}
                    />
                    <SubmitButton className="btn btn-primary">Schedule</SubmitButton>
                  </ActionForm>
                )}
              </div>
            )}
            {!!participant.trip_assignments?.length && (
              <div className="">
                <h3 className="text-xl">Trips</h3>
                <div className="grid grid-cols-5 gap-y-1 gap-x-2">
                  {myGroupBy2(participant.trip_assignments, (tripAssignment) =>
                    tripAssignment.trip.date === context.date.todayParam
                      ? "1.Today"
                      : tripAssignment.is_past
                        ? "3.Completed"
                        : "2.Upcoming",
                  )
                    .sort((a, b) => a.groupKey.localeCompare(b.groupKey))
                    .map((group) => {
                      return (
                        <Fragment key={group.groupKey}>
                          <div className={"col-span-5 text-lg font-bold"}>
                            <div className="pt-3">{group.groupKey.slice(2)}</div>
                          </div>
                          <div className="text-sm text-slate-700">Date</div>
                          <div className="text-sm text-slate-700">Time</div>
                          <div className="text-sm text-slate-700">Activity</div>
                          <div className="text-sm text-slate-700">Instructor</div>
                          <div className="text-sm text-slate-700">Sites</div>
                          {group.items.map((tripAssignment) => {
                            // const activity = participant.bookings?.map(.activities.find((activity) => activity.id === tripAssignment.sale_item_id);
                            // const product = activity?.product;
                            const tripType = getTripType(tripAssignment.trip.type);
                            const productType = getActivity(tripAssignment.activity_slug || "");
                            // const productTitle = product && getProductTitle(product);
                            return (
                              <Fragment key={tripAssignment.id}>
                                <div>{tripAssignment.trip.date}</div>
                                <div>{tripAssignment.trip.start_time?.slice(0, 5)}</div>
                                <div>{productType?.name}</div>
                                {/*<div>{productTitle}</div>*/}
                                <div>{tripAssignment.member_name}</div>
                                <div>{tripType?.showSites ? toSitesStr(tripAssignment.trip.sites) : ""}</div>
                              </Fragment>
                            );
                          })}
                        </Fragment>
                      );
                    })}
                </div>
              </div>
            )}
            {isEstablishmentManager && (
              <div className="space-y-3 pt-8">
                <p className="text-xl ">Page URL</p>
                <div className="space-y-1">
                  <div className="relative">
                    <input type="text" readOnly defaultValue={participantUrl} className="w-full input" />
                    <button
                      type={"button"}
                      onClick={async (e) => {
                        e.preventDefault();
                        console.log("kom ik hier?");
                        await navigator.clipboard.writeText(participantUrl);
                        toast("URL copied");
                      }}
                      className="absolute right-1 inset-y-1 btn bg-white font-semibold text-slate-500 hover:text-slate-900"
                    >
                      Copy
                    </button>
                  </div>
                </div>
                <div className="flex-row flex gap-3 items-center">
                  <p className="text-xl">Signed URL(s)</p>
                  <ActionForm>
                    <RInput table={"participant_token"} field={"data.participant_id"} type={"hidden"} value={participant.id} />
                    <SubmitButton className="link">Generate</SubmitButton>
                  </ActionForm>
                </div>
                <div className="space-y-3">
                  {participant.participant_tokens
                    .filter((participantToken) => participantToken.valid || search.state.persist_debug)
                    .map((participantToken) => {
                      const newSearchParams = new URLSearchParams();
                      mergeStateToParams(newSearchParams, { persist_token: participantToken.token });
                      const signedUrl = participantUrl + "?" + newSearchParams.toString();
                      return (
                        <div key={participantToken.id} className="border border-slate-300 rounded-md overflow-hidden">
                          <div className="flex items-center pr-1">
                            <input type="text" readOnly defaultValue={signedUrl} className="flex-1 border-none text-sm py-3" />
                            <div className="flex flex-row  items-center bg-white px-1">
                              {participantToken.valid ? (
                                <div className="text-green-600 whitespace-nowrap">
                                  {Math.abs(participantToken.remaining_minutes)}min. remaining
                                </div>
                              ) : (
                                <div className="text-red-500">Expired</div>
                              )}
                              <button
                                type={"button"}
                                onClick={async (e) => {
                                  e.preventDefault();
                                  console.log("kom ik hier?");
                                  await navigator.clipboard.writeText(signedUrl);
                                  toast("URL copied");
                                }}
                                className="btn bg-white font-semibold text-slate-500 hover:text-slate-900"
                              >
                                Copy
                              </button>
                              <DeleteButtonForm table={"participant_token"} className="text-slate-500 p-1" values={[participantToken.id]}>
                                <TrashIcon className="h-4 w-4" />
                              </DeleteButtonForm>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            )}
            <div>
              {participant.bookings.map((booking) => {
                const my_participating_ids = booking.participations
                  .map((participation) => participation.participant_id)
                  .filter((participantId): participantId is string => participantId === participant.id);

                const openSlots = booking.participations.filter((participation) => !participation.participant_id);
                return (
                  <div key={booking.id} className="space-y-3">
                    {booking.direct_booking && !!openSlots.length && (
                      <div className="border rounded-md border-primary p-3 space-y-3">
                        <p>
                          <span className="font-semibold">Important:</span> Your fellow participants need to register too. Visit your
                          booking page to share the details with them
                        </p>
                        <div className="flex justify-center">
                          <ParamLink path={_booking_detail(booking.id)} className="btn btn-primary items-center px-4">
                            Go to Booking <ChevronRightIcon className={"w-5 h-5"} />
                          </ParamLink>
                        </div>
                      </div>
                    )}
                    {context.editor && !search.state.customer_view && participant.bookings.length > 0 && (
                      <ActionForm key={booking.id} replace className="w-full" confirmMessage={"Are you sure?"}>
                        {participant.booking_id === booking.id && (
                          <Fragment>
                            <RInput table={"participant"} field={"id"} index={booking.id} value={participant.id} />
                            <RInput table={"participant"} field={"data.booking_id"} index={booking.id} value={""} type={"hidden"} />
                          </Fragment>
                        )}
                        {my_participating_ids.map((participationId) => (
                          <Fragment key={participationId}>
                            <RInput table={"participation"} field={"id"} index={participationId} value={participationId} />
                            <RInput table={"participation"} field={"data.participant_id"} index={participationId} type={"hidden"} />
                          </Fragment>
                        ))}
                        <div className="p-3 rounded-md border border-1 border-slate-300 flex flex-wrap justify-between items-center w-full gap-3">
                          <span>
                            Connected to booking <span className="font-semibold">{booking.booking_reference || booking.sqid}</span>{" "}
                          </span>
                          <DeleteButton className="">Disconnect</DeleteButton>
                        </div>
                      </ActionForm>
                    )}
                    <DebugContainer>
                      {booking.activities.map((activity, index) => (
                        <div key={activity.id} className="flex flex-wrap gap-3">
                          <span className="font-bold">
                            {activity.registration_form?.name}: {index} {activity.created_at}
                          </span>
                          {activity.waivers.map((waiver) => (
                            <span key={waiver.id}>{waiver.slug}</span>
                          ))}
                        </div>
                      ))}
                    </DebugContainer>
                  </div>
                );
              })}
            </div>
            {isEstablishmentManager && !!participant.callbacks.length && (
              <div className={"space-y-3 pt-3"}>
                <p className="text-xl">Emails</p>
                <div style={{ gridTemplateColumns: "auto auto auto" }} className="grid gap-x-3 gap-y-2 text-sm md:text-base">
                  <div className="contents text-sm text-slate-500">
                    <div>Mail</div>
                    <div>Status</div>
                    <div>Time</div>
                  </div>
                  {participant.callbacks.map((callback) => {
                    const callbackObj = callbacks[callback.name as CallbackName];
                    const status = callback.success ? (
                      <CheckDoneIcon className={"text-green-600"} />
                    ) : callback.handled_at ? (
                      <NotDoneIcon className="text-red-500" />
                    ) : (
                      <ClockIcon className="text-yellow-500" />
                    );
                    return (
                      <Fragment key={callback.id}>
                        <div>{callbackObj.name}</div>
                        <div className="flex items-center gap-3 text-sm text-slate-700">
                          {status}
                          <span className="hidden md:block">{callback.msg}</span>
                        </div>
                        <div className="block md:hidden items-center gap-3 text-sm text-slate-700">
                          <Tooltip description={callback.msg}>{status}</Tooltip>
                        </div>
                        <div>{callback.handled_at_formatted}</div>
                      </Fragment>
                    );
                  })}
                  {participant.mails.map((mail) => (
                    <Fragment key={mail.id}>
                      <div>
                        Notify for activity
                        <IfDebug>
                          <br />#{mail.id}
                        </IfDebug>
                      </div>
                      <div className="hidden md:flex items-center gap-3 text-sm text-slate-700">
                        {mail.success ? <CheckDoneIcon className={"text-green-600"} /> : <NotDoneIcon className="text-red-500" />}
                        <span className="hidden md:block">{mail.msg}</span>
                      </div>
                      <div className="block md:hidden items-center gap-3 text-sm text-slate-700">
                        <Tooltip description={mail.msg}>
                          {mail.success ? <CheckDoneIcon className={"text-green-600"} /> : <NotDoneIcon className="text-red-500" />}
                        </Tooltip>
                      </div>
                      <div>{mail.created_at_formatted}</div>
                    </Fragment>
                  ))}
                  {participant.upcoming_reminders.map((reminder) => (
                    <Fragment key={reminder.participation_id}>
                      <div>Notify for activity</div>
                      <div className="hidden md:flex items-center gap-3 text-sm text-slate-700">
                        <ClockIcon className="text-yellow-500 w-4 h-4" />
                        <span className="hidden md:block">Upcoming</span>
                      </div>
                      <div className="block md:hidden items-center gap-3 text-sm text-slate-700">
                        <Tooltip description={"Upcoming"}>
                          <ClockIcon className="text-yellow-500 w-4 h-4" />
                        </Tooltip>
                      </div>
                      <div>{reminder.scheduled_local_datetime_formatted}</div>
                    </Fragment>
                  ))}
                </div>
              </div>
            )}
            {search.state.persist_debug && (
              <Fragment>
                <ActionForm identifier={resendMailFormId} className="group">
                  <CallbackInput callbackName={"send_participant_registration_email"} target_id={participant.id} />
                  <div className="flex flex-row gap-3 items-center">
                    <SubmitButton className="btn btn-secondary">Send mail again</SubmitButton>
                    <p className="hidden group-data-success:block text-green-600">Success!</p>
                  </div>
                </ActionForm>
                <ActionForm>
                  <RInput table={"participant"} field={"id"} value={participant.id} index={participant.id} />
                  <RInput
                    table={"participant"}
                    field={"data.digital_signing_agreed_at"}
                    index={participant.id}
                    type={"hidden"}
                    value={""}
                  />
                  <RLabel table={"participant"} field={"data.digital_signing_agreed_at"}>
                    <RInput
                      table={"participant"}
                      field={"data.digital_signing_agreed_at"}
                      type={"checkbox"}
                      index={participant.id}
                      // defaultValue={participant}
                      value={at_now_value}
                      defaultChecked={!!participant.digital_signing_agreed_at}
                    />
                    <span>agree digital signing</span>
                  </RLabel>
                  <SubmitButton className={"btn btn-primary"}>Save</SubmitButton>
                </ActionForm>
              </Fragment>
            )}
          </Fragment>
        ) : (
          <Fragment>
            <ActionForm>
              <p>
                You don't currently have permission to view this page. If this is your account, you can gain access by requesting a One-Time
                Password (OTP) to be sent to your registered email.
              </p>
              <div className="space-y-3 max-w-96">
                <OtpBlock user_id={participant.user_id} />
              </div>
            </ActionForm>
          </Fragment>
        )}
      </div>
      <SidePanel className="bg-white min-w-96" dialogname="side">
        <SidePanelHeading>
          <span>Waiver Details</span>
        </SidePanelHeading>
        <WaiverDetailPanel />
      </SidePanel>
    </Fragment>
  );
}
