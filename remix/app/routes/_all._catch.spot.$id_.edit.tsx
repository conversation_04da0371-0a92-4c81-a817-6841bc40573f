import type { DataFunctionArgs } from "@remix-run/server-runtime";
import type { AsyncReturnType } from "type-fest";
import { useLoaderData } from "@remix-run/react";
import { Backbutton } from "~/components/base/base";
import { kysely } from "~/misc/database.server";
import { SubmitButton } from "~/components/base/Button";
import { InputFilesDefault } from "~/components/form/FilesInput";
import React from "react";
import { myGroupBy } from "~/misc/helpers";
import { getSessionSimple } from "~/utils/session.server";
import { EditorRequired } from "~/components/account/AccountContainer";
import { stAsGeoJsonPoint } from "~/kysely/kysely-helpers";
import { RInput, RSelect } from "~/components/ResourceInputs";
import { RedirectInput } from "~/components/form/DefaultInput";
import { _spot_detail } from "~/misc/paths";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { ActionForm } from "~/components/form/BaseFrom";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: DataFunctionArgs) => {
  // const { context } = await getSession(request);
  const { session_id } = await getSessionSimple(request);
  const user = await activeUserSessionSimple(kysely, session_id, true).select("_user.editor").executeTakeFirst();
  if (!user?.editor) return null;
  // if (!context.editor) return null;
  const id = params.id!;
  return Promise.all([
    kysely
      .selectFrom("spot")
      .innerJoin("region", "region.id", "spot.region_id")
      .selectAll("spot")
      .select((eb) => [
        "region.name as region_name",
        "region.country_code",
        stAsGeoJsonPoint(eb.ref("spot.geom")).as("geom"),
        jsonArrayFrom(fileTargetsQb(kysely, "spot", eb.ref("spot.id"))).as("files"),
      ])
      .where("spot.id", "=", id)
      .executeTakeFirstOrThrow(),
    kysely.selectFrom("region").select(["region.id", "region.name", "region.country_code", "region.published"]).execute(),
  ]);
};

type LoaderResponse = AsyncReturnType<typeof loader>;

export default function Page() {
  const response = useLoaderData<LoaderResponse>();

  if (!response) return <EditorRequired />;

  const [spot, regions] = response;

  return (
    <div className="app-container py-3">
      {spot ? (
        <h1 className="text-xl font-bold">
          Edit: {spot.name}, {spot.region_name}, {spot.country_code}
        </h1>
      ) : (
        <h1 className="text-xl font-bold">Create spot</h1>
      )}
      <ActionForm className="space-y-6 py-3">
        <RInput table={"spot"} field={"id"} value={spot.id} />
        <RedirectInput value={_spot_detail(spot.id)} />
        <InputFilesDefault target={"spot"} target_id={spot.id} defaultValue={spot?.files || []} />
        <RInput table={"spot"} field={"data.name"} className="input" defaultValue={spot?.name || ""} required placeholder="name" />
        <label>
          <span>Region</span>
          <RSelect table={"spot"} field={"data.region_id"} className="input" defaultValue={spot?.region_id || ""} required>
            <option value="">select region</option>
            {myGroupBy(regions, (region) => region.country_code).map((country) => (
              <optgroup key={country.country_code} label={country.country_code}>
                {country.items.map((region) => (
                  <option key={region.id} value={region.id}>
                    {region.name}
                  </option>
                ))}
              </optgroup>
            ))}
          </RSelect>
        </label>
        <div className="flex flex-row items-center">
          <Backbutton className="btn">Cancel</Backbutton>
          <SubmitButton className="btn btn-primary">Save</SubmitButton>
        </div>
      </ActionForm>
    </div>
  );
}
