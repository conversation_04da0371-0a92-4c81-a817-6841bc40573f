import { use<PERSON><PERSON>derD<PERSON> } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { getSessionSimple } from "~/utils/session.server";
import { _establishment_detail, _establishment_path, _operator_edit, _userDetail } from "~/misc/paths";
import React from "react";
import { unauthorized } from "~/misc/responses";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { LoaderFunctionArgs } from "@remix-run/router";
import { ParamLink } from "~/components/meta/CustomComponents";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);
  const user = await activeUserSessionSimple(kysely, session_id, true).select("_user.editor").executeTakeFirst();
  if (!user?.editor) throw unauthorized();

  const operators = await kysely
    .selectFrom("operator")
    .selectAll("operator")
    .select((eb) => [
      jsonArrayFrom(
        eb
          .selectFrom("establishment")
          .selectAll("establishment")
          .select((eb) => [
            jsonObjectFrom(eb.selectFrom("spot").whereRef("spot.id", "=", "establishment.spot_id").selectAll("spot")).as("spot"),
            jsonArrayFrom(
              eb
                .selectFrom("member")
                .innerJoin("user", "user.id", "member.user_id")
                .where("user.deleted_at", "=", at_infinity_value)
                .selectAll("member")
                .select(["user.email as user_email"])
                .where("member.owner", "=", true)
                .whereRef("member.establishment_id", "=", "establishment.id"),
            ).as("owners"),
          ])
          .whereRef("establishment.operator_id", "=", "operator.id"),
      ).as("establishments"),
    ])
    .execute();

  return {
    operators: operators,
  };
};

export default function Page() {
  const { operators } = useLoaderData<typeof loader>();

  return (
    <div className="app-container">
      <div className="space-y-4 py-4">
        <div className="flex flex-wrap items-center gap-2">
          <h1 className="text-2xl font-bold">Operators</h1>
          <ParamLink path={_establishment_path} className="link">
            Locations
          </ParamLink>
        </div>
        {operators.map((operator) => (
          <div key={operator.id}>
            <div className="flex flex-wrap items-center gap-2">
              <h2 className="text-xl font-bold">{operator.name}</h2>
              <ParamLink className="link" path={_operator_edit(operator.id)}>
                view
              </ParamLink>
            </div>
            <div className="space-y-2 py-1 pl-2">
              {operator.establishments.map((establishment) => (
                <div key={establishment.id} className="flex flex-wrap gap-2">
                  <div className="flex flex-wrap gap-3">
                    <ParamLink className="link" key={establishment.id} path={_establishment_detail(establishment.id)}>
                      {establishment.spot?.name || "__spot__"} {establishment.location_name && " - " + establishment.location_name}
                    </ParamLink>
                  </div>
                  {establishment.xendit_account_id && <span className="bg-green-600 rounded-md p-1 text-white text-xs">xendit</span>}
                  {establishment.owners.map((owner) => (
                    <ParamLink className="bg-slate-50 hover:underline active:underline" key={owner.id} path={_userDetail(owner.user_id!)}>
                      {owner.user_email}
                    </ParamLink>
                  ))}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
