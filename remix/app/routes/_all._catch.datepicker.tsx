import { weekDays } from "~/misc/vars";
import React, { useEffect } from "react";
import { useFetcher } from "@remix-run/react";
import { getMonthObj } from "~/domain/planning/plannings-helpers";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { addMonths, format } from "date-fns";
import { ParamLink } from "~/components/meta/CustomComponents";
import { ChevronLeftIcon } from "@heroicons/react/20/solid";
import { twMerge } from "tailwind-merge";
import { Spinner } from "~/components/base/base";
import type { LoaderFunctionArgs } from "@remix-run/router";
import { getDateFromParams } from "~/domain/planning/planning.helpers.server";
import { monthLoader } from "~/server/loaders/month-loader";
import { uniqueBy } from "remeda";
import { useAppContext } from "~/hooks/use-app-context";
import { toUtc } from "~/misc/date-helpers";

export const loader = (args: LoaderFunctionArgs) => {
  const date = getDateFromParams(args.request);
  return monthLoader(args, date.monthParam);
};

export const MonthLink = (props: { add: -1 | 1 }) => {
  const search = useSearchParams2();
  const ctx = useAppContext();
  const date = toUtc(search.state.persist_month || ctx.date.monthParam);
  const newMonth = addMonths(date, props.add);
  const newMonthParam = format(newMonth, "yyyy-MM");
  return (
    <ParamLink preventScrollReset className="text-secondary-500" paramState={{ persist_month: newMonthParam }}>
      <ChevronLeftIcon className={twMerge("h-5 w-5", props.add > 0 && "rotate-180")} />
    </ParamLink>
  );
};

export const DatePicker = () => {
  const ctx = useAppContext();
  const search = useSearchParams2();

  const selectedDateValue = search.state.persist_date || ctx.date.todayParam;

  const selectedMonthValue = search.state.persist_month || ctx.date.monthParam;
  const month = getMonthObj(selectedMonthValue + "-01");

  const navigatingDateStr = search?.pendingState?.persist_date;

  const fetcher = useFetcher<typeof monthLoader>();
  const searchParams = search.params.toString();
  useEffect(() => {
    const timeoutId = setTimeout(() => fetcher.load(`/datepicker?${searchParams}`), 300);
    // console.log("running fetch again");
    return () => clearTimeout(timeoutId);
  }, [search.state.persist_month, search.state.persist_operator_id, search.state.persist_establishment_id]);

  return (
    <div className="min-w-[310px]">
      <div className="grid grid-cols-7">
        {weekDays.map((day, index) => {
          return (
            <div className="text-center font-bold capitalize" key={index}>
              {day.key}
            </div>
          );
        })}
        {month.weeks.map((week, weekIndex) => {
          return week.dates.map((dateValue, dayIndex) => {
            const foundDay = fetcher.data?.days.find((day) => day.day === dateValue);
            // const dayNumberDiff = dayIndex + weekIndex * nrOfDaysInWeek - startofMonthDate.getDay();
            // const date = addTimezoneOffset(addDays(startofMonthDate, dayNumberDiff), search.state.persist_timezone);
            // const dateValue = format(date, "yyyy-MM-dd");
            const date = toUtc(dateValue);

            const trips = (foundDay?.trips || []).filter(
              (trip) => !search.state.persist_toggle_establishment_ids.includes(trip.establishment_id),
            );
            const unscheduledBookings = uniqueBy(
              (foundDay?.unscheduled_participants || []).filter(
                (booking) => !search.state.persist_toggle_establishment_ids.includes(booking.establishment_id),
              ),
              (participant) => participant.booking_id,
            );
            // const unscheduledBookings = [];
            const hasEvent = !!(trips.length + unscheduledBookings.length);

            const isLoading = navigatingDateStr && navigatingDateStr === dateValue;

            const monthValue = dateValue.slice(0, 7);
            return (
              <div key={dayIndex} className="flex items-center justify-center">
                <ParamLink
                  preventScrollReset
                  // prefetch="intent"
                  path={"./"}
                  paramState={{ persist_date: dateValue, toggle_trip_templates_panel: undefined, filter_id: null }}
                  className={twMerge(
                    "relative flex h-10 w-10 items-center justify-center rounded-full p-3 text-center transition-colors",
                    isLoading && "cursor-progress",
                    dateValue === selectedDateValue ? "bg-primary font-bold text-white" : "hover:bg-slate-100",
                    dateValue === ctx.date.todayParam && "border-2 border-secondary",
                    monthValue !== selectedMonthValue && "opacity-50",
                    // hasEvent && "underline"
                  )}
                >
                  <span className={twMerge("bg-secondary-400 rounded-md p-3", hasEvent && "underline")}>{format(date, "d")}</span>
                  {isLoading && (
                    <div className="absolute inset-0 text-secondary-600">
                      <Spinner className="h-full w-full" />
                    </div>
                  )}
                </ParamLink>
              </div>
            );
          });
        })}
      </div>
    </div>
  );
};
