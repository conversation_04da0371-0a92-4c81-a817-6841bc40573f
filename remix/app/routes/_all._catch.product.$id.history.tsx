import { DataFunctionArgs } from "@remix-run/server-runtime";
import { AsyncReturnType } from "type-fest";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { getSessionSimple } from "~/utils/session.server";
import { unauthorized } from "~/misc/responses";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: DataFunctionArgs) => {
  const id = params.id!;
  const { session_id } = await getSessionSimple(request);
  const user = await activeUserSessionSimple(kysely, session_id, true).select("_user.editor").executeTakeFirst();
  if (!user?.editor) throw unauthorized();

  return kysely
    .selectFrom("entity_action")
    .where("entity_action.entity_id", "=", id)
    .innerJoin("user_event", "user_event.id", "entity_action.user_event_id")
    .innerJoin("user_session", "user_session.id", "user_event.user_session_id")
    .innerJoin("user", "user.id", "user_session.user_id")
    .selectAll("entity_action")
    .select(["user.email as user_email", "user_event.created_at"])
    .orderBy("user_event.created_at", "desc")
    .execute();
};

type LoaderResponse = AsyncReturnType<typeof loader>;

export default function Page() {
  const response = useLoaderData<LoaderResponse>();

  return (
    <div className="overflow-auto">
      <table>
        <thead>
          <tr className="">
            <td className="p-2">Action</td>
            <td className="p-2">At</td>
            <td className="p-2">By</td>
          </tr>
        </thead>
        {response.map((action) => {
          const actionData = action.data ? JSON.stringify(action.data, null, 4) : "";

          return (
            <tr key={action.id} className="border-t border-slate-200">
              <td className="p-2">{action.action_name}</td>
              <td className="p-2">{action.created_at}</td>
              <td className="p-2">{action.user_email}</td>
              <td className="p-2">
                <div className="max-w-xl overflow-x-scroll">
                  {actionData.length > 0 && actionData.length < 100 && <pre>{actionData}</pre>}
                </div>
              </td>
            </tr>
          );
        })}
      </table>
    </div>
  );
}
