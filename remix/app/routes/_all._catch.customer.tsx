import { redirect } from "@remix-run/server-runtime";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { getSessionSimple } from "~/utils/session.server";
import { ParamLink } from "~/components/meta/CustomComponents";
import { ChevronRightIcon, UserIcon } from "@heroicons/react/20/solid";
import { unauthorized } from "~/misc/responses";
import { activeUserSessionSimple, memberIsAdminOrOwnerQb } from "~/domain/member/member-queries.server";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import React, { Fragment } from "react";
import { _customerDetail, _participant_detail, _participant_mutate } from "~/misc/paths";
import type { StateInputKey } from "~/misc/parsers/global-state-parsers";
import { mergeStateToParams, paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { operatorQb } from "~/domain/operator/operator-queries";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { EstablishmentSeperator } from "~/domain/establishment/EstablishmentSeperator";
import { createPersonSearchQb, participantQb, participatingParticipantIdQb } from "~/domain/participant/participant.queries.server";
import { ErrorKey, findErrorMessage, getErrorMsgFromCatch } from "~/misc/error-translations";
import { addDays, differenceInCalendarDays, endOfYear, format, startOfYear } from "date-fns";
import { Alert } from "~/components/base/alert";
import { LoaderFunctionArgs } from "@remix-run/router";
import { ParticipantStatusBadges } from "~/domain/participant/participant.components";
import { SearchField, SearchForm } from "~/components/SearchForm";
import { formatDatetime } from "~/kysely/kysely-helpers";
import { getDateFromParams } from "~/domain/planning/planning.helpers.server";
import { ExportIcon } from "~/components/Icons";
import { toUtc } from "~/misc/date-helpers";
import { useAppContext } from "~/hooks/use-app-context";
import { pageLimits, Paging } from "~/components/Paging";
import { activities, ActivitySlug, activitySlugs, getActivity } from "~/domain/activity/activity";
import { flat, unique } from "remeda";
import { RSelect } from "~/components/ResourceInputs";
import { NavigatingSelect } from "~/components/NavigatingSelect";
import { showSiblingSelectPicker } from "~/utils/component-utils";

export { action } from "~/routes/_all._catch.resource";

const itemsPerPage = 60;

const infinityRange = "[,]";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);
  const activity = state.activity_slug;

  const date = getDateFromParams(request);
  const todayDate = toUtc(date.todayParam);

  const formatToDate = (date: Date) => format(date, "yyyy-MM-dd");

  const pageLimit = pageLimits.find((limit) => limit === state.page_limit) || pageLimits[0];

  const durationFilters = {
    today: formatToDate(todayDate),
    tomorrow: formatToDate(addDays(todayDate, 1)),
    thisYear: format(todayDate, "yyyy"),
    today30DaysLater: formatToDate(addDays(todayDate, 30)),
    today30DaysEarlier: formatToDate(addDays(todayDate, -30)),
    startOfThisYear: formatToDate(startOfYear(todayDate)),
    endOfThisYear: formatToDate(endOfYear(todayDate)),
  };

  const validateDate = (date: string | null | undefined) => {
    if (!date) return "";
    return `'${formatToDate(toUtc(date))}'`;
  };

  let range = `[${validateDate(state.persist_date_from)},${validateDate(state.persist_date_to)}]`;
  const messages: string[] = [];

  if (state.persist_date_from && state.persist_date_to) {
    const dateFrom = new Date(state.persist_date_from);
    const dateTo = new Date(state.persist_date_to);
    const differenceInDays = differenceInCalendarDays(dateTo, dateFrom);
    if (differenceInDays < 0) {
      range = infinityRange;
      messages.push("Start date cannot be bigger than the end date");
    }
  }

  const targetEstablishments = kysely
    .selectFrom("establishment")
    .where("establishment.operator_id", "=", state.persist_operator_id)
    .$if(!!state.persist_establishment_id, (eb) => eb.where("establishment.id", "=", state.persist_establishment_id))
    .$if(state.persist_toggle_establishment_ids.length > 0, (eb) =>
      eb.where("establishment.id", "not in", state.persist_toggle_establishment_ids),
    )
    .where(
      "establishment.id",
      "in",
      memberIsAdminOrOwnerQb({ trx: kysely, ctx: { session_id: session_id } }).select("_member.establishment_id"),
    );
  try {
    const allowedCtx = await activeUserSessionSimple(kysely, session_id, true)
      .select((eb) => {
        return [
          jsonObjectFrom(operatorQb(state)).as("operator"),
          jsonArrayFrom(
            targetEstablishments
              .leftJoin("spot", "spot.id", "establishment.spot_id")
              .leftJoin("region", "region.id", "spot.region_id")
              .selectAll("establishment")
              .select((establishmentEb) => {
                const activityFilterQb = establishmentEb
                  .selectFrom("sale_item")
                  .innerJoin("booking", "booking.id", "sale_item.booking_id")
                  .leftJoin("product", "product.id", "sale_item.product_id")
                  .leftJoin("item", "item.id", "product.item_id")
                  .$if(range !== infinityRange, (eb) => eb.where("sale_item.duration", "&&", range))
                  .$if(!!state.activity_slug, (eb) => eb.where("item.activity_slug", "=", state.activity_slug))
                  .where("booking.establishment_id", "=", establishmentEb.ref("establishment.id"));

                const customerIds = activityFilterQb
                  .innerJoin("participation", "participation.sale_item_id", "sale_item.id")
                  .innerJoin("participant", "participant.id", "participation.participant_id")
                  .select("participant.customer_id")
                  .union(
                    activityFilterQb.innerJoin("participant", "participant.booking_id", "booking.id").select("participant.customer_id"),
                  );

                const filteredResults = createPersonSearchQb(state.search)
                  .innerJoin("customer as s_customer", "s_customer.person_id", "s_person.id")
                  .where("s_customer.establishment_id", "=", establishmentEb.ref("establishment.id"))
                  .where("s_customer.id", "in", customerIds);

                return [
                  "spot.name as spot_name",
                  filteredResults
                    .clearOrderBy()
                    .select((eb) => eb.fn.count<number>("s_customer.id").as("count"))
                    .as("customer_count"),
                  jsonArrayFrom(
                    filteredResults
                      .select((customerEb) => [
                        "s_user.email",
                        "s_customer.id",
                        "s_customer.person_id",
                        "s_person.first_name",
                        "s_person.last_name",
                        "s_person.full_name",
                        jsonArrayFrom(
                          participantQb(kysely)
                            .where("participant.customer_id", "=", customerEb.ref("s_customer.id"))
                            .clearOrderBy()
                            .orderBy("participant.created_at", "desc"),
                        ).as("participants"),
                      ])
                      .offset(state.page_nr * pageLimit)
                      .limit(pageLimit),
                  ).as("customers"),
                  jsonArrayFrom(
                    participantQb(kysely)
                      .select((eb) => [
                        // incompleteFieldsQb(eb),
                        eb.selectFrom("form").select("form.name").where("form.id", "=", eb.ref("participant.form_id")).as("form_name"),
                        formatDatetime(eb.ref("participant.created_at"), "DD Mon YYYY", establishmentEb.ref("region.timezone")).as(
                          "created_at_formatted",
                        ),
                      ])
                      .where("participant.booking_id", "is", null)
                      .where("participant.id", "not in", participatingParticipantIdQb)
                      .where("customer.establishment_id", "=", establishmentEb.ref("establishment.id")),
                  ).as("pending_participants"),
                  jsonArrayFrom(
                    establishmentEb
                      .selectFrom("sale_item")
                      .innerJoin("booking", "booking.id", "sale_item.booking_id")
                      .where("booking.establishment_id", "=", establishmentEb.ref("establishment.id"))
                      .innerJoin("product", "product.id", "sale_item.product_id")
                      .innerJoin("item", "item.id", "product.item_id")
                      .select("item.activity_slug")
                      .distinct(),
                  ).as("all_activity_slugs"),
                ];
              }),
          ).as("establishments"),
          // eb.fn.count(eb.selectFrom('participant').innerJoin('user', 'user.id', 'participant.user_id').distinct('')).as('total'),,
        ];
      })
      .executeTakeFirst();

    if (!allowedCtx) throw unauthorized();

    return { ...allowedCtx, page_limit: pageLimit, filter: durationFilters, messages: messages };
  } catch (e) {
    console.error(e);
    const errorMsg = getErrorMsgFromCatch(e);
    if (errorMsg === ("range lower bound must be less than or equal to range upper bound" satisfies ErrorKey)) {
      const translatedMsg = findErrorMessage(errorMsg);
      const redirectUrl = new URL(url);
      mergeStateToParams(redirectUrl.searchParams, {
        error_message: translatedMsg,
      });
      throw redirect(redirectUrl.toString());
    }
    throw e;
  }
};

export const ReturningCustomerBadge = (props: { participant_count: number }) => {
  const participantCount = props.participant_count;
  if (participantCount <= 1) return <Fragment />;
  return (
    <div style={{ opacity: participantCount / 10 + 0.3 }} className="p-1 rounded-md text-xs text-center bg-secondary-700">
      {participantCount === 2 ? (
        <span>
          returning
          <br /> customer
        </span>
      ) : (
        <span>
          {participantCount}x<br />
          customer
        </span>
      )}
    </div>
  );
};

const DurationFilterButtons = () => {
  const response = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const durationFilterButtons = [
    {
      label: "Today",
      from: response.filter.today,
      to: response.filter.today,
    },
    {
      label: "Tomorrow",
      from: response.filter.tomorrow,
      to: response.filter.tomorrow,
    },
    {
      label: "Last 30 days",
      from: response.filter.today30DaysEarlier,
      to: response.filter.today,
    },
    {
      label: "Next 30 days",
      from: response.filter.today,
      to: response.filter.today30DaysLater,
    },
    {
      label: response.filter.thisYear,
      from: response.filter.startOfThisYear,
      to: response.filter.endOfThisYear,
    },
    {
      label: "All time",
      from: "",
      to: "",
    },
  ];

  return (
    <div className="flex flex-wrap gap-3">
      {durationFilterButtons.map((filterButton) => (
        <ParamLink
          key={filterButton.label}
          path={"./"}
          paramsActive={search.state.persist_date_from === filterButton.from && search.state.persist_date_to === filterButton.to}
          paramState={{
            persist_date_from: filterButton.from,
            persist_date_to: filterButton.to,
            rerender: search.state.rerender + 1,
          }}
          className="btn btn-basic aria-current:bg-secondary-500 aria-busy:spinner spinner-dark flex-1 whitespace-nowrap"
        >
          {filterButton.label}
        </ParamLink>
      ))}
    </div>
  );
};

export default function Page() {
  const app = useAppContext();
  const response = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const allActivitySlugs = unique(
    flat(response.establishments.map((establishment) => establishment.all_activity_slugs)).map((activity) => activity.activity_slug),
  );
  // const [fromDate, setFromDate] = useState('')
  // const [toDate, setToDate] = useState('')

  return (
    <div className="px-3 space-y-3">
      <div className="space-y-3 flex-1">
        <div className="flex flex-row justify-between gap-3">
          <h1 className="text-xl text-secondary font-bold">Customers</h1>
        </div>
        {response.messages.map((message) => (
          <Alert key={message} status={"error"}>
            {message}
          </Alert>
        ))}
        {!!search.state.error_message && <p className="p-2 rounded-md bg-red-500 text-white">{search.state.error_message}</p>}
        <SearchForm>
          <div className="space-y-3">
            <input type={"hidden"} name={"rerender" satisfies StateInputKey} value={search.state.rerender + 1} />
            <DurationFilterButtons />
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3" key={search.state.rerender}>
              <input
                name={"persist_date_from" satisfies StateInputKey}
                type={"date"}
                placeholder="Date from"
                className="input w-full"
                onChange={(e) => {
                  const dateFrom = e.target.value;
                  const dateToEl = e.target.form?.querySelector<HTMLInputElement>(
                    `input[name="${"persist_date_to" satisfies StateInputKey}"]`,
                  );
                  const dateTo = dateToEl?.value;

                  if (dateToEl) {
                    dateToEl.setCustomValidity("");
                  }
                  try {
                    if (dateFrom && dateTo && new Date(dateFrom) > new Date(dateTo)) {
                      e.target.setCustomValidity("Start date cannot be after end date");
                    } else {
                      e.target.setCustomValidity("");
                    }
                  } catch (e) {
                    console.log("could not validate because invalid date");
                  }
                }}
                defaultValue={search.state.persist_date_from || ""}
              />
              <input
                name={"persist_date_to" satisfies StateInputKey}
                type={"date"}
                placeholder="Date to"
                onChange={(e) => {
                  const dateTo = e.target.value;
                  const dateFromEl = e.target.form?.querySelector<HTMLInputElement>(
                    `input[name="${"persist_date_from" satisfies StateInputKey}"]`,
                  );
                  const dateFrom = dateFromEl?.value;

                  if (dateFromEl) {
                    dateFromEl.setCustomValidity("");
                  }

                  if (dateFrom && dateTo && new Date(dateFrom) > new Date(dateTo)) {
                    e.target.setCustomValidity("End date cannot be before start date");
                  } else {
                    e.target.setCustomValidity("");
                  }
                }}
                className="input w-full"
                defaultValue={search.state.persist_date_to || ""}
              />
              <SearchField className="col-span-2" />
            </div>
            <div className="flex flex-wrap">
              <div className={"flex flex-row rounded-md border-slate-200 border overflow-hidden"}>
                <button className=" text-slate-700 p-2 " type={"button"} onClick={showSiblingSelectPicker}>
                  Category
                </button>
                {/*<p>{allActivitySlugs.toString()}</p>*/}
                <NavigatingSelect
                  onChange={(value) => ({ activity_slug: value as ActivitySlug })}
                  className="flex flex-wrap gap-3 border-none"
                  defaultValue={search.state.activity_slug || ""}
                >
                  <option value="">All</option>
                  {activitySlugs
                    .filter((slug) => allActivitySlugs.includes(slug))
                    .map((slug) => {
                      const type = getActivity(slug);
                      return (
                        <option disabled={slug === search.state.activity_slug} value={slug} key={slug}>
                          {type.name}
                        </option>
                      );
                    })}
                </NavigatingSelect>
              </div>
            </div>
          </div>
        </SearchForm>
        <div className="space-y-5 py-5">
          {response.establishments.map((establishment) => {
            const customers = establishment.customers;
            const customerCount = establishment.customer_count as number;
            const nrOfPages = Math.ceil(customerCount / response.page_limit);
            const nextPage = search.state.page_nr + 1;
            const exportName = nrOfPages > 1 && customers.length + " (page " + nextPage + ")";
            const exportFileName = `customers-${app.date.todayParam.replace(/-/g, "")}-${customers.length}-page-${nextPage}.csv`;
            return (
              <div key={establishment.id} className="space-y-3">
                {response.establishments.length > 1 && <EstablishmentSeperator establishment={establishment} />}
                {establishment.pending_participants.length > 0 && (
                  <div className="space-y-3 border-primary border rounded-md p-3">
                    <p className="text-xl text-slate-500 font-semibold">Pending {establishment.pending_participants.length}</p>
                    <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-y-3 gap-x-6">
                      {establishment.pending_participants.map((participant) => {
                        return (
                          <ParamLink
                            key={participant.id}
                            path={_participant_detail(participant.id)}
                            className="flex flex-row gap-3 items-center hover:bg-slate-50 text-slate-800 rounded-md md:max-w-sm"
                          >
                            <div className="p-2 rounded-full bg-slate-200">
                              <UserIcon className="w-8 h-8 text-slate-600" />
                            </div>
                            <div className="flex-1">
                              <div className="flex flex-row gap-3 items-center pr-3">
                                <div className="flex-1">
                                  <p className="font-semibold line-clamp-1 break-all">
                                    {participant.first_name} {participant.last_name}
                                  </p>
                                  <p className="text-slate-500 text-xs font-semibold line-clamp-1 break-all">{participant.email}</p>
                                </div>
                                <ParticipantStatusBadges {...participant} />
                              </div>
                              <div
                                className="whitespace-nowrap grid p-1 bg-secondary-50 rounded-md text-xs gap-3 overflow-hidden"
                                style={{ gridTemplateColumns: "1fr auto" }}
                              >
                                <span className=" line-clamp-1 truncate overflow-ellipsis">{participant.form_name}</span>
                                <span className="text-slate-500 flex-shrink-0">{participant.created_at_formatted}</span>
                              </div>
                            </div>
                            <ChevronRightIcon className="w-5 h-5" />
                          </ParamLink>
                        );
                      })}
                    </div>
                  </div>
                )}

                <div className="flex flex-row gap-1 items-center">
                  <p className="text-slate-500 text-xl font-semibold">
                    {customers.length === 0 ? <span>No Results</span> : <span>{customerCount} results</span>}
                  </p>
                  <button
                    className="btn gap-1 text-primary hover:text-primary-700 whitespace-nowrap"
                    type={"button"}
                    onClick={() => {
                      try {
                        const headers = ["email", "name"].join(",");
                        const rows = establishment.customers.map((customer) => [customer.email, customer.full_name].join(",")).join("\n");

                        const csvContent = headers + "\n" + rows;

                        const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8" });
                        const a = document.createElement("a");
                        const downloadUrl = URL.createObjectURL(blob);
                        a.href = downloadUrl;
                        a.download = exportFileName;
                        a.style.display = "none";
                        document.body.appendChild(a);

                        a.click();

                        document.body.removeChild(a);

                        URL.revokeObjectURL(downloadUrl);
                      } catch (e) {
                        console.error("export error", e);
                        window.alert("export error");
                      }
                    }}
                  >
                    <ExportIcon className="w-6 h-6" />
                    Export {exportName}
                  </button>
                  {false && (
                    <ParamLink path={_participant_mutate} className="link" paramState={{ persist_establishment_id: establishment.id }}>
                      create
                    </ParamLink>
                  )}
                </div>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-y-3 gap-x-6">
                  {customers.map((customer, index) => {
                    const participantCount = customer.participants.length;
                    return (
                      <ParamLink
                        key={customer.id}
                        path={_customerDetail(customer.id)}
                        className="flex flex-row gap-3 items-center hover:bg-slate-50 text-slate-800 rounded-md md:max-w-sm"
                      >
                        <div className="p-2 rounded-full bg-slate-200 flex-shrink-0">
                          <UserIcon className="w-8 h-8 text-slate-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="font-semibold line-clamp-1">
                            {customer.first_name} {customer.last_name}
                          </p>
                          <p className="text-slate-500 text-xs line-clamp-1">{customer.email}</p>
                        </div>
                        <ReturningCustomerBadge participant_count={participantCount} />
                        <ChevronRightIcon className="w-5 h-5 flex-shrink-0" />
                      </ParamLink>
                    );
                  })}
                </div>
                <Paging totalCount={customerCount} pageLimit={response.page_limit} />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
