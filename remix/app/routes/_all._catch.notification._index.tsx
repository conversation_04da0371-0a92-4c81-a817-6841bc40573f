import { useLoaderD<PERSON> } from "@remix-run/react";
import { LoaderFunctionArgs } from "@remix-run/router";
import { SubmitButton } from "~/components/base/Button";
import { ActionForm } from "~/components/form/BaseFrom";
import { ParamLink } from "~/components/meta/CustomComponents";
import { pageLimits, Paging } from "~/components/Paging";
import { RInput } from "~/components/ResourceInputs";
import { upcomingRemindersQuery } from "~/domain/email/participation-query.server";
import { isEditorQb, memberIsAdminQb, toArgs } from "~/domain/member/member-queries.server";
import { useAppContext } from "~/hooks/use-app-context";
import { kysely } from "~/misc/database.server";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { _booking_detail, _participant_detail } from "~/misc/paths";
import { getSessionSimple } from "~/utils/session.server";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);
  const { session_id } = await getSessionSimple(request);

  const pageLimit = pageLimits.find((limit) => limit === state.page_limit) || pageLimits[0];

  const query = upcomingRemindersQuery.where((eb) => {
    return state.persist_establishment_id
      ? eb.and([
          eb("establishment.id", "=", state.persist_establishment_id),
          eb("establishment.id", "in", memberIsAdminQb(toArgs(kysely, session_id), "write").select("_member.establishment_id")),
        ])
      : eb.exists(isEditorQb(toArgs(kysely, session_id)).select("_user.editor"));
  });

  const participations = await query
    .offset(state.page_nr * pageLimit)
    .limit(pageLimit)
    .execute();

  const allParticipations = await query
    .clearSelect()
    .clearOrderBy()
    .select((eb) => eb.fn.count("participation.id").as("count"))
    .executeTakeFirstOrThrow();

  const totalCount = Number(allParticipations.count);

  return {
    participations,
    totalCount,
    pageLimit,
  };
};

export default function UpcomingNotifications() {
  const { participations, totalCount, pageLimit } = useLoaderData<typeof loader>();
  const ctx = useAppContext();

  return (
    <div className="space-y-4">
      <div className="overflow-auto">
        <div style={{ gridTemplateColumns: "repeat(6, minmax(0, auto))" }} className="grid gap-4 text-sm min-w-max">
          <div className="font-semibold contents">
            <div className="whitespace-nowrap">Type</div>
            <div className="whitespace-nowrap">Status</div>
            <div className="whitespace-nowrap">Scheduled at</div>
            <div className="whitespace-nowrap">Name</div>
            <div className="whitespace-nowrap">Email</div>
            <div className="whitespace-nowrap">Activity Start Date</div>
            {/* <div>Duration</div> */}
            {/*<div>Msg</div>*/}
          </div>
          {participations.map((participation) => (
            <div key={participation.participation_id} className="contents">
              <div className="whitespace-nowrap">Activity Reminder</div>
              <div className="whitespace-nowrap">
                {participation.ready_to_send ? (
                  "Sending in next batch"
                ) : (
                  <div className="flex flex-row gap-3">
                    Upcoming
                    <ActionForm>
                      <RInput table={"mail"} field={"data.participant_id"} value={participation.participant_id} type={"hidden"} />
                      <RInput table={"mail"} field={"data.sale_item_id"} value={participation.sale_item_id} type={"hidden"} />
                      <RInput table={"mail"} field={"data.to_email"} value={participation.participant_email} type={"hidden"} />
                      <RInput table={"mail"} field={"data.success"} hiddenType={"__boolean__"} type={"hidden"} value={""} />
                      <SubmitButton className="link">Cancel</SubmitButton>
                    </ActionForm>
                    <ActionForm confirmMessage={`Are you sure you want to send the activity reminder email to ${participation.participant_email} now?`}>
                      <RInput table={"mail"} field={"data.participant_id"} value={participation.participant_id} type={"hidden"} />
                      <RInput table={"mail"} field={"data.sale_item_id"} value={participation.sale_item_id} type={"hidden"} />
                      <RInput table={"mail"} field={"data.to_email"} value={participation.participant_email} type={"hidden"} />
                      <RInput table={"mail"} field={"data.success"} hiddenType={"__boolean__"} type={"hidden"} value={"true"} />
                      <SubmitButton className="link">Send Now</SubmitButton>
                    </ActionForm>
                  </div>
                )}
              </div>
              <div className="whitespace-nowrap">
                {participation.scheduled_local_datetime_formatted} ({participation.timezone})<br />
                {ctx.editor && <span>{participation.scheduled_date_time_in_utc} (UTC)</span>}
              </div>
              <div className="whitespace-nowrap">
                <ParamLink path={_participant_detail(participation.participant_id)} className="link">
                  {participation.participant_full_name}
                </ParamLink>
              </div>
              <div className="whitespace-nowrap">{participation.participant_email}</div>
              <div className="whitespace-nowrap">
                <ParamLink className="link" path={_booking_detail(participation.booking_id)}>
                  {participation.activity_start_date_formatted}
                </ParamLink>
              </div>
              {/* <div>{participation.duration}</div> */}
              {/*<div></div>*/}
            </div>
          ))}
        </div>
      </div>

      <Paging totalCount={totalCount} pageLimit={pageLimit} />
    </div>
  );
}
