import { Trans } from "@lingui/react/macro";
import { t } from "@lingui/core/macro";
import { ikUrl, useIkUrl } from "~/components/IkImage";
import type { ReactNode } from "react";
import { useEffect, useState } from "react";
import { useLoaderData, useNavigation } from "@remix-run/react";
import { BlueBox } from "~/components/base/base";
import { kysely } from "~/misc/database.server";
import { LinksFunction, redirect } from "@remix-run/server-runtime";
import { SearchButton } from "~/routes/search";
import type { DivingCourseTagKey } from "~/data/diving";
import { divingCourseTagKeys } from "~/data/diving";
import { _explore_base, _operator_detail } from "~/misc/paths";
import { ProductItem } from "~/domain/product/ProductItem";
import { RiWhatsappFill } from "react-icons/ri";
import { GrMail } from "react-icons/gr";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { bgUrl } from "~/misc/helpers";
import { activeActivitySlugs, activities } from "~/domain/activity/activity";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { createPageOverwrites } from "~/misc/consts";
import { useAppContext } from "~/hooks/use-app-context";
import { getWhitelabelFromHost } from "~/utils/domain-helpers";
import type { LoaderFunctionArgs } from "@remix-run/router";
import { useLingui } from "@lingui/react";
import { getHost } from "~/misc/web-helpers";
import { createMeta } from "~/misc/route-helpers";
import { ParamLink } from "~/components/meta/CustomComponents";

const heroImages = [
  { path: "/images/diver-swimming-with-fish-school.jpeg", query: "tr:q-80,h-570" },
  { path: "/images/amed_bali1.jpeg", query: "tr:q-80,h-570" },
  { path: "/images/banyuwedang_bay_bali.jpeg", query: "tr:q-80,h-570" },
];

const beginnerButtons: Record<
  DivingCourseTagKey,
  {
    title: string;
    description: ReactNode;
    image: string;
    className: string;
  }
> = {
  first_time_diver: {
    title: "Wanna try if diving is for \nyou?",
    description: `Get a first taste of what it's like to \nbe a diver`,
    image: ikUrl("/images/people-taking-diving-lesson-indoor.jpeg", "tr:q-80,h-300,w-570"),
    className: "from-primary via-primary/90",
  },
  ready_to_start: {
    title: "Ready to start your \ntraining?",
    description: "Get certified and start your life-long \nunderwater adventure",
    image: ikUrl("/images/group-of-divers-underwater.jpeg", "tr:q-80,h-300,w-570"),
    className: "from-secondary-700 via-secondary-700/90",
  },
};

const ikQuery = "tr:q-80,h-570,w-1980,c-at_least";

export const links: LinksFunction = () => [
  ...heroImages.map((image) => ({
    rel: "preload",
    as: "image",
    href: ikUrl(image.path, ikQuery),
  })),
  ...Object.values(beginnerButtons).map((item) => ({
    rel: "prefetch",
    as: "image",
    href: item.image,
  })),
];

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const host = getHost(request);
  const prefix = getWhitelabelFromHost(host);
  if (prefix) throw redirect(_operator_detail(prefix));
  const data = await kysely
    .selectNoFrom([
      jsonArrayFrom(
        kysely
          .selectFrom("spot")
          .innerJoin("region", "region.id", "spot.region_id")
          .where("region.published", "=", true)
          .select((eb) => [
            "spot.id",
            "spot.name",
            eb
              .exists(
                eb
                  .selectFrom("establishment")
                  .whereRef("establishment.spot_id", "=", "spot.id")
                  .where("establishment.published", "=", true)
                  .select("establishment.id"),
              )
              .as("published"),
            jsonArrayFrom(fileTargetsQb(kysely, "spot", eb.ref("spot.id"))).as("files"),
          ])
          .groupBy(["spot.id", "spot.name"]),
      ).as("spots"),
      jsonArrayFrom(kysely.selectFrom("diving_course").where("tag", "in", divingCourseTagKeys).limit(50).select(["id", "tag"])).as(
        "diving_courses",
      ),
    ])
    .executeTakeFirstOrThrow();

  return {
    ...data,
    ...createPageOverwrites({ show_whatsapp: true }),
  };
};

export const meta = createMeta({ title: "Explore Bali’s underwater world with traveltruster", robots: "index" });

export default function Page() {
  const context = useAppContext();
  const { state, pendingState, params } = useSearchParams2();
  const [activeImage, setActiveImage] = useState(0);
  const { fromBucket } = useIkUrl();
  useEffect(() => {
    const timeout = setTimeout(() => {
      setActiveImage((activeImage + 1) % heroImages.length);
    }, 7000);
    return () => clearTimeout(timeout);
  }, [activeImage]);
  const response = useLoaderData<typeof loader>();

  const navigation = useNavigation();

  // if (typeof response === "string") return <div>{response}</div>;

  const currentLoadingTo = navigation.location && navigation.location.pathname + navigation.location.search;

  const spots = context.editor ? response.spots : response.spots.filter((spot) => spot.published);
  const i18 = useLingui();

  return (
    <div>
      <div className="relative flex h-96 content-center items-center object-cover md:h-[570px]">
        {heroImages.map((image, index) => (
          <img
            key={image.path}
            src={ikUrl(image.path, ikQuery)}
            className={`absolute h-full w-full object-cover transition-all duration-1000 ${
              index === activeImage ? "opacity-100" : "opacity-0"
            }`}
          />
        ))}
        <div className="app-container relative h-auto space-y-2">
          <h1 className="text-3xl font-semibold tracking-wide text-white drop-shadow-md md:text-5xl">
            <Trans>Explore Bali underwater</Trans>
          </h1>
          <p className="tracking-wider text-white drop-shadow-md md:text-xl">{t(i18.i18n)`Research & booking made easier`}</p>
          <SearchButton />
        </div>
      </div>
      <div className="app-container space-y-6 py-3 md:py-6">
        <div>
          <div className="flex flex-wrap items-center gap-2">
            <h2 className="text-2xl font-bold text-slate-800">Select your activity</h2>
            {/*<EditorLink />*/}
          </div>
          <div className="pt-3">
            <div className="grid grid-cols-2 gap-3 md:grid-cols-4 md:gap-6">
              {activeActivitySlugs.map((slug) => {
                const activity = activities[slug];
                return (
                  <ParamLink
                    className={`overflow-hidden rounded-xl border bg-secondary-50 hover:opacity-80 active:opacity-80 
                      aria-busy::animate-pulse aria-busy:cursor-progress aria-busy:opacity-80`}
                    prefetch={"intent"}
                    path={_explore_base}
                    aria-busy={navigation.location?.pathname === _explore_base && pendingState?.activity_slug === slug}
                    paramState={{ activity_slug: slug }}
                    key={slug}
                  >
                    <img
                      alt={activity.name}
                      src={ikUrl(`images/activities/${slug}.webp`, `tr:w-540,h-400`)}
                      className="h-[100px] w-full object-cover md:h-[150px]"
                    />
                    <p className="max-w-[350px] p-2 text-center text-xs font-bold line-clamp-1 md:p-3 md:text-sm">{activity.name}</p>
                  </ParamLink>
                );
              })}
            </div>
          </div>
        </div>
        <h2 className="text-2xl font-bold text-slate-800">Explore locations</h2>
        <div>
          <div className="grid grid-cols-2 gap-3 md:grid-cols-3 md:gap-6">
            {spots.map((spot) => {
              const imgPath = spot.files?.[0]?.filename || "";
              return (
                <ParamLink
                  key={spot.id}
                  path={_explore_base}
                  paramState={{ spot_id: spot.id }}
                  aria-busy={navigation.location?.pathname === _explore_base && pendingState?.spot_id === spot.id}
                  style={{
                    backgroundImage: bgUrl(fromBucket(imgPath, "tr:q-80,h-400,w-540")),
                  }}
                  prefetch={"intent"}
                  className={`block overflow-hidden rounded-xl bg-slate-600 bg-cover bg-center hover:opacity-80 active:opacity-80
                  aria-busy:animate-pulse aria-busy:cursor-progress aria-busy:opacity-80 
                  `}
                >
                  <div className="h-16 md:h-28" />
                  <div className="flex flex-col bg-gradient-to-t from-black/80 via-black/50 pt-3">
                    <p className="py-3 text-center text-sm font-bold text-white line-clamp-1">{spot.name}</p>
                  </div>
                </ParamLink>
              );
            })}
          </div>
        </div>
        <h2 className="text-2xl font-bold text-slate-700">For beginner divers</h2>
        <div className="grid gap-3 text-white md:grid-cols-2">
          {divingCourseTagKeys.map((tagKey) => {
            const divingCourseTag = beginnerButtons[tagKey];
            const divingCourseIds = response.diving_courses
              .filter((divingCourse) => divingCourse.tag === tagKey)
              .map((divingCourse) => divingCourse.id);

            return (
              <ParamLink
                key={tagKey}
                prefetch="intent"
                aria-busy={navigation.location?.pathname === _explore_base && pendingState?.diving_course_tag === tagKey}
                className={`block overflow-hidden rounded-xl bg-cover transition-opacity hover:opacity-80 active:opacity-80
                aria-busy:animate-pulse aria-busy:cursor-progress aria-busy:opacity-70`}
                path={_explore_base}
                paramState={{
                  diving_courses: divingCourseIds,
                  diving_course_tag: tagKey,
                  activity_slug: "diving-course",
                }}
                style={{ backgroundImage: bgUrl(divingCourseTag.image) }}
              >
                <div className={`flex h-52 flex-col gap-2 bg-gradient-to-r p-3 md:h-64 ${divingCourseTag.className}`}>
                  <p className="whitespace-pre-wrap text-2xl font-semibold">{divingCourseTag.title}</p>
                  <p className="whitespace-pre-wrap">{divingCourseTag.description}</p>
                  <div className="flex-1" />
                  <div>
                    <div className="inline-block rounded-xl bg-slate-100 p-3 font-semibold text-slate-500">Browse activities</div>
                  </div>
                </div>
              </ParamLink>
            );
          })}
        </div>

        <h2 className="pt-4 text-2xl font-bold text-slate-700 md:text-3xl">Your next adventure in 3 easy steps</h2>
        <div className="flex flex-row gap-6 pl-1">
          <ol className="max-w-md space-y-6 pt-3">
            <li className="flex flex-row items-center gap-4">
              <p className="w-10 text-center text-5xl font-bold text-slate-600">1</p>
              <div className="w-full space-y-2 rounded-2xl border border-slate-200 p-3">
                <p className="pb-1 text-xl font-bold text-slate-800">Choose your activity</p>
                <BlueBox className="rounded-xl rounded-t-none border-t-0 p-1" />
                <ProductItem
                  className="rounded-xl"
                  to_currency="EUR"
                  locale={null}
                  item={{
                    title: "Diving with Mantas at Manta Point",
                    activity_slug: "fun-diving",
                    diving_courses: [],
                    diving_count: 2,
                    diving_locations: [{ name: "Penida / Lembongan" }],
                    product_prices: [{ amount: 85, currency_id: "EUR" }],
                    to_currency: "EUR",
                    gear_included: true,
                    pickup: true,
                    duration_in_hours: "[7,)",
                  }}
                />
              </div>
            </li>
            <li className="flex flex-row items-center gap-3">
              <p className="w-10 text-center text-5xl font-bold text-slate-600">2</p>
              <div className="flex-1 space-y-2 rounded-2xl border border-slate-200 p-3">
                <p className="text-xl font-bold text-slate-800">Directly chat with operators</p>
                <div className="flex flex-row items-center justify-center gap-3">
                  <p>Through WhatsApp or Email</p>
                  <div className="flex-1" />
                  <RiWhatsappFill className="text-4xl text-green-500" />
                  <GrMail className="rounded-xl text-4xl text-secondary-500" />
                </div>
                <div className="btn btn-primary w-full rounded-xl">Make an inquiry</div>
              </div>
            </li>
            <li className="flex flex-row items-center gap-3">
              <p className="w-10 text-center text-5xl font-bold text-slate-600">3</p>
              <div className="flex-1 space-y-2 rounded-2xl border border-slate-200 p-3">
                <p className="pb-1 text-xl font-bold text-slate-800">Start your adventure</p>
                <div className="w-full overflow-hidden rounded-xl bg-secondary-50">
                  <img alt="diving into water" height={100} src={"/images/diver-2.jpeg"} className="h-[95px] object-cover object-center" />
                </div>
              </div>
            </li>
          </ol>
          <div className="w-[315px] max-md:hidden">
            <img src={ikUrl("/images/smartphone-with-traveltruster-inquiry-screen.png", "tr:q-80")} alt={""} />
          </div>
        </div>
      </div>
    </div>
  );
}
