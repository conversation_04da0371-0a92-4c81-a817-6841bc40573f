import { redirect } from "@remix-run/server-runtime";
import { kysely } from "~/misc/database.server";
import { notFound } from "~/misc/responses";
import { _establishment_detail, _product_detail } from "~/misc/paths";
import { LoaderFunctionArgs } from "@remix-run/router";
import { at_infinity_value } from "~/kysely/db-static-vars";

export const loader = async ({ params }: LoaderFunctionArgs) => {
  const code = params.code!;

  const foundProduct = await kysely
    .selectFrom("product")
    .where((eb) =>
      eb.or([eb("product.id", "=", code), eb.and([eb("product.root_id", "=", code), eb("product.deleted_at", "=", at_infinity_value)])]),
    )
    .select("product.root_id")
    .executeTakeFirst();

  if (foundProduct) {
    return redirect(_product_detail(foundProduct.root_id));
  }

  const foundEstablishment = await kysely.selectFrom("establishment").select("establishment.id").where("id", "=", code).executeTakeFirst();

  if (foundEstablishment) {
    return redirect(_establishment_detail(foundEstablishment.id));
  }

  throw notFound();
};
