import React, { Fragment, ReactNode, useEffect } from "react";
import { Outlet } from "@remix-run/react";
import { AnimatingDiv, Spacer } from "~/components/base/base";
import { Trans } from "~/components/Trans";
import { MapInitPortal } from "~/components/MapPortal";
import { SecretForm, SecretLock } from "~/routes/_all._catch.secret";
import { PolicyLink, TermsLink } from "~/components/shared";
import { ParamLink } from "~/components/meta/CustomComponents";
import { BoxLogoCase } from "~/components/BoxLogo";
import { CurrencySwitch } from "~/components/CurrencySwitch";
import { AccountTopmenu } from "~/components/account/AccountTopmenu";
import { Divider } from "~/components/Divider";
import { traveltruserWhatsappNr, traveltrusterName } from "~/misc/consts";
import { useBoolean } from "~/hooks/use-boolean";
import { RiWhatsappFill } from "react-icons/ri";
import { TrashIcon, XMarkIcon } from "@heroicons/react/20/solid";
import { twMerge } from "tailwind-merge";
import { usePageOverwrites } from "~/utils/remix";
import { HamburgerMenuButton, HamburgerPanel } from "~/components/HamburgerMenu";
import { ActionAlert } from "~/components/ActionAlert";
import { fileUploadFormId } from "~/misc/vars";
import { ActionForm } from "~/components/form/BaseFrom";
import { useAppContext } from "~/hooks/use-app-context";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { Feature } from "~/components/Feature";
import { toast } from "~/misc/toast";
import { Toggle } from "~/components/Checker";
import { useShowCustomerToggle } from "~/hooks/use-show-customer-toggle";
import { ViewportDebug } from "~/components/debug";
import { useOptimisticMenu } from "~/hooks/use-optimistic-menu";
import { flat, sumBy } from "remeda";
import { SidePanel, SidePanelHeading } from "~/components/SidePanel";
import { SubmitButton } from "~/components/base/Button";
import { RInput } from "~/components/ResourceInputs";
import { getProductTitle } from "~/domain/product/ProductItem";
import { _item_detail } from "~/misc/paths";
import { OperationInput } from "~/components/form/DefaultInput";
import { BsCart } from "react-icons/bs";
import { IkImage } from "~/components/IkImage";

export { action } from "~/routes/_all._catch.resource";

const whatsappUrl = `https://api.whatsapp.com/send?phone=${encodeURIComponent(traveltruserWhatsappNr)}`;
// const whatsappUrl = `https://api.whatsapp.com/send?phone=${whatsappNr}`;

const WhatsappButton = (props: { className?: any; children?: ReactNode }) => {
  return (
    <a
      target="_blank"
      rel="noreferrer"
      href={whatsappUrl}
      className={twMerge("flex flex-row items-center gap-2 bg-slate-100 py-2 px-3", props.className)}
    >
      <RiWhatsappFill className="h-10 w-10 text-green-500" />
      <div>
        <p className="text-sm font-semibold">Chat with us</p>
        <p className="text-xs">on whatsapp</p>
      </div>
      {props.children}
    </a>
  );
};

const WhatsAppButtonFixed = () => {
  const fixed = useBoolean(true);
  const mergedData = usePageOverwrites();
  const finalWhatsappFixed = fixed.isOn ? mergedData.whatsapp_fixed : fixed.isOn;

  return (
    <div className={twMerge("flex flex-col print:hidden", finalWhatsappFixed ? "fixed bottom-0 right-0 pb-3 pr-3" : "relative")}>
      <WhatsappButton className={finalWhatsappFixed ? "rounded-full shadow-md" : "rounded-full"}>
        {finalWhatsappFixed && (
          <button
            type="button"
            hidden={!finalWhatsappFixed}
            className="p-1 text-slate-800"
            onClick={(e) => {
              e.preventDefault();
              fixed.off();
            }}
          >
            <XMarkIcon className="h-5 w-5 drop-shadow-md" />
          </button>
        )}
      </WhatsappButton>
    </div>
  );
};

const AccountVerifyBar = () => {
  const ctx = useAppContext();
  if (!ctx.email) return <Fragment />;

  if (ctx.verified_at) return <Fragment />;

  return (
    <ParamLink paramState={{ toggle_modal: "account" }} className="block bg-yellow-100 p-3">
      Verify {ctx.email}
    </ParamLink>
  );
};

const Cart = () => {
  const app = useAppContext();

  const total = sumBy(flat(app.cart_bookings.map((booking) => booking.activities)), (activity) => activity.quantity);
  if (!total) return <Fragment />;

  return (
    <div>
      {/*<ParamLink paramState={{ toggle_modal: "cart" }} className="btn text-primary relative">*/}
      {/*  <span className="text-xs text-slate-400 absolute bg-white border-2 scale-75 border-slate-400 rounded-full w-4 h-4 right-3.5 top-0 flex justify-center items-center">*/}
      {/*    {total}*/}
      {/*  </span>*/}
      {/*  <ShoppingCartIcon className="w-6 h-6 relative" />*/}
      {/*</ParamLink>*/}
      <ParamLink paramState={{ toggle_modal: "cart" }} className="btn text-primary relative">
        <BsCart className="w-6 h-6 relative" />
        <span className="text-xs absolute text-primary scale-90 rounded-full bg-white right-5 px-0.5 top-1 flex justify-center items-center">
          {total}
        </span>
      </ParamLink>
      <SidePanel dialogname={"cart"} className="w-full md:w-96">
        <div className="h-full w-full md:w-96 bg-white overflow-auto">
          <div className="py-5">
            <SidePanelHeading>
              <h2 className="text-xl font-bold">Cart</h2>
            </SidePanelHeading>
            <div className="space-y-5 p-5">
              {app.cart_bookings.map((booking) => {
                return (
                  <div key={booking.id} className="space-y-3">
                    <div className="space-y-3">
                      {booking.activities.map((activity) => {
                        const product = activity.product;
                        const firstImage = product?.files?.[0];
                        return (
                          <div key={activity.id} className="flex flex-row gap-3 items-center">
                            {/*<RInput table={} field={}*/}
                            <div>{firstImage && <IkImage w={50} h={50} path={firstImage.filename} />}</div>
                            <div className="flex-1">
                              {product && (
                                <ParamLink path={_item_detail(product.item_id)} paramState={{ product_id: product.id }}>
                                  {getProductTitle(product) || product.sku || product.id}
                                </ParamLink>
                              )}
                            </div>
                            <ActionForm className="flex flex-row gap-3 items-center">
                              <RInput table={"sale_item"} field={"id"} index={activity.id} value={activity.id} />
                              <RInput
                                table={"sale_item"}
                                field={"data.quantity"}
                                index={activity.id}
                                defaultValue={activity.quantity}
                                className="input w-20"
                                type={"number"}
                              />
                            </ActionForm>
                            <ActionForm>
                              <RInput table={"sale_item"} field={"id"} value={activity.id} />
                              <OperationInput table={"sale_item"} value={"delete"} />
                              {activity.participation_ids?.map((id) => (
                                <Fragment key={id}>
                                  <RInput table={"participation"} field={"id"} value={id} />
                                  <OperationInput table={"participation"} value={"delete"} />
                                </Fragment>
                              ))}
                              <SubmitButton>
                                <TrashIcon className="w-4 h-4 text-slate-700" />
                              </SubmitButton>
                            </ActionForm>
                          </div>
                        );
                      })}
                      <SubmitButton className="btn btn-primary ">Update</SubmitButton>
                    </div>
                    <SubmitButton className="btn btn-primary">Checkout</SubmitButton>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </SidePanel>
    </div>
  );
};

export default function Page() {
  const app = useAppContext();
  const search = useSearchParams2();
  const overwrites = usePageOverwrites();
  const showCustomerToggle = useShowCustomerToggle();
  const menu = useOptimisticMenu();

  useEffect(() => {
    const elementActions = search.state.element_action;
    try {
      elementActions.forEach((elementId) => {
        const element = document.getElementById(elementId);
        if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
          element.focus();
          element.setSelectionRange(element.value.length, element.value.length);
        }
        if (element instanceof HTMLSelectElement) {
          element.focus();
        }
        if (element instanceof HTMLDialogElement) {
          if (element.open) {
            element.close();
          } else {
            element.showModal();
          }
        }
      });
    } catch (e) {
      console.error(e);
    }
  }, [search.state.element_action.toString(), search.state.rerender]);

  useEffect(() => {
    const elementActions = search.state.element_clear;
    elementActions.forEach((elementId) => {
      const element = document.getElementById(elementId);
      if (element instanceof HTMLInputElement || element instanceof HTMLSelectElement) {
        element.value = "";
      }
    });
  }, [search.state.element_clear.toString(), search.state.rerender]);

  useEffect(() => {
    if (app.flash_success_message) {
      toast(app.flash_success_message);
    }
  }, [app.flash_success_message]);

  if (!app.passed)
    return (
      <div className={"mx-auto max-w-screen-md py-6"}>
        <SecretForm />
      </div>
    );

  const showMenu = !overwrites.simple_view && (!app.operator || !!app.members.length || app.editor);

  return (
    <div className="min-h-svh flex flex-col">
      <ViewportDebug />
      <ActionForm id={fileUploadFormId} />
      <AccountVerifyBar />
      <div className={twMerge(menu.pinned ? "md:flex md:flex-row md:flex-1" : "flex-1 flex-col flex")}>
        {showMenu && <HamburgerPanel />}
        <div className={twMerge("flex flex-col flex-1", overwrites.fixed_width && "overflow-auto")}>
          <div>
            <header
              className={twMerge(
                "container relative flex max-w-6xl flex-row items-center py-3 px-0 transition-colors",
                search.state.print_friendly && "print:hidden",
              )}
            >
              {showMenu ? <HamburgerMenuButton /> : <div className="w-3"></div>}
              <BoxLogoCase />
              <div className="flex-1" />
              <div className="flex flex-row items-center">
                {showCustomerToggle && (
                  <div className="pr-2">
                    <ParamLink
                      paramState={{ customer_view: !search.state.customer_view }}
                      className="gap-1 flex flex-row items-center group aria-checked:text-primary max-md:hidden"
                      aria-checked={search.state.customer_view}
                    >
                      <span className="max-md:hidden">Customer View</span>
                      <Toggle />
                    </ParamLink>
                  </div>
                )}
                <SecretLock />
                <Feature feature={"i18"}>
                  <ParamLink replace paramState={{ toggle_modal: "language" }} className="uppercase relative">
                    {app.locale}
                  </ParamLink>
                </Feature>
                {overwrites.show_currency_swap && <CurrencySwitch />}
                <Cart />
                <AccountTopmenu />
              </div>
            </header>
          </div>
          <div>
            <MapInitPortal />
          </div>
          <div className={"flex flex-col space-y-3 flex-1"}>
            <div>
              <Outlet />
            </div>
            <div className="flex-1" />
            <Divider />
            {!overwrites.simple_view && !app.operator && (
              <footer
                className={twMerge("app-container flex flex-row justify-between pb-6 pt-3", search.state.print_friendly && "print:hidden")}
              >
                <div>
                  <ul>
                    <li>
                      <ParamLink path={"/"} className="text-gray-600 hover:underline">
                        <Trans>Home</Trans>
                      </ParamLink>
                    </li>
                    <li>
                      <TermsLink className="text-gray-600" />
                    </li>
                    <li>
                      <PolicyLink className="text-gray-600" />
                    </li>
                    <li>
                      <ParamLink path={"/contact"} className="text-gray-600 hover:underline">
                        <Trans>Leave feedback / Contact</Trans>
                      </ParamLink>
                    </li>
                  </ul>
                  <p className="pt-1 text-sm text-slate-400">
                    &copy; {new Date(app.date.todayParam).getFullYear()} {traveltrusterName}
                  </p>
                </div>
                {overwrites.show_whatsapp && <WhatsAppButtonFixed />}
              </footer>
            )}
          </div>
        </div>
      </div>
      <AnimatingDiv className="fixed inset-x-0 top-0 z-10">
        <ActionAlert />
      </AnimatingDiv>
      <div
        className={twMerge(
          showCustomerToggle && search.state.customer_view
            ? "bg-yellow-200 sticky left-0 bottom-0 z-90 text-center px-1 text-slate-700"
            : "hidden",
        )}
      >
        Customer View
      </div>
    </div>
  );
}

const ErrorLayout = (props: { children: ReactNode; topRight?: ReactNode }) => {
  return (
    <div className={"flex flex-col"}>
      <div className="container relative z-10 mx-auto flex max-w-6xl flex-row space-x-3 p-3 md:px-0">
        {/*<BoxLogo />*/}
        <Spacer />
      </div>
      <div className="app-container">{props.children}</div>
    </div>
  );
};

// export const ErrorBoundary = () => (
//   <ErrorLayout>
//     <DefaultErrorBoundary />
//   </ErrorLayout>
// );
