import { kysely } from "~/misc/database.server";
import { sqid } from "~/kysely/kysely-helpers";
import { unauthorized } from "~/misc/responses";
import { notifyPage } from "~/utils/firebase.server";
import { _booking_detail } from "~/misc/paths";
import { ActionFunctionArgs } from "@remix-run/node";
import { LoaderFunctionArgs } from "@remix-run/router";
import { Head, Html, Link, Section, Text } from "@react-email/components";
import { TailwindBody } from "~/components/Mail";
import { getFullUrl, urlFromSegments } from "~/misc/helpers";
import { createMim, getMailContextForPrefix } from "~/server/mail/email.client.server";
import React from "react";
import { getInvoice } from "~/domain/payment/xendit-client.server";
import { createCallbacksAndSendCloudTasks } from "~/server/utils/google-task.server";
import { getHost } from "~/misc/web-helpers";
import { getWhitelabelFromHost } from "~/utils/domain-helpers";
import { updateBookingCache } from "~/domain/booking/booking-resource";

export const action = async (args: ActionFunctionArgs) => {
  const invoice = await args.request.json();
  const callbackToken = args.request.headers.get("x-callback-token");
  const xenditUserId = invoice.user_id;
  const xenditInvoiceId = invoice.id;
  const externalId = invoice.external_id;
  const invoiceStatus = invoice.status;

  if (!callbackToken || !xenditUserId || !xenditInvoiceId || !externalId) throw unauthorized();

  if (externalId === "invoice_123124123") {
    console.log("Test Webhook");
    return { success: true };
  }

  const item = await kysely
    .selectFrom("payment")
    .innerJoin("booking", "booking.id", "payment.booking_id")
    .innerJoin("establishment", "establishment.id", "booking.establishment_id")
    .innerJoin("xendit_account", "xendit_account.id", "payment.xendit_account_id")
    .innerJoin("xendit_platform", "xendit_platform.id", "xendit_account.xendit_platform_id")
    .innerJoin("xendit_environment", "xendit_environment.xendit_platform_id", "xendit_platform.id")
    .leftJoin("invoice", "invoice.booking_id", "booking.id")
    .where("payment.id", "=", externalId)
    .whereRef("xendit_environment.production", "=", "xendit_account.production")
    .where("xendit_account.xendit_user_id", "=", xenditUserId)
    .where("xendit_account.xendit_invoice_callback_token", "=", callbackToken)
    .select((eb) => [
      "payment.id as payment_id",
      "payment.payed_at",
      "payment.xendit_invoice_id",
      "establishment.id as establishment_id",
      "xendit_environment.xendit_api_key",
      "establishment.email as establishment_email",
      "booking.id as booking_id",
      "booking.booking_reference as booking_booking_reference",
      "booking.id_seq as booking_id_seq",
      "invoice.id as invoice_id",
      "payment.host",
      sqid(eb.ref("booking.id_seq")).as("booking_sqid"),
    ])
    .executeTakeFirstOrThrow();

  if (item.payed_at) return { success: true };

  if (invoiceStatus === "PAID" || invoiceStatus === "SETTLED") {
    const invoice: any = await getInvoice({
      apiKey: item.xendit_api_key,
      invoiceId: xenditInvoiceId,
      headers: { "for-user-id": xenditUserId },
    });

    const updatedPayment = await kysely
      .updateTable("payment")
      .where("payment.id", "=", item.payment_id)
      .set({ payed_at: invoice.paid_at })
      .executeTakeFirstOrThrow();

    await updateBookingCache(kysely, item.booking_id);

    if (item.invoice_id) {
      await createCallbacksAndSendCloudTasks(kysely, {
        name: "send_invoice_to_intuit",
        target_id: item.invoice_id,
        delay_in_seconds: 5,
        host: getHost(args.request),
      });
    }

    const url = new URL(args.request.url);
    const baseUrl = getFullUrl(item.host || url.host);

    const toEmail = item.establishment_email;
    if (toEmail) {
      const bookingRef = item.booking_booking_reference || item.booking_sqid;
      const { msg, send } = await createMim(
        <Html>
          <Head />
          <TailwindBody>
            <Section>
              <Text>You received a payment for booking {bookingRef}</Text>
              <Link href={urlFromSegments(baseUrl, _booking_detail(item.booking_id))}>
                {urlFromSegments(baseUrl, _booking_detail(item.booking_id))}
              </Link>
              <Text>
                Amount: {invoice.amount} {invoice.currency}
                <br />
                Paid at: {invoice.paid_at}
                <br />
                Payment method: {invoice.payment_method} (xendit)
              </Text>
            </Section>
          </TailwindBody>
        </Html>,
      );
      const mailCtx = await getMailContextForPrefix(kysely, getWhitelabelFromHost(item.host || ""));
      msg.setSubject("Payment received for booking " + bookingRef);
      msg.setSender(mailCtx.sender);
      msg.setTo(toEmail);
      await send();
    }

    await notifyPage(_booking_detail(item.booking_id));
  } else {
    console.log("nothing to update");
  }

  return { success: true };
};

export const loader = async (args: LoaderFunctionArgs) => {
  return { success: true };
};
