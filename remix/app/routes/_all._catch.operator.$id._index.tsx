import { redirect } from "@remix-run/server-runtime";
import type { AsyncReturnType } from "type-fest";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { _establishment_detail } from "~/misc/paths";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { filesQuery } from "~/domain/file/file-queries";
import { notFound } from "~/misc/responses";
import { getWhitelabelFromHost } from "~/utils/domain-helpers";
import { EstablishmentItem } from "~/domain/establishment/EstablishmentItem";
import React from "react";
import { establishmentQb } from "~/domain/establishment/queries";
import { LoaderFunctionArgs } from "@remix-run/router";
import { getHost } from "~/misc/web-helpers";
import { isUuidRegex } from "~/misc/helpers";
import { ParamLink } from "~/components/meta/CustomComponents";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const idOrSlug = params.id;
  const prefix = getWhitelabelFromHost(getHost(request));
  if (typeof idOrSlug !== "string") throw notFound("invalid id");
  const isUuid = isUuidRegex.test(idOrSlug);
  const operator = await kysely
    .selectFrom("operator as _operator")
    .where(isUuid ? "_operator.id" : "_operator.slug", "=", idOrSlug)
    .selectAll("_operator")
    .select((eb) => [
      jsonArrayFrom(establishmentQb.where("establishment.operator_id", "=", eb.ref("_operator.id"))).as("establishments"),
      jsonArrayFrom(filesQuery("operator_logo").where("file_target.target_id", "=", eb.ref("_operator.id"))).as("logo_files"),
    ])
    .executeTakeFirstOrThrow();

  const firstEstablishment = operator.establishments[0];
  if (prefix && firstEstablishment && operator.establishments.length === 1) throw redirect(_establishment_detail(firstEstablishment.id));
  return operator;
};

type LoaderResponse = AsyncReturnType<typeof loader>;

export default function Page() {
  const operator = useLoaderData<LoaderResponse>();

  return (
    <div className="app-container space-y-6 py-6">
      <h1 className="text-2xl font-bold">{operator.name}</h1>
      <div className="grid md:grid-cols-2 gap-6">
        {operator.establishments.map((establishment) => (
          <ParamLink
            path={_establishment_detail(establishment.id)}
            key={establishment.id}
            className="rounded-md bg-slate-50 hover:bg-slate-100 hover:underline active:underline"
          >
            <EstablishmentItem item={establishment} />
          </ParamLink>
        ))}
      </div>
    </div>
  );
}
