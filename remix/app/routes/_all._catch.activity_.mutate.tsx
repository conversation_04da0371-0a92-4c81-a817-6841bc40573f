import { RIn<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ct, RTextarea } from "~/components/ResourceInputs";
import { SubmitButton } from "~/components/base/Button";
import { ActionAlert } from "~/components/ActionAlert";
import { AnimatingDiv, Backbutton } from "~/components/base/base";
import { getProductTitle, ProductItem } from "~/domain/product/ProductItem";
import { HiddenTypeInput, OperationInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { _booking_detail, _form_mutate } from "~/misc/paths";
import React, { Fragment, useId, useState } from "react";
import { activities, activitySlugs, getActivity } from "~/domain/activity/activity";
import { fName, tableIdRef } from "~/misc/helpers";
import { ActionForm } from "~/components/form/BaseFrom";
import { getSessionSimple } from "~/utils/session.server";
import type { QbArgs } from "~/domain/member/member-queries.server";
import { memberIsAdminQb } from "~/domain/member/member-queries.server";
import { kysely } from "~/misc/database.server";
import { bookingQb } from "~/domain/booking/booking-queries";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { createPageOverwrites } from "~/misc/consts";
import { paramsToRecord, toggleArray } from "~/misc/parsers/global-state-parsers";
import { notFound, unauthorized } from "~/misc/responses";
import {
  baseProductQb,
  divingCoursesJsonEb,
  divingLevelsJsonEb,
  divingLocationsJsonEb,
  orderProduct,
  pricesJsonEb,
  selectMaxDurationInHours,
} from "~/domain/product/product-queries.server";
import { useLoaderData } from "@remix-run/react";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { ParamLink } from "~/components/meta/CustomComponents";
import type { Role } from "~/domain/planning/plannings-consts";
import { participantsNrArray } from "~/domain/planning/plannings-consts";
import type { AddonBase } from "~/kysely/db";
import { mapValueToRange } from "~/components/form/RangeInput";
import { defaultCurrency } from "~/misc/vars";
import { MeetingInput } from "~/domain/booking/booking-components";
import { Trans } from "~/components/Trans";
import { AddonUnit, addonUnits, formatAddonText } from "~/domain/addon/addon";
import { arrayFrom } from "~/domain/planning/plannings-helpers";
import { establishmentQb } from "~/domain/establishment/queries";
import { CallbackInput } from "~/domain/callback/callback.components";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { useAppContext } from "~/hooks/use-app-context";
import { saleItemWithProductQb } from "~/domain/activity/activity-queries";
import { Tooltip } from "~/components/base/tooltip";
import { ArrowUturnLeftIcon, CheckIcon, InformationCircleIcon, TrashIcon, UserIcon, XMarkIcon } from "@heroicons/react/20/solid";
import { formatDuration } from "~/domain/activity/activity.helpers";
import { participantQb, participatingParticipantIdQb } from "~/domain/participant/participant.queries.server";
import { LoaderFunctionArgs } from "@remix-run/router";
import { twMerge } from "tailwind-merge";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { arrayAgg, upper } from "~/kysely/kysely-helpers";
import { getCategoryLabel, useProductLists } from "~/domain/product/product-categories";
import { disableScrollOnNumberInput } from "~/utils/component-utils";
import { DebugContainer } from "~/components/debug";
import { BookingMessage } from "~/domain/booking/booking-message";
import { uniqueBy } from "remeda";
import { CheckDoneIcon } from "~/components/Icons";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const ctx = await getSessionSimple(request);

  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  const args: QbArgs = {
    trx: kysely,
    ctx: { session_id: ctx.session_id },
  };

  const productQb = baseProductQb
    .selectAll("item")
    .selectAll("product")
    .select([divingCoursesJsonEb, divingLocationsJsonEb, divingLevelsJsonEb, selectMaxDurationInHours, pricesJsonEb])
    .select((eb) => [
      jsonObjectFrom(
        eb
          .selectFrom("form")
          .selectAll("form")
          .where("form.root_id", "=", eb.ref("item.form_root_id"))
          .where("form.deleted_at", "=", at_infinity_value)
          .select("form.id"),
      ).as("form"),
      eb
        .selectFrom("product__tag")
        .where("product__tag.product_id", "=", eb.ref("product.id"))
        .select((eb) => arrayAgg(eb.ref("product__tag.tag_id"), "uuid").as("tag_ids"))
        .as("tag_ids"),
    ])
    .$call((eb) => orderProduct(eb));

  const establishmentQ = establishmentQb.select((establishmentEb) => [
    jsonArrayFrom(
      establishmentEb.selectFrom("tag").where("tag.establishment_id", "=", establishmentEb.ref("establishment.id")).selectAll("tag"),
    ).as("tags"),
    establishmentEb
      .exists(memberIsAdminQb(args).where("_member.establishment_id", "=", establishmentEb.ref("establishment.id")))
      .as("allowed"),
    jsonArrayFrom(
      productQb.where((eb) => {
        const activeCmpr = eb("product.deleted_at", "=", at_infinity_value);
        const showCmpr = state.product_id ? eb.or([eb("product.id", "=", state.product_id), activeCmpr]) : activeCmpr;
        const establishmentCmpr = eb.and([eb("item.establishment_id", "=", establishmentEb.ref("establishment.id")), showCmpr]);
        if (!state.id) return establishmentCmpr;
        const activityProductIdEb = eb.selectFrom("sale_item").where("sale_item.id", "=", state.id).select("sale_item.product_id");
        const activityProductCmpr = eb("product.id", "=", activityProductIdEb);
        return eb.or([establishmentCmpr, activityProductCmpr]);
      }),
    ).as("products"),
    jsonArrayFrom(
      participantQb(kysely)
        .where("participant.booking_id", "is", null)
        .where("participant.id", "not in", participatingParticipantIdQb)
        .where("customer.establishment_id", "=", establishmentEb.ref("establishment.id")),
    ).as("pending_participants"),
  ]);

  const bookingQ = bookingQb(kysely).select((eb) => [
    jsonObjectFrom(establishmentQ.where("establishment.id", "=", eb.ref("booking.establishment_id"))).as("establishment"),
    eb
      .selectFrom("sale_item")
      .whereRef("sale_item.booking_id", "=", "booking.id")
      .select((eb) => eb.fn.max(upper(eb.ref("sale_item.duration"))).as("max_date"))
      .as("max_end_date"),
    jsonArrayFrom(
      saleItemWithProductQb
        .select((eb) =>
          jsonArrayFrom(
            eb.selectFrom("participation").selectAll("participation").where("participation.sale_item_id", "=", eb.ref("sale_item.id")),
          ).as("participations"),
        )
        .where("sale_item.booking_id", "=", eb.ref("booking.id")),
    ).as("activities"),
  ]);

  const context = await kysely
    .selectNoFrom([
      jsonObjectFrom(establishmentQ.where("establishment.id", "=", state.establishment_id || null)).as("establishment"),
      jsonObjectFrom(bookingQ.where("booking.id", "=", state.booking_id || null)).as("booking"),
      jsonObjectFrom(
        saleItemWithProductQb.where("sale_item.id", "=", state.id || null).select((eb) => [
          jsonObjectFrom(eb.selectFrom("form").where("form.id", "=", eb.ref("sale_item.form_id"))).as("form"),
          jsonObjectFrom(bookingQ.where("booking.id", "=", eb.ref("sale_item.booking_id"))).as("booking"),
          jsonObjectFrom(productQb.where("product.id", "=", eb.ref("sale_item.product_id"))).as("product"),
          jsonArrayFrom(
            eb.selectFrom("participation").selectAll("participation").whereRef("participation.sale_item_id", "=", "sale_item.id"),
          ).as("participations"),
          // eb.exists(memberQb(args).where("_member.establishment_id", "=", eb.ref("booking.establishment_id"))).as("simple_view"),
        ]),
      ).as("activity"),
    ])
    .executeTakeFirst();

  if (!context) throw notFound();
  const activity = context.activity;
  const booking = activity?.booking || context.booking;
  // if (!booking) throw notFound("Booking required to create activity");
  const establishment = booking?.establishment || context.establishment;
  if (!establishment) throw notFound("Establishment is required to create activity");
  const products = establishment.products || [];

  if (!establishment.allowed) throw unauthorized();

  return {
    activity: activity,
    booking: booking,
    establishment: establishment,
    products: products,
    ...createPageOverwrites({ simple_view: false }),
  };
};

const hiddenAddonFields: Array<keyof AddonBase> = ["name", "unit"];

const TakeOver = (props: { onUpdate: () => void }) => {
  const [used, setUsed] = useState<boolean | null>(null);

  if (used)
    return (
      <p className="bg-yellow-100 text-slate-700 flex flex-row gap-3 pl-4 p-2 items-center">
        <CheckDoneIcon className="w-4 h-4 text-green-600" />
        <span>Updated, submit to save changes</span>
        <span className="flex-1"></span>
        <button
          type={"button"}
          className="btn btn-icon text-slate-500"
          onClick={() => {
            setUsed(false);
          }}
        >
          <XMarkIcon className="w-4 h-4" />
        </button>
      </p>
    );

  if (used === false) {
    return <Fragment />;
  }

  return (
    <p className=" py-3 pl-4 px-2 bg-yellow-100 text-slate-700 flex flex-row items-center gap-2 md:gap-5 justify-between">
      <span>New selection has a different price and/or duration. Press 'Update' to apply the new values.</span>
      <button
        type={"button"}
        className="btn border-slate-400 text-slate-400 border md:px-6 py-1"
        onClick={() => {
          setUsed(true);
          props.onUpdate();
        }}
      >
        Update
      </button>
      <span className="flex-1"></span>
      <button
        type={"button"}
        className="btn btn-icon text-slate-500"
        onClick={() => {
          setUsed(false);
        }}
      >
        <XMarkIcon className="w-4 h-4" />
      </button>
    </p>
  );
};

const SelectNrOfParticipants = (props: { defaultValue: number }) => {
  const search = useSearchParams2();
  const [nrOfParticipants, setNrOfParticipants] = useState(props.defaultValue);
  const tripId = search.state.trip_id;
  return (
    <div className="flex-1 flex-wrap gap-3">
      <div>
        <label className={"required"}>Nr of participants</label>
        <br />
        <select className="select w-full" required value={nrOfParticipants} onChange={(e) => setNrOfParticipants(Number(e.target.value))}>
          {participantsNrArray.map((nr) => (
            <option key={nr} value={nr + ""}>
              {nr}
            </option>
          ))}
        </select>
        <RInput table={"sale_item"} field={"data.quantity"} value={nrOfParticipants} type={"hidden"} />
        {Array.from({ length: nrOfParticipants }).map((_, index) => (
          <Fragment key={index}>
            <RInput table={"participation"} field={"data.sale_item_id"} index={index} value={tableIdRef("sale_item")} type={"hidden"} />
            {tripId && (
              <Fragment>
                <RInput table={"trip_assignment"} field={"data.trip_id"} index={index} type={"hidden"} value={tripId} />
                <RInput table={"trip_assignment"} field={"data.role"} index={index} type={"hidden"} value={"instructor" satisfies Role} />
                <RInput
                  table={"trip_assignment"}
                  field={"data.participation_id"}
                  index={index}
                  type={"hidden"}
                  value={tableIdRef("participation", index)}
                />
              </Fragment>
            )}
          </Fragment>
        ))}
      </div>
    </div>
  );
};

export default function Page() {
  const context = useAppContext();
  const search = useSearchParams2();
  const [manualAddonIds, setManualAddonIds] = useState<Record<string, boolean>>({});
  const data = useLoaderData<typeof loader>();
  const booking = data.booking;
  const establishment = data.establishment;
  const activity = data.activity;
  const otherActivites = booking?.activities.filter((item) => item.id !== activity?.id) || [];

  const pax = activity?.participations.length || 0;
  const openSlots = activity?.participations.filter((p) => !p.participant_id).length || 0;

  const productId = search.state.product_id ?? activity?.product_id;
  const establishmentId = search.state.establishment_id;
  const selectedProduct = data.products.find((product) => product.id === productId);
  const selectedProductPrice = selectedProduct?.product_prices[0];

  const durationInputId = useId();
  const startDateInputId = useId();

  const duration = mapValueToRange(selectedProduct?.duration_in_hours);
  const finalDuration = duration[0] || duration[1];
  const durationInDays = finalDuration && Math.ceil(Number(finalDuration) / 24);

  const startTime = activity?.duration_start || booking?.max_end_date || context.date.dateParam || "";
  // const endTime = booking && mapToDuration(booking.duration)[1];
  const establishmentAddons = establishment.addons.map((addon) => ({
    ...addon,
    id: null,
    price_amount: addon.price?.amount || 0,
    addon_id: addon.id,
  }));

  const addonsFromProduct = establishmentAddons.filter((addon) => selectedProduct?.addon_ids?.includes(addon.addon_id));

  const baseAddons = activity?.activity_addons || addonsFromProduct || [];
  const manualAddons = establishmentAddons.filter((addon) => manualAddonIds[addon.addon_id]);
  const activityAddons = uniqueBy([...baseAddons, ...manualAddons], (addon) => addon.addon_id);

  const [currency, setCurrency] = useState<string>("");
  const finalCurrency =
    currency || booking?.currency_id || selectedProductPrice?.currency_id || establishment.default_currency || defaultCurrency;

  const defaultNrOfParticipns = search.state.participant_ids.length || otherActivites[0]?.participations.length || 2;

  const pendingParticipants = establishment.pending_participants;
  const stays = pendingParticipants
    .filter((participant) => search.state.participant_ids.includes(participant.id))
    .map((participant) => participant.stay)
    .filter((stay): stay is string => !!stay);

  const lists = useProductLists(data);

  const currencyObj = context.currencies.find((c) => c.id === finalCurrency);
  const currencyDecimals = currencyObj?.decimals ?? 2;

  const step = Math.pow(10, -currencyDecimals);

  return (
    <main className="app-container space-y-3">
      <div>
        <h2 className="text-xl font-semibold text-slate-600">{getEstablishmentName(establishment)}</h2>
        {booking && <p className="text-slate-600">Booking reference: {booking.booking_reference || booking.sqid}</p>}
      </div>
      {otherActivites.length > 0 && (
        <div className="text-slate-500">
          <h3 className="text text-xl font-semibold">Other activities in this booking:</h3>
          {otherActivites.map((item) => (
            <p key={item.id} className="flex flex-row justify-between gap-3">
              {item.product ? (
                <span>
                  {getActivity(item.product.activity_slug).name}, {getProductTitle(item.product)}
                </span>
              ) : (
                <span>Custom</span>
              )}
              <span>{formatDuration(item)}</span>
            </p>
          ))}
        </div>
      )}
      <AnimatingDiv>
        <ActionForm className="space-y-2" replace>
          <ActionAlert />
          <div>
            <h1 className="text-2xl font-semibold">{activity ? "Edit activity" : booking ? "Create activity" : "Create booking"}</h1>
          </div>
          {!activity && pendingParticipants.length > 0 && (
            <div className="border-red-500 p-3 rounded-md border space-y-3">
              <h2>Connect pending registrations to this activity</h2>
              {pendingParticipants.map((participant) => {
                const fieldIndex = participant.id;
                const isChecked = search.state.participant_ids.includes(participant.id);
                return (
                  <ParamLink
                    paramState={{ participant_ids: toggleArray(search.state.participant_ids, participant.id) }}
                    key={participant.id}
                    className="flex items-center gap-2 text-left hover:cursor-pointer group"
                    aria-selected={isChecked}
                  >
                    {isChecked && (
                      <Fragment>
                        <RInput table={"participant"} field={"id"} value={participant.id} index={fieldIndex} />
                        <RInput
                          table={"participant"}
                          field={"data.booking_id"}
                          index={fieldIndex}
                          value={tableIdRef("booking")}
                          type={"hidden"}
                        />
                        <OperationInput value={"update"} table={"participation"} index={fieldIndex} />
                        <RInput
                          table={"participation"}
                          field={"data.participant_id"}
                          index={fieldIndex}
                          value={participant.id}
                          type={"hidden"}
                        />
                        <RInput
                          table={"participation"}
                          field={"data.sale_item_id"}
                          index={fieldIndex}
                          value={tableIdRef("sale_item")}
                          type={"hidden"}
                        />
                      </Fragment>
                    )}
                    <span className="rounded-md w-5 h-5 group-aria-selected:bg-primary bg-white flex items-center justify-center transition-colors border border-slate-500 group-aria-selected:border-primary">
                      <CheckIcon className="w-4 h-4 hidden group-aria-selected:block text-white" />
                    </span>
                    <div className="text-slate-800 bg-slate-200 rounded-full p-0.5">
                      <UserIcon className="w-6 h-6" />
                    </div>
                    <div>
                      <p className="font-semibold">
                        {participant.first_name} {participant.last_name}
                      </p>
                      <p className="text-xs text-slate-600">{participant.email}</p>
                    </div>
                    <span className="flex-1" />
                  </ParamLink>
                );
              })}
            </div>
          )}
          {selectedProduct && selectedProductPrice ? (
            <Fragment>
              <p className="mb-2 font-bold">
                {activities[selectedProduct.activity_slug].name}
                {!!pax && (
                  <span className="font-normal text-slate-500">
                    &nbsp; {pax} pax ({openSlots} open slot{openSlots !== 1 && "s"})
                  </span>
                )}
              </p>
              <ParamLink type={"button"} paramState={{ product_id: "" }} className="group relative w-full overflow-hidden rounded-md">
                <ProductItem
                  className="rounded-md border-2 border-secondary-500"
                  item={selectedProduct}
                  to_currency={selectedProductPrice.currency_id}
                  locale={establishment.locale}
                />
                <div className="absolute inset-0 flex items-center justify-center rounded-md bg-black text-center text-white opacity-0 transition-opacity group-hover:opacity-30">
                  <span>Remove</span>
                </div>
              </ParamLink>
            </Fragment>
          ) : productId ? (
            <Fragment>
              <p className="mb-2 font-bold">Selected baseproduct</p>
              <p>None selected or the product does not exist anymore</p>
            </Fragment>
          ) : (
            <Fragment>
              <p className="mb-2 font-bold w-fit relative">
                Select baseproduct
                <input
                  required
                  className="opacity-0 absolute inset-0"
                  title={"Please select a base product"}
                  onInvalid={(e) => {
                    console.log("run invalid");
                    e.currentTarget?.setCustomValidity("Please select a base product");
                  }}
                />
              </p>
              <div className="grid grid-cols-3 md:grid-cols-6 gap-3">
                {lists.filtered_product_types
                  .filter((slug) => lists.all.find((product) => product.activity_slug === slug))
                  .map((slug) => (
                    <ParamLink
                      key={slug}
                      paramState={{ activity_slug: slug, categories: [] }}
                      aria-selected={slug === search.state.activity_slug}
                      className="btn btn-basic aria-selected:bg-primary aria-selected:text-white text-center md:min-h-16"
                    >
                      {activities[slug].name}
                    </ParamLink>
                  ))}
              </div>
              <div className="flex flex-wrap gap-3">
                {lists.filtered_product_type_categories
                  .filter((cat) => getCategoryLabel(cat))
                  .map((category) => (
                    <ParamLink
                      key={category}
                      paramState={{ categories: toggleArray(search.state.categories, category) }}
                      aria-selected={search.state.categories.includes(category)}
                      className="btn btn-basic w-fit py-1 md:py-1.5 aria-selected:bg-primary aria-selected:text-white text-center capitalize text-sm font-normal"
                    >
                      {getCategoryLabel(category)}
                    </ParamLink>
                  ))}
                {lists.tags.map((tag) => (
                  <ParamLink
                    key={tag.id}
                    paramState={{ tags: toggleArray(search.state.tags, tag.id) }}
                    aria-selected={search.state.tags.includes(tag.id)}
                    className="btn btn-basic border py-1 md:py-1.5 border-slate-400 w-fit aria-selected:bg-secondary-tag aria-selected:text-white text-sm font-normal text-center "
                  >
                    {tag.name}
                  </ParamLink>
                ))}
              </div>
              <div className="space-y-2 pt-6">
                {activitySlugs.map((slug) => {
                  const products = lists.filtered.filter((product) => product.activity_slug === slug);
                  if (!products.length) return <Fragment key={slug} />;
                  return (
                    <div className="space-y-1">
                      <p className="text-lg font-semibold">{activities[slug].name}</p>
                      <div className="grid gap-3 md:grid-cols-2">
                        {products.map((product) => {
                          const firstPrice = product.product_prices[0];
                          return (
                            <ParamLink className="hover:opacity-60" key={product.id} paramState={{ product_id: product.id }}>
                              <ProductItem
                                item={product}
                                className={twMerge("rounded-md", product.deleted_at !== at_infinity_value && "bg-slate-100")}
                                to_currency={firstPrice?.currency_id}
                                locale={establishment.locale}
                              />
                            </ParamLink>
                          );
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>
            </Fragment>
          )}
          {activity && activity.product_id !== productId && selectedProductPrice && (
            <TakeOver
              onUpdate={() => {
                const durationInDaysInput = document.getElementsByName(fName("sale_item", "data.duration", 0, "duration"))[0];
                if (durationInDaysInput instanceof HTMLInputElement) {
                  durationInDaysInput.value = durationInDays ? durationInDays + "" : "";
                }
                const activityPriceInput = document.getElementsByName(fName("sale_item", "data.price_pp"))[0];
                if (activityPriceInput instanceof HTMLInputElement) {
                  activityPriceInput.value = (selectedProductPrice.amount || 0) + "";
                }
                setCurrency(selectedProductPrice.currency_id || defaultCurrency);
              }}
            />
          )}
          {activity?.product_id !== productId && (
            <Fragment>
              <RInput table={"sale_item"} field={"data.form_id"} type={"hidden"} value={selectedProduct?.form?.id || ""} />
              <DebugContainer>
                {selectedProduct?.form ? (
                  <ParamLink
                    path={_form_mutate}
                    className="link"
                    paramState={{
                      id: selectedProduct?.form.id,
                      establishment_id: selectedProduct.form.establishment_id,
                    }}
                  >
                    {selectedProduct?.form?.name}
                  </ParamLink>
                ) : (
                  <span>No form</span>
                )}
              </DebugContainer>
            </Fragment>
          )}
          {activity && <RInput table={"sale_item"} field={"id"} value={activity.id} />}
          {booking ? (
            <Fragment>
              <RInput table={"booking"} field={"id"} value={booking.id} />
            </Fragment>
          ) : (
            <Fragment>
              <CallbackInput callbackName={"notify_booking_page"} target_id={tableIdRef("booking")} />
              <RInput table={"booking"} field={"data.establishment_id"} type={"hidden"} value={establishmentId || ""} />
            </Fragment>
          )}

          <RInput table={"sale_item"} field={"data.product_id"} type={"hidden"} value={productId || ""} />
          <RedirectParamsInput
            path={_booking_detail(activity?.booking_id || tableIdRef("booking"))}
            paramState={{ persist_date: fName("sale_item", "data.duration", 0, "date") }}
          />
          {(!!activity || !!selectedProduct) && (
            <div className="space-y-3">
              {!booking && (
                <RInput
                  table={"booking"}
                  field={"data.booking_reference"}
                  label={"Booking reference"}
                  className="input"
                  defaultValue={""}
                />
              )}
              {!activity && <RInput table={"sale_item"} field={"data.booking_id"} type={"hidden"} value={tableIdRef("booking")} />}
              {!activity && !booking && (
                <RInput table={"booking"} field={"data.establishment_id"} type={"hidden"} value={establishment.id} />
              )}
              {!activity && <SelectNrOfParticipants key={defaultNrOfParticipns} defaultValue={defaultNrOfParticipns} />}
              <div className="space-y-3">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {!booking && (
                    <div className="md:col-span-2">
                      <div className="grid md:grid-cols-4 grid-cols-1 gap-3">
                        <MeetingInput
                          operator_address={establishment?.address}
                          default={{
                            ...(booking || {
                              meeting_type: establishment?.default_booking_meeting_type,
                              meeting_time: establishment.default_booking_meeting_time,
                            }),
                            participant_stays: stays,
                          }}
                          country_code={establishment.country_code || ""}
                        />
                      </div>
                    </div>
                  )}
                  <div className="flex flex-row gap-3">
                    <div className="flex-1">
                      <label htmlFor={startDateInputId}>Start date:</label>
                      <br />
                      <input
                        id={startDateInputId}
                        type="date"
                        required
                        className={"input"}
                        name={fName("sale_item", "data.duration", 0, "date")}
                        defaultValue={startTime}
                      />
                    </div>
                    <div className="flex-1">
                      <label htmlFor={durationInputId} className="required">
                        Duration in days
                      </label>
                      <br />
                      <input
                        key={productId}
                        required
                        min={1}
                        id={durationInputId}
                        onWheel={disableScrollOnNumberInput}
                        name={fName("sale_item", "data.duration", 0, "duration")}
                        type="number"
                        defaultValue={activity?.duration_in_days || durationInDays || ""}
                        className="input"
                      />
                      <HiddenTypeInput name={fName("sale_item", "data.duration")} value={"__pg_daterange"} />
                    </div>
                  </div>
                  {/*{!booking && (*/}
                  {/*  <div>*/}
                  {/*    <label htmlFor={meetingTimeInputId}>Meeting time</label>*/}
                  {/*    <br />*/}
                  {/*    <input*/}
                  {/*      required*/}
                  {/*      className="input"*/}
                  {/*      id={meetingTimeInputId}*/}
                  {/*      name={fName("booking", "data.meeting_time")}*/}
                  {/*      defaultValue={establishment.default_booking_meeting_time || ""}*/}
                  {/*      type="time"*/}
                  {/*    />*/}
                  {/*  </div>*/}
                  {/*)}*/}
                  <div>
                    <RLabel table={"sale_item"} field={"data.price_pp"} className="required">
                      Price p.p.
                    </RLabel>
                    <br />
                    <div className="flex w-full items-center flex-wrap gap-3">
                      <RInput
                        type="number"
                        className="input flex-1"
                        table={"sale_item"}
                        field={"data.price_pp"}
                        step={step}
                        onWheel={disableScrollOnNumberInput}
                        required
                        defaultValue={activity?.price_pp ?? selectedProduct?.product_prices[0]?.amount ?? ""}
                      />
                      <RSelect
                        className="select w-fit"
                        required
                        table={"booking"}
                        field={"data.currency_id"}
                        // disabled={!!booking}
                        value={finalCurrency}
                        onChange={(e) => setCurrency(e.target.value)}
                      >
                        {context.currencies.map((currency) => (
                          <option key={currency.id} value={currency.id}>
                            {currency.id}
                          </option>
                        ))}
                      </RSelect>
                      <Tooltip description={"Changing the currency here will change it for all products/addons of this booking"}>
                        <InformationCircleIcon className="w-5 h-5" />
                      </Tooltip>
                    </div>
                  </div>
                  <div>
                    <RLabel table={"sale_item"} field={"data.discount_percentage"}>
                      Discount
                    </RLabel>
                    <fieldset className="formcontrol flex flex-row items-center gap-2 rounded-md border focus:border-slate-800">
                      <RInput
                        table={"sale_item"}
                        field={"data.discount_percentage"}
                        className="input-clean flex-1 p-2.5"
                        type="number"
                        onWheel={disableScrollOnNumberInput}
                        required
                        defaultValue={activity?.discount_percentage || 0}
                      />
                      <span className="px-3">%</span>
                    </fieldset>
                  </div>
                </div>
                {!booking && (
                  <Fragment>
                    <div>
                      <BookingMessage establishment={establishment} />
                      <div className="flex flex-row items-center">
                        <RLabel table={"booking"} field={"data.internal_note"}>
                          Internal notes
                        </RLabel>
                        <span className="flex-1 text-right">** Only visible to staff **</span>
                      </div>
                      <RTextarea
                        table={"booking"}
                        field={"data.internal_note"}
                        className="input"
                        rows={3}
                        placeholder="Type an optional internal note here"
                      />
                    </div>
                    <div>
                      <RInput table={"booking"} field={"data.booking_source"} className="input" label={"Booking Source"} />
                    </div>
                    <div className="flex-row items-center gap-2 flex">
                      <RLabel table={"booking"} field={"data.hide_price_for_customer"}>
                        Hide Price for Customer
                      </RLabel>
                      <RInput
                        table={"booking"}
                        field={"data.hide_price_for_customer"}
                        className="checkbox"
                        type={"checkbox"}
                        hiddenType={"__boolean__"}
                      />
                    </div>
                  </Fragment>
                )}
                {!!(activityAddons.length || establishment.addons.length) && (
                  <div className="space-y-2">
                    <p className="font-bold">
                      <Trans>Add-ons</Trans> {activity && <span className="text-slate-500 font-normal">({pax} pax)</span>}
                    </p>
                    <table className="[&_td]:p-1">
                      <tbody>
                        {activityAddons.map((activityAddon, index) => {
                          const shouldDelete = manualAddonIds[activityAddon.addon_id] === false;
                          return (
                            <tr key={index} className={twMerge(shouldDelete && "line-through")}>
                              <td>
                                <p>{activityAddon.name}</p>
                                <p className="text-xs">{addonUnits[activityAddon.unit as AddonUnit]?.select_label}</p>
                              </td>
                              <td className="text-slate-500">
                                <div className="flex flex-row items-center gap-2">
                                  <span>{finalCurrency}</span>
                                  <RInput
                                    table={"activity_addon"}
                                    field={"data.price_amount"}
                                    disabled={shouldDelete}
                                    onWheel={disableScrollOnNumberInput}
                                    type={"number"}
                                    index={index}
                                    required
                                    className="input"
                                    defaultValue={activityAddon.price_amount}
                                  />
                                </div>
                              </td>
                              <td>
                                {activityAddon.id && (
                                  <RInput table={"activity_addon"} field={"id"} index={index} value={activityAddon.id} />
                                )}
                                <RInput
                                  table="activity_addon"
                                  field={"data.addon_id"}
                                  index={index}
                                  type="hidden"
                                  value={activityAddon.addon_id}
                                />
                                <RInput
                                  table={"activity_addon"}
                                  field={"data.sale_item_id"}
                                  index={index}
                                  type="hidden"
                                  value={tableIdRef("sale_item")}
                                />
                                {hiddenAddonFields.map((key) => (
                                  <RInput
                                    key={key}
                                    type="hidden"
                                    table="activity_addon"
                                    field={`data.${key}`}
                                    value={activityAddon[key] ? activityAddon[key] + "" : ""}
                                    index={index}
                                  />
                                ))}
                                {shouldDelete && <OperationInput table={"activity_addon"} value={"delete"} index={index} />}
                                <RSelect
                                  table={"activity_addon"}
                                  field={"data.quantity"}
                                  index={index}
                                  disabled={shouldDelete}
                                  className={twMerge("input w-14", shouldDelete && "line-through")}
                                  placeholder="Nr"
                                  defaultValue={activityAddon.quantity || "0"}
                                >
                                  <option value="0">0</option>
                                  {arrayFrom(20).map((nr) => (
                                    <option key={nr} value={nr}>
                                      {nr}
                                    </option>
                                  ))}
                                </RSelect>
                              </td>
                              <td>
                                <label className="flex flex-wrap items-center gap-2">
                                  <RInput
                                    hiddenType="__boolean__"
                                    table={"activity_addon"}
                                    disabled={shouldDelete}
                                    index={index}
                                    field={"data.allow_change"}
                                    defaultChecked={activityAddon.allow_change}
                                    type={"checkbox"}
                                    className="checkbox"
                                  />
                                  <span className="text-xs">
                                    Selectable for
                                    <br />
                                    participant
                                  </span>
                                </label>
                              </td>
                              <td>
                                <button
                                  type={"button"}
                                  className={twMerge("group", shouldDelete && "line-through")}
                                  onClick={() => {
                                    setManualAddonIds({ ...manualAddonIds, [activityAddon.addon_id]: shouldDelete });
                                  }}
                                >
                                  {shouldDelete ? <ArrowUturnLeftIcon className="h-4 w-4" /> : <TrashIcon className="w-4 h-4" />}
                                </button>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                    <select
                      value=""
                      className="select max-w-full"
                      onChange={(e) => {
                        if (e.target.value) {
                          setManualAddonIds({ ...manualAddonIds, [e.target.value]: true });
                        }
                      }}
                    >
                      <option value="">Add add-on</option>
                      {establishment.addons.map((addon) => (
                        <option
                          key={addon.id}
                          value={addon.id}
                          disabled={!!activityAddons.find((activityAddon) => activityAddon.addon_id === addon.id)}
                        >
                          {formatAddonText(context, currency, establishment.locale, addon)}
                        </option>
                      ))}
                    </select>
                    <div>
                      <label>
                        <RInput
                          table={"sale_item"}
                          field={"data.exclude_discount_for_addons"}
                          defaultChecked={!!activity?.exclude_discount_for_addons}
                          hiddenType={"__boolean__"}
                          type={"checkbox"}
                          className="checkbox"
                        />
                        &nbsp;
                        <Trans>Exclude discount for add-ons</Trans>
                      </label>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          <div className="pt-5 flex flex-row justify-end gap-3">
            <Backbutton className="btn hover:underline">Cancel</Backbutton>
            <SubmitButton className="btn btn-primary">Submit</SubmitButton>
          </div>
        </ActionForm>
      </AnimatingDiv>
    </main>
  );
}
