import type { AsyncReturnType } from "type-fest";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { SubmitButton } from "~/components/base/Button";
import { ActionForm } from "~/components/form/BaseFrom";
import { CallbackInput } from "~/domain/callback/callback.components";
import { DeleteButtonForm } from "~/components/ResourceInputs";
import { ActionAlert } from "~/components/ActionAlert";
import { sql } from "kysely";

export { action } from "~/routes/_all._catch.resource";

export const loader = async () => {
  const plannedJobs = await kysely
    .selectFrom("callback")
    .where("callback.name", "=", "update_currencies")
    .where("callback.handled_at", "is", null)
    .orderBy("callback.created_at")
    .selectAll("callback")
    .select((eb) =>
      // sql.raw<string>(`callback.created_at + interval 'callback.delay_in_seconds seconds'`).as("execute_at"),
      sql<string>`${eb.ref("callback.created_at")} + ${eb.ref("callback.delay_in_seconds")} * interval '1 second'`.as("execute_at"),
    )
    .execute();
  const currencies = await kysely.selectFrom("currency").selectAll().execute();
  return {
    currencies: currencies,
    planned_jobs: plannedJobs,
  };
};

type LoaderResponse = AsyncReturnType<typeof loader>;

export default function Page() {
  const response = useLoaderData<LoaderResponse>();

  return (
    <div className="flex flex-col space-y-1">
      <ActionForm>
        <ActionAlert />
        <CallbackInput callbackName={"update_currencies"} target_id={""} />
        <SubmitButton className="btn btn-primary">reload</SubmitButton>
      </ActionForm>
      <div>Planned jobs: {response.planned_jobs.length}</div>
      <table className="w-fit">
        <thead>
          <tr>
            <th className="p-2 font-semibold text-left">Name</th>
            <th className="p-2 font-semibold text-left">Created at</th>
            <th className="p-2 font-semibold text-left">Delay in seconds</th>
            <th className="p-2 font-semibold text-left">Execute at</th>
          </tr>
        </thead>
        <tbody>
          {response.planned_jobs.map((job) => (
            <tr key={job.id}>
              <td className="p-2">{job.name}</td>
              <td className="p-2">{job.created_at}</td>
              <td className="p-2"> {job.delay_in_seconds}</td>
              <td className="p-2">{job.execute_at}</td>
              <td className="p-2">
                <DeleteButtonForm table={"callback"} values={[job.id]} />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <div>
        <pre>{JSON.stringify(response.currencies, undefined, 2)}</pre>
      </div>
    </div>
  );
}
