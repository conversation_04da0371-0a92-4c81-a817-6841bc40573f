import { useNavigation } from "@remix-run/react";
import React, { Fragment } from "react";
import { ActionAlert } from "~/components/ActionAlert";
import { CgLock } from "react-icons/cg";
import { BoxLogoCase } from "~/components/BoxLogo";
import { ActionForm } from "~/components/form/BaseFrom";
import { RInput } from "~/components/ResourceInputs";
import { fName } from "~/misc/helpers";
import { useAppContext } from "~/hooks/use-app-context";

export { action } from "~/routes/_all._catch.resource";

export const SecretLock = () => {
  const session = useAppContext();

  if (!session.protected) return <Fragment />;
  return (
    <ActionForm>
      <RInput table={"session"} field={"data.env_verified"} type={"hidden"} hiddenType={"__boolean__"} />
      <button
        className={
          "rounded p-2 text-sm text-primary hover:border-primary-dark hover:text-primary-dark active:border-primary-dark active:text-primary-dark"
        }
        aria-label={"protected"}
      >
        <CgLock className="h-4 w-4" />
      </button>
    </ActionForm>
  );
};

export const SecretForm = () => {
  const data = useAppContext();
  const navigation = useNavigation();

  if (data.passed)
    return (
      <ActionForm className="space-y-3">
        <ActionAlert />
        <RInput table={"session"} field={"data.env_verified"} hiddenType={"__boolean__"} />
        <button type={"submit"}>Clear secret</button>
      </ActionForm>
    );

  return (
    <div className={"flex w-full flex-col space-y-6"}>
      <BoxLogoCase />
      <p>This environment is password protected.</p>
      <ActionForm className="space-y-3">
        <ActionAlert />
        <div className={"flex flex-row space-x-1"}>
          <input
            className="rounded disabled:opacity-60"
            required
            disabled={!!navigation.formData}
            type={"password"}
            name={fName("session", "data.env_verified")}
            placeholder={"password"}
          />
          <button type={"submit"} disabled={!!navigation.formData} className="btn btn-primary disabled:opacity-60">
            {navigation.formData ? "Submitting..." : "Submit"}
          </button>
        </div>
      </ActionForm>
    </div>
  );
};
