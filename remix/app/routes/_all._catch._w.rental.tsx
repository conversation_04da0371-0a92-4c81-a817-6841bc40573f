import { use<PERSON>oaderD<PERSON> } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import React, { Fragment, useState } from "react";
import { LoaderFunctionArgs } from "@remix-run/router";
import { ParamLink } from "~/components/meta/CustomComponents";
import { paramsToRecord, StateInput, StateInputKey, toggleArray } from "~/misc/parsers/global-state-parsers";
import { useAppContext } from "~/hooks/use-app-context";
import { createPageOverwrites } from "~/misc/consts";
import { setState, useSearchParams2 } from "~/hooks/use-search-params2";
import { SidePanel, SidePanelHeading } from "~/components/SidePanel";
import { OnFormSuccess, SubmitButton } from "~/components/base/Button";
import { ActionForm, defaultEqualCheck } from "~/components/form/BaseFrom";
import { OperationInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { ConnectToBookingBanner } from "~/domain/booking/booking-components";
import { RInput, RLabel, RTextarea, toInputId } from "~/components/ResourceInputs";
import { FaPlus } from "react-icons/fa";
import { unique } from "remeda";
import { twMerge } from "tailwind-merge";
import { Selectable } from "kysely";
import { Rentable, RentableService } from "~/kysely/db";
import { InputSearchParamCopies } from "~/components/meta/input";
import { DaySwitch } from "~/domain/meta/datetime";
import { SortButton } from "~/components/SortButton";
import { at_infinity_value, at_now_value } from "~/kysely/db-static-vars";
import { getDateFromParams } from "~/domain/planning/planning.helpers.server";
import { SortColumn, sortColumns } from "~/domain/rentable/rentable-vars";
import { fName, tableIdRef } from "~/misc/helpers";
import { TrashIcon } from "@heroicons/react/20/solid";
import { ChevronLeft, CopyIcon, PlusIcon } from "lucide-react";
import { refreshFormdata } from "~/components/form/form-hooks";
import { Alert } from "~/components/base/alert";
import { ActionAlert } from "~/components/ActionAlert";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { sql } from "kysely";
import { BackFallbackLink } from "~/components/base/BackFallbackLink";
import {
  ascNullsFirst,
  ascNullsLast,
  contextRef,
  descNullsFirst,
  descNullsLast,
  lower,
  formatDatetime,
  plus,
  toDate,
  least,
} from "~/kysely/kysely-helpers";
import { DefaultInfoIcon, Tooltip } from "~/components/base/tooltip";
import { AnimatingDiv } from "~/components/base/base";
import { PencilIcon, XMarkIcon } from "@heroicons/react/20/solid";
import { ProfileIcon } from "~/components/Icons";
import { AdminLevel, getAdminLevelIndex } from "~/domain/member/member-vars";
import { DB } from "~/kysely/db";
import { Checker, ControlledChecker } from "~/components/Checker";
import { formatValidityDuration, ValidityDurationField, ValidityDurationSelect } from "~/components/duration";
import { NavigatingSelect } from "~/components/NavigatingSelect";

export { action } from "~/routes/_all._catch.resource";

const tabs = ["All", "Available", "Rented", "Out of Use"];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const record = paramsToRecord(url.searchParams);
  const date = getDateFromParams(request);
  const establishmentId = record.establishment_id || record.persist_establishment_id;

  if (!establishmentId) throw new Error("establishment_id param Required");

  // const rentableServices = await kysely.selectFrom('rentable_service').

  const items = await kysely
    .selectFrom("rentable")
    .leftJoin("sortable_value", (join) => join.on("sortable_value.value", "=", lower(contextRef("rentable", "size"))))
    .leftJoin("establishment", "establishment.id", "rentable.establishment_id")
    .leftJoin("spot", "spot.id", "establishment.spot_id")
    .leftJoin("region", "region.id", "spot.region_id")
    .selectAll("rentable")
    .select((eb) => {
      const firstAssignment = eb
        .selectFrom("rental_assignment")
        .whereRef("rental_assignment.rentable_id", "=", "rentable.id")
        .select((eb) => [eb.fn.min<string>("rental_assignment.date").as("first_rental_date")])
        .limit(1);
      const firstUse = least(eb.ref("rentable.first_use_date"), firstAssignment as any);
      const nextServiceDate = toDate(plus(firstUse, eb.ref("rentable.service_interval")));
      const nextServiceDateValid = eb(nextServiceDate, ">", date.dateParam);
      return [
        "sortable_value.sort_order as size_sort_order",
        jsonObjectFrom(
          eb
            .selectFrom("rentable_service")
            .whereRef("rentable_service.rentable_id", "=", "rentable.id")
            .select(["rentable_service.id", "rentable_service.service_date"])
            .where((eb) =>
              eb.or([eb("rentable_service.service_date", "<=", date.dateParam), eb("rentable_service.service_date", "is", null)]),
            )
            .orderBy("rentable_service.service_date", descNullsLast)
            .select((eb) => {
              const nextServiceDateSelect = toDate(plus(eb.ref("rentable_service.service_date"), eb.ref("rentable.service_interval")));
              return [
                nextServiceDateSelect.as("next_service_date"),
                eb(nextServiceDateSelect, ">", date.dateParam).as("next_service_date_valid"),
                eb
                  .selectFrom("rental_assignment")
                  .whereRef("rental_assignment.rentable_id", "=", "rentable_service.rentable_id")
                  .where((eb) =>
                    eb.or([
                      eb("rental_assignment.date", ">=", eb.ref("rentable_service.service_date")),
                      eb("rentable_service.service_date", "is", null),
                    ]),
                  )
                  .where("rental_assignment.date", "<=", date.dateParam)
                  .select((eb) => eb.fn.countAll<number>().as("count"))
                  .as("rental_assignment_count"),
              ];
            })
            .limit(1),
        ).as("last_service_since_date"),

        nextServiceDate.as("next_service_date"),
        nextServiceDateValid.as("next_service_date_valid"),
        firstUse.as("final_first_use_date"),
        jsonArrayFrom(
          eb
            .selectFrom("rentable_service")
            .whereRef("rentable_service.rentable_id", "=", "rentable.id")
            .orderBy("rentable_service.service_date", ascNullsFirst)
            .selectAll("rentable_service"),
        ).as("rentable_services"),
        jsonObjectFrom(
          eb
            .selectFrom("rental_assignment")
            .where("rental_assignment.rentable_id", "=", eb.ref("rentable.id"))
            .select((eb) => [
              eb.fn.countAll<number>().as("total_count"),
              eb.fn.countAll<number>().filterWhere("rental_assignment.date", "=", date.dateParam).as("date_count"),
              eb.fn.min("rental_assignment.date").as("first_rental_date"),
            ]),
        ).as("aggregate"),
        jsonArrayFrom(
          eb
            .selectFrom("comment")
            .where("comment.target", "=", "rentable" satisfies keyof DB)
            .where("comment.target_id", "=", eb.ref("rentable.id"))
            .orderBy("comment.created_at", "desc")
            .selectAll("comment")
            .select((eb) =>
              formatDatetime(eb.ref("comment.created_at"), "DD Mon YYYY, HH24:MI", eb.ref("region.timezone")).as("created_at_formatted"),
            ),
        ).as("comments"),
      ];
    })
    .where("rentable.establishment_id", "=", establishmentId)
    .$call((eb) => {
      let orderEb = eb;
      const filteredSorts = record.sorts.filter((sort) => sortColumns.includes(sort.key as SortColumn));
      if (filteredSorts.length === 0) return eb.orderBy("rentable.type", "asc");
      filteredSorts.forEach((sort) => {
        if (sort.key === "rentable.size") {
          orderEb = orderEb
            .orderBy("sortable_value.sort_order", sort.direction === "asc" ? ascNullsLast : descNullsFirst)
            .orderBy("rentable.size", sort.direction === "asc" ? "asc" : "desc");
        } else {
          orderEb = orderEb.orderBy(sort.key as SortColumn, sort.direction === "asc" ? "asc" : "desc");
        }
      });
      return orderEb;
    })
    .execute();

  return {
    items: items,
    ...createPageOverwrites({ establishment_id: establishmentId, fixed_width: true }),
  };
};

const modalTypes = ["rentable_create", "rentable_copy"] as const;
const getModalType = (modalType: (typeof modalTypes)[number]) => modalType;

const RentableForm = (props: {
  rentable?: Partial<Selectable<Rentable>>;
  brands: string[];
  sizes: string[];
  types: string[];
  colors: string[];
}) => {
  const rentable = props.rentable;
  const brands = props.brands;
  const sizes = props.sizes;
  const types = props.types;
  const colors = props.colors;
  return (
    <Fragment>
      {rentable?.id && <RInput table="rentable" field="id" value={rentable.id} type="hidden" />}
      <div>
        <RInput
          label="Category"
          table="rentable"
          hiddenType="__trim__"
          field="data.type"
          className="input w-full"
          defaultValue={rentable?.type || ""}
          required
          datalist={types.map((type) => (
            <option key={type} value={type}>
              {type}
            </option>
          ))}
        />
      </div>
      <div>
        <RInput label="Title" table="rentable" hiddenType="__trim__" field="data.title" className="input w-full" defaultValue={rentable?.title || ""} />
      </div>
      <div>
        <RInput
          label={"Brand"}
          table="rentable"
          hiddenType="__trim__"
          field="data.brand"
          className="input w-full"
          defaultValue={rentable?.brand || ""}
          datalist={brands.map((brand) => (
            <option key={brand} value={brand}>
              {brand}
            </option>
          ))}
        />
      </div>
      <div>
        <RInput
          label={"Size"}
          table="rentable"
          hiddenType="__trim__"
          field="data.size"
          className="input w-full"
          maxLength={4}
          defaultValue={rentable?.size || ""}
          datalist={sizes.map((size) => (
            <option key={size} value={size}>
              {size}
            </option>
          ))}
        />
      </div>
      <div>
        <RInput
          label="Color"
          table="rentable"
          hiddenType="__trim__"
          field="data.color"
          className="input w-full"
          defaultValue={rentable?.color || ""}
          datalist={colors.map((color) => (
            <option key={color} value={color}>
              {color}
            </option>
          ))}
        />
      </div>
      <div>
        <RInput
          table="rentable"
          hiddenType="__trim__"
          field="data.reference_id"
          className="input w-full"
          label="ID Number"
          defaultValue={rentable?.reference_id || ""}
          required
        />
      </div>
      <div>
        <RLabel table="rentable" field="data.storage_location">
          Storage Location
        </RLabel>
        <RInput
          table="rentable"
          hiddenType="__trim__"
          field="data.storage_location"
          className="input w-full"
          defaultValue={rentable?.storage_location || ""}
        />
      </div>
      <div>
        <RLabel table="rentable" field="data.first_use_date">
          Date of First Use (Optional)
        </RLabel>
        <p className="text-sm text-slate-500 pt-1 pb-1">Leave blank for new items. A date is automatically populated at first use.</p>
        <RInput
          table="rentable"
          type="date"
          field="data.first_use_date"
          className="input w-full"
          defaultValue={rentable?.first_use_date || ""}
        />
      </div>
      <div>
        <p>Service Interval (Optional)</p>
        <p className="text-sm pt-1 pb-2 text-slate-500">Trigger next service by interval and/or use count.</p>
        <div className="flex flex-row gap-3 items-center">
          <div className="flex-2">
            <ValidityDurationField name={fName("rentable", "data.service_interval")} defaultValue={rentable?.service_interval || ""} />
          </div>
          <div className="text-slate-500 text-sm">or</div>
          <div className="flex-1">
            <RInput
              table="rentable"
              hiddenType="__trim__"
              field="data.service_use_count"
              className="input w-full"
              placeholder="Use Count"
              defaultValue={rentable?.service_use_count || ""}
            />
          </div>
        </div>
      </div>
      {rentable && <RInput table="rentable" field="data.establishment_id" value={rentable.establishment_id} type="hidden" />}
    </Fragment>
  );
};

const rentableCopyKey = "rentable_copy";
const editKey = "rentable_edit";
const bulkKey = "bulk";

const RentableServiceSection = (props: { showEdit: boolean }) => {
  const data = useLoaderData<typeof loader>();
  const ctx = useAppContext();
  const search = useSearchParams2();
  const selectedItem = data.items.find((item) => item.id === search.state.modal_detail_id);
  const [rentable_service, setRentableService] = useState<Partial<Selectable<RentableService>> | null>(null);

  if (!selectedItem) return null;

  console.log('selectedItem', selectedItem);

  return (
    <AnimatingDiv className="space-y-6">
      <p>Rental services</p>
      <div className="grid grid-cols-1 gap-3 text-sm ">
        {/* <div className="contents"> */}
        {/* <p>Service Date</p> */}
        {/* <p className="col-span-2">Next Service</p> */}
        {/* </div> */}
        {selectedItem.rentable_services.map((service) => {
          const isSelected = rentable_service?.id === service.id;
          return (
            <div key={service.id} className="contents ">
              <div className={twMerge("flex flex-row gap-2 items-center justify-between", isSelected && "bg-slate-100")}>
                <button disabled={isSelected} className="link disabled:text-slate-700" onClick={() => setRentableService(service)}>
                  {service.service_date || "initial"}
                </button>
                <ActionForm confirmMessage="Are you sure?">
                  <RInput table="rentable_service" field="id" value={service.id} type="hidden" />
                  <OperationInput table="rentable_service" value="delete" />
                  {!isSelected && (
                    <button type="submit" className="text-red-500 hover:text-red-700 text-sm">
                      <XMarkIcon className="w-4 h-4" />
                    </button>
                  )}
                </ActionForm>
              </div>
              {/* <p className={twMerge(isSelected && "bg-slate-100")}>{service.next_service_date || "-"}</p> */}
              {/* <p className={twMerge(isSelected && "bg-slate-100")}>{service.max_use_count ? `at ${service.max_use_count} uses` : "-"}</p> */}
            </div>
          );
        })}
      </div>
      {!rentable_service && (
        <div className="text-right justify-end flex">
          {props.showEdit && (
            <button
              type="button"
              className="link flex items-center gap-1"
              onClick={() => setRentableService({ rentable_id: selectedItem.id })}
            >
              <PlusIcon className="w-4 h-4" /> Add Service
            </button>
          )}
        </div>
      )}
      {rentable_service && (
        <ActionForm className="space-y-3 text-sm" key={rentable_service.id || "new"}>
          <hr />
          <ActionAlert />
          <OnFormSuccess onSuccess={() => setRentableService(null)} />
          <RInput table={"rentable_service"} field={"data.rentable_id"} value={rentable_service.rentable_id} type={"hidden"} />
          <div>
            <RInput
              autoFocus
              label="Service Date"
              table="rentable_service"
              field="data.service_date"
              className="input"
              type="date"
              defaultValue={rentable_service?.service_date || ctx.date.todayParam ||  ""}
            />
          </div>

          {rentable_service.id && <RInput table="rentable_service" field="id" type="hidden" value={rentable_service.id} />}
          {/* <div className="grid grid-cols-2 gap-3">
            <div>
              <RInput
                autoFocus
                label="Service Date"
                table="rentable_service"
                field="data.service_date"
                className="input"
                type="date"
                defaultValue={rentable_service?.service_date || ""}
              />
            </div>
            <div>
              <RInput
                label="Next Service Date"
                table="rentable_service"
                field="data.next_service_date"
                className="input"
                type="date"
                defaultValue={rentable_service?.next_service_date || ""}
              />
            </div>
            <div>
              <RInput
                label="Initial Use Count"
                table="rentable_service"
                field="data.initial_use_count"
                className="input"
                type="number"
                required
                defaultValue={rentable_service?.initial_use_count || 0}
              />
            </div>

            <div>
              <RInput
                label="Max Use Count"
                table="rentable_service"
                field="data.max_use_count"
                className="input"
                type="number"
                defaultValue={rentable_service?.max_use_count || ""}
              />
            </div>
          </div> */}
          <div className="flex flex-row gap-3 justify-end">
            <button type="button" className="btn btn-basic" onClick={() => setRentableService(null)}>
              Cancel
            </button>
            <SubmitButton className="btn btn-primary">{rentable_service.id ? "Update" : "Add"}</SubmitButton>
          </div>
        </ActionForm>
      )}
    </AnimatingDiv>
  );
};

const RentableCommentSection = () => {
  const data = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const ctx = useAppContext();
  const selectedItem = data.items.find((item) => item.id === search.state.modal_detail_id);

  const [commentId, setCommentId] = useState("");
  const selectedComment = selectedItem?.comments?.find((comment) => comment.id === commentId);

  if (!selectedItem) return null;

  const comments = selectedItem.comments || [];

  const getHasRightOnAllEstablishments = (minLevel: AdminLevel) => {
    const adminEstablishments = data.items.filter((item) =>
      ctx.members.find((member) => member.establishment_id === item.establishment_id && member.admin >= getAdminLevelIndex(minLevel)),
    );
    return !!data.items.length && adminEstablishments.length === data.items.length;
  };

  const hasWriteOnAllEstablishments = getHasRightOnAllEstablishments("write");

  return (
    <div className="space-y-4">
      <div className="space-y-3">
        <h3 className="">Comments ({comments.length})</h3>
        {comments.length === 0 ? (
          <p className="text-slate-500 text-sm">No comments yet.</p>
        ) : (
          <div className="space-y-3 overflow-y-auto">
            {comments.map((comment, index) => (
              <div key={comment.id || index} className="bg-slate-50 p-3 rounded-md">
                <div className="flex justify-between items-start gap-2">
                  <div className="flex-1">
                    <p className="text-sm whitespace-pre-wrap">{comment.content}</p>
                    <div className="flex flex-row gap-2 items-center justify-between">
                      <p className="text-xs text-slate-500 mt-1">{comment.created_at_formatted}</p>
                      <button
                        type="button"
                        className="text-blue-500 hover:text-blue-700 text-sm"
                        onClick={() => {
                          setCommentId(comment.id);
                          setTimeout(() => {
                            document.getElementById(toInputId(fName("comment", "data.content")))?.focus();
                          }, 10);
                        }}
                      >
                        <PencilIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  {hasWriteOnAllEstablishments && (
                    <ActionForm confirmMessage="Delete this comment?">
                      <RInput table="comment" field="id" value={comment.id} type="hidden" />
                      <OperationInput table="comment" value="delete" />
                      <button type="submit" className="text-red-500 hover:text-red-700 text-sm">
                        <XMarkIcon className="w-4 h-4" />
                      </button>
                    </ActionForm>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Add New Comment */}
      {hasWriteOnAllEstablishments && (
        <ActionForm key={commentId || ""} onCheckEqual={defaultEqualCheck}>
          <OnFormSuccess
            onSuccess={() => {
              setCommentId(commentId ? "" : "new");
            }}
          />
          <div className="space-y-3">
            <h3 className="font-medium">{selectedComment ? "Edit Comment" : "Add Comment"}</h3>
            <div>
              {selectedComment && <RInput table="comment" field="id" value={selectedComment.id} type="hidden" />}
              <RInput table="comment" field="data.target" value={"rentable" satisfies keyof DB} type="hidden" />
              <RInput table="comment" field="data.target_id" value={selectedItem.id} type="hidden" />
              <RTextarea
                table="comment"
                field="data.content"
                placeholder="Enter your comment here..."
                className="input min-h-24 w-full"
                required
              >
                {selectedComment?.content}
              </RTextarea>
            </div>
            <div className="flex gap-2 justify-end">
              {selectedComment && (
                <button type="button" className="btn btn-basic" onClick={() => setCommentId("")}>
                  Cancel
                </button>
              )}
              <SubmitButton className="btn btn-primary">{selectedComment ? "Save" : "Add"}</SubmitButton>
            </div>
          </div>
        </ActionForm>
      )}
    </div>
  );
};

const BulkEditPanel = (props: { items: Selectable<Rentable>[] }) => {
  const firstItem = props.items[0]!;
  const [bulk, setBulk] = useState(firstItem);
  const itemCombined = {
    type: unique(props.items.map((item) => item.type)),
    size: unique(props.items.map((item) => item.size)),
    color: unique(props.items.map((item) => item.color)),
    service_interval: unique(props.items.map((item) => item.service_interval)),
    service_use_count: unique(props.items.map((item) => item.service_use_count)),
  };
  const initialEditable = {
    type: itemCombined.type.length < 2,
    size: itemCombined.size.length < 2,
    color: itemCombined.color.length < 2,
    service_interval: itemCombined.service_interval.length < 2,
    service_use_count: itemCombined.service_use_count.length < 2,
  };
  const [editable, setEditable] = useState(initialEditable);

  console.log(itemCombined);

  return (
    <div className="px-3">
      <ActionForm className="space-y-3">
        <ActionAlert />
        <div>
          <div className="flex flex-row gap-2 items-center">
            <label className="text-sm" htmlFor="bulk-type">
              Category
            </label>
            {editable.type && !initialEditable.type && (
              <button
                type="button"
                className="link text-sm"
                onClick={() => {
                  setEditable({ ...editable, type: !editable.type });
                }}
              >
                {editable.type ? "Cancel" : "Override"}
              </button>
            )}
          </div>
          <input
            id="bulk-type"
            className={twMerge("input", !editable.type && "line-through")}
            type="text"
            disabled={!editable.type}
            value={editable.type ? bulk?.type || "" : "mixed values"}
            onChange={(e) => setBulk({ ...bulk!, type: e.target.value })}
          />
        </div>
        <div>
          <div className="flex flex-row gap-2 items-center">
            <label className="text-sm" htmlFor="bulk-size">
              Size
            </label>
            {editable.size && !initialEditable.size && (
              <button
                type="button"
                className="link text-sm"
                onClick={() => {
                  setEditable({ ...editable, size: !editable.size });
                }}
              >
                {editable.size ? "Cancel" : "Override"}
              </button>
            )}
          </div>
          {editable.size ? (
            <input
              id="bulk-size"
              className={twMerge("input", !editable.size && "line-through")}
              type="text"
              disabled={!editable.size}
              value={editable.size ? bulk?.size || "" : "mixed values"}
              onChange={(e) => setBulk({ ...bulk, size: e.target.value })}
            />
          ) : (
            <button
              type="button"
              className="input text-left group hover:bg-slate-50"
              onClick={() => {
                setEditable({ ...editable, size: true });
                setTimeout(() => {
                  document.getElementById("bulk-size")?.focus();
                }, 10);
              }}
            >
              <span className=" line-through text-slate-500 ">
                Mixed values: {itemCombined.size.map((val) => (val === null ? "null" : val)).join(", ")}
              </span>
            </button>
          )}
        </div>
        <div>
          <div className="flex flex-row gap-2 items-center">
            <label className="text-sm" htmlFor="bulk-color">
              Color
            </label>
            {editable.color && !initialEditable.color && (
              <button
                type="button"
                className="link text-sm"
                onClick={() => {
                  setEditable({ ...editable, color: !editable.color });
                }}
              >
                {editable.color ? "Cancel" : "Override"}
              </button>
            )}
          </div>
          {editable.color ? (
            <input
              id="bulk-color"
              className={twMerge("input", !editable.color && "line-through")}
              type="text"
              disabled={!editable.color}
              value={editable.color ? bulk?.color || "" : "mixed values"}
              onChange={(e) => setBulk({ ...bulk, color: e.target.value })}
            />
          ) : (
            <button
              type="button"
              className="input text-left group hover:bg-slate-50"
              onClick={() => {
                setEditable({ ...editable, color: true });
                setTimeout(() => {
                  document.getElementById("bulk-color")?.focus();
                }, 10);
              }}
            >
              <span className=" line-through text-slate-500 ">
                Mixed values: {itemCombined.color.map((val) => (val === null ? "null" : val)).join(", ")}
              </span>
            </button>
          )}
        </div>
        <div>
          <div className="flex flex-row gap-2 items-center">
            <label className="text-sm" htmlFor="bulk-service-interval">
              Service Interval
            </label>
            {editable.service_interval && !initialEditable.service_interval && (
              <button
                type="button"
                className="link text-sm"
                onClick={() => {
                  setEditable({ ...editable, service_interval: !editable.service_interval });
                }}
              >
                {editable.service_interval ? "Cancel" : "Override"}
              </button>
            )}
          </div>
          {editable.service_interval ? (
            <ValidityDurationSelect
              name="bulk-service-interval"
              disabled={!editable.service_interval}
              value={bulk?.service_interval || ""}
              onChange={(value) => setBulk({ ...bulk, service_interval: value || null })}
            />
          ) : (
            <button
              type="button"
              className="input text-left group hover:bg-slate-50"
              onClick={() => {
                setEditable({ ...editable, service_interval: true });
                setTimeout(() => {
                  document.getElementById("bulk-service-interval")?.focus();
                }, 10);
              }}
            >
              <span className=" line-through text-slate-500 ">
                Mixed values: {itemCombined.service_interval.map((val) => (val === null ? "null" : formatValidityDuration(val))).join(", ")}
              </span>
            </button>
          )}
        </div>
        <div>
          <div className="flex flex-row gap-2 items-center">
            <label className="text-sm" htmlFor="bulk-service-use-count">
              Service Use Count
            </label>
            {editable.service_use_count && !initialEditable.service_use_count && (
              <button
                type="button"
                className="link text-sm"
                onClick={() => {
                  setEditable({ ...editable, service_use_count: !editable.service_use_count });
                }}
              >
                {editable.service_use_count ? "Cancel" : "Override"}
              </button>
            )}
          </div>
          {editable.service_use_count ? (
            <input
              id="bulk-service-use-count"
              className={twMerge("input", !editable.service_use_count && "line-through")}
              type="number"
              disabled={!editable.service_use_count}
              value={editable.service_use_count ? bulk.service_use_count || "" : "mixed values"}
              onChange={(e) => setBulk({ ...bulk, service_use_count: parseInt(e.target.value) || null })}
            />
          ) : (
            <button
              type="button"
              className="input text-left group hover:bg-slate-50"
              onClick={() => {
                setEditable({ ...editable, service_use_count: true });
                setTimeout(() => {
                  document.getElementById("bulk-service-use-count")?.focus();
                }, 10);
              }}
            >
              <span className=" line-through text-slate-500 ">
                Mixed values: {itemCombined.service_use_count.map((val) => (val === null ? "null" : val)).join(", ")}
              </span>
            </button>
          )}
        </div>
        <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined }} />
        {props.items.map((item) => (
          <Fragment key={item.id}>
            <RInput table={"rentable"} field={"id"} index={item.id} value={item.id} />
            {editable.type && (
              <RInput table={"rentable"} field={"data.type"} type={"hidden"} index={item.id} value={bulk?.type} hiddenType={"__trim__"} />
            )}
            {editable.size && (
              <RInput
                table={"rentable"}
                field={"data.size"}
                type={"hidden"}
                index={item.id}
                value={bulk?.size || ""}
                hiddenType={"__trim__"}
              />
            )}
            {editable.color && (
              <RInput
                table={"rentable"}
                field={"data.color"}
                type={"hidden"}
                index={item.id}
                value={bulk?.color || ""}
                hiddenType={"__trim__"}
              />
            )}
            {editable.service_interval && (
              <RInput
                table={"rentable"}
                field={"data.service_interval"}
                type={"hidden"}
                index={item.id}
                value={bulk?.service_interval || ""}
              />
            )}
            {editable.service_use_count && (
              <RInput
                table={"rentable"}
                field={"data.service_use_count"}
                type={"hidden"}
                index={item.id}
                value={bulk?.service_use_count || ""}
              />
            )}
          </Fragment>
        ))}
        <div className="flex flex-row justify-end gap-3 items-center">
          <ParamLink paramState={{ toggle_modal: undefined }} className="btn btn-basic">
            Cancel
          </ParamLink>
          <SubmitButton className="btn btn-primary">Submit</SubmitButton>
        </div>
      </ActionForm>
    </div>
  );
};

export default function Page() {
  const ctx = useAppContext();
  const search = useSearchParams2();
  const data = useLoaderData<typeof loader>();
  const establishment = ctx.establishment;
  if (!establishment) return <div>Establishment required</div>;
  const member = ctx.members.find((member) => member.establishment_id === establishment?.id);
  const isManager = (member?.admin || 0) > 1;
  const showEdit = !search.state.customer_view && isManager;

  // Column configuration object - order is defined by the object keys
  const columns = {
    title: {
      title: <SortButton column="rentable.title">Title</SortButton>,
      comp: (item) => (
        <div>
          <ParamLink
            className="whitespace-nowrap link"
            paramState={{ toggle_modal: "side", modal_detail_name: undefined, modal_detail_id: item.id }}
          >
            {item.title || "-"}
          </ParamLink>
        </div>
      ),
    },
    type: {
      title: <SortButton column="rentable.type">Category</SortButton>,
      comp: (item) => <div className={twMerge("whitespace-nowrap")}>{item.type}</div>,
    },
    brand: {
      title: <SortButton column="rentable.brand">Brand</SortButton>,
      comp: (item) => <div className={twMerge("whitespace-nowrap")}>{item.brand || "-"}</div>,
    },
    reference_id: {
      title: <SortButton column="rentable.reference_id">ID Number</SortButton>,
      comp: (item) => (
        <div>
          <ParamLink
            className="whitespace-nowrap link"
            paramState={{ toggle_modal: "side", modal_detail_name: undefined, modal_detail_id: item.id }}
          >
            {item.reference_id}
          </ParamLink>
        </div>
      ),
    },
    color: {
      title: <SortButton column="rentable.color">Color</SortButton>,
      comp: (item) => <div className={twMerge("whitespace-nowrap")}>{item.color || "-"}</div>,
    },
    size: {
      title: <SortButton column="rentable.size">Size</SortButton>,
      comp: (item) => <div className={twMerge("whitespace-nowrap")}>{item.size}</div>,
    },
    status: {
      title: "Status",
      comp: (item) => (
        <div className={twMerge("whitespace-nowrap")}>
          {item.aggregate?.date_count ? (
            <div className="bg-orange-100 text-orange-600 w-fit rounded-full px-3 py-1">
              Rented {item.aggregate?.date_count > 1 ? `(${item.aggregate?.date_count})` : ""}
            </div>
          ) : item.deleted_at === at_infinity_value ? (
            <div className="bg-green-100 text-green-700  w-fit rounded-full px-3 py-1">Available</div>
          ) : (
            <div className="bg-red-100 text-red-700  w-fit rounded-full px-3 py-1">Out of Use</div>
          )}
        </div>
      ),
    },
    storage_location: {
      title: <SortButton column="rentable.storage_location">Storage Location</SortButton>,
      comp: (item) => <div className={twMerge("whitespace-nowrap")}>{item.storage_location || "-"}</div>,
    },
    first_use: {
      title: "First Use",
      comp: (item) => <div className={twMerge("whitespace-nowrap")}>{item.final_first_use_date}</div>,
    },
    times_used: {
      title: (
        <div className="whitespace-nowrap flex items-center gap-1">
          Times Used{" "}
          <Tooltip description="Total times used">
            <DefaultInfoIcon />
          </Tooltip>
        </div>
      ),
      comp: (item) => {
        const totalUseCount = item.aggregate?.total_count || 0;
        return <div className={twMerge("whitespace-nowrap")}>{totalUseCount}</div>;
      },
    },
    next_service: {
      title: "Next Service",
      comp: (item) => {
        const lastService = item.last_service_since_date;

        const getRemainingUses = (count?: number | null | undefined) => [
          !!item.service_use_count && item.service_use_count - (count || 0),
          (count || 0) < (item.service_use_count || 0),
        ];

        const nextServiceChecks = (
          lastService
            ? [
                [!!lastService.next_service_date && lastService.next_service_date, lastService.next_service_date_valid],
                getRemainingUses(lastService.rental_assignment_count),
              ]
            : [
                [!!item.next_service_date && item.next_service_date, item.next_service_date_valid],
                getRemainingUses(item.aggregate?.total_count),
              ]
        ).filter(([value]) => value !== false);

        return (
          <div className="whitespace-nowrap">
            {nextServiceChecks.length > 0 ? (
              <div>
                {nextServiceChecks.map(([value, valid], index) => (
                  <div key={index}>
                    {!!index && <span className=""> or </span>}
                    <span className={twMerge(!valid && "bg-red-100 text-red-700")}>
                      {typeof value === "number" ? `in ${value} uses` : "on " + value}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              "-"
            )}
          </div>
        );
      },
    },
    last_service: {
      title: "Last Service",
      comp: (item) => <div className={twMerge("whitespace-nowrap")}>{item.last_service_since_date?.service_date || "-"}</div>,
    },
    service_interval: {
      title: "Service Interval",
      comp: (item) => {
        const nextServiceChecks = [
          formatValidityDuration(item.service_interval),
          item.service_use_count && `${item.service_use_count} uses`,
        ].filter((value) => !!value);
        return (
          <div className={twMerge("whitespace-nowrap")}>
            {nextServiceChecks
              .map((value) => value)
              .map((value, index) => (
                <div key={value}>
                  {!!index && <span>or </span>}
                  {value}
                </div>
              ))}
          </div>
        );
      },
    },
  } satisfies Record<string, { title: React.ReactNode; comp: (item: (typeof data.items)[number]) => React.ReactNode }>;

  const activeItems = data.items.filter((item) => !!item.aggregate?.date_count || item.deleted_at === at_infinity_value);
  const types = unique(activeItems.map((item) => item.type)).sort((a, b) => a.localeCompare(b));
  const brands = unique(activeItems.map((item) => item.brand).filter((brand): brand is string => brand !== null));
  const selectedType = types.find((type) => type === search.state.type);
  const sizes = unique(activeItems.map((item) => item.size).filter((size): size is string => size !== null));
  const selectedSize = sizes.find((size) => size === search.state.size);
  const colors = unique(activeItems.map((item) => item.color).filter((color): color is string => color !== null));
  const selectedColor = colors.find((color) => color === search.state.color);

  const selectedItem = data.items.find((item) => item.id === search.state.modal_detail_id);

  const currentTab = tabs.find((tab) => tab === search.state.tab) || tabs[0];

  const filteredItems = data.items
    .filter((item) => !selectedType || item.type === selectedType)
    .filter((item) => !selectedSize || item.size === selectedSize)
    .filter((item) => !selectedColor || item.color === selectedColor)
    .filter((item) => {
      if (currentTab === "Rented") return !!item.aggregate?.date_count;
      if (currentTab === "Available") return !item.aggregate?.date_count && item.deleted_at === at_infinity_value;
      if (currentTab === "Out of Use") return item.deleted_at !== at_infinity_value;
      return !!item.aggregate?.date_count || item.deleted_at === at_infinity_value;
    });

  const backParamState: Partial<StateInput> = {
    toggle_modal: search.state.modal_detail_name && selectedItem ? "side" : undefined,
    modal_detail_name: search.state.modal_detail_name ? undefined : search.state.modal_detail_name,
  };

  const bulkSelectedItems = filteredItems.filter((item) => search.state.ids.includes(item.id));
  const bulkSelectedAlreadyAssignedItems = bulkSelectedItems.filter(
    (item) => item.aggregate?.total_count && item.deleted_at === at_infinity_value,
  );
  const bulkSelectedAlreadyDeleted = bulkSelectedItems.filter((item) => item.deleted_at !== at_infinity_value);
  const bulkDeleteMsg = `This will delete ${bulkSelectedItems.length - bulkSelectedAlreadyAssignedItems.length - bulkSelectedAlreadyDeleted.length} rental items${bulkSelectedAlreadyAssignedItems.length ? ` and put ${bulkSelectedAlreadyAssignedItems.length} rental items to "out of use" because they were already assigned.` : bulkSelectedAlreadyDeleted.length ? ` and restore ${bulkSelectedAlreadyDeleted.length} rental items.` : "."}
  
  Are you sure?
  `;

  return (
    <Fragment>
      <div className="px-6 space-y-6">
        <ConnectToBookingBanner />

        {/* Date Picker moved to top */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h2 className="text-xl font-semibold">Rental</h2>
          </div>
          {showEdit && (
            <ParamLink
              paramState={{ toggle_modal: "side", modal_detail_name: editKey, modal_detail_id: editKey }}
              className="btn btn-primary gap-2 text-sm"
            >
              <FaPlus className="w-4 h-4" />
              Create Rental Item
            </ParamLink>
          )}
        </div>
        <ActionForm
          key={ctx.date.dateParam}
          method={"GET"}
          className="flex flex-row gap-3"
          onCheckEqual={(args) => args.finalFormData.get("persist_date" satisfies StateInputKey) === ctx.date.dateParam}
        >
          <InputSearchParamCopies excludeKeys={["persist_date"]} />
          <DaySwitch className="flex-1" />
        </ActionForm>
        <div className="space-y-3">
          <div className="space-y-3">
            {!!types.length && (
              <div className="flex flex-wrap gap-3 justify-start">
                {types.map((type) => (
                  <ParamLink
                    key={type}
                    className="flex-grow flex-shrink btn btn-basic aria-selected:btn-secondary"
                    paramState={{ type: type === selectedType ? undefined : type }}
                    aria-selected={type === selectedType}
                  >
                    {type}
                  </ParamLink>
                ))}
              </div>
            )}
            {/*{!!types.length && !!sizes.length && <div className="border-l m-2.5 border-slate-200"></div>}*/}
            <div className="flex flex-row gap-3 items-center">
              {!!sizes.length && (
                <div
                  className={twMerge(
                    "flex flex-row gap-1 items-center rounded-md border overflow-hidden border-slate-200",
                    !!selectedSize && "bg-secondary ",
                  )}
                >
                  <label
                    htmlFor="size-select"
                    className="pl-3 py-1"
                    onClick={() => {
                      const select = document.getElementById("size-select");
                      if (select instanceof HTMLSelectElement && "showPicker" in select) {
                        (select as any).showPicker();
                      }
                    }}
                  >
                    Size:
                  </label>
                  <select
                    id="size-select"
                    defaultValue={selectedSize}
                    onChange={(e) => setState({ size: e.target.value || undefined }, { replaceRoute: true })}
                    className="border-none bg-transparent "
                  >
                    <option value="">All</option>
                    {sizes.map((size) => (
                      <option key={size} value={size}>
                        {size}
                      </option>
                    ))}
                  </select>
                </div>
              )}
              <div
                className={twMerge(
                  "flex flex-row gap-1 items-center overflow-hidden rounded-md border border-slate-200",
                  currentTab !== tabs[0] && "bg-secondary",
                )}
              >
                <label
                  className="pl-3 py-1 "
                  htmlFor="status-select"
                  onClick={() => {
                    const select = document.getElementById("status-select");
                    if (select instanceof HTMLSelectElement && "showPicker" in select) {
                      (select as any).showPicker();
                    }
                  }}
                >
                  Status:
                </label>
                <select
                  id="status-select"
                  defaultValue={currentTab}
                  onChange={(e) => setState({ tab: e.target.value || undefined }, { replaceRoute: true })}
                  className="border-none bg-transparent"
                >
                  {tabs.map((tab) => (
                    <option key={tab} value={tab}>
                      {tab}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            {/*{!!sizes.length && !!colors.length && <div className="border-l m-2.5 border-slate-200"></div>}*/}
            {/*{!!colors.length && (*/}
            {/*  <Fragment>*/}
            {/*    {colors.map((color) => (*/}
            {/*      <ParamLink*/}
            {/*        key={color}*/}
            {/*        className="btn btn-basic  aria-selected:btn-secondary"*/}
            {/*        paramState={{ color: color === selectedColor ? undefined : color }}*/}
            {/*        aria-selected={color === selectedColor}*/}
            {/*      >*/}
            {/*        {color}*/}
            {/*      </ParamLink>*/}
            {/*    ))}*/}
            {/*  </Fragment>*/}
            {/*)}*/}
          </div>
          {/* <div className="flex flex-wrap border-b border-slate-200">
            {tabs.map((tab) => (
              <ParamLink
                key={tab}
                className="px-6 py-1 aria-selected:border-secondary border-transparent border-b-2"
                paramState={{ tab: tab }}
                aria-selected={tab === currentTab}
              >
                {tab}
              </ParamLink>
            ))}
          </div> */}

          {/* Bulk selection info */}
          <div className="flex gap-3">
            {!!bulkSelectedItems.length ? (
              <Fragment>
                {bulkSelectedItems.length + " selected"}{" "}
                <ParamLink className="link" paramState={{ toggle_modal: "side", modal_detail_name: bulkKey }}>
                  Bulk Edit
                </ParamLink>
                <ActionForm confirmMessage={bulkDeleteMsg}>
                  {bulkSelectedItems.map((item) => {
                    return (
                      <Fragment key={item.id}>
                        <RInput table={"rentable"} field={"id"} index={item.id} value={item.id} />
                        {item.deleted_at !== at_infinity_value ? (
                          <Fragment>
                            <RInput table="rentable" field="data.deleted_at" index={item.id} type="hidden" value={at_infinity_value} />
                          </Fragment>
                        ) : item.aggregate?.total_count ? (
                          <Fragment>
                            <RInput table="rentable" field="data.deleted_at" index={item.id} type="hidden" value={at_now_value} />
                          </Fragment>
                        ) : (
                          <Fragment>
                            <OperationInput table="rentable" value="delete" index={item.id} />
                            {item.rentable_services.map((item) => (
                              <Fragment key={item.id}>
                                <RInput table="rentable_service" field="id" index={item.id} value={item.id} type="hidden" />
                                <OperationInput table="rentable_service" value="delete" index={item.id} />
                              </Fragment>
                            ))}
                          </Fragment>
                        )}
                      </Fragment>
                    );
                  })}
                  <SubmitButton className="link text-red-500">
                    {/*<TrashIcon className="w-4 h-4" />*/}
                    {bulkSelectedItems.length === bulkSelectedAlreadyDeleted.length
                      ? "Restore"
                      : bulkSelectedAlreadyDeleted.length
                        ? "Restore/Remove"
                        : "Remove"}
                  </SubmitButton>
                </ActionForm>
                <ParamLink className="link" paramState={{ ids: [] }}>
                  Clear
                </ParamLink>
              </Fragment>
            ) : (
              <span>{filteredItems.length} items</span>
            )}
          </div>

          <div className="overflow-auto">
            <div className="grid gap-x-4 text-sm" style={{ gridTemplateColumns: `repeat(${Object.keys(columns).length + 1}, 1fr)` }}>
              {/* Header row */}
              <div className="font-semibold contents">
                <div className="pt-3 pb-2">
                  <ParamLink paramState={{ ids: bulkSelectedItems.length ? [] : filteredItems.map((item) => item.id) }}>
                    <ControlledChecker
                      className={"border border-slate-300"}
                      checked={bulkSelectedItems.length ? (bulkSelectedItems.length === filteredItems.length ? "true" : "mixed") : "false"}
                    />
                  </ParamLink>
                </div>
                {Object.values(columns).map((column, index) => (
                  <div key={index} className="whitespace-nowrap flex items-center py-4">
                    {column.title}
                  </div>
                ))}
              </div>

              {/* Data rows */}
              {filteredItems.map((item) => {
                const isSelected = search.state.ids.includes(item.id);
                return (
                  <div
                    key={item.id}
                    className="grid grid-cols-subgrid gap-4 col-span-full relative group hover:bg-slate-50 transition-colors py-4 border-t border-slate-150"
                  >
                    <div>
                      <button
                        type="button"
                        // paramsActive={search.state.ids.includes(item.id)}
                        // paramState={{ ids: toggleArray(search.state.ids, item.id) }}
                        aria-selected={isSelected}
                        onClick={(e) => {
                          e.preventDefault();
                          if (e.shiftKey) {
                            const itemIndex = filteredItems.findIndex((i) => i.id === item.id);
                            const lastSelectedIndex = filteredItems.findIndex(
                              (i) => i.id === search.state.ids[search.state.ids.length - 1],
                            );
                            const finalLastIndex = lastSelectedIndex === -1 ? itemIndex : lastSelectedIndex;
                            const start = Math.min(itemIndex, finalLastIndex);
                            const end = Math.max(itemIndex, finalLastIndex);
                            const shifhtSelectedIds = filteredItems.slice(start, end + 1).map((i) => i.id);
                            const newIds = isSelected
                              ? search.state.ids.filter((id) => !shifhtSelectedIds.includes(id))
                              : [...search.state.ids, ...shifhtSelectedIds];
                            setState({ ids: newIds }, { replaceRoute: true });
                          } else {
                            setState({ ids: toggleArray(search.state.ids, item.id) }, { replaceRoute: true });
                          }
                        }}
                        className="group"
                      >
                        <Checker className="border border-slate-300" />
                      </button>
                    </div>
                    {Object.values(columns).map((column, index) => (
                      <div key={index}>{column.comp(item)}</div>
                    ))}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
      <SidePanel className="max-md:inset-x-0">
        <div className="h-full w-full md:min-w-96 md:max-w-96 space-y-6 bg-white overflow-auto">
          <div className={twMerge("flex flex-row items-center pl-2 sticky top-0 bg-white p-3")}>
            <ParamLink paramState={backParamState} className="p-2">
              <ChevronLeft className="w-5 h-5" />
            </ParamLink>
            <div>
              {search.state.modal_detail_name === bulkKey ? (
                <p>Bulk Edit ({bulkSelectedItems.length} items)</p>
              ) : selectedItem ? (
                <div>
                  <p className="text-xl">
                    {search.state.modal_detail_name === rentableCopyKey
                      ? "Copy Rental Item"
                      : search.state.modal_detail_name === editKey
                        ? "Edit Rental Item"
                        : "Rental Item"}
                  </p>
                  <p className="text-xs">{selectedItem.reference_id}</p>
                </div>
              ) : (
                <p className="text-xl">Create Rental Item</p>
              )}
            </div>
          </div>

          {search.state.modal_detail_name === bulkKey ? (
            <div>
              <BulkEditPanel items={bulkSelectedItems} />
            </div>
          ) : selectedItem && !search.state.modal_detail_name ? (
            <div className="space-y-5 px-3 pb-3">
              <div className="flex flex-row items-center justify-between">
                <p className=" text-slate-800 font-semibold">Details</p>
                {showEdit && (
                  <ParamLink
                    paramState={{ toggle_modal: "side", modal_detail_name: editKey, modal_detail_id: selectedItem.id }}
                    className="link font-semibold"
                  >
                    Edit
                  </ParamLink>
                )}
              </div>
              <div>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="contents">
                    <span className="text-slate-400">Category:</span>
                    <div className="text-slate-700">{selectedItem.type}</div>
                  </div>
                  <div className="contents">
                    <span className="text-slate-400">Title:</span>
                    <div className="text-slate-700">{selectedItem.title || "-"}</div>
                  </div>
                  <div className="contents">
                    <span className="text-slate-400">Brand:</span>
                    <div className="text-slate-700">{columns.brand.comp(selectedItem)}</div>
                  </div>
                  <div className="contents">
                    <span className="text-slate-400">Size:</span>
                    <div className="text-slate-700">{selectedItem.size}</div>
                  </div>
                  <div className="contents">
                    <span className="text-slate-400">Color:</span>
                    <div className="text-slate-700">{columns.color.comp(selectedItem)}</div>
                  </div>
                  <div className="contents">
                    <span className="text-slate-400">ID Number:</span>
                    <div className="text-slate-700">{selectedItem.reference_id}</div>
                  </div>
                  <div className="contents">
                    <span className="text-slate-400">Storage Location:</span>
                    <div className="text-slate-700">{selectedItem.storage_location || "-"}</div>
                  </div>
                  {/* <div className="contents">
                    <span className="text-slate-400">Total Times Used:</span>
                    <div className="text-slate-700">{columns.times_used.comp(selectedItem)}</div>
                  </div> */}
                  <div className="contents">
                    <span className="text-slate-400">First Use:</span>
                    <div className="text-slate-700">{columns.first_use.comp(selectedItem)}</div>
                  </div>
                  <div className="contents">
                    <span className="text-slate-400">Times Used:</span>
                    <div className="text-slate-700">{columns.times_used.comp(selectedItem)}</div>
                  </div>
                  <div className="contents">
                    <div className="text-slate-400 col-span-2 py-2">
                      <span className="text-sm">On {ctx.date.dateParam}</span>
                      <hr className="mt-2" />
                    </div>
                  </div>
                  <div className="contents">
                    <span className="text-slate-400">Last Service:</span>
                    <div className="text-slate-700">{columns.last_service.comp(selectedItem)}</div>
                  </div>
                  <div className="contents">
                    <span className="text-slate-400">Next Service:</span>
                    <div className="text-slate-700">{columns.next_service.comp(selectedItem)}</div>
                  </div>
                </div>
              </div>
              <hr />
              <RentableServiceSection showEdit={showEdit} />
              <hr />
              <RentableCommentSection />
              <hr />
              <div className="space-y-5">
                <ParamLink
                  paramState={{
                    toggle_modal: "side",
                    modal_detail_name: rentableCopyKey,
                    element_action: [fName("rentable", "data.reference_id")],
                    modal_detail_id: selectedItem.id,
                  }}
                  className="btn btn-basic text-left"
                  onClick={() => {
                    document.getElementsByName(fName("rentable", "data.reference_id")).forEach((el) => {
                      if (el instanceof HTMLInputElement) {
                        const copySuffix = " (copy)";
                        el.value = `${el.value}${copySuffix}`;
                        el.focus();
                        el.setSelectionRange(el.value.length - copySuffix.length, el.value.length);
                      }
                    });
                  }}
                >
                  <CopyIcon className="w-4 h-4" />
                  Copy
                </ParamLink>
                <div className="">
                  <ActionForm
                    confirmMessage={
                      selectedItem.deleted_at === at_infinity_value && !selectedItem.aggregate?.total_count
                        ? "Are you sure you want to delete this rental item?"
                        : undefined
                    }
                  >
                    <RInput table="rentable" field="id" value={selectedItem.id} type="hidden" />
                    {selectedItem.deleted_at !== at_infinity_value ? (
                      <div className="rounded-md border-primary border-2 p-3">
                        <p>
                          This item is out of use.
                          <br />
                          <RInput table="rentable" field="data.deleted_at" type="hidden" value={at_infinity_value} />
                          <SubmitButton className="btn btn-basic">
                            {/*<TrashIcon className="w-4 h-4" />*/}
                            Undo
                          </SubmitButton>
                        </p>
                      </div>
                    ) : selectedItem.aggregate?.total_count ? (
                      <div className="rounded-md border-primary border-2 p-3">
                        <p className="pb-3">This item cannot be deleted because it has a usage history.</p>
                        <RInput table="rentable" field="data.deleted_at" type="hidden" value={at_now_value} />
                        <SubmitButton className="btn btn-basic">
                          {/*<TrashIcon className="w-4 h-4" />*/}
                          Mark as out of use
                        </SubmitButton>
                        {/* <SubmitButton className="btn">Set as out of use</SubmitButton> */}
                      </div>
                    ) : (
                      <div className="rounded-md border-red-500 border-2 p-3">
                        <OperationInput table="rentable" value="delete" />
                        {selectedItem.rentable_services.map((item) => (
                          <Fragment key={item.id}>
                            <RInput table="rentable_service" field="id" index={item.id} value={item.id} type="hidden" />
                            <OperationInput table="rentable_service" value="delete" index={item.id} />
                          </Fragment>
                        ))}
                        <SubmitButton className="btn">
                          <TrashIcon className="w-4 h-4" />
                          Delete
                        </SubmitButton>
                      </div>
                    )}
                  </ActionForm>
                </div>
              </div>
            </div>
          ) : (
            <ActionForm onCheckEqual={defaultEqualCheck}>
              <OnFormSuccess
                onSuccess={() => {
                  refreshFormdata();
                }}
              />
              <RedirectParamsInput
                path="./"
                paramState={{
                  toggle_modal: selectedItem ? "side" : undefined,
                  modal_detail_id: selectedItem ? tableIdRef("rentable") : search.state.modal_detail_id,
                  modal_detail_name: undefined,
                }}
              />

              <div className="space-y-3 px-3">
                <ActionAlert />
                <RentableForm
                  rentable={
                    selectedItem
                      ? search.state.modal_detail_name === rentableCopyKey
                        ? {
                            ...selectedItem,
                            id: undefined,
                            first_use_date: undefined,
                            reference_id: `${selectedItem.reference_id} (copy)`,
                          }
                        : selectedItem
                      : { establishment_id: establishment.id }
                  }
                  brands={brands}
                  sizes={sizes}
                  types={types}
                  colors={colors}
                />
                <div className="flex flex-row gap-3 items-center justify-end ">
                  <ParamLink paramState={backParamState} className="btn">
                    Cancel
                  </ParamLink>
                  <SubmitButton className="btn btn-primary">Save</SubmitButton>
                </div>
              </div>
            </ActionForm>
          )}
        </div>
      </SidePanel>
    </Fragment>
  );
}
