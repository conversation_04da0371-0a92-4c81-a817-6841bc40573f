import type { DataFunctionArgs } from "@remix-run/server-runtime";
import type { AsyncReturnType } from "type-fest";
import { useLoaderData, useParams } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { SubmitButton } from "~/components/base/Button";
import { _establishment_detail, _establishment_path } from "~/misc/paths";
import { OperationInput, RedirectInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { RInput, RLabel } from "~/components/ResourceInputs";
import { ActionForm } from "~/components/form/BaseFrom";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { filesQuery } from "~/domain/file/file-queries";
import { InputFilesDefault } from "~/components/form/FilesInput";
import { notFound } from "~/misc/responses";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { arrayAgg } from "~/kysely/kysely-helpers";
import { Fragment } from "react";
import { ParamLink } from "~/components/meta/CustomComponents";

export { action } from "~/routes/_all._catch.resource";

const isUuidRegex = /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i;

export const loader = async ({ request, params }: DataFunctionArgs) => {
  const idOrSlug = params.id;
  if (typeof idOrSlug !== "string") throw notFound("invalid id");
  const isUuid = isUuidRegex.test(idOrSlug);
  return kysely
    .selectFrom("operator")
    .where(isUuid ? "operator.id" : "operator.slug", "=", idOrSlug)
    .selectAll("operator")
    .select((eb) => [
      eb
        .selectFrom("view")
        .where("view.operator_id", "=", eb.ref("operator.id"))
        .select((eb) => arrayAgg(eb.ref("view.id"), "uuid").as("ids"))
        .as("view_ids"),
      jsonArrayFrom(
        eb
          .selectFrom("establishment")
          .where("establishment.operator_id", "=", eb.ref("operator.id"))
          .leftJoin("spot", "spot.id", "establishment.spot_id")
          .select(["establishment.id", "establishment.location_name", "spot.name as spot_name"]),
      ).as("establishments"),
      jsonArrayFrom(filesQuery("operator_logo").where("file_target.target_id", "=", eb.ref("operator.id"))).as("logo_files"),
      jsonArrayFrom(filesQuery("operator_favicon").where("file_target.target_id", "=", eb.ref("operator.id"))).as("favicon_files"),
    ])
    .executeTakeFirstOrThrow();
};

type LoaderResponse = AsyncReturnType<typeof loader>;

export default function Page() {
  const search = useSearchParams2();
  const operator = useLoaderData<LoaderResponse>();
  console.log("operaotr", operator);
  // const [uploadedFile, setUploadedFile] = useState<{
  //   id: string;
  //   filename: string;
  // } | null>(operator.logo_file ? { id: operator.logo_file.file_id, filename: operator.logo_file.filename } : null);
  const params = useParams();
  const operatorId = params.id!;

  return (
    <div className="app-container space-y-6 py-6">
      <h1 className="text-2xl font-bold">{operator.name}</h1>
      <div className="space-y-3 rounded-md border border-slate-400 p-6">
        <h2 className="text-xl font-bold">Locations</h2>
        <div>
          {operator.establishments.map((establishment) => (
            <ParamLink
              path={_establishment_detail(establishment.id)}
              key={establishment.id}
              className="rounded-md bg-slate-50 p-3 hover:bg-slate-100 hover:underline active:underline"
            >
              {establishment.spot_name || "unspecified"}
              {establishment.location_name ? establishment.location_name + "" : ""}
            </ParamLink>
          ))}
        </div>
      </div>
      <ActionForm className="space-y-3 rounded-md border border-slate-400 p-6" key={search.state.rerender + ""}>
        <RedirectParamsInput path={"./"} paramState={{ rerender: search.state.rerender + 1 }} />
        <h2 className="text-xl font-bold">Edit</h2>
        <RInput table={"operator"} field={"id"} value={operatorId} />
        <div className="space-y-6">
          <div>
            <RInput table="operator" label={"Name"} field={"data.name"} type="text" className="input" defaultValue={operator.name} />
          </div>
          <div>
            <RInput table="operator" label={"Slug"} field={"data.slug"} type="text" className="input" defaultValue={operator.slug || ""} />
          </div>
          <RLabel table={"operator"} field={"data.enterprise"} className="flex flex-row items-center gap-2">
            enterprise
            <RInput
              className="checkbox"
              table={"operator"}
              field={"data.enterprise"}
              type={"checkbox"}
              hiddenType={"__boolean__"}
              defaultChecked={operator.enterprise}
            />
          </RLabel>
          <div>
            <h3>Logo</h3>
            <InputFilesDefault defaultValue={operator.logo_files} target_id={operator.id} target={"operator_logo"} />
          </div>
          <div>
            <h3>Favicon</h3>
            <InputFilesDefault defaultValue={operator.favicon_files} target_id={operator.id} target={"operator_favicon"} />
          </div>
          <SubmitButton className={"btn btn-primary"}>Save</SubmitButton>
        </div>
      </ActionForm>
      {/*<ActionForm replace className="space-y-3 rounded-md border border-slate-400 p-6">*/}
      {/*  <h2 className="text-xl font-bold">Logo</h2>*/}
      {/*  <InputFilesDefault defaultValue={operator.logo_files} target_id={operator.id} target={"operator_logo"} />*/}
      {/*  <SubmitButton className="btn btn-primary">Save</SubmitButton>*/}
      {/*</ActionForm>*/}
      {/*<ActionForm replace className="w-fit space-y-3 rounded-md border border-slate-400 p-6">*/}
      {/*  <UploadButton  onUploaded={file => setUploadedFile(file[0] || null)} />*/}
      {/*  <SubmitButton>Save</SubmitButton>*/}
      {/*</ActionForm>*/}
      <ActionForm replace className="w-fit space-y-3 rounded-md border border-red-500 p-6">
        <h2 className="text-xl font-bold">Delete operator</h2>
        <RedirectInput value={_establishment_path} />
        <RInput table={"operator"} field={"id"} value={operatorId} />
        <OperationInput table={"operator"} value={"delete"} />
        {operator.view_ids?.map((id) => (
          <Fragment key={id}>
            <RInput table={"view"} field={"id"} index={id} value={id} />
            <OperationInput table={"view"} value={"delete"} index={id} />
          </Fragment>
        ))}
        {operator.establishments.length > 0 && <p>Cannot delete operator as there are operator establishments under it.</p>}
        <SubmitButton disabled={operator.establishments.length > 0} className="btn btn-red">
          Delete
        </SubmitButton>
      </ActionForm>
    </div>
  );
}
