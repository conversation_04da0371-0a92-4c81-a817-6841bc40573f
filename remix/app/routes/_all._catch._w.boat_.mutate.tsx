import type { DataFunctionArgs } from "@remix-run/server-runtime";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { SubmitButton } from "~/components/base/Button";
import React from "react";
import { _boat } from "~/misc/paths";
import { OperationInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { RInput } from "~/components/ResourceInputs";
import { ActionForm, defaultEqualCheck } from "~/components/form/BaseFrom";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { SharedBoatFields } from "~/domain/boat/BoatForm";
import { useAppContext } from "~/hooks/use-app-context";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: DataFunctionArgs) => {
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  const result = await kysely
    .selectFrom("boat")
    .where("id", "=", state.id)
    .where("boat.establishment_id", "=", state.establishment_id || state.persist_establishment_id)
    .selectAll("boat")
    .executeTakeFirst();

  return {
    boat: result,
  };
};

export default function Page() {
  const appCtx = useAppContext();
  const response = useLoaderData<typeof loader>();
  const establishment = appCtx.establishment;

  if (!establishment) return <div>Establishment required</div>;
  const establishmentId = response.boat?.establishment_id || establishment.id;

  return (
    <div className="app-container space-y-3">
      <div>
        <h1 className="text-xl font-bold first-letter:capitalize">{response.boat ? "Edit boat" : "Create boat"}</h1>
        <h2 className="text-slate-600">{getEstablishmentName(establishment)}</h2>
      </div>
      <ActionForm className="flex flex-wrap gap-3" onCheckEqual={defaultEqualCheck}>
        {response.boat && <RInput type="hidden" table={"boat"} field={"id"} value={response.boat.id} />}
        <RedirectParamsInput path={_boat} paramState={{ persist_establishment_id: establishmentId }} />
        <RInput table={"boat"} field={"data.establishment_id"} value={establishmentId} type={"hidden"} />
        <SharedBoatFields boat={response.boat} />
      </ActionForm>
      {response.boat && (
        <ActionForm confirmMessage={`Are you sure you want to delete boat ${response.boat.name}?`}>
          <hr className="mb-5 mt-3" />
          <RedirectParamsInput path={_boat} paramState={{ persist_establishment_id: establishmentId }} />
          <RInput table={"boat"} field={"id"} value={response.boat.id} />
          <OperationInput table={"boat"} value={"delete"} />
          <div className="flex flex-row items-end">
            <SubmitButton type="submit" className="btn btn-red">
              Delete boat
            </SubmitButton>
          </div>
        </ActionForm>
      )}
    </div>
  );
}
