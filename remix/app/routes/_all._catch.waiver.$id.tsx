import { Trans } from "@lingui/react/macro";
import { Link, useLoaderData, useParams } from "@remix-run/react";
import React, { Fragment, useMemo, useState } from "react";
import type { LoaderFunctionArgs } from "@remix-run/router";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { getSessionSimple } from "~/utils/session.server";
import { kysely } from "~/misc/database.server";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { establishmentQb } from "~/domain/establishment/queries";
import { allowedParticipantIdsFor } from "~/domain/participant/participant-auth-queries.server";
import { notFound, notFoundOrUnauthorzied } from "~/misc/responses";
import { appConfig } from "~/config/config.server";
import { createPageOverwrites } from "~/misc/consts";
import { participantSimpleQb } from "~/domain/participant/participant.queries.server";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { ActionForm } from "~/components/form/BaseFrom";
import { ActionAlert } from "~/components/ActionAlert";
import { HiddenTypeInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { _participant_detail, _waiver } from "~/misc/paths";
import { LabelInput, LabelTextarea, RInput } from "~/components/ResourceInputs";
import { letters, translateQuestions } from "~/domain/medical/medical";
import { AnimatingDiv } from "~/components/base/base";
import { fName, tableIdRef } from "~/misc/helpers";
import { ParamLink } from "~/components/meta/CustomComponents";
import { SubmitButton } from "~/components/base/Button";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { MarkdocComp } from "~/domain/waiver/waiver-components";
import { addToNow, arrayAgg, ascNullsLast, descNullsLast, unnestArray } from "~/kysely/kysely-helpers";
import { getClientLocales } from "~/misc/utils";
import { defaultLangauge } from "~/misc/vars";
import { CallbackInput } from "~/domain/callback/callback.components";
import { useAppContext } from "~/hooks/use-app-context";
import { SignatureFullComp } from "~/domain/participant/participant.signature.component";
import { activeLanguages, languages } from "~/data/languages";
import { I18nProvider } from "@lingui/react";
import { setupI18n } from "@lingui/core";
import { CDialog } from "~/components/base/Dialog";
import { allMarkdocComps } from "~/domain/waiver/waiver-markdoc";
import { OtpBlock } from "~/domain/otp/otp-components";
import { AcceptDigitalSigning } from "~/domain/participant/participant.components";
import { getWaiverType } from "~/domain/waiver/waiver-vars";
import { flat } from "remeda";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const record = paramsToRecord(url.searchParams);
  const waiverId = params.id!;

  const getLocals = () => {
    const clientLocals = getClientLocales(request) || [];
    return [record.page_lang, record.persist_page_lang, record.lang, ...clientLocals, defaultLangauge]
      .filter((locale) => !!locale)
      .map((locale) => locale?.toLowerCase() as string);
  };

  const locales = getLocals();
  const session = await getSessionSimple(request);

  const allowedForPrint = record.print_token === appConfig.PRINT_TOKEN;

  const waiver = await kysely
    .selectFrom("waiver")
    .selectAll("waiver")
    .where("waiver.id", "=", waiverId)
    .select((waiverEb) => {
      return [
        jsonObjectFrom(establishmentQb.where("establishment.id", "=", record.persist_establishment_id)).as("establishment"),
        jsonObjectFrom(
          waiverEb
            .selectFrom("waiver_translation")
            .leftJoin(unnestArray(locales, "locale"), "locale.key", "waiver_translation.language_code")
            .selectAll("waiver_translation")
            .where("waiver_translation.waiver_id", "=", waiverEb.ref("waiver.id"))
            .orderBy(
              (eb) =>
                eb(
                  eb
                    .selectFrom("participant_waiver")
                    .where("participant_waiver.id", "=", record.participant_waiver_id)
                    .select("participant_waiver.signed_language_code"),
                  "=",
                  eb.ref("waiver_translation.language_code"),
                ),
              descNullsLast,
            )
            .orderBy("locale.pos", ascNullsLast)
            .orderBy("waiver_translation.sort_order", ascNullsLast)
            .limit(1),
        ).as("translation"),
        jsonObjectFrom(
          participantSimpleQb(kysely)
            .$if(!allowedForPrint, (eb) =>
              eb.where(
                "participant.id",
                "in",
                allowedParticipantIdsFor({
                  trx: kysely,
                  request: request,
                  ctx: { session_id: session.session_id },
                }).select("_participant.id"),
              ),
            )
            .where((eb) => {
              const participantId = record.participant_waiver_id
                ? eb
                    .selectFrom("participant_waiver")
                    .where("participant_waiver.id", "=", record.participant_waiver_id)
                    .select("participant_waiver.participant_id")
                : record.participant_id;
              return eb("participant.id", "=", participantId);
            })
            .select((eb) => [
              eb
                .selectFrom("participant_token")
                .select("participant_token.created_at")
                .where("participant_token.participant_id", "=", eb.ref("participant.id"))
                .where("participant_token.token", "=", record.persist_token)
                .where("participant_token.created_at", ">", addToNow(-30, "minutes"))
                .as("valid_token"),
              jsonObjectFrom(
                establishmentQb
                  .select("establishment.require_email_verification_for_signing")
                  .where("establishment.id", "=", eb.ref("customer.establishment_id")),
              ).as("establishment"),
              jsonObjectFrom(
                eb
                  .selectFrom("participant_waiver")
                  .where("participant_waiver.waiver_id", "=", waiverEb.ref("waiver.id"))
                  .where("participant_waiver.id", "=", record.participant_waiver_id)
                  .select((eb) => [
                    jsonArrayFrom(
                      eb
                        .selectFrom("signature")
                        .where("signature.participant_waiver_id", "=", eb.ref("participant_waiver.id"))
                        .selectAll("signature"),
                    ).as("signatures"),
                  ])
                  .selectAll("participant_waiver"),
              ).as("participant_waiver"),
            ]),
        ).as("participant"),
        waiverEb
          .selectFrom("waiver_translation")
          .where("waiver_translation.waiver_id", "=", waiverEb.ref("waiver.id"))
          .select((eb) => arrayAgg(eb.ref("waiver_translation.language_code")).as("language_codes"))
          .as("available_languages"),
      ];
    })
    .executeTakeFirst();

  if (!waiver) throw notFound();
  if ((record.participant_id || record.participant_waiver_id) && !waiver.participant)
    throw notFoundOrUnauthorzied("Participant was not found or you are unauthorized");

  const appLanguage = activeLanguages.find((language) => language === waiver.translation?.language_code) || defaultLangauge;

  const { messages } = await import(`./../../generated/locales/${appLanguage}/messages.po`);

  return {
    ...createPageOverwrites({ show_whatsapp: false, simple_view: true }),
    waiver: waiver,
    translations: messages,
    app_language_code: appLanguage,
    print: allowedForPrint,
  };
};

const QuestionaireStep = () => {
  const data = useLoaderData<typeof loader>();
  const waiver = data.waiver;
  const participant = waiver.participant;
  const [answers, setAnswers] = useState<Record<string, boolean>>(() => {
    const initAnswers: Record<string, boolean> = {};
    participant?.participant_waiver?.medical_yes_answers?.forEach((answer) => {
      initAnswers[answer] = true;
    });
    return initAnswers;
  });
  const alreadyFilled = !!participant?.participant_waiver;

  return (
    <div className="space-y-3 print:text-xs">
      <HiddenTypeInput name={fName("participant_waiver", "data.medical_yes_answers")} value={"__empty_array__"} />
      <div className="space-y-3">
        {translateQuestions(waiver.translation?.questions).map((q, rootQuestionIndex) => {
          const rootQuestionFieldName = q.name;
          const isDirty = typeof answers[rootQuestionFieldName] === "boolean" || alreadyFilled;
          const answeredYes = !!answers[rootQuestionFieldName];
          return (
            <div key={rootQuestionFieldName} className="flex flex-row gap-3 border-t border-slate-300 pt-4 pb-2 break-inside-avoid">
              <strong>{rootQuestionIndex + 1}.</strong>
              <AnimatingDiv className="space-y-3">
                <div className="relative">{q.question} </div>
                {/*<div>Index - Fieldname: {rootQuestionFieldName}</div>*/}
                {/*<div>isdirty: {isDirty ? "yes" : "no"}</div>*/}
                {/*<div>answeryes: {answeredYes ? "yes" : "no"}</div>*/}
                <div className="flex flex-wrap gap-5 items-center">
                  <div>
                    <label>
                      <input
                        required
                        name={fName("participant_waiver", "data.medical_yes_answers", 0, q.totalIndex)}
                        className="radio"
                        disabled={alreadyFilled}
                        type={"radio"}
                        checked={answeredYes}
                        onChange={() => {
                          setAnswers({ ...answers, [rootQuestionFieldName]: true });
                        }}
                        value={rootQuestionFieldName}
                      />
                      &nbsp;<Trans>Yes</Trans>
                    </label>
                  </div>
                  <div>
                    <label>
                      <input
                        required
                        name={fName("participant_waiver", "data.medical_yes_answers", 0, q.totalIndex)}
                        className="radio"
                        disabled={alreadyFilled}
                        checked={(isDirty || undefined) && !answeredYes}
                        onChange={() => {
                          setAnswers({ ...answers, [rootQuestionFieldName]: false });
                        }}
                        type={"radio"}
                        value={""}
                      />
                      &nbsp;<Trans>No</Trans>
                    </label>
                  </div>
                </div>
                {q.subQuestions && answeredYes && (
                  <div className="space-y-3 pl-2">
                    {q.subQuestions.map((sq, subQuestionIndex) => {
                      const subquestionFieldName = sq.name
                      const isDirty = typeof answers[subquestionFieldName] === "boolean" || alreadyFilled;
                      const answeredYes = !!answers[subquestionFieldName];

                      return (
                        <div key={subQuestionIndex} className="flex flex-row gap-3">
                          <strong>
                            {rootQuestionIndex + 1}
                            {letters[subQuestionIndex]}.
                          </strong>
                          <div className="space-y-2">
                            <p>{sq.question}</p>
                            <div className="flex flex-wrap gap-4">
                              <div>
                                <label>
                                  <input
                                    required
                                    className="radio"
                                    disabled={alreadyFilled}
                                    name={fName("participant_waiver", "data.medical_yes_answers", 0, sq.totalIndex)}
                                    type={"radio"}
                                    value={subquestionFieldName}
                                    checked={answeredYes}
                                    onChange={() => {
                                      setAnswers({ ...answers, [subquestionFieldName]: true });
                                    }}
                                  />
                                  &nbsp;<Trans>Yes</Trans>
                                </label>
                              </div>
                              <div>
                                <label>
                                  <input
                                    required
                                    className="radio"
                                    disabled={alreadyFilled}
                                    name={fName("participant_waiver", "data.medical_yes_answers", 0, sq.totalIndex)}
                                    type={"radio"}
                                    value={""}
                                    checked={(isDirty || undefined) && !answeredYes}
                                    onChange={() => {
                                      setAnswers({ ...answers, [subquestionFieldName]: false });
                                    }}
                                  />
                                  &nbsp;<Trans>No</Trans>
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </AnimatingDiv>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const MedicalEvaluation = () => {
  const data = useLoaderData<typeof loader>();
  const participant = data.waiver.participant;
  return (
    <div className="space-y-6 break-inside-avoid">
      <h3 className="text-2xl font-bold">
        <span>
          <Trans>Diver Medical</Trans>
        </span>{" "}
        |{" "}
        <span className="font-semibold">
          <Trans>Medical Examiner's Evaluation Form</Trans>
        </span>
      </h3>
      <div className="flex flex-row gap-3">
        <label>
          <span>
            <Trans>Participant name</Trans>
          </span>
          <input className="input" />
        </label>
        <label>
          <span>
            <Trans>Birthdate</Trans>
          </span>
          <input className="input" />
        </label>
      </div>
      <p>
        <Trans>
          The above-named person requests your opinion of his/her medical suitability to participate in recreational scuba diving or
          freediving training or activity. Please visit{" "}
          <a
            className="link"
            href={"https://www.uhms.org/resources/featured-resources/recreational-diving-medical-screening-system.html"}
            target={"_blank"}
          >
            uhms.org
          </a>{" "}
          for medical guidance on medical conditions as they relate to diving. Review the areas relevant to your patient as part of your
          evaluation.
        </Trans>
      </p>
      <div className="space-y-3">
        <h4 className="font-bold">Evaluation Result</h4>
        <label className="flex flex-row gap-3 items-center">
          <input type={"checkbox"} className="checkbox" />
          <span>
            <Trans>Approved – I find no conditions that I consider incompatible with recreational scuba diving or freediving.</Trans>
          </span>
        </label>
        <label className="flex flex-row gap-3 items-center">
          <input type={"checkbox"} className="checkbox" />
          <span>
            <Trans>Not approved – I find conditions that I consider incompatible with recreational scuba diving or freediving</Trans>
          </span>
        </label>
      </div>
      <div className="grid grid-cols-2 gap-6 [&_label]:font-semibold">
        <div>
          <LabelTextarea
            required
            rows={5}
            label={<Trans>Signature of certified medical doctor or other legally certified medical provider</Trans>}
            className="input"
          />
        </div>
        <div>
          <LabelInput label={<Trans>Date (dd/mm/yyyy)</Trans>} required className="input" />
        </div>
        <div>
          <LabelInput label={<Trans>Medical Examiner’s Name</Trans>} required className="input" />
        </div>
        <div>
          <LabelInput label={<Trans>Clinical Degrees/Credentials</Trans>} required className={"input"} />
        </div>
        <div>
          <LabelInput label={<Trans>Clinic/Hospital</Trans>} className="input" />
        </div>
        <div>
          <LabelInput label={<Trans>Address</Trans>} className="input" />
        </div>
        <div>
          <LabelInput label={<Trans>Phone</Trans>} className="input" />
        </div>
        <div>
          <LabelInput label={<Trans>Email</Trans>} className="input" />
        </div>
      </div>
      <div className="mx-64">
        <div>
          <LabelTextarea
            rows={6}
            label={
              <span className="font-semibold">
                <Trans>Physician/Clinic Stamp (optional)</Trans>
              </span>
            }
            className="input"
          />
        </div>
        <div>
          <Trans>
            Created by the{" "}
            <a
              href={"https://www.uhms.org/resources/featured-resources/recreational-diving-medical-screening-system.html"}
              target="_blank"
              className="link"
            >
              Diver Medical Screen Committee
            </a>{" "}
            in association with the following bodies:
          </Trans>
          <p className="font-semibold">The Undersea & Hyperbaric Medical Society</p>
          <p className="font-semibold">DAN (US)</p>
          <p className="font-semibold">DAN Europe</p>
          <p className="font-semibold">Hyperbaric Medicine Division, University of California, San Diego</p>
        </div>
      </div>
    </div>
  );
};

const Siganture = () => {
  const data = useLoaderData<typeof loader>();
  const participant = data.waiver.participant;
  return (
    <Fragment>
      <SignatureFullComp participant={participant} signatures={participant?.participant_waiver?.signatures} />
      {participant ? (
        <RedirectParamsInput path={_participant_detail(participant.id)} paramState={{}} />
      ) : (
        <RedirectParamsInput path={_waiver} paramState={{}} />
      )}
    </Fragment>
  );
};

export default function FormPage() {
  const ctx = useAppContext();
  const params = useParams();
  const search = useSearchParams2();
  const data = useLoaderData<typeof loader>();

  const i18nn = useMemo(() => {
    const nes = setupI18n();
    // nes.activate(app.locale);
    nes.loadAndActivate({ locale: data.app_language_code, messages: data.translations });
    // nes.activate(app.locale);
    return nes;
  }, [data.app_language_code]);

  const translation = data.waiver.translation;
  if (!translation) return <div>No translation available</div>;

  const participant = data.waiver.participant;

  // const formNameParam = params.form as FormName;
  // const formMeta = forms[formNameParam];
  // const form = getFilledForm(data, formNameParam);
  // if (!formMeta) return <div>Form not found</div>;
  // if (!form) return <div>Not found</div>;

  const availableLanguages = data.waiver.available_languages;

  const editable = !!participant && !participant?.participant_waiver?.signatures?.length;

  const waiverType = getWaiverType(participant?.participant_waiver?.waiver_type || data.waiver.type);

  return (
    <I18nProvider i18n={i18nn}>
      <div className="app-container">
        {/*{ctx.environment === "development" && (*/}
        {/*  <ParamLink className="link print:hidden" path={_waiver_pdf(data.waiver.id)}>*/}
        {/*    pdf*/}
        {/*  </ParamLink>*/}
        {/*)}*/}
        <div className="flex flex-row gap-3 print:hidden">
          <Link to={-1 as any} className="link">
            back
          </Link>
          {availableLanguages && availableLanguages.length > 0 && (
            <div className="flex-1">
              <CDialog dialogname={"page_language"}>
                <div className="space-y-6">
                  <p className="text-xl text-slate-500 text-center">Select waiver language</p>
                  <div className="grid grid-cols-2 gap-3 ">
                    {availableLanguages.map((langCode) => {
                      const langauge = languages.find((language) => language.code === langCode);
                      return (
                        <div key={langCode}>
                          <ParamLink
                            key={langCode}
                            aria-selected={langCode === search.state.lang}
                            replace
                            paramState={{ toggle_modal: undefined, persist_page_lang: langCode, page_lang: langCode }}
                            reload
                            className={"btn aria-selected:bg-slate-100 hover:bg-slate-200 rounded-full"}
                          >
                            {langauge?.name} <span className="uppercase">({langCode})</span>
                          </ParamLink>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </CDialog>
              <div className="text-right flex flex-row items-center justify-end gap-1">
                <span className="text-slate-500 text-xs">Waiver language</span> &nbsp;
                <ParamLink className="link uppercase" replace paramState={{ toggle_modal: "page_language" }}>
                  {translation.language_code}
                </ParamLink>
              </div>
            </div>
          )}
        </div>
        {!data.print &&
        participant &&
        participant.establishment?.require_email_verification_for_signing &&
        (!ctx.verified_at || ctx.email !== participant.email) &&
        !participant.valid_token ? (
          <ActionForm>
            <OtpBlock user_id={participant.user_id} />
          </ActionForm>
        ) : !data.print && participant && !participant.digital_signing_agreed_at ? (
          <ActionForm>
            <AcceptDigitalSigning participant={participant} />
          </ActionForm>
        ) : (
          <ActionForm replace>
            <ActionAlert scrollTo />
            <RInput table={"participant_waiver"} field={"data.waiver_id"} value={data.waiver.id} type={"hidden"} />
            <RInput table={"participant_waiver"} field={"data.participant_id"} value={data.waiver.participant?.id} type={"hidden"} />
            <RInput table={"participant_waiver"} field={"data.signed_language_code"} value={translation.language_code} type={"hidden"} />
            <CallbackInput callbackName={"participant_waiver_mutation"} target_id={tableIdRef("participant_waiver")} />
            <div className="space-y-6">
              <MarkdocComp
                content={data.waiver.translation?.markdoc || ""}
                comps={{
                  ...allMarkdocComps,
                  MedicalQuestions: waiverType.key === "medical" && QuestionaireStep,
                  Signature: (participant?.participant_waiver?.signature_required || waiverType.signature) && Siganture,
                  MedicalEvaluation: waiverType.key === "medical" && participant?.participant_waiver?.upload_required && MedicalEvaluation,
                }}
                vars={{
                  participant: data.waiver?.participant,
                  operator: getEstablishmentName(data.waiver.participant?.establishment || data.waiver.establishment),
                  input: data.waiver?.participant?.participant_waiver?.input,
                }}
              />
              {editable && (
                <div className="pb-6">
                  {/*<div className={"py-3"}>*/}
                  {/*  <label className="flex flex-row gap-3 items-center">*/}
                  {/*    <input type={"checkbox"} className="checkbox" required />*/}
                  {/*    <span>*/}
                  {/*      <Trans>I agree to sign this document digitaly</Trans>*/}
                  {/*    </span>*/}
                  {/*  </label>*/}
                  {/*</div>*/}
                  <SubmitButton className="btn btn-primary print:hidden p-4 px-10 text-xl">
                    <Trans>Save</Trans>
                  </SubmitButton>
                </div>
              )}
            </div>
          </ActionForm>
        )}
      </div>
    </I18nProvider>
  );
}
