import { kysely } from "~/misc/database.server";
import { useLoaderData, useNavigate, useNavigation } from "@remix-run/react";
import React from "react";
import { _establishment_detail, _notification } from "~/misc/paths";
import { RInput, <PERSON><PERSON><PERSON><PERSON>, RSelect } from "~/components/ResourceInputs";
import { tableIdRef } from "~/misc/helpers";
import { SubmitButton } from "~/components/base/Button";
import { ActionAlert } from "~/components/ActionAlert";
import { ActionForm, defaultEqualCheck } from "~/components/form/BaseFrom";
import { RedirectInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { useAppContext } from "~/hooks/use-app-context";
import { LoaderFunctionArgs } from "@remix-run/router";
import { ParamLink } from "~/components/meta/CustomComponents";
import { ActivityReminderEmail, placeholder } from "~/domain/participant/participant-email-templates";
import { EmailMarkdocComp } from "~/domain/email/email-components";
import { baseEmailMarkdocComps } from "~/domain/email/email-markdoc";
import { ActivityDetails, BookingLink, EstablishmentContact, ParticipantInfo } from "~/domain/email/email-template-components";
import { defaultPostActivityEmailTemplate } from "~/domain/email/post-activity-email-template";
import { EstablishmentLayout } from "~/components/AllowedForEstablishment";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);
  const establishmentQb = kysely
    .selectFrom("establishment")
    .selectAll("establishment")
    .where("establishment.id", "=", state.establishment_id || state.persist_establishment_id);

  const establishment = await establishmentQb.executeTakeFirst();

  return {
    item: establishment,
  };
};

const afterActivityNotificationEnabled = false;

export default function Page() {
  const app = useAppContext();
  const response = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  const navigation = useNavigation();
  const isLoading = !!navigation.formData;

  return (
    <EstablishmentLayout className="app-container" title={<h1 className="text-xl font-semibold mb-4">Notification Settings</h1>}>
      <ActionForm onCheckEqual={defaultEqualCheck}>
        <RedirectParamsInput path={_notification} paramState={{}} />
        {!!response.item?.id && <RInput table={"establishment"} field={"id"} value={response.item.id} />}
        <div className="space-y-9 pb-6">
          <ActionAlert scrollTo />
          <div className="space-y-2">
            <h2 className="font-semibold">Registration notifications</h2>
            <p className="text-slate-500">
        Fill the email below if you&#39;d like to receive completed registration forms at a different email address than your general email address.
              <br />
            If empty, your general email address ({app.establishment?.email}) will be used.
            </p>
            {/*<span>Notification email (if empty, contact email "{app.establishment?.email}" will be used)</span>*/}
            <RInput
              table={"establishment"}
              field={"data.notification_email"}
              className="input"
              type="email"
              defaultValue={response.item?.notification_email || ""}
              disabled={isLoading}
            />
          </div>
          <hr className="border-2" />
          <div className="space-y-3">
            <h2 className="font-semibold">Reminder notifications</h2>
            <div className="flex flex-col md:flex-row gap-3 p-3 border border-slate-200 rounded-md">
              <div className="space-y-3 flex-1">
                <div className="flex items-center gap-1 flex-1">
                  <RLabel table={"establishment"} field={"data.activity_reminder_in_days_before_start"}>
                    Activity reminder in days before start
                  </RLabel>
                </div>
                <p className="text-sm text-slate-500">
       By setting this option, an email reminder will be sent to the participant a specified number of hours before the activity
                  start date. Additionally, the email will include a call to action if the participant still has waivers to sign or has an
                  incomplete form.
                </p>
                <RSelect
                  table={"establishment"}
                  field={"data.activity_reminder_in_days_before_start"}
                  defaultValue={response.item?.activity_reminder_in_days_before_start || ""}
                  className="select"
                >
                  <option value="">Don't send reminder</option>
                  <option value="1">1 day</option>
                  <option value="2">2 days</option>
                  <option value="3">3 days</option>
                </RSelect>
              </div>
              <div className="md:max-w-[500px] w-full space-y-3 bg-white text-slate-700 p-3 flex-1">
                <h4 className="text-md text-slate-600">Email template:</h4>
                <hr className="border-slate-200" />
                <ActivityReminderEmail reminderTo={placeholder} />
              </div>
            </div>
            {afterActivityNotificationEnabled && (
              <div className="flex flex-col md:flex-row gap-3 p-3 border border-slate-200 rounded-md">
                <div className="space-y-3 flex-1">
                  <div className="flex items-center gap-1 flex-1">
                    <p>Send post-activity email</p>
                  </div>
                  <p className="text-sm text-slate-500">
                    Send a follow-up email to participants after their activity to ask for feedback and reviews.
                  </p>
                  <RSelect
                    table={"establishment"}
                    field={"data.activity_reminder_in_days_before_start"}
                    defaultValue={response.item?.post_activity_email_days_after || ""}
                    className="select"
                  >
                    <option value="">Don't send mail after activity</option>
                    <option value="1">1 day</option>
                    <option value="2">2 days</option>
                    <option value="3">3 days</option>
                    <option value="7">1 week</option>
                  </RSelect>

                  {/*<div className="space-y-3">*/}

                  {/*  <div>*/}
                  {/*    <RLabel table={"establishment"} field={"data.post_activity_email_subject"}>*/}
                  {/*      Email subject*/}
                  {/*    </RLabel>*/}
                  {/*    <RInput*/}
                  {/*      table={"establishment"}*/}
                  {/*      field={"data.post_activity_email_subject"}*/}
                  {/*      className="input"*/}
                  {/*      defaultValue={response.item?.post_activity_email_subject || defaultPostActivityEmailSubject}*/}
                  {/*      disabled={isLoading}*/}
                  {/*    />*/}
                  {/*  </div>*/}

                  {/*  <div>*/}
                  {/*    <RLabel table={"establishment"} field={"data.post_activity_email_template"}>*/}
                  {/*      Email template (Markdoc format)*/}
                  {/*    </RLabel>*/}
                  {/*    <textarea*/}
                  {/*      name={fName("establishment", "data.post_activity_email_template")}*/}
                  {/*      defaultValue={response.item?.post_activity_email_template || defaultPostActivityEmailTemplate}*/}
                  {/*      className="input w-full h-64 font-mono text-sm"*/}
                  {/*      disabled={isLoading}*/}
                  {/*    />*/}
                  {/*  </div>*/}
                  {/*</div>*/}
                </div>
                <div className="md:max-w-[500px] w-full space-y-3 bg-white text-slate-700 p-3 flex-1">
                  <h4 className="text-md text-slate-600">Post-activity email template:</h4>
                  <hr className="border-slate-200" />
                  <EmailMarkdocComp
                    content={defaultPostActivityEmailTemplate}
                    comps={{
                      ...baseEmailMarkdocComps,
                      ActivityDetails: () => (
                        <ActivityDetails
                          vars={{
                            participant_name: "John Doe",
                            participant_email: "<EMAIL>",
                            activity_name: "Scuba Diving Adventure",
                            activity_date: "2025-01-15",
                            establishment_name: "Dive Center",
                            establishment_email: "<EMAIL>",
                            booking_id: "12345",
                            booking_url: "https://example.com/booking/12345",
                            operator_name: "Dive Center",
                          }}
                        />
                      ),
                      ParticipantInfo: () => (
                        <ParticipantInfo
                          vars={{
                            participant_name: "John Doe",
                            participant_email: "<EMAIL>",
                            activity_name: "Scuba Diving Adventure",
                            activity_date: "2025-01-15",
                            establishment_name: "Dive Center",
                            establishment_email: "<EMAIL>",
                            booking_id: "12345",
                            booking_url: "https://example.com/booking/12345",
                            operator_name: "Dive Center",
                          }}
                        />
                      ),
                      BookingLink: () => (
                        <BookingLink
                          vars={{
                            participant_name: "John Doe",
                            participant_email: "<EMAIL>",
                            activity_name: "Scuba Diving Adventure",
                            activity_date: "2025-01-15",
                            establishment_name: "Dive Center",
                            establishment_email: "<EMAIL>",
                            booking_id: "12345",
                            booking_url: "https://example.com/booking/12345",
                            operator_name: "Dive Center",
                          }}
                        />
                      ),
                      EstablishmentContact: () => (
                        <EstablishmentContact
                          vars={{
                            participant_name: "John Doe",
                            participant_email: "<EMAIL>",
                            activity_name: "Scuba Diving Adventure",
                            activity_date: "2025-01-15",
                            establishment_name: "Dive Center",
                            establishment_email: "<EMAIL>",
                            booking_id: "12345",
                            booking_url: "https://example.com/booking/12345",
                            operator_name: "Dive Center",
                          }}
                        />
                      ),
                    }}
                    vars={{
                      participant_name: "John Doe",
                      participant_email: "<EMAIL>",
                      activity_name: "Scuba Diving Adventure",
                      activity_date: "2025-01-15",
                      establishment_name: "Dive Center",
                      establishment_email: "<EMAIL>",
                      booking_id: "12345",
                      booking_url: "https://example.com/booking/12345",
                      operator_name: "Dive Center",
                    }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="sticky bottom-0 z-10 flex flex-row items-center gap-3 bg-white py-3">
          <div className="flex-1" />
          <ParamLink className="hover:underline" type="button" path={_notification}>
            cancel
          </ParamLink>
          <SubmitButton className="btn btn-primary">Save</SubmitButton>
        </div>
      </ActionForm>
    </EstablishmentLayout>
  );
}
