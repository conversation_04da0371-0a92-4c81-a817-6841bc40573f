import { Form, useActionData, useNavigation, useRouteLoaderData } from "@remix-run/react";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import React, { useState } from "react";
import { AnimatingDiv } from "~/components/base/base";
import { Button } from "~/components/base/Button";
import type { LoaderResponse } from "./_all._catch.product.$id";
import { __product, _participant_mutate } from "~/misc/paths";
import { RInput, RLabel, RTextarea } from "~/components/ResourceInputs";
import { getProductTitle } from "~/domain/product/ProductItem";
import { useWindowLocation } from "~/utils/remix";
import { MoneyValue, useMoneyValue } from "~/components/field/MoneyValue";
import { strictWhatsapp } from "~/utils/formatters";
import { fName } from "~/misc/helpers";
import { activities } from "~/domain/activity/activity";
import { IkImage } from "~/components/IkImage";
import { ParamLink } from "~/components/meta/CustomComponents";
import { contactMetas, getContactMeta } from "~/domain/establishment/EstablishmentContact";
import { PolicyLink, TermsLink } from "~/components/shared";
import { addDays, format } from "date-fns";
import { postEvent } from "~/domain/event/event-fetcher";
import { UserRegisterFieldsForInquery } from "~/domain/user/UserRegisterFields";
import { InformationCircleIcon } from "@heroicons/react/20/solid";
import { useAppContext } from "~/hooks/use-app-context";
import { useCurrency } from "~/domain/currency/use-currency";
import { CDialog, DialogCloseButton } from "~/components/base/Dialog";
import { RedirectParamsInput } from "~/components/form/DefaultInput";

export { action } from "~/routes/_all._catch.resource";

const contactButtons = ["whatsapp", "email"] satisfies Array<keyof typeof contactMetas>;

export default function Page() {
  const location = useWindowLocation();
  const appContext = useAppContext();
  const { state } = useSearchParams2();
  const navigation = useNavigation();
  const actionData = useActionData();
  const { finalSelected } = useCurrency();
  const { item, establishment } = useRouteLoaderData(__product) as LoaderResponse;
  const firstPrice = item.product_prices[0];
  const [isTooltipOpen, setIsTooltipOpen] = useState(false);

  const moneyValue = useMoneyValue(
    firstPrice
      ? {
          nativeAmount: firstPrice.amount,
          nativeCurrency: firstPrice.currency_id,
          toCurrency: finalSelected,
          locale: establishment.locale,
        }
      : undefined,
  );

  const finalShortId = item.id;
  const shortUrl = location.origin + "/s/" + finalShortId;

  const nr = establishment.whatsapp ? strictWhatsapp(establishment.whatsapp) : "";

  const contactMeta = getContactMeta(state.modal_detail_name);
  return (
    <div className="app-container">
      <div className="space-y-3">
        {establishment.direct_booking_mode ? (
          <div className="flex justify-center gap-3">
            <ParamLink
              className="btn btn-primary md:px-48 text-lg max-md:w-full"
              path={_participant_mutate}
              paramState={{ product_id: item.id, form_id: item.establishment?.direct_booking_form_id || item.form_id }}
            >
              Book now!
            </ParamLink>
          </div>
        ) : (
          <div className="flex justify-center gap-3">
            {contactButtons
              .filter((btn) => !!establishment[btn])
              .map((btn) => {
                const meta = contactMetas[btn];
                return (
                  <ParamLink
                    key={btn}
                    paramState={{ modal_detail_name: btn, toggle_modal: "contact" }}
                    className={"btn btn-primary w-full md:max-w-xs"}
                    onClick={() => {
                      postEvent({
                        tag: btn,
                        type: "click",
                        url: window.location.href,
                      });
                    }}
                  >
                    <meta.Icon className="h-7 w-7" />
                    <span>{meta.label}</span>
                  </ParamLink>
                );
              })}
          </div>
        )}
        {/*<p className="text-left md:text-center">*/}
        {/*  <MdCheck className="inline-block text-green-500" /> {t`Ask your questions without obligations`}*/}
        {/*</p>*/}
      </div>
      <CDialog
        dialogname={"contact"}
        className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all"
      >
        <div className={"flex flex-row items-center justify-between"}>
          <h3 className="text-lg font-medium leading-6 text-gray-900">Contact {establishment.title}</h3>
          <DialogCloseButton />
        </div>
        <div className={"mt-2 space-y-3 pt-3"}>
          <div>
            <IkImage path={establishment.files?.[0]?.filename} w={540} h={150} className="h-[150px] w-full rounded-md object-cover" />
          </div>
          <p className="text-lg">{getProductTitle(item)}</p>
          {!!item.subtitle && <p className="text-slate-600">{item.subtitle}</p>}
          <div className={"pt-1"}>
            {contactMeta ? (
              <Form
                method="POST"
                onSubmit={(e) => {
                  const formdata = new FormData(e.currentTarget);
                  const email = appContext.email || formdata.get(fName("user", "data.email"));
                  const displayName = appContext.display_name || formdata.get(fName("user_session", "data.display_name"));
                  const pax = formdata.get(fName("inquiry", "data.nr_of_participants"));
                  const prefDate = formdata.get(fName("inquiry", "data.date"));
                  const flexible = formdata.get(fName("inquiry", "data.flexible_date"));
                  const question = formdata.get(fName("inquiry", "data.question"));

                  /****** Whatsapp template *******/
                  const whatsappMsg = `Hi ${establishment.operator_name},

I would like to inquire the following activity: 
${shortUrl}

${activities[item.activity_slug].name}
${getProductTitle(item)} - ${moneyValue}

Preferred date: ${prefDate ? prefDate : ""} ${flexible ? "(flexible dates)" : ""}
Pax: ${pax}
Name: ${displayName}
Email: ${email}

${question || ""}`;
                  const whatsappLink = encodeURI(`https://api.whatsapp.com/send?phone=${nr}&text=${whatsappMsg}`);

                  /****** End Whatsapp template *******/

                  /****** Start Email template *******/
                  const emailMsg = `Hi ${establishment.operator_name},

${question || ""}

${activities[item.activity_slug].name}
${getProductTitle(item)} - ${moneyValue}
${shortUrl}

Preferred date: ${prefDate ? prefDate : ""} ${flexible ? "(flexible dates)" : ""}
Pax: ${pax}
Name: ${displayName}
Email: ${email}`;
                  const emailSubject = `Inquiry via traveltruster`;
                  const emailLink = `mailto:${establishment.email}?subject=${emailSubject}&body=${encodeURIComponent(emailMsg)}`;
                  /****** End email template *******/

                  const link = contactMeta.key === "whatsapp" ? whatsappLink : emailLink;
                  window.open(link)?.focus();
                }}
              >
                <RedirectParamsInput path={"./"} paramState={{ modal_detail_name: undefined }} />
                <RInput table={"inquiry"} field={"data.communication_method"} value={contactMeta.key} type={"hidden"} />
                <RInput table={"inquiry"} field={"data.establishment_id"} value={item.establishment_id} type={"hidden"} />
                <RInput table={"inquiry"} field={"data.product_id"} value={item.id} type={"hidden"} />
                <div className="space-y-5">
                  <div>
                    <RInput
                      disabled={!!navigation.formData}
                      className="input"
                      table={"inquiry"}
                      field={"data.date"}
                      type="date"
                      defaultValue={format(addDays(new Date(), 1), "yyyy-MM-dd")}
                      label="Select a date"
                    />
                  </div>
                  <AnimatingDiv className="space-y-2">
                    <div className="flex flex-wrap items-center gap-1">
                      <RInput
                        disabled={!!navigation.formData}
                        table={"inquiry"}
                        field={"data.flexible_date"}
                        className="checkbox"
                        type={"checkbox"}
                      />
                      <RLabel table={"inquiry"} field={"data.flexible_date"}>
                        Flexible dates
                      </RLabel>
                      <button
                        type={"button"}
                        aria-selected={isTooltipOpen}
                        className="text-primary hover:text-primary-800 aria-selected:text-primary-800"
                        onClick={() => setIsTooltipOpen(!isTooltipOpen)}
                      >
                        <InformationCircleIcon className={"h-5 w-5"} />
                      </button>
                    </div>
                    {isTooltipOpen && (
                      <div className="rounded-md border border-primary p-3 italic text-slate-700">
                        <p>
                          Select this whenever you’re flexible in your dates.
                          <br />
                          This way the operator might be able to make you a better offer.
                        </p>
                      </div>
                    )}
                  </AnimatingDiv>
                  <div>
                    <RLabel table={"inquiry"} field={"data.question"}>
                      Ask your questions here
                    </RLabel>
                    <br />
                    <RTextarea disabled={!!navigation.formData} className="input" table={"inquiry"} field={"data.question"} />
                  </div>
                  <div>
                    <RInput
                      className="input"
                      type="number"
                      table={"inquiry"}
                      field={"data.nr_of_participants"}
                      label="Total participants"
                      defaultValue={1}
                      required
                      disabled={!!navigation.formData}
                    />
                  </div>
                  <UserRegisterFieldsForInquery />
                  <p className="text-xs text-slate-600">
                    By sending this inquiry, you agree with our <TermsLink /> and <PolicyLink />
                  </p>
                  <hr />
                  <div className="space-y-2">
                    {firstPrice && (
                      <p className="text-center font-bold">
                        <MoneyValue
                          nativeAmount={firstPrice.amount}
                          nativeCurrency={firstPrice.currency_id}
                          toCurrency={finalSelected}
                          locale={establishment.locale}
                        />
                      </p>
                    )}
                    <Button loading={!!navigation.formData} className="btn btn-primary w-full">
                      <contactMeta.value.Icon className="h-7 w-7" />
                    </Button>
                    <p className="text-center italic text-slate-800">Send inquiry via {contactMeta.value.label}</p>
                  </div>
                </div>
              </Form>
            ) : (
              <div>Thanks for contacting!</div>
            )}
          </div>
        </div>
      </CDialog>
    </div>
  );
}
