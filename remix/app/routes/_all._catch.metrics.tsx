import { redirect } from "@remix-run/server-runtime";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { getSessionSimple } from "~/utils/session.server";
import { ParamLink } from "~/components/meta/CustomComponents";
import { notFoundOrUnauthorzied, unauthorized } from "~/misc/responses";
import { activeUserSessionSimple, memberHasPermission, QbArgs } from "~/domain/member/member-queries.server";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { Fragment } from "react";
import type { StateInputKey } from "~/misc/parsers/global-state-parsers";
import { mergeStateToParams, paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { operatorQb } from "~/domain/operator/operator-queries";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { EstablishmentSeperator } from "~/domain/establishment/EstablishmentSeperator";
import { Error<PERSON>ey, findErrorMessage, getErrorMsgFromCatch } from "~/misc/error-translations";
import { addDays, endOfYear, format, startOfYear } from "date-fns";
import { Alert } from "~/components/base/alert";
import { LoaderFunctionArgs } from "@remix-run/router";
import { SearchField, SearchForm } from "~/components/SearchForm";
import { getDateFromParams } from "~/domain/planning/planning.helpers.server";
import { toUtc } from "~/misc/date-helpers";
import { Bar, BarChart, Cell, Pie, PieChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";
import { getFullProductTitle } from "~/domain/product/ProductItem";
import { useAppContext } from "~/hooks/use-app-context";
import { getMonthObj } from "~/domain/planning/plannings-helpers";
import { distinct, lower } from "~/kysely/kysely-helpers";
import { ArrowTrendingDownIcon, ArrowTrendingUpIcon } from "@heroicons/react/20/solid";
import { twMerge } from "tailwind-merge";
import { formatPrice } from "~/utils/money";
import { baseProductWithSelect } from "~/domain/product/product-queries.server";
import { getActivity } from "~/domain/activity/activity";
import { sumBy } from "remeda";
import { defaultCountryColor, getCountry } from "~/data/countries";
import { PieBlock, Total } from "~/components/metrics";
import { useBoolean } from "~/hooks/use-boolean";
import { ChevronLeft } from "lucide-react";
import { myGroupBy2 } from "~/misc/helpers";
import { defaultLocale } from "~/misc/vars";

export { action } from "~/routes/_all._catch.resource";

// https://blocks.tremor.so/
// https://github.com/recharts/recharts
const unkownColor = "grey";
const colorsByPostions = ["#66CDAA", "#4682B4", "#5F9EA0", "#B0C4DE"] as const;

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  const date = getDateFromParams(request);
  const monthObj = getMonthObj(date.monthParam);
  const todayDate = toUtc(date.todayParam);
  const formatToDate = (date: Date) => format(date, "yyyy-MM-dd");

  const filters = {
    today: formatToDate(todayDate),
    tomorrow: formatToDate(addDays(todayDate, 1)),
    thisYear: format(todayDate, "yyyy"),
    today30DaysLater: formatToDate(addDays(todayDate, 30)),
    today30DaysEarlier: formatToDate(addDays(todayDate, -30)),
    startOfThisYear: formatToDate(startOfYear(todayDate)),
    endOfThisYear: formatToDate(endOfYear(todayDate)),
  };

  const range = `[${monthObj.firstDate},${monthObj.lastDate}]`;
  const rangePreviousMonth = `[${monthObj.firstDatePreviousMonth},${monthObj.firstDate})`;

  const messages: string[] = [];

  const args: QbArgs = { trx: kysely, ctx: { session_id: session_id } };

  const targetEstablishments = kysely
    .selectFrom("establishment")
    .where("establishment.operator_id", "=", state.persist_operator_id)
    .$if(!!state.persist_establishment_id, (eb) => eb.where("establishment.id", "=", state.persist_establishment_id))
    .$if(state.persist_toggle_establishment_ids.length > 0, (eb) =>
      eb.where("establishment.id", "not in", state.persist_toggle_establishment_ids),
    )
    .where("establishment.id", "in", memberHasPermission(args, "metrics").select("_member.establishment_id"));

  try {
    const allowedCtx = await activeUserSessionSimple(kysely, session_id, true)
      .select(() => {
        return [
          jsonObjectFrom(operatorQb(state)).as("operator"),
          jsonArrayFrom(
            targetEstablishments
              .leftJoin("spot", "spot.id", "establishment.spot_id")
              .leftJoin("region", "region.id", "spot.region_id")
              .selectAll("establishment")
              .select((establishmentEb) => {
                const bookingsWithinMonthQb = establishmentEb
                  .selectFrom("booking")
                  .where("booking.establishment_id", "=", establishmentEb.ref("establishment.id"))
                  .where("booking.cart_for_session_id", "is", null)
                  .where("booking.cancelled_at", "is", null)
                  .where((eb) => {
                    const lowerDuration = lower(eb.ref("booking.cached_duration"));
                    return eb.and([eb(lowerDuration, ">=", monthObj.firstDate), eb(lowerDuration, "<=", monthObj.lastDate)]);
                  });
                // .where("booking.cached_duration", "&&", rangeRef);
                // .where("booking.cached_duration", ">=", monthObj.firstDate)
                // .where("booking.created_at", "<=", monthObj.lastDate);

                const customersByCountryTotalsQb = establishmentEb
                  .selectFrom("customer")
                  .innerJoin("person", "person.id", "customer.person_id")
                  .innerJoin("participant", "participant.customer_id", "customer.id")
                  .innerJoin("participation", "participation.participant_id", "participant.id")
                  .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
                  .innerJoin("booking", "booking.id", "sale_item.booking_id")
                  .where("booking.cancelled_at", "is", null)
                  .where("customer.establishment_id", "=", establishmentEb.ref("establishment.id"))
                  .groupBy("participant.country_code")
                  .select((eb) => ["participant.country_code", eb.fn.count<number>(distinct(eb.ref("customer.id"))).as("count")]);

                const incombeQb = establishmentEb
                  .selectFrom("payment")
                  .innerJoin("booking", "booking.id", "payment.booking_id")
                  .where("booking.establishment_id", "=", establishmentEb.ref("establishment.id"))
                  .where("booking.cancelled_at", "is", null)
                  .groupBy("booking.currency_id")
                  .select((eb) => [eb.fn.sum<number>("payment.amount").as("amount"), "booking.currency_id"]);
                return [
                  jsonArrayFrom(
                    bookingsWithinMonthQb
                      .innerJoin("sale_item", "booking.id", "sale_item.booking_id")
                      .innerJoin("participation", "participation.sale_item_id", "sale_item.id")
                      .innerJoin("product as directProduct", "directProduct.id", "sale_item.product_id")
                      // .innerJoin('item as directItem', 'directItem.id', 'directProduct.item_id')
                      .groupBy(["booking.currency_id", "directProduct.root_id"])
                      .select((eb) => [
                        "booking.currency_id",
                        "directProduct.root_id",
                        eb.fn.count<number>("sale_item.id").as("count"),
                        eb.fn.sum<number>("sale_item.price_pp").as("revenue"),
                        jsonObjectFrom(
                          baseProductWithSelect
                            .where("product.root_id", "=", eb.ref("directProduct.root_id"))
                            .orderBy("product.created_at desc")
                            .limit(1),
                        ).as("product"),
                      ]),
                  ).as("sold_product_totals"),
                  jsonArrayFrom(
                    bookingsWithinMonthQb
                      .innerJoin("sale_item", "booking.id", "sale_item.booking_id")
                      .innerJoin("participation", "participation.sale_item_id", "sale_item.id")
                      .leftJoin("product", "product.id", "sale_item.product_id")
                      .leftJoin("item", "item.id", "product.item_id")
                      .groupBy("item.activity_slug")
                      .select((eb) => ["item.activity_slug", eb.fn.count<number>("sale_item.id").as("count")]),
                  ).as("sold_product_groups_totals"),
                  jsonArrayFrom(
                    bookingsWithinMonthQb
                      .groupBy("booking.currency_id")
                      .select((eb) => [
                        "booking.currency_id",
                        eb.fn.sum<number>("booking.cached_final_amount").as("amount"),
                        eb.fn.sum<number>("booking.cached_final_paid").as("paid"),
                      ]),
                  ).as("booking_totals"),
                  jsonArrayFrom(customersByCountryTotalsQb.where("sale_item.duration", "&&", range)).as("customer_totals"),
                  jsonArrayFrom(customersByCountryTotalsQb.where("sale_item.duration", "&&", rangePreviousMonth)).as(
                    "customer_totals_previous",
                  ),
                  jsonArrayFrom(
                    incombeQb
                      .where("payment.payed_at", ">=", monthObj.firstDatePreviousMonth)
                      .where("payment.payed_at", "<", monthObj.firstDate),
                  ).as("income_previous"),
                  jsonArrayFrom(
                    incombeQb.where("payment.payed_at", "<=", monthObj.lastDate).where("payment.payed_at", ">=", monthObj.firstDate),
                  ).as("income"),
                  jsonArrayFrom(
                    bookingsWithinMonthQb
                      .groupBy("booking.booking_source")
                      .select((eb) => ["booking.booking_source", eb.fn.count<number>("booking.id").as("count")]),
                  ).as("booking_source_totals"),
                  "spot.name as spot_name",
                ];
              }),
          ).as("establishments"),
          // eb.fn.count(eb.selectFrom('participant').innerJoin('user', 'user.id', 'participant.user_id').distinct('')).as('total'),,
        ];
      })
      .executeTakeFirst();

    if (!allowedCtx) throw unauthorized();
    if (!allowedCtx.establishments.length) throw notFoundOrUnauthorzied();

    return { ...allowedCtx, month: monthObj, filter: filters, messages: messages };
  } catch (e) {
    console.error(e);
    const errorMsg = getErrorMsgFromCatch(e);
    if (errorMsg === ("range lower bound must be less than or equal to range upper bound" satisfies ErrorKey)) {
      const translatedMsg = findErrorMessage(errorMsg);
      const redirectUrl = new URL(url);
      mergeStateToParams(redirectUrl.searchParams, {
        error_message: translatedMsg,
      });
      throw redirect(redirectUrl.toString());
    }
    throw e;
  }
};

export const ReturningCustomerBadge = (props: { participant_count: number }) => {
  const participantCount = props.participant_count;
  if (participantCount <= 1) return <Fragment />;
  return (
    <div style={{ opacity: participantCount / 10 + 0.3 }} className="p-1 rounded-md text-xs text-center bg-secondary-700">
      {participantCount === 2 ? (
        <span>
          returning
          <br /> customer
        </span>
      ) : (
        <span>
          {participantCount}x<br />
          customer
        </span>
      )}
    </div>
  );
};

const DurationFilterButtons = () => {
  const response = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const filterButtons = [
    {
      label: "Today",
      from: response.filter.today,
      to: response.filter.today,
    },
    {
      label: "Tomorrow",
      from: response.filter.tomorrow,
      to: response.filter.tomorrow,
    },
    {
      label: "Last 30 days",
      from: response.filter.today30DaysEarlier,
      to: response.filter.today,
    },
    {
      label: "Next 30 days",
      from: response.filter.today,
      to: response.filter.today30DaysLater,
    },
    {
      label: response.filter.thisYear,
      from: response.filter.startOfThisYear,
      to: response.filter.endOfThisYear,
    },
    {
      label: "All time",
      from: "",
      to: "",
    },
  ];

  return (
    <div className="flex flex-wrap gap-3">
      {filterButtons.map((filterButton) => (
        <ParamLink
          key={filterButton.label}
          path={"./"}
          paramsActive={search.state.persist_date_from === filterButton.from && search.state.persist_date_to === filterButton.to}
          paramState={{
            persist_date_from: filterButton.from,
            persist_date_to: filterButton.to,
            rerender: search.state.rerender + 1,
          }}
          className="btn btn-basic aria-current:bg-secondary-500 aria-busy:spinner spinner-dark flex-1 whitespace-nowrap"
        >
          {filterButton.label}
        </ParamLink>
      ))}
    </div>
  );
};

const TrendIndicator = (props: { current: number; previous: number }) => {
  const percentage = ((props.current - props.previous) / props.previous) * 100;
  return (
    <p className={twMerge("flex text-sm items-center gap-2", percentage < 0 ? "text-red-500" : "text-green-700")}>
      {percentage < 0 ? "-" : "+"}
      {Math.abs(percentage).toFixed(1).replace(/\.0$/, "")}%
      {percentage < 0 ? (
        <span>
          <ArrowTrendingDownIcon className={"w-6 h-6"} />
        </span>
      ) : (
        <span>
          <ArrowTrendingUpIcon className={"w-6 h-6"} />
        </span>
      )}
    </p>
  );
};

const yAxisWidth = 70;

const PrevMonthButton = (props: { direction: -1 | 1 }) => {
  const ctx = useAppContext();

  return (
    <ParamLink paramState={{ persist_month: props.direction < 0 ? ctx.month.previousMonth : ctx.month.nextMonth }}>
      <ChevronLeft className={twMerge("w-5 h-5", props.direction > 0 && "rotate-180")} />
    </ParamLink>
  );
};

export default function Page() {
  const ctx = useAppContext();
  const response = useLoaderData<typeof loader>();
  const search = useSearchParams2();

  const productsByRevenue = useBoolean(false);

  return (
    <div className="app-container space-y-3">
      <div className="space-y-3">
        <div className="flex flex-row justify-between gap-3">
          <h1 className="text-xl text-secondary font-bold">Metrics</h1>
        </div>
        <div className="flex flex-row gap-1 items-center">
          <ParamLink
            path={"./"}
            className="btn btn-text hover:bg-slate-50 text-secondary transition-colors rounded-full aria-disabled:animate-pulse aria-disabled:opacity-70"
            paramState={{ persist_month: response.month.previousMonth }}
          >
            <ChevronLeft className={"w-6 h-6"} />
          </ParamLink>
          <input
            type={"month"}
            key={ctx.date.monthParam}
            defaultValue={ctx.date.monthParam}
            className="input"
            onChange={(e) => {
              search.setState({ persist_month: e.target.value as any });
            }}
          />
          <ParamLink
            path={"./"}
            className="btn btn-text hover:bg-slate-50 text-secondary transition-colors rounded-full aria-disabled:animate-pulse aria-disabled:opacity-70"
            paramState={{ persist_month: response.month.nextMonth }}
          >
            <ChevronLeft className={"w-6 h-6 rotate-180"} />
          </ParamLink>
        </div>
        {response.messages.map((message) => (
          <Alert key={message} status={"error"}>
            {message}
          </Alert>
        ))}
        {!!search.state.error_message && <p className="p-2 rounded-md bg-red-500 text-white">{search.state.error_message}</p>}
        <SearchForm>
          <div className="space-y-3 hidden">
            <input type={"hidden"} name={"rerender" satisfies StateInputKey} value={search.state.rerender + 1} />
            <DurationFilterButtons />
            <div className="flex flex-row gap-3" key={search.state.rerender}>
              <input
                name={"persist_date_from" satisfies StateInputKey}
                type={"date"}
                placeholder="Date from"
                className="input"
                onChange={(e) => {
                  const dateFrom = e.target.value;
                  const dateToEl = e.target.form?.querySelector<HTMLInputElement>(
                    `input[name="${"persist_date_to" satisfies StateInputKey}"]`,
                  );
                  const dateTo = dateToEl?.value;

                  if (dateToEl) {
                    dateToEl.setCustomValidity("");
                  }
                  try {
                    if (dateFrom && dateTo && new Date(dateFrom) > new Date(dateTo)) {
                      e.target.setCustomValidity("Start date cannot be after end date");
                    } else {
                      e.target.setCustomValidity("");
                    }
                  } catch (e) {
                    console.log("could not validate because invalid date", e);
                  }
                }}
                defaultValue={search.state.persist_date_from || ""}
              />
              <input
                name={"persist_date_to" satisfies StateInputKey}
                type={"date"}
                placeholder="Date to"
                onChange={(e) => {
                  const dateTo = e.target.value;
                  const dateFromEl = e.target.form?.querySelector<HTMLInputElement>(
                    `input[name="${"persist_date_from" satisfies StateInputKey}"]`,
                  );
                  const dateFrom = dateFromEl?.value;

                  if (dateFromEl) {
                    dateFromEl.setCustomValidity("");
                  }

                  if (dateFrom && dateTo && new Date(dateFrom) > new Date(dateTo)) {
                    e.target.setCustomValidity("End date cannot be before start date");
                  } else {
                    e.target.setCustomValidity("");
                  }
                }}
                className="input"
                defaultValue={search.state.persist_date_to || ""}
              />
            </div>
            <SearchField />
          </div>
        </SearchForm>
        <div className="space-y-5 py-5">
          {response.establishments.map((establishment) => {
            const customerTotalCount = sumBy(establishment.customer_totals, (total) => total.count || 0);
            const customerTotalCountPrevious = sumBy(establishment.customer_totals_previous, (total) => total.count || 0);

            const customerTotals = establishment.customer_totals.map((total) => {
              const country = getCountry(total.country_code || undefined);
              return {
                label: country?.country_name || "Unkown",
                color: country?.color || defaultCountryColor,
                count: total.count,
              };
            });

            const bookingSourceTotals = establishment.booking_source_totals.map((total, index) => {
              return {
                count: total.count,
                label: total.booking_source || "Unkown",
                color: (total.booking_source && colorsByPostions[index]) || unkownColor,
              };
            });
            const activitiesByProductGroup: Total[] = establishment.sold_product_groups_totals.map((total) => {
              const activity = getActivity(total.activity_slug || undefined);
              return {
                label: total.activity_slug ? activity.name : "Unkown",
                color: total.activity_slug ? activity.color : unkownColor,
                count: total.count,
              } satisfies Total;
            });
            console.log("probygorup", establishment.sold_product_groups_totals, activitiesByProductGroup);
            const soldProductsGroupedByCurrency = myGroupBy2(establishment.sold_product_totals, (item) => item.currency_id);

            return (
              <div key={establishment.id} className="space-y-6">
                {response.establishments.length > 1 && <EstablishmentSeperator establishment={establishment} />}
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="p-3 bg-slate-100 rounded-md border border-slate-200">
                    <p className="font-bold">Incoming</p>
                    {establishment.income.map((income) => {
                      const currency = income.currency_id;
                      const incomePrevious = establishment.income_previous?.find((income) => (income.currency_id = currency))?.amount || 0;
                      const incomeCurrent = income.amount || 0;
                      return (
                        <div className="flex items-center gap-3" key={currency}>
                          <p className="text-xl font-bold">{formatPrice(incomeCurrent, currency, establishment.locale || defaultLocale)}</p>
                          <TrendIndicator previous={incomePrevious} current={incomeCurrent} />
                        </div>
                      );
                    })}
                  </div>
                  <div className="p-3 bg-slate-100 rounded-md border border-slate-200">
                    <p className="font-bold">Outstanding</p>
                    {establishment.booking_totals.map((total) => (
                      <p className="text-xl font-bold" key={total.currency_id}>
                        {formatPrice((total.amount || 0) - (total.paid || 0), total.currency_id, establishment.locale || defaultLocale)}
                      </p>
                    ))}
                  </div>
                  <div className="p-3 bg-slate-100 rounded-md border border-slate-200">
                    <p className="font-bold">Customers</p>
                    <div className="flex items-center gap-3">
                      <p className="text-xl font-bold">{customerTotalCount}</p>
                      <TrendIndicator previous={customerTotalCountPrevious} current={customerTotalCount} />
                    </div>
                  </div>
                </div>
                <div className="p-3 pr-6 bg-slate-100 rounded-md border border-slate-200 space-y-3">
                  <div className="flex-row flex gap-6 items-center">
                    <p className="font-bold">Top Activities</p>
                    <div className="border rounded-md border-secondary flex items-center">
                      <button
                        className={"disabled:bg-secondary disabled:text-white px-4 py-1 text-secondary"}
                        disabled={!productsByRevenue.isOn}
                        onClick={productsByRevenue.toggle}
                      >
                        By Sales
                      </button>
                      <button
                        className={"disabled:bg-secondary disabled:text-white px-4 py-1 text-secondary"}
                        disabled={productsByRevenue.isOn}
                        onClick={productsByRevenue.toggle}
                      >
                        By Revenue
                      </button>
                    </div>
                  </div>
                  {soldProductsGroupedByCurrency.map((group) => {
                    const soldProductTotalsTop6 = group.items
                      .sort((a, b) => {
                        if (productsByRevenue.isOn) return b.revenue - a.revenue;
                        return b.count - a.count;
                      })
                      .slice(0, 6)
                      .reverse();

                    return (
                      <div key={group.groupKey}>
                        <div className="h-80">
                          <ResponsiveContainer>
                            <BarChart data={soldProductTotalsTop6} barCategoryGap="20%">
                              <XAxis
                                type={"category"}
                                dataKey={(obj) => {
                                  return getFullProductTitle(obj.product);
                                }}
                                height={4}
                                tickLine={false}
                                tickFormatter={() => ""}
                              />
                              <Tooltip
                                content={({ active, payload }) => {
                                  const data = payload?.[0]?.payload;
                                  if (!active || !data) return null;
                                  return (
                                    <div className="border border-slate-200 bg-white rounded-md p-3 shadow-md">
                                      <p className="font-semibold mb-2">{getFullProductTitle(data.product)}</p>
                                      <div className="space-y-1 text-sm">
                                        <p aria-current={!productsByRevenue.isOn} className="text-slate-400 aria-current:text-slate-800">
                                          Total Sold: {data.count}
                                        </p>
                                        <p aria-current={productsByRevenue.isOn} className="text-slate-400 aria-current:text-slate-800">
                                          Revenue: {formatPrice(data.revenue, group.groupKey, establishment.locale || defaultLocale)}
                                        </p>
                                      </div>
                                    </div>
                                  );
                                }}
                              />
                              <Bar dataKey={productsByRevenue.isOn ? "revenue" : "count"} fill={"#8BB7C9"} barSize={20} />
                              <YAxis
                                width={yAxisWidth}
                                type={"number"}
                                dataKey={productsByRevenue.isOn ? "revenue" : "count"}
                                orientation="left"
                                {...({ angle: productsByRevenue.isOn ? -60 : 0 } as any)}
                                textAnchor="end"
                                tickFormatter={(value) =>
                                  productsByRevenue.isOn ? formatPrice(value, group.groupKey, establishment.locale) : value
                                }
                              />
                            </BarChart>
                          </ResponsiveContainer>
                        </div>
                        <div className="max-sm:hidden">
                          <div style={{ paddingLeft: `${yAxisWidth + 0}px` }} className={"flex flex-row"}>
                            {soldProductTotalsTop6.map((total) => (
                              <div key={total.root_id} className="text-xs md:text-sm text-slate-700 flex-1 text-center">
                                <div className="px-5">{total.product ? getFullProductTitle(total.product) : "Unkown"}</div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
                <div className="grid md:grid-cols-3 gap-6">
                  <PieBlock totals={customerTotals}>Customers by Country</PieBlock>
                  <PieBlock totals={bookingSourceTotals}>Booking Source</PieBlock>
                  <PieBlock totals={activitiesByProductGroup}>Activities by Category</PieBlock>
                </div>
                {false && (
                  <PieChart width={400} height={300}>
                    <Pie
                      dataKey={"count"}
                      data={establishment.sold_product_groups_totals}
                      label={(obj) => {
                        const activity = getActivity(obj.activity_slug);
                        return activity.totals_name || "Other";
                      }}
                    >
                      {establishment.sold_product_groups_totals.map((total) => {
                        const activity = getActivity(total.activity_slug || "");
                        return <Cell key={total.activity_slug} fill={activity.color} />;
                      })}
                    </Pie>
                    <Tooltip
                      content={(args) => {
                        if (!args.active) return null;
                        return (
                          <div className="border border-slate-200 bg-slate-50 rounded-md p-3">
                            {args.payload?.map((payload) => {
                              const obj = payload.payload;
                              const activity = getActivity(obj.activity_slug);
                              return (
                                <p key={payload.dataKey}>
                                  {obj.count} {activity.totals_name || "Other"}
                                </p>
                              );
                            })}
                            {/*<pre>{JSON.stringify(args.payload, null, 2)}</pre>*/}
                          </div>
                        );
                      }}
                    />
                  </PieChart>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
