import { use<PERSON><PERSON>der<PERSON><PERSON> } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import React, { Fragment } from "react";
import { ParamLink } from "~/components/meta/CustomComponents";
import { getSessionSimple } from "~/utils/session.server";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { notFoundOrUnauthorzied } from "~/misc/responses";
import { memberIsAdminQb } from "~/domain/member/member-queries.server";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { LoaderFunctionArgs } from "@remix-run/router";
import { _payment_method_mutate } from "~/misc/paths";
import { at_infinity_value } from "~/kysely/db-static-vars";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const ctx = await getSessionSimple(request);
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  const result = await simpleEstablishmentQb
    .where("establishment.id", "=", state.persist_establishment_id)
    .where("establishment.id", "in", memberIsAdminQb({ ctx: ctx, trx: kysely }, "read").select("_member.establishment_id"))
    .select((eb) => [
      jsonArrayFrom(
        eb
          .selectFrom("payment_method")
          .where("payment_method.deleted_at", "=", at_infinity_value)
          .orderBy("payment_method.name")
          .selectAll("payment_method")
          .select((eb) => [
            jsonObjectFrom(
              eb
                .selectFrom("intuit_connection")
                .where("intuit_connection.id", "=", eb.ref("payment_method.intuit_connection_id"))
                .select(["intuit_connection.id", "intuit_connection.intuit_company", "intuit_connection.intuit_user"]),
            ).as("intuit_connection"),
          ])
          .where("payment_method.establishment_id", "=", eb.ref("establishment.id")),
      ).as("payment_methods"),
    ])
    .executeTakeFirst();

  if (!result) throw notFoundOrUnauthorzied("establishment not found or you not authorized");
  return result;
};

export default function Page() {
  const establishment = useLoaderData<typeof loader>();

  return (
    <div className="app-container space-y-3">
      <div>
        <div className="flex flex-wrap items-center gap-2">
          <h1 className="text-xl font-bold">Payment Methods</h1>
          <ParamLink path={_payment_method_mutate} className="link">
            create
          </ParamLink>
        </div>
        <h2 className="text-slate-600">{getEstablishmentName(establishment)}</h2>
      </div>
      <p className="bg-secondary-50 p-3 rounded-md">
        Create and edit your custom payment methods. To make edits, click the orange hyperlink in the payment method column. Once created,
        your custom payment methods will be available for selection on the booking page.
      </p>
      {/*<p className="bg-secondary-50 p-3 rounded-md">*/}
      {/*  Manage your payment methods on this page. <br />*/}
      {/*  You can create and edit payment methods and write a default message which will be shown to your customers within*/}
      {/*  the payment*/}
      {/*  section.*/}
      {/*</p>*/}
      <div className="space-y-3 overflow-auto text-xs md:text-base">
        <div className="grid gap-x-3 gap-y-3 grid-cols-[auto_auto_auto_minmax(0,1fr)]">
          <div>Name</div>
          <div>Surcharge</div>
          <div>
            Payment <br className="inline md:hidden" />
            Gateway
          </div>
          <div>Quickbooks (Account name)</div>
          {establishment.payment_methods.map((paymentMethod) => {
            return (
              <Fragment key={paymentMethod.id}>
                <div>
                  <ParamLink className="link" path={_payment_method_mutate} paramState={{ id: paymentMethod.id }}>
                    {paymentMethod.name}
                  </ParamLink>
                </div>
                <div>
                  <span>{paymentMethod.default_surcharge_percentage}%</span>
                </div>
                <div>{paymentMethod.xendit ? <span>Xendit</span> : <span>-</span>}</div>
                <div>{paymentMethod.intuit_connection ? `QB: ${paymentMethod.intuit_connection.intuit_company.CompanyName}` : "-"}</div>
              </Fragment>
            );
          })}
        </div>
      </div>
    </div>
  );
}
