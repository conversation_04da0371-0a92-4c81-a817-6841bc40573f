import type { MetaFunction } from "@remix-run/react";
import { Link } from "@remix-run/react";
import React, { useState, useEffect } from "react";

export const meta: MetaFunction = () => {
  return [
    { title: "Mutate Operations - API Documentation" },
    { name: "description", content: "Documentation for data mutation operations in ScamGem's form-based API system" },
  ];
};

const sections = [
  { id: "overview", title: "Overview", icon: "📖" },
  { id: "architecture", title: "Architecture", icon: "🏗️" },
  { id: "how-it-works", title: "How It Works", icon: "⚙️" },
  { id: "field-naming", title: "Field Naming", icon: "🏷️" },
  { id: "field-types", title: "Field Types", icon: "🔧" },
  { id: "operations", title: "Operations", icon: "🔄" },
  { id: "authorization", title: "Authorization", icon: "🔐" },
  { id: "examples", title: "Examples", icon: "💡" },
];

export default function MutateOperationsDocumentation() {
  const [activeSection, setActiveSection] = useState("overview");

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
      setActiveSection(sectionId);
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      const sections = document.querySelectorAll("section[id]");
      let current = "";
      sections.forEach((section) => {
        const sectionTop = (section as HTMLElement).offsetTop;
        const sectionHeight = (section as HTMLElement).clientHeight;
        if (window.scrollY >= sectionTop - 200) {
          current = section.getAttribute("id") || "";
        }
      });
      setActiveSection(current);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link to="/api-documentation" className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
            Back to API Documentation
          </Link>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Mutate Operations</h1>
          <p className="text-gray-600">Form-based API for data creation, updates, and deletion</p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Navigation */}
          <div className="lg:w-64 lg:sticky lg:top-8 lg:self-start lg:h-fit lg:max-h-screen lg:overflow-y-auto">
            <h2 className="text-sm font-semibold text-gray-900 mb-3">Navigation</h2>
            <nav className="space-y-1">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => scrollToSection(section.id)}
                  className={`w-full text-left px-2 py-1.5 rounded font-medium transition-colors ${
                    activeSection === section.id ? "bg-green-100 text-green-700" : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                  }`}
                >
                  <span className="mr-1">{section.icon}</span>
                  {section.title}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 bg-white rounded-lg shadow-lg p-8">
            {/* Overview Section */}
            <section id="overview" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Overview</h2>
              <div className="prose max-w-none">
                <p className="text-gray-600 mb-4">
                  The Form-Based API is a powerful, type-safe system for data mutations (create, update, delete) that uses a single endpoint
                  with structured form data. This approach provides GraphQL-like flexibility with the simplicity of traditional form
                  submissions.
                </p>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                  <h3 className="font-semibold text-green-900 mb-2">Key Features</h3>
                  <ul className="text-green-800 space-y-1">
                    <li>• Single endpoint for all mutations</li>
                    <li>• Type-safe field naming and processing</li>
                    <li>• Batch operations support</li>
                    <li>• Centralized authorization</li>
                    <li>• Automatic reference resolution</li>
                    <li>• Rich field type transformations</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Architecture Section */}
            <section id="architecture" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Architecture</h2>
              <div className="prose max-w-none">
                <p className="text-gray-600 mb-4">
                  The system consists of several key components that work together to provide a seamless mutation experience:
                </p>
                <div className="grid md:grid-cols-2 gap-6 mb-6">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 className="font-semibold text-blue-900 mb-2">Database Schema</h3>
                    <p className="text-blue-800 text-sm">
                      TypeScript-generated database schema provides type safety and validation.
                      {/* db.ts */}
                    </p>
                  </div>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 className="font-semibold text-green-900 mb-2">Form Processing</h3>
                    <p className="text-green-800 text-sm">
                      Form data processor converts form data to structured JSON.
                      {/* formdata-to-nested-json.ts */}
                    </p>
                  </div>
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h3 className="font-semibold text-purple-900 mb-2">Resource Authorization</h3>
                    <p className="text-purple-800 text-sm">
                      Resource server handles authorization and business logic.
                      {/* resource.server.ts */}
                    </p>
                  </div>
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <h3 className="font-semibold text-orange-900 mb-2">API Endpoint</h3>
                    <p className="text-orange-800 text-sm">
                      Central endpoint processes all mutation requests.
                      {/* _all._catch.resource.tsx */}
                    </p>
                  </div>
                </div>
              </div>
            </section>

            {/* How It Works Section */}
            <section id="how-it-works" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">How It Works</h2>
              <div className="prose max-w-none">
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-6 font-mono text-sm">
                  <div>1. Form data is submitted to /resource endpoint</div>
                  <div>2. Form data is parsed into nested JSON structure</div>
                  <div>3. References are resolved (ref-table-index → actual IDs)</div>
                  <div>4. Field types are processed and transformed</div>
                  <div>5. Authorization is checked for each operation</div>
                  <div>6. Database operations are executed</div>
                  <div>7. Response is returned with success/error information</div>
                </div>
                <p className="text-gray-600 mb-4">
                  The system processes form data in a specific order to handle dependencies correctly. References are resolved after the
                  initial parsing, and field type transformations are applied last.
                </p>
              </div>
            </section>

            {/* Field Naming Section */}
            <section id="field-naming" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Field Naming Convention</h2>
              <div className="prose max-w-none">
                <p className="text-gray-600 mb-4">Fields follow a structured naming convention that determines how they are processed:</p>
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-6 font-mono text-sm">
                  <div>table.index.operation</div>
                  <div>table.index.field_path</div>
                  <div>table.index.field_path.__type__</div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Components</h3>
                <ul className="list-disc list-inside text-gray-600 mb-4">
                  <li>
                    <code>table</code>: Database table name (e.g., participant, booking, establishment)
                  </li>
                  <li>
                    <code>index</code>: Unique identifier for this operation (e.g., 0, 1, 2)
                  </li>
                  <li>
                    <code>operation</code>: Operation type (insert, update, delete)
                  </li>
                  <li>
                    <code>field_path</code>: Nested field path (e.g., data.first_name, data.address.city)
                  </li>
                  <li>
                    <code>__type__</code>: Special field type for transformations
                  </li>
                </ul>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Examples</h3>
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                  <div>participant.0.operation = "insert"</div>
                  <div>participant.0.data.first_name = "John"</div>
                  <div>participant.0.data.last_name = "Doe"</div>
                  <div>participant.0.data.email = "<EMAIL>"</div>
                  <div>participant.0.data.active.__boolean__ = "true"</div>
                </div>
              </div>
            </section>

            {/* Field Types Section */}
            <section id="field-types" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Field Types</h2>
              <div className="prose max-w-none">
                <p className="text-gray-600 mb-4">Special field types provide automatic transformations and validations:</p>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Example</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr>
                        <td className="px-6 py-4 text-sm font-mono text-gray-900">__boolean__</td>
                        <td className="px-6 py-4 text-sm text-gray-600">Converts string to boolean</td>
                        <td className="px-6 py-4 text-sm font-mono text-gray-600">"true" → true</td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 text-sm font-mono text-gray-900">__pg_int_range__</td>
                        <td className="px-6 py-4 text-sm text-gray-600">PostgreSQL integer range</td>
                        <td className="px-6 py-4 text-sm font-mono text-gray-600">{"{from: 1, to: 10}"} → [1,11)</td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 text-sm font-mono text-gray-900">__pg_coordinates__</td>
                        <td className="px-6 py-4 text-sm text-gray-600">PostGIS Point coordinates</td>
                        <td className="px-6 py-4 text-sm font-mono text-gray-600">"lat,lng" → Point</td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 text-sm font-mono text-gray-900">__pg_daterange__</td>
                        <td className="px-6 py-4 text-sm text-gray-600">PostgreSQL date range</td>
                        <td className="px-6 py-4 text-sm font-mono text-gray-600">{"{from: '2024-01-01', to: '2024-01-31}"} → daterange</td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 text-sm font-mono text-gray-900">__sum__</td>
                        <td className="px-6 py-4 text-sm text-gray-600">Sums array of numbers</td>
                        <td className="px-6 py-4 text-sm font-mono text-gray-600">[1,2,3] → 6</td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 text-sm font-mono text-gray-900">__join__</td>
                        <td className="px-6 py-4 text-sm text-gray-600">Joins array of strings</td>
                        <td className="px-6 py-4 text-sm font-mono text-gray-600">["a","b","c"] → "abc"</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </section>

            {/* Operations Section */}
            <section id="operations" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Operations</h2>
              <div className="prose max-w-none">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Supported Operations</h3>
                <div className="grid md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h4 className="font-semibold text-green-900 mb-2">Insert</h4>
                    <p className="text-green-800 text-sm">Create new records</p>
                    <code className="bg-green-100 px-1 rounded text-xs">table.0.operation = "insert"</code>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="font-semibold text-blue-900 mb-2">Update</h4>
                    <p className="text-blue-800 text-sm">Modify existing records</p>
                    <code className="bg-blue-100 px-1 rounded text-xs">table.0.operation = "update"</code>
                  </div>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h4 className="font-semibold text-red-900 mb-2">Delete</h4>
                    <p className="text-red-800 text-sm">Remove records</p>
                    <code className="bg-red-100 px-1 rounded text-xs">table.0.operation = "delete"</code>
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Batch Operations</h3>
                <p className="text-gray-600 mb-4">Multiple operations can be combined in a single request by using different indices:</p>
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                  <div>participant.0.operation = "insert"</div>
                  <div>participant.0.data.first_name = "John"</div>
                  <div>participant.1.operation = "insert"</div>
                  <div>participant.1.data.first_name = "Jane"</div>
                  <div>booking.0.operation = "update"</div>
                  <div>booking.0.id = "booking-123"</div>
                  <div>booking.0.data.status = "confirmed"</div>
                </div>
              </div>
            </section>

            {/* Authorization Section */}
            <section id="authorization" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Authorization</h2>
              <div className="prose max-w-none">
                <p className="text-gray-600 mb-4">
                  Authorization is handled centrally through the resource server. Each operation is checked against the user's permissions:
                </p>
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-4 font-mono text-sm">
                  <div>// Check if user can perform operation on table</div>
                  <div>const canInsert = await canInsertQb(args, "participant");</div>
                  <div>const canUpdate = await canUpdateQb(args, "booking", bookingId);</div>
                  <div>const canDelete = await canDeleteQb(args, "establishment", establishmentId);</div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Permission Levels</h3>
                <ul className="list-disc list-inside text-gray-600 mb-4">
                  <li>
                    <strong>Owner</strong>: Full access to establishment data
                  </li>
                  <li>
                    <strong>Admin</strong>: Administrative access with restrictions
                  </li>
                  <li>
                    <strong>Member</strong>: Limited access to assigned data
                  </li>
                  <li>
                    <strong>Customer</strong>: Access to own bookings and data
                  </li>
                </ul>
              </div>
            </section>

            {/* Examples Section */}
            <section id="examples" className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Examples</h2>
              <div className="prose max-w-none">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Creating a New Participant</h3>
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-6 font-mono text-sm">
                  <div>{'<form method="post" action="/resource">'}</div>
                  <div> {'<input type="hidden" name="participant.0.operation" value="insert" />'}</div>
                  <div> {'<input type="text" name="participant.0.data.first_name" required />'}</div>
                  <div> {'<input type="text" name="participant.0.data.last_name" required />'}</div>
                  <div> {'<input type="email" name="participant.0.data.email" required />'}</div>
                  <div> {'<input type="hidden" name="redirect" value="/success" />'}</div>
                  <div> {'<input type="hidden" name="redirect_error" value="/error" />'}</div>
                  <div> {'<button type="submit">Create Participant</button>'}</div>
                  <div>{"</form>"}</div>
                </div>

                <h3 className="text-xl font-semibold text-gray-900 mb-3">Complex Nested Operations</h3>
                <div className="bg-gray-900 text-green-400 rounded-lg p-4 mb-6 font-mono text-sm">
                  <div>{'<form method="post" action="/resource">'}</div>
                  <div> {"<!-- Create participant -->"}</div>
                  <div> {'<input type="hidden" name="participant.0.operation" value="insert" />'}</div>
                  <div> {'<input type="text" name="participant.0.data.first_name" required />'}</div>
                  <div> {'<input type="text" name="participant.0.data.last_name" required />'}</div>
                  <div> </div>
                  <div> {"<!-- Create waiver for participant -->"}</div>
                  <div> {'<input type="hidden" name="participant_waiver.0.operation" value="insert" />'}</div>
                  <div> {'<input type="hidden" name="participant_waiver.0.data.participant_id" value="ref-participant-0" />'}</div>
                  <div> {'<input type="hidden" name="participant_waiver.0.data.waiver_id" value="waiver-uuid" />'}</div>
                  <div> {'<button type="submit">Submit</button>'}</div>
                  <div>{"</form>"}</div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}
