import { useLoaderData } from "@remix-run/react";
import React, { Fragment } from "react";
import { activities, getActivity } from "~/domain/activity/activity";
import { getTripTotals } from "~/domain/trip/trip-components";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { usePageRefresh } from "~/hooks/use-page-refresh";
import { Calendar, CalendarItems, CalendarLegend } from "~/domain/calendar/Calendar";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _member_detail, _planning, _planning_u } from "~/misc/paths";
import { twMerge } from "tailwind-merge";
import { format } from "date-fns";
import { useAppContext } from "~/hooks/use-app-context";
import { getEstablishmentShort } from "~/domain/establishment/helpers";
import { monthLoader } from "~/server/loaders/month-loader";
import type { LoaderFunctionArgs } from "@remix-run/router";
import { getDateFromParams } from "~/domain/planning/planning.helpers.server";

export { action } from "~/routes/_all._catch.resource";

export const loader = (args: LoaderFunctionArgs) => {
  const date = getDateFromParams(args.request);

  return monthLoader(args, date.dateParam);
};

export default function Page() {
  const ctx = useAppContext();
  const search = useSearchParams2();
  const data = useLoaderData<typeof loader>();
  // console.log("month result", data);
  usePageRefresh(20, data);
  const navigatingDateStr = search.pendingState?.persist_date;
  return (
    <div className="min-w-[310px] space-y-3 @container">
      {ctx.members.length > 0 && (
        <div className="px-3 leading-8">
          {!search.state.persist_operator_id &&
            ctx.members.map((member) => (
              <Fragment key={member.id}>
                <ParamLink path={_member_detail(member.id)} className="link">
                  Availability calendar for {getEstablishmentShort(member.establishment)}
                </ParamLink>
                <br />
              </Fragment>
            ))}
        </div>
      )}
      <Calendar>
        <CalendarItems
          item={(args) => {
            const isLoading = navigatingDateStr && navigatingDateStr === args.dateStr;
            const day = data.days.find((day) => (day.day as any) === args.dateStr);

            const trips = (day?.trips || []).filter(
              (trip) => !search.state.persist_toggle_establishment_ids.includes(trip.establishment_id),
            );
            const unscheduledParticipations = (day?.unscheduled_participants || []).filter(
              (booking) => !search.state.persist_toggle_establishment_ids.includes(booking.establishment_id),
            );
            // const unscheduledBookings = [];
            return (
              <div className="items-center justify-center  border-l border-b border-slate-200" key={args.dateStr}>
                <ParamLink
                  preventScrollReset
                  // prefetch="intent"
                  path={search.state.persist_operator_id ? _planning : _planning_u}
                  paramState={{ persist_date: args.dateStr, toggle_trip_templates_panel: undefined }}
                  className={twMerge(
                    " block min-h-[100px] space-y-1 py-1",
                    isLoading && "spinner spinner-dark",
                    // dateValue === selectedDateValue ? "bg-primary font-bold text-white" : "hover:bg-slate-100",
                    // dateValue === todayDateValue && "border-2 border-secondary",
                    !args.isMonth && "opacity-50",
                  )}
                >
                  <div className="text-center">
                    <span
                      className={twMerge(
                        "inline-block rounded-full border border-transparent p-[2px] px-3 text-xs",
                        args.isToday && "border-secondary-500",
                        args.isSelected && "text-primary",
                      )}
                    >
                      {format(args.date, "d")}
                    </span>
                  </div>
                  <div className="w-full space-y-1">
                    {trips.map((trip) => {
                      const tripTotals = getTripTotals(trip);

                      const totals = trip.assignment_totals.map((total) => {
                        const activity = getActivity(total.activity_slug || "");
                        return {
                          ...total,
                          activity: activity,
                        };
                      });

                      return (
                        <div
                          key={trip.id}
                          className={`flex w-full flex-row items-center gap-[2px] overflow-hidden 
                               border-r-2 border-white bg-secondary px-1 text-xs text-white`}
                          style={{
                            maxHeight: "20px",
                            backgroundColor: totals[0]?.activity?.color || activities.other.color,
                          }}
                        >
                          <span className="flex-1 line-clamp-1">
                            {trip.boat?.calendar_display_boat_name ? trip.boat.name : trip.activity_location}
                          </span>
                          {totals.slice(1).map((total) => (
                            <span
                              key={total.activity_slug}
                              style={{ backgroundColor: total.activity.color }}
                              className="h-2 w-2 rounded-full border border-white max-sm:hidden"
                            />
                          ))}
                          <span>{tripTotals.available < 1 ? "F" : tripTotals.available}</span>
                          {/*<span>{JSON.stringify(trip.assignment_totals)}</span>*/}
                        </div>
                      );
                    })}
                    {unscheduledParticipations.length > 0 && (
                      <div
                        style={{
                          whiteSpace: "nowrap",
                          textOverflow: "ellipsis",
                          width: "calc(99%)",
                          overflow: "hidden",
                        }}
                        className="text-primary text-xs px-1 whitespace-nowrap overflow-ellipsis"
                      >
                        {unscheduledParticipations.length} Unscheduled
                      </div>
                    )}
                  </div>
                </ParamLink>
              </div>
            );
          }}
        />
      </Calendar>
      <CalendarLegend />
    </div>
  );
}
