import React from "react";
import { images } from "~/data/images";
import { createPageOverwrites, traveltrusterName } from "~/misc/consts";
import { ikUrl } from "~/components/IkImage";
import type { LinksFunction } from "@remix-run/server-runtime";

interface Promise {
  title: string;
  description: string;
  image: keyof typeof images;
}

const heroImage = {
  path: "/images/happy-snorkelers-standing-in-water-cropped.jpeg",
  ikQuery: "tr:q-80,h-500,w-1980,c-at_least",
};
const heroUrl = ikUrl(heroImage.path, heroImage.ikQuery);

export const links: LinksFunction = () => [
  {
    as: "image",
    href: heroUrl,
    rel: "preload",
  },
];

const promises: Promise[] = [
  {
    title: "Best price guarantee",
    image: "diving-group-on-boat-with-laughing-girl-on-foreground.jpeg",
    description: `The prices displayed are directly from our partner operators. 
We promise there are no added fees whatsoever for the use of our service.`,
  },
  {
    title: "Up to date information",
    image: "girl-with-laptop.jpeg",
    description: `Information guaranteed for all divers. We promise you that what’s on the platform are regularly updated with the latest information from partner operators.`,
  },
  {
    title: "Enjoyable underwater adventure",
    image: "snorkeler.jpeg",
    description: `Explore the beautiful ocean with credible and trusted operators.
We promise more time underwater is well spent.`,
  },
  {
    title: "Direct contact with operators",
    image: "app-with-sea-background.png",
    description: `Freely, easily and confidently contact the operators with the information you see from our platform. 
We promise you this is free of obligation.`,
  },
];

export { action } from "~/routes/_all._catch.resource";

export const loader = () => createPageOverwrites({ show_whatsapp: true });

export default function Page() {
  return (
    <div>
      <div className="bg-cover bg-center object-fill" style={{ backgroundImage: `url(${heroUrl})` }}>
        <div className="flex h-80 w-full content-center items-center bg-black/30">
          <div className="app-container h-auto space-y-2">
            <h1 className="text-2xl font-semibold tracking-wide text-white drop-shadow-md md:text-5xl">Out of a passion for diving</h1>
            <p className="text-sm tracking-wider text-white drop-shadow-md md:text-xl">Trusted information for your peace of mind</p>
          </div>
        </div>
      </div>
      <section className="app-container">
        <div className="space-y-6 py-10">
          <h2 className="text-xl font-bold uppercase">What is {traveltrusterName}</h2>
          <p>
            {traveltrusterName} is your handy diving platform that you can rely on to give you trusted information for your next diving
            adventure (in Bali).
          </p>
          <p>
            Diving for the first time? Wanna learn how to dive? Or just wanting to snorkel? {traveltrusterName} provides you all the needed
            information for these activities with just a few taps or clicks away without spending a lot of time researching.
          </p>
        </div>
      </section>
      <div className="bg-secondary-50">
        <section className="app-container space-y-6 py-10">
          <h2 className="text-xl font-bold uppercase">What we offer</h2>
          <p>
            We’re here to give you more time underwater rather than behind a computer, laptop or phone to plan and research your underwater
            activity.
            <br />
            Whether it’s information on where to dive, what marine life you will find, dive site info and required experience, what is
            included, how much should you pay, and more..
          </p>
          <p>We’ve mapped this information for you straight from our trusted and credible partner operators.</p>
        </section>
      </div>
      <section className="app-container space-y-6 py-10">
        <h2 className="text-xl font-semibold uppercase">What we promise</h2>
        <ul className="grid grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-4">
          {promises.map((promise) => (
            <li key={promise.title}>
              <img
                className="h-[200px] w-full object-cover"
                src={ikUrl(`images/${promise.image}`, `tr:w-620`)}
                alt={images[promise.image].alt}
              />
              <h3 className="my-3 font-bold">{promise.title}</h3>
              <p className="whitespace-pre-wrap text-slate-800">{promise.description}</p>
            </li>
          ))}
        </ul>
      </section>
    </div>
  );
}
