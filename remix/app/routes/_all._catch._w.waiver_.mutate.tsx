import { kysely } from "~/misc/database.server";
import { LoaderFunctionArgs } from "@remix-run/router";
import { useLoaderData } from "@remix-run/react";
import { fName, tableIdRef } from "~/misc/helpers";
import { RInput, RLabel, RSelect, RTextarea } from "~/components/ResourceInputs";
import { ActionForm } from "~/components/form/BaseFrom";
import { SubmitButton } from "~/components/base/Button";
import React, { Fragment, ReactNode, useId, useState } from "react";
import { InputFilesDefault } from "~/components/form/FilesInput";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { fileTargetsBaseQb, fileTargetsQb, FileTargetValue } from "~/domain/file/file-resource";
import { renderStr } from "~/libs/markdoc/markdoc";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { notFound } from "~/misc/responses";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { languages } from "~/data/languages";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _waiver } from "~/misc/paths";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { ActionAlert } from "~/components/ActionAlert";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { divingOrganizationszz } from "~/domain/diving-course/diving-courses.data";
import { AnimatingDiv } from "~/components/base/base";
import { MarkdocComp } from "~/domain/waiver/waiver-components";
import { letters, questions } from "~/domain/medical/medical";
import { DividerWithText } from "~/components/Divider";
import { Tooltip } from "~/components/base/tooltip";
import { InformationCircleIcon } from "@heroicons/react/20/solid";
import { baseMarkdocComps } from "~/domain/waiver/waiver-markdoc";
import { getWaiverType, WaiverType, waiverTypes } from "~/domain/waiver/waiver-vars";

import { ValidityDurationField } from "~/components/duration";

export { action } from "~/routes/_all._catch.resource";

const endsWithOpeningTag = (text: string) => text.lastIndexOf("{%") > text.lastIndexOf("%}");

export const loader = async (args: LoaderFunctionArgs) => {
  const url = new URL(args.request.url);
  const record = paramsToRecord(url.searchParams);

  const establishmentId = record.establishment_id || record.persist_establishment_id;

  const filesQb = fileTargetsBaseQb(kysely)
    .where("file_target.target", "=", "establishment_waiver" satisfies FileTargetValue)
    .where((eb) => {
      if (record.id) {
        const establishmentIdFromWaiver = eb.selectFrom("waiver").select("waiver.establishment_id").where("waiver.id", "=", record.id);
        return eb.or([
          eb.and([eb("file_target.target_id", "is not", null), eb("file_target.target_id", "=", establishmentIdFromWaiver)]),
          eb.and([eb("file_target.target_id", "is", null), eb(establishmentIdFromWaiver, "is", null)]),
        ]);
      }
      return eb("file_target.target_id", establishmentId ? "=" : "is", establishmentId);
    });

  const result = await kysely
    .selectNoFrom([
      jsonObjectFrom(simpleEstablishmentQb.where("establishment.id", "=", establishmentId)).as("establishment"),
      jsonObjectFrom(
        kysely
          .selectFrom("waiver")
          .selectAll("waiver")
          .select((eb) => [
            jsonArrayFrom(fileTargetsQb(kysely, "establishment_waiver", eb.ref("waiver.establishment_id"))).as("files"),
            jsonObjectFrom(
              eb
                .selectFrom("waiver_translation")
                .selectAll("waiver_translation")
                .where("waiver_translation.waiver_id", "=", eb.ref("waiver.id"))
                .where("waiver_translation.language_code", "=", record.language_code),
            ).as("translation"),
          ])
          .where("waiver.id", "=", record.id),
      ).as("waiver"),
      jsonArrayFrom(filesQb).as("files"),
    ])
    .executeTakeFirstOrThrow();

  if (record.id && !result.waiver) throw notFound();

  // const ast = parseAndTransformContent(result.waiver?.translation?.markdoc || "", {
  //   participant: "{participant}",
  //   operator: "{operator}",
  // });

  return {
    // ast: ast,
    // ...createPageOverwrites({ print_friendly: true }),
    ...result,
  };
};

const wrapFunc =
  (prefixText: string, suffixText: string = "") =>
  (e: React.SyntheticEvent) => {
    e.preventDefault();
    const textarea = document.getElementById("markdoc-field") as HTMLTextAreaElement;
    const selectionStart = textarea.selectionStart;
    const selectionEnd = textarea.selectionEnd;

    const startText = textarea.value.slice(0, selectionStart);
    const selectedText = textarea.value.slice(selectionStart, selectionEnd);
    const endText = textarea.value.slice(selectionEnd);

    textarea.focus();
    const newText = prefixText + selectedText + suffixText;
    if (document.queryCommandSupported("insertText")) {
      document.execCommand("insertText", false, newText);
    } else {
      textarea.value = startText + newText + endText;
    }
    textarea.setSelectionRange(selectionStart + prefixText.length, selectionEnd + prefixText.length);
  };

const varFunc = (e: React.SyntheticEvent, variableName: string) => {
  const textarea = document.getElementById("markdoc-field") as HTMLTextAreaElement;
  const selectionStart = textarea.selectionStart;
  const startText = textarea.value.slice(0, selectionStart);
  const prefixText = endsWithOpeningTag(startText) ? `$${variableName}` : `{% $${variableName} %}`;
  wrapFunc(prefixText)(e);
};

interface Command {
  func: (e: React.SyntheticEvent) => void;
  name: string;
  icon: unknown;
  info: ReactNode;
}

const commands = [
  {
    func: wrapFunc("**", "**"),
    name: "bold",
    icon: <strong>B</strong>,
    info: null,
  },
  {
    func: wrapFunc("*", "*"),
    name: "italic",
    icon: <span className="italic font-bold">I</span>,
    info: null,
  },
  {
    func: wrapFunc("{% u %}", "{% /u %}"),
    name: "U",
    icon: <span className="underline">U</span>,
    info: null,
  },
  {
    func: wrapFunc("# "),
    name: "h1",
    icon: <span className="">H1</span>,
    info: null,
  },
  {
    func: wrapFunc("## "),
    name: "h2",
    icon: <span className="">H2</span>,
    info: null,
  },
  {
    func: wrapFunc("{% input value=", " /%}"),
    // func: (e) => {},
    name: "Input",
    icon: <span className="">Input</span>,
    info: (
      <div className="space-y-3">
        <div className="space-y-1">
          <p>Optional attributes:</p>
          <div className="grid grid-cols-[auto_auto] gap-y-0.5 gap-x-3 pl-1">
            <div>
              <strong>name</strong>:
            </div>
            <div>the key under which the entered text gets saved, if you have multiple inputs, use different names</div>
            <div>type</div>
            <div>the type of input, can be text, radio, checkbox. text by default</div>
            <div>
              <strong>placeholder</strong>:
            </div>
            <div>the text shown when the input is empty</div>
            <div>
              <strong>required</strong>:
            </div>
            <div>use if the input is required</div>
            <div>
              <strong>defaultValue</strong>:
            </div>
            <div>the value the input is filled with by default</div>
            <div>
              <strong>value</strong>:
            </div>
            <div>the value the input is filled with. Value cannot be changed</div>
          </div>
        </div>
        <div className="space-y-1">
          <p>Examples:</p>
          <p className="pl-1">
            {'{% input name="dm_name" placeholder="Enter the name of your divemaster or instructor" required=true /%}'}
          </p>
          <p className="pl-1">{"{% input value=$participant /%}"}</p>
        </div>
      </div>
    ),
  },
  {
    func: wrapFunc(`{% div className="" %}\n`, "\n{% /div %}"),
    name: "Block",
    icon: <span className="">Block</span>,
    info: null,
  },
  {
    func: wrapFunc("{% br /%}", ""),
    name: "Break",
    icon: <span className="">Break</span>,
    info: null,
  },
  // {
  //   func: wrapFunc(`{% signature /%}`),
  //   name: "Signature",
  //   icon: <span className="">Signature</span>,
  // },
] satisfies Command[];

const varCommands = ["participant", "operator"];

export default function WaiverPage() {
  const data = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const backdropId = useId();
  const establishmentTitle = data.establishment && getEstablishmentName(data.establishment);
  // const backdropRef = useRef()

  const waiver = data.waiver;
  const waiverTranslation = waiver?.translation;
  const [waiverTypeKey, setWaiverTypeKey] = useState(waiver?.type);
  const waiverType = getWaiverType(waiverTypeKey || ("signature" satisfies WaiverType));

  const [content, setContent] = useState(waiverTranslation?.markdoc || "");

  const highlightedText = renderStr(content.replace(/\n$/g, "\n\n"));

  const actionType = search.state.action_type;
  // const waiverReadonly = !!waiver && !search.state.language_code;
  const isMedicalWaiver = waiverType.key === "medical";
  return (
    <div className="space-y-3 app-container">
      <p className="print:hidden">{establishmentTitle || "Default waiver"}</p>
      <div className="flex flex-wrap gap-3 items-center print:hidden">
        <h1 className="text-xl text-slate-500">
          {waiver ? (actionType === "copy" ? "Copy" : actionType === "view" ? "View" : "Edit") : "Create"} waiver
        </h1>
      </div>
      <ActionForm className="space-y-3">
        <div className="space-y-3 print:hidden">
          <ActionAlert />
          <RedirectParamsInput path={_waiver} paramState={{}} />
          <InputFilesDefault
            target={"establishment_waiver"}
            target_id={waiver?.establishment_id || search.state.establishment_id || ""}
            defaultValue={data.files}
            readonly={search.state.preview}
            onInsert={(e, path) => {
              wrapFunc("![", `](/file/${path})`)(e);
              console.log("path", path);
            }}
          />
          {actionType !== "copy" && (
            <Fragment>
              {waiver && <RInput table={"waiver"} field={"id"} value={waiver.id} />}
              {waiverTranslation && <RInput table={"waiver_translation"} field={"id"} value={waiverTranslation.id} />}
            </Fragment>
          )}
          <div className="flex flex-wrap gap-3">
            <div>
              <RInput
                label={"Identifier"}
                table={"waiver"}
                field={"data.slug"}
                required
                disabled={search.state.preview}
                defaultValue={waiver?.slug || ""}
                className="input"
              />
            </div>
            {!isMedicalWaiver && (
              <div>
                <RLabel table={"waiver"} field={"data.type"}>
                  Type
                </RLabel>
                <br />
                <RSelect
                  table={"waiver"}
                  field={"data.type"}
                  required
                  value={waiverType.key}
                  onChange={(e) => setWaiverTypeKey(e.target.value as WaiverType)}
                  className="select"
                >
                  {Object.entries(waiverTypes)
                    .filter(([key]) => key !== ("medical" satisfies WaiverType))
                    .map(([key, waiverType]) => {
                      return (
                        <option key={key} value={key}>
                          {key}
                        </option>
                      );
                    })}
                </RSelect>
              </div>
            )}
            {!search.state.establishment_id && (
              <div>
                <RLabel table={"waiver"} field={"data.diving_certificate_organization_key"}>
                  Diving organization
                </RLabel>
                <br />
                <RSelect
                  table={"waiver"}
                  field={"data.diving_certificate_organization_key"}
                  defaultValue={waiver?.diving_certificate_organization_key || ""}
                  className="select"
                  disabled={search.state.preview}
                >
                  <option value="">n/a</option>
                  {Object.entries(divingOrganizationszz).map(([key, org]) => (
                    <option key={key} value={key}>
                      {org.name}
                    </option>
                  ))}
                </RSelect>
              </div>
            )}
            <div>
              <RLabel table={"waiver"} field={"data.validity_duration"}>
                Valid term
              </RLabel>
              <ValidityDurationField name={fName("waiver", "data.validity_duration")} defaultValue={waiver?.validity_duration} />
            </div>
            {!!search.state.establishment_id && (
              <RInput table={"waiver"} field={"data.establishment_id"} type={"hidden"} defaultValue={search.state.establishment_id} />
            )}
          </div>
          <div>
            <RLabel table={"waiver"} field={"data.description"}>
              Instruction Tooltip
            </RLabel>
            <br />
            <RTextarea className="input text-sm" table={"waiver"} field={"data.description"} defaultValue={waiver?.description || ""} />
          </div>
        </div>
        {waiverType.key !== "upload" && (
          <Fragment>
            <RInput table={"waiver_translation"} field={"data.waiver_id"} value={tableIdRef("waiver")} type={"hidden"} />
            <div className="py-3">
              <DividerWithText>Translation</DividerWithText>
            </div>
            <div className="flex flex-wrap gap-3">
              <div>
                <RLabel table={"waiver_translation"} field={"data.language_code"} className="required">
                  Language
                </RLabel>
                <br />
                <RSelect
                  table={"waiver_translation"}
                  field={"data.language_code"}
                  defaultValue={waiverTranslation?.language_code || search.state.language_code || "EN"}
                  disabled={search.state.preview}
                  required
                  className="select"
                >
                  <option value="">Select a language</option>
                  {languages.map((language) => (
                    <option key={language.code} value={language.code}>
                      {language.name}
                    </option>
                  ))}
                </RSelect>
              </div>
              <div>
                <RInput
                  label={"Display name"}
                  table={"waiver_translation"}
                  field={"data.name"}
                  required
                  disabled={search.state.preview}
                  defaultValue={waiverTranslation?.name || ""}
                  className="input"
                />
              </div>
            </div>
            <AnimatingDiv className="flex flex-wrap gap-2 items-center print:hidden">
              {search.state.preview ? (
                <Fragment>
                  <p className="font-semibold text-slate-600">Preview</p>
                  <ParamLink className="link" paramState={{ preview: !search.state.preview }}>
                    {search.state.preview ? "edit" : "preview"}
                  </ParamLink>
                  <button
                    type={"button"}
                    className="link"
                    onClick={(e) => {
                      window.print();
                    }}
                  >
                    pdf
                  </button>
                </Fragment>
              ) : (
                <Fragment>
                  <ParamLink className="link" paramState={{ preview: !search.state.preview }}>
                    {search.state.preview ? "edit" : "Preview"}
                  </ParamLink>
                  {commands.map((command) => {
                    return (
                      <div key={command.name} className="inline-flex items-center">
                        <button tabIndex={-1} type={"button"} onClick={command.func} className="hover:text-black text-slate-600">
                          {command.icon}
                        </button>
                        {command.info && (
                          <Tooltip description={command.info}>
                            <InformationCircleIcon className="w-4 h-4" />
                          </Tooltip>
                        )}
                      </div>
                    );
                  })}
                  <select
                    tabIndex={-1}
                    className="select py-0.5 px-2"
                    onChange={(e) => {
                      varFunc(e, e.target.value);
                    }}
                    value=""
                  >
                    {varCommands}
                    <option value="">insert</option>
                    {varCommands.map((option) => {
                      return (
                        <option key={option} value={option}>
                          {option}
                        </option>
                      );
                    })}
                  </select>
                  <a
                    className="link text-xs"
                    tabIndex={-1}
                    target={"_blank"}
                    href={"https://www.diversdesk.com/articles/waiver_cheatsheet/"}
                  >
                    cheatsheat
                  </a>
                </Fragment>
              )}
            </AnimatingDiv>

            {search.state.preview ? (
              <div className="pt-2">
                <hr className="print:hidden" />
                {/*<ParticipantFormProvider data={{ formKey: "medical" }}>*/}
                <MarkdocComp content={content} comps={baseMarkdocComps} vars={{ operator: establishmentTitle || undefined }} />
                {/*<div className="space-y-6">*/}
                {/*  <div className="markdoc print:p-6">*/}
                {/*<MarkdownCompSimple ast={data.ast} />*/}
                {/*<MarkdownComp*/}
                {/*  content={content}*/}
                {/*  vars={{*/}
                {/*    participant: "{participant_name}",*/}
                {/*    operator: establishmentTitle || "{operator_name}",*/}
                {/*  }}*/}
                {/*/>*/}
                {/*</div>*/}
                {/*<SignatureFullComp />*/}
                {/*</div>*/}
                {/*</ParticipantFormProvider>*/}
              </div>
            ) : (
              <div className="relative markdoc-editor">
                <textarea
                  id={"markdoc-field"}
                  value={content}
                  required
                  onChange={(e) => setContent(e.target.value)}
                  className="w-full min-h-72 input relative z-10 bg-transparent text-xs text-transparent caret-black p-2 m-0 border border-slate-300 block"
                  name={fName("waiver_translation", "data.markdoc")}
                  onScroll={(e) => {
                    const textAreaScroll = e.currentTarget.scrollTop;
                    document.getElementById(backdropId)?.scrollTo({ top: textAreaScroll });
                  }}
                  // defaultValue={data?.waiver?.markdoc || ""}
                ></textarea>
                <div
                  className="absolute inset-0 overflow-auto pointer-events-none whitespace-pre-wrap break-words border border-transparent m-0 p-2"
                  id={backdropId}
                >
                  <div
                    className="whitespace-pre-wrap break-words text-xs p-0 border border-transparent"
                    dangerouslySetInnerHTML={{ __html: highlightedText }}
                  ></div>
                </div>
              </div>
            )}
            <div className="space-y-3">
              {isMedicalWaiver &&
                questions.map(([question, subQuestions], questionInex) => {
                  const overwrite = waiverTranslation?.questions?.[questionInex];
                  const overwriteQuestion = overwrite?.[0];
                  const overwriteSubquestions = overwrite?.[1];
                  return (
                    <div key={questionInex} className="space-y-3">
                      <label className="flex flex-row items-center gap-3">
                        <span>{questionInex + 1}: </span>
                        <input
                          disabled={search.state.preview}
                          className="input"
                          name={fName("waiver_translation", "data.questions", 0, questionInex, 0)}
                          defaultValue={overwriteQuestion || question}
                        />
                      </label>
                      {subQuestions?.map((subquestion, subquestionIndex) => {
                        const overwriteSubquestion = overwriteSubquestions?.[subquestionIndex];
                        return (
                          <label key={subquestionIndex} className="flex flex-row items-center gap-3 pl-6">
                            <span>{letters[subquestionIndex] || subquestionIndex + 1}: </span>
                            <input
                              disabled={search.state.preview}
                              className="input"
                              name={fName("waiver_translation", "data.questions", 0, questionInex, 1, subquestionIndex)}
                              defaultValue={overwriteSubquestion || subquestion}
                            />
                          </label>
                        );
                      })}
                    </div>
                  );
                })}
            </div>
          </Fragment>
        )}
        {!search.state.preview && (
          <div className="flex flex-row gap-3 justify-end items-center">
            <ParamLink className="hover:underline" path={_waiver}>
              Cancel
            </ParamLink>
            <SubmitButton className="btn btn-primary">Submit</SubmitButton>
          </div>
        )}
      </ActionForm>
    </div>
  );
}
