import { kysely } from "~/misc/database.server";
import { useLoaderData } from "@remix-run/react";
import { getEstablishmentShort } from "~/domain/establishment/helpers";
import { getSessionSimple } from "~/utils/session.server";
import { isEditorQb } from "~/domain/member/member-queries.server";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { LoaderFunctionArgs } from "@remix-run/router";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { ActionForm } from "~/components/form/BaseFrom";
import { RInput } from "~/components/ResourceInputs";
import { SubmitButton } from "~/components/base/Button";
import { unauthorized } from "~/misc/responses";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { _userDetail } from "~/misc/paths";
import { tableIdRef } from "~/misc/helpers";
import { pageLimits, Paging } from "~/components/Paging";
import { paramsToR<PERSON>ord, StateInputKey } from "~/misc/parsers/global-state-parsers";
import { ParamLink } from "~/components/meta/CustomComponents";
import { useSearchParams2 } from "~/hooks/use-search-params2";

export { ErrorBoundary } from "~/components/RoutDefaults";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);
  const ctx = await getSessionSimple(request);

  const pageLimit = pageLimits.find((limit) => limit === state.page_limit) || pageLimits[0];

  const baseUserQb = kysely
    .selectFrom("user")
    .where("deleted_at", "=", at_infinity_value)
    .$if(!!state.search, (eb) => eb.where("user.email", "ilike", `%${state.search}%`));

  const me = await isEditorQb({ trx: kysely, ctx: ctx })
    .select((eb) => [
      "_user.admin",
      baseUserQb.select((eb) => eb.fn.count<number>("user.id").as("count")).as("total_count"),
      jsonArrayFrom(
        baseUserQb
          .limit(pageLimit)
          .offset(state.page_nr * pageLimit)
          .orderBy("user.email")
          .select(["user.id", "user.email", "user.editor"])
          .select((eb) =>
            jsonArrayFrom(
              kysely
                .selectFrom("member")
                .innerJoin("establishment", "establishment.id", "member.establishment_id")
                .innerJoin("operator", "operator.id", "establishment.operator_id")
                .leftJoin("spot", "spot.id", "establishment.spot_id")
                .where("member.user_id", "=", eb.ref("user.id"))
                .where("member.owner", "=", true)
                .select([
                  "member.user_id",
                  "establishment_id as establishment_id",
                  "establishment.spot_id",
                  "spot.name as spot_name",
                  "establishment.location_name as establishment_name",
                  "operator.name as operator_name",
                ]),
            ).as("owners"),
          ),
      ).as("users"),
    ])
    .executeTakeFirst();
  if (!me) throw unauthorized();
  return {
    ...me,

    page_limit: pageLimit,
  };
};

const searchInputId = "search-input";

export default function Page() {
  const response = useLoaderData<typeof loader>();
  const search = useSearchParams2();

  return (
    <div className="app-container space-y-3 py-3">
      <h1 className="text-2xl font-bold">Users</h1>
      <div className="space-y-9">
        <div>
          <p>Add User</p>
          <ActionForm className="flex flex-row gap-3">
            <RedirectParamsInput path={_userDetail(tableIdRef("user"))} paramState={{}} />
            <RInput className="input" table={"user"} field={"data.email"} placeholder={"email"} />
            <SubmitButton className="btn btn-primary">add</SubmitButton>
          </ActionForm>
        </div>
        <hr />
        <div>
          <p>Search Users</p>
          <ActionForm className="flex flex-row gap-3" method={"get"}>
            <input id={searchInputId} name={"search" satisfies StateInputKey} className="input" placeholder={"Search for email"} />
            <input type={"hidden"} value={""} name={"page_nr" satisfies StateInputKey} />
            {search.state.search && (
              <ParamLink
                path={"./"}
                paramState={{
                  element_action: [searchInputId],
                  element_clear: [searchInputId],
                  rerender: search.state.rerender + 1,
                  page_nr: undefined,
                  search: undefined,
                }}
                className="text-slate-600 p-2 hover:text-black"
              >
                Clear
              </ParamLink>
            )}
            <SubmitButton className="btn btn-primary">Search</SubmitButton>
          </ActionForm>
        </div>
      </div>
      <div className="grid gap-3 md:grid-cols-3">
        {response.users.map((user) => (
          <ParamLink key={user.id} path={user.id} className="rounded-md p-3 shadow-md space-y-3">
            <div className="flex flex-row gap-3 items-center">
              <p>{user.email}</p>
              {user.editor && <span className="bg-slate-200 text-slate-800 p-1 rounded-md text-xs ">editor</span>}
            </div>
            <div className="flex flex-wrap gap-2">
              {user.owners.map((owner) => {
                return (
                  <div className="rounded-md text-sm text-slate-600" key={owner.establishment_id}>
                    {getEstablishmentShort(owner)}
                  </div>
                );
              })}
            </div>
          </ParamLink>
        ))}
      </div>
      <Paging totalCount={response.total_count || 0} pageLimit={response.page_limit} />
    </div>
  );
}
