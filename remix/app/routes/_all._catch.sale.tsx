import { useLoaderD<PERSON> } from "@remix-run/react";
import { LoaderFunctionArgs } from "@remix-run/router";
import { ParamLink } from "~/components/meta/CustomComponents";
import { pageLimits, Paging } from "~/components/Paging";
import { useAppContext } from "~/hooks/use-app-context";
import { isEditorQb, memberHasPermission, memberIsAdminQb, toArgs } from "~/domain/member/member-queries.server";
import { kysely } from "~/misc/database.server";
import { paramsToRecord, StateInputKey } from "~/misc/parsers/global-state-parsers";
import { _booking_detail, _item_detail, _product_detail } from "~/misc/paths";
import { getSessionSimple } from "~/utils/session.server";
import { EstablishmentLayout } from "~/components/AllowedForEstablishment";
import { saleItemWithProductQb } from "~/domain/activity/activity-queries";
import { twMerge } from "tailwind-merge";
import { ActivitySlug, getActivity } from "~/domain/activity/activity";
import { getProductTitle } from "~/domain/product/ProductItem";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { Fragment } from "react";
import { FilterIcon } from "lucide-react";
import { XMarkIcon } from "@heroicons/react/20/solid";
import { arrayAggDisinct, ascNullsFirst, descNullsLast, formatDatetime } from "~/kysely/kysely-helpers";
import { MoneyValue } from "~/components/field/MoneyValue";
import { SidePanel, SidePanelHeading } from "~/components/SidePanel";
import { keys } from "~/misc/helpers";
import { sumBy } from "remeda";
import { ExportIcon } from "~/components/Icons";
import { ActionForm, defaultEqualCheck } from "~/components/form/BaseFrom";
import { SubmitButton } from "~/components/base/Button";
import { DateRangePicker } from "~/components/DateRangePicker";
import type { TableFieldName } from "~/kysely/db";
import { SortButton } from "~/components/SortButton";
import { InputSearchParamCopies } from "~/components/meta/input";
import { OnFormSuccess } from "~/components/base/Button";
import { refreshFormdata } from "~/components/form/form-hooks";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { notFoundOrUnauthorzied } from "~/misc/responses";

export { action } from "~/routes/_all._catch.resource";

const sortColumns = ["product.color", "product.size", "item.activity_slug", "invoice.created_at"] as const;
type SortColumn = (typeof sortColumns)[number];

const dateFields = {
  "invoice.invoice_local_date": "Invoice Date",
  "booking.created_at": "Booking Date",
} satisfies Partial<Record<TableFieldName, string>>;

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);
  const { session_id } = await getSessionSimple(request);

  const pageLimit = pageLimits.find((limit) => limit === state.page_limit) || pageLimits[0];

  if (state.persist_establishment_id) {
    const targetEstablishments = await kysely
      .selectFrom("establishment")
      .where("establishment.id", "=", state.persist_establishment_id)
      .where("establishment.id", "in", memberHasPermission(toArgs(kysely, session_id), "sales").select("_member.establishment_id"))
      .executeTakeFirst();

    if (!targetEstablishments) {
      throw notFoundOrUnauthorzied();
    }
  } else {
    const isEditor = await isEditorQb(toArgs(kysely, session_id)).executeTakeFirst();
    if (!isEditor) {
      throw notFoundOrUnauthorzied();
    }
  }

  console.log(state.sorts);

  const baseQuery = saleItemWithProductQb
    .innerJoin("booking", "booking.id", "sale_item.booking_id")
    .innerJoin("establishment", "establishment.id", "booking.establishment_id")
    .innerJoin("operator", "operator.id", "establishment.operator_id")
    .innerJoin("spot", "spot.id", "establishment.spot_id")
    .innerJoin("region", "region.id", "spot.region_id")
    .leftJoin("product", "product.id", "sale_item.product_id")
    .leftJoin("item", "item.id", "product.item_id")
    .leftJoin("invoice", "invoice.booking_id", "booking.id")
    .where("booking.cancelled_at", "is", null)
    .where("booking.cart_for_session_id", "is", null)
    .$if(!!state.persist_establishment_id, (eb) => eb.where("establishment.id", "=", state.persist_establishment_id));

  const baseTotalsQuery = await baseQuery
    .clearSelect()
    .clearOrderBy()
    .select((eb) => [
      arrayAggDisinct(eb.ref("booking.currency_id")).as("currency_ids"),
      arrayAggDisinct(eb.ref("item.activity_slug")).as("activity_slugs"),
    ])
    .executeTakeFirstOrThrow();

  // const currency = baseTotalsQuery.currency_ids[0] || defaultCurrency;

  const filteredQuery = baseQuery
    .select((eb) => [
      "operator.name as operator_name",
      "booking.booking_reference as booking_reference",
      "booking.id as booking_id",
      "product.color",
      "product.size",
      "booking.vat_rate",
      "booking.currency_id",
      "establishment.locale as establishment_locale",
      formatDatetime(eb.ref("invoice.created_at"), "YYYY.MM.DD HH24:MI:SS", eb.ref("region.timezone")).as("invoice_created_at_formatted"),
      formatDatetime(eb.ref("booking.created_at"), "YYYY.MM.DD HH24:MI:SS", eb.ref("region.timezone")).as("booking_created_at_formatted"),
    ])
    .$call((eb) => {
      let filterEb = eb;
      if (state.activity_slugs.length > 0) {
        filterEb = filterEb.where("item.activity_slug", "in", state.activity_slugs);
      }
      if (state.product_id) {
        filterEb = filterEb.where("sale_item.product_id", "=", state.product_id);
      }
      if (state.currency_id) {
        filterEb = filterEb.where("booking.currency_id", "=", state.currency_id);
      }
      const dateField = keys(dateFields).find((key) => key === state.date_field);
      if (dateField) {
        if (state.date_from) {
          filterEb = filterEb.where(dateField, ">=", state.date_from);
        }
        if (state.date_to) {
          filterEb = filterEb.where(dateField, "<=", state.date_to);
        }
      }

      return filterEb;
    })
    .$call((eb) => {
      let orderEb = eb;
      const filteredSorts = state.sorts.filter((sort) => sortColumns.includes(sort.key as SortColumn));
      if (filteredSorts.length === 0) return eb.orderBy("sale_item.created_at", "desc");
      filteredSorts.forEach((sort) => {
        orderEb = orderEb.orderBy(sort.key as SortColumn, sort.direction === "asc" ? ascNullsFirst : descNullsLast);
      });
      return orderEb;
    });

  const sales = await filteredQuery
    .offset(state.page_nr * pageLimit)
    .limit(pageLimit)
    .execute();

  const filteredTotals = await filteredQuery
    .clearSelect()
    .clearOrderBy()
    .groupBy("booking.currency_id")
    .select((eb) => [
      eb.fn.count<number>("sale_item.id").as("count"),
      eb.fn.sum<number>("sale_item.quantity").as("quantity"),
      eb.ref("booking.currency_id").as("currency_id"),
      eb.fn.sum<number>("sale_item.cached_total_price_amount").as("total_price_amount"),
      eb.fn.sum<number>("sale_item.cached_total_tax_amount").as("total_tax_amount"),
    ])
    .execute();

  const filteredProduct =
    state.product_id &&
    (await kysely
      .selectFrom("product")
      .innerJoin("item", "item.id", "product.item_id")
      .where("product.id", "=", state.product_id)
      .select(["product.id", "product.sku", "product.color", "product.size", "item.title", "item.description"])
      .executeTakeFirst());

  return {
    sales,
    baseTotals: baseTotalsQuery,
    totals: filteredTotals,
    pageLimit,
    filteredProduct,
  };
};

export default function SalesPage() {
  const search = useSearchParams2();
  const { sales, totals, pageLimit, filteredProduct, baseTotals } = useLoaderData<typeof loader>();
  const { establishment, date } = useAppContext();
  const totalCount = sumBy(totals, (total) => total.count);

  const nrOfFilters =
    search.state.activity_slugs.length +
    (search.state.product_id ? 1 : 0) +
    (search.state.currency_id ? 1 : 0) +
    (search.state.date_from || search.state.date_to ? 1 : 0);

  return (
    <EstablishmentLayout
      title={
        <div className="flex items-center gap-3 mb-4">
          <h1 className="text-xl font-semibold">Sales</h1>
          <ParamLink paramState={{ toggle_modal: "filter" }} className="btn gap-0.5 text-sm">
            <FilterIcon className="w-4 h-4" />
            <span>Filters</span>
          </ParamLink>
          {nrOfFilters > 0 && (
            <ParamLink
              paramState={{ activity_slugs: [], product_id: null, currency_id: null, date_from: null, date_to: null }}
              reload
              className="btn gap-0.5 text-sm rounded-md bg-slate-100"
            >
              <XMarkIcon className="w-4 h-4" />
              <span>Clear {nrOfFilters}</span>
            </ParamLink>
          )}
          <button
            className="btn gap-1 text-primary hover:text-primary-700 whitespace-nowrap"
            type="button"
            onClick={() => {
              try {
                const headers = [
                  ...(establishment ? [] : ["Establishment"]),
                  "Booking Reference",
                  "Type",
                  "Product/Service",
                  "Color",
                  "Size",
                  "Quantity",
                  "Currency",
                  "Price/Person",
                  "Total",
                  "Tax",
                  "Invoice Date",
                ].join(",");

                const rows = sales
                  .map((activity) => {
                    const productType = getActivity(activity.product?.activity_slug);
                    const product = activity.product;
                    const total = activity.price_pp * activity.quantity;
                    const tax = total * (activity.vat_rate / 100);

                    return [
                      ...(establishment ? [] : [activity.operator_name || ""]),
                      activity.booking_reference || "",
                      productType.name || "Custom item",
                      product ? getProductTitle(product) : "-",
                      activity.color || "-",
                      activity.size || "-",
                      activity.quantity,
                      activity.currency_id,
                      activity.price_pp,
                      total,
                      tax,
                      activity.invoice_created_at_formatted || "",
                    ]
                      .map((field) => (typeof field === "string" && field.includes(",") ? `"${field}"` : field))
                      .join(",");
                  })
                  .join("\n");

                const csvContent = headers + "\n" + rows;

                const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8" });
                const a = document.createElement("a");
                const downloadUrl = URL.createObjectURL(blob);
                a.href = downloadUrl;
                a.download = `sales-export-${new Date().toISOString().split("T")[0]}.csv`;
                a.style.display = "none";
                document.body.appendChild(a);

                a.click();

                document.body.removeChild(a);
                URL.revokeObjectURL(downloadUrl);
              } catch (e) {
                console.error("export error", e);
                window.alert("export error");
              }
            }}
          >
            <ExportIcon className="w-4 h-4" />
            Export ({sales.length})
          </button>
        </div>
      }
    >
      <div className="space-y-4">
        <div className="overflow-auto">
          <div
            style={{ gridTemplateColumns: `repeat(${establishment ? 11 : 12}, minmax(auto, 1fr))` }}
            className={twMerge("grid gap-x-4 text-sm")}
          >
            {totals.map((total) => (
              <div className="contents">
                <div style={{ gridColumn: `span ${establishment ? 5 : 6}` }} className="text-sm font-medium">
                  Total Sales {total.currency_id} ({total.count})
                </div>
                <div className="text-sm font-medium">{total.quantity}</div>
                <div></div>
                <div className="text-sm font-medium">
                  <MoneyValue
                    nativeAmount={total.total_price_amount}
                    nativeCurrency={total.currency_id}
                    toCurrency={total.currency_id}
                    locale={null}
                  />
                </div>
                <div className="text-sm font-medium">
                  <MoneyValue
                    nativeAmount={total.total_tax_amount}
                    nativeCurrency={total.currency_id}
                    toCurrency={total.currency_id}
                    locale={null}
                  />
                </div>
                <div></div>
                <div></div>
              </div>
            ))}
            <div className="font-semibold contents">
              {!establishment && <div className="py-4">Establishment</div>}
              <div className="whitespace-nowrap py-4">Booking/Sale</div>
              <div className="whitespace-nowrap flex items-center py-4">
                <SortButton column={"item.activity_slug" as SortColumn}>Type</SortButton>
              </div>
              <div className="whitespace-nowrap py-4">Product/Service</div>
              <div className="whitespace-nowrap flex items-center py-4">
                <SortButton column={"product.color" as SortColumn}>Color</SortButton>
              </div>
              <div className="whitespace-nowrap flex items-center py-4">
                <SortButton column={"product.size" as SortColumn}>Size</SortButton>
              </div>
              <div className="whitespace-nowrap py-4">Quantity</div>
              <div className="whitespace-nowrap py-4">Price/Person</div>
              <div className="whitespace-nowrap py-4">Total</div>
              <div className="whitespace-nowrap py-4">Tax</div>
              <div className="whitespace-nowrap items-center flex py-4">
                <SortButton column={"invoice.created_at" as SortColumn}>Invoice Date</SortButton>
              </div>
              <div className="whitespace-nowrap items-center flex py-4">
                <SortButton column={"booking.created_at" as SortColumn}>Booking Date</SortButton>
              </div>
            </div>
            {sales.map((activity, index) => {
              const productType = getActivity(activity.product?.activity_slug);
              const product = activity.product;
              const total = activity.price_pp * activity.quantity;
              const tax = total * (activity.vat_rate / 100);
              return (
                <Fragment key={activity.id}>
                  {/* <div className="col-span-full relative border-b border-slate-100" /> */}
                  {/* <div className={"col-span-full relative border-b border-slate-100"}>
                    <div className="h-12 absolute top-0 left-0 w-full hover:bg-slate-100"></div>
                  </div> */}
                  <div
                    className={
                      "grid grid-cols-subgrid gap-4 col-span-full relative group hover:bg-slate-50 transition-colors py-4 border-t border-slate-150"
                    }
                  >
                    {!establishment && <div className="">{activity.operator_name}</div>}
                    <div className={twMerge("whitespace-nowrap ", index % 2 === 0 && "")}>
                      <ParamLink path={_booking_detail(activity.booking_id)} className="link">
                        {activity.booking_reference}
                      </ParamLink>
                    </div>
                    <div className={twMerge("whitespace-nowrap ", index % 2 === 0 && "")}>{productType.name || "Custom item"}</div>
                    <div className={twMerge("whitespace-nowrap ", index % 2 === 0 && "")}>
                      {product ? (
                        <ParamLink
                          path={productType.retail ? _item_detail(product.item_id) : _product_detail(product.id)}
                          paramState={{ product_id: product.id }}
                          className="link"
                        >
                          {getProductTitle(product)}
                        </ParamLink>
                      ) : (
                        activity.description || "-"
                      )}
                    </div>
                    <div className={twMerge("whitespace-nowrap ", index % 2 === 0 && "")}>{activity.color || "-"}</div>
                    <div className={twMerge("whitespace-nowrap ", index % 2 === 0 && "")}>{activity.size || "-"}</div>
                    <div className={twMerge("whitespace-nowrap ", index % 2 === 0 && "")}>{activity.quantity}</div>
                    <div className={twMerge("whitespace-nowrap ", index % 2 === 0 && "")}>
                      <MoneyValue
                        nativeAmount={activity.price_pp}
                        nativeCurrency={activity.currency_id}
                        toCurrency={activity.currency_id}
                        locale={activity.establishment_locale}
                      />
                    </div>
                    <div className={twMerge("whitespace-nowrap ", index % 2 === 0 && "")}>
                      <MoneyValue
                        nativeAmount={total}
                        nativeCurrency={activity.currency_id}
                        toCurrency={activity.currency_id}
                        locale={activity.establishment_locale}
                      />
                    </div>
                    <div className={twMerge("whitespace-nowrap ", index % 2 === 0 && "")}>
                      <MoneyValue
                        nativeAmount={tax}
                        nativeCurrency={activity.currency_id}
                        toCurrency={activity.currency_id}
                        locale={activity.establishment_locale}
                      />
                    </div>
                    <div className={twMerge("whitespace-nowrap ", index % 2 === 0 && "")}>{activity.invoice_created_at_formatted}</div>
                    <div className={twMerge("whitespace-nowrap ", index % 2 === 0 && "")}>{activity.booking_created_at_formatted}</div>
                  </div>
                </Fragment>
              );
            })}
          </div>
        </div>
        <Paging totalCount={totalCount} pageLimit={pageLimit} />
      </div>
      <SidePanel dialogname="filter">
        <div className=" bg-white h-full overflow-auto md:max-w-md">
          <ActionForm method="get" onCheckEqual={defaultEqualCheck}>
            <SidePanelHeading className="sticky top-0 bg-white pt-4 pb-2 ">
              <h2>Filters</h2>
            </SidePanelHeading>
            {/* <OnFormSuccess
              onSuccess={() => {
                refreshFormdata();
              }}
            /> */}
            <InputSearchParamCopies
              excludeKeys={[
                "search",
                "form_success_id",
                "date_field",
                "error_message",
                "date_from",
                "date_to",
                "element_action",
                "element_clear",
                "toggle_modal",
              ]}
            />
            <RedirectParamsInput path="./" paramState={{ toggle_modal: undefined }} />
            <div className="space-y-6">
              <div className="space-y-4 p-5">
                {filteredProduct && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold text-blue-900">Filtered by Product:</h3>
                        <div className="text-blue-800 mt-1">
                          <span className="font-medium">{filteredProduct.title}</span>
                          {(filteredProduct.color || filteredProduct.size || filteredProduct.sku) && (
                            <span className="text-sm ml-2">
                              (
                              {[
                                filteredProduct.sku && `SKU: ${filteredProduct.sku}`,
                                filteredProduct.color && `Color: ${filteredProduct.color}`,
                                filteredProduct.size && `Size: ${filteredProduct.size}`,
                              ]
                                .filter(Boolean)
                                .join(", ")}
                              )
                            </span>
                          )}
                        </div>
                      </div>
                      <ParamLink paramState={{ product_id: null }} reload className="btn btn-sm btn-basic">
                        Clear Filter
                      </ParamLink>
                    </div>
                  </div>
                )}
                <div className="space-y-4">
                  <p className="text-lg text-slate-600">Product Type</p>
                  <div className="flex items-center gap-2">
                    <div className="flex flex-wrap gap-2">
                      {baseTotals.activity_slugs.map((activitySlug) => {
                        const activity = getActivity(activitySlug);
                        const id = `activity_slug_${activitySlug}`;
                        return (
                          <div key={id}>
                            <input
                              type="checkbox"
                              id={id}
                              name="activity_slugs"
                              defaultChecked={search.state.activity_slugs.includes(activitySlug as ActivitySlug)}
                              value={activitySlug}
                              className="hidden peer"
                            />
                            <label htmlFor={id} className="btn btn-sm btn-basic peer-checked:btn-secondary cursor-pointer">
                              {activity.name}
                            </label>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
              <div className="p-5 border-t border-slate-100 space-y-4">
                <p className="text-lg text-slate-600">Currency</p>
                <div className="flex items-center gap-2">
                  <div className="flex flex-wrap gap-2">
                    {baseTotals.currency_ids.map((currency_id) => (
                      <div key={currency_id}>
                        <input
                          type="radio"
                          id={currency_id}
                          name="currency_id"
                          defaultChecked={search.state.currency_id === currency_id}
                          value={currency_id}
                          className="hidden peer"
                        />
                        <label
                          key={currency_id}
                          className={`btn btn-sm btn-basic peer-checked:btn-secondary cursor-pointer`}
                          htmlFor={currency_id}
                        >
                          {currency_id}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="p-5 border-t border-slate-100 space-y-4">
                <p className="text-lg text-slate-600 flex items-center gap-2">
                  <select
                    name={"date_field" satisfies StateInputKey}
                    className="select w-full"
                    defaultValue={search.state.date_field || ("invoice.invoice_local_date" satisfies keyof typeof dateFields)}
                  >
                    {Object.entries(dateFields).map(([dateField, label]) => {
                      return (
                        <option key={dateField} value={dateField}>
                          {label}
                        </option>
                      );
                    })}
                  </select>
                </p>
                <div className="space-y-4">
                  <DateRangePicker
                    defaultValue={{
                      from: search.state.date_from ? new Date(search.state.date_from) : null,
                      to: search.state.date_to ? new Date(search.state.date_to) : null,
                    }}
                    fromName={"date_from" satisfies StateInputKey}
                    toName={"date_to" satisfies StateInputKey}
                  />
                </div>
              </div>
              <div className="p-5 border-t border-slate-100 sticky bottom-0 bg-white">
                <SubmitButton className="btn btn-primary">Apply</SubmitButton>
              </div>
            </div>
          </ActionForm>
        </div>
      </SidePanel>
    </EstablishmentLayout>
  );
}
