import React, { Fragment, useId, useMemo } from "react";
import { Form, useLoaderData, useNavigation, useSubmit } from "@remix-run/react";
import { BaseLink, OverlayTransitionChild } from "~/components/base/base";
import type { DataFunctionArgs } from "@remix-run/server-runtime";
import { bbox, feature, featureCollection } from "@turf/turf";
import { useMap } from "~/hooks/use-map-context";
import buffer from "@turf/buffer";
import { useMapNavigateOnPointClick } from "~/hooks/use-map-navigate-on-point-click";
import { t } from "~/misc/trans";
import { ProductItem } from "~/domain/product/ProductItem";
import {
  baseProductQbExplore,
  divingCoursesEb,
  divingCoursesJsonEb,
  divingLevelsEb,
  divingLevelsJsonEb,
  divingLocationsEb,
  divingLocationsJsonEb,
  orderProduct,
  pricesJsonEb,
  productFilterPerKey,
} from "~/domain/product/product-queries.server";
import { usePointsLayer } from "~/hooks/use-points-layer";
import { MapPortal } from "~/components/MapPortal";
import { kysely } from "~/misc/database.server";
import { sql } from "kysely";
import { EstablishmentItem } from "~/domain/establishment/EstablishmentItem";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { BiFilter } from "react-icons/bi";
import { Dialog, Transition } from "@headlessui/react";
import { Button, CloseButton } from "~/components/base/Button";
import { useBoolean } from "~/hooks/use-boolean";
import { GreyTag, TagCloseButton } from "~/components/base/Tag";
import type { IdName } from "~/misc/models";
import { durationGroupKeys, durationGroups } from "~/domain/product/product-data";
import { _establishment_detail, _product_detail } from "~/misc/paths";
import type { DivingCourseTagKey } from "~/data/diving";
import { divingCourseTagKeyParser } from "~/data/diving";
import { defaultNavOptions } from "~/misc/vars";
import { BooleanFilter, createFilterUrl, FilterCheckbox, FilterSelect } from "~/domain/product/filter-components";
import { ChevronRightIcon } from "@heroicons/react/20/solid";
import { Divider } from "~/components/Divider";
import { languagesAsIdNames } from "~/data/languages";
import { reviewsRatingQb } from "~/domain/review/review-queries";
import { activeActivitySlugs, activities, FilterKey, getFilters } from "~/domain/activity/activity";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { createPageOverwrites } from "~/misc/consts";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { unnestArray } from "~/kysely/kysely-helpers";
import { divingLevelsz, divingOrganizationszz, getDivingCertificateOrganization } from "~/domain/diving-course/diving-courses.data";
import { keys } from "~/misc/helpers";
import { getWhitelabelFromHost } from "~/utils/domain-helpers";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { getHost } from "~/misc/web-helpers";
import { createMeta } from "~/misc/route-helpers";
import { paramsToRecord, StateInputKey } from "~/misc/parsers/global-state-parsers";
import { ParamLink } from "~/components/meta/CustomComponents";

const productLimitPerOperator = 2;

const exploreFiltersFormClass = "explore-filters-form";

export const meta = createMeta({ title: "Explore", robots: "index" });

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: DataFunctionArgs) => {
  // if (environment === "dev") {
  //   await new Promise((resolve) => setTimeout(resolve, 10000));
  // }

  const operatorSlug = getWhitelabelFromHost(getHost(request));
  // throw new Error("blabal");
  // if (operatorSubdomain) throw notFound();

  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);
  const getFilterValues = (key: FilterKey) => url.searchParams.getAll(key);

  const activitySlug = activeActivitySlugs.find((type) => type === state.activity_slug);
  const activeFilters = getFilters(activitySlug);

  const initQb = baseProductQbExplore.where("product.deleted_at", "=", at_infinity_value);

  const baseQb = (activitySlug ? initQb.where("item.activity_slug", "=", activitySlug) : initQb).$if(!!operatorSlug, (eb) =>
    eb.innerJoin("operator", "operator.id", "p_establishment.operator_id").where("operator.slug", "=", operatorSlug),
  );

  const getFilteredQb = (exclude?: FilterKey) =>
    activeFilters
      .filter((key) => url.searchParams.getAll(key).length > 0 && exclude !== key)
      .reduce((accumulatedQueryBuilder, key) => {
        const filterValue = url.searchParams.getAll(key);
        return productFilterPerKey[key](accumulatedQueryBuilder, filterValue);
      }, baseQb);

  const divingLocationIdsInBaseResultQb = baseQb
    .innerJoin("product__diving_location as pdl", "pdl.product_id", "product.id")
    .select("pdl.diving_location_id");

  const divingLocationsQb = kysely
    .selectFrom("diving_location")
    .where("diving_location.id", "in", divingLocationIdsInBaseResultQb)
    .select([
      "diving_location.id",
      "diving_location.name",
      (eb) =>
        getFilteredQb("diving_locations")
          .innerJoin("product__diving_location as pdl", "pdl.product_id", "product.id")
          .select((eb) => sql<number>`(count ( distinct ${eb.ref("product.id")}))`.as("count"))
          .whereRef("pdl.diving_location_id", "=", eb.ref("diving_location.id"))
          .as("count"),
    ])
    .orderBy("name");

  const divingSiteIdsInBaseResultQb = baseQb
    .innerJoin("product__diving_site", "product__diving_site.product_id", "product.id")
    .select("product__diving_site.diving_site_id");

  const divingSitesQb = kysely
    .selectFrom("diving_site")
    .where("diving_site.id", "in", divingSiteIdsInBaseResultQb)
    .innerJoin("diving_location", "diving_location.id", "diving_site.diving_location_id")
    .select([
      "diving_site.id",
      "diving_site.name",
      "diving_site.diving_location_id",
      "diving_location.name as diving_location_name",
      (eb) =>
        getFilteredQb("diving_sites")
          .innerJoin("product__diving_site", "product__diving_site.product_id", "product.id")
          .select((eb) => sql`(count ( distinct ${eb.ref("product.id")}))`.as("count"))
          .whereRef("product__diving_site.diving_site_id", "=", eb.ref("diving_site.id"))
          .as("count"),
    ])
    .orderBy("diving_location.name")
    .orderBy("diving_site.name");

  const divingLevelKeysInBaseResultQb = baseQb
    .innerJoin("item__diving_course as pdc", "pdc.item_id", "item.id")
    .innerJoin("diving_course as dc", "dc.id", "pdc.diving_course_id")
    .select("dc.diving_certificate_level_key");

  const divingLevelsQb = kysely
    .selectFrom(unnestArray(keys(divingLevelsz)))
    .where("arr.key", "in", divingLevelKeysInBaseResultQb)
    .selectAll()
    .select([
      (eb) =>
        getFilteredQb("diving_certificate_level")
          .innerJoin("item__diving_course as pdc", "pdc.item_id", "item.id")
          .innerJoin("diving_course as dc", "dc.id", "pdc.diving_course_id")
          .select((eb) => sql<number>`(count ( distinct ${eb.ref("product.id")}))`.as("count"))
          .whereRef("dc.diving_certificate_level_key", "=", eb.ref("arr.key"))
          .as("count"),
    ]);

  const divingCertificateOrgIdsInBaseResultQb = baseQb
    .innerJoin("item__diving_course as pdc", "pdc.item_id", "item.id")
    .innerJoin("diving_course as dc", "dc.id", "pdc.diving_course_id")
    .select("dc.diving_certificate_organization_key");

  const divingCertificateOrgsQb = kysely
    .selectFrom(unnestArray(keys(divingOrganizationszz)))
    .where("arr.key", "in", divingCertificateOrgIdsInBaseResultQb)
    .select([
      "arr.key",
      (eb) =>
        getFilteredQb("diving_certificate_organization")
          .innerJoin("item__diving_course as pdc", "pdc.item_id", "item.id")
          .innerJoin("diving_course as dc", "dc.id", "pdc.diving_course_id")
          .select((eb) => sql<number>`(count ( distinct ${eb.ref("product.id")}))`.as("count"))
          .whereRef("dc.diving_certificate_organization_key", "=", eb.ref("arr.key"))
          .as("count"),
    ]);

  const spotsQb = kysely
    .selectFrom("spot")
    .select(["spot.id", "spot.name"])
    .where("spot.id", "in", baseQb.select("spot.id"))
    .orderBy("spot.name");

  const divingCourseIdsInBaseResultQb = baseQb
    .innerJoin("item__diving_course as pdc", "pdc.item_id", "item.id")
    .select("pdc.diving_course_id");

  const divingCoursesQb = kysely
    .selectFrom("diving_course as dc")
    .where("dc.id", "in", divingCourseIdsInBaseResultQb)
    .innerJoin(unnestArray(keys(divingLevelsz)), "arr.key", "dc.diving_certificate_level_key")
    .select([
      "dc.id",
      "dc.name",
      "dc.diving_certificate_organization_key",
      "dc.diving_certificate_level_key",
      (eb) =>
        getFilteredQb("diving_courses")
          .innerJoin("item__diving_course as pdc", "pdc.item_id", "item.id")
          .select((eb) => sql<number>`(count ( distinct ${eb.ref("pdc.item_id")}))`.as("count"))
          .whereRef("pdc.diving_course_id", "=", eb.ref("dc.id"))
          .as("count"),
    ])
    .orderBy("arr.pos")
    .orderBy("dc.sort_order");

  const operatorLanguagesQb = kysely.selectFrom("establishment__language").select("language_code").distinct();

  const pickupQb = getFilteredQb("pickup").select([
    (eb) => sql<number>`(count (${eb.ref("product.id")}) filter (where ${eb.ref("product.pickup")} = true))`.as("yes"),
    (eb) => sql<number>`(count (${eb.ref("product.id")}) filter (where ${eb.ref("product.pickup")} = false))`.as("no"),
  ]);

  const gearQb = getFilteredQb("gear_included").select([
    (eb) => sql<number>`(count (${eb.ref("product.id")}) filter (where ${eb.ref("product.gear_included")} = true))`.as("yes"),
    (eb) => sql<number>`(count (${eb.ref("product.id")}) filter (where ${eb.ref("product.gear_included")} = false))`.as("no"),
  ]);

  const stayQb = getFilteredQb("stay").select([
    (eb) => sql<number>`(count (${eb.ref("product.id")}) filter (where ${eb.ref("product.stay")} = true))`.as("yes"),
    (eb) => sql<number>`(count (${eb.ref("product.id")}) filter (where ${eb.ref("product.stay")} = false))`.as("no"),
  ]);

  const operatorLocationsQb = kysely
    .with("product", () =>
      getFilteredQb()
        .selectAll("product")
        .select((eb) => [
          "p_establishment.operator_id as operator_id",
          "p_establishment.spot_id as spot_id",
          eb
            .selectFrom(divingCoursesEb.as("list"))
            .select((eb) => sql<string[]>`(coalesce (array_agg(${eb.ref("list.name")}), '{}'::text []))`.as("names"))
            .as("diving_course_names"),
          eb
            .selectFrom(divingLocationsEb.as("list"))
            .select((eb) => sql<string[]>`(coalesce (array_agg(${eb.ref("list.name")}), '{}'::text []))`.as("names"))
            .as("diving_location_names"),
          eb
            .selectFrom(divingLevelsEb.as("list"))
            .select((eb) => sql<string[]>`(coalesce (array_agg(${eb.ref("list.name")}), '{}'::text []))`.as("names"))
            .as("diving_certificate_level_names"),
          divingCoursesJsonEb,
          divingLocationsJsonEb,
          divingLevelsJsonEb,
          pricesJsonEb,
        ])
        .$call((eb) => orderProduct(eb)),
    )
    .selectFrom("establishment")
    .innerJoin("spot", "spot.id", "establishment.spot_id")
    .innerJoin("operator", "operator.id", "establishment.operator_id")
    .select((eb) => [
      "establishment.id",
      "operator.name as operator_name",
      "spot.name as spot_name",
      "establishment.location_name",
      jsonArrayFrom(fileTargetsQb(kysely, "establishment", eb.ref("establishment.id"))).as("files"),
      sql<any | null>`(ST_asGeoJSON(${eb.ref("establishment.geom")})::jsonb)`.as("geom"),
      "establishment.address",
      "establishment.bio",
      reviewsRatingQb.where("review.establishment_id", "=", eb.ref("establishment.id")).as("ratings"),
      eb
        .selectFrom("product")
        .innerJoin("item", "item.id", "product.item_id")
        .select((eb) => eb.fn.count("product.id").as("count"))
        .whereRef("item.establishment_id", "=", "establishment.id")
        .as("product_count"),
      jsonArrayFrom(
        eb
          .selectFrom("product")
          .innerJoin("item", "item.id", "product.item_id")
          .selectAll("item")
          .selectAll("product")
          .whereRef("item.establishment_id", "=", "establishment.id")
          .orderBy((eb) => sql`(array_length(${eb.ref("product.diving_course_names")}, 1))`, sql`asc nulls last`)
          .orderBy((eb) => sql`(array_length(${eb.ref("product.diving_location_names")}, 1))`, sql`asc nulls last`)
          .orderBy("product.diving_count")
          .orderBy("product.diving_course_names")
          .orderBy("product.diving_location_names")
          .limit(productLimitPerOperator),
      ).as("products"),
    ])
    .where("establishment.id", "in", (eb) =>
      eb.selectFrom("product").innerJoin("item", "item.id", "product.item_id").select("item.establishment_id"),
    );

  const combinedQb = kysely.selectNoFrom((eb) => [
    jsonArrayFrom(operatorLocationsQb).as("operatorLocationResult"),
    jsonArrayFrom(divingLevelsQb).as("divingLevels"),
    jsonArrayFrom(spotsQb).as("spots"),
    jsonObjectFrom(pickupQb).as("pickup"),
    jsonObjectFrom(stayQb).as("stay"),
    jsonObjectFrom(gearQb).as("gear"),
    jsonArrayFrom(divingCoursesQb).as("divingCourses"),
    jsonArrayFrom(divingCertificateOrgsQb).as("divingOrganizations"),
    jsonArrayFrom(divingLocationsQb).as("divingLocations"),
    jsonArrayFrom(divingSitesQb).as("divingSites"),
    jsonArrayFrom(operatorLanguagesQb).as("operatorLanguagesResult"),
  ]);

  console.time("combined");
  const result = await combinedQb.executeTakeFirstOrThrow();
  console.timeEnd("combined");

  const {
    operatorLocationResult,
    divingLevels,
    spots,
    pickup,
    stay,
    gear,
    divingCourses,
    divingOrganizations,
    divingLocations,
    divingSites,
    operatorLanguagesResult,
  } = result;

  const operatorLanuagesCodes = operatorLanguagesResult.map((operatorLanguage) => operatorLanguage.language_code);

  return {
    ...createPageOverwrites({ show_whatsapp: true, show_currency_swap: true }),
    establishments: operatorLocationResult,
    total_count: operatorLocationResult.length,
    order_fields: [],
    filters: {
      divingLevels: divingLevels.map((level) => ({
        id: level.key,
        name: divingLevelsz[level.key],
        count: level.count,
      })),
      spots: spots,
      pickup: pickup,
      stay: stay,
      gear: gear,
      // durations: durations,
      diving_courses: divingCourses
        .filter((item) => {
          if (getFilterValues("diving_courses").find((id) => item.id === id)) return true;
          const dclFilterValues = getFilterValues("diving_certificate_level");
          const dcoFilterValues = getFilterValues("diving_certificate_organization");
          const totalCount = dclFilterValues.length + dcoFilterValues.length;
          return (
            totalCount > 0 &&
            (dclFilterValues.length === 0 || dclFilterValues.find((key) => item.diving_certificate_level_key === key)) &&
            (dcoFilterValues.length === 0 || dcoFilterValues.find((key) => item.diving_certificate_organization_key === key))
          );
        })
        .map((divingCourse) => ({
          ...divingCourse,
          name: getDivingCertificateOrganization(divingCourse.diving_certificate_organization_key)?.name + " - " + divingCourse.name,
        })),
      diving_locations: divingLocations,
      diving_organizations: divingOrganizations.map((level) => ({
        id: level.key,
        name: getDivingCertificateOrganization(level.key)?.name || "",
        count: level.count,
      })),
      diving_sites: divingSites
        .filter(
          (item) =>
            getFilterValues("diving_locations").find((id) => item.diving_location_id === id) ||
            getFilterValues("diving_sites").find((id) => item.id === id),
        )
        .map((item) => ({
          ...item,
          name: item.diving_location_name + " - " + item.name,
        })),
      languages: languagesAsIdNames.filter((language) => operatorLanuagesCodes.includes(language.id)),
    },
    data: operatorLocationResult,
  };
};

const divingCourseTitles: Record<
  DivingCourseTagKey,
  {
    title: string;
    description: string;
  }
> = {
  first_time_diver: {
    title: "Try-dive Courses",
    description: "These courses are perfect to try if diving is for you before committing to a multi-day course.",
  },
  ready_to_start: {
    title: "Beginner Diver Courses",
    description:
      "All suitable courses for you as a beginner diver are listed below. Narrow down your preferences using the filters to find your perfect match.",
  },
};

const MapBla = () => {
  const response = useLoaderData<typeof loader>();
  const features = useMemo(
    () =>
      response.data.map((operatorLocation) =>
        feature(operatorLocation.geom, {
          name: operatorLocation.operator_name,
          path: _establishment_detail(operatorLocation.id),
        }),
      ),
    [response.data],
  );

  const collection = useMemo(() => featureCollection(features), [features]);

  useMap();
  usePointsLayer(collection);
  useMapNavigateOnPointClick();

  return (
    <MapPortal
      onFit={(map) => {
        if (features.length > 0) {
          const bufferedCollection = buffer(collection, 1, {
            units: "kilometers",
          });
          const [minLng, minLat, maxLng, maxLat] = bbox(bufferedCollection);
          map.fitBounds(
            [
              [minLng, minLat],
              [maxLng, maxLat],
            ],
            { padding: 40, duration: 20 },
          );
        }
      }}
    />
  );
};

const Filters = () => {
  const { state } = useSearchParams2();
  // we use prefixId to make unique ids for label/checkbox because this form is used twice on the same page
  const prefixId = useId();
  const activitySlug = activeActivitySlugs.find((type) => type === state.activity_slug);
  const activeFilters = getFilters(activitySlug);
  const submit = useSubmit();
  const response = useLoaderData<typeof loader>();

  const divingLevels = response.filters.divingLevels;

  const isFilterEnabled = (key: FilterKey) => !!activeFilters.find((filterKey) => filterKey === key);

  return (
    <Form preventScrollReset className={`flex flex-col space-y-3 ${exploreFiltersFormClass}`}>
      <div className="space-y-2">
        {activeActivitySlugs.map((slug) => {
          const id = "id_prefix_" + slug + prefixId;
          return (
            <div key={slug} className="flex flex-row items-center gap-2 text-primary hover:text-primary-700">
              <input
                id={id}
                type={"radio"}
                name={"activity_slug" satisfies StateInputKey}
                className={"peer flex h-4 w-4 flex-row gap-2 rounded-full text-primary transition-colors"}
                onChange={(e) => {
                  const form = e.currentTarget?.form;
                  form && submit(form);
                }}
                defaultChecked={activitySlug === slug}
                value={slug}
              />
              <label htmlFor={id} className="leading-3 peer-checked:font-bold">
                {activities[slug].name}
              </label>
            </div>
          );
        })}
      </div>
      {isFilterEnabled("diving_locations") && (
        <div className="flex flex-col gap-1">
          <p className="font-bold">Where do you want to {activitySlug === "snorkeling" ? "snorkel" : "dive"}?</p>
          {response.filters.diving_locations.map((item) => (
            <FilterCheckbox key={item.id} name={"diving_locations"} value={item.id} label={item.name} total={item.count || 0} />
          ))}
        </div>
      )}
      {isFilterEnabled("diving_sites") && (
        <FilterSelect
          label={`What ${activitySlug === "snorkeling" ? "snorkel" : "dive"} site(s) do you want to visit?`}
          options={response.filters.diving_sites}
          name={"diving_sites"}
        />
      )}
      {isFilterEnabled("duration_in_hours") && (
        <div className="flex flex-col gap-1">
          <p className="font-bold">Preferred duration</p>
          {durationGroupKeys.map((key) => {
            const duration = durationGroups[key];
            return <FilterCheckbox key={key} name={"duration_in_hours"} value={key} label={duration.name} />;
          })}
        </div>
      )}
      {isFilterEnabled("diving_certificate_level") && (
        <div className="flex flex-col gap-1">
          <p className="font-bold">Select your level</p>
          {divingLevels.map((divingLevel) => (
            <FilterCheckbox
              key={divingLevel.id}
              name={"diving_certificate_level"}
              value={divingLevel.id}
              label={divingLevel.name}
              total={divingLevel.count || 0}
            />
          ))}
        </div>
      )}
      {isFilterEnabled("diving_certificate_organization") && (
        <div className="flex flex-col gap-1">
          <p className="font-bold">Diving organization</p>
          {response.filters.diving_organizations.map((item) => (
            <FilterCheckbox
              key={item.id}
              name={"diving_certificate_organization"}
              value={item.id}
              label={item.name}
              total={item.count || 0}
            />
          ))}
        </div>
      )}
      {isFilterEnabled("diving_courses") && (
        <FilterSelect label="Diving course" options={response.filters.diving_courses} name={"diving_courses"} />
      )}
      {isFilterEnabled("stay") && <BooleanFilter label="Accomodation" count={response.filters.stay} name={"stay"} />}
      {isFilterEnabled("pickup") && <BooleanFilter label="Hotel/hostel pickup" count={response.filters.pickup} name={"pickup"} />}
      {isFilterEnabled("gear_included") && <BooleanFilter label="Gear included" count={response.filters.gear} name={"gear_included"} />}
      {isFilterEnabled("language_code") && (
        <FilterSelect label={"Language preference"} options={response.filters.languages} name={"language_code"} />
      )}
      {isFilterEnabled("spot_id") && (
        <FilterSelect
          label="Dive center location"
          description="Got a preferred dive center location? Select it here"
          options={response.filters.spots}
          name={"spot_id"}
        />
      )}
    </Form>
  );
};

export default function Page() {
  const { params, debounce, state, isPending } = useSearchParams2();
  const activitySlug = activeActivitySlugs.find((type) => type === state.activity_slug);
  const activeFilters = getFilters(activitySlug);
  const transition = useNavigation();
  const isLoadingResults = transition.state === "loading" || isPending;
  const filterModal = useBoolean();
  const response = useLoaderData<typeof loader>();

  const divingCourseTag = params.get("diving_course_tag");
  const divingCourseTitle = useMemo(() => {
    const parsed = divingCourseTagKeyParser.safeParse(divingCourseTag);
    if (!parsed.success) return null;
    return divingCourseTitles[parsed.data];
  }, [divingCourseTag]);

  const filterTags: Record<FilterKey, (filterValues: Set<string>) => IdName[]> = {
    diving_certificate_level: (filterValues) => response.filters.divingLevels.filter((item) => filterValues.has(item.id)),
    diving_certificate_organization: (filterValues) => response.filters.diving_organizations.filter((item) => filterValues.has(item.id)),
    diving_courses: (filterValues) => response.filters.diving_courses.filter((item) => filterValues.has(item.id)),
    diving_locations: (filterValues) => response.filters.diving_locations.filter((item) => filterValues.has(item.id)),
    diving_sites: (filterValues) => response.filters.diving_sites.filter((item) => filterValues.has(item.id)),
    duration_in_hours: (filterValues) =>
      durationGroupKeys.filter((key) => filterValues.has(key)).map((key) => ({ id: key, ...durationGroups[key] })),
    gear_included: (filterValues) =>
      filterValues.has("yes")
        ? [{ id: "yes", name: "Gear included" }]
        : filterValues.has("")
          ? [
              {
                id: "",
                name: "Gear excluded",
              },
            ]
          : [],
    pickup: (filterValues) =>
      filterValues.has("yes")
        ? [{ id: "yes", name: "Pickup" }]
        : filterValues.has("")
          ? [
              {
                id: "",
                name: "No pickup",
              },
            ]
          : [],
    spot_id: (filterValues) => response.filters.spots.filter((item) => filterValues.has(item.id)),
    stay: (filterValues) =>
      filterValues.has("yes")
        ? [{ id: "yes", name: "stay" }]
        : filterValues.has("")
          ? [
              {
                id: "",
                name: "no stay",
              },
            ]
          : [],
    language_code: (filterValues) => languagesAsIdNames.filter((language) => filterValues.has(language.id)),
  };

  const paramsForClear = useMemo(() => {
    const newParams = new URLSearchParams(params);
    activeFilters.forEach((filterKey) => {
      newParams.delete(filterKey);
    });
    return newParams;
  }, [params]);

  return (
    <div>
      <div style={{ height: 250 }}>
        {/*<Suspense fallback={<div>loading map</div>}>*/}
        <MapBla />
        {/*</Suspense>*/}
      </div>
      <div className="app-container px-0 pt-3 pb-4">
        <div className="space-y-1 px-3 pb-1 md:pb-3">
          <div className="flex flex-row items-center gap-2">
            <h1 className="text-2xl font-bold line-clamp-1">
              {divingCourseTitle ? divingCourseTitle.title : activitySlug ? activities[activitySlug].name : t`Operators`}
            </h1>
            {/*<EditorLink />*/}
          </div>
          {!!divingCourseTitle && <p className="text-slate-600">{divingCourseTitle.description}</p>}
          <div className="hidden pt-3 pb-2 lg:block">
            <Divider />
          </div>
          {/*<hr className="my-3" />*/}
        </div>
        <div className="relative">
          <div className={"sticky top-0 z-10 bg-white px-3 py-2 lg:hidden"}>
            <button className="btn btn-primary w-full" onClick={filterModal.toggle}>
              <span>Filters</span>
              <BiFilter />
            </button>
          </div>
          <Transition appear show={filterModal.isOn} as={Fragment}>
            <Dialog as="div" onClose={filterModal.off}>
              <OverlayTransitionChild />
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="fixed inset-0 z-30 max-w-md overflow-y-auto bg-white">
                  <div className="sticky top-0 z-10 flex flex-row items-center justify-between bg-white px-6 pt-6">
                    <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                      Filters
                    </Dialog.Title>
                    <CloseButton onClick={filterModal.off} />
                  </div>
                  <div className="w-full max-w-md transform overflow-hidden bg-white p-6 text-left align-middle shadow-xl transition-all">
                    <Filters />
                  </div>
                  <div className="sticky bottom-0 z-10 bg-white p-6">
                    <Button loading={isLoadingResults} className="btn btn-primary w-full" onClick={filterModal.off}>
                      <span>Show results ({response.data.length})</span>
                    </Button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </Dialog>
          </Transition>
          <div className="relative flex flex-row">
            <div className={"hidden w-64 flex-col space-y-3 pl-3 lg:flex"}>
              <p className={"text-xl font-bold text-gray-800"}>Filter</p>
              <Filters />
            </div>
            <div className={"flex flex-1 flex-col space-y-3 px-0 md:px-3"}>
              <div className="flex flex-wrap gap-3 px-3 md:px-0">
                <GreyTag>{response.data.length} results</GreyTag>
                {activeFilters.map((filterKey) => {
                  const filterValues = params.getAll(filterKey);
                  const idNames = filterTags[filterKey](new Set(filterValues));
                  return idNames.map((option) => (
                    <GreyTag key={option.id}>
                      <span className="first-letter:capitalize">{option.name}</span>
                      <TagCloseButton
                        onClick={() => {
                          const url = createFilterUrl(filterKey, option.id, false, true);
                          debounce(url, 0, defaultNavOptions);
                          const forms = document.getElementsByClassName(exploreFiltersFormClass);
                          for (let form of forms) {
                            if (form instanceof HTMLFormElement) {
                              form.reset();
                            }
                          }
                        }}
                      />
                    </GreyTag>
                  ));
                })}
                {Array.from(params.keys()).length > 0 && (
                  <BaseLink
                    className="text-primary hover:underline active:underline"
                    preventScrollReset
                    to={{ pathname: "./", search: paramsForClear.toString() }}
                    onClick={() => {
                      const forms = document.getElementsByClassName(exploreFiltersFormClass);
                      for (let form of forms) {
                        if (form instanceof HTMLFormElement) {
                          form.reset();
                        }
                      }
                    }}
                  >
                    Clear
                  </BaseLink>
                )}
              </div>
              <div className="relative">
                {isLoadingResults && (
                  <div className="absolute inset-0 z-10 bg-white/30">
                    <div className="flex h-full w-full animate-pulse items-start justify-center bg-white/20">
                      <p className="border-1 sticky top-10 mt-20 rounded-xl border-slate-800 bg-white/30 p-3 text-slate-800 drop-shadow-md">
                        Loading...
                      </p>
                    </div>
                  </div>
                )}
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {response.data.map((operatorLocation) => {
                    return (
                      <div className="flex flex-col" key={operatorLocation.id}>
                        <div className="overflow-hidden">
                          <div>
                            <ParamLink
                              persistAllParams
                              path={_establishment_detail(operatorLocation.id)}
                              className="block w-full hover:opacity-80 active:opacity-80"
                            >
                              <EstablishmentItem item={operatorLocation} />
                            </ParamLink>
                          </div>
                          {activitySlug && (
                            <div className="flex h-full flex-col gap-3 px-3 md:px-0">
                              {operatorLocation.products?.map((product) => (
                                <ProductItem
                                  key={product.id}
                                  item={product}
                                  locale={null}
                                  link={{
                                    prefetch: "intent",
                                    path: _product_detail(product.id),
                                    state: "canGoBack",
                                  }}
                                  className="rounded-md border border-secondary-stroke"
                                />
                              ))}
                              {Number(operatorLocation.product_count) > productLimitPerOperator && (
                                <ParamLink
                                  className="flex flex-row items-center justify-between rounded-md border border-secondary-stroke bg-secondary-50 py-1 pl-3 pr-1 text-slate-700 hover:opacity-80 active:opacity-80"
                                  path={_establishment_detail(operatorLocation.id)}
                                  persistAllParams
                                >
                                  {Number(operatorLocation.product_count) - productLimitPerOperator} More{" "}
                                  <ChevronRightIcon className="h-6 w-6" />
                                </ParamLink>
                              )}
                            </div>
                          )}
                        </div>
                        <div className={"flex-1"} />
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
