import { Outlet, useLoaderD<PERSON>, useLocation } from "@remix-run/react";
import { createPageOverwrites } from "~/misc/consts";
import { DatePicker, MonthLink } from "~/routes/_all._catch.datepicker";
import React, { Fragment } from "react";
import { twMerge } from "tailwind-merge";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _planning, _planning_month, _planning_u } from "~/misc/paths";
import { format } from "date-fns";
import { TodayButton } from "~/domain/planning/shared-planning-components";
import { paramsToRecord, toggleArray } from "~/misc/parsers/global-state-parsers";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { notFound } from "~/misc/responses";
import { operatorQb } from "~/domain/operator/operator-queries";
import { MonthSwitch } from "~/domain/meta/datetime";
import { useAppContext } from "~/hooks/use-app-context";
import { useOptionalPlanningDayLoader } from "~/routes/_all._catch._w.planning._index";
import { LoaderFunctionArgs } from "@remix-run/router";
import { unique } from "remeda";
import { toUtc } from "~/misc/date-helpers";
import { activities, getActivitySlug } from "~/domain/activity/activity";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  const getOperator = async () => {
    if (!state.persist_operator_id) return null;
    const operator = await operatorQb(state).executeTakeFirst();
    if (!operator) throw notFound("Operator not found");
    return operator;
  };

  const operator = await getOperator();
  // const timezones = operator?.establishments.map((establishment) => getTimezone(establishment.timezone || ""));

  return {
    operator: operator,
    // timezone: timezones?.[0] || defaultTimezone,
    ...createPageOverwrites({ show_whatsapp: false }),
  };
};

const ViewSwitch = () => {
  const data = useLoaderData<typeof loader>();
  return (
    <div className="flex flex-row overflow-hidden rounded-md">
      <ParamLink
        preventScrollReset
        end
        path={data.operator ? _planning : _planning_u}
        className="aria-busy:spinner spinner-light bg-secondary-500 p-1 px-2 text-white opacity-50 hover:opacity-80 aria-current:opacity-100"
      >
        <span className="hidden @md:inline">Day</span>
        <span className="@md:hidden">D</span>
      </ParamLink>
      <ParamLink
        preventScrollReset
        end
        path={_planning_month}
        className="aria-busy:spinner spinner-light bg-secondary-500 p-1 px-2 text-white opacity-50 hover:opacity-80 aria-current:opacity-100"
      >
        <span className="hidden @md:inline">Month</span>
        <span className="@md:hidden">M</span>
      </ParamLink>
    </div>
  );
};

export const Filters = () => {
  const participantData = useOptionalPlanningDayLoader();
  const search = useSearchParams2();
  if (!participantData) return null;

  const activitySlugs = unique(participantData.allProducts.map((product) => product.activity_slug));

  return (
    <div className="space-y-3 pt-2">
      {participantData.trips.length > 0 && (
        <div className="space-y-3">
          <p>Filter by Trip:</p>
          <div className="flex flex-wrap gap-3">
            {participantData.trips.map((trip) => (
              <ParamLink
                key={trip.id}
                paramState={{
                  filter_id: participantData.selectedTrip === trip ? null : trip.id,
                }}
                aria-selected={participantData.selectedTrip === trip}
                className="px-2 py-1 border-slate-200 bg-slate-100 border rounded-md aria-selected:bg-primary aria-selected:text-white"
              >
                {trip.activity_location}
              </ParamLink>
            ))}
          </div>
        </div>
      )}

      {participantData.activityTitles.length > 0 && (
        <div className="space-y-3">
          <p>Filter by Activity:</p>
          <div className="flex flex-wrap gap-3">
            {participantData.activityTitles.map((title) => {
              const participations = participantData.allParticipations.filter((participation) => participation.activityTitle === title);
              const scheduledPaticipations = participations.filter((participation) => !!participation.trip_assignment);
              const unscheduledParticipations = participations.filter((participation) => !participation.trip_assignment);
              const participationsForOtherFilters = participations.filter(
                (participation) =>
                  !!search.state.filter_id &&
                  (participation.trip_assignment?.trip_id === search.state.filter_id ||
                    participation.product?.activity_slug === search.state.filter_id),
              );
              const allProductGroups = unique(participations.map((participation) => participation.product_panel_identifier));
              const scheduledProductGroups = unique(scheduledPaticipations.map((participation) => participation.product_panel_identifier));
              return (
                <ParamLink
                  key={title}
                  paramState={{
                    filter_id: search.state.filter_id === title ? null : title,
                    persist_toggle_activity_panel_id: search.state.filter_id !== title ? scheduledProductGroups : [],
                    // search.state.filter_id !== title && scheduledProductGroups.length === 1 && allProductGroups.length === 1
                    //   ? scheduledProductGroups
                    //   : [],
                  }}
                  aria-selected={search.state.filter_id === title}
                  className={twMerge(
                    "px-2 py-1 border-slate-200 bg-slate-100 border rounded-md aria-selected:bg-primary aria-selected:text-white",
                    unscheduledParticipations.length && "border-primary",
                    participationsForOtherFilters.length && "bg-slate-300",
                  )}
                >
                  {title}
                </ParamLink>
              );
            })}
          </div>
        </div>
      )}

      {activitySlugs.filter((slug) => getActivitySlug(slug)).length > 0 && (
        <div className="space-y-3">
          <p>Filter by Type:</p>
          <div className="flex flex-wrap gap-3">
            {activitySlugs.map((activitySlugStr) => {
              const activitySlug = getActivitySlug(activitySlugStr);
              if (!activitySlug) return <Fragment key={activitySlugStr || ""} />;
              const activity = activities[activitySlug];
              return (
                <ParamLink
                  key={activitySlugStr}
                  paramState={{
                    filter_id: search.state.filter_id === activitySlugStr ? null : activitySlugStr,
                  }}
                  aria-selected={search.state.filter_id === activitySlugStr}
                  className="px-2 py-1 border-slate-200 bg-slate-100 border rounded-md aria-selected:bg-primary aria-selected:text-white"
                >
                  {activity.name}
                </ParamLink>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default function Page() {
  const ctx = useAppContext();
  const data = useLoaderData<typeof loader>();
  const participantData = useOptionalPlanningDayLoader();
  const allParticipations = participantData?.allParticipations || [];
  const tobeScheduledParticipations = allParticipations.filter((participant) => participant.base_group === "3.To be scheduled");
  const tobeScheudlesCount = tobeScheduledParticipations.length;
  const directBookingIds = unique(
    tobeScheduledParticipations
      .filter((participation) => participation.booking?.direct_booking)
      .map((participation) => participation.booking?.id),
  );
  const search = useSearchParams2();
  const location = useLocation();
  const isMonthView = location.pathname.includes("/month");

  const operatorEstablishments = data.operator?.establishments || [];
  const firstEstablishment = operatorEstablishments[0];
  const formattedDate = format(toUtc(ctx.date.dateParam), "dd MMMM yyyy");

  const banner = (
    <Fragment>
      {!!tobeScheudlesCount && (
        <div className="px-1">
          <ParamLink
            paramState={{ filter_id: null }}
            onClick={() => {
              setTimeout(() => {
                // Scroll to the "To be scheduled" section
                const toBeScheduledElement = document.getElementById("divider3");
                if (toBeScheduledElement) {
                  toBeScheduledElement.scrollIntoView({
                    behavior: "smooth",
                    block: "start",
                  });
                }
              }, 200); // Increased timeout to allow DOM to update
            }}
            className="block max-lg bg-orange-200 rounded-md p-2 text-center text-slate-800"
          >
            {tobeScheudlesCount === 1 ? (
              <span>
                There is <span className="font-semibold">{tobeScheudlesCount}</span> unscheduled participant
              </span>
            ) : (
              <span>
                There are <span className="font-semibold">{tobeScheudlesCount}</span> unscheduled participants
              </span>
            )}
          </ParamLink>
        </div>
      )}
      {!!directBookingIds.length && (
        <div className="px-1 pt-1">
          <div className="max-lg bg-slate-500 rounded-md p-2 text-center text-white">
            {directBookingIds.length === 1 ? (
              <span>
                There is <span className="font-semibold">{directBookingIds.length}</span> direct booking
              </span>
            ) : (
              <span>
                There are <span className="font-semibold">{directBookingIds.length}</span> direct bookings
              </span>
            )}
          </div>
        </div>
      )}
    </Fragment>
  );

  const monthDate = toUtc(search.state.persist_month || ctx.date.monthParam);
  return (
    <main className="lg:px-3">
      <div
        className={twMerge(
          "lg:grid gap-x-5 flex flex-col",
          isMonthView ? "lg:grid-cols-[auto_minmax(0,1fr)]" : "lg:grid-cols-[auto_auto_minmax(0,1fr)]",
        )}
      >
        <div className="space-y-3 lg:max-w-[300px]">
          <div className="sticky top-0">
            {data.operator ? (
              <div className="flex flex-wrap gap-3 justify-between items-center max-lg:px-3">
                <h1 className=" text-2xl font-bold line-clamp-3">{data.operator.name}</h1>
                <p>
                  {operatorEstablishments.length > 1
                    ? "Enterprise"
                    : firstEstablishment?.spot_name + (firstEstablishment?.location_name ? " - " + firstEstablishment.location_name : "")}
                </p>
              </div>
            ) : (
              <div>
                <h1 className=" text-2xl font-bold line-clamp-3 max-lg:px-3">My schedule</h1>
              </div>
            )}
            <div className={twMerge("max-lg:px-3 space-y-3", isMonthView && "max-lg:hidden")}>
              <div className="@container flex flex-row justify-between items-center">
                <div className="flex flex-row">
                  <MonthLink add={-1} />
                  <span className="min-w-[150px] text-center">{format(monthDate, "MMMM yyyy")}</span>
                  <MonthLink add={1} />
                </div>
                <div className="flex flex-row gap-3 items-center">
                  <TodayButton params={{ persist_date: null, persist_month: null, filter_id: null }} />
                  <div className="lg:hidden">
                    <ViewSwitch />
                  </div>
                </div>
              </div>
              <div className={twMerge("", isMonthView ? "hidden lg:block" : "block")}>
                <DatePicker />
              </div>
            </div>
            {!isMonthView && (
              <div className="hidden lg:block pt-3">
                {banner}
                <Filters />
              </div>
            )}
          </div>
        </div>
        {!isMonthView && (
          <div className="lg:hidden sticky top-0  z-10 pb-3">
            <div className="flex justify-center py-2 bg-white">
              <p className={"justify-start bg-white rounded-md px-1"}>{formattedDate}</p>
            </div>
            {banner}
          </div>
        )}
        {!isMonthView && <div className="w-2 bg-secondary-50"></div>}
        <div className="flex-1">
          <div className="flex flex-row gap-3">
            {operatorEstablishments.length > 1 &&
              operatorEstablishments.map((establishment) => {
                const isToggled = search.state.persist_toggle_establishment_ids.includes(establishment.id);
                // const isLoading = navigation.location?.search.includes(`toggle_establishment_ids=${establishment.id}`)
                return (
                  <ParamLink
                    preventScrollReset
                    log
                    exact
                    key={establishment.id}
                    aria-selected={!isToggled}
                    paramState={{
                      persist_toggle_establishment_ids: toggleArray(search.state.persist_toggle_establishment_ids, establishment.id),
                    }}
                    className="p-3 rounded-md bg-secondary-50 flex flex-row gap-2 items-center aria-busy:spinner spinner-light"
                  >
                    <p>{establishment.location_name || establishment.spot_name || "no spot"}</p>
                    <input key={isToggled + ""} type={"checkbox"} checked={!isToggled} className="checkbox" />
                  </ParamLink>
                );
              })}
          </div>
          <div
            className={twMerge("@container max-lg:px-3 flex flex-row items-center justify-between gap-3", !isMonthView && "hidden lg:flex")}
          >
            {isMonthView ? (
              <MonthSwitch />
            ) : (
              <div>
                <span className="min-w-[150px] text-center font-semibold text-xl">{formattedDate}</span>
              </div>
            )}
            <div className="flex flex-row gap-3 items-center">
              <div className={twMerge("hidden", isMonthView && "max-lg:block")}>
                <TodayButton />
              </div>
              {/*<div className={twMerge("flex-1", isMonthView && "hidden lg:block")}>*/}
              {/*  <h2 className="p-3 text-center">{formatInTimeZone(selectedDate, defaultTimezone, "dd MMMM yyyy")}</h2>*/}
              {/*</div>*/}
              <ViewSwitch />
            </div>
          </div>
          <Outlet />
        </div>
      </div>
    </main>
  );
}
