import { DataFunctionArgs } from "@remix-run/server-runtime";
import { getSessionSimple } from "~/utils/session.server";
import { useLoaderData } from "@remix-run/react";
import React from "react";
import { ikUrl } from "~/components/IkImage";
import { EditorRequired } from "~/components/account/AccountContainer";
import { activities, getActivityImage } from "~/domain/activity/activity";
import { toArray } from "~/misc/helpers";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import { kysely } from "~/misc/database.server";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: DataFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);
  if (!session_id) return null;

  const user = await activeUserSessionSimple(kysely, session_id, true).select("_user.editor").executeTakeFirst();
  if (user?.editor) return true;

  return null;
};

export default function Page() {
  const response = useLoaderData<typeof loader>();

  if (!response) return <EditorRequired />;

  return (
    <div className="app-container py-3 ">
      <div className="flex flex-row items-center gap-3">
        <h1 className="text-2xl font-bold">Activities</h1>
      </div>
      <div className="space-y-2 pt-1">
        <div className="grid grid-cols-2 gap-3 md:grid-cols-3">
          {toArray(activities).map((item) => (
            <div className="overflow-hidden rounded-xl border bg-secondary-50 hover:opacity-80 active:opacity-80" key={item.key}>
              <img
                alt={item.name}
                src={ikUrl(getActivityImage(item.key), `tr:w-540,h-400`)}
                className="h-[100px] w-full object-cover md:h-[150px]"
              />
              <p className="max-w-[350px] p-2 text-center text-xs font-bold line-clamp-1 md:p-3 md:text-sm">{item.name}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
