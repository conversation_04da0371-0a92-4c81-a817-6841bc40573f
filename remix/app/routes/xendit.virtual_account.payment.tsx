import { kysely } from "~/misc/database.server";
import { unauthorized } from "~/misc/responses";
import { ActionFunctionArgs } from "@remix-run/node";
import { LoaderFunctionArgs } from "@remix-run/router";

export const action = async (args: ActionFunctionArgs) => {
  const paymentData = await args.request.json();
  const ownerId = paymentData.owner_id;
  const virtualBankAccountId = paymentData.callback_virtual_account_id;
  const callbackToken = args.request.headers.get("x-callback-token");

  console.log("bankaccount", paymentData);

  if (!callbackToken || !virtualBankAccountId || !ownerId) throw unauthorized();

  return kysely.transaction().execute(async (trx) => {
    const xenditVirtualAccountRecord = await trx
      .selectFrom("xendit_virtual_bank_account")
      .innerJoin("xendit_account", "xendit_account.id", "xendit_virtual_bank_account.xendit_account_id")
      .where("xendit_account.xendit_user_id", "=", ownerId)
      .where("xendit_virtual_bank_account.xendit_virtual_bank_account_id", "=", virtualBankAccountId)
      // .where("payment.xendit_invoice_id", "=", xenditInvoiceId)
      // .where("xendit_account.xendit_user_id", "=", xenditUserId)
      .where("xendit_account.xendit_invoice_callback_token", "=", callbackToken)
      .selectAll("xendit_virtual_bank_account")
      .executeTakeFirstOrThrow();

    await trx
      .insertInto("xendit_virtual_bank_account_payment")
      .values({
        xendit_virtual_bank_account_id: xenditVirtualAccountRecord.id,
        xendit_payment_id: paymentData.id,
        obj: paymentData,
      })
      .executeTakeFirstOrThrow();

    return { success: true };
  });
};

export const loader = async (args: LoaderFunctionArgs) => {
  return { success: true };
};
