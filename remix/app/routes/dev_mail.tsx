import type { DataFunctionArgs } from "@remix-run/server-runtime";
import { useLoaderData } from "@remix-run/react";
import { mockedEmails } from "~/server/mail/email.client.server";

export const loader = async ({ request, params }: DataFunctionArgs) => {
  return {
    emails: mockedEmails.length,
  };
};

export default function Page() {
  const data = useLoaderData<typeof loader>();
  return <div>emails</div>;
}
