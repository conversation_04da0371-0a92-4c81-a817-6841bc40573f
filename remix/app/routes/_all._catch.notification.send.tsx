import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { formatDate, formatDatetime, lower } from "~/kysely/kysely-helpers";
import { ActionForm } from "~/components/form/BaseFrom";
import { SubmitButton } from "~/components/base/Button";
import { RInput } from "~/components/ResourceInputs";
import { emailCancelledMsg } from "~/domain/email/email";
import { LoaderFunctionArgs } from "@remix-run/router";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { pageLimits, Paging } from "~/components/Paging";
import { isEditorQb, memberIsAdminQb, toArgs } from "~/domain/member/member-queries.server";
import { getSessionSimple } from "~/utils/session.server";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _booking_detail, _participant_detail } from "~/misc/paths";
import { useAppContext } from "~/hooks/use-app-context";
import { Debug<PERSON>ontainer, IfDebug } from "~/components/debug";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);
  const { session_id } = await getSessionSimple(request);
  const pageLimit = pageLimits.find((limit) => limit === state.page_limit) || pageLimits[0];

  const baseQb = kysely
    .selectFrom("mail")
    .innerJoin("participant", "participant.id", "mail.participant_id")
    .innerJoin("sale_item", "sale_item.id", "mail.sale_item_id")
    .innerJoin("booking", "booking.id", "sale_item.booking_id")
    .innerJoin("establishment", "establishment.id", "booking.establishment_id")
    .innerJoin("spot", "spot.id", "establishment.spot_id")
    .innerJoin("region", "region.id", "spot.region_id")
    .where((eb) => {
      return state.persist_establishment_id
        ? eb.and([
            eb("establishment.id", "=", state.persist_establishment_id),
            eb("establishment.id", "in", memberIsAdminQb(toArgs(kysely, session_id), "write").select("_member.establishment_id")),
          ])
        : eb.exists(isEditorQb(toArgs(kysely, session_id)).select("_user.editor"));
    });

  const mailsQb = await baseQb
    .selectAll("mail")
    .select((eb) => [
      "sale_item.booking_id",
      formatDate(lower(eb.ref("sale_item.duration")), "DD Mon YYYY").as("activity_start_date_formatted"),
      "region.timezone",
      formatDatetime(eb.ref("mail.created_at"), "DD Mon YYYY, HH24:MI", eb.ref("region.timezone")).as("local_created_at_formatted"),
      formatDatetime(eb.ref("mail.created_at"), "DD Mon YYYY, HH24:MI", eb.val("UTC")).as("utc_created_at_formatted"),
    ])
    .offset(state.page_nr * pageLimit)
    .limit(pageLimit)
    .execute();

  const countResult = await baseQb
    .clearSelect()
    .clearOrderBy()
    .clearWhere()
    .select((eb) => eb.fn.count("mail.id").as("count"))
    .executeTakeFirst();
  const totalCount = Number(countResult?.count || 0);

  return {
    mails: mailsQb,
    totalCount,
    pageLimit,
  };
};

export default function SentNotifications() {
  const ctx = useAppContext();
  const { mails, totalCount, pageLimit } = useLoaderData<typeof loader>();

  return (
    <div className="space-y-4">
      <div className="overflow-auto">
        <div style={{ gridTemplateColumns: "repeat(6, minmax(0, auto))" }} className="grid gap-4 text-sm min-w-max">
          <div className="font-semibold contents">
            <div className="whitespace-nowrap">Type</div>
            <div className="whitespace-nowrap">Status</div>
            <div className="whitespace-nowrap">Sent at</div>
            <div className="whitespace-nowrap">Name</div>
            <div className="whitespace-nowrap">Email</div>
            <div className="whitespace-nowrap">Activity Start Date</div>
          </div>
          {mails.map((mail) => (
            <div key={mail.id} className="contents">
              <div className="whitespace-nowrap">
                Activity Reminder
                <IfDebug>
                  <br />#{mail.id}
                </IfDebug>
              </div>
              <div className="whitespace-nowrap">
                {mail.success ? (
                  "Success"
                ) : mail.msg === emailCancelledMsg ? (
                  <div>Cancelled</div>
                ) : (
                  <div className="flex flex-row gap-3">
                    Failed
                    <ActionForm>
                      <RInput table={"mail"} field={"data.participant_id"} value={mail?.participant_id || ""} type={"hidden"} />
                      <RInput table={"mail"} field={"data.sale_item_id"} value={mail?.sale_item_id || ""} type={"hidden"} />
                      <RInput table={"mail"} field={"data.to_email"} value={mail.to_email} type={"hidden"} />
                      <RInput table={"mail"} field={"data.success"} hiddenType={"__boolean__"} type={"hidden"} value={"true"} />
                      <SubmitButton className="link">Send again</SubmitButton>
                    </ActionForm>
                  </div>
                )}
              </div>
              <div className="whitespace-nowrap">
                {mail.local_created_at_formatted} ({mail.timezone})<br />
                {ctx.editor && <span>{mail.utc_created_at_formatted} (UTC)</span>}
              </div>
              <div className="whitespace-nowrap">
                {mail.participant_id ? (
                  <ParamLink path={_participant_detail(mail.participant_id)} className="link">
                    {mail.to_name}
                  </ParamLink>
                ) : (
                  <span className="line-through">{mail.to_name}</span>
                )}
              </div>
              <div className="whitespace-nowrap">{mail.to_email}</div>
              <div className="whitespace-nowrap">
                <ParamLink className="link" path={_booking_detail(mail.booking_id)}>
                  {mail.activity_start_date_formatted}
                </ParamLink>
              </div>
            </div>
          ))}
        </div>
      </div>

      <Paging totalCount={totalCount} pageLimit={pageLimit} />
    </div>
  );
}
