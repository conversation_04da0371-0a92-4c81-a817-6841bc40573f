import React from "react";
import { LoaderFunctionArgs } from "@remix-run/router";
import { createPageOverwrites } from "~/misc/consts";
import { createXenditPaymentUpdateData } from "~/domain/payment/payment-resource.server";
import { kysely } from "~/misc/database.server";
import { isEmpty } from "remeda";

export const loader = async (args: LoaderFunctionArgs) => {
  const paymentId = args.params.payment_id!;

  const updateData = await createXenditPaymentUpdateData(kysely, paymentId);
  console.log("paymentid", paymentId, updateData);
  if (!isEmpty(updateData)) {
    console.log("update payment");
    await kysely.updateTable("payment").where("payment.id", "=", paymentId).set(updateData).executeTakeFirstOrThrow();
  }

  return {
    ...createPageOverwrites({}),
  };
};

export default function Page() {
  return (
    <div className="app-container">
      <div className="text-center py-6 text-slate-700 text-xl">Payment success</div>
    </div>
  );
}
