import { useLoaderD<PERSON>, useParams } from "@remix-run/react";
import { RatingInput } from "~/components/field/rating/rating-input";
import { getEstablishmentShort } from "~/domain/establishment/helpers";
import React, { Fragment, useId } from "react";
import { _establishment_detail } from "~/misc/paths";
import { InputFilesDefault } from "~/components/form/FilesInput";
import { FeedbackPointsInput } from "~/domain/review/review-components";
import { activeActivitySlugs, activities } from "~/domain/activity/activity";
import { RegisterEmailInput, RegisterNameInput, RegisterNameLabel } from "~/domain/user/UserRegisterFields";
import { RInput, RLabel, RTextarea, toInputId } from "~/components/ResourceInputs";
import { fName, tableIdRef } from "~/misc/helpers";
import { SubmitButton } from "~/components/base/Button";
import { ActionAlert } from "~/components/ActionAlert";
import { ActionForm } from "~/components/form/BaseFrom";
import { useAppContext } from "~/hooks/use-app-context";
import { LoaderFunctionArgs } from "@remix-run/router";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { AuthzMethod } from "~/domain/user_session/user_session";
import { ParamLink } from "~/components/meta/CustomComponents";

export { action } from "~/routes/_all._catch.resource";

const multiAnserHelpText = "Multiple answers can be selected.";

export const loader = (args: LoaderFunctionArgs) => {
  const id = args.params.id!;
  return simpleEstablishmentQb.where("establishment.id", "=", id).executeTakeFirstOrThrow();
};

export default function Page() {
  const params = useParams();
  const context = useAppContext();
  const establishmentId = params.id!;
  const formId = useId();
  const data = useLoaderData<typeof loader>();

  return (
    <ActionForm replace className="pt-6" identifier={formId}>
      <ActionAlert />
      <RedirectParamsInput path={_establishment_detail(establishmentId) + "#reviews"} paramState={{}} />
      <RInput table={"review"} field={"data.establishment_id"} type={"hidden"} value={establishmentId} />
      <div className="app-container">
        <h2 id="create-review" className="text-xl font-semibold">
          Write your review
        </h2>
        <div className="space-y-12 pt-6">
          <div className="flex flex-col gap-6 md:flex-row">
            <div className="space-y-2 md:w-1/2">
              <p className="required">I joined this activity:</p>
              <div className="flex flex-wrap gap-3">
                {activeActivitySlugs.map((slug) => (
                  <div key={slug} className="relative">
                    <RInput
                      table={"review"}
                      field={"data.activity_slug"}
                      required
                      type={"radio"}
                      id={slug}
                      className="peer absolute bottom-0 left-1/2 opacity-0"
                      value={slug}
                    />
                    <RLabel
                      table={"review"}
                      field={"data.activity_slug"}
                      htmlFor={slug}
                      className={`relative block cursor-pointer rounded-full border border-primary bg-primary bg-opacity-10 py-2 px-3
               text-primary-700 hover:bg-opacity-20 active:bg-opacity-20 peer-checked:cursor-default peer-checked:bg-opacity-100 peer-checked:text-white`}
                    >
                      {activities[slug].name}
                    </RLabel>
                  </div>
                ))}
              </div>
            </div>
            <div className="space-y-2 md:w-1/2">
              <p className="required">
                My overall experience with <strong>{getEstablishmentShort(data)}</strong>:
              </p>
              <RatingInput name={fName("review", "data.experience_rating")} required />
              <p className="text-xs text-slate-600">From poor (1 star) to excellent (5 stars)</p>
            </div>
          </div>
          <div className="flex flex-col gap-6 md:flex-row">
            <div className="md:w-1/2">
              <FeedbackPointsInput type={"plus"} />
              <p className="pl-1 pt-2 text-xs text-slate-500 max-md:hidden">{multiAnserHelpText}</p>
            </div>
            <div className="md:w-1/2">
              <FeedbackPointsInput type={"min"} />
              <p className="pl-1 pt-2  text-xs text-slate-500">{multiAnserHelpText}</p>
            </div>
          </div>
          <div>
            <div className="space-y-2">
              <RLabel table={"review"} field={"data.experience"}>
                Tell us more about your experience
              </RLabel>
              <RTextarea table={"review"} field={"data.experience"} className="input" />
            </div>
            <div className="space-y-2">
              <label>Photos</label>
              <InputFilesDefault target={"review"} target_id={tableIdRef("review")} multiple />
            </div>
          </div>
          <div className="flex flex-col gap-y-3 gap-x-6 md:flex-row">
            <div className="space-y-2 md:w-1/2">
              <div className="required">
                {!context.user_session_id && (
                  <Fragment>
                    <RInput table={"user_session"} field={"data.user_id"} value={tableIdRef("user")} type={"hidden"} />
                    <RInput table={"session"} field={"data.selected_user_id"} value={tableIdRef("user")} type={"hidden"} />
                    <RInput table={"user_session"} field={"data.method"} value={"otp" satisfies AuthzMethod} type={"hidden"} />
                    <RInput table={"one_time_password"} field={"data.user_id"} value={tableIdRef("user")} type={"hidden"} />
                  </Fragment>
                )}
                <label htmlFor={toInputId(fName("user", "data.email"))}>Email</label>
                {!context.email && (
                  <span>
                    &nbsp;or&nbsp;
                    <ParamLink paramState={{ toggle_modal: "account" }} className="link">
                      Login / Register
                    </ParamLink>
                  </span>
                )}
              </div>
              <RegisterEmailInput />
            </div>
            <div className="space-y-2 md:w-1/2">
              <RegisterNameLabel />
              <RegisterNameInput />
            </div>
          </div>
        </div>
      </div>
      <div className="sticky bottom-0 z-10 bg-white py-4">
        <div className="app-container flex flex-row items-center justify-end gap-2">
          <ParamLink path={_establishment_detail(establishmentId)} prefetch={"render"} className="btn hover:underline">
            cancel
          </ParamLink>
          <SubmitButton className="btn btn-primary">Submit</SubmitButton>
        </div>
      </div>
    </ActionForm>
  );
}
