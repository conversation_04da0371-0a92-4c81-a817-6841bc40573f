import { useNavigation } from "@remix-run/react";
import { Backbutton } from "~/components/base/base";
import { Button } from "~/components/base/Button";
import { _diving_site } from "~/misc/paths";
import React from "react";
import { InputFilesDefault } from "~/components/form/FilesInput";
import { EditorRequired } from "~/components/account/AccountContainer";
import type { DivingSite } from "~/kysely/db";
import { RangeInput } from "~/components/form/RangeInput";
import { MapPortal } from "~/components/MapPortal";
import { accessChecks, difficultyLevels, ranges, tags, types } from "~/domain/diving-site/diving-site";
import { entries, fName, keys, toArray } from "~/misc/helpers";
import { RedirectInput } from "~/components/form/DefaultInput";
import { DefaultFieldInputGeom } from "~/components/form/DefaultFieldInputGeom";
import { useDivingLocationAndSites } from "~/routes/_all._catch.diving-location.$diving_location_id";
import { RInput, RLabel, RTextarea, toInputId } from "~/components/ResourceInputs";
import { ActionForm } from "~/components/form/BaseFrom";

export { ErrorBoundary } from "~/components/RoutDefaults";

const labels: Partial<Record<keyof DivingSite, string>> = {
  access_via_boat: "boat",
  access_via_shore: "shore",
  description: "info",
  sources: "sources (internal)",
  difficulty_info: "Additional info about the difficulty",
};

export { action } from "~/routes/_all._catch.resource";

const TextField = (props: { name: keyof DivingSite; data: Partial<Record<keyof DivingSite, unknown>>; placeholder?: string }) => {
  const inputName = fName("diving_site", `data.${props.name}`);
  const inputId = toInputId(inputName);
  return (
    <div className="space-y-1">
      <label htmlFor={inputId} className="first-letter:capitalize">
        {labels[props.name] || props.name}
      </label>
      <textarea
        className="input min-h-[200px]"
        id={inputId}
        name={inputName}
        // onChange={(e) => props.data.merge({ [props.name]: e.target.value || null })}
        defaultValue={(props.data[props.name] as string) || ""}
        placeholder={props.placeholder}
      />
    </div>
  );
};

export default function Page() {
  const { site } = useDivingLocationAndSites();
  const transition = useNavigation();
  const isSaving = !!transition.formData;

  if (!site) return <EditorRequired />;

  return (
    <div className="app-container">
      <ActionForm replace className="space-y-6">
        <RInput table={"diving_site"} field={"id"} value={site.id} />
        <RedirectInput value={_diving_site({ id: site.id, diving_location_id: site.diving_location_id })} />
        <InputFilesDefault target="diving_site" target_id={site.id} defaultValue={site.files} />
        <div className="space-y-1">
          <RLabel table={"diving_site"} field={"data.name"}>
            Name
          </RLabel>
          <RInput table={"diving_site"} field={"data.name"} className="input" disabled={isSaving} defaultValue={site.name} />
        </div>
        <div className="space-y-1">
          <RLabel table={"diving_site"} field={"data.headline"}>
            Headline (hero image)
          </RLabel>
          <RInput table={"diving_site"} field={"data.headline"} className="input" defaultValue={site.headline || ""} disabled={isSaving} />
        </div>
        <div className="space-y-1">
          <RLabel table={"diving_site"} field={"data.summary"}>
            Summary (used when showing in list)
          </RLabel>
          <RTextarea
            table={"diving_site"}
            field={"data.summary"}
            cols={2}
            className="input"
            defaultValue={site.summary || ""}
            disabled={isSaving}
          />
        </div>
        <div className="space-y-3">
          <RLabel table={"diving_site"} field={"data.difficulty_level"}>
            Difficulty
          </RLabel>
          <div className="flex flex-row">
            {difficultyLevels.map((difficulty, index) => {
              const id = "difficultie" + index;

              return (
                <div key={index}>
                  <RInput
                    id={id}
                    field="data.difficulty_level"
                    table={"diving_site"}
                    className="peer hidden"
                    type="radio"
                    value={index + 1}
                    defaultChecked={index + 1 === site.difficulty_level}
                  />
                  <label
                    htmlFor={id}
                    className={`relative block p-3 text-white  transition-all
                 hover:z-20 hover:scale-150 hover:rounded-xl hover:ring-1
                 ${index === 0 ? "rounded-l-md" : ""}
                 ${index + 1 === difficultyLevels.length ? "rounded-r-md" : ""}
                 hover:ring-white active:z-20 active:scale-150
                 active:rounded-xl active:ring-1 active:ring-white
                   peer-checked:z-10 peer-checked:scale-125 peer-checked:rounded-xl peer-checked:ring-1 peer-checked:ring-white`}
                    style={{ backgroundColor: difficulty.color }}
                  >
                    {index + 1}
                  </label>
                </div>
              );
            })}
          </div>
        </div>
        <TextField data={site} name={"difficulty_info"} />
        <div className="flex w-fit flex-wrap items-center gap-5 rounded-md border border-slate-300 p-3">
          <span>Acces via</span>
          {entries(accessChecks).map(([key, value]) => (
            <label key={key} className="flex w-fit flex-row items-center gap-1">
              <RInput
                table={"diving_site"}
                type="checkbox"
                field={`data.${key}`}
                hiddenType={"__boolean__"}
                defaultChecked={!!site[key]}
                className="checkbox"
              />
              <span className="capitalize">{value.label}</span>
            </label>
          ))}
        </div>
        <div>
          <span>Type</span>
          <div className="mt-1 flex w-fit flex-wrap items-center gap-5 rounded-md border border-slate-300 p-3">
            {toArray(types).map((type) => (
              <label key={type.key} className="flex w-fit flex-row items-center gap-1">
                <RInput
                  hiddenType={"__boolean__"}
                  table={"diving_site"}
                  type="checkbox"
                  field={`data.${type.key}`}
                  defaultChecked={!!site[type.key]}
                  className="checkbox"
                />
                <span className="first-letter:capitalize">{type.key}</span>
              </label>
            ))}
          </div>
        </div>
        <div>
          <span>Tags</span>
          <div className="mt-1 flex w-fit flex-wrap items-center gap-5 rounded-md border border-slate-300 p-3">
            {entries(tags).map(([name, label]) => (
              <label key={name} className="flex w-fit flex-row items-center gap-1">
                <RInput
                  table={"diving_site"}
                  type="checkbox"
                  hiddenType={"__boolean__"}
                  field={`data.${name}`}
                  defaultChecked={!!site[name]}
                  className="checkbox"
                />
                <span className="first-letter:capitalize">{label || name}</span>
              </label>
            ))}
          </div>
        </div>
        <div className="grid items-center gap-6 md:grid-cols-2">
          {keys(ranges).map((rangeKey) => {
            const range = ranges[rangeKey];
            return <RangeInput label={range.label} key={rangeKey} data={site} name={rangeKey} className="input" disabled={isSaving} />;
          })}
        </div>
        <div className="flex h-[300px] flex-col">
          <MapPortal>
            <div className="absolute bottom-0 left-0 right-0 z-10 gap-3">
              <div className="p-3">
                <DefaultFieldInputGeom defaultValue={site.geom} name={fName("diving_site", "data.geom")} />
              </div>
            </div>
          </MapPortal>
        </div>
        <TextField data={site} name={"description"} />
        <TextField
          data={site}
          name={"highlights"}
          placeholder={`a list of highlights, e.g.:
sites for all level divers
healthy corals
great wreck diving
      `}
        />
        <TextField data={site} name={"sources"} />
        <div className="sticky bottom-0 z-10 flex flex-row items-center justify-end bg-white py-3">
          <Backbutton className="btn hover:underline">Cancel</Backbutton>
          <Button loading={isSaving} className="btn btn-primary">
            Save
          </Button>
        </div>
      </ActionForm>
    </div>
  );
}
