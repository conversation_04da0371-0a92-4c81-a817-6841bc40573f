import { getSessionSimple } from "~/utils/session.server";
import { kysely } from "~/misc/database.server";
import { useLoaderData, useNavigation } from "@remix-run/react";
import React, { forwardRef, Fragment, useId, useRef, useState } from "react";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { EditorRequired } from "~/components/account/AccountContainer";
import { fName } from "~/misc/helpers";
import { AnimatingTableBody } from "~/components/base/base";
import { ParamLink } from "~/components/meta/CustomComponents";
import { weekDays } from "~/misc/vars";
import { Button, SubmitButton } from "~/components/base/Button";
import { RInput, RLabel } from "~/components/ResourceInputs";
import { HiddenTypeInput, OperationInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { ActionAlert } from "~/components/ActionAlert";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { ActionForm } from "~/components/form/BaseFrom";
import { sql } from "kysely";
import { activeUserSessionQb, memberIsAdminOrOwnerQb } from "~/domain/member/member-queries.server";
import { establishmentQb } from "~/domain/establishment/queries";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import type { CalendarItemArgs } from "~/domain/calendar/Calendar";
import { Calendar, CalendarItems, getFromTo, getIsSelected, useCalendarContext } from "~/domain/calendar/Calendar";
import { getMonthObj } from "~/domain/planning/plannings-helpers";
import { getDateFromParams } from "~/domain/planning/planning.helpers.server";
import { twMerge } from "tailwind-merge";
import { format } from "date-fns";
import { MonthSwitch } from "~/domain/meta/datetime";
import { tripCalendarQb } from "~/domain/trip/trip.server";
import { getTripTotals } from "~/domain/trip/trip-components";
import { activities, getActivity } from "~/domain/activity/activity";
import { useDrag } from "@use-gesture/react";
import { ArrowsPointingOutIcon } from "@heroicons/react/20/solid";
import { range } from "~/domain/schedule/HiddenRangeInput";
import { paramsToRecord, removeFromArray, toggleArray } from "~/misc/parsers/global-state-parsers";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { LoaderFunctionArgs } from "@remix-run/router";
import { arrayAgg, ascNullsFirst, dateRange, generateDateSeries } from "~/kysely/kysely-helpers";

export { action } from "~/routes/_all._catch.resource";

// const monthDates =

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const ctx = await getSessionSimple(request);
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);
  const memberId = params.member_id!;
  const date = getDateFromParams(request, true);
  const month = getMonthObj(date.dateParam);

  const daysOfMonthQb = kysely.selectFrom(generateDateSeries(month.firstDateInCalender, month.lastDateInCalender).as("series"));

  const member = await kysely
    .selectFrom("member")
    .leftJoin("user", (eb) => eb.onRef("member.user_id", "=", "user.id").on("user.deleted_at", "=", at_infinity_value))
    .where("member.id", "=", memberId)
    .where((eb) => {
      const args = { trx: kysely, ctx: ctx };
      return eb.or([
        eb("member.establishment_id", "in", memberIsAdminOrOwnerQb(args, "read").select("_member.establishment_id")),
        eb("member.user_id", "=", activeUserSessionQb(args, true).select("_user.id")),
        // eb.exists(activeUserSessionQb(args, true).where("_user.editor", "=", true)),
      ]);
    })
    .selectAll("member")
    .select((eb) => [
      "user.email",
      jsonObjectFrom(establishmentQb.where("establishment.id", "=", eb.ref("member.establishment_id"))).as("establishment"),
      jsonArrayFrom(daysOfMonthQb.selectAll("series").select(sql.raw("extract(isodow from series.date::date) - 1").as("day_of_week"))).as(
        "days",
      ),
      jsonArrayFrom(
        eb
          .selectFrom("schedule")
          .selectAll("schedule")
          .select((eb) => [
            sql<string | null>`((lower (${eb.ref("schedule.range")})):: date)`.as("from"),
            sql<string | null>`((upper (${eb.ref("schedule.range")})):: date - 1)`.as("to"),
            daysOfMonthQb
              .where((seriesEb) =>
                seriesEb(eb.ref("schedule.range"), "&&", dateRange(seriesEb.ref("series.date"), seriesEb.ref("series.date"))),
              )
              // .where('sch')
              .select((eb) => arrayAgg(eb.ref("series.date"), "date").as("date"))
              .as("days"),
          ])
          .where("schedule.range", "&&", dateRange(sql.lit(month.firstDateInCalender), sql.lit(month.lastDateInCalender)))
          .orderBy("schedule.created_at", ascNullsFirst)
          .orderBy("schedule.available", "desc")
          .orderBy("schedule.range", "asc")
          // .orderBy("schedule.created_at", sql`desc nulls last`)
          // .orderBy("schedule.available", "asc")
          // .orderBy("schedule.range", "desc")
          .whereRef("schedule.target_id", "=", "member.id"),
      ).as("schedules"),
      jsonArrayFrom(
        tripCalendarQb
          .where(
            "trip.id",
            "in",
            eb
              .selectFrom("trip_assignment")
              .leftJoin("participation", "participation.id", "trip_assignment.participation_id")
              .leftJoin("sale_item", "sale_item.id", "participation.sale_item_id")
              .leftJoin("booking", "booking.id", "sale_item.booking_id")
              .select("trip_assignment.trip_id")
              .where("booking.cancelled_at", "is", null)
              .where("trip_assignment.member_id", "=", memberId),
          )
          .where("trip.date", ">=", month.firstDateInCalender)
          .where("trip.date", "<=", month.lastDateInCalender),
      ).as("trips"),
    ])
    .executeTakeFirstOrThrow();

  return {
    member_id: memberId,
    member: member,
  };
};

const editKey = "edit";
const removeKey = "remove";

export const ScheduleDialog = forwardRef<
  HTMLDialogElement,
  {
    default: { from: string; to: string; available?: boolean };
    // onClose: () => void;
  }
>((props, ref) => {
  const id = useId();
  const response = useLoaderData<typeof loader>();

  return (
    <dialog
      ref={ref}
      id={id}
      onClick={(e) => {
        e.currentTarget.close();
      }}
    >
      <ActionForm
        key={props.default?.from + "" + props.default?.to + ""}
        className="p-6 space-y-6 rounded-md"
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <RedirectParamsInput path={"./"} paramState={{ element_action: [id] }} />
        <div className="w-full">Schedule</div>
        <RInput table={"schedule"} field={"data.target_id"} value={response.member.id} type={"hidden"} />
        {range.map((rangeFieldKey) => {
          return (
            <div key={rangeFieldKey}>
              <input
                required={rangeFieldKey === "from"}
                type="date"
                defaultValue={props.default?.[rangeFieldKey] || ""}
                className="input"
                name={fName("schedule", "data.range", 0, rangeFieldKey)}
              />
            </div>
          );
        })}
        <HiddenTypeInput name={fName("schedule", "data.range")} value={"__pg_daterange__"} />
        <p>Available:</p>
        <label className="text-green-500">
          <RInput
            required
            hiddenType={"__boolean__"}
            type="radio"
            className="accent-green-500"
            table={"schedule"}
            defaultChecked={props.default.available}
            field={"data.available"}
            value={"yes"}
          />
          &nbsp;yes
        </label>
        &nbsp; &nbsp;
        <label className="text-red-500">
          <RInput
            required
            table={"schedule"}
            field={"data.available"}
            type="radio"
            value={"no"}
            defaultChecked={props.default.available !== undefined ? !props.default.available : undefined}
          />
          &nbsp;no
        </label>
        <div>
          <SubmitButton className="btn btn-primary">Save</SubmitButton>
        </div>
      </ActionForm>
    </dialog>
  );
});

const CalendarItem = (props: { args: CalendarItemArgs }) => {
  const response = useLoaderData<typeof loader>();
  const calendarCtx = useCalendarContext();
  const dialogRef = useRef<HTMLDialogElement>(null);
  const [defaultValue, setDefaultValue] = useState<{ from: string; to: string; available?: boolean }>(initFromTo);

  const args = props.args;
  const divRef = useRef<HTMLDivElement>(null);
  const foundDate = response.member.days.find((date) => date.date === args.dateStr);
  const schedules =
    response.member.schedules
      ?.filter((schedule) => schedule.days?.includes(args.dateStr))
      .filter((schedule) => {
        const daysOfWeek = (schedule.days_of_week || []).filter((day) => day !== null);
        return daysOfWeek.length === 0 || daysOfWeek.includes(foundDate?.day_of_week as any);
      }) || [];
  const oneDaySchedules = schedules.filter((schedule) => schedule.from === args.dateStr && schedule.to === args.dateStr);
  const schedule = schedules[schedules.length - 1];
  const available = !!schedule?.available;

  const dragStart = args.ctx.state.start;
  const dragCurrent = args.ctx.state.current;

  const rect = divRef.current?.getBoundingClientRect();

  const isSelected = !!rect && !!dragStart && !!dragCurrent && getIsSelected(rect, dragStart, dragCurrent);

  const trips = response.member.trips.filter((trip) => trip.date === args.dateStr) || [];

  const bindDrag = useDrag(
    (e) => {
      // console.log("fromdragbind", e.dragging, e.initial, e.xy);
      const parentEl = calendarCtx.parentRef?.current;
      if (!parentEl) return;
      if (!e.dragging) {
        if (parentEl) {
          const fromTo = getFromTo(parentEl, e.initial, e.xy);
          const defaultAvailable = available ? false : true;
          // if (fromTo.from )
          if (fromTo.from !== fromTo.to) {
            dialogRef.current?.showModal();
          }

          setDefaultValue({ ...fromTo, available: defaultAvailable });
        }
        calendarCtx.setState({ start: null, current: null });
      } else {
        calendarCtx.setState({ start: e.initial, current: e.xy });
      }
    },
    { preventDefault: true, preventScroll: true },
  );
  // const isSelected = rect && dragStart && dragCurrent && rect.x < dragStart[0] && rect.y < dragStart[1];

  return (
    <div
      ref={divRef}
      data-date={args.dateStr}
      aria-selected={isSelected}
      onMouseDown={(e) => e.preventDefault()}
      className={twMerge(
        "select-none min-h-[100px] border-l border-b border-slate-200 touch-auto aria-selected:outline-2 -outline-offset-4 outline-black aria-selected:outline",
        !args.isMonth && "opacity-50",
        !schedule?.available && "bg-red-300",
        // !schedule?.available ? "bg-red-300 aria-selected:bg-red-400" : 'aria-selected:bg-slate-50',
        // schedule ? (schedule.available ? "bg-green-500" : "bg-red-500") : "bg-slate-50",
      )}
    >
      <div className="h-full flex flex-col">
        <ScheduleDialog ref={dialogRef} default={defaultValue} />
        <div className="text-center">
          <span
            className={twMerge(
              "inline-block rounded-full border border-transparent p-[2px] px-3 text-xs",
              args.isToday && "border-secondary-500",
              args.isSelected && "text-primary",
            )}
          >
            {format(args.date, "d")}
          </span>
        </div>
        <div className="p-1">
          <ActionForm>
            {oneDaySchedules.map((oneDaySchedule) => (
              <Fragment key={oneDaySchedule.id}>
                <RInput table={"schedule"} field={"id"} index={oneDaySchedule.id} value={oneDaySchedule.id} />
                <OperationInput table={"schedule"} index={oneDaySchedule.id} value={"delete"} />
              </Fragment>
            ))}
            <RInput table={"schedule"} field={"data.target_id"} value={response.member.id} type={"hidden"} />
            <RInput
              table={"schedule"}
              field={"data.available"}
              hiddenType={"__boolean__"}
              // checked={!available}
              value={available ? "false" : "true"}
              // value={available ? "false" : "yes"}
              type={"hidden"}
            />
            <RInput table={"schedule"} field={"data.range"} value={`['${args.dateStr} 00:00', '${args.dateStr} 00:00']`} type={"hidden"} />
            <SubmitButton>invert</SubmitButton>
          </ActionForm>
          <button {...bindDrag()} className="hover:cursor-crosshair flex flex-row w-fit gap-2 items-center touch-none">
            range <ArrowsPointingOutIcon className="w-4 h-4 hidden" />
          </button>
        </div>
        <div className="h-full w-full flex-1">
          <div className="w-full space-y-1">
            {trips.map((trip) => {
              const tripTotals = getTripTotals(trip);

              const totals = trip.assignment_totals.map((total) => {
                const activity = getActivity(total.activity_slug || "");
                return {
                  ...total,
                  activity: activity,
                };
              });

              return (
                <div
                  key={trip.id}
                  className={`flex w-full flex-row items-center gap-[2px] overflow-hidden 
                               border-r-2 border-white bg-secondary px-1 text-xs text-white`}
                  style={{
                    maxHeight: "20px",
                    backgroundColor: totals[0]?.activity?.color || activities.other.color,
                  }}
                >
                  <span className="flex-1 line-clamp-1">{trip.activity_location}</span>
                  {totals.slice(1).map((total) => (
                    <span
                      key={total.activity_slug}
                      style={{ backgroundColor: total.activity.color }}
                      className="h-2 w-2 rounded-full border border-white max-sm:hidden"
                    />
                  ))}
                  <span>{tripTotals.available < 1 ? "F" : tripTotals.available}</span>
                  {/*<span>{JSON.stringify(trip.assignment_totals)}</span>*/}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

const initFromTo = { from: "", to: "", available: undefined };

export default function Page() {
  const dialogRef = useRef<HTMLDialogElement>(null);
  const [fromTo, setFromTo] = useState<{ from: string; to: string; available?: boolean }>(initFromTo);
  const searh = useSearchParams2();
  const response = useLoaderData<typeof loader>();
  const navigation = useNavigation();
  console.log("response", response);
  if (!response) return <EditorRequired />;

  const schedules = response.member.schedules;
  const toBeEditedItems = schedules.filter((schedule) => searh.state.edit.includes(schedule.id));
  const toBeRemovedItems = schedules.filter((schedule) => searh.state.remove.includes(schedule.id));

  const isSaving = !!navigation.formData;

  return (
    <div className="app-container space-y-3">
      <div>
        <h2 className="text-xl font-semibold">
          {response.member.name} ({response.member.email}){" "}
        </h2>
        <h3>{getEstablishmentName(response.member.establishment)}</h3>
      </div>
      <h3 className="">Schedule</h3>
      <MonthSwitch />
      <ScheduleDialog ref={dialogRef} default={fromTo} />
      <div className="select-none">
        <Calendar
          onDateSelected={(startDate, endDate) => {
            setFromTo({ from: startDate, to: endDate, available: false });
            dialogRef.current?.showModal();
          }}
        >
          <CalendarItems item={(args) => <CalendarItem key={args.dateStr} args={args} />} />
        </Calendar>
      </div>
      <div className="text-xs text-slate-600">
        <p>
          If no schedule is specified it means unavailable, you can schedule availability for a specific date/range and possibly specific
          days of the week.
        </p>
        <p>You can also leave the "To" empty to indicate that the duration is unkown (infinity)</p>
        <p>
          You can overwrite availability with "Available = no". This could be usefull for a vacation where you want to keep the general
          availability but overwrite for the duration of the vacation.
        </p>
      </div>
      <ActionForm>
        <ActionAlert />
        <RedirectParamsInput path={"./"} paramState={{ edit: [], remove: [], create: false }} />
        <div className="overflow-auto">
          <table className="w-full [&_td]:p-2">
            <thead className="font-semibold">
              <tr>
                <td className="min-w-[200px]">From</td>
                <td className="min-w-[150px] whitespace-nowrap">To</td>
                {weekDays.map((day) => (
                  <td key={day.key} className="text-center font-normal uppercase">
                    {day.key}
                  </td>
                ))}
                <td className="font-normal">Available</td>
                <td />
              </tr>
            </thead>
            <AnimatingTableBody>
              {schedules.map((schedule) => {
                const toBeRemoved = toBeRemovedItems.includes(schedule);
                const toBeEdited = toBeEditedItems.includes(schedule);
                const isLoading = !!navigation.formData && (toBeRemoved || toBeEdited);
                return (
                  <tr key={schedule.id} className={toBeRemoved ? "opacity-50" : ""}>
                    {/*<td>{schedule.range}</td>*/}
                    {toBeRemoved ? (
                      <Fragment>
                        <td colSpan={weekDays.length + range.length}>Will be removed on save</td>
                      </Fragment>
                    ) : (
                      <Fragment>
                        {/*<td>{schedule.id}</td>*/}
                        {range.map((rangeFieldKey) => {
                          const defaultValue = schedule[rangeFieldKey];
                          return (
                            <td key={rangeFieldKey}>
                              <input
                                disabled={isLoading || !toBeEdited}
                                required={rangeFieldKey === "from"}
                                type="date"
                                defaultValue={defaultValue || ""}
                                onClick={(e) => {
                                  if (!toBeEdited) {
                                    e.currentTarget.focus();
                                  }
                                }}
                                className="input"
                                name={toBeEdited ? fName("schedule", "data.range", schedule.id, rangeFieldKey) : ""}
                              />
                              {rangeFieldKey === "to" && toBeEdited && (
                                <HiddenTypeInput name={fName("schedule", "data.range", schedule.id)} value={"__pg_daterange__"} />
                              )}
                            </td>
                          );
                        })}
                        {weekDays.map((day, dayIndex) => (
                          <td key={dayIndex} className="text-center">
                            <label className="block text-center text-xs leading-5">
                              <input
                                type="checkbox"
                                className="checkbox"
                                name={toBeEdited ? fName("schedule", "data.days_of_week", schedule.id, dayIndex) : ""}
                                defaultChecked={schedule.days_of_week?.includes(dayIndex)}
                                value={dayIndex + ""}
                                disabled={isLoading || !toBeEdited}
                              />
                            </label>
                          </td>
                        ))}
                        <td>
                          <label className="text-green-500">
                            <RInput
                              hiddenType={"__boolean__"}
                              type="radio"
                              className="accent-green-500"
                              disabled={!toBeEdited}
                              table={"schedule"}
                              field={"data.available"}
                              index={schedule.id}
                              value={"yes"}
                              defaultChecked={schedule.available}
                            />
                            &nbsp;yes
                          </label>
                          &nbsp; &nbsp;
                          <label className="text-red-500">
                            <RInput
                              table={"schedule"}
                              field={"data.available"}
                              index={schedule.id}
                              type="radio"
                              disabled={!toBeEdited}
                              value={"no"}
                              defaultChecked={!schedule.available}
                            />
                            &nbsp;no
                          </label>
                        </td>
                      </Fragment>
                    )}
                    {toBeRemoved || toBeEdited ? (
                      <td className="text-right">
                        <RInput table={"schedule"} field={"id"} index={schedule.id} type="hidden" value={schedule.id} />
                        {toBeRemoved && <OperationInput table={"schedule"} index={schedule.id} value={"delete"} />}
                        {/*<RO*/}
                        <ParamLink
                          className="link"
                          paramState={{
                            remove: removeFromArray(searh.state.remove, schedule.id),
                            edit: removeFromArray(searh.state.edit, schedule.id),
                          }}
                        >
                          cancel
                        </ParamLink>
                      </td>
                    ) : (
                      <td className="text-right">
                        <ParamLink className="link" paramState={{ edit: toggleArray(searh.state.edit, schedule.id) }}>
                          edit
                        </ParamLink>
                        &nbsp;
                        {/*<ParamLink params={(p) => toggleParamByKeyValue(p, editKey, olUser.user_id, !checked)}>*/}
                        {/*  <input key={searh.params.get(editKey) + ""} type="checkbox" className="checkbox" checked={checked} />*/}
                        {/*</ParamLink>*/}
                        <ParamLink className="text-red-500" paramState={{ remove: toggleArray(searh.state.remove, schedule.id) }}>
                          remove
                        </ParamLink>
                      </td>
                    )}
                  </tr>
                );
              })}
              <tr>
                <td colSpan={range.length + weekDays.length + 2} className="py-5">
                  <hr />
                </td>
              </tr>
              <tr>
                {searh.state.create ? (
                  <Fragment>
                    {range.map((name, rangeIndex) => (
                      <td key={rangeIndex}>
                        <input
                          required={name === "from"}
                          disabled={isSaving}
                          type="date"
                          className="input"
                          name={fName("schedule", "data.range", 0, name)}
                        />
                        {name === "to" && <HiddenTypeInput name={fName("schedule", "data.range")} value={"__pg_daterange__"} />}
                      </td>
                    ))}
                    {weekDays.map((day, dayIndex) => (
                      <td key={dayIndex} className="text-center">
                        <label className="block text-center text-xs leading-5">
                          <input
                            type="checkbox"
                            className="checkbox"
                            name={fName("schedule", "data.days_of_week", 0, dayIndex)}
                            defaultChecked
                            value={dayIndex + ""}
                            disabled={isSaving}
                          />
                        </label>
                      </td>
                    ))}
                    <td>
                      <RInput table={"schedule"} field={"data.target_id"} type="hidden" value={response.member.id} />
                      <span className="accent-green-500">
                        <RInput
                          hiddenType={"__boolean__"}
                          table={"schedule"}
                          type="radio"
                          field={"data.available"}
                          defaultChecked
                          // value={"yes"}
                          required
                        />
                        <RLabel table={"schedule"} field={"data.available"}>
                          &nbsp;yes
                        </RLabel>
                      </span>
                      &nbsp; &nbsp;
                      <span>
                        <RInput table={"schedule"} type="radio" field={"data.available"} value={"no"} required />
                        <RLabel table={"schedule"} field={"data.available"} className="text-red-500">
                          &nbsp;no
                        </RLabel>
                      </span>
                    </td>
                  </Fragment>
                ) : (
                  <td colSpan={range.length + weekDays.length + 1} />
                )}
                <td className="text-right">
                  <ParamLink className="link" paramState={{ create: true }}>
                    {searh.state.create ? "cancel" : "add"}
                  </ParamLink>
                </td>
              </tr>
            </AnimatingTableBody>
          </table>
        </div>
        <div className="flex justify-end p-3">
          <Button
            loading={isSaving}
            className="btn btn-primary"
            disabled={searh.state.edit.length + searh.state.remove.length + (searh.state.create ? 1 : 0) === 0}
          >
            Save
          </Button>
        </div>
      </ActionForm>
    </div>
  );
}
