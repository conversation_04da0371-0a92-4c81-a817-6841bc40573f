import type { DataFunctionArgs } from "@remix-run/server-runtime";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import React from "react";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _boat_mutate } from "~/misc/paths";
import { getSessionSimple } from "~/utils/session.server";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { notFoundOrUnauthorzied } from "~/misc/responses";
import { memberIsAdminQb } from "~/domain/member/member-queries.server";
import { getEstablishmentName } from "~/domain/establishment/helpers";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: DataFunctionArgs) => {
  const ctx = await getSessionSimple(request);
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  const myEstablishmentIds = memberIsAdminQb({ ctx: ctx, trx: kysely }, "read").select("_member.establishment_id");

  const result = await simpleEstablishmentQb
    .where("establishment.id", "=", state.persist_establishment_id)
    .where("establishment.id", "in", myEstablishmentIds)
    .select((eb) => [
      jsonArrayFrom(
        eb.selectFrom("boat").orderBy("boat.name").selectAll("boat").where("boat.establishment_id", "=", eb.ref("establishment.id")),
      ).as("boats"),
    ])
    .executeTakeFirst();

  if (!result) throw notFoundOrUnauthorzied("establishment not found");
  return result;
};

export default function Page() {
  const establishment = useLoaderData<typeof loader>();

  return (
    <div className="app-container space-y-3">
      <div>
        <div className="flex flex-wrap items-center gap-2">
          <h1 className="text-xl font-bold">Boats</h1>
          <ParamLink path={_boat_mutate} className="link">
            create
          </ParamLink>
        </div>
        <h2 className="text-slate-600">{getEstablishmentName(establishment)}</h2>
      </div>
      <div className="space-y-3">
        <ul>
          {establishment.boats.map((boat) => {
            return (
              <li key={boat.id}>
                <ParamLink className="link" path={_boat_mutate} paramState={{ id: boat.id }}>
                  {boat.name}, Capacity: {boat.capacity}
                </ParamLink>
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
}
