import { LoaderFunctionArgs } from "@remix-run/router";
import { kysely } from "~/misc/database.server";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { useLoaderData } from "@remix-run/react";
import { SingleImageGallery } from "~/components/field/SingleImageGallery";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { flat, unique, uniqueBy } from "remeda";
import { ParamLink } from "~/components/meta/CustomComponents";
import { ActionForm, defaultEqualCheck } from "~/components/form/BaseFrom";
import { DeleteButton, SubmitButton } from "~/components/base/Button";
import { defaultCurrency } from "~/misc/vars";
import React, { Fragment, useEffect, useRef } from "react";
import { pricesJsonEb } from "~/domain/product/product-queries.server";
import { twMerge } from "tailwind-merge";
import { PlusIcon, TrashIcon } from "@heroicons/react/20/solid";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { SidePanel, SidePanelHeading } from "~/components/SidePanel";
import { attributes, ItemForm, SimpleProductForm } from "~/domain/product/SimpleProductForm";
import { ProductPriceForm } from "~/domain/product/ProductPriceForm";
import { ConnectToBookingBanner } from "~/domain/booking/booking-components";
import { useAppContext } from "~/hooks/use-app-context";
import { RInput } from "~/components/ResourceInputs";
import { tableIdRef } from "~/misc/helpers";
import { MoneyValue } from "~/components/field/MoneyValue";
import { _booking_detail, _retail, _sale } from "~/misc/paths";
import { formatPrice } from "~/utils/money";
import { InputFilesDefault } from "~/components/form/FilesInput";
import { bookingCartKey } from "~/domain/booking/booking-vars";
import { RetailBreadCrumb } from "~/domain/category/CategoryComponents";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { createPageOverwrites } from "~/misc/consts";
import { useCanEdit } from "~/domain/member/member-hooks";
import { getItemTitle } from "~/domain/product/ProductItem";
import { StockBadge } from "~/domain/product/StockBadge";
import { salesCountSelect } from "~/domain/activity/activity-queries";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const id = params.id!;

  const item = await kysely
    .selectFrom("item")
    .where("item.id", "=", id)
    .selectAll("item")
    .select((eb) => [
      jsonArrayFrom(
        eb
          .selectFrom("product")
          .selectAll("product")
          .where("product.deleted_at", "=", at_infinity_value)
          .select((eb) => [
            pricesJsonEb,
            jsonArrayFrom(fileTargetsQb(kysely, "product", eb.ref("product.id"))).as("files"),
            salesCountSelect.where("sale_item.product_id", "=", eb.ref("product.id")).as("sales_count"),
          ])
          .where("product.item_id", "=", eb.ref("item.id")),
      ).as("products"),
      jsonObjectFrom(simpleEstablishmentQb.where("establishment.id", "=", eb.ref("item.establishment_id"))).as("establishment"),
    ])
    .executeTakeFirstOrThrow();

  return { item: item, ...createPageOverwrites({ establishment_id: item.establishment_id, customer_toggle: true }) };
};

const Dialog = () => {
  const ref = useRef<HTMLDialogElement>(null);
  const search = useSearchParams2();
  const open = search.state.toggle_modal === "native";
  useEffect(() => {
    if (!ref.current) return;
    if (open) {
      ref.current.open = false;
      ref.current.showModal();
    } else {
      ref.current.close();
    }
  }, [open]);
  return (
    <>
      {open && <div className="fixed inset-0 bg-black/20 z-10" aria-hidden="true" />}
      <dialog
        ref={ref}
        open={open}
        className={twMerge(
          "m-auto z-20 max-w-md rounded-2xl bg-white p-6 shadow-2xl",
          !ref.current && open && "fixed inset-0 flex items-center justify-center",
        )}
      >
        blabla
      </dialog>
    </>
  );
};

const ProductForm = () => {
  const search = useSearchParams2();
  const data = useLoaderData<typeof loader>();
  const allFiles = uniqueBy(
    flat(
      data.item.products.map((product) =>
        product.files.map((filetarget) => ({
          id: filetarget.file_id,
          filename: filetarget.filename,
        })),
      ),
    ),
    (file) => file.id,
  );
  const product = data.item.products.find((product) => product.id === search.state.product_id);

  const productPrice = product?.product_prices[0];

  const defaultCurrencyId =
    productPrice?.currency_id ||
    data.item.products[0]?.product_prices[0]?.currency_id ||
    data.item.establishment?.default_currency ||
    defaultCurrency;
  const defaultPrice = productPrice?.amount || data.item.products[0]?.product_prices[0]?.amount;

  const selectedAttibutes = attributes.filter((attr) => data.item.products.find((product) => product[attr]));

  return (
    <div className="space-y-3">
      <SidePanelHeading>
        <h3 className="text-xl">{product ? "Edit Variant" : "Create Variant"}</h3>
      </SidePanelHeading>
      <div className="space-y-5 px-3">
        <SimpleProductForm product={product || { item_id: data.item.id }} attributes={selectedAttibutes} />
        <ProductPriceForm productPrice={productPrice || { currency_id: defaultCurrencyId, amount: defaultPrice }} />
        <InputFilesDefault
          target_id={tableIdRef("product")}
          target={"product"}
          defaultValue={product?.files}
          small
          existingFiles={allFiles}
        />
        <div className="flex flex-row gap-3 justify-end">
          <ParamLink className="btn" paramState={{ toggle_modal: undefined }}>
            Cancel
          </ParamLink>
          <SubmitButton className="btn btn-primary">Save</SubmitButton>
        </div>
      </div>
    </div>
  );
};

const mutateProductKey = "product";

const SidePanelContent = () => {
  const search = useSearchParams2();
  const data = useLoaderData<typeof loader>();

  if (search.state.modal_detail_name === mutateProductKey) return <ProductForm />;

  return (
    <div className="space-y-3">
      <SidePanelHeading>
        <h3 className="text-xl">Edit Item</h3>
      </SidePanelHeading>
      <ItemForm item={data.item} />
      <div className="flex flex-row gap-3 justify-end">
        <ParamLink className="btn" paramState={{ toggle_modal: undefined }}>
          Cancel
        </ParamLink>
        <SubmitButton className="btn btn-primary">Save</SubmitButton>
      </div>
    </div>
  );
};

const AddToBooking = (props: { product_id: string; price: { amount: number; currency_id: string } }) => {
  const data = useLoaderData<typeof loader>();
  const ctx = useAppContext();
  const booking = ctx.establishment?.booking;

  // if (!booking) return <Fragment/>

  return (
    <ActionForm className="flex flex-row gap-3">
      {booking && <RInput table={"booking"} field={"id"} value={booking.id} />}
      <RInput table={"sale_item"} field={"data.booking_id"} type={"hidden"} value={tableIdRef("booking")} />
      <RInput table={"sale_item"} field={"data.product_id"} type={"hidden"} value={props.product_id} />
      <RInput table={"sale_item"} field={"data.price_pp"} type={"hidden"} value={props.price.amount} />
      {!booking && (
        <Fragment>
          <RInput table={"booking"} field={"data.establishment_id"} type={"hidden"} value={data.item.establishment_id} />
          {/*<RInput table={"booking"} field={"data.meeting_type"} type={"hidden"} value={"DIVE_CENTER" satisfies MeetingType} />*/}
          <RInput table={"booking"} field={"data.currency_id"} type={"hidden"} value={props.price.currency_id} />
        </Fragment>
      )}
      <RInput table={"sale_item"} field={"data.quantity"} type={"number"} className="input w-20" defaultValue={1} min={1} required />
      <RedirectParamsInput path={_booking_detail(tableIdRef("booking"))} paramState={{}} />
      {/*<RInput table={"sale_item"} field={"data.qu"} />*/}
      {/*<RInput table={'activity'} field={''}*/}
      <SubmitButton className="btn btn-primary">
        {booking ? "Add to" : "Create"} order {booking?.booking_reference || booking?.sqid}
      </SubmitButton>
    </ActionForm>
  );
};

const AddToCart = (props: { product_id: string; price: { amount: number; currency_id: string } }) => {
  const data = useLoaderData<typeof loader>();
  // if (!booking) return <Fragment/>

  return (
    <ActionForm className="flex flex-row gap-3">
      <RInput table={"booking"} field={"data.cart_for_session_id"} type={"hidden"} value={bookingCartKey} />
      <RInput table={"sale_item"} field={"data.booking_id"} type={"hidden"} value={tableIdRef("booking")} />
      <RInput table={"sale_item"} field={"data.product_id"} type={"hidden"} value={props.product_id} />
      <RInput table={"sale_item"} field={"data.price_pp"} type={"hidden"} value={props.price.amount} />
      <RInput table={"booking"} field={"data.establishment_id"} type={"hidden"} value={data.item.establishment_id} />
      <RInput table={"booking"} field={"data.currency_id"} type={"hidden"} value={props.price.currency_id} />
      <RInput table={"sale_item"} field={"data.quantity"} type={"number"} className="input w-20" defaultValue={1} min={1} required />
      <SubmitButton className="btn btn-primary">Add to Cart</SubmitButton>
    </ActionForm>
  );
};

export default function Page() {
  const data = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const ctx = useAppContext();

  // Get the selected product directly by ID
  const selectedProduct = data.item.products.find((product) => product.id === search.state.product_id);

  const canEdit = useCanEdit(data.item.establishment_id);

  // If no product is selected, use the first product as default
  const activeProduct = selectedProduct || data.item.products[0];

  // Colors and sizes from all products
  const colors = unique(data.item.products.map((product) => product.color)).sort((a, b) => (!a ? 1 : !b ? -1 : a.localeCompare(b)));
  const sizes = unique(data.item.products.map((product) => product.size)).sort((a, b) => (!a ? 1 : !b ? -1 : a.localeCompare(b)));

  // Get products for the selected color (for showing available sizes)
  const productsForColor = activeProduct ? data.item.products.filter((p) => p.color === activeProduct.color) : [];

  // Available sizes for the selected color
  const sizesForColor = unique(productsForColor.map((p) => p.size));

  // Handle files
  const allFiles = uniqueBy(flat(data.item.products.map((p) => p.files.map((file) => file))), (file) => file.filename).map(
    (file) => file.filename,
  );
  const selectedProductFiles = activeProduct?.files.map((file) => file.filename);
  const finalFiles = selectedProductFiles?.length ? selectedProductFiles : allFiles;

  const selectedProductPrice = activeProduct?.product_prices[0];

  const showEdit = canEdit && !search.state.customer_view;
  return (
    <Fragment>
      <div className="space-y-6">
        <div className="app-container space-y-6">
          <ConnectToBookingBanner />
          <RetailBreadCrumb category_id={data.item.category_id}>
            <span className="text-slate-700">{getItemTitle(data.item)}</span>
          </RetailBreadCrumb>
          <div className="flex flex-row gap-3 items-center">
            <h2 className="text-2xl font-semibold">{getItemTitle(data.item)}</h2>
            {showEdit && (
              <Fragment>
                <ParamLink className={"link"} paramState={{ toggle_modal: "side", modal_detail_name: null }}>
                  Edit
                </ParamLink>
                <ActionForm confirmMessage={"Are you sure?"}>
                  {data.item.products.map((product) => (
                    <Fragment key={product.id}>
                      <RInput table={"product"} field={"id"} value={product.id} index={product.id} />
                      <RInput table={"product"} field={"data.deleted_at"} value={"now"} index={product.id} type={"hidden"} />
                    </Fragment>
                  ))}
                  <RedirectParamsInput path={_retail} paramState={{ category_id: data.item.category_id }} />
                  <DeleteButton className="capitalize" />
                </ActionForm>
              </Fragment>
            )}
          </div>
          <div className="flex-col md:flex-row flex gap-6">
            {!!allFiles.length && (
              <div className={twMerge("flex-1", !selectedProductFiles?.length && "opacity-50")}>
                <SingleImageGallery key={selectedProduct?.id || ""} fileNames={finalFiles} />
              </div>
            )}
            <div className="flex-1 space-y-6">
              <div className="space-y-6">
                {!!colors[0] && (
                  <div className="space-y-1">
                    <p className="font-semibold">Color:</p>
                    <div className="flex flex-wrap gap-3">
                      {colors.map((color) => {
                        // Find products with this color
                        const productsWithThisColor = data.item.products.filter((p) => p.color === color);

                        // Sort products so that ones with the same size as activeProduct come first
                        const currentSize = activeProduct?.size;
                        const sortedProducts = [...productsWithThisColor].sort((a, b) => {
                          if (a.size === currentSize && b.size !== currentSize) return -1;
                          if (a.size !== currentSize && b.size === currentSize) return 1;
                          return 0;
                        });

                        // Use the first product after sorting (which will be one with the same size if available)
                        const productToUse = sortedProducts[0];

                        return (
                          <ParamLink
                            className="btn btn-basic aria-selected:btn-secondary"
                            paramState={{ product_id: productToUse?.id }}
                            aria-selected={activeProduct?.color === color}
                            key={color}
                          >
                            {color || "No Color"}
                          </ParamLink>
                        );
                      })}
                    </div>
                  </div>
                )}
                {!!sizes[0] && (
                  <div className="space-y-1">
                    <p className="font-semibold">Size:</p>
                    <div className="flex flex-wrap gap-3">
                      {sizes.map((size) => {
                        // Find a product with the current color and this size
                        const productWithThisSize = activeProduct?.color
                          ? data.item.products.find((p) => p.color === activeProduct.color && p.size === size)
                          : data.item.products.find((p) => p.size === size);

                        return (
                          <ParamLink
                            className="btn btn-basic aria-selected:btn-secondary aria-disabled:opacity-50 relative overflow-hidden aria-disabled:line-through"
                            paramState={{ product_id: productWithThisSize?.id }}
                            aria-disabled={!sizesForColor.includes(size)}
                            aria-selected={activeProduct?.size === size}
                            key={size}
                          >
                            {size || "No Size"}
                          </ParamLink>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
              {activeProduct && selectedProductPrice && (
                <div className="space-y-6">
                  {/*{JSON.stringify(selectedProductPrice)}*/}
                  <p className="text-xl font-bold">
                    <MoneyValue
                      locale={null}
                      // locale={selectedProductPrice.currency_id}
                      toCurrency={selectedProductPrice.currency_id}
                      nativeAmount={selectedProductPrice.amount}
                      nativeCurrency={selectedProductPrice.currency_id}
                    />
                  </p>
                  {showEdit && (
                    <div>
                      <AddToBooking product_id={activeProduct.id} price={selectedProductPrice} />
                    </div>
                  )}
                  {ctx.environment === "development" && (
                    <div>
                      <AddToCart product_id={activeProduct.id} price={selectedProductPrice} />
                    </div>
                  )}
                </div>
              )}
              {!!data.item.description && (
                <div>
                  <h3 className="font-bold">Description</h3>
                  <p className="whitespace-pre-wrap">{data.item.description}</p>
                </div>
              )}
              {activeProduct && (
                <div>
                  <ParamLink
                    path={_sale}
                    paramState={{ product_id: activeProduct.id, persist_establishment_id: data.item.establishment_id }}
                    className="btn btn-basic w-fit"
                  >
                    Show Sales
                  </ParamLink>
                </div>
              )}
            </div>
          </div>
        </div>
        {showEdit && (
          <Fragment>
            <hr />
            <div className="container overflow-auto w-full min-w-0">
              <div>
                <div className="flex flex-row justify-between items-center gap-3">
                  <h3 className="text-xl">Variants: {!activeProduct && <span className="text-red-500">None Selected</span>}</h3>
                  <ParamLink
                    paramState={{ toggle_modal: "side", modal_detail_name: mutateProductKey, product_id: null }}
                    className="btn btn-primary flex flex-wrap items-center gap-1"
                  >
                    <PlusIcon className="w-4 h-4" />
                    Add Variant
                  </ParamLink>
                </div>
                <div className="grid grid-cols-[repeat(7,minmax(auto,1fr))]  gap-y-2 gap-x-2 text-slate-700">
                  <div>SKU</div>
                  <div>Color</div>
                  <div>Size</div>
                  <div>Stock</div>
                  <div>Price</div>
                  <div>Sales</div>
                  <div></div>
                  {data.item.products.map((product) => {
                    const firstPrice = product.product_prices[0];
                    const currentStock = (product.stock || 0) - (product.sales_count || 0);
                    return (
                      <div
                        className={twMerge(
                          "col-span-7 grid grid-cols-subgrid p-1  gap-3 rounded-md",
                          product === activeProduct && "bg-slate-100",
                        )}
                        key={product.id}
                      >
                        <div className="flex flex-wrap gap-3">
                          <ParamLink
                            paramState={{
                              toggle_modal: "side",
                              modal_detail_name: mutateProductKey,
                              product_id: product.id,
                            }}
                            className="link whitespace-nowrap"
                          >
                            {product.sku || product.id}
                          </ParamLink>
                        </div>
                        <div>{product.color || "-"}</div>
                        <div>{product.size || "-"}</div>
                        <div>
                          <StockBadge stock={currentStock} />
                        </div>
                        <div>{firstPrice && formatPrice(firstPrice.amount, firstPrice.currency_id, firstPrice.currency_id)}</div>
                        <div className="text-center">{product.sales_count || 0}</div>
                        <div className="flex justify-end">
                          <ActionForm
                            confirmMessage={
                              data.item.products.length === 1
                                ? "Because this is the last variant it will also delete the product itself, Are you sure?"
                                : "Are you sure?"
                            }
                          >
                            {data.item.products.length === 1 && (
                              <RedirectParamsInput path={_retail} paramState={{ category_id: data.item.category_id }} />
                            )}
                            <RInput table={"product"} field={"id"} value={product.id} index={product.id} />
                            <RInput table={"product"} field={"data.deleted_at"} value={"now"} index={product.id} type={"hidden"} />
                            <SubmitButton>
                              <TrashIcon className="w-4 h-4" />
                            </SubmitButton>
                          </ActionForm>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </Fragment>
        )}
      </div>

      <Dialog />
      <SidePanel className="max-md:inset-x-0">
        <div className="h-full w-full md:w-[460px] bg-white p-3 overflow-auto">
          <ActionForm onCheckEqual={defaultEqualCheck}>
            <SidePanelContent />
            <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined }} />
          </ActionForm>
        </div>
      </SidePanel>
    </Fragment>
  );
}
