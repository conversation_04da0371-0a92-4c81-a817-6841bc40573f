import { t } from "@lingui/core/macro";
import { setupI18n } from "@lingui/core";
import type { MetaFunction } from "@remix-run/react";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { getSessionSimple } from "~/utils/session.server";
import type { ReactNode } from "react";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { getProductTitle, ProductTags } from "~/domain/product/ProductItem";
import { getDateFromParams } from "~/domain/planning/planning.helpers.server";
import { myGroupBy2 } from "~/misc/helpers";
import { getMeetingType, getTripType } from "~/domain/planning/plannings-consts";
import { differenceInYears } from "date-fns";
import { getNrOfDivesOption } from "~/domain/participant/participant-data";
import { TripPanelBasic } from "~/domain/trip/TripPanel";
import { tripQb } from "~/domain/trip/trip.server";
import { memberQb } from "~/domain/member/member-queries.server";
import { dayNumberSelect, sqid } from "~/kysely/kysely-helpers";
import { useAppContext } from "~/hooks/use-app-context";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { saleItemSimpleQb } from "~/domain/activity/activity-queries";
import { flat } from "remeda";
import { baseProductWithSelect } from "~/domain/product/product-queries.server";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { LoaderFunctionArgs } from "@remix-run/router";
import { getLastDivedShort } from "~/domain/participant/participant-helpers";
import { participantSimpleQb } from "~/domain/participant/participant.queries.server";
import { toUtc } from "~/misc/date-helpers";
import { getAppContextInMeta } from "~/misc/route-helpers";
import { robotsMeta } from "~/misc/web-helpers";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);

  const date = getDateFromParams(request, true);

  const myMemberIdsQb = memberQb({ trx: kysely, ctx: { session_id: session_id } }).select("_member.id");

  const tripAssignmentsQb = kysely
    .selectFrom("trip_assignment")
    .leftJoin("participation", "participation.id", "trip_assignment.participation_id")
    .leftJoin("sale_item", "sale_item.id", "participation.sale_item_id")
    .leftJoin("booking", "booking.id", "sale_item.booking_id")
    .where("booking.cancelled_at", "is", null);

  const trips = await tripQb
    .where("trip.id", "in", tripAssignmentsQb.where("trip_assignment.member_id", "in", myMemberIdsQb).select("trip_assignment.trip_id"))
    .where("trip.date", "=", date.dateParam)
    .orderBy("trip.start_time")
    .orderBy("trip.activity_location")
    .orderBy("trip.id")
    .select((eb) => [
      jsonArrayFrom(
        tripAssignmentsQb
          .selectAll("trip_assignment")
          .select((eb) => [
            jsonObjectFrom(eb.selectFrom("member").whereRef("member.id", "=", "trip_assignment.member_id").selectAll("member")).as(
              "member",
            ),
            jsonObjectFrom(
              eb
                .selectFrom("participation")
                .whereRef("participation.id", "=", "trip_assignment.participation_id")
                .selectAll("participation")
                .select((eb) => [
                  jsonObjectFrom(
                    saleItemSimpleQb
                      .where("sale_item.id", "=", eb.ref("participation.sale_item_id"))
                      .select((eb) => dayNumberSelect(date.dateParam, eb.ref("sale_item.duration")).as("day_number")),
                  ).as("activity"),
                  jsonObjectFrom(participantSimpleQb(kysely).where("participant.id", "=", eb.ref("participation.participant_id"))).as(
                    "participant",
                  ),
                  jsonObjectFrom(
                    eb
                      .selectFrom("booking")
                      .whereRef("booking.id", "=", "sale_item.booking_id")
                      .selectAll("booking")
                      .select((eb) => sqid(eb.ref("booking.id_seq")).as("sqid")),
                  ).as("booking"),
                ]),
            ).as("participation"),
          ])
          .where("trip_assignment.trip_id", "=", eb.ref("trip.id")),
      ).as("assignments"),
      jsonObjectFrom(simpleEstablishmentQb.where("establishment.id", "=", eb.ref("trip.establishment_id"))).as("establishment"),
    ])
    .execute();

  const productIds = flat(trips.map((trip) => trip.assignments))
    .map((assignment) => assignment.participation?.activity?.product_id)
    .filter((productId): productId is string => !!productId);
  const products = productIds.length > 0 ? await baseProductWithSelect.where("product.id", "in", productIds).execute() : [];
  return {
    date: date,
    trips: trips,
    products: products,
  };
};

export const meta: MetaFunction<typeof loader> = (args) => {
  const ctx = getAppContextInMeta(args);
  const i18n = setupI18n();
  if (ctx) i18n.loadAndActivate({ locale: ctx.locale, messages: ctx.translations });
  return [robotsMeta("noindex"), { title: t(i18n)`Planning` }];
};

const participantHeaderColumns = [
  "name",
  "ref#",
  "meet",
  "time",
  "cert",
  "dives",
  "last",
  "age",
  "weight",
  "boots",
  "height",
  "wetsuit",
] as const;

export default function Page() {
  const session = useAppContext();
  const response = useLoaderData<typeof loader>();

  return (
    <div className="max-lg:px-3 flex-1 space-y-3">
      {response.trips.length === 0 && <p className="text-slate-600">Nothing scheduled</p>}
      <div className="space-y-3">
        {response.trips.map((trip) => {
          // const startTime = formatInTimeZone(trip.start_time, defaultTimezone, "HH:mm");
          const myAssignments = trip.assignments.filter((assignment) => session.user_id && assignment.member?.user_id === session.user_id);
          // const rolesRaw = myAssignments.map((assignment) => assignment.role);
          // const finalRoles = entries(roles).filter(([roleKey]) => rolesRaw.includes(roleKey));

          const tripEntry = getTripType(trip.type);

          const myParticipants = myAssignments
            .map((assignment) => assignment.participation)
            .filter((participant): participant is NonNullable<typeof participant> => !!participant);

          return (
            <div key={trip.id} className="space-y-3">
              <h3 className="text-xl font-semibold">{getEstablishmentName(trip.establishment)}</h3>
              <TripPanelBasic trip={trip} readonly />
              {/*<div className="rounded-md border border-primary p-3">*/}
              {/*  <table>*/}
              {/*    <TripBaseDetails trip={trip} />*/}
              {/*  </table>*/}
              {/*  <p>You're assigned as {finalRoles.map(([_, role]) => role.label).join(", ")}</p>*/}
              {/*</div>*/}
              {myGroupBy2(myParticipants, (participant) => participant?.activity?.product_id || "").map((productGroup) => {
                const activity = productGroup.items[0]?.activity;
                const product = response.products.find((product) => product.id === productGroup.groupKey);
                const dayNrText =
                  activity &&
                  !!activity.duration_in_days &&
                  Number(activity.duration_in_days) > 1 &&
                  " , day " + ((activity.day_number || 0) + 1);
                return (
                  <div key={productGroup.groupKey} className="w-full space-y-3">
                    <div className="flex flex-row justify-between rounded-md bg-secondary-50 p-2">
                      <div className="space-y-1">
                        <div>
                          <p className="font-semibold">
                            {product && getProductTitle(product)}
                            {dayNrText}
                          </p>
                          {product?.subtitle && <p className="text-xs text-gray-500 line-clamp-1">{product.subtitle}</p>}
                        </div>
                        {product && (
                          <div className={"flex flex-wrap gap-2"}>
                            <ProductTags item={product} />
                          </div>
                        )}
                      </div>
                      {tripEntry ? (
                        <div className="space-y-1">
                          <div className="h-14 w-20 text-secondary-tag">{tripEntry.icon}</div>
                          {trip?.boat && (
                            <div className="text-center text-secondary-tag">
                              <span>{trip?.boat.name}</span>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="p-4">?</div>
                      )}
                    </div>
                    <div className="rounded-md border border-slate-500">
                      <div className="overflow-auto">
                        <div className="p-2">
                          <table className="w-full text-slate-600 [&_td]:whitespace-nowrap [&_td]:p-1">
                            <thead className="text-slate-700">
                              <tr>
                                {participantHeaderColumns.map((column) => (
                                  <th key={column} className="font-normal text-left capitalize text-black first:pl-0 last:pr-0 px-1">
                                    {column}
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {productGroup.items.map((participation) => {
                                const meetingType = getMeetingType(participation?.booking?.meeting_type);

                                const participant = participation?.participant;

                                const columns: Record<(typeof participantHeaderColumns)[number], ReactNode> = {
                                  name: participant ? participant.first_name + " " + participant.last_name : "?",
                                  "ref#": participation?.booking?.booking_reference || participation?.booking?.sqid || "booking",
                                  meet: meetingType?.short,
                                  time: participation?.booking?.meeting_time?.slice(0, 5) || "-",
                                  cert: participant?.diving_certificate_level || "?",
                                  dives: getNrOfDivesOption(participant?.number_of_dives)[1],
                                  last: getLastDivedShort(participant?.last_dive_within_months) || "?",
                                  age: participant?.birth_date
                                    ? differenceInYears(toUtc(response.date.dateParam), toUtc(participant.birth_date))
                                    : "",
                                  weight: participant?.weight_value && participant.weight_value + "" + participant?.weight_unit,
                                  boots: participant?.shoe_size_value && participant.shoe_size_value + "" + participant?.shoe_size_unit,
                                  height: participant?.height_value && participant.height_value + "" + participant?.height_unit,
                                  wetsuit: participant?.wetsuit_size,
                                };

                                // const startTime = participant?.booking?.duration_start;
                                return (
                                  <tr key={participation.id} className="relative aria-selected:bg-secondary-50">
                                    {participantHeaderColumns.map((column) => (
                                      <td className=" first:pl-0 last:pr-0 px-1" key={column}>
                                        {columns[column]}
                                      </td>
                                    ))}
                                  </tr>
                                );
                              })}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
    </div>
  );
}
