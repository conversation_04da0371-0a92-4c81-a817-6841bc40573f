import React from "react";
import { _connect } from "~/misc/paths";
import { ParamLink } from "~/components/meta/CustomComponents";

export { action } from "~/routes/_all._catch.resource";

export default function Page() {
  return (
    <div className="app-container py-6 space-y-3">
      <h1 className="text-xl">Connect</h1>
      <ul className="space-y-3">
        <li>
          <ParamLink path={_connect("production")} className={"link"}>
            Production
          </ParamLink>
        </li>
        <li>
          <ParamLink path={_connect("sandbox")} className="link">
            Sandbox
          </ParamLink>
        </li>
      </ul>
    </div>
  );
}
