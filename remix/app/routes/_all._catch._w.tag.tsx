import { use<PERSON><PERSON>derD<PERSON> } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import React, { Fragment } from "react";
import { ParamLink } from "~/components/meta/CustomComponents";
import { getSessionSimple } from "~/utils/session.server";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { notFoundOrUnauthorzied } from "~/misc/responses";
import { memberIsAdminQb } from "~/domain/member/member-queries.server";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { LoaderFunctionArgs } from "@remix-run/router";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { CDialog } from "~/components/base/Dialog";
import { ActionForm } from "~/components/form/BaseFrom";
import { RInput } from "~/components/ResourceInputs";
import { DeleteButton, SubmitButton } from "~/components/base/Button";
import { ActionAlert } from "~/components/ActionAlert";
import { CgClose } from "react-icons/cg";
import { OperationInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { arrayAgg } from "~/kysely/kysely-helpers";
import { fName } from "~/misc/helpers";
import { EstablishmentLayout } from "~/components/AllowedForEstablishment";

export { action } from "~/routes/_all._catch.resource";

const enableCategories = false;

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const ctx = await getSessionSimple(request);
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  const result = await simpleEstablishmentQb
    .where("establishment.id", "=", state.persist_establishment_id)
    .where(
      "establishment.id",
      "in",
      memberIsAdminQb(
        {
          ctx: ctx,
          trx: kysely,
        },
        "read",
      ).select("_member.establishment_id"),
    )
    .select((eb) => [
      jsonArrayFrom(
        eb
          .selectFrom("tag")
          .orderBy("tag.name")
          .selectAll("tag")
          .select((eb) =>
            eb
              .selectFrom("product__tag")
              .where("product__tag.tag_id", "=", eb.ref("tag.id"))
              .select((eb) => arrayAgg(eb.ref("product__tag.id"), "uuid").as("product__tag_ids"))
              .as("product__tag_ids"),
          )
          .where("tag.establishment_id", "=", eb.ref("establishment.id")),
      ).as("tags"),
      jsonArrayFrom(eb.selectFrom("category").selectAll("category").whereRef("category.establishment_id", "=", "establishment.id")).as(
        "categories",
      ),
    ])
    .executeTakeFirst();

  if (!result) throw notFoundOrUnauthorzied("establishment not found or you not authorized");
  return result;
};

const tagCreateKey = "tag";
const categoryCreateKey = "category";

const CategoryBlock = (props: { category_id: string; parentCategoryNames: string[] }) => {
  const establishment = useLoaderData<typeof loader>();
  const category = establishment.categories.find((cat) => cat.id === props.category_id);
  if (!category) return <div>Could not find category</div>;
  const subCategories = establishment.categories.filter((cat) => cat.parent_category_id === category.id);
  return (
    <div>
      <div>
        {!!props.parentCategoryNames.length && (
          <span>
            {props.parentCategoryNames.join(" -> ")} {"->"}{" "}
          </span>
        )}
        <ParamLink paramState={{ toggle_modal: "content", id: category.id }} className={"link"}>
          {category.name}
        </ParamLink>

        {/*<ParamLink className="link" paramState={{ toggle_modal: "content", id: categoryCreateKey + category.id }}>*/}
        {/*  create*/}
        {/*</ParamLink>*/}
      </div>
      {subCategories.map((subCat) => (
        <CategoryBlock key={subCat.id} category_id={subCat.id} parentCategoryNames={[...props.parentCategoryNames, category.name]} />
      ))}
    </div>
  );
};

export default function Page() {
  const establishment = useLoaderData<typeof loader>();
  const search = useSearchParams2();
  const selectedTag = establishment.tags.find((tag) => tag.id === search.state.id);
  const selectedCategory = establishment.categories.find((cat) => cat.id === search.state.id);
  const parentCategoryId = search.state.id?.startsWith(categoryCreateKey) && search.state.id.replace(categoryCreateKey, "");
  const parentCateogry = establishment.categories.find((cat) => cat.id === parentCategoryId);

  return (
    <EstablishmentLayout
      title={
        <div className="flex flex-wrap items-center gap-2">
          <h1 className="text-xl font-semibold mb-4">Tags</h1>
          <ParamLink paramState={{ id: tagCreateKey, toggle_modal: "content" }} className="link">
            create
          </ParamLink>
        </div>
      }
    >
      <div className="space-y-3">
        <div className="space-y-3">
          <div className="flex flex-wrap gap-3">
            {establishment.tags.map((tag) => {
              return (
                <ParamLink key={tag.id} className="btn bg-slate-200" paramState={{ id: tag.id, toggle_modal: "content" }}>
                  {tag.name}
                </ParamLink>
              );
            })}
          </div>
        </div>

        {enableCategories && (
          <div>
            <div className="flex flex-wrap items-center gap-2">
              <h1 className="text-xl font-bold">Categories</h1>
              <ParamLink paramState={{ id: categoryCreateKey, toggle_modal: "content" }} className="link">
                create
              </ParamLink>
            </div>
            {establishment.categories.map((cat) => (
              <CategoryBlock key={cat.id} category_id={cat.id} parentCategoryNames={[]} />
            ))}
          </div>
        )}

        <CDialog dialogname={"content"} className="space-y-3">
          {(selectedTag || search.state.id === tagCreateKey) && (
            <ActionForm className="space-y-3 pb-3" generateIdentifier>
              <div className="flex flex-row justify-between items-center gap-3 pb-2">
                {selectedTag ? <p>Edit tag "{selectedTag.name}"</p> : <p>Create tag</p>}
                <ParamLink paramState={{ toggle_modal: undefined }} replace>
                  <CgClose />
                </ParamLink>
              </div>
              <ActionAlert />
              <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined }} />
              {selectedTag && <RInput table={"tag"} field={"id"} value={selectedTag.id} />}
              <RInput table={"tag"} field={"data.establishment_id"} value={establishment.establishment_id} type={"hidden"} />
              <div className="flex flex-row gap-3">
                <RInput
                  table={"tag"}
                  field={"data.name"}
                  className="input"
                  placeholder={"Enter tag name..."}
                  defaultValue={selectedTag?.name || ""}
                />
                <SubmitButton className={"btn btn-primary"}>{selectedTag ? "Save" : "Create"}</SubmitButton>
              </div>
            </ActionForm>
          )}
          {selectedTag && (
            <Fragment>
              <hr />
              <div className="">
                <p></p>
                {/*<hr />*/}
                <ActionForm
                  confirmMessage={
                    selectedTag.product__tag_ids?.length
                      ? `This will also remove tag "${selectedTag.name}" from ${selectedTag.product__tag_ids?.length} product(s).\n\nAre you sure?`
                      : `Are you sure you want to delete tag "${selectedTag.name}"?`
                  }
                  className="text-right"
                >
                  <RInput table={"tag"} field={"id"} value={selectedTag.id} />
                  {selectedTag.product__tag_ids?.map((productTagId) => (
                    <Fragment key={productTagId}>
                      <RInput table={"product__tag"} field={"id"} index={productTagId} value={productTagId} />
                      <OperationInput table={"product__tag"} value={"delete"} index={productTagId} />
                    </Fragment>
                  ))}
                  <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined }} />
                  <OperationInput table={"tag"} value={"delete"} />
                  <DeleteButton>Delete tag</DeleteButton>
                </ActionForm>
              </div>
            </Fragment>
          )}
          {(selectedCategory || search.state.id?.startsWith(categoryCreateKey)) && (
            <ActionForm key={search.state.rerender + ""} className="space-y-3 pb-3" generateIdentifier>
              <div className="flex flex-row justify-between items-center gap-3 pb-2">
                {selectedCategory ? (
                  <p>Edit Category "{selectedCategory.name}"</p>
                ) : parentCateogry ? (
                  <p>Create Sub-Category under "{parentCateogry.name}"</p>
                ) : (
                  <p>Create Category</p>
                )}
                <ParamLink paramState={{ toggle_modal: undefined }} replace>
                  <CgClose />
                </ParamLink>
              </div>
              <ActionAlert />
              <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined }} />
              {!!parentCategoryId && (
                <RInput table={"category"} field={"data.parent_category_id"} value={parentCategoryId} type={"hidden"} />
              )}
              {selectedCategory && <RInput table={"category"} field={"id"} value={selectedCategory.id} />}
              <RInput table={"category"} field={"data.establishment_id"} value={establishment.establishment_id} type={"hidden"} />
              <div className="flex flex-row gap-3">
                <RInput
                  table={"category"}
                  field={"data.name"}
                  className="input"
                  placeholder={"Enter category name..."}
                  defaultValue={selectedCategory?.name || ""}
                />
                <SubmitButton className={"btn btn-primary"}>{selectedCategory ? "Save" : "Create"}</SubmitButton>
              </div>
            </ActionForm>
          )}
          {selectedCategory && (
            <Fragment>
              <hr />
              <div className="text-right">
                <ParamLink
                  paramState={{
                    toggle_modal: "content",
                    id: categoryCreateKey + selectedCategory.id,
                    rerender: search.state.rerender + 1,
                    element_action: [fName("category", "data.name")],
                  }}
                  className="link"
                >
                  Create Sub-Category
                </ParamLink>
              </div>
              <hr />
              <div className="">
                <p></p>
                {/*<hr />*/}
                <ActionForm
                  // confirmMessage={
                  //   selectedCategory.product__tag_ids?.length
                  //     ? `This will also remove tag "${selectedCategory.name}" from ${selectedCategory.product__tag_ids?.length} product(s).\n\nAre you sure?`
                  //     : `Are you sure you want to delete tag "${selectedCategory.name}"?`
                  // }
                  className="text-right"
                >
                  <RInput table={"category"} field={"id"} value={selectedCategory.id} />
                  {/*{selectedCategory.product__tag_ids?.map((productTagId) => (*/}
                  {/*  <Fragment key={productTagId}>*/}
                  {/*    <RInput table={"product__tag"} field={"id"} index={productTagId} value={productTagId} />*/}
                  {/*    <OperationInput table={"product__tag"} value={"delete"} index={productTagId} />*/}
                  {/*  </Fragment>*/}
                  {/*))}*/}
                  <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined }} />
                  <OperationInput table={"category"} value={"delete"} />
                  <DeleteButton>Delete Category</DeleteButton>
                </ActionForm>
              </div>
            </Fragment>
          )}
        </CDialog>
      </div>
    </EstablishmentLayout>
  );
}
