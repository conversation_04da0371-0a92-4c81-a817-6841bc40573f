import { LoaderFunctionArgs } from "@remix-run/router";
import { getAccessTokenAndRefresh, getQuery } from "~/domain/quickbooks/quickbooks-client.server";
import { useLoaderData } from "@remix-run/react";
import { kysely } from "~/misc/database.server";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _connect, _establishment_detail } from "~/misc/paths";
import { RInput, RLabel, RSelect } from "~/components/ResourceInputs";
import React from "react";
import { ActionForm, defaultEqualCheck } from "~/components/form/BaseFrom";
import { DeleteButton, SubmitButton } from "~/components/base/Button";
import { RedirectInput } from "~/components/form/DefaultInput";
import { removeObjectKeys } from "~/misc/helpers";
import { at_infinity_value, at_now_value } from "~/kysely/db-static-vars";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { Alert } from "~/components/base/alert";
import { getCachedValue } from "~/server/cache/cache.server";

export { action } from "~/routes/_all._catch.resource";

export const loader = async (args: LoaderFunctionArgs) => {
  const intuitConnectionId = args.params.id!;
  const establishments = await kysely
    .selectFrom("intuit_connection")
    .where("intuit_connection.id", "=", intuitConnectionId)
    .selectAll("intuit_connection")
    .select((eb) => [
      jsonArrayFrom(
        simpleEstablishmentQb.where(
          "establishment.id",
          "in",
          eb
            .selectFrom("payment_method")
            .select("payment_method.establishment_id")
            .where("payment_method.intuit_connection_id", "=", eb.ref("intuit_connection.id"))
            .where("payment_method.deleted_at", "=", at_infinity_value),
        ),
      ).as("connected_establishments"),
    ])
    .executeTakeFirstOrThrow();

  const intuitConnection = removeObjectKeys(establishments, "refresh_token");
  try {
    const result = await getCachedValue({
      key: `intuit_connection_${intuitConnectionId}_tax_codes`,
      request: async () => {
        const token = await getAccessTokenAndRefresh(kysely, intuitConnectionId);
        const taxcodesResult = await getQuery(token, "TaxCode");
        return taxcodesResult;
      },
    });
    console.log("tax codes", typeof result, result);
    return {
      intuit_connection: intuitConnection,
      taxCodes: result?.QueryResponse?.TaxCode || [],
      error: null,
    };
  } catch (e) {
    console.error("could not get tax codes", e);
    return {
      intuit_connection: intuitConnection,
      taxCodes: [],
      error: `failed to refresh accesstoken for intuit_connection ${intuitConnectionId}`,
    };
  }
};

export default function Page() {
  const response = useLoaderData<typeof loader>();
  return (
    <div className="app-container space-y-3">
      <div className="flex-wrap flex gap-3 items-center">
        <h1>Quickbooks: {response.intuit_connection.intuit_company?.CompanyName}</h1>
        <ActionForm>
          <RInput table={"intuit_connection"} field={"data.deleted_at"} value={at_now_value} type={"hidden"} />
          <RInput table={"intuit_connection"} field={"id"} value={response.intuit_connection.id} />
          <RedirectInput value={_connect(response.intuit_connection.sandbox ? "SANDBOX" : "PRODUCTION")} />
          <DeleteButton>Disconnect</DeleteButton>
        </ActionForm>
      </div>
      {response.error && <Alert status={"error"}>{response.error}</Alert>}
      <div>
        <p>Connected to:</p>
        <ul>
          {response.intuit_connection.connected_establishments.map((establishment) => (
            <li key={establishment.establishment_id}>
              <ParamLink className="link" path={_establishment_detail(establishment.establishment_id)}>
                {getEstablishmentName(establishment)}
              </ParamLink>
            </li>
          ))}
        </ul>
      </div>
      {response.taxCodes && (
        <div className="space-y-3">
          <h2 className="text-xl font-bold">Tax codes</h2>
          <table>
            <tr className="text-slate-500 border-b border-slate-200">
              <td className="p-2">Id</td>
              <td className="p-2">Name</td>
              <td className="p-2">Active</td>
              <td className="p-2">Description</td>
            </tr>
            {response.taxCodes.map((taxCode) => (
              <tr key={taxCode.Id} className="border-b border-slate-200">
                <td className="p-2">{taxCode.Id}</td>
                <td className="p-2">{taxCode.Name}</td>
                <td className="p-2">{taxCode.Active ? "yes" : "no"}</td>
                <td className="p-2">{taxCode.Description}</td>
                {/*<td>*/}
                {/*  <pre>{JSON.stringify(taxCode, null, 3)}</pre>*/}
                {/*</td>*/}
              </tr>
            ))}
          </table>
          <ActionForm onCheckEqual={defaultEqualCheck} className="space-y-3">
            <RInput table={"intuit_connection"} field={"id"} value={response.intuit_connection.id} />
            <div>
              <RLabel table={"intuit_connection"} field={"data.default_intuit_tax_code_id"}>
                Set default tax code
              </RLabel>
              <br />
              <RSelect
                table={"intuit_connection"}
                field={"data.default_intuit_tax_code_id"}
                className="select"
                defaultValue={response.intuit_connection.default_intuit_tax_code_id || ""}
              >
                <option value={""}>No default</option>
                {response.taxCodes.map((taxCode) => (
                  <option key={taxCode.Id} value={taxCode.Id}>
                    {taxCode.Name}
                  </option>
                ))}
              </RSelect>
            </div>
            <SubmitButton className="btn btn-primary">Save</SubmitButton>
          </ActionForm>
        </div>
      )}
      {/*{response.products && (*/}
      {/*  <div>*/}
      {/*    <pre>{JSON.stringify(response.products.QueryResponse.Item, null, 3)}</pre>*/}
      {/*  </div>*/}
      {/*)}*/}
    </div>
  );
}
