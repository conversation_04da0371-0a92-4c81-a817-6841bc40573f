import type { DataFunctionArgs } from "@remix-run/server-runtime";
import { useLoaderData } from "@remix-run/react";
import React from "react";
import { kysely } from "~/misc/database.server";
import type { AsyncReturnType } from "type-fest";
import { getSessionSimple } from "~/utils/session.server";
import { activeUserSessionSimple } from "~/domain/member/member-queries.server";
import { at_infinity_value } from "~/kysely/db-static-vars";

export const loader = async ({ request }: DataFunctionArgs) => {
  const { session_id } = await getSessionSimple(request);
  const user = await activeUserSessionSimple(kysely, session_id, true).select("_user.admin").executeTakeFirst();
  if (!user?.admin)
    return {
      count: 0,
      list: [],
    };

  const baseQb = kysely.selectFrom("session").where("session.destroyed_at", "=", at_infinity_value);

  const countResult = await baseQb.select((eb) => eb.fn.count("id").as("count")).executeTakeFirstOrThrow();

  const count = countResult.count;

  const sessions = await baseQb
    .selectAll()
    .orderBy((eb) => eb.fn.coalesce("session.updated_at", "session.created_at"), "desc")
    .limit(100)
    .execute();

  return {
    count: count,
    list: sessions,
  };
};

type LoaderResponse = AsyncReturnType<typeof loader>;

export default function Page() {
  const response = useLoaderData<LoaderResponse>();
  const list = response.list;
  const first = list[0];

  if (!first) return <div>No sessions</div>;

  const headers = Object.keys(first);
  return (
    <div className="overflow-x-scroll">
      <table>
        <thead>
          <tr>
            <td />
            {headers.map((header) => (
              <td key={header}>{header}</td>
            ))}
          </tr>
        </thead>
        <tbody>
          {list.map((item: any, index: number) => {
            const values = Object.values(item);
            return (
              <tr key={index}>
                <td>{index + 1}</td>
                {values.map((value: any, valueIndex) => (
                  <td key={valueIndex}>{typeof value === "object" ? JSON.stringify(value) : value}</td>
                ))}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}
