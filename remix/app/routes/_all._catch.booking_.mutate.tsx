import { RIn<PERSON>, <PERSON><PERSON><PERSON><PERSON>, R<PERSON><PERSON><PERSON><PERSON> } from "~/components/ResourceInputs";
import { SubmitButton } from "~/components/base/Button";
import { ActionAlert } from "~/components/ActionAlert";
import { AnimatingDiv, Backbutton } from "~/components/base/base";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { _booking_detail } from "~/misc/paths";
import React, { Fragment, useState } from "react";
import { tableIdRef } from "~/misc/helpers";
import { ActionForm } from "~/components/form/BaseFrom";
import { getSessionSimple } from "~/utils/session.server";
import type { QbArgs } from "~/domain/member/member-queries.server";
import { memberIsAdminQb, memberQb } from "~/domain/member/member-queries.server";
import { kysely } from "~/misc/database.server";
import { bookingQb } from "~/domain/booking/booking-queries";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { createPageOverwrites } from "~/misc/consts";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { notFound, unauthorized } from "~/misc/responses";
import { baseProductWithSelect, orderProduct } from "~/domain/product/product-queries.server";
import { useLoaderData } from "@remix-run/react";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { participantsNrArray } from "~/domain/planning/plannings-consts";
import { defaultCurrency } from "~/misc/vars";
import { MeetingInput } from "~/domain/booking/booking-components";
import { establishmentQb } from "~/domain/establishment/queries";
import { getDateFromParams } from "~/domain/planning/planning.helpers.server";
import { CallbackInput } from "~/domain/callback/callback.components";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { LoaderFunctionArgs } from "@remix-run/router";
import { arrayAgg } from "~/kysely/kysely-helpers";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { BookingMessage } from "~/domain/booking/booking-message";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const ctx = await getSessionSimple(request);

  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  const date = getDateFromParams(request);

  const args: QbArgs = {
    trx: kysely,
    ctx: { session_id: ctx.session_id },
  };

  const productQb = baseProductWithSelect.$call((eb) => orderProduct(eb));
  const establishmentQ = establishmentQb.select((eb) => [
    eb.exists(memberIsAdminQb(args).where("_member.establishment_id", "=", eb.ref("establishment.id"))).as("allowed"),
    jsonArrayFrom(productQb.where("item.establishment_id", "=", eb.ref("establishment.id"))).as("products"),
  ]);

  const context = await kysely
    .selectNoFrom([
      jsonObjectFrom(establishmentQ.where("establishment.id", "=", state.establishment_id || null)).as("establishment"),
      jsonObjectFrom(
        bookingQb(kysely)
          .where("booking.id", "=", state.id || null)
          .select((eb) => [
            jsonObjectFrom(establishmentQ.where("establishment.id", "=", eb.ref("booking.establishment_id"))).as("establishment"),
            eb.exists(memberQb(args).where("_member.establishment_id", "=", eb.ref("booking.establishment_id"))).as("simple_view"),
            eb
              .selectFrom("participant")
              .innerJoin("participation", "participation.participant_id", "participant.id")
              .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
              .where("participant.booking_id", "=", eb.ref("booking.id"))
              .select((eb) => arrayAgg(eb.ref("participant.stay")).as("stays"))
              .where("participant.stay", "is not", null)
              .where((eb) =>
                eb.or([eb("participant.booking_id", "=", eb.ref("booking.id")), eb("sale_item.booking_id", "=", eb.ref("booking.id"))]),
              )
              .as("participant_stays"),
            jsonArrayFrom(
              eb
                .selectFrom("payment")
                .selectAll("payment")
                .whereRef("payment.booking_id", "=", "booking.id")
                .where("payment.deleted_at", "=", at_infinity_value)
                .orderBy("payment.created_at"),
            ).as("payments"),
          ]),
      ).as("booking"),
    ])
    .executeTakeFirst();

  if (!context) throw notFound();

  const booking = context.booking;
  const establishment = booking?.establishment || context.establishment;
  const products = establishment?.products || [];

  if (!establishment?.allowed) throw unauthorized();

  return {
    booking: booking,
    establishment: establishment,
    products: products,
    date: date,
    ...createPageOverwrites({ simple_view: false }),
  };
};

export default function Page() {
  const search = useSearchParams2();
  const data = useLoaderData<typeof loader>();
  const [nrOfParticipants, setNrOfParticipants] = useState(2);
  const booking = data.booking;
  const establishment = data.establishment;

  // const productId = search.state.product_id ?? booking?.product_id;
  // const tripId = search.state.trip_id;
  const establishmentId = search.state.establishment_id;
  // const selectedProduct = data.products.find((product) => product.id === productId);

  const [currency, setCurrency] = useState<string>("");
  const finalCurrency = currency || booking?.currency_id || establishment.default_currency || defaultCurrency;

  return (
    <main className="app-container">
      <h2 className="text-xl font-semibold text-slate-600">{getEstablishmentName(establishment)}</h2>
      <AnimatingDiv>
        <ActionForm className="space-y-2" replace>
          <ActionAlert />
          <div>
            {booking ? (
              <h1 className="text-2xl font-semibold">Edit booking</h1>
            ) : (
              <h1 className="text-2xl font-semibold">Create booking</h1>
            )}
          </div>
          {booking ? (
            <Fragment>
              <RInput table={"booking"} field={"id"} value={booking.id} />
            </Fragment>
          ) : (
            <Fragment>
              <CallbackInput callbackName={"notify_booking_page"} target_id={tableIdRef("booking")} />
              <RInput table={"booking"} field={"data.establishment_id"} type={"hidden"} value={establishmentId || ""} />
            </Fragment>
          )}
          {/*<RInput table={"booking"} field={"data.product_id"} type={"hidden"} value={productId || ""} />*/}
          <RedirectParamsInput path={_booking_detail(tableIdRef("booking"))} paramState={{}} />
          <div className="space-y-3">
            <div>
              <RInput
                table={"booking"}
                field={"data.booking_reference"}
                label={"Booking reference"}
                className="input"
                defaultValue={booking?.booking_reference || ""}
              />
            </div>
            {!booking && (
              <div className="flex-1 flex-wrap gap-3">
                <div>
                  <label className={"required"}>Nr of participants</label>
                  <br />
                  <select
                    className="select w-full"
                    required
                    value={nrOfParticipants}
                    onChange={(e) => setNrOfParticipants(Number(e.target.value))}
                  >
                    {participantsNrArray.map((nr) => (
                      <option key={nr} value={nr + ""}>
                        {nr}
                      </option>
                    ))}
                  </select>
                  {/*{Array.from({ length: nrOfParticipants }).map((_, index) => (*/}
                  {/*  <Fragment key={index}>*/}
                  {/*    <RInput*/}
                  {/*      table={"participant"}*/}
                  {/*      field={"data.establishment_id"}*/}
                  {/*      index={index}*/}
                  {/*      value={establishmentId || ""}*/}
                  {/*      type={"hidden"}*/}
                  {/*    />*/}
                  {/*    <RInput table={"participant"} field={"data.booking_id"} index={index} value={tableRef("booking")} type={"hidden"} />*/}
                  {/*    {tripId && (*/}
                  {/*      <Fragment>*/}
                  {/*        <RInput table={"trip_assignment"} field={"data.trip_id"} index={index} type={"hidden"} value={tripId} />*/}
                  {/*        <RInput*/}
                  {/*          table={"trip_assignment"}*/}
                  {/*          field={"data.role"}*/}
                  {/*          index={index}*/}
                  {/*          type={"hidden"}*/}
                  {/*          value={"instructor" satisfies Role}*/}
                  {/*        />*/}
                  {/*        <RInput*/}
                  {/*          table={"trip_assignment"}*/}
                  {/*          field={"data.participant_id"}*/}
                  {/*          index={index}*/}
                  {/*          type={"hidden"}*/}
                  {/*          value={tableRef("participant", index)}*/}
                  {/*        />*/}
                  {/*      </Fragment>*/}
                  {/*    )}*/}
                  {/*  </Fragment>*/}
                  {/*))}*/}
                </div>
              </div>
            )}
            <div className="space-y-6">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
                <MeetingInput operator_address={establishment?.address} default={booking} country_code={establishment?.country_code} />
              </div>
              {establishment && <BookingMessage establishment={establishment} booking={booking} />}
              <div>
                <div className="flex flex-row items-center">
                  <RLabel table={"booking"} field={"data.internal_note"}>
                    Internal notes
                  </RLabel>
                  <span className="flex-1 text-right">** Only visible to staff **</span>
                </div>
                <RTextarea
                  table={"booking"}
                  field={"data.internal_note"}
                  className="input"
                  rows={3}
                  placeholder="Type an optional internal note here"
                  defaultValue={booking?.internal_note || ""}
                />
              </div>
              <div>
                <RInput
                  table={"booking"}
                  field={"data.booking_source"}
                  className="input"
                  label={"Booking Source"}
                  defaultValue={booking?.booking_source || ""}
                />
              </div>
              <div className="flex-row items-center gap-2 flex">
                <RLabel table={"booking"} field={"data.hide_price_for_customer"}>
                  Hide Price for Customer
                </RLabel>
                <RInput
                  table={"booking"}
                  field={"data.hide_price_for_customer"}
                  className="checkbox"
                  type={"checkbox"}
                  hiddenType={"__boolean__"}
                  defaultChecked={!!booking?.hide_price_for_customer}
                />
              </div>
            </div>
          </div>

          {/*<input type={"date"} defaultValue={duration[0] || ""} />*/}
          {/*<input type={"date"} defaultValue={duration[1] || ""} />*/}
          <div className="pt-5 flex flex-row justify-end gap-3">
            <Backbutton className="btn hover:underline">Cancel</Backbutton>
            <SubmitButton className="btn btn-primary">Submit</SubmitButton>
          </div>
        </ActionForm>
      </AnimatingDiv>
    </main>
  );
}
