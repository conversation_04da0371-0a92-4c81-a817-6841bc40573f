import { type MetaFunction, useLoaderData } from "@remix-run/react";
import { _participant_mutate, _register } from "~/misc/paths";
import { ParamLink } from "~/components/meta/CustomComponents";
import { IkImage } from "~/components/IkImage";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { filesQuery } from "~/domain/file/file-queries";
import { getFullUrl } from "~/misc/helpers";
import React from "react";
import { useAppContext } from "~/hooks/use-app-context";
import { DocumentDuplicateIcon, QrCodeIcon, ShareIcon } from "@heroicons/react/20/solid";
import { AnimatingDiv } from "~/components/base/base";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { toast } from "~/misc/toast";
import type { LoaderFunctionArgs } from "@remix-run/router";
import { QRCodeSVG } from "qrcode.react";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { createPageOverwrites, traveltrusterName } from "~/misc/consts";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { ascNullsLast } from "~/kysely/kysely-helpers";
import { getSessionSimple } from "~/utils/session.server";
import { memberQb, toArgs } from "~/domain/member/member-queries.server";
import { kysely } from "~/misc/database.server";
import { robotsMeta } from "~/misc/web-helpers";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const ctx = await getSessionSimple(request);
  const establishmentId = params.id!;

  const establishment = await simpleEstablishmentQb
    .select(["establishment.id", "establishment.bio", "establishment.about", "operator.name as operator_name"])
    .where("establishment.id", "=", establishmentId)
    .select((eb) => [
      eb("establishment.id", "not in", memberQb(toArgs(kysely, ctx.session_id)).select("_member.establishment_id")).as("simple_view"),
      jsonArrayFrom(
        eb
          .selectFrom("form")
          .whereRef("form.establishment_id", "=", "establishment.id")
          .where("form.selectable", "=", true)
          .where("form.deleted_at", "=", at_infinity_value)
          .orderBy("form.sort_order", ascNullsLast)
          .orderBy("form.name asc")
          .selectAll("form")
          .select((eb) => jsonObjectFrom(filesQuery("form").where("file_target.target_id", "=", eb.ref("form.id")).limit(1)).as("image")),
      ).as("forms"),
    ])
    .executeTakeFirstOrThrow();

  return {
    establishment: establishment,
    ...createPageOverwrites({
      simple_view: !!establishment.simple_view,
    }),
  };
};

export const meta: MetaFunction<typeof loader> = (args) => {
  const data = args.data;
  const establishment = data?.establishment;
  return [
    robotsMeta("noindex"),
    { title: `Register at ` + (establishment ? getEstablishmentName(establishment) : traveltrusterName) },
    { description: establishment?.bio },
    { "og:description": establishment?.bio || establishment?.about },
    // bucketUrl ? { "og:image": imagePath && createImageUrl(bucketUrl, imagePath, 300, 200) } : {},
  ];
};

export default function Page() {
  const search = useSearchParams2();
  const app = useAppContext();
  const data = useLoaderData<typeof loader>();
  const registrationUrl = getFullUrl(app.host) + _register(data.establishment.id);
  return (
    <div className="app-container space-y-6">
      <h2 className="text-xl font-semibold">Registration form(s)</h2>
      <hr />
      <p className="text-slate-700 text-xl">
        Welcome! To start your registration process, please select an option that best describes you.
      </p>
      {/*{data.forms}*/}
      <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
        {data.establishment.forms.map((form) => (
          <div key={form.id}>
            <ParamLink
              className="rounded-md bg-slate-200 hover:opacity-60 block overflow-hidden"
              path={_participant_mutate}
              paramState={{ persist_establishment_id: form.establishment_id, form_id: form.id }}
            >
              {form.image && <IkImage w={540} h={200} className="h-[200px] w-full rounded-md object-cover" path={form.image.filename} />}
              <div className="p-2 py-3 font-semibold text-xl text-center">{form.name}</div>
            </ParamLink>
          </div>
        ))}
      </div>
      <hr />
      <AnimatingDiv className="space-y-6">
        <h2 className="font-semibold text-xl">Share registration URL</h2>
        <div className="flex flex-row gap-6  text-slate-600">
          <a
            rel={"noreferrer"}
            href={registrationUrl}
            target={"_blank"}
            className={" hover:opacity-80  flex flex-col justify-center items-center"}
            onClick={(e) => {
              try {
                if (navigator.clipboard) {
                  e.preventDefault();
                  navigator.clipboard.writeText(registrationUrl);
                  toast("URL copied");
                } else {
                  console.log("both share and clipboard apis are not supported");
                }
              } catch (e) {
                console.log("could not handle onclick copy", e);
              }
            }}
          >
            <DocumentDuplicateIcon className="h-10 w-10" />
            <span>Copy</span>
          </a>
          <ParamLink
            className="hover:opacity-80 flex flex-col justify-center items-center"
            paramState={{ toggle_qr: !search.state.toggle_qr }}
          >
            <QrCodeIcon className="h-10 w-10" />
            <span>{search.state.toggle_qr ? "Hide" : "Show"} QR</span>
            {/*<QRCodeSVG*/}
            {/*  height={50}*/}
            {/*  width={50}*/}
            {/*  value={registrationUrl}*/}
            {/*/>*/}
          </ParamLink>
          <a
            rel={"noreferrer"}
            href={registrationUrl}
            target={"_blank"}
            className={"flex flex-col justify-center items-center hover:opacity-80"}
            onClick={(e) => {
              try {
                if (navigator.share) {
                  e.preventDefault();
                  navigator.share({ title: "Registration", text: "Registration", url: registrationUrl });
                } else if (navigator.clipboard) {
                  e.preventDefault();
                  navigator.clipboard.writeText(registrationUrl);
                  toast("URL copied, share is not supported for this browser");
                } else {
                  console.log("both share and clipboard apis are not supported");
                }
              } catch (e) {
                console.log("could not handle onclick copy", e);
              }
            }}
          >
            <ShareIcon className="h-10 w-10" />
            <span>Share</span>
          </a>
        </div>
        {search.state.toggle_qr && (
          <div>
            <QRCodeSVG height={300} width={300} value={registrationUrl} />
          </div>
        )}
      </AnimatingDiv>
    </div>
  );
}
