import type { MetaFunction } from "@remix-run/react";
import { useLoaderData } from "@remix-run/react";
import { AnimatingDiv } from "~/components/base/base";
import { _booking_detail } from "~/misc/paths";
import { ParamLink } from "~/components/meta/CustomComponents";
import { activities } from "~/domain/activity/activity";
import { UserIcon } from "@heroicons/react/20/solid";
import { RInput, RLabel, RSelect, RTextarea } from "~/components/ResourceInputs";
import React from "react";
import { SubmitButton } from "~/components/base/Button";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { ActionForm } from "~/components/form/BaseFrom";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { getSessionSimple } from "~/utils/session.server";
import { memberIsAdminQb, QbArgs } from "~/domain/member/member-queries.server";
import { kysely } from "~/misc/database.server";
import { bookingQb } from "~/domain/booking/booking-queries";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { traveltrusterName } from "~/misc/consts";
import { establishmentQb } from "~/domain/establishment/queries";
import { notFound, notFoundOrUnauthorzied } from "~/misc/responses";
import { createImageUrl } from "~/components/IkImage";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { useAppContext } from "~/hooks/use-app-context";
import { saleItemWithProductQb } from "~/domain/activity/activity-queries";
import { participantQb, participantSimpleQb, participationQb } from "~/domain/participant/participant.queries.server";
import { sql } from "kysely";
import { arrayAgg, dateRange } from "~/kysely/kysely-helpers";
import type { LoaderFunctionArgs } from "@remix-run/router";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { baseProductWithSelect } from "~/domain/product/product-queries.server";
import { BasicCountrySelect } from "~/components/CountrySelect";
import { fName, tableIdRef } from "~/misc/helpers";
import { getCountry } from "~/data/countries";
import { Tooltip } from "~/components/base/tooltip";
import { IoInformationCircleOutline } from "react-icons/io5";
import { getVatSentence } from "~/domain/invoice/invoice-vars";
import { Alert } from "~/components/base/alert";
import { CallbackInput } from "~/domain/callback/callback.components";
import { getAppContextInMeta } from "~/misc/route-helpers";
import { robotsMeta } from "~/misc/web-helpers";

export { action } from "~/routes/_all._catch.resource";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const bookingId = params.booking_id!;

  const ctx = await getSessionSimple(request);

  const args: QbArgs = {
    trx: kysely,
    ctx: { session_id: ctx.session_id },
  };

  const booking = await bookingQb(kysely)
    .where("booking.id", "=", bookingId)
    .where("booking.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"))
    .select((eb) => [
      eb
        .selectFrom("payment")
        .innerJoin("payment_method", "payment_method.id", "payment.payment_method_id")
        .where("payment.deleted_at", "=", at_infinity_value)
        .where("payment_method.deleted_at", "=", at_infinity_value)
        .where("payment.booking_id", "=", eb.ref("booking.id"))
        .limit(1)
        .select("payment_method.intuit_connection_id")
        .orderBy("payment.created_at asc")
        .as("default_intuit_connection_id"),
      jsonArrayFrom(
        eb
          .selectFrom("intuit_connection")
          .select(["intuit_connection.id", "intuit_connection.intuit_company", "intuit_connection.intuit_user"])
          .where("intuit_connection.deleted_at", "=", at_infinity_value)
          .where(
            "intuit_connection.id",
            "in",
            eb
              .selectFrom("payment_method")
              .where("payment_method.establishment_id", "=", eb.ref("booking.establishment_id"))
              .select("payment_method.intuit_connection_id"),
          ),
      ).as("intuit_connections"),
      eb
        .selectFrom("participant")
        .select((eb) => arrayAgg(eb.ref("participant.stay")).as("stays"))
        .innerJoin("participation", "participation.participant_id", "participant.id")
        .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
        .where("participant.stay", "is not", null)
        .where("sale_item.booking_id", "=", eb.ref("booking.id"))
        .as("participant_stays"),
      jsonArrayFrom(
        participantQb(kysely).where(
          "participant.id",
          "in",
          eb
            .selectFrom("participation")
            .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
            .select("participation.participant_id")
            .where("sale_item.booking_id", "=", eb.ref("booking.id")),
        ),
      ).as("participants"),
      jsonObjectFrom(
        establishmentQb
          .where("establishment.id", "=", eb.ref("booking.establishment_id"))
          .select((eb) => [
            jsonArrayFrom(baseProductWithSelect.where("item.establishment_id", "=", eb.ref("establishment.id"))).as("products"),
          ]),
      ).as("establishment"),
      jsonArrayFrom(
        saleItemWithProductQb
          .leftJoin("product", "product.id", "sale_item.product_id")
          .leftJoin("item", "item.id", "product.item_id")
          .where("sale_item.booking_id", "=", eb.ref("booking.id"))
          .select((eb) => [
            jsonObjectFrom(
              eb
                .selectFrom("form")
                .where("form.root_id", "=", eb.ref("item.form_root_id"))
                .where("form.deleted_at", "=", at_infinity_value)
                .selectAll("form"),
            ).as("latest_form"),
            jsonArrayFrom(
              eb
                .selectFrom("trip_assignment")
                .innerJoin("trip", "trip.id", "trip_assignment.trip_id")
                .innerJoin("participation", "participation.id", "trip_assignment.participation_id")
                .selectAll("trip_assignment")
                .select((eb) => [
                  "trip.date as trip_date",
                  sql<boolean>`(${dateRange(eb.ref("trip.date"), eb.ref("trip.date"))} && ${eb.ref("sale_item.duration")})`.as(
                    "is_within_duration",
                  ),
                ])
                .where("participation.sale_item_id", "=", eb.ref("sale_item.id")),
            ).as("assignments"),
            jsonArrayFrom(
              participationQb
                .select((eb) => [
                  jsonArrayFrom(
                    eb
                      .selectFrom("trip_assignment")
                      .selectAll("trip_assignment")
                      .where("trip_assignment.participation_id", "=", eb.ref("participation.id")),
                  ).as("assignments"),
                ])
                .where("participation.sale_item_id", "=", eb.ref("sale_item.id")),
            ).as("participations"),
          ]),
      ).as("activities"),
      jsonArrayFrom(
        participantSimpleQb(kysely).where(
          "participant.id",
          "in",
          eb
            .selectFrom("participation")
            .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
            .select("participation.participant_id")
            .where("sale_item.booking_id", "=", eb.ref("booking.id")),
        ),
      ).as("participants"),
    ])
    .executeTakeFirst();

  if (!booking) throw notFoundOrUnauthorzied();
  const establishment = booking.establishment;
  if (!establishment) throw notFound("Establishment belonging to booking was not found");

  return {
    booking: {
      ...booking,
      establishment: establishment,
    },
  };
};

export const meta: MetaFunction<typeof loader> = (args) => {
  const parentData = getAppContextInMeta(args);
  const config = parentData?.env;
  const bucketUrl = config?.firebase_singapore.storageBucket;
  const data = args.data;
  const establishment = data?.booking.establishment;
  const imagePath = establishment?.files?.[0]?.filename;
  return [
    robotsMeta("noindex"),
    { title: `Booking at ` + (establishment ? getEstablishmentName(establishment) : traveltrusterName) },
    { description: establishment?.bio },
    { "og:description": establishment?.bio || establishment?.about },
    bucketUrl ? { "og:image": imagePath && createImageUrl(bucketUrl, imagePath, 300, 200) } : {},
  ];
};

const countrySelectid = "invoice-country-select";

export default function Page() {
  const context = useAppContext();
  const search = useSearchParams2();
  const response = useLoaderData<typeof loader>();

  const booking = response.booking;

  const selectedParticipant = booking.participants.find((participant) => participant.id === search.state.participant_id);

  const countryVatRate = getCountry(booking.establishment.country_code as any)?.vat_rate;
  console.log("booking", countryVatRate);
  return (
    <div className="space-y-3">
      <section className="app-container space-y-5">
        {booking.establishment && <h2 className="text-xl font-semibold text-slate-600">{getEstablishmentName(booking.establishment)}</h2>}
        <div className="space-y-2">
          <h1 className="text-2xl">Generate invoice</h1>
          <div className="flex flex-wrap gap-2 items-center text-slate-500">
            Booking {!!response.booking.booking_reference && <span>{response.booking.booking_reference}</span>}
            <span className="flex-1"></span>
            <span>{booking.sqid}</span>
          </div>
        </div>
        <ActionForm className="space-y-6">
          <RedirectParamsInput path={_booking_detail(booking.id)} paramState={{}} />
          <CallbackInput callbackName={"generate_invoice_pdf"} target_id={tableIdRef("invoice")} delay_in_seconds={1} />
          <CallbackInput callbackName={"send_invoice_to_intuit"} target_id={tableIdRef("invoice")} delay_in_seconds={1} />
          <div>
            {/*<h3 className="">Invoice recipient</h3>*/}
            <AnimatingDiv className="space-y-3">
              {booking.participants.length > 0 && (
                <ParamLink className="link" paramState={{ toggle_participant_select: !search.state.toggle_participant_select }}>
                  {search.state.toggle_participant_select ? "Prefill customer data" : "close"}
                </ParamLink>
              )}
              {!search.state.toggle_participant_select && (
                <div className="flex flex-wrap">
                  {/*<p>Prefill customer</p>*/}
                  {booking.participants.map((participant) => {
                    return (
                      <ParamLink
                        key={participant.id}
                        className="flex gap-2 items-center flex-row hover:bg-slate-50 p-1 rounded-md aria-busy:animate-pulse aria-busy:cursor-progress group aria-disabled:hover:bg-inherit group"
                        paramState={{ participant_id: participant.id, toggle_participant_select: true }}
                      >
                        <div className="text-slate-800 bg-slate-200 rounded-full p-0.5">
                          <UserIcon className="w-6 h-6" />
                        </div>
                        <div>
                          <p className="font-semibold group-aria-disabled:text-xl">
                            {participant.first_name} {participant.last_name}
                          </p>
                          {/*<p className="text-xs text-slate-600 group-aria-disabled:hidden">{participant.email}</p>*/}
                        </div>
                        <span className="flex-1" />
                        {/*<ChevronRightIcon className="w-5 h-5 text-slate-800 group-aria-disabled:opacity-0" />*/}
                      </ParamLink>
                    );
                  })}
                  {/*<ParamLink className="link">Clear entry</ParamLink>*/}
                </div>
              )}
            </AnimatingDiv>
          </div>
          <RInput table={"booking"} field={"id"} value={booking.id} />
          <RInput table={"invoice"} field={"data.booking_id"} value={tableIdRef("booking")} type={"hidden"} />
          <div className="space-y-3">
            <div key={search.state.participant_id || ""} className="space-y-3">
              <div>
                <RInput
                  required
                  table={"invoice"}
                  field={"data.customer_name"}
                  label="Customer name"
                  className="input"
                  defaultValue={selectedParticipant ? selectedParticipant.first_name + " " + selectedParticipant.last_name : ""}
                />
              </div>
              <div>
                <RInput
                  table={"invoice"}
                  field={"data.customer_address"}
                  label={"Address"}
                  className="input"
                  defaultValue={selectedParticipant?.address || ""}
                />
              </div>
              <div>
                <label htmlFor={countrySelectid}>Country</label>
                <br />
                <BasicCountrySelect
                  id={countrySelectid}
                  name={fName("invoice", "data.customer_country_code")}
                  className="select"
                  defaultValue={selectedParticipant?.country_code || ""}
                />
              </div>
            </div>
            <div>
              <div className="flex flex-row gap-1 items-center">
                <RLabel table={"booking"} field={"data.vat_rate"}>
                  VAT Rate
                </RLabel>
                <Tooltip
                  description={
                    <div className="space-y-2">
                      {/*<p className="">VAT Percentage</p>*/}
                      <p>A message will be shown on the invoice to inform that prices are including VAT</p>
                      <p>
                        e.g. <strong>{getVatSentence(11)}</strong>
                      </p>
                      <p>A 0% VAT entry will hide this message on the invoice</p>
                    </div>
                  }
                >
                  <IoInformationCircleOutline className="h-5 w-5 text-primary" />
                </Tooltip>
              </div>
              <div>
                <RInput
                  className="input w-fit min-w-24"
                  table={"booking"}
                  field={"data.vat_rate"}
                  type={"number"}
                  min={0}
                  max={100}
                  step={0.1}
                  defaultValue={booking.vat_rate ?? countryVatRate ?? "0"}
                />
                <span> %</span>
              </div>
            </div>
            <div>
              <RLabel table={"invoice"} field={"data.message"}>
                Message (optional)
              </RLabel>
              <br />
              <RTextarea table={"invoice"} field={"data.message"} className="input min-h-24" placeholder="Type message" />
            </div>
            {!!booking.intuit_connections.length && (
              <div>
                <RLabel table={"invoice"} field={"data.intuit_connection_id"}>
                  Quickbooks Account
                </RLabel>
                <RSelect
                  table={"invoice"}
                  field={"data.intuit_connection_id"}
                  defaultValue={response.booking?.default_intuit_connection_id || ""}
                  className="select w-full"
                >
                  <option value={""}>Don't send to quickbooks</option>
                  {booking.intuit_connections.map((connection) => (
                    <option value={connection.id}>
                      {connection.intuit_company.CompanyName} ({connection.intuit_user.email})
                    </option>
                  ))}
                </RSelect>
              </div>
            )}
          </div>
          <Alert status={"warning"} className="space-y-2">
            <p>
              <strong>Note</strong>: Generating an invoice will lock this booking.
            </p>
            <p className="text-xs">
              Once generated, you cannot edit any details that affect the price of this booking. Conversely, deleting the invoice will
              unlock the booking.
            </p>
          </Alert>
          {/*<RInput table={"booking"} field={"data"} />*/}
          {/*<CancelBu*/}
          <div className="flex flex-row justify-end items-center gap-3">
            <ParamLink className="link px-3" path={_booking_detail(booking.id)}>
              Cancel
            </ParamLink>
            <SubmitButton className="btn btn-primary">Generate</SubmitButton>
          </div>
        </ActionForm>
        {/*{placeholders.length > 0 && !canEdit && (*/}
        {/*  <div className="app-container sticky top-0 px-0 py-2">*/}
        {/*    <div className="rounded-md bg-orange-200 p-3">Please register to confirm your booking</div>*/}
        {/*  </div>*/}
        {/*)}*/}
      </section>
    </div>
  );
}
