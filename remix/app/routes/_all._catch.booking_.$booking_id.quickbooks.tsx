import { redirect } from "@remix-run/server-runtime";
import { kysely } from "~/misc/database.server";
import { notFound } from "~/misc/responses";
import type { LoaderFunctionArgs } from "@remix-run/router";

export { action } from "~/routes/_all._catch.resource";

const maxRetryCount = 11;
const waitBeforeRetryInMs = 4000;

function delay(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export const loader = async (args: LoaderFunctionArgs) => {
  const bookingId = args.params.booking_id!;

  for (let retryCount = 0; retryCount < maxRetryCount; retryCount++) {
    const invoice = await kysely
      .selectFrom("invoice")
      .innerJoin("intuit_connection", "intuit_connection.id", "invoice.intuit_connection_id")
      .selectAll("invoice")
      .select("intuit_connection.realm_id")
      .where("invoice.intuit_invoice_id", "is not", null)
      .where("invoice.booking_id", "=", bookingId)
      .executeTakeFirst();

    if (invoice) {
      const url = `https://app.qbo.intuit.com/app/invoice?txnId=${invoice.intuit_invoice_id}&cid=${invoice.realm_id}`;
      throw redirect(url, 307);
    }
    await delay(waitBeforeRetryInMs);
  }

  throw notFound("File does not exist or is still generating");
};

export const Page = () => <div>Quickbooks invoice redirect</div>;
