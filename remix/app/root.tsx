import { withSentry } from "@sentry/remix";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>let, <PERSON><PERSON><PERSON>, ScrollRestoration, useLoaderData, useNavigation } from "@remix-run/react";

import mapboxGlStyles from "~/styles/mapbox-gl.css?url";
import mapboxGlDrawStyles from "~/styles/mapbox-gl-draw.css?url";
import styles from "~/../styles/app.css?url";

import React, { Fragment, ReactNode, StrictMode, Suspense, useMemo } from "react";
import { LinksFunction } from "@remix-run/server-runtime";
import { getSession } from "~/utils/session.server";
import { postEvent } from "~/domain/event/event-fetcher";
import { kysely } from "~/misc/database.server";
import { DefaultErrorBoundary } from "~/components/RoutDefaults";
import { CDialog } from "~/components/base/Dialog";
import { AccountContainer } from "~/components/account/AccountContainer";
import { AccountManageBox } from "~/components/account/AccountManageBox";
import type { LoaderFunctionArgs } from "@remix-run/router";
import { FirebaseProvider } from "~/components/context/FirebaseProvider";
import { ParamLink } from "~/components/meta/CustomComponents";
import { XMarkIcon } from "@heroicons/react/20/solid";
import { getDateFromParams } from "~/domain/planning/planning.helpers.server";

import { environment } from "~/config/environment.server";
import { appConfig } from "./config/config.server";
import { getMonthObj } from "~/domain/planning/plannings-helpers";
import { getWhitelabelFromHost } from "~/utils/domain-helpers";
import { filesQuery } from "~/domain/file/file-queries";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { getHost } from "~/misc/web-helpers";
import { useAppContext } from "~/hooks/use-app-context";
import { I18nProvider } from "@lingui/react";
import { setupI18n } from "@lingui/core";
import { paramsToRecord, resetResponseInput } from "~/misc/parsers/global-state-parsers";
import { sql } from "kysely";
import { defaultTimezone, getTimezone, Timezone } from "~/data/timezones";
import { useIkUrl } from "~/components/IkImage";
import { getLanguage } from "~/utils/request.server";
import { activeLanguages, languages } from "~/data/languages";
import { Toaster } from "~/misc/toast";
import { createMeta } from "~/misc/route-helpers";
import { simpleEstablishmentQb } from "~/domain/establishment/queries";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { sqid } from "~/kysely/kysely-helpers";

export { action } from "~/routes/_all._catch.resource";

export const config = { runtime: "nodejs", maxDuration: 300, memory: 3000 };

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const record = paramsToRecord(url.searchParams);
  const printAllowed = record.print_token === appConfig.PRINT_TOKEN;
  const finalLocale = getLanguage(request);
  const currenciesQb = kysely.selectFrom("currency").select(["id", "conversion_rate_usd", "decimals"]);

  const { messages } = await import(`./../generated/locales/${finalLocale}/messages.po`);

  const prefix = getWhitelabelFromHost(getHost(request));
  const prefixOperator = prefix
    ? await kysely
        .selectFrom("operator")
        .where("operator.slug", "=", prefix)
        .selectAll("operator")
        .select((eb) => [
          eb.fn
            .coalesce(
              eb
                .selectFrom("establishment")
                .where("establishment.operator_id", "=", eb.ref("operator.id"))
                .innerJoin("spot", "spot.id", "establishment.spot_id")
                .innerJoin("region", "region.id", "spot.region_id")
                .select("region.timezone")
                .limit(1),
              sql.raw<Timezone>(`'${defaultTimezone}'`),
            )
            .as("timezone"),
          jsonObjectFrom(filesQuery("operator_logo").where("file_target.target_id", "=", eb.ref("operator.id")).limit(1)).as("logo_file"),
          jsonObjectFrom(filesQuery("operator_favicon").where("file_target.target_id", "=", eb.ref("operator.id")).limit(1)).as(
            "favicon_file",
          ),
        ])
        .executeTakeFirstOrThrow()
    : null;

  const establishmentId = record.establishment_id || record.persist_establishment_id;
  const establishment = establishmentId
    ? await simpleEstablishmentQb
        .select((eb) => [
          "establishment.id",
          "establishment.tanks",
          "establishment.blends",
          jsonArrayFrom(
            eb
              .selectFrom("category")
              .selectAll("category")
              .select((eb) => [jsonArrayFrom(fileTargetsQb(kysely, "category", eb.ref("category.id")).limit(1)).as("files")])
              .whereRef("category.establishment_id", "=", "establishment.id"),
          ).as("categories"),
          jsonObjectFrom(
            eb
              .selectFrom("booking")
              .selectAll("booking")
              .select((eb) => sqid(eb.ref("booking.id_seq")).as("sqid"))
              .where("booking.id", "=", record.booking_id)
              .where("booking.establishment_id", "=", eb.ref("establishment.id")),
          ).as("booking"),
        ])
        .where("establishment.id", "=", establishmentId)
        .executeTakeFirst()
    : null;

  const [session, currencies] = await Promise.all([getSession(request), currenciesQb.execute()]);
  const context = session.context;
  const timezone = getTimezone(prefixOperator?.timezone);

  const date = getDateFromParams(request);
  const month = getMonthObj(date.dateParam);

  return {
    ...context,
    locale: finalLocale,
    currencies: currencies,
    operator: prefixOperator,
    establishment: establishment,
    protected: !!appConfig.ENV_SECRET,
    passed: !appConfig.ENV_SECRET || context.env_verified || printAllowed,
    environment: environment,
    host: getHost(request),
    env: appConfig.PUBLIC,
    prefix: prefix,
    // timezone: timezone,
    date: date,
    month: month,
    translations: messages,
  };
};

export const links: LinksFunction = () => {
  return [
    { rel: "stylesheet", href: styles },
    { rel: "stylesheet", href: mapboxGlStyles },
    { rel: "stylesheet", href: mapboxGlDrawStyles },
  ];
};

export const meta = createMeta({ title: "" });

const GAScript = () => {
  const data = useLoaderData<typeof loader>();
  const gaId = data?.env?.GOOGLE_ANALYTICS_MEASUREMENT_ID;
  if (!gaId) return <Fragment />;
  return (
    <>
      <script async src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`} />
      <script
        dangerouslySetInnerHTML={{
          __html: `
                                window.dataLayer = window.dataLayer || [];
                                function gtag(){dataLayer.push(arguments);}
                                gtag('js', new Date());
                                gtag('config', '${gaId}');
                            `,
        }}
      ></script>
    </>
  );
};

let url = "";
let lastSubmitted = "";
let timeout: any;
if (typeof MutationObserver !== "undefined") {
  const observer = new MutationObserver(() => {
    if (url !== location.href) {
      if (timeout) {
        clearTimeout(timeout);
      }
      const submit = location.href;
      timeout = setTimeout(() => {
        if (submit !== location.href || lastSubmitted === location.href) return;
        lastSubmitted = location.href;
        postEvent({ url: location.href, type: "page_view" });
      }, 3000);
    }
    url = location.href;
  });
  observer.observe(document, { subtree: true, childList: true });
}

// const tracker = new OpenReplay({
//   projectKey: publicConfig.openreplayProjectKey,
//   __DISABLE_SECURE_MODE: process.env.NODE_ENV === "development",
// });

const Layout = (props: { children: ReactNode; prefix?: string | null; isError?: boolean; favicon: string }) => {
  console.log("prefiex", props.prefix);
  console.log("error", props.isError);
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1, maximum-scale=1, viewport-fit=cover, initial-scale=1.0" />
        <link rel="icon" href={props.favicon} />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstaticom" />
        <link
          href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&display=swap"
          rel="stylesheet"
        />
        <link href="https://fonts.googleapis.com/css2?family=Allura&display=swap" rel="stylesheet" />
        <Meta />
        {!props.isError && <GAScript />}
        <Links />
      </head>
      <body>
        {props.children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
};

const GlobalModals = () => {
  const ctx = useAppContext();
  return (
    <Fragment>
      <CDialog dialogname={"language"}>
        <div className="space-y-6">
          <p className="text-xl text-slate-500 text-center">Select language</p>
          <div className="grid grid-cols-2 gap-3 ">
            {activeLanguages.map((langCode) => {
              const langauge = languages.find((language) => language.code === langCode);
              return (
                <div key={langCode}>
                  <ParamLink
                    key={langCode}
                    aria-selected={langCode === ctx.locale}
                    replace
                    paramState={{ toggle_modal: undefined, lang: langCode }}
                    reload
                    className={"btn aria-selected:bg-slate-100 hover:bg-slate-200 rounded-full"}
                  >
                    {langauge?.name} <span className="uppercase">({langCode})</span>
                  </ParamLink>
                </div>
              );
            })}
          </div>
        </div>
      </CDialog>
      <CDialog dialogname={"account"}>
        <div className="text-right">
          <ParamLink
            paramState={{
              toggle_modal: undefined,
              persist_flow: undefined,
              persist_authMethod: undefined,
              ...resetResponseInput,
            }}
            aria-label="close"
            className={"inline-block rounded-xl p-2 text-right hover:bg-slate-100 active:bg-slate-100"}
          >
            <XMarkIcon className="h-5 w-5" />
          </ParamLink>
        </div>
        <AccountContainer />
      </CDialog>
      <CDialog dialogname={"profile"}>
        <div>
          <div className="text-right">
            <ParamLink
              paramState={{ toggle_modal: undefined, persist_flow: undefined, persist_authMethod: undefined }}
              aria-label="close"
              className={"inline-block rounded-xl p-2 text-right hover:bg-slate-100 active:bg-slate-100"}
            >
              <XMarkIcon className="h-5 w-5" />
            </ParamLink>
          </div>
          <AccountManageBox />
        </div>
      </CDialog>
    </Fragment>
  );
};

const noFavicon =
  "data:image/x-icon;base64,AAABAAEAEBAQAAEABAAoAQAAFgAAACgAAAAQAAAAIAAAAAEABAAAAAAAgAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAA/4QAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAERERERERERERAAAAAAAAERAQAAAAAAEBEAEAAAAAEAEQABAAAAEAARAAAQAAEAABEAAAEAEAAAEQAAABEAAAARAAAAEQAAABEAAAEAEAAAEQAAEAABAAARAAEAAAAQABEAEAAAAAEAEQEAAAAAABAREAAAAAAAAREREREREREREAAAAAP/wAAF/6AABv9gAAd+4AAHveAAB9vgAAfn4AAH5+AAB9vgAAe94AAHfuAABv9gAAX/oAAD/8AAAAAAAA";

function App() {
  const app = useAppContext();
  const navigation = useNavigation();
  const { fromBucket } = useIkUrl();
  const isLoading = navigation.state === "loading";

  // if (typeof window === "undefined") {
  //   dynamicActivate(search.state.persist_lang || "fr");
  // }
  const i18nn = useMemo(() => {
    const nes = setupI18n();
    nes.loadAndActivate({ locale: app.locale, messages: app.translations });
    return nes;
  }, [app.locale]);
  // dynamicActivate(app.locale);
  //
  // useEffect(() => {
  //   dynamicActivate(app.locale);
  // }, [app.locale]);
  console.log("dates", app.month.previousMonth, app.month.nextMonth);

  const operatorFavicon = app.operator?.favicon_file?.filename;
  const faviconPath = operatorFavicon ? fromBucket(operatorFavicon, "tr:w-16,h-16") : "/favicon/tt/favicon.ico";
  return (
    <StrictMode>
      <Layout favicon={faviconPath} prefix={app.prefix}>
        <I18nProvider i18n={i18nn}>
          <FirebaseProvider>
            <Suspense fallback={<div>doing something</div>}>
              {isLoading && (
                <div className="fixed top-0 left-0 z-50 h-1 w-full">
                  <div className="animate-slide h-full -translate-x-full bg-primary"></div>
                </div>
              )}
              <GlobalModals />
              <div className="flex flex-col">
                <Outlet />
              </div>
              <Toaster />
            </Suspense>
          </FirebaseProvider>
        </I18nProvider>
      </Layout>
    </StrictMode>
  );
}

export default withSentry(App);

export const ErrorBoundary = () => {
  return (
    <Layout isError favicon={noFavicon}>
      <div className={"flex flex-col"}>
        {/*<div className="container relative z-10 mx-auto flex max-w-6xl flex-row space-x-3 p-3 md:px-0">*/}
        {/*  <BaseLink to={"/"}>Home</BaseLink>*/}
        {/*  <Spacer />*/}
        {/*</div>*/}
        <div className="app-container">
          <DefaultErrorBoundary />
        </div>
        <div className="app-container">
          <ParamLink path={"/"} className="text-primary hover:underline active:underline">
            Back to home
          </ParamLink>
        </div>
      </div>
    </Layout>
  );
};
