const environments = ["production", "preview", "development"] as const;

export type Environment = (typeof environments)[number];

export const environment: Environment = environments.find((env) => env === process.env.VERCEL_ENV) || "development";

console.log("running env: ", environment);

// const dbEnvParser = z
//   .object({
//     DB_URL: z.string(),
//   })
//   .or(
//     z.object({
//       DB_USER: z.string(),
//       DB_PASSWORD: z.string(),
//       DB_HOSTNAME: z.string(),
//       DB_PORT: z.string(),
//       DB_DATABASE: z.string(),
//     }),
//   );
//
// const envParser = dbEnvParser;
//
// export const parsedEnv = dbEnvParser.optional().parse(process.env);
