const envKeys = [
  "GOOGLE_SERVICE_ACCOUNT",
  "GOOGLE_RECAPTCHA_SECRET_KEY",
  "AWS_SECRET_ACCESS_KEY",
  "API_KEY",
  "SESSION_SECRET",
  "BASE_URL",
  "<PERSON>IS<PERSON>LE_MAIL_MOCK",
  "DIS<PERSON>LE_TASKS_MOCK",
  "DB_URL",
  "DB_USER",
  "DB_PASSWORD",
  "DB_HOSTNAME",
  "DB_PORT",
  "DB_DATABASE",
  "DB_POOL_MAX",
  "INTUIT_PRODUCTION_CLIENT_ID",
  "INTUIT_PRODUCTION_CLIENT_SECRET",
  "INTUIT_SANDBOX_CLIENT_ID",
  "INTUIT_SANDBOX_CLIENT_SECRET",
  "VERCEL_URL",
] as const;

type EnvKey = (typeof envKeys)[number];

export const getEnvVar = (key: EnvKey) => process.env[key];

export const requireEnvVar = (key: EnvKey) => {
  const value = process.env[key];
  if (!value) throw new Error(`Env variable ${key} is required`);
  return value;
};
