import type { FirebaseOptions } from "@firebase/app-types";
import { environment } from "~/config/environment.server";
import type { PoolConfig } from "pg";
import { getEnvVar, requireEnvVar } from "~/config/env.server.helpers";

interface PublicConfig {
  openreplayProjectKey: string;
  googleMapsPublicApiKey: string;
  mapBoxPublicApiKey: string;
  GOOGLE_OAUTH_CLIENT_ID: string;
  GOOGLE_ANALYTICS_MEASUREMENT_ID?: string;
  firebase_singapore: Required<Omit<FirebaseOptions, "databaseURL">>;
  INTUIT_PRODUCTION_CLIENT_ID?: string;
  INTUIT_SANDBOX_CLIENT_ID?: string;
}

interface Config {
  API_KEY: string;
  SESSION_SECRET: string;
  GOOGLE_SERVICE_ACCOUNT?: any;
  GOOGLE_RECAPTCHA_SECRET_KEY?: string;
  ENV_SECRET?: string;
  PRINT_TOKEN: string;
  DB: PoolConfig;
  AWS_ACCESS_KEY_ID: string;
  AWS_SECRET_ACCESS_KEY?: string;
  firebase_singapore: {
    clientEmail: string;
    projectId: string;
    privateKey: string;
  };
  MOCK_TASKS_ENABLED: boolean;
  MOCK_MAIL_ENABLED: boolean;
  PUBLIC: PublicConfig;
  INTUIT_SANDBOX_CLIENT_SECRET?: string;
  INTUIT_PRODUCTION_CLIENT_SECRET?: string;
  ALLOWED_EMAIL_SUFFIXES: string[];
}

const allowedEmailSuffixes = ["traveltruster.com", "diversdesk.com", "dinkelworks.nl", "dinkel.works"];

const sharedPublicConfig = {
  openreplayProjectKey: "Wzl6jJfE4yKbJj0JF9WV",
  googleMapsPublicApiKey: "AIzaSyDjUqZNWwUVJN1yoauMF167lM9kDWYgyH8",
  mapBoxPublicApiKey: "pk.eyJ1Ijoic2NhbWdlbSIsImEiOiJja2ZncGpkMWowODAzMndwOTFlcDR4dmc1In0.-E8JDQK_6u2-YfybYbxQzw",
  GOOGLE_OAUTH_CLIENT_ID: "************-067v890seuedrmei3bd7tajq3c9oq0ev.apps.googleusercontent.com",
  INTUIT_PRODUCTION_CLIENT_ID: getEnvVar("INTUIT_PRODUCTION_CLIENT_ID"),
  INTUIT_SANDBOX_CLIENT_ID: getEnvVar("INTUIT_SANDBOX_CLIENT_ID"),
} satisfies Partial<PublicConfig>;

const maxPool = getEnvVar("DB_POOL_MAX");
const db: PoolConfig = {
  connectionString: getEnvVar("DB_URL"),
  ssl: { rejectUnauthorized: false },
  max: maxPool ? Number(maxPool) : undefined,
};

const googleServiceAccountRaw = getEnvVar("GOOGLE_SERVICE_ACCOUNT");
const googleServiceAccount = googleServiceAccountRaw && JSON.parse(googleServiceAccountRaw);

const sharedServerConfig = {
  PRINT_TOKEN: "kGtmTcCYdMAdYglYMfo18ISl85OJ69P3qqaklsI2xWYLCxJ1usJQl6RAtO4hehLw",
  GOOGLE_RECAPTCHA_SECRET_KEY: getEnvVar("GOOGLE_RECAPTCHA_SECRET_KEY"),
  INTUIT_PRODUCTION_CLIENT_SECRET: getEnvVar("INTUIT_PRODUCTION_CLIENT_SECRET"),
  INTUIT_SANDBOX_CLIENT_SECRET: getEnvVar("INTUIT_SANDBOX_CLIENT_SECRET"),
} satisfies Partial<Config>;

const getConfig = (): Config => {
  if (environment === "development") {
    const defaultLocalDbConfig: PoolConfig = {
      connectionString: "postgresql://localhost:5432/traveltruster_dev",
      max: 10,
    };
    return {
      ...sharedServerConfig,
      ALLOWED_EMAIL_SUFFIXES: allowedEmailSuffixes,
      DB: db.connectionString ? db : defaultLocalDbConfig,
      AWS_ACCESS_KEY_ID: "AKIAZYFXQ7GANTVVGMG4",
      AWS_SECRET_ACCESS_KEY: getEnvVar("AWS_SECRET_ACCESS_KEY"),
      GOOGLE_SERVICE_ACCOUNT: googleServiceAccount,
      MOCK_MAIL_ENABLED: !getEnvVar("DISABLE_MAIL_MOCK"),
      MOCK_TASKS_ENABLED: !getEnvVar("DISABLE_TASKS_MOCK"),
      SESSION_SECRET: "blabla2",
      API_KEY: "mysecretapikey",
      firebase_singapore: {
        projectId: "traveltruster-singapore-test",
        privateKey:
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        clientEmail: "<EMAIL>",
      },
      PUBLIC: {
        ...sharedPublicConfig,
        firebase_singapore: {
          apiKey: "AIzaSyBO57VzbYhLQMkIHPD2Eo7WLBvnx9_JvMg",
          authDomain: "traveltruster-singapore-test.firebaseapp.com",
          projectId: "traveltruster-singapore-test",
          storageBucket: "traveltruster-singapore-test.appspot.com",
          messagingSenderId: "************",
          appId: "1:************:web:eece05a4a9ddbd761e67ed",
          measurementId: "G-J86YLQ5JDX",
        },
      },
    };
  }

  if (!googleServiceAccount) throw new Error("GOOGLE SERVICE ACCOUNT is missing from env or could not be parsed");

  if (environment === "preview") {
    return {
      ...sharedServerConfig,
      ALLOWED_EMAIL_SUFFIXES: allowedEmailSuffixes,
      // INTUIT_BASE_URL: intuit_base_url.sandbox,
      MOCK_MAIL_ENABLED: false,
      MOCK_TASKS_ENABLED: false,
      DB: db,
      AWS_ACCESS_KEY_ID: "AKIAZYFXQ7GANTVVGMG4",
      AWS_SECRET_ACCESS_KEY: requireEnvVar("AWS_SECRET_ACCESS_KEY"),
      GOOGLE_SERVICE_ACCOUNT: googleServiceAccount,
      // XENDIT_CONFIG: xenditDevTestConfig,
      // XENDIT_API_KEYS: "id:xnd_development_gFzbKXHwxvmOm39etYnvlTR3fMUTLXHqb6aPpFw91kcSLfbAeJEMOqjwEX2d",
      ENV_SECRET: "tt2023",
      API_KEY: "mysecretapikey",
      SESSION_SECRET: "blabla",
      firebase_singapore: {
        projectId: "traveltruster-singapore-test",
        privateKey:
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        clientEmail: "<EMAIL>",
      },
      PUBLIC: {
        ...sharedPublicConfig,
        // host: requireEnvVar("VERCEL_URL"),
        GOOGLE_ANALYTICS_MEASUREMENT_ID: "G-HK2PMBTNQ7",
        firebase_singapore: {
          apiKey: "AIzaSyBO57VzbYhLQMkIHPD2Eo7WLBvnx9_JvMg",
          authDomain: "traveltruster-singapore-test.firebaseapp.com",
          projectId: "traveltruster-singapore-test",
          storageBucket: "traveltruster-singapore-test.appspot.com",
          messagingSenderId: "************",
          appId: "1:************:web:eece05a4a9ddbd761e67ed",
          measurementId: "G-J86YLQ5JDX",
        },
      },
    };
  }
  return {
    ...sharedServerConfig,
    ALLOWED_EMAIL_SUFFIXES: [],
    // INTUIT_BASE_URL: intuit_base_url.production,
    MOCK_MAIL_ENABLED: false,
    MOCK_TASKS_ENABLED: false,
    DB: db,
    AWS_ACCESS_KEY_ID: "AKIAZYFXQ7GANTVVGMG4",
    AWS_SECRET_ACCESS_KEY: requireEnvVar("AWS_SECRET_ACCESS_KEY"),
    GOOGLE_SERVICE_ACCOUNT: googleServiceAccount,
    // XENDIT_CONFIG: requireEnvVar("XENDIT_CONFIG"),
    // XENDIT_API_KEYS: requireEnvVar("XENDIT_API_KEYS"),
    API_KEY: requireEnvVar("API_KEY"),
    SESSION_SECRET: requireEnvVar("SESSION_SECRET"),
    firebase_singapore: {
      privateKey:
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      projectId: "traveltruster-singapore",
      clientEmail: "<EMAIL>",
    },
    PUBLIC: {
      ...sharedPublicConfig,
      // host: requireEnvVar("VERCEL_URL"),
      GOOGLE_ANALYTICS_MEASUREMENT_ID: "G-SF9WNSFKQ0",
      firebase_singapore: {
        apiKey: "AIzaSyDljWR0uidzFXJsHHXhkUnj2ZC3xXjn3Hc",
        authDomain: "traveltruster-singapore.firebaseapp.com",
        projectId: "traveltruster-singapore",
        storageBucket: "traveltruster-singapore.appspot.com",
        messagingSenderId: "1066339040018",
        appId: "1:1066339040018:web:401f29d5aed68a806711cb",
        measurementId: "G-WSF7VT9KEJ",
      },
    },
  };
};

export const appConfig = getConfig();
