import React, {
  createContext,
  Dispatch,
  Fragment,
  ReactNode,
  RefObject,
  SetStateAction,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import { weekDays } from "~/misc/vars";
import { activeActivitySlugs, activities } from "~/domain/activity/activity";
import { format } from "date-fns";
import { useAppContext } from "~/hooks/use-app-context";
import { toUtc } from "~/misc/date-helpers";

interface MouseDragState {
  start: [x: number, y: number] | null;
  current: [x: number, y: number] | null;
}

interface CalendarContextModel {
  parentRef?: RefObject<HTMLDivElement>;
  state: MouseDragState;
  setState: Dispatch<SetStateAction<MouseDragState>>;
}

export interface CalendarItemArgs {
  date: Date;
  dateStr: string;
  isToday: boolean;
  isSelected: boolean;
  isMonth: boolean;
  ctx: CalendarContextModel;
}

interface Props {
  item: (args: CalendarItemArgs) => ReactNode;
}

export const CalendarItems = (props: Props) => {
  const calendarCtx = useCalendarContext();
  const { month, date } = useAppContext();
  const selectedDateValue = date.dateParam;
  const selectedMonthValue = selectedDateValue.slice(0, 7);
  return (
    <Fragment>
      {month.weeks.map((week, weekIndex) => {
        return (
          <Fragment key={weekIndex}>
            <div className="bg-secondary-50 p-2 text-xs text-slate-800">{week.number}</div>
            {week.dates.map((day, dayIndex) => {
              // const dayNumberDiff = dayIndex + weekIndex * nrOfDaysInWeek - startofMonthDate.getDay();
              // const date = addTimezoneOffset(addDays(startofMonthDate, dayNumberDiff), defaultTimezone);
              const dayDate = toUtc(day);
              const dayDateValue = format(dayDate, "yyyy-MM-dd");
              const dayMonthValue = dayDateValue.slice(0, 7);

              return props.item({
                dateStr: dayDateValue,
                date: dayDate,
                isSelected: dayDateValue === selectedDateValue,
                isMonth: dayMonthValue === selectedMonthValue,
                isToday: dayDateValue === date.todayParam,
                ctx: calendarCtx,
              });
            })}
          </Fragment>
        );
      })}
    </Fragment>
  );
};

const CalendarContext = createContext<CalendarContextModel>({
  setState: () => "sdf",
  state: {
    start: null,
    current: null,
  },
});

export const getIsSelected = (rect: DOMRect, dragStart: [x: number, y: number], dragCurrent: [x: number, y: number]) => {
  const top = dragCurrent[1] > dragStart[1] ? dragCurrent : dragStart;
  const bottom = top === dragCurrent ? dragStart : dragCurrent;
  const [topX, topY] = top;
  const [bottomX, bottomY] = bottom;

  const startX = dragStart[0];
  const currentX = dragCurrent[0];
  const minX = Math.min(startX, currentX);
  const maxX = Math.max(startX, currentX);

  const withinTopY = yIsWithingRect(rect, topY);
  const withinBottomY = yIsWithingRect(rect, bottomY);

  if (withinTopY && withinBottomY) {
    return xIsWithingRect(rect, topX) || xIsWithingRect(rect, bottomX) || (rect.left > minX && rect.right < maxX);
  }
  if (!withinTopY && !withinBottomY && rect.bottom > bottomY && rect.top < topY) return true;

  if (withinTopY && rect.left < topX) return true;
  return withinBottomY && rect.right > bottomX;
};

export const getFromTo = (el: HTMLDivElement, start: [x: number, y: number], end: [x: number, y: number]) => {
  let startDate = "";
  let endDate = "";
  Array.from(el.childNodes.values()).forEach((node) => {
    if (!(node instanceof HTMLDivElement)) return;
    if (!getIsSelected(node.getBoundingClientRect(), start, end)) return;
    const date = node.getAttribute("data-date");
    if (!date) return;
    if (!startDate) {
      startDate = date;
    }
    endDate = date;
  });
  return { from: startDate, to: endDate };
};

export const Calendar = (props: { children: ReactNode; onDateSelected?: (startDate: string, endDate: string) => void }) => {
  const { onDateSelected } = props;
  const parentGridRef = useRef<HTMLDivElement>(null);
  const [calendarState, setCalendarState] = useState<MouseDragState>({ start: null, current: null });
  // const [isDragging, setIsDragging] = useState(false)

  useEffect(() => {
    const handleTouchMove = (e: TouchEvent) => {
      const x = e.touches[0]?.clientX;
      const y = e.touches[0]?.clientY;
      if (!x || !y) return;
      setCalendarState((s) => ({ start: s.start, current: [x, y] }));
    };
    const handleMouseMove = (e: MouseEvent) => {
      // console.log("ndoe", window.getSelection()?.getRangeAt(0));
      // console.log("xy", [e.x, e.y]);
      setCalendarState((s) => ({ start: s.start, current: [e.x, e.y] }));
    };
    // document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("touchmove", handleTouchMove);

    const handleMouseUp = () => {
      if (!calendarState.start || !calendarState.current) return;
      setCalendarState((s) => ({ start: null, current: null }));
      const parentEl = parentGridRef.current;
      const dragStart = calendarState.start;
      const dragCurrent = calendarState.current;
      console.log("handlmouups");
      if (dragStart && dragCurrent && parentEl) {
        const { from, to } = getFromTo(parentEl, dragStart, dragCurrent);
        return onDateSelected?.(from, to);
      }
    };
    // document.addEventListener("mouseup", handleMouseUp);
    // document.addEventListener("touchend", handleMouseUp);
    // if (calendarState.start) {
    //
    // }

    return () => {
      document.removeEventListener("touchend", handleMouseUp);
      document.removeEventListener("touchmove", handleTouchMove);
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [calendarState, onDateSelected, setCalendarState]);

  return (
    <CalendarContext.Provider value={{ setState: setCalendarState, state: calendarState, parentRef: parentGridRef }}>
      <div ref={parentGridRef} className="grid" style={{ gridTemplateColumns: `auto ${weekDays.map((_) => "minmax(0, 1fr)").join(" ")}` }}>
        <div />
        {weekDays.map((day, index) => {
          return (
            <div className="items-start border-b py-1 border-slate-200 text-center text-xs font-bold capitalize" key={index}>
              {day.key}
            </div>
          );
        })}
        {props.children}
      </div>
    </CalendarContext.Provider>
  );
};

export const useCalendarContext = () => useContext(CalendarContext);

export const CalendarLegend = () => (
  <div className="max-lg:px-3 grid grid-cols-2 gap-3 @md:grid-cols-4">
    {activeActivitySlugs
      .map((slug) => activities[slug])
      .map((activity) => (
        <span className="rounded-md p-1 text-xs text-white text-center" key={activity.name} style={{ backgroundColor: activity.color }}>
          {activity.name}
        </span>
      ))}
  </div>
);
export const xIsWithingRect = (rect: DOMRect, x: number) => x > rect.left && x < rect.right;
export const yIsWithingRect = (rect: DOMRect, y: number) => y > rect.top && y < rect.bottom;
