import { <PERSON>, <PERSON><PERSON>, Html, <PERSON>, Section, Text } from "@react-email/components";
import { Fragment } from "react";
import { TailwindBody } from "~/components/Mail";
import { getHost } from "~/misc/web-helpers";
import { getWhitelabelFromHost } from "~/utils/domain-helpers";
import { createMim, getMailContextForPrefix } from "~/server/mail/email.client.server";
import { addToNow, nowValue } from "~/kysely/kysely-helpers";
import { randomInt } from "crypto";
import { ResourceError } from "~/utils/error";
import { otpValidDurtionInMinutes } from "~/domain/otp/otp-vars";
import { Args } from "~/server/resource/resource-helpers.server";

export const oneTimePasswordResource: Args<"one_time_password"> = {
  authorize(args) {
    return (
      args.trx
        .selectFrom("one_time_password")
        .where("one_time_password.session_id", "=", args.ctx.session_id)
        .where("one_time_password.id", "=", args.id)
        // .where("one_time_password.verified_at", "is", null)
        .executeTakeFirst()
    );
  },
  insert: async (args) => {
    args.after_mutations.insideTransaction.set("sendOneTimePasswordMail" + args.id, async () => {
      const oneTimePassword = await args.trx
        .selectFrom("one_time_password")
        .innerJoin("user", "user.id", "one_time_password.user_id")
        .select(["one_time_password.password", "user.email"])
        .where("one_time_password.id", "=", args.id)
        .executeTakeFirstOrThrow();
      const operatorPrefix = getWhitelabelFromHost(getHost(args.request));
      const mailSender = await getMailContextForPrefix(args.trx, operatorPrefix);

      const mailTmp = (
        <Html>
          <Head />
          <TailwindBody>
            <Section>
              <Text>Hi there!</Text>
              <Text>Here is your one-time password (OTP):</Text>
              <Text>
                <strong>{oneTimePassword.password}</strong>
              </Text>
              <Text>
                This code is valid for one use only and will expire in {otpValidDurtionInMinutes} minutes. For your security, please do not
                share it with anyone.
              </Text>
              <Text>If you didn’t request this verification, kindly disregard this email.</Text>
              <Text>Kind regards,</Text>
              <Text>Team {mailSender.sender.name}</Text>
            </Section>
            {mailSender.contact && (
              <Fragment>
                <Hr />
                <Section className="text-xs text-slate-600">
                  <Text className="text-xs">
                    This non-reply email was automatically generated by &copy;Diversdesk on behalf of {mailSender.contact.name}.<br />
                    {mailSender.contact.addr && (
                      <Fragment>
                        For questions, please feel free to reach out to{" "}
                        <Link href={`mailto:${mailSender.contact.addr}`}>{mailSender.contact.addr}</Link>
                      </Fragment>
                    )}
                  </Text>
                </Section>
              </Fragment>
            )}
          </TailwindBody>
        </Html>
      );
      const { msg, send } = await createMim(mailTmp);
      msg.setSubject("One Time Password");
      msg.setSender(mailSender.sender);
      msg.setTo(oneTimePassword.email);
      await send();
    });

    // https://dev.to/mahendra_singh_7500/generating-a-secure-6-digit-otp-in-javascript-and-nodejs-2nbo
    const otp = randomInt(100000, 999999);
    return {
      session_id: args.ctx.session_id,
      user_id: args.data.user_id,
      created_at: nowValue,
      password: otp + "",
    };
  },
  update: async (args) => {
    const latestOtp = await args.trx
      .selectFrom("one_time_password")
      .where("one_time_password.id", "=", args.id)
      .select((eb) => [
        "one_time_password.user_id",
        "one_time_password.password",
        "one_time_password.verified_at",
        eb("one_time_password.created_at", ">", addToNow(-otpValidDurtionInMinutes, "minutes")).as("valid"),
      ])
      .executeTakeFirst();
    if (!latestOtp) throw new ResourceError("No OTP found");
    if (latestOtp.verified_at) throw new ResourceError("OTP already verified");
    if (!latestOtp.valid) throw new ResourceError("One Time Password is expired");
    if (latestOtp.password !== args.data.password) throw new ResourceError("Incorrect OTP. Please Check carefully and try again.");

    return {
      verified_at: nowValue,
    };
  },
  delete: () => true,
};
