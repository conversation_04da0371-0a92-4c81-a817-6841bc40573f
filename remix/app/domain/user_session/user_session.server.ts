import { authMethods, AuthzMethod } from "~/domain/user_session/user_session";
import { ResourceError } from "~/utils/error";
import { atInfinityLiteralFn, nowValue } from "~/kysely/kysely-helpers";
import { validateGoogleToken } from "~/utils/firebase.server";
import { validatePassword } from "~/server/auth/auth.password.server";
import { v4 } from "uuid";
import { activeSession } from "~/domain/member/member-queries.server";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { Selectable } from "kysely";
import { UserSession } from "~/kysely/db";
import { Args } from "~/server/resource/resource-helpers.server";

export const userSessionResource: Args<"user_session"> = {
  authorize: ({ ctx, id, trx }) => true,
  disableAudit: () => true,
  beforeMutate: async (args) => {
    if (args.id) return { id: args.id, operation: args.operation, data: args.data };
    const userId = args.data?.user_id;
    const existingUserSession = await args.trx
      .selectFrom("user_session")
      .select(["user_session.id", "user_session.verified_at"])
      .where("user_session.user_id", "=", userId || null)
      .where("user_session.session_id", "=", args.ctx.session_id)
      .where("user_session.destroyed_at", "=", atInfinityLiteralFn)
      .executeTakeFirst();

    const getOperation = () => {
      if (existingUserSession) {
        if (existingUserSession.verified_at && args.data?.method === ("otp" satisfies AuthzMethod)) {
          return "ignore";
        }
        if (args.operation === "insert") return "update";
      }
      return args.operation;
    };

    return {
      id: existingUserSession?.id || v4(),
      data: args.data,
      operation: getOperation(),
    };
  },
  insert: async (args) => {
    const userId = args.data.user_id;
    const method: AuthzMethod = authMethods.find((method) => method === args.data.method) || "email";
    let passwordOrToken = args.data.verification_token;

    if (typeof userId !== "string") throw new ResourceError("User is required to create user_session");

    const user = await args.trx
      .selectFrom("user")
      .select(["id", "email", "password_hash", "deleted_at"])
      .where("user.deleted_at", "=", at_infinity_value)
      .where("user.id", "=", userId)
      .limit(1)
      .executeTakeFirst();

    if (!user) throw new ResourceError("User does not exist");

    const baseReturn = {
      user_id: userId,
      session_id: args.ctx.session_id,
      method: method,
      display_name: args.data.display_name,
    } satisfies Partial<Selectable<UserSession>>;

    if (method === "google") {
      if (typeof passwordOrToken !== "string") throw new ResourceError("Invalid google credentials");
      const token = await validateGoogleToken(passwordOrToken);
      if (user.email !== token.email) throw new ResourceError("Invalid google credentials");
      if (!token.email_verified) throw new ResourceError("Your Google email is not verified");
      return { ...baseReturn, verified_at: nowValue };
    }

    if (method === "otp") {
      const verifiedAndValidOtp = await args.trx
        .selectFrom("one_time_password")
        .select("one_time_password.id")
        .where("one_time_password.user_id", "=", userId)
        .where("one_time_password.verified_at", "=", nowValue)
        .where("one_time_password.session_id", "=", args.ctx.session_id)
        .executeTakeFirst();
      if (verifiedAndValidOtp) {
        return { ...baseReturn, verified_at: nowValue };
      }
    }

    if (method === "password") {
      if (typeof passwordOrToken !== "string") throw new ResourceError("Invalid credentials");
      const verified = await validatePassword(args.trx, { user_id: userId, password: passwordOrToken });
      if (!verified) throw new ResourceError("Invalid credentials");
      return { ...baseReturn, verified_at: nowValue };
    }

    return { ...baseReturn, verification_token: method === "email" ? v4() : null };
  },
  update: async (args) => {
    const foundUserSession = await activeSession(args.trx, args.ctx.session_id)
      .innerJoin("user_session", "user_session.session_id", "_session.id")
      .where("user_session.id", "=", args.id)
      .where("user_session.destroyed_at", "=", atInfinityLiteralFn)
      .select((eb) => [
        "user_session.id",
        "user_session.session_id",
        "user_session.method",
        eb
          .selectFrom("one_time_password")
          .where("one_time_password.user_id", "=", eb.ref("user_session.user_id"))
          .where("one_time_password.session_id", "=", eb.ref("user_session.session_id"))
          .where("one_time_password.verified_at", "=", nowValue)
          .select("one_time_password.id")
          .limit(1)
          .as("one_time_password"),
      ])
      .executeTakeFirst();

    if (!foundUserSession) return false;

    const verifyNow = args.data.verified_at;
    if (verifyNow) {
      if (foundUserSession.one_time_password) return { verified_at: nowValue, method: "otp" satisfies AuthzMethod };
      throw new ResourceError("One Time Password is expired or invalid");
    }

    return {
      destroyed_at: args.data.destroyed_at ? nowValue : undefined,
      // currency_switched: args.data.currency_switched,
    };
  },
  delete: () => false,
};
