import { RInput } from "~/components/ResourceInputs";
import { fName } from "~/misc/helpers";
import { HiddenTypeInput } from "~/components/form/DefaultInput";
import React, { Fragment } from "react";

export const range = ["from", "to"] as const;

export const HiddenInfinateAvailableScheduleInputs = (props: { target_id: string; index: string | number }) => {
  return (
    <Fragment>
      <RInput table={"schedule"} field={"data.target_id"} value={props.target_id} index={props.index} type={"hidden"} />
      {range.map((rangeFieldKey) => {
        return (
          <input
            key={rangeFieldKey}
            type="hidden"
            value={""}
            className="input"
            name={fName("schedule", "data.range", props.index, rangeFieldKey)}
          />
        );
      })}
      <HiddenTypeInput name={fName("schedule", "data.range", props.index)} value={"__pg_daterange__"} />
    </Fragment>
  );
};

// {weekDays.map((_, index) => (
//   <input key={index} type={"hidden"} name={fName("schedule", "data.days_of_week", props.name, index)} value={index + ""} />
// ))}
