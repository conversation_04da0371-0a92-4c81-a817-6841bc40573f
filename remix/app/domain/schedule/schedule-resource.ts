import { isEditorQb, memberIsAdminOrOwnerQb, userSessionId } from "~/domain/member/member-queries.server";
import { nowValue } from "~/kysely/kysely-helpers";
import { Args, bustCacheAfter } from "~/server/resource/resource-helpers.server";
import { getCache<PERSON>ey } from "~/server/cache/cache.planning.server";

export const scheduleResource: Args<"schedule"> = {
  authorize: async (args) => {
    const schedule = await args.trx
      .selectFrom("schedule")
      .innerJoin("member", "member.id", "schedule.target_id")
      .where((eb) =>
        eb.or([
          eb.exists(isEditorQb(args)),
          eb("member.establishment_id", "in", memberIsAdminOrOwnerQb(args).select("_member.establishment_id")),
        ]),
      )
      .where("schedule.id", "=", args.id)
      .select(["schedule.id", "member.establishment_id"])
      .executeTakeFirst();
    if (schedule) {
      bustCacheAfter(args, getCacheKey({ establishmentId: schedule.establishment_id }));
    }
    return schedule;
  },
  insert: (args) => ({
    ...args.data,
    created_by_user_session_id: userSessionId(args),
    created_at: nowValue,
  }),
  update: (args) => args.data,
  delete: () => true,
};
