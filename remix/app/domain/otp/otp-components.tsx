import { useAppContext } from "~/hooks/use-app-context";
import { RInput, RLabel, toInputId } from "~/components/ResourceInputs";
import { fName } from "~/misc/helpers";
import { AuthzMethod } from "~/domain/user_session/user_session";
import { at_now_value } from "~/kysely/db-static-vars";
import { SubmitButton } from "~/components/base/Button";
import React, { Fragment, ReactNode, useEffect, useId, useRef, useState } from "react";
import { toUtc } from "~/misc/date-helpers";
import { addMinutes, differenceInSeconds } from "date-fns";
import { otpValidDurtionInMinutes } from "~/domain/otp/otp-vars";
import { ActionForm } from "~/components/form/BaseFrom";
import { OperationInput } from "~/components/form/DefaultInput";
import { ActionAlert } from "~/components/ActionAlert";

export const PinInput = () => {
  const inputGroupId = useId();
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // document.getElementById(inputGroupId + 0)?.focus();
    inputRef.current?.focus();
  }, [inputGroupId]);

  // useEffect(() => {
  //   const input = inputRef.current;
  //   if (!input) return;
  //   input.addEventListener("input", () => input.style.setProperty("--_otp-digit", input.selectionStart));
  // }, []);

  return (
    <div className="scroll-y-1">
      <RLabel table={"one_time_password"} field={"data.password"}>
        Enter OTP
      </RLabel>
      <br />
      <input
        ref={inputRef}
        type="text"
        id={toInputId(fName("one_time_password", "data.password"))}
        name={fName("one_time_password", "data.password")}
        // className="input py-5 px-6 text-left text-xl font-mono"
        // placeholder="______"
        // style={{ letterSpacing: 7, width: "164px" }}
        className={"input py-5 px-6 text-center  text-xl font-mono"}
        style={{ letterSpacing: 7 }}
        autoComplete="one-time-code"
        inputMode="numeric"
        maxLength={6}
        pattern="\d{6}"
        required
        minLength={6}
      />
    </div>
  );

  return (
    <div className="flex flex-row gap-3">
      {[...Array(6)].map((_, index) => {
        const inputId = inputGroupId + index;
        return (
          <input
            key={index}
            id={inputId}
            className="w-10 border-slate-400 rounded-md"
            name={fName("one_time_password", "data.password", 0, index)}
            inputMode={"numeric"}
            maxLength={1}
            min={0}
            max={9}
            onPaste={(e) => {
              const pastedData = e.clipboardData.getData("text");
              const inputs = Array.from(e.currentTarget.closest("div")?.querySelectorAll("input") || []);

              inputs.forEach((input, index) => {
                if (index < pastedData.length) {
                  if (input) input.value = pastedData[index] as any;
                } else {
                  input.value = "";
                }
              });

              e.preventDefault();
            }}
            onKeyDown={(e) => {
              const value = e.currentTarget.value;
              if (e.key === "Backspace" && value) return;
              const sibling = value ? e.currentTarget.nextElementSibling : e.currentTarget.previousElementSibling;
              if (!(sibling instanceof HTMLInputElement)) return;
              sibling.focus();
              sibling.select();
              // if (e.key === "Backspace" && !e.currentTarget.value) {
              //   const sibling = e.currentTarget.previousElementSibling;
              //   if (!(sibling instanceof HTMLInputElement)) return;
              //   sibling.focus();
              //   sibling.select();
              // }
            }}
            // onChange={(e) => {
            //   const sibling = e.target.value ? e.target.nextElementSibling : e.target.previousElementSibling;
            //   if (!(sibling instanceof HTMLInputElement)) return;
            //   sibling.focus();
            //   sibling.select();
            // }}
          />
        );
      })}
    </div>
  );
};

function formatSecondsToMinutes(seconds: number) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds < 10 ? "0" : ""}${remainingSeconds}`;
}

export const OtpGoBackBlock = () => {
  const context = useAppContext();
  const userSessionId = context.user_session_id;
  const otpId = context.otp?.[0]?.id;
  if (!otpId) return <Fragment />;
  return (
    <ActionForm className="text-center">
      <RInput table={"one_time_password"} field={"id"} value={otpId} />
      <OperationInput table={"one_time_password"} value={"delete"} />
      {userSessionId && (
        <Fragment>
          <RInput table={"session"} field={"data.selected_user_id"} type={"hidden"} value={""} />
          <RInput table={"user_session"} field={"id"} value={userSessionId} />
          <RInput table={"user_session"} field={"data.destroyed_at"} value={"yes"} type={"hidden"} />
        </Fragment>
      )}
      <SubmitButton className="link">Go Back</SubmitButton>
    </ActionForm>
  );
};

export const OtpBlock = (props: { user_id: string; requestOTPComp?: ReactNode; verifyOTPComp?: ReactNode }) => {
  const context = useAppContext();
  const [now, setNow] = useState(new Date());

  const otp = context.otp?.find((otp) => otp.user_id === props.user_id);
  const createdAt = toUtc(otp?.created_at || now);
  const seconds = differenceInSeconds(addMinutes(createdAt, otpValidDurtionInMinutes), toUtc(now));

  useEffect(() => {
    const interval = setInterval(() => {
      setNow(new Date());
    }, 1000);
    return () => clearTimeout(interval);
  }, [setNow]);

  const userId = props.user_id;
  const email = context.user_id === props.user_id && context.email;

  if (otp && otp.active && !otp.verified_at && (Number.isNaN(seconds) || seconds > 0)) {
    return (
      <div className="space-y-3">
        <ActionForm className="space-y-3 max-w-80" generateIdentifier>
          <RInput type="hidden" table="one_time_password" field="id" value={otp.id} />
          <p>
            We've sent a One-Time Password (OTP) to {email ? <strong>{email}</strong> : <span>your registered email</span>}. Please enter
            this code to proceed
          </p>
          <PinInput />
          {props.verifyOTPComp}
          {/*<RInput table={"user"} field={"data.email"} value={userId} type={"hidden"} />*/}
          <RInput table={"user_session"} field={"data.user_id"} value={userId} type={"hidden"} />
          <RInput table={"user_session"} field={"data.method"} value={"otp" satisfies AuthzMethod} type={"hidden"} />
          <RInput table={"user_session"} field={"data.verified_at"} value={at_now_value} type={"hidden"} />
          <RInput table={"session"} field={"id"} value={context.session_id} />
          <RInput table={"session"} field={"data.selected_user_id"} value={userId} type={"hidden"} />
          <SubmitButton className={"w-full btn btn-primary"}>Verify</SubmitButton>
          <ActionAlert />
          {!Number.isNaN(seconds) && (
            <div className="text-center">
              Didn’t receive your OTP? Please check your spam folder or request a new password in {formatSecondsToMinutes(seconds)}
            </div>
          )}
        </ActionForm>
      </div>
    );
  }

  const isExpired = otp && !otp.verified_at;
  return (
    <div className="space-y-3">
      <ActionForm className="space-y-3">
        <ActionAlert />
        {props.requestOTPComp}
        {isExpired && (
          <p>
            OTP{" "}
            {email && (
              <Fragment>
                for <strong>{email} </strong>
              </Fragment>
            )}
            Has been expired. Please try again
          </p>
        )}
        <RInput table={"one_time_password"} field={"data.user_id"} type={"hidden"} value={userId} />
        <SubmitButton className={"btn btn-primary group w-full"}>
          <span className={"group-aria-busy:hidden"}>{isExpired ? "Email OTP Again" : "Email OTP"}</span>
          <span className={"hidden group-aria-busy:inline loader-dots"}>Sending OTP Mail</span>
        </SubmitButton>
      </ActionForm>
    </div>
  );
};
