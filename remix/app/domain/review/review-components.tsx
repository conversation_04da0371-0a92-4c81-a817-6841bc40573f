import React, { Fragment, ReactNode } from "react";
import { NEGATIVES, POSITIVES } from "~/domain/review/review-data";
import { fName } from "~/misc/helpers";
import { toInputId } from "~/components/ResourceInputs";

interface FeedbackPointsContainer {
  negatives?: string[] | null;
  positives?: string[] | null;
}

const metaObj = {
  plus: {
    name: "positives" as const,
    totals_label: "Show tops",
    list: POSITIVES,
    input: {
      label: "What are your positive experiences?",
      className: "border-green-500 bg-green-100 text-green-700 peer-checked:bg-green-600/80",
    },
    chip_count: {
      className: "bg-green-700",
    },
    chip: {
      className: "border-green-700 text-green-700",
    },
  },
  min: {
    name: "negatives" as const,
    totals_label: "Show improvements",
    list: NEGATIVES,
    input: {
      label: "Which areas can be improved?",
      className: "border-red-500 bg-red-100 text-red-600 peer-checked:bg-red-500/90",
    },
    chip_count: {
      className: "bg-gray-500",
    },
    chip: {
      className: "border-gray-500 text-gray-600",
    },
  },
} as const;

export const FeedbackSingleChip = (props: { label?: string; type: "min" | "plus"; right?: ReactNode }) => {
  const meta = metaObj[props.type];

  if (!props.label) return <Fragment />;

  return (
    <div
      className={`flex flex-row items-center gap-2 rounded-full border p-1 text-sm 
     ${meta.chip.className} ${props.right ? "pl-4" : "pr-4"}`}
    >
      {!props.right && (
        <span
          className={`flex h-5 w-5 items-center justify-center rounded-full text-center text-white 
                ${meta.chip_count.className}`}
        >
          {props.type === "plus" ? "+" : "-"}
        </span>
      )}
      <span className="first-letter:capitalize">{props.label}</span>
      {props.right && (
        <span
          className={`flex h-5 w-5 items-center justify-center rounded-full text-center text-xs text-white
                ${meta.chip_count.className}`}
        >
          {props.right}
        </span>
      )}
    </div>
  );
};

export const FeedbackChipCounts = (props: { items: FeedbackPointsContainer[]; type: "min" | "plus" }) => {
  const activeMeta = metaObj[props.type];
  const finalChips = Object.entries(activeMeta.list)
    .map(([key, value]) => {
      const reviews = props.items.filter((review) => review[activeMeta.name]?.includes(key));
      return { count: reviews.length, text: value, key: key };
    })
    .filter((value) => value.count);

  if (finalChips.length === 0) return <Fragment />;

  return (
    <Fragment>
      {finalChips.map((value) => (
        <FeedbackSingleChip
          key={value.key}
          label={value.text}
          type={props.type}
          right={(props.type === "plus" ? "+" : "-") + value.count}
        />
      ))}
    </Fragment>
  );
};

export const FeedbackPointsInput = (props: { type: "min" | "plus"; defaultValue?: string[] }) => {
  const meta = metaObj[props.type];
  return (
    <div className="space-y-3">
      <p>{meta.input.label}</p>
      <div className="flex flex-wrap gap-3">
        {Object.entries(meta.list).map(([value, label], index) => {
          const inputName = fName("review", `data.${meta.name}`, 0, index);
          const inputId = toInputId(inputName);
          return (
            <div key={value}>
              <input
                type="checkbox"
                id={inputId}
                className="peer hidden"
                name={inputName}
                value={value}
                defaultChecked={props.defaultValue && props.defaultValue.includes(value)}
              />
              <label
                key={value}
                htmlFor={inputId}
                className={`border-1 block cursor-pointer rounded-full border py-2 px-3 text-sm text-green-800 opacity-80 
              transition-colors first-letter:capitalize hover:opacity-100 active:opacity-100 peer-checked:text-white
              peer-checked:opacity-100 ${meta.input.className}`}
              >
                {label}
              </label>
            </div>
          );
        })}
      </div>
    </div>
  );
};
