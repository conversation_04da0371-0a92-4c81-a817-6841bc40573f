import type { Kysely } from "kysely";
import type { <PERSON> } from "~/kysely/db";
import { coalesceIntArray } from "~/kysely/kysely-helpers";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { fileTargetsQb } from "~/domain/file/file-resource";
import { kysely } from "~/misc/database.server";

export const reviewsRatingQb = kysely
  .selectFrom("review")
  .innerJoin("user_session", "user_session.id", "review.created_by_user_session_id")
  .innerJoin("user", "user.id", "user_session.user_id")
  .select((eb) => coalesceIntArray(eb.ref("review.experience_rating")).as("ratings"));

export const reviewQb = (client: Kysely<DB>) =>
  client
    .selectFrom("review")
    .innerJoin("user_session", "user_session.id", "review.created_by_user_session_id")
    .innerJoin("user", "user.id", "user_session.user_id")
    .selectAll("review")
    .select((eb) => [
      "user_session.verified_at",
      "user.email",
      "user.display_name",
      "review.created_at",
      "user_session.id as user_session_id",
      jsonArrayFrom(fileTargetsQb(client, "review", eb.ref("review.id"))).as("files"),
    ]);
