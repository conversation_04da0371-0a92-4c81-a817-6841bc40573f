import React, { Fragment, ReactNode, useRef } from "react";
import { addDays, addMonths, format } from "date-fns";
import { ParamLink } from "~/components/meta/CustomComponents";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/20/solid";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { useAppContext } from "~/hooks/use-app-context";
import { toUtc } from "~/misc/date-helpers";
import { StateInputKey } from "~/misc/parsers/global-state-parsers";
import { twMerge } from "tailwind-merge";
import { useFormCtx } from "~/components/form/BaseFrom";
import { SubmitButton } from "~/components/base/Button";
import { useNavigation } from "@remix-run/react";

export const MonthSwitch = () => {
  const ctx = useAppContext();
  const search = useSearchParams2();
  const inputRef = useRef<HTMLInputElement>(null);
  const selectedDateValue = ctx.date.dateParam;
  const selectedMonthValue = selectedDateValue.slice(0, 7);
  const selectMonth = toUtc(selectedMonthValue);

  const isBusy = !!search.pendingState?.persist_date;

  const NewMonthLink = (props: { add: number; children: ReactNode }) => {
    const newMonth = addMonths(selectMonth, props.add);
    const newDate = format(newMonth, "yyyy-MM") + "-01";
    return (
      <ParamLink
        preventScrollReset
        className="btn btn-text max-md:px-1 hover:bg-slate-50 text-secondary transition-colors rounded-full aria-disabled:animate-pulse aria-disabled:opacity-70"
        path="./"
        paramState={{ persist_date: newDate }}
        aria-disabled={search.pendingState?.persist_date === newDate}
      >
        {props.children}
      </ParamLink>
    );
  };

  return (
    <div className="flex flex-row items-center gap-1">
      <NewMonthLink add={-1}>
        <ChevronLeftIcon className="h-6 w-6" />
      </NewMonthLink>
      <input
        className="hidden"
        ref={inputRef}
        type={"month"}
        key={selectedMonthValue}
        defaultValue={selectedMonthValue}
        onChange={(e) => {
          search.setState({ persist_date: (e.target.value as any) + "-01" });
        }}
      />
      <button
        onClick={() => {
          inputRef.current?.focus();
          inputRef.current?.click();
        }}
        aria-busy={isBusy}
        className="hidden min-w-[150px] text-center aria-busy:spinner spinner-dark"
      >
        {format(selectMonth, "MMMM yyyy")}
      </button>
      <span className="min-w-[150px] text-center">{format(selectMonth, "MMMM yyyy")}</span>
      <NewMonthLink add={1}>
        <ChevronRightIcon className="h-6 w-6" />
      </NewMonthLink>
    </div>
  );
};

export const DaySwitch = (props: { className?: string }) => {
  const ctx = useAppContext();
  const form = useFormCtx();
  const search = useSearchParams2();
  const inputRef = useRef<HTMLInputElement>(null);
  const selectedDateValue = ctx.date.dateParam;
  const selectDate = toUtc(selectedDateValue);

  const isBusy = !!search.pendingState?.persist_date;
  const navigation = useNavigation();

  const isEqual = !!form.formdata && form.formdata.get("persist_date" satisfies StateInputKey) === search.state.persist_date;

  const NewDayLink = (props: { add: number; children: ReactNode }) => {
    const newDay = addDays(selectDate, props.add);
    const newDate = format(newDay, "yyyy-MM-dd");
    return (
      <ParamLink
        preventScrollReset
        className="btn btn-text hover:bg-slate-50 text-secondary transition-colors rounded-full aria-disabled:animate-pulse aria-disabled:opacity-70"
        path="./"
        paramState={{ persist_date: newDate }}
        aria-disabled={search.pendingState?.persist_date === newDate}
      >
        {props.children}
      </ParamLink>
    );
  };

  return (
    <Fragment>
      <div className={twMerge("flex flex-row items-center gap-1", props.className)}>
        <NewDayLink add={-1}>
          <ChevronLeftIcon className="h-6 w-6" />
        </NewDayLink>
        <NewDayLink add={1}>
          <ChevronRightIcon className="h-6 w-6" />
        </NewDayLink>
        <input
          ref={inputRef}
          className="input flex-1"
          type={"date"}
          name={"persist_date" satisfies StateInputKey}
          key={selectedDateValue}
          defaultValue={selectedDateValue}
          // onChange={(e) => {
          //   const newSearchParams = search.createNewParams((searchParams) => {
          //     mergeStateToParams(searchParams, { persist_date: (e.target.value as any) + "" });
          //   });
          //   search.init(newSearchParams);
          // }}
        />
      </div>
      <SubmitButton
        className="btn btn-secondary  aria-busy:animate-pulse aria-busy:cursor-progress aria-busy:spinner spinner-light"
        aria-busy={navigation.state !== "idle"}
      >
        Go
      </SubmitButton>
    </Fragment>
  );
};
