import { add, addDays, eachWeekOfInterval, endOfMonth, endOfWeek, format, getWeek, startOfMonth, startOfWeek } from "date-fns";
import { weekDays } from "~/misc/vars";
import { toUtc } from "~/misc/date-helpers";

export const arrayFrom = (total: number) => Array.from({ length: total }, (_, index) => index + 1 + "");

export const getMonthObj = (month: string) => {
  const selectMonth = toUtc(month);

  const previousMonth = add(selectMonth, { months: -1 });
  const nextMonth = add(selectMonth, { months: 1 });
  const startOfPreviousMonthDate = startOfMonth(previousMonth);
  const startofMonthDate = startOfMonth(selectMonth);
  const endOfMonthDate = endOfMonth(selectMonth);

  const startOfCalendarDate = startOfWeek(startofMonthDate, { weekStartsOn: 1 });
  const endOfCalendarDate = endOfWeek(endOfMonthDate, { weekStartsOn: 1 });
  const weeksInMonth = eachWeekOfInterval({ start: startOfCalendarDate, end: endOfMonthDate }, { weekStartsOn: 1 });

  const weeks = weeksInMonth.map((weekDate, index) => ({
    number: getWeek(weekDate, { weekStartsOn: 1 }),
    dates: weekDays.map((_, index) => addDays(weekDate, index)).map((date) => format(date, "yyyy-MM-dd")),
  }));

  return {
    weeks: weeks,
    currentMonth: format(selectMonth, "yyyy-MM"),
    previousMonth: format(previousMonth, "yyyy-MM"),
    nextMonth: format(nextMonth, "yyyy-MM"),
    firstDateInCalender: format(startOfCalendarDate, "yyyy-MM-dd"),
    lastDateInCalender: format(endOfCalendarDate, "yyyy-MM-dd"),
    firstDatePreviousMonth: format(startOfPreviousMonthDate, "yyyy-MM-dd"),
    firstDate: format(startofMonthDate, "yyyy-MM-dd"),
    lastDate: format(endOfMonthDate, "yyyy-MM-dd"),
  };
};
