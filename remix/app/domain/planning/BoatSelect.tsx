import { RInput, toInputId } from "~/components/ResourceInputs";
import { tableIdRef } from "~/misc/helpers";
import { AnimatingDiv } from "~/components/base/base";
import React from "react";
import { usePlanningDayLoader } from "~/routes/_all._catch._w.planning._index";
import { flat } from "remeda";
import { CDialog } from "~/components/base/Dialog";
import { SharedBoatFields } from "~/domain/boat/BoatForm";
import { setState, useSearchParams2 } from "~/hooks/use-search-params2";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { ActionForm } from "~/components/form/BaseFrom";

export const BoatCreateDialog = () => {
  const search = useSearchParams2();
  const establishmentId = search.state.establishment_id || search.state.persist_establishment_id;
  return (
    <CDialog dialogname={"boat"} className={"max-w-xl"}>
      {search.state.establishment_id ? (
        <div>establishment_id is required to create a boat</div>
      ) : (
        <div>
          <ActionForm className="space-y-3">
            <h3 className="text-xl">Create Boat</h3>
            <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined, boat_id: tableIdRef("boat") }} />
            <RInput type={"hidden"} table={"boat"} field={"data.establishment_id"} value={establishmentId || ""} />
            <SharedBoatFields />
          </ActionForm>
        </div>
      )}
    </CDialog>
  );
};

export const BoatSelect = (props: { name: string; defaultValue?: string | null; disabled?: boolean }) => {
  const data = usePlanningDayLoader();
  const search = useSearchParams2();

  const value = typeof search.state.boat_id === "string" ? search.state.boat_id : props.defaultValue || "";

  const inputId = toInputId(props.name);

  const newBoatIdRef = tableIdRef("boat", props.name);
  return (
    <AnimatingDiv className="space-y-3">
      <div className="space-y-1">
        <div className="w-full space-y-1">
          <label htmlFor={inputId} className="required">
            Boat
          </label>
          <br />
          <select
            name={props.name}
            id={inputId}
            disabled={props.disabled}
            value={value}
            onChange={(e) => {
              const value = e.target.value;
              setState(value === newBoatIdRef ? { toggle_modal: "boat" } : { boat_id: value }, { replaceRoute: true });
            }}
            className="select w-full"
            required
          >
            <option value="">Select a boat</option>
            {data.establishments.map((establishment) =>
              establishment.boats.map((boat) => {
                const trips = flat(data.establishments.map((establishment) => establishment.trips)).filter(
                  (trip) => trip.boat_id === boat.id,
                );
                const tripsText = trips.map((trip) => `${trip.start_time?.slice(0, 5)} ${trip.activity_location}`).join(", ");
                return (
                  <option key={boat.id} value={boat.id}>
                    {boat.name}, Max. {boat.capacity} {trips.length > 0 && <span>({tripsText})</span>}
                  </option>
                );
              }),
            )}
            <option value={newBoatIdRef}>Create a boat</option>
          </select>
        </div>
      </div>
    </AnimatingDiv>
  );
};
