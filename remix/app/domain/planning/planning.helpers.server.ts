import { format } from "date-fns";
import { redirect } from "@remix-run/server-runtime";
import { mergeStateToParams, paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { TZDate } from "@date-fns/tz";
import { dateFormat } from "~/misc/vars";

export const getDateFromParams = (request: Request, redirectIfNoDate?: boolean) => {
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);

  const todayLocal = new Date();
  const todayZoned = new TZDate(todayLocal, state.persist_timezone);
  if (redirectIfNoDate && !state.persist_date) {
    mergeStateToParams(url.searchParams, { persist_date: format(todayZoned, dateFormat) });
    throw redirect(url.toString());
  }

  const todayAsParam = format(todayZoned, dateFormat);
  const dateParam = state.persist_date || todayAsParam;
  const montParams = state.persist_month || format(todayZoned, "yyyy-MM");
  // const selectedDate = new Date(dateParam);
  // const startOfDayDate = selectedDate;
  // const endOfDayDate = addHours(selectedDate, 24);

  return {
    // today: today,
    todayParam: todayAsParam,
    dateParam: dateParam,
    monthParam: montParams,
    // date: selectedDate,
    // startOfDay: startOfDayDate,
    // endOfDay: endOfDayDate,
  };
};
