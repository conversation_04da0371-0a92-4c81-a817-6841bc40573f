import { usePlanningDayLoader } from "~/routes/_all._catch._w.planning._index";
import { useBoolean } from "~/hooks/use-boolean";
import type { ReactNode } from "react";
import React, { Fragment, useState } from "react";
import { AnimatingDiv } from "~/components/base/base";
import { Label, Listbox, ListboxButton, ListboxOption, ListboxOptions } from "@headlessui/react";
import { tableIdRef } from "~/misc/helpers";
import { ChevronDownIcon, PlusCircleIcon } from "@heroicons/react/20/solid";
import { RInput } from "~/components/ResourceInputs";
import type { Role } from "~/domain/planning/plannings-consts";
import { availabilities, roles } from "~/domain/planning/plannings-consts";
import { twMerge } from "tailwind-merge";
import { OperationInput } from "~/components/form/DefaultInput";
import { flat } from "remeda";
import { EstablishmentSeperator } from "~/domain/establishment/EstablishmentSeperator";
import { MemberDiveLevel } from "~/domain/member/member-components";
import { HiddenInfinateAvailableScheduleInputs } from "~/domain/schedule/HiddenRangeInput";
import { refreshFormdata } from "~/components/form/form-hooks";

export const MemberSelect = (props: { name: string; establishment_id: string; role: Role; defaultValue?: string; label?: ReactNode }) => {
  const data = usePlanningDayLoader();
  const showAll = useBoolean();
  const [memberId, setMemberId] = useState(props.defaultValue);
  const role = roles[props.role];

  const members = flat(data.establishments.map((establishment) => establishment.members));
  const selectedMember = members.find((members) => members.id === memberId);
  const selectedEstablishment = data.establishments.find((establishment) => establishment.id === selectedMember?.establishment_id);
  const availability = selectedMember && availabilities[selectedMember.availability[props.role]];
  const memberAddRefValue = tableIdRef("member", props.name);
  return (
    <AnimatingDiv className="space-y-2">
      <Listbox
        value={memberId}
        onChange={(value) => {
          setMemberId(value);
          refreshFormdata();
        }}
        name={props.name}
      >
        <AnimatingDiv className={"space-y-1"}>
          {!!props.label && <Label className="flex w-full flex-row items-center justify-between gap-2">{props.label}</Label>}
          {/*{props.name === rName("member", "id") && <OperationInput table={"member"} value={"ignore"} />}*/}
          <ListboxButton className="group flex w-full flex-row items-center gap-2 rounded-md border border-slate-300 p-2">
            {selectedMember ? (
              <Fragment>
                <div className={twMerge("h-5 w-5 rounded-full", availability && availability.className)} />
                <span>
                  {data.establishments.length > 1 && selectedEstablishment?.short && selectedEstablishment.short + ", "}
                  {selectedMember.name}
                </span>
                <MemberDiveLevel diving_level={selectedMember.diving_level} />
              </Fragment>
            ) : memberId === memberAddRefValue ? (
              <Fragment>
                <PlusCircleIcon className="h-5 w-5" /> <span>New {role.selectText}</span>
              </Fragment>
            ) : (
              memberId || <span>Select {role.selectText}</span>
            )}
            <span className="flex-1" />
            <ChevronDownIcon className="h-5 w-5 transition-transform ui-open:rotate-180" />
          </ListboxButton>
          <ListboxOptions modal={false}>
            <AnimatingDiv className="space-y-2 rounded-md border border-slate-300">
              {data.establishments.map((establishment) => (
                <div key={establishment.id}>
                  {data.allEstablishments.length > 1 && (
                    <div className="p-3">
                      <EstablishmentSeperator establishment={establishment} />
                    </div>
                  )}
                  {establishment.members
                    .filter((member) => showAll.isOn || member[role.memberValue.name])
                    .sort((a, b) => {
                      // a.availability[props.role] - b.availability[props.role])

                      return (
                        (availabilities[a.availability[props.role]]?.sortingOrder || 0) -
                        (availabilities[b.availability[props.role]]?.sortingOrder || 0)
                      );
                    })
                    .map((member) => {
                      const availebility = availabilities[member.availability[props.role]];
                      return (
                        <ListboxOption
                          value={member.id}
                          key={member.id}
                          className="flex w-full cursor-default flex-wrap items-center gap-3 p-3 hover:bg-slate-200 ui-selected:bg-slate-100 ui-active:bg-slate-200"
                        >
                          <div className={twMerge("h-5 w-5 rounded-full bg-green-500", availebility?.className)} />
                          <span>{member.name}</span>
                          <MemberDiveLevel diving_level={member.diving_level} />
                        </ListboxOption>
                      );
                    })}
                </div>
              ))}
              <button
                type={"button"}
                className={
                  "flex w-full cursor-default flex-wrap items-center gap-3 p-3 hover:bg-slate-200 ui-selected:bg-slate-100 ui-active:bg-slate-200"
                }
                onClick={showAll.toggle}
              >
                {showAll.isOn ? `only show ${role.plural}` : "show all members"}
              </button>
              <ListboxOption
                value={""}
                className="flex w-full cursor-default flex-wrap items-center gap-3 p-3 hover:bg-slate-200 ui-selected:bg-slate-100 ui-active:bg-slate-200"
              >
                unselect
              </ListboxOption>
              <ListboxOption
                className="flex w-full cursor-default flex-wrap items-center gap-3 p-3 text-primary hover:bg-slate-200 ui-selected:bg-slate-100 ui-active:bg-slate-200"
                value={memberAddRefValue}
              >
                <PlusCircleIcon className="h-5 w-5" /> <span>New {role.selectText}</span>
              </ListboxOption>
            </AnimatingDiv>
          </ListboxOptions>
        </AnimatingDiv>
      </Listbox>
      {memberId === memberAddRefValue && (
        <div>
          <RInput table={"member"} field={"data.establishment_id"} index={props.name} type={"hidden"} value={props.establishment_id} />
          <OperationInput table={"member"} value={"insert"} index={props.name} />
          <RInput
            table={"member"}
            field={`data.${role.memberValue.name}`}
            index={props.name}
            type={"hidden"}
            value={role.memberValue.value}
            hiddenType={role.memberValue.hiddenType}
          />
          <div className="space-y-3 rounded-md border border-slate-300 p-3 pb-5">
            {"customInput" in role && role.customInput(props.name)}
            <div className="space-y-1">
              <RInput className="input" label="Name" required table={"member"} field={"data.name"} index={props.name} />
            </div>
            <div className="space-y-1">
              <RInput
                table={"member"}
                field={"data.user_id"}
                index={props.name}
                type={"hidden"}
                value={tableIdRef("user", "00-" + props.name)}
              />
              <RInput className="input" table={"user"} label={"Email"} field={"data.email"} type={"email"} index={"00-" + props.name} />
            </div>
            <HiddenInfinateAvailableScheduleInputs target_id={memberAddRefValue} index={props.name} />
          </div>
        </div>
      )}
    </AnimatingDiv>
  );
};
