import { twMerge } from "tailwind-merge";
import { AnimatingDiv } from "~/components/base/base";
import {
  _boat_mutate,
  _booking_detail,
  _customer,
  _establishment_paths,
  _member_detail,
  _participant_detail,
  _participant_mutate,
} from "~/misc/paths";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { usePlanningDayLoader } from "~/routes/_all._catch._w.planning._index";
import { entries, keys, myGroupBy2 } from "~/misc/helpers";
import { ParamLink } from "~/components/meta/CustomComponents";
import { BoatEntry16x16Icon, CaptainIcon, CrewIcon, InstructorsIcon, ParticipantsIcon } from "~/components/Icons";
import type { Role } from "~/domain/planning/plannings-consts";
import { availabilities, isRole, roles } from "~/domain/planning/plannings-consts";
import React, { Fragment, ReactNode, useState } from "react";
import { getProductTitle } from "~/domain/product/ProductItem";
import { flat, unique, uniqueBy } from "remeda";
import { getTripTotals } from "~/domain/trip/trip-components";
import { getNrOfDivesOption } from "~/domain/participant/participant-data";
import { EstablishmentSeperator } from "~/domain/establishment/EstablishmentSeperator";
import { MemberDiveLevel } from "~/domain/member/member-components";
import { formatDivingCertShort } from "~/domain/diving-course/diving-courses.data";
import { getLastDivedShort } from "~/domain/participant/participant-helpers";
import { defaultNotFilledValue, defaultUnkownValue } from "~/misc/vars";
import { formatMealPref } from "~/domain/participant/participant-fields";
import { defaultNotRegisteredValue } from "~/components/shared";
import { BookingPaymentBadge, getPaymentState } from "~/domain/booking/booking-components";
import { Tooltip } from "~/components/base/tooltip";
import { InformationCircleIcon } from "@heroicons/react/20/solid";
import { useAppContext } from "~/hooks/use-app-context";

import { getAdminLevelIndex } from "~/domain/member/member-vars";
import { LuListFilter } from "react-icons/lu";

const participantHeaderColumns = [
  "name",
  "ref#",
  "paid",
  "activity",
  "instructor",
  "cert",
  "dives",
  "last",
  "age",
  "country",
  "diet",
  "allergies",
] as const;

const tabs = {
  boat: { icon: <BoatEntry16x16Icon className="h-full w-full" /> },
  captain: { icon: <CaptainIcon className="h-full w-full" /> },
  crew: { icon: <CrewIcon className="h-full w-full" /> },
  instructor: { icon: <InstructorsIcon className="h-full w-full" />, seperate: true },
  // bus: { icon: <TruckIcon className="h-full w-full" /> },
  participant: { icon: <ParticipantsIcon className="h-full w-full" /> },
} satisfies Record<
  string,
  {
    icon: unknown;
    seperate?: boolean;
  }
>;

const EngagementsTooltip = () => {
  return (
    <Tooltip
      description={
        <div className="max-w-md space-y-3 p-3">
          <h2 className="text-xl">Participants & Engagements</h2>
          <p>
            <strong className="text-slate-200">Participants</strong>
            <br />
            <span>This indicates the number of unique participants engaged in activities on this particular day.</span>
          </p>
          <p>
            <strong className="text-slate-200">Engagements</strong>
            <br />
            <span>
              A participant may engage in more than one activity over the course of a day. A participant is shown per activity engagement,
              which can cause the number of engagements to differ from the number of participants.
            </span>
          </p>
          <p>
            <strong className="text-slate-200">Multiple trips</strong>
            <br />
            <span>A participant scheduled for more than one trip is indicated with #T behind their name.</span>
          </p>
          {/*<p>Read more about Participants and Engagements in this article.</p>*/}
        </div>
      }
    >
      <InformationCircleIcon className="w-5 h-5" />
    </Tooltip>
  );
};

export const PlanningTabs = () => {
  const data = usePlanningDayLoader();
  const search = useSearchParams2();
  const [highlightedParticipantRow, setHighlightedParticipantRow] = useState<null | string>(null);
  const currentTab: keyof typeof tabs | undefined = keys(tabs).find((tab) => tab === search.state.tab);
  const ctx = useAppContext();
  const adminWriteEstablishments = data.establishments.filter((establishment) =>
    ctx.members.find((member) => member.establishment_id === establishment.id && member.admin === getAdminLevelIndex("write")),
  );
  const hasWriteOnAllEstablishments = !!data.establishments.length && adminWriteEstablishments.length === data.establishments.length;
  const pendingParticipants = flat(data.establishments.map((establishment) => establishment.pending_participants));

  const mapsUrl = new URL("https://www.google.com/maps/dir/");
  mapsUrl.searchParams.set("api", "1");
  // mapsUrl.searchParams.set("origin", data.operator.address);
  const waypoints: string[] = [];
  // data.trips.forEach((trip) =>
  //   trip.bookings.forEach((booking) => {
  //     if (booking.meeting_address) {
  //       waypoints.push(booking.meeting_address);
  //     }
  //   })
  // );
  if (waypoints.length > 0) {
    mapsUrl.searchParams.set("waypoints", waypoints.join("|"));
  }
  mapsUrl.searchParams.set("destination", "raalte station");
  mapsUrl.searchParams.set("travelmode", "driving");
  // const mapsUrl = new URL(`https://www.google.com/maps/dir/${pickupLocations.join("/")}?entry=ttu`).toString();

  const roleTab = (role: Role) => {
    const finalRole = roles[role];
    const totalAvailableMembers = flat(data.establishments.map((establishment) => establishment.members)).filter(
      (member) =>
        member.assignments.find(isRole(role)) ||
        (member[finalRole.memberValue.name] && member.schedules?.[member.schedules.length - 1]?.available),
    );
    const plannableMembers = totalAvailableMembers.filter((member) => member.assignments.length === 0);

    return {
      counts: [plannableMembers.length, totalAvailableMembers.length],
      content: (
        <div className="space-y-3">
          <p className="text-xl first-letter:uppercase">{roles[role].plural}</p>
          <div className="space-y-6">
            {data.establishments.map((establishment, index) => {
              return (
                <div key={establishment.id} className="space-y-3">
                  {data.allEstablishments.length > 1 && <EstablishmentSeperator establishment={establishment} />}
                  {establishment.members
                    .filter((member) => member[finalRole.memberValue.name] || member.assignments.find(isRole(role)))
                    .sort((a, b) => a.availability[role] - b.availability[role])
                    .map((member) => {
                      return (
                        <div key={member.id} className="flex flex-wrap gap-2 items-center">
                          <div className={twMerge(`h-4 w-4 rounded-full`, availabilities[member.availability[role]]?.className)} />
                          <ParamLink path={_member_detail(member.id)}>{member.name}</ParamLink>
                          <span className="flex-1" />
                          <span className="text-slate-600">
                            {uniqueBy(member.assignments, (assignment) => assignment.trip?.id)
                              .map((assignment) => assignment.trip)
                              .map((trip) => trip?.boat_name || trip?.activity_location)
                              .join(", ")}
                          </span>
                        </div>
                      );
                    })}
                </div>
              );
            })}
          </div>
        </div>
      ),
    };
  };

  const members = flat(data.establishments.map((establishment) => establishment.members));
  const totalAvailableMembers = members.filter((member) => {
    const schedules = member.schedules;
    return member.diving_level && schedules && schedules[schedules.length - 1]?.available;
  });
  const plannableMembers = totalAvailableMembers.filter((member) => member.assignments.length === 0);

  const boats = flat(data.establishments.map((establishment) => establishment.boats)).map((boat) => {
    const trips = flat(data.establishments.map((establishment) => establishment.trips)).filter(
      (trip) => trip.date && trip.boat_id === boat.id,
    );
    const firstTrip = trips[0];

    const tripTotals = firstTrip && getTripTotals(firstTrip);
    const capacity = firstTrip?.capacity || boat.capacity;
    const taken = tripTotals?.seatsTaken || 0;
    const available = capacity - taken;
    return {
      ...boat,
      trips: trips,
      available: available,
    };
  });

  const partcipations = data.filteredParticipations;
  const participationActivityGroups = myGroupBy2(partcipations, (participation) => participation.id);
  const participantOrParticipationIds = unique(partcipations.map((participation) => participation.participant_id || participation.id));
  const tabsContent = {
    boat: {
      counts: [boats.filter((boat) => boat.trips.length === 0).length, boats.length],
      content: (
        <div className="space-y-3">
          <h3 className="text-xl">Boats</h3>
          {boats.map((boat) => {
            return (
              <div key={boat.id} className="space-y-1">
                <div className="flex items-center gap-3">
                  <div
                    className={twMerge(
                      "h-5 w-5 rounded-full bg-green-500",
                      boat.trips.length > 0 && "bg-orange-500",
                      boat.available < 1 && "bg-red-500",
                    )}
                  />
                  <ParamLink path={_boat_mutate} paramState={{ id: boat.id }} className="font-semibold">
                    {boat.name}
                  </ParamLink>
                  <span className="flex-1" />
                  <span>Capacity {boat.capacity}</span>
                </div>
                <div className="space-y-1 pl-8">
                  {boat.trips.map((trip) => {
                    const tripTotals = getTripTotals(trip);
                    return (
                      <div key={trip.id} className="flex justify-between text-xs text-slate-600">
                        <span className="flex-1">{trip.activity_location}</span>
                        <span>{trip.start_time?.slice(0, 5)}</span>
                        <span className="flex-1 text-right">{tripTotals.available} available</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      ),
    },
    captain: roleTab("captain"),
    instructor: {
      counts: [plannableMembers.length, totalAvailableMembers.length],
      content: (
        <div className="space-y-3">
          <p className="text-xl first-letter:uppercase">Instructors</p>
          <div className="space-y-6">
            {data.establishments.map((establishment, index) => {
              return (
                <div key={establishment.id} className="space-y-3">
                  {data.allEstablishments.length > 1 && <EstablishmentSeperator establishment={establishment} />}
                  {establishment.members
                    .filter((member) => member.diving_level)
                    .sort((a, b) => {
                      // return a.availability.divemaster - b.availability.divemaster;
                      return (
                        (availabilities[a.availability.divemaster]?.sortingOrder || 0) -
                        (availabilities[b.availability.divemaster]?.sortingOrder || 0)
                      );
                    })
                    .map((member) => {
                      return (
                        <div key={member.id} className="flex flex-wrap gap-2 items-center">
                          <div className={twMerge(`h-4 w-4 rounded-full`, availabilities[member.availability.divemaster]?.className)} />
                          <ParamLink path={_member_detail(member.id)}>
                            {member.name} <MemberDiveLevel diving_level={member.diving_level} />
                          </ParamLink>
                          <span className="flex-1" />
                          <span className="text-slate-600">
                            {uniqueBy(member.assignments, (assignment) => assignment.trip?.id)
                              .map((assignment) => assignment.trip)
                              .map((trip) => trip?.boat_name || trip?.activity_location)
                              .join(", ")}
                          </span>
                        </div>
                      );
                    })}
                </div>
              );
            })}
          </div>
        </div>
      ),
    },
    crew: roleTab("crew"),
    // bus: {
    //   content: (
    //     <p>
    //       bus
    //       {data.trips.map((trip) => (
    //         <div key={trip.id}>
    //           {trip.bookings.map((booking) => (
    //             <div key={booking.id}>
    //               {booking.registrations.map((registration) => (
    //                 <div key={registration.id}>
    //                   <span>
    //                     {registration.first_name} {registration.last_name}
    //                   </span>
    //                   <span>{registration.pickup_location && "Dive center"}</span>
    //                 </div>
    //               ))}
    //             </div>
    //           ))}
    //         </div>
    //       ))}
    //       <a href={mapsUrl.toString()} target={"_blank"} className="link">
    //         google maps
    //       </a>
    //     </p>
    //   ),
    //   totalNr: 0,
    //   activeNr: 0,
    // },
    participant: {
      content: (
        <div className="space-y-3">
          {!!pendingParticipants.length && (
            <Tooltip description={!hasWriteOnAllEstablishments && "No user rights to view pending participants"}>
              <ParamLink
                path={_customer}
                aria-disabled={!hasWriteOnAllEstablishments}
                className="max-lg border-red-500 border rounded-md p-2 text-center text-red-500 hover:bg-red-50 transition-colors block aria-disabled:opacity-60"
              >
                {pendingParticipants.length === 1 ? (
                  <span>
                    <span className="font-semibold">{pendingParticipants.length}</span> pending registrants
                  </span>
                ) : (
                  <span>
                    <span className="font-semibold">{pendingParticipants.length}</span> pending registrants
                  </span>
                )}
                &nbsp;- click here to view
              </ParamLink>
            </Tooltip>
          )}
          <p className="flex flex-row gap-3 items-center">
            <span className="text-xl">Participants ({participantOrParticipationIds.length})</span>
            {participantOrParticipationIds.length !== participationActivityGroups.length && (
              <span className="text-slate-400">Engagements ({participationActivityGroups.length})</span>
            )}
            <EngagementsTooltip />
          </p>
          <div className="overflow-auto w-full">
            <table className="w-full">
              <thead>
                <tr>
                  {participantHeaderColumns.map((name) => (
                    <th className="text-left capitalize font-semibold text-slate-500 p-1 whitespace-nowrap" key={name}>
                      {name}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {participationActivityGroups.map((participationGroup, index) => {
                  const participation = participationGroup.items[0]!;
                  const participationTrips = participationGroup.items;
                  const participant = participation.participant;
                  const toggleKey = "registration-row" + participation.id + "-" + index;
                  const instructors = uniqueBy(
                    participationTrips
                      .map((participation) => participation?.trip_assignment?.member)
                      .filter((member): member is NonNullable<typeof member> => !!member),
                    (member) => member.id,
                  );

                  const booking = participation?.booking;

                  const allParticipantParticipations = participationActivityGroups.filter(
                    (participation) => participant && participant.id === participation.items[0]?.participant_id,
                  );
                  const participationIndex = allParticipantParticipations.findIndex((item) => item.groupKey === participation.id) + 1;

                  const product = data.allProducts.find((product) => product.id === participation.activity?.product_id);
                  const operatorLocationPaths = _establishment_paths(booking?.establishment_id || "");

                  const cols = {
                    name: participant ? (
                      <ParamLink
                        path={participant ? _participant_detail(participant.id) : _participant_mutate}
                        paramState={{ booking_id: booking?.id }}
                        className="link flex flex-row gap-2 items-center"
                      >
                        <span className={twMerge(allParticipantParticipations.length > 1 && "font-semibold")}>
                          {participant.first_name + " " + participant.last_name}
                        </span>
                        {allParticipantParticipations.length > 1 && (
                          <span className="text-xs text-slate-700">
                            {participationIndex}/{allParticipantParticipations.length}
                          </span>
                        )}
                        {participationTrips.length > 1 && <span className="text-primary font-semibold">{participationTrips.length}T</span>}
                      </ParamLink>
                    ) : (
                      defaultNotRegisteredValue
                    ),
                    "ref#": (
                      <ParamLink className="link" path={_booking_detail(booking?.id!)}>
                        {booking?.booking_reference || booking?.sqid || "booking"}
                      </ParamLink>
                    ),
                    paid: booking && <BookingPaymentBadge status={getPaymentState(booking)} />,
                    activity: product && getProductTitle(product),
                    instructor: instructors.map((instructor) => instructor.name).join(", ") || (
                      <span className="opacity-50">Not scheduled</span>
                    ),
                    cert: participant && (formatDivingCertShort(participant?.diving_certificate_level) || defaultNotFilledValue),
                    dives: participant && (getNrOfDivesOption(participant.number_of_dives)[1] || defaultNotFilledValue),
                    last: participant && (getLastDivedShort(participant?.last_dive_within_months) || defaultNotFilledValue),
                    age: participant && (participant.age || defaultNotFilledValue),
                    country: participant && (participant.country_code || defaultNotFilledValue),
                    diet: participant?.user_id ? formatMealPref(participant?.diet) || defaultNotFilledValue : defaultUnkownValue,
                    allergies: participant ? participant.food_allergies || defaultNotFilledValue : defaultUnkownValue,
                  } satisfies Record<(typeof participantHeaderColumns)[number], ReactNode>;

                  return (
                    <tr
                      key={index}
                      className="aria-selected:bg-secondary-50"
                      aria-selected={highlightedParticipantRow === toggleKey}
                      onClick={() => setHighlightedParticipantRow(highlightedParticipantRow === toggleKey ? null : toggleKey)}
                    >
                      {participantHeaderColumns.map((colName) => (
                        <td className="p-1 whitespace-nowrap" key={colName}>
                          {cols[colName]}
                        </td>
                      ))}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      ),
      counts: [participantOrParticipationIds.length],
    },
  } satisfies Record<
    keyof typeof tabs,
    {
      content: unknown;
      counts: number[];
    }
  >;
  return (
    <AnimatingDiv className="space-y-3">
      <div className="relative">
        {!!search.state.filter_id && (
          <div className="absolute left-0 top-0 bottom-0 z-10" style={{ right: "21%" }}>
            <ParamLink
              paramState={{ filter_id: null }}
              className="w-full group flex justify-center items-center h-full text-center text-4xl md:text-5xl text-primary gap-3 transition-all bg-white/70"
            >
              <LuListFilter className="w-12 h-12 md:w-14 md:h-14 inline" />
              <span className="group-hover:hidden">Filtered View</span>
              <span className="hidden group-hover:block">Clear Filters</span>
            </ParamLink>
          </div>
        )}
        <nav className="flex flex-wrap  gap-3 justify-between">
          {entries(tabs).map(([key, tab]) => (
            <Fragment key={key}>
              {key === "participant" && <div className="w-[2px] bg-secondary-50" />}
              <ParamLink
                paramState={{ tab: key === search.state.tab ? undefined : key }}
                className={"group text-center transition-colors relative"}
                aria-selected={key === currentTab}
              >
                {key === "participant" && !!pendingParticipants.length && (
                  <span className="bg-red-500 flex items-center justify-center rounded-full text-white w-4 h-4 text-xs absolute -top-2 right-0">
                    {pendingParticipants.length}
                  </span>
                )}
                <span
                  className={twMerge(
                    "inline-block h-10 w-10 text-secondary-500 transition-opacity group-hover:opacity-60 group-aria-selected:opacity-100 group-aria-selected:hover:opacity-70",
                    currentTab && "opacity-40 group-hover:opacity-60 ",
                    key !== currentTab && "group-hover:opacity-60",
                    // (!currentTab || key === currentTab) && "opacity-100",
                    !currentTab && "group-hover:text-secondary-tag",
                  )}
                >
                  {tab.icon}
                </span>
                <br />
                <span className="text-slate-700">
                  {tabsContent[key].counts.map((count, index) => (
                    <span key={index} className="font-semibold before:content-['/'] first:text-xl first:before:content-[]">
                      {count}
                    </span>
                  ))}
                </span>
              </ParamLink>
            </Fragment>
          ))}
        </nav>
      </div>
      {!!currentTab && (!search.state.filter_id || currentTab === "participant") && (
        <Fragment key={currentTab}>
          <div style={{ height: 1 }} className="bg-secondary-stroke" />
          <div>{tabsContent[currentTab].content}</div>
        </Fragment>
      )}
    </AnimatingDiv>
  );
};
