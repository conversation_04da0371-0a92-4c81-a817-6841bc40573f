import { ParamLink } from "~/components/meta/CustomComponents";
import React from "react";
import type { StateInput } from "~/misc/parsers/global-state-parsers";

export const TodayButton = (props: { params?: Partial<Pick<StateInput, "persist_date" | "persist_month" | "filter_id">> }) => (
  <ParamLink
    prefetch={"intent"}
    path={"./"}
    className="rounded-md border border-secondary text-secondary hover:bg-secondary-50 p-1 aria-disabled:opacity-50  aria-disabled:hover:bg-white aria-disabled:cursor-auto"
    paramState={props.params || { persist_date: undefined }}
  >
    Today
  </ParamLink>
);
