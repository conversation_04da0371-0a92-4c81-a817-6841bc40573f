import { keys } from "~/misc/helpers";
import { BoatEntry16x16Icon, DiveCenterIcon, PoolIcon, RestDayIcon, ShoreEntryIcon } from "~/components/Icons";
import { RSelect } from "~/components/ResourceInputs";
import { ReactNode } from "react";

export const availabilities = [
  {
    className: "bg-green-500",
    sortingOrder: 1,
  },
  {
    className: "bg-primary",
    sortingOrder: 0,
  },
  {
    className: "bg-red-500",
    sortingOrder: 2,
  },
  {
    className: "bg-gray-200",
    sortingOrder: 3,
  },
] as const;

export type Availability = keyof typeof availabilities;

const DivingLevelSelect = (props: { index: string | number; defaultValue?: string }) => (
  <RSelect className="select" table={"member"} field={"data.diving_level"} index={props.index} defaultValue={props.defaultValue}>
    <option value="0">None</option>
    <option value="1">Divemaster</option>
    <option value="2">Instructor</option>
  </RSelect>
);

export const roles = {
  captain: {
    short: "CP",
    label: "captain",
    plural: "captains",
    selectText: "captain",
    memberValue: { name: "captain", value: "on", hiddenType: "__boolean__" },
  },
  crew: {
    short: "CM",
    label: "crew member",
    plural: "crew members",
    selectText: "crew member",
    memberValue: { name: "crew", value: "on", hiddenType: "__boolean__" },
  },
  divemaster: {
    short: "DM",
    label: "divemaster",
    plural: "divemasters",
    selectText: "instructor/guide",
    memberValue: { name: "diving_level", value: "1", hiddenType: undefined },
    customInput: (index: string | number) => <DivingLevelSelect index={index} defaultValue={"1"} />,
  },
  instructor: {
    short: "IS",
    label: "instructor",
    plural: "instructors",
    selectText: "instructor/guide",
    memberValue: { name: "diving_level", value: "2", hiddenType: undefined },
    customInput: (index: string | number) => <DivingLevelSelect index={index} defaultValue={"2"} />,
  },
} as const;

export type Role = keyof typeof roles;

export type RolesSharedInterface = Record<Role, boolean | number>;

export const isRole =
  (...roles: Role[]) =>
  (assignment: { role: string }) =>
    roles.includes(assignment.role);

export const tripTypes = {
  boat: { name: "Boat", icon: <BoatEntry16x16Icon className="h-full w-full" />, showSites: true, selectable: true },
  shore: { name: "Shore", icon: <ShoreEntryIcon className="h-full w-full" />, showSites: true, selectable: true },
  dry: {
    name: "Dry session/training",
    icon: <DiveCenterIcon className="h-full w-full" />,
    showSites: false,
    selectable: true,
  },
  pool: {
    name: "Pool/confined water",
    icon: <PoolIcon className="h-full w-full" />,
    showSites: false,
    selectable: true,
  },
  rest: {
    name: "Rest",
    icon: (
      <div className="h-full pr-2 text-primary">
        <RestDayIcon className="w-full h-full" />
      </div>
    ),
    showSites: false,
    selectable: false,
  },
};

export const tripTypesList = keys(tripTypes);

export type TripType = keyof typeof tripTypes;

export const getTripType = (str?: string | null | boolean) => {
  const tripEntryKey = tripTypesList.find((key) => key === str);
  return tripEntryKey && { ...tripTypes[tripEntryKey], key: tripEntryKey };
};

export const defaultMaxPax = 4;

export const defaultMaxPaticiapnNr = 20;

export const participantsNrArray = Array.from({ length: 20 }, (_, index) => index + 1 + "");

export const addToArrayIfNotExists = (arr: string[], defaultValue?: string) => {
  if (defaultValue && arr.includes(defaultValue)) return arr;
  return [...arr, defaultValue];
};

export const meetingTypes = {
  DIVE_CENTER: {
    label: "Dive center",
    short: "DC",
    option_label: "Meet at divecenter",
    empty_text: null,
    getPlaceholder: () => null,
  },
  PICKUP: {
    label: "Pickup",
    short: "PL",
    option_label: "Pickup at hotel/hostel",
    empty_text: "Please contact us to confirm your pickup address, as no address has been specified.",
    getPlaceholder: (participantStays?: string[] | null) =>
      (participantStays?.length || 0) > 0 ? <span className="link">Select</span> : "Enter",
  },
  DIVE_LOCATION: {
    label: "Dive location",
    short: "DL",
    option_label: "Meet at dive location",
    empty_text: "Please contact us to confirm the dive location address, as no address has been specified.",
    getPlaceholder: () => "Enter",
  },
} satisfies Record<
  string,
  {
    label: string;
    short: string;
    option_label: string;
    empty_text: string | null;
    getPlaceholder: (participantStays?: string[] | null) => ReactNode;
  }
>;

export type MeetingType = keyof typeof meetingTypes;

export const getMeetingType = (str?: string | null | boolean) => {
  const key = keys(meetingTypes).find((key) => key === str);
  return key && { ...meetingTypes[key], key: key };
};

export const medicalFormName = "medical";
