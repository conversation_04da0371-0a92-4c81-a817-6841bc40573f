import { SelectQueryBuilder } from "kysely";
import { ascNullsLast, descNullsLast } from "~/kysely/kysely-helpers";
import { Direction, Sort, SortableColumnKey } from "~/domain/participant/participant-columns";
import { kysely } from "~/misc/database.server";
import { orderProduct } from "~/domain/product/product-queries.server";

type SortFn = <T extends SelectQueryBuilder<any, any, any>>(qb: SelectQueryBuilder<any, any, any>, direction: Direction) => T;

export const sortColumnsServer: Record<SortableColumnKey, SortFn> = {
  participant_filled: (qb, direction) => {
    return qb.orderBy(
      (eb) => eb.exists(eb.selectFrom("participant").where("participant.id", "=", eb.ref("participation.participant_id"))),
      direction,
    ) as any;
  },
  participant_name: (qb, direction) => {
    return qb.orderBy(
      (eb) =>
        eb
          .select<PERSON>rom("participant")
          .innerJoin("customer", "customer.id", "participant.customer_id")
          .innerJoin("person", "person.id", "customer.person_id")
          .select("person.first_name")
          .where("participant.id", "=", eb.ref("participation.participant_id")),
      direction === "asc" ? ascNullsLast : descNullsLast,
    ) as any;
  },
  booking_reference: (qb) => qb.orderBy("booking.id") as any,
  activity: (qb) => orderProduct(qb) as any,
  instructor_name: (qb, direction) => {
    return qb.orderBy(
      (eb) => kysely.selectFrom("member").where("member.id", "=", eb.ref("trip_assignment.member_id")).select("member.name").limit(1),
      direction === "asc" ? ascNullsLast : descNullsLast,
    ) as any;
  },
};

export const callOrderColumns = <T extends SelectQueryBuilder<any, any, any>>(eb: T, columns: Sort[]) => {
  let orderedEb = eb;
  columns.forEach((orderColumn) => {
    const columnOrderBy = sortColumnsServer[orderColumn.key];
    orderedEb = columnOrderBy(orderedEb, orderColumn.direction);
  });
  return orderedEb as T;
};
