import { kysely } from "~/misc/database.server";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { Expression, ExpressionBuilder, Kysely, sql } from "kysely";
import type { DB } from "~/kysely/db";
import { fileTargetsQb } from "~/domain/file/file-resource";
import {
  arrayAggDisinct,
  ascNullsLast,
  contextRef,
  dateRange,
  descNullsLast,
  emptyTextArray,
  formatDatetime,
  lower,
  nowValue,
} from "~/kysely/kysely-helpers";
import { participantFieldsServer } from "~/domain/participant/participant-fields.server";
import { lockedFields, participantFieldImpls, participantFields } from "~/domain/participant/ParticipantFields";
import { waiversEb } from "~/domain/waiver/waiver-queries.server";
import { CallbackName } from "~/domain/callback/callback";
import { WaiverType } from "~/domain/waiver/waiver-vars";
import { keys } from "~/misc/helpers";

export const waiverFileUploadsQb = (db: Kysely<DB>, id: Expression<string>) =>
  fileTargetsQb(db, "participant_waiver", id)
    .clearOrderBy()
    .orderBy("file_target.sort_order", ascNullsLast)
    .orderBy("file_target.created_at", descNullsLast);

export const participationQb = kysely
  .selectFrom("participation")
  .selectAll("participation")
  .select((eb) => [
    jsonArrayFrom(
      eb
        .selectFrom("participation_addon")
        .where("participation_addon.participation_id", "=", eb.ref("participation.id"))
        .selectAll("participation_addon"),
    ).as("addons"),
  ]);

export const participantSimpleQb = (db: Kysely<DB>) => {
  return db
    .selectFrom(`participant`)
    .innerJoin("customer", `participant.customer_id`, "customer.id")
    .innerJoin("person", "person.id", "customer.person_id")
    .innerJoin("user", "user.id", "person.user_id")
    .selectAll(`participant`)
    .select((eb) => {
      const firstActivityStartDate = eb
        .selectFrom("sale_item")
        .innerJoin("participation", "participation.sale_item_id", "sale_item.id")
        .whereRef("participation.participant_id", "=", `participant.id`)
        .select((eb) => lower(eb.ref("sale_item.duration")).as("start_date"))
        .limit(1);

      return [
        "user.email",
        "person.user_id",
        "person.full_name",
        "person.first_name",
        "person.last_name",
        "customer.establishment_id",
        sql<number>`(date_part('years', age(${eb.fn.coalesce(firstActivityStartDate, nowValue)}, ${eb.ref("participant.birth_date")})))`.as(
          "age",
        ),
      ];
    });
};

export const participantWaiverWithFiles = (db: Kysely<DB>) =>
  db
    .selectFrom("participant_waiver")
    .selectAll("participant_waiver")
    .select((eb) => [jsonArrayFrom(waiverFileUploadsQb(kysely, eb.ref("participant_waiver.id"))).as("files")]);

const participationWaiversQbBase = waiversEb
  .leftJoin("waiver_establishment", (join) =>
    join
      .onRef("waiver_establishment.waiver_id", "=", "waiver.id")
      .onRef("waiver_establishment.establishment_id", "=", contextRef("customer", "establishment_id")),
  )
  .leftJoin("sale_item", (join) =>
    join
      .on("sale_item.form_id", "in", (eb) =>
        eb.selectFrom("form_waiver").where("form_waiver.waiver_id", "=", eb.ref("waiver.id")).select("form_waiver.form_id"),
      )
      .on(
        "sale_item.id",
        "in",
        kysely
          .selectFrom("participation")
          .select("participation.sale_item_id")
          .where("participation.participant_id", "=", contextRef("participant", "id")),
      ),
  )
  .leftJoin("participation", (join) =>
    join.onRef("participation.sale_item_id", "=", "sale_item.id").on("participation.participant_id", "=", contextRef("participant", "id")),
  )
  .where((eb) =>
    eb.or([
      eb.and([
        eb(
          contextRef("participant", "form_id"),
          "in",
          eb.selectFrom("form_waiver").where("form_waiver.waiver_id", "=", eb.ref("waiver.id")).select("form_waiver.form_id"),
        ),
        eb("participation.id", "is", null),
      ]),
      eb("participation.participant_id", "=", contextRef("participant", "id")),
    ]),
  );

const advancedParticipantWaiversQb = kysely
  .selectFrom(
    kysely
      .selectFrom("participant_waiver")
      .leftJoin("participation_waiver", (join) =>
        join
          .onRef("participation_waiver.participant_waiver_id", "=", "participant_waiver.id")
          .on("participation_waiver.participant_id", "=", contextRef("participant", "id"))
          .on("participation_waiver.sale_item_id", "is not distinct from", contextRef("sale_item", "id")),
      )
      .where((eb) =>
        eb.or([
          eb("participant_waiver.customer_id", "=", contextRef("participant", "customer_id")),
          eb("participant_waiver.participant_id", "=", contextRef("participant", "id")),
        ]),
      )
      .where("participant_waiver.waiver_type", "=", contextRef("waiver", "type"))
      .where("participant_waiver.waiver_id", "=", contextRef("waiver", "id"))
      .where("participation_waiver.manually_approved", "is distinct from", false)
      .where((eb) =>
        eb.or([
          eb("participation_waiver.manually_approved", "is not distinct from", true),
          eb(
            "participant_waiver.validity_duration",
            "=",
            eb.fn.coalesce(contextRef("waiver_establishment", "validity_duration"), contextRef("waiver", "validity_duration")),
          ),
          eb("participant_waiver.participant_id", "=", contextRef("participant", "id")),
        ]),
      )
      .selectAll("participant_waiver")
      .select((eb) => {
        const signatureQb = eb.selectFrom("signature").where("signature.participant_waiver_id", "=", eb.ref("participant_waiver.id"));
        const signedDate = signatureQb.where("signature.signed_by_representative", "=", false).select("signature.signed_at").limit(1);
        // const fromTimestamptz = eb.fn.coalesce(signedDate, "allP.created_at");
        const fromTimestamptz = signedDate;
        const validFrom = sql<string>`((${fromTimestamptz}):: date)`;
        const validTo = sql<string>`((${fromTimestamptz}:: date + ${eb.ref("participant_waiver.validity_duration")}):: date)`;
        const participantWaiverValidityRange = dateRange(validFrom, validTo);
        const readWaiverValidTo = sql<string | null>`(${eb.ref("participant_waiver.created_at")} + ${eb.ref(
          "participant_waiver.validity_duration",
        )})`;
        const isValidAfterRegistrationDate = eb(contextRef("participant", "created_at"), "<=", readWaiverValidTo);
        const isForThisParticipant = eb("participant_waiver.participant_id", "=", contextRef("participant", "id"));
        // const participantCreatedAtAsDate = tstzToDate(contextRef("participant", "created_at"), contextRef("region", "timezone"));
        const participantCreatedAtAsDate = sql<string>`(${contextRef("participant", "created_at")}:: date)`;
        const isValidWithinActivityDuration = eb(
          participantWaiverValidityRange,
          "@>",
          eb.fn.coalesce(contextRef("sale_item", "duration"), dateRange(participantCreatedAtAsDate, participantCreatedAtAsDate)),
        );
        const isSignedAfterActivityStart = eb(fromTimestamptz, ">", lower(contextRef("sale_item", "duration")));
        const isAlwaysValidForThisParticipantWithUnlimitedDuration = eb.and([
          eb(eb.ref("participant_waiver.validity_duration"), "is", null),
          isForThisParticipant,
        ]);
        const isAlwaysValidForPendingParticipant = eb.and([isForThisParticipant, eb(contextRef("participation", "id"), "is", null)]);
        const isValidForActivityDuration = eb.or([
          isValidWithinActivityDuration,
          isAlwaysValidForPendingParticipant,
          isAlwaysValidForThisParticipantWithUnlimitedDuration,
        ]);
        const isSameName = eb("participant_waiver.customer_id", "=", contextRef("participant", "customer_id"));
        const isManuallyApproved = eb(contextRef("participation_waiver", "manually_approved"), "=", true);

        const isSigned = eb.exists(signatureQb);
        const isSignedIfRequired = eb.or([isSigned, eb("participant_waiver.signature_required", "=", false)]);
        const validUploadIfRequired = eb("participant_waiver.upload_required", "=", eb.ref("participant_waiver.upload_approved"));
        const isReadonly = eb.and([
          eb("participant_waiver.signature_required", "=", false),
          eb("participant_waiver.upload_required", "=", false),
        ]);
        const isValid = eb.or([
          eb.and([isSameName, isSignedIfRequired, isValidForActivityDuration, validUploadIfRequired]),
          isManuallyApproved,
          eb.and([eb.or([isValidAfterRegistrationDate, isForThisParticipant]), isReadonly]),
        ]);
        return [
          isValid.as("isValid"),
          isSigned.as("isSigned"),
          isSameName.as("isSameName"),
          validFrom.as("valid_from"),
          validTo.as("valid_to"),
          isSignedAfterActivityStart.as("isSignedAfterActivityStart"),
          lower(contextRef("sale_item", "duration")).as("activity_duration_start"),
          contextRef("sale_item", "duration").as("activity_duration"),
          isForThisParticipant.as("isForThisParticipant"),
          eb.ref("participant_waiver.validity_duration").as("validity_duration"),
          isValidWithinActivityDuration.as("isValidWithinActivityDuration"),
          isAlwaysValidForPendingParticipant.as("isAlwaysValidForPendingParticipant"),
          isAlwaysValidForThisParticipantWithUnlimitedDuration.as("isAlwaysValidForThisParticipantWithUnlimitedDuration"),
          "participant_waiver.created_at",
          formatDatetime(eb.ref("participant_waiver.created_at"), "DD Mon ''YY", sql.lit(null)).as("created_at_formatted"),
          "participation_waiver.manually_approved",
          "participation_waiver.id as participation_waiver_id",
        ];
      })
      .as("participant_waiver_advanced"),
  )
  .orderBy("participant_waiver_advanced.isValid", descNullsLast)
  // .orderBy('partwaiad')
  .orderBy("participant_waiver_advanced.id");

export const myParticipatonWaiversQb = participationWaiversQbBase.select((eb) => [
  "waiver.id",
  // eb.exists(kysely.selectFrom("participant_waiver")).as("valid"),
  eb.exists(advancedParticipantWaiversQb.where("participant_waiver_advanced.isValid", "=", true)).as("valid"),
]);

export const myParticipatonWaiversAsBoolean = (db: Kysely<DB>, signatureRequired: boolean) =>
  db
    .selectFrom(myParticipatonWaiversQb.where("waiver.type", signatureRequired ? "!=" : "=", "read" satisfies WaiverType).as("myWaivers"))
    .select("myWaivers.valid")
    .orderBy("myWaivers.valid asc")
    .limit(1);

export const participationWaiversQb = participationWaiversQbBase.select((eb) => {
  // const signatureQb = eb.selectFrom("signature").where("signature.participant_waiver_id", "=", eb.ref("participant_waiver.id"));
  // const fullSignatureQb = signatureQb.selectAll("signature");

  // const lowerQb = eb.fn<string | null>("min", [eb.fn("lower", [eb.ref("activity.duration")])]);
  // const upperQb = sql<string | null>`(max ( upper (${eb.ref("activity.duration")})))`;
  // const durationInDays = sql<number | null>`(${upperQb} - ${lowerQb})`;
  // const daysInterval = sql<string>`(make_interval(days => ${durationInDays}))`;

  // const minimalSignDate = sql`$({upperQb} - ${eb.ref("waiver.validity_duration")})`;

  const validityDuration = eb.fn.coalesce(eb.ref("waiver_establishment.validity_duration"), eb.ref("waiver.validity_duration"));

  const upperQb = sql<string | null>`(upper (${eb.ref("sale_item.duration")}) - 1)`;
  const minSignDate = sql<string | null>`(((${upperQb}) - ${validityDuration}):: date)`;
  const canSign = eb.or([eb(minSignDate, "is", null), eb(nowValue, ">=", minSignDate)]);

  return [
    "waiver.id as waiver_id",
    "waiver.type",
    "waiver.slug",
    "sale_item.duration",
    "participation.id as participation_id",
    "waiver.validity_duration",
    "waiver_establishment.validity_duration as validity_duration_establishment",
    validityDuration.as("validity_duration_final"),
    minSignDate.as("minSignDate"),
    formatDatetime(minSignDate, "DD Mon ''YY", contextRef("region", "timezone")).as("minSignDateFormatted"),
    canSign.as("canSign"),
    eb.exists(advancedParticipantWaiversQb.where("participant_waiver_advanced.isValid", "=", true)).as("valid"),
    // durationInDays.as("duration_in_days"),
    // daysInterval.as("duration_interval"),
    // minimalSignDate.as("min_sign_date"),
    eb
      .selectFrom("waiver_translation")
      .select("waiver_translation.name")
      .where("waiver_translation.language_code", "=", "en")
      .where("waiver_translation.waiver_id", "=", eb.ref("waiver.id"))
      .as("name"),
    jsonArrayFrom(
      advancedParticipantWaiversQb
        // .where("participant_waiver_advanced.isValid", "=", true)
        .selectAll("participant_waiver_advanced")
        .select((eb) => [
          formatDatetime(eb.ref("participant_waiver_advanced.valid_from"), "DD Mon ''YY", contextRef("region", "timezone")).as(
            "valid_from_formatted",
          ),
          "participant_waiver_advanced.upload_required",
          formatDatetime(eb.ref("participant_waiver_advanced.valid_to"), "DD Mon ''YY", contextRef("region", "timezone")).as(
            "valid_to_formatted",
          ),
          jsonArrayFrom(
            eb
              .selectFrom("signature")
              .select((eb) => [
                "signature.id",
                "signature.participant_waiver_id",
                "signature.signed_at",
                formatDatetime(eb.ref("signature.signed_at"), "DD Mon ''YY", contextRef("region", "timezone")).as("signed_at_formatted"),
              ])
              .orderBy("signature.signed_at asc")
              .where("signature.participant_waiver_id", "=", eb.ref("participant_waiver_advanced.id")),
          ).as("signatures"),
          eb
            .exists(
              eb
                .selectFrom("callback")
                .where("callback.handled_at", "is", null)
                .where("participant_waiver_advanced.id", "is not", null)
                .where("callback.target_id", "=", eb.ref("participant_waiver_advanced.id"))
                .where("name", "=", "participant_waiver_mutation" satisfies CallbackName),
            )
            .as("generating_pdf"),
          jsonArrayFrom(waiverFileUploadsQb(kysely, eb.ref("participant_waiver_advanced.id"))).as("files"),
        ]),
    ).as("participant_waivers"),
    // jsonArrayFrom().as(''),
    // jsonObjectFrom(fullSignatureQb.where("signature.signed_by_representative", "=", false)).as("signature"),
    // jsonObjectFrom(fullSignatureQb.where("signature.signed_by_representative", "=", true)).as("signature_guardian"),
  ];
});

export const participantQb = (db: Kysely<DB>) =>
  participantSimpleQb(db)
    .orderBy("participant.created_at")
    .select((eb) => {
      return [
        jsonArrayFrom(fileTargetsQb(db, "participant_diving_certificate", eb.ref("participant.id"))).as("file_diving_certificates"),
        jsonArrayFrom(fileTargetsQb(db, "participant_passport", eb.ref("participant.id"))).as("files_passport"),
        jsonArrayFrom(
          eb
            .selectFrom("participation_addon")
            .innerJoin("participation", "participation.id", "participation_addon.participation_id")
            .whereRef("participation.participant_id", "=", "participant.id")
            .selectAll("participation_addon"),
        ).as("addons"),
        jsonObjectFrom(
          eb.selectFrom("user").whereRef("user.id", "=", "person.user_id").select(["user.email", "user.id", "user.display_name"]),
        ).as("user"),
      ];
    });

export const createPersonSearchQb = (search?: string | null) =>
  kysely
    .selectFrom("person as s_person")
    .innerJoin("user as s_user", "s_user.id", "s_person.user_id")
    .orderBy("s_person.first_name", "asc")
    .orderBy("s_person.last_name", "asc")
    .orderBy("s_user.email", "asc")
    .$call((eb) => {
      if (!search) return eb;
      return eb.where((eb) =>
        eb.or([
          eb("s_person.full_name", "ilike", `%${search}%`),
          // eb("s_person.first_name", "ilike", `%${search}%`),
          // eb("s_person.last_name", "ilike", `%${search}%`),
          eb("s_user.email", "ilike", `%${search}%`),
        ]),
      );
    });

const createFormQb = (participantEb: Pick<ExpressionBuilder<DB, "participant">, "selectFrom">) =>
  participantEb
    .selectFrom("form")
    .where((eb) =>
      eb.or([
        eb("form.id", "=", eb.ref("participant.form_id")),
        eb(
          "form.id",
          "in",
          eb
            .selectFrom("sale_item")
            .innerJoin("participation", "participation.sale_item_id", "sale_item.id")
            .where("participation.participant_id", "=", eb.ref("participant.id"))
            .select("sale_item.form_id"),
        ),
      ]),
    );

export const incompleteFieldsQb = (participantEb: Pick<ExpressionBuilder<DB, "participant">, "selectFrom" | "ref">) => {
  const formQb = createFormQb(participantEb);

  return participantEb
    .selectFrom(
      formQb
        .innerJoin("field", "field.form_id", "form.id")
        .select("field.name as field_name")
        .where("field.status", ">", 1)
        .where((eb) => {
          const fieldCmprs = keys(participantFields)
            .filter((fieldKey) => !lockedFields.includes(fieldKey))
            .map((fiedlKey) => {
              const baseCmpr = eb("field.name", "=", fiedlKey);
              const fieldImp = participantFieldImpls[fiedlKey];
              const isValidCmpr = participantFieldsServer[fiedlKey as keyof typeof participantFieldsServer]?.isValidCmpr;
              if (isValidCmpr) {
                return eb.and([baseCmpr, eb.not(isValidCmpr(eb))]);
              }

              if ("field" in fieldImp && fieldImp.field) {
                const defaultIsInvalidCmpr = eb(`participant.${fieldImp.field}`, "is", null);
                return eb.and([baseCmpr, defaultIsInvalidCmpr]);
              }
              return baseCmpr;
            });
          return eb.or(fieldCmprs);
        })
        .as("incomplete_fields"),
    )
    .select((eb) => eb.fn.coalesce(arrayAggDisinct(eb.ref("incomplete_fields.field_name")), emptyTextArray).as("incomplete_fields"));
};

export const updateParticipantsQb = (trx: Kysely<DB>) =>
  trx
    .updateTable("participant")
    .from("customer")
    .whereRef("customer.id", "=", "participant.customer_id")
    .set((eb) => ({
      cached_incomplete_fields: incompleteFieldsQb(eb) as any,
      cached_signature_waivers_valid: myParticipatonWaiversAsBoolean(trx, true) as any,
      cached_read_waivers_valid: myParticipatonWaiversAsBoolean(trx, false) as any,
    }));

export const participatingParticipantIdQb = kysely
  .selectFrom("participation")
  .where("participation.participant_id", "is not", null)
  .select("participation.participant_id");
