import React, { ReactNode } from "react";
import { twMerge } from "tailwind-merge";
import { CheckDoneIcon, NotDoneIcon, RegistrationIcon, SignatureIcon } from "~/components/Icons";
import { convertHeight, convertShoesize, convertWeight, strToHeightUnit, strToShoesizeUnit, strToWeightUnit } from "~/utils/metric-helpers";
import { Tooltip } from "~/components/base/tooltip";
import { InfoIcon } from "lucide-react";
import { defaultNotFilledValue } from "~/misc/vars";
import { RInput, RLabel } from "~/components/ResourceInputs";
import { at_now_value } from "~/kysely/db-static-vars";
import { SubmitButton } from "~/components/base/Button";

const ParticipantBadge = (props: { incomplete?: string[] | null; children: ReactNode }) => {
  const isDone = props.incomplete?.length === 0;
  return (
    <div
      className={twMerge(
        "relative w-[22px] h-[22px] bg-secondary rounded-md flex items-center justify-center opacity-50",
        props.incomplete && "opacity-100",
      )}
    >
      {props.children}
      {props.incomplete && (
        <div
          className={twMerge(
            "w-[16px] h-[16px] p-[2px] absolute -right-3 bottom-0 border-2 border-white rounded-full bg-red-500 text-white",
            isDone && "bg-[#27AE60]",
          )}
        >
          {isDone ? <CheckDoneIcon className="w-full h-full" /> : <NotDoneIcon className="w-full h-full" />}
        </div>
      )}
    </div>
  );
};

export const ParticipantStatusBadges = (props: {
  cached_incomplete_fields: string[] | null;
  cached_read_waivers_valid: any;
  cached_signature_waivers_valid: any;
}) => {
  const incompleteFields = [...(props.cached_incomplete_fields || [])];
  if (props.cached_read_waivers_valid === false) {
    incompleteFields.push("waiver");
  }
  return (
    <div className="flex flex-row gap-6">
      <ParticipantBadge incomplete={incompleteFields}>
        <RegistrationIcon className="text-white w-[14px] h-[14px]" />
      </ParticipantBadge>
      <ParticipantBadge
        incomplete={props.cached_signature_waivers_valid === null ? null : !props.cached_signature_waivers_valid ? ["invalid"] : []}
      >
        <SignatureIcon className="text-white w-[15px] h-[15px]" />
      </ParticipantBadge>
    </div>
  );
};

export const HeightInfo = (args: { value?: number | null; unit?: string | null; default_unit?: string | null }) => {
  if (!args.unit || !args.value) return "-";
  if (args.default_unit && args.default_unit !== args.unit) {
    const convertedWeight = convertHeight(args.value, strToHeightUnit(args.unit), strToHeightUnit(args.default_unit));
    return (
      <div className="flex flex-row gap-1 items-center">
        {convertedWeight}
        {args.default_unit}
        <Tooltip
          description={
            <div>
              Converted from{" "}
              <strong>
                {args.value}
                {args.unit}
              </strong>{" "}
              to{" "}
              <strong>
                {convertedWeight}
                {args.default_unit}
              </strong>
            </div>
          }
        >
          <InfoIcon className="w-3 h-3 text-primary" />
        </Tooltip>
      </div>
    );
  }
  return args.value + args.unit + "";
};

export const WeightInfo = (args: { value?: number | null; unit?: string | null; default_unit?: string | null }) => {
  if (!args.unit || !args.value) return defaultNotFilledValue;
  if (args.default_unit && args.unit && args.default_unit !== args.unit) {
    const convertedWeight = convertWeight(args.value, strToWeightUnit(args.unit), strToWeightUnit(args.default_unit));
    return (
      <div className="flex flex-row gap-1 items-center">
        {convertedWeight}
        {args.default_unit}
        <Tooltip
          description={
            <div>
              Converted from{" "}
              <strong>
                {args.value}
                {args.unit}
              </strong>{" "}
              to{" "}
              <strong>
                {convertedWeight}
                {args.default_unit}
              </strong>
            </div>
          }
        >
          <InfoIcon className="w-3 h-3 text-primary" />
        </Tooltip>
      </div>
    );
  }
  return args.value + args.unit + "";
};

export const ShoeSizeInfo = (args: { value?: number | null; unit?: string | null; default_unit?: string | null }) => {
  if (!args.unit || !args.value) return defaultNotFilledValue;
  const filledShoesize = args.value + args.unit + "";
  if (!args.default_unit || args.default_unit === args.unit) return filledShoesize;
  const convertedShoeSize = convertShoesize(args.value, strToShoesizeUnit(args.unit), strToShoesizeUnit(args.default_unit));
  if (!convertedShoeSize)
    return (
      <div className="flex flex-row gap-1 items-center">
        {filledShoesize}
        <Tooltip
          description={
            <div>
              Could not convert{" "}
              <strong>
                {args.value}
                {args.unit}
              </strong>{" "}
              to <strong>{args.default_unit}</strong>
            </div>
          }
        >
          <InfoIcon className="w-3 h-3 text-primary" />
        </Tooltip>
      </div>
    );
  return (
    <div className="flex flex-row gap-1 items-center">
      {convertedShoeSize}
      {args.default_unit}
      <Tooltip
        description={
          <div>
            Converted from{" "}
            <strong>
              {args.value}
              {args.unit}
            </strong>{" "}
            to{" "}
            <strong>
              {convertedShoeSize}
              {args.default_unit}
            </strong>
          </div>
        }
      >
        <InfoIcon className="w-3 h-3 text-primary" />
      </Tooltip>
    </div>
  );
};

export const AcceptDigitalSigning = (props: { participant: { id: string } }) => {
  return (
    <div className="space-y-3">
      <RInput table={"participant"} field={"id"} value={props.participant.id} />
      <div className={"inline-flex flex-row items-center gap-3"}>
        <RLabel table={"participant"} field={"data.digital_signing_agreed_at"}>
          I consent to electronically sign the waivers presented, acknowledging my e-signature has the same weight as a written signature.
        </RLabel>
        <RInput
          table={"participant"}
          field={"data.digital_signing_agreed_at"}
          value={at_now_value}
          type={"checkbox"}
          className={"checkbox"}
          required
        />
      </div>
      <SubmitButton className="btn btn-primary">Continue</SubmitButton>
    </div>
  );
};
