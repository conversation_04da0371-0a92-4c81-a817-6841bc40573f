import { Trans } from "@lingui/react/macro";
import React, { type ReactNode } from "react";

export type Option = [value: string, label: ReactNode];

export const mealPreferences: Option[] = [
  ["vegetarian", <Trans>Vegetarian</Trans>],
  ["vegan", <Trans>Vegan</Trans>],
  ["gluten_free", <Trans>Gluten free</Trans>],
  ["na", <Trans>No preference</Trans>],
  ["bring_my_own", <Trans>Bring my own</Trans>],
];

export const formatMealPref = (str?: string | null) => {
  return mealPreferences.find(([key]) => key === str)?.[1];
};
