import { QbArgs, userSessionId } from "~/domain/member/member-queries.server";
import { activeUserSessionQb, memberIsAdminOrOwnerQb } from "~/domain/member/member-queries.server";
import { AdminLevel } from "~/domain/member/member-vars";
import { kysely } from "~/misc/database.server";
import { addToNow } from "~/kysely/kysely-helpers";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";

export const myRegisteredParticipants = (args: QbArgs) =>
  args.trx
    .selectFrom("participant as _participant")
    .innerJoin("customer as _customer", "_customer.id", "_participant.customer_id")
    .innerJoin("person as _person", "_person.id", "_customer.person_id")
    .where((eb) =>
      eb.or([
        eb("_person.user_id", "=", activeUserSessionQb(args, true).select("_user.id")),
        eb('_participant.created_by_user_session_id', '=', userSessionId(args, false))
      ]),
    );

export const allowedParticipantsForMember = (args: QbArgs, minLevel: AdminLevel = "write") => {
  return args.trx
    .selectFrom("participant as _participant")
    .innerJoin("customer as _customer", "_customer.id", "_participant.customer_id")
    .where("_customer.establishment_id", "in", memberIsAdminOrOwnerQb(args, minLevel).select("_member.establishment_id"));
};

const allowedParticipantTokenQb = kysely
  .selectFrom("participant_token")
  .where("participant_token.created_at", ">", addToNow(-30, "minutes"));

const allowedParticipantIdsForToken = (args: { request: Request }) => {
  const url = new URL(args.request.url);
  const record = paramsToRecord(url.searchParams);
  return allowedParticipantTokenQb.where("participant_token.token", "=", record.persist_token);
};

export const allowedParticipantIdsFor = (
  args: QbArgs & {
    request: Request;
  },
  minLevel: AdminLevel = "write",
) => {
  return args.trx.selectFrom(
    allowedParticipantsForMember(args, minLevel)
      .select("_participant.id")
      .union(myRegisteredParticipants(args).select("_participant.id"))
      .union(allowedParticipantIdsForToken(args).select("participant_token.participant_id as id"))
      .as("_participant"),
  );
};

export const allowedCustomerIdsFor = (
  args: QbArgs & {
    request: Request;
  },
  minLevel: AdminLevel = "write",
) => {
  return args.trx.selectFrom(
    allowedParticipantsForMember(args, minLevel)
      .select("_participant.customer_id as id")
      .union(myRegisteredParticipants(args).select("_participant.customer_id as id"))
      .union(
        allowedParticipantIdsForToken(args)
          .innerJoin("participant", "participant.id", "participant_token.participant_id")
          .select("participant.customer_id as id"),
      )
      .as("_customer"),
  );
};
