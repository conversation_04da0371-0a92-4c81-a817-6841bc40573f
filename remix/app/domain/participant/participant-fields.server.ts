import { DB, Participant } from "~/kysely/db";
import { FileTargetValue } from "~/domain/file/file-resource";
import { ExpressionBuilder } from "kysely";
import { ParticipantFieldKey } from "~/domain/participant/ParticipantFields";

const getNumberUploadIsValidFunc =
  (field: keyof Participant, fileTarget: FileTargetValue) => (eb: ExpressionBuilder<DB, "participant" | "field">) => {
    const fileUploadCmpr = eb.exists(
      eb
        .selectFrom("file_target")
        .where("file_target.target", "=", fileTarget)
        .where("file_target.target_id", "=", eb.ref("participant.id")),
    );
    const numberFilledCmpr = eb(`participant.${field}`, "is not", null);
    return eb.or([
      eb("field.status", "<", 4),
      eb.and([eb("field.status", "=", 4), eb.or([fileUploadCmpr, numberFilledCmpr])]),
      eb.and([eb("field.status", "=", 5), numberFilledCmpr]),
      eb.and([eb("field.status", "=", 6), fileUploadCmpr]),
      eb.and([eb("field.status", "=", 7), eb.and([fileUploadCmpr, numberFilledCmpr])]),
    ]);
  };

export const participantFieldsServer = {
  diving_certificate_number: {
    isValidCmpr: getNumberUploadIsValidFunc("diving_certificate_number", "participant_diving_certificate"),
  },
  passport_number: {
    isValidCmpr: getNumberUploadIsValidFunc("passport_number", "participant_passport"),
  },
  // passport_upload: {
  //   isValidCmpr: (eb: ExpressionBuilder<DB, "participant" | "field">) => {
  //     const fileUploadCmpr = eb.exists(
  //       eb
  //         .selectFrom("file_target")
  //         .where("file_target.target", "=", "participant_passport" satisfies FileTargetValue)
  //         .where("file_target.target_id", "=", eb.ref("participant.id")),
  //     );
  //     return eb.and([eb("field.status", ">", 1), fileUploadCmpr]);
  //   },
  // },
} satisfies Partial<Record<ParticipantFieldKey, { isValidCmpr: unknown }>>;
