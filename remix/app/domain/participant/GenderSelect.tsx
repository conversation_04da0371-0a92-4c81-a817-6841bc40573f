import { Trans } from "@lingui/react/macro";
import React, { useState } from "react";
import { RSelect } from "~/components/ResourceInputs";
import { useIsInterative } from "~/hooks/hooks";
import { useBoolean } from "~/hooks/use-boolean";

export const genderOptions = {
  male: <Trans>Male</Trans>,
  female: <Trans>Female</Trans>,
  non_binary: <Trans>Non-binary</Trans>,
  prefer_not_to_say: <Trans>Prefer not to say</Trans>,
} as const;

export type GenderOption = keyof typeof genderOptions;

const selfDecribeKey = "self_describe";

export const GenderSelect = (props: {
  disabled?: boolean;
  required?: boolean;
  className?: string;
  name: string;
  defaultValue?: string;
}) => {
  const isInterative = useIsInterative();
  const selfDescribe = useBoolean();
  const selfDescribedGender = !genderOptions[props.defaultValue as GenderOption] && props.defaultValue;

  if (selfDescribe.isOn) {
    return (
      <div className="flex gap-2">
        <input
          type="text"
          name={props.name}
          className="input input-sm"
          disabled={props.disabled}
          required={props.required}
          defaultValue={selfDescribedGender || ""}
          maxLength={32}
        />
        <button type="button" className="btn btn-basic btn-sm" onClick={() => selfDescribe.off()} tabIndex={-1}>
          Cancel
        </button>
      </div>
    );
  }

  return (
    <RSelect
      disabled={props.disabled}
      required={props.required}
      className={props.className}
      name={props.name}
      onChange={(e) => {
        if (e.target.value === selfDecribeKey) {
          selfDescribe.on();
          setTimeout(() => {
            document.getElementsByName(props.name)?.[0]?.focus();
          }, 10);
        }
      }}
      defaultValue={props.defaultValue || ""}
      table="participant"
      field="data.gender"
    >
      <option value="">
        <Trans>Select gender</Trans>
      </option>
      {Object.entries(genderOptions).map(([value, label]) => (
        <option key={value} value={value}>
          {label}
        </option>
      ))}
      {!!selfDescribedGender && <option value={props.defaultValue}>{props.defaultValue} (self described)</option>}
      {isInterative && <option value={selfDecribeKey}>{selfDescribedGender ? "Change" : "Self describe"}</option>}
    </RSelect>
  );
};
