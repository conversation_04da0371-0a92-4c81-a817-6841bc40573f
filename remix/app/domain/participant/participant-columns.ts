export const columnKeys = [
  "name",
  "booking_reference",
  "country",
  "address",
  "stay",
  "booking_source",
  "age",
  "birth_date",
  "gender",
  "dives",
  "level",
  "registration",
  "waivers",
  "activity",
  "comment",
  "payment_status",
  "contact_number",
  "instructor",
  "meet",
  "time",
  "location",
  "diet",
  "allergies",
  "participant_comment",
  "booking_internal_note",
  "height",
  "weight",
  "wetsuit",
  "boots",
  "bcd_size",
  "passport_number",
  "room",
  "insurance",
  "emergency_contact_name",
  "emergency_contact_phone",
  "emergency_contact_relationship",
  "diving_certificate_organization",
  "diving_certificate_number",
  "years_of_experience",
  "last_dive_within_months",
  "my_gear",
  "weightbelt",
  "referral_source",
  "allow_contact_for_experience",
  "instagram",
  "direct_booking",
  "trip_time",
  "trip_type",
  "gear_check",
] as const;

export const directions = ["asc", "desc"] as const;

export type Direction = (typeof directions)[number];

export type ColumnKey = (typeof columnKeys)[number];

export type SortKey = `${ColumnKey}_${Direction}`;

export const sortableColumns = {
  instructor_name: { name: "Instructor name", column: "instructor" },
  participant_name: { name: "Participant name", column: "name" },
  activity: { name: "Activity", column: "activity" },
  booking_reference: { name: "Booking reference", column: "booking_reference" },
  participant_filled: { name: "Participant filled", column: "name" },
} satisfies Record<string, { name: string; column: ColumnKey }>;

// export const sortableColumns = ["instructor", "activity", "booking_reference", "name"] satisfies ColumnKey[];

export type SortableColumnKey = keyof typeof sortableColumns;

export const detailModals = [
  "participant",
  "participant_comment",
  "trip",
  "internal_booking_note",
  "comment",
  "customise",
  "reorder",
  "sorting",
  "booking",
] as const;

export type DetailModalName = (typeof detailModals)[number];

export interface Sort {
  key: SortableColumnKey;
  direction: Direction;
}

export const defaultOrderColumns: Sort[] = [
  {
    key: "instructor_name",
    direction: "asc",
  },
  {
    key: "participant_filled",
    direction: "desc",
  },
  { key: "activity", direction: "asc" },
  { key: "booking_reference", direction: "asc" },
  {
    key: "participant_name",
    direction: "asc",
  },
];

export const toValidOrderableColumns = (columns: { key: string; direction: string }[]) => {
  if (!(columns instanceof Array)) return [];
  return columns
    .map((column) => ({
      ...column,
      direction: directions.find((direction) => direction === column.direction) || "asc",
    }))
    .filter((column): column is Sort => {
      return !!sortableColumns[column.key as SortableColumnKey];
    });
};
