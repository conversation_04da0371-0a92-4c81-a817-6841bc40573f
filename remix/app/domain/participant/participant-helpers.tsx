import { Trans } from "@lingui/react/macro";
import { twMerge } from "tailwind-merge";
import { ReactNode } from "react";
import { defaultNotFilledValue, defaultUnkownValue } from "~/misc/vars";
import { NonUndefined } from "@use-gesture/react";

const needRefreshedThreshold = 12;

export const lastDivedValues = {
  3: { long: <Trans>Within the last 3 months</Trans>, short: <Trans>&lt;3mo</Trans> },
  6: { long: <Trans>Within the last 6 months</Trans>, short: <Trans>&lt;6mo</Trans> },
  12: { long: <Trans>Within the last year</Trans>, short: <Trans>&lt;1y</Trans> },
  13: { long: <Trans>More than 1 year ago</Trans>, short: <Trans>&gt;1y</Trans> },
  25: { long: <Trans>more than 2 years ago</Trans>, short: <Trans>&gt;2y</Trans> },
} satisfies Record<number, { long: ReactNode; short: ReactNode }>;

export const getLastDivedKey = (months?: number | null): keyof typeof lastDivedValues | null => {
  if (months === null || months === undefined) return null;
  if (months <= 3) return 3;
  if (months <= 6) return 6;
  if (months <= 12) return 12;
  if (months <= 24) return 13;
  return 25;
};

export const getLastDivedText = (month?: number | null) => {
  const key = getLastDivedKey(month);
  return key && lastDivedValues[key].long;
};

export const getLastDivedShort = (month?: number | null) => {
  const key = getLastDivedKey(month);
  if (!key) return null;
  const lastDive = lastDivedValues[key];
  return key && <span className={twMerge(key > needRefreshedThreshold && "font-bold text-red-500")}>{lastDive.short}</span>;
};

export const safeFormat = <T extends object | null, R extends ReactNode>(item: T, fn: (item: NonNullable<NonUndefined<T>>) => R) => {
  if (!item) return defaultUnkownValue;
  const formattedValue = fn(item as NonNullable<NonUndefined<T>>);
  return formattedValue || defaultNotFilledValue;
};
