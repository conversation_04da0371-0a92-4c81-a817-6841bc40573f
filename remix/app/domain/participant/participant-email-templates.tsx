import React from "react";
import { Head, Html, Link, Section, Text } from "@react-email/components";
import { StyledLink, TailwindBody } from "~/components/Mail";
import { ParticipationActivityReminder } from "~/domain/email/participation-query.server";
import { getFullUrl } from "~/misc/helpers";
import { _booking_detail, _participant_detail } from "~/misc/paths";

export const placeholder = {
  participant_full_name: "John Doe",
  duration: "2025-01-01" as string | null,
  booking_host: "" as string | null,
  booking_id: "123",
  participant_id: "456",
  name: "Activity",
  slug: "activity" as string | null,
  cached_signature_waivers_valid: false as boolean | null,
  cached_read_waivers_valid: false as boolean | null,
  cached_incomplete_fields: [] as string[] | null,
  participation_id: "123",
  establishment_email: "<EMAIL>" as string | null,
  user_id: "123",
  operator_name: "{operator}",
  participant_email: "<EMAIL>"
} satisfies Partial<ParticipationActivityReminder>;

export const ActivityReminderEmail = (props: { reminderTo: typeof placeholder }) => {
  const registrationCompleted = !props.reminderTo.cached_incomplete_fields?.length && props.reminderTo.cached_read_waivers_valid;
  const signingCompleted = props.reminderTo.cached_signature_waivers_valid;
  const bookingHost = props.reminderTo.booking_host;
  return (
    <Html>
      <Head />
      <TailwindBody>
        <Section>
          <Text>Hi {props.reminderTo.participant_full_name},</Text>
          <Text>Just a quick reminder that your activity is scheduled for {props.reminderTo.duration}.</Text>
          {(!signingCompleted || !registrationCompleted) && (
            <Text>We noticed you still have <Link className="text-primary hover:underline"
                                                  href={bookingHost ? getFullUrl(bookingHost) + _participant_detail(props.reminderTo.participant_id) : ''}>
                pending paperwork
  </Link>. Please complete this online before your activity begins.</Text>
          )}
          {/*{!signingCompleted && <Text>You still need to sign your waivers.</Text>}*/}
              {/*{!registrationCompleted && <Text>You still need to complete your registration.</Text>}*/}
              <Text>
                We advice you to double check the <Link className="text-primary hover:underline"
                                                        href={bookingHost ? getFullUrl(bookingHost) + _booking_detail(props.reminderTo.booking_id) : '.'}>Booking</Link> for
                the latest details around your activity.
              </Text>
              {/*{registrationCompleted && signingCompleted ? (*/}
              {/*  <StyledLink href={getFullUrl(props.reminderTo.booking_host) + _booking_detail(props.reminderTo.booking_id)}>Booking</StyledLink>*/}
              {/*) : (*/}
              {/*  <StyledLink href={getFullUrl(props.reminderTo.booking_host) + _participant_detail(props.reminderTo.participant_id)}>*/}
              {/*    Complete registration*/}
              {/*  </StyledLink>*/}
              {/*)}*/}
              <Text>See you soon!</Text>
              <Text>Kind regards, Team {props.reminderTo.operator_name}</Text>
            </Section>
            </TailwindBody>
            </Html>
            );
          };
