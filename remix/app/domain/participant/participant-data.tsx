import { Trans } from "@lingui/react/macro";
import { keys } from "~/misc/helpers";

type Option = [value: string, label: string];

const nrOfDivesEnds = [0, 9, 19, 29, 39, 49, 69, 99, 299, 499, 999];
const nrOfDivesGeneratedOptions: Option[] = nrOfDivesEnds.slice(1).map((end, index) => {
  const start = nrOfDivesEnds[index]!;
  const value = `[${start + 1},${end + 1})`;
  const label = `${start + 1}-${end}`;
  return [value, label];
});
export const nrOfDivesOptions: Option[] = [["", "N/A"], ...nrOfDivesGeneratedOptions, ["[1000,)", "1000+"]];

const experienceInYearsEnds = [1, 3, 6, 9, 14, 19, 29, 39];
const experienceInYearsGeneratedOptions: Option[] = experienceInYearsEnds.slice(1).map((end, index) => {
  const start = experienceInYearsEnds[index]!;
  const value = `[${start + 1},${end + 1})`;
  const label = `${start + 1}-${end}`;
  return [value, label];
});
export const experienceInYearsOptions: Option[] = [["", "N/A"], ["(,2)", "<1"], ...experienceInYearsGeneratedOptions, ["[40,)", ">40"]];

export const formatExperienceinYears = (value?: string | null) => {
  return value && experienceInYearsGeneratedOptions.find((option) => option[0] === value)?.[1];
};

export const getNrOfDivesOption = (value?: string | null) =>
  (typeof value === "string" && (nrOfDivesOptions.find(([key]) => key === value) || [value, value])) || ["", ""];

export const gears = {
  mask: <Trans>Mask</Trans>,
  fins: <Trans>Fins</Trans>,
  BCD: <Trans>BCD</Trans>,
  regulator: <Trans>Regulator</Trans>,
  wetsuit: <Trans>Wetsuit</Trans>,
} as const;

export const gearKeys = keys(gears);

export const formatGear = (gear?: string | null) => gears[(gear || "") as keyof typeof gears] || gear;

export const referralSources = {
  friend: <Trans>Friend</Trans>,
  instagram: <Trans>Instagram</Trans>,
  facebook: <Trans>Facebook</Trans>,
  "walk-in": <Trans>Walk-in</Trans>,
  google: <Trans>Google</Trans>,
  "google maps": <Trans>Google Maps</Trans>,
  youtube: <Trans>YouTube</Trans>,
  hotel: <Trans>Hotel</Trans>,
  agency: <Trans>Agency</Trans>,
};

export const referralSourceKeys = keys(referralSources);
