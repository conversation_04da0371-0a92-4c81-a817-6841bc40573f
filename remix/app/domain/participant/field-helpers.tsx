import { EyeIcon } from "@heroicons/react/20/solid";
import { Asterisk, AsteriskIcon, EyeOffIcon } from "lucide-react";
import { ReactNode } from "react";

export interface FieldStatus {
  label: string;
  color: string;
  Comp: ReactNode;
  labelTooltip?: ReactNode;
}

const baseFieldStatusList: FieldStatus[] = [
  {
    label: "Hide",
    color: "grey",
    Comp: (
      <div className="flex flex-row gap-2 px-1 items-center">
        <EyeOffIcon className="w-4 h-4 text-slate-500" />
        Hide
      </div>
    ),
  },
  {
    label: "Show",
    color: "green",
    Comp: (
      <div className="flex flex-row gap-2 px-1 items-center">
        <EyeIcon className="w-4 h-4 text-green-600" />
        Show
      </div>
    ),
  },
];

export const defaultFieldStatusList: FieldStatus[] = [
  ...baseFieldStatusList,
  {
    label: "Must",
    color: "red",
    Comp: (
      <div className="flex flex-row gap-1 pr-1 items-center">
        <Asterisk className="w-6 h-6 text-primary" />
        Must
      </div>
    ),
  },
];

const fieldPlusUploadStatusKeys = ["hide", "show", "show_number", "show_upload", "require_or", "require_number", "require_upload"] as const;

export const fieldPlusUploadStatusList: FieldStatus[] = [
  ...baseFieldStatusList,
  {
    label: "Show Number",
    color: "green",
    Comp: (
      <div className="flex flex-row gap-2 px-1 items-center whitespace-nowrap truncate">
        <EyeIcon className="w-4 h-4 text-green-600" />
        <span className="truncate">Show Number</span>
      </div>
    ),
  },
  {
    label: "Show Upload",
    color: "green",
    Comp: (
      <div className="flex flex-row gap-2 px-1 items-center whitespace-nowrap truncate">
        <EyeIcon className="w-4 h-4 text-green-600" />
        <span className="truncate">Show Upload</span>
      </div>
    ),
  },
  {
    label: "Must",
    color: "red",
    labelTooltip: "Either the Number or Upload field must be completed",
    Comp: (
      <div className="flex flex-row gap-1 pr-1 items-center whitespace-nowrap">
        <div className="min-w-4">
          <AsteriskIcon className="w-6 h-6 text-primary" />
        </div>
        <span>Must</span>
      </div>
    ),
  },
  {
    label: "Must Number",
    color: "red",
    Comp: (
      <div className="flex flex-row gap-1 pr-1 items-center whitespace-nowrap truncate">
        <div className="min-w-4">
          <Asterisk className="w-6 h-6 text-primary" />
        </div>
        <span className="truncate">Must Number</span>
      </div>
    ),
  },
  {
    label: "Must Upload",
    color: "red",
    Comp: (
      <div className="flex flex-row gap-1 pr-1 items-center whitespace-nowrap truncate">
        <div className="min-w-4">
          <Asterisk className="w-6 h-6 text-primary" />
        </div>
        <span className="flex-1 truncate min-w-0">Must Upload</span>
      </div>
    ),
  },
];

export const getStatusIndex = (key: (typeof fieldPlusUploadStatusKeys)[number]) => {
  return fieldPlusUploadStatusKeys.indexOf(key);
};

export const isNot = (statusIndex: number, ...keys: (typeof fieldPlusUploadStatusKeys)[number][]) => {
  const status = fieldPlusUploadStatusKeys[statusIndex];
  return !keys.find((key) => key === status);
};

export const includesIndex = (statusIndex: number, ...keys: (typeof fieldPlusUploadStatusKeys)[number][]) => {
  isNot(statusIndex, ...keys);
};

export const includesStatus = (...keys: (typeof fieldPlusUploadStatusKeys)[number][]) => {
  return fieldPlusUploadStatusKeys.find((status) => keys.find((key) => key === status));
};
