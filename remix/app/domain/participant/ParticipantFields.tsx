import { Trans } from "@lingui/react/macro";
import { t } from "@lingui/core/macro";
import type { Participant } from "~/kysely/db";
import React, { ComponentProps, Fragment, ReactElement, ReactNode, Suspense, useEffect, useState } from "react";
import { bcdSizes, countries, heightMetrics, shoeSizeUnits, tshirtSizes, weightMetrics } from "~/data/countries";
import type { Selectable } from "kysely";
import { entries, fName, keys, tableIdRef } from "~/misc/helpers";
import { twMerge } from "tailwind-merge";
import { AddressInput } from "~/components/base/AddressInput";
import { experienceInYearsOptions, gearKeys, gears, nrOfDivesOptions, referralSources } from "~/domain/participant/participant-data";
import { RInput, RSelect, toInputId } from "~/components/ResourceInputs";
import { HiddenTypeInput, OperationInput } from "~/components/form/DefaultInput";
import type { FileTargetValue } from "~/domain/file/file-resource";
import { BaseLink } from "~/components/base/base";
import { format, formatISO } from "date-fns";
import isInteractive from "is-interactive";
import { useIsLoading } from "~/components/base/Button";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { UploadButton } from "~/components/form/FilesInput";
import {
  defaultCertificateNames,
  divingOrganizationszz,
  getDivingCertificateOrganization,
} from "~/domain/diving-course/diving-courses.data";
import { defaultCountryCode } from "~/misc/vars";
import { _file_download } from "~/misc/paths";
import { getLastDivedKey, lastDivedValues } from "~/domain/participant/participant-helpers";
import { I18nContext } from "@lingui/react";
import { mealPreferences, Option } from "~/domain/participant/participant-fields";
import {
  convertHeight,
  convertShoesize,
  convertWeight,
  roundToMaxDecimals,
  strToHeightUnit,
  strToShoesizeUnit,
  strToWeightUnit,
} from "~/utils/metric-helpers";
import { toUtc } from "~/misc/date-helpers";
import { defaultFieldStatusList, fieldPlusUploadStatusList, FieldStatus, getStatusIndex, isNot } from "~/domain/participant/field-helpers";
import { ChevronDown, InfoIcon } from "lucide-react";
import { FloatingSelect } from "~/components/base/Select";
import { Tooltip } from "~/components/base/tooltip";
import { getStr, useFormCtx } from "~/components/form/BaseFrom";
import { TelephoneInput } from "~/components/TelephoneInput";
import { refreshFormdata } from "~/components/form/form-hooks";
import { CountrySelect } from "~/components/CountrySelect";
import { ErrorBoundaryWithSuspense } from "~/components/ErrorBoundaryWithSuspense";
import { GenderSelect } from "./GenderSelect";

export interface OverwriteField {
  name: string;
  status: number;
}

type FileItemType = {
  id?: string;
  file_id: string;
  sort_order: number;
  target_id?: string | null;
  filename: string;
  delete?: boolean;
};

type DefaultFileUploads = {
  file_diving_certificates: FileItemType[];
  files_passport: FileItemType[];
};

export type DefaultValue =
  | (Partial<Selectable<Participant>> & {
      first_name: string;
      last_name: string;
    } & DefaultFileUploads)
  | null
  | undefined;

interface Args {
  name: string;
  disabled?: boolean;
  // readOnly?: boolean;
  defaultValue?: DefaultValue;
  // required?: boolean;
  status: number;
  edit: boolean;
  context: Context;
}

interface SelectField {
  field: keyof Participant;
  list: Option[];
  firstItem?: ReactNode;
  props?: (args: Args) => Partial<ComponentProps<"select">>;
}

interface TextareaField {
  field: keyof Participant;
  rows: number;
  props?: (args: Args) => Partial<ComponentProps<"textarea">>;
}

interface InputField {
  field: keyof Participant;
  props?: (args: Args) => Partial<ComponentProps<"input">>;
}

interface CustomField {
  field?: keyof Participant;
  Comp: (args: Args) => ReactElement;
}

export const participantFieldGroups = {
  details: {
    title: <Trans>Participant details</Trans>,
    subTitle: null,
  },
  emergency: {
    title: <Trans>Emergency contact details</Trans>,
    subTitle: (
      <p className="font-semibold text-red-500 pb-2">
        <Trans>Important! Ensure the emergency contact is not someone joining the activity.</Trans>
      </p>
    ),
  },
  dive: {
    title: <Trans>Dive specific details</Trans>,
    subTitle: null,
  },
  feedback: {
    title: <Trans>Feedback</Trans>,
    subTitle: null,
  },
  other: {
    title: <Trans>Other info (optional)</Trans>,
    subTitle: null,
  },
} satisfies Record<string, { title: ReactNode; subTitle?: ReactNode }>;

export type Field = {
  statusList?: FieldStatus[];
  label: ReactNode;
  subText?: ReactNode;
} & (SelectField | InputField | TextareaField | CustomField);

export const participantFields = {
  first_name: "details",
  last_name: "details",
  passport_number: "details",
  country_code: "details",
  address: "details",
  stay: "details",
  room: "details",
  birth_date: "details",
  gender: "details",
  phone: "details",
  diet: "details",
  food_allergies: "details",
  insurance: "details",
  emergency_contact_name: "emergency",
  emergency_contact_phone: "emergency",
  emergency_contact_relationship: "emergency",
  diving_certificate_organization: "dive",
  diving_certificate_level: "dive",
  diving_certificate_number: "dive",
  number_of_dives: "dive",
  years_of_experience: "dive",
  last_dive_within_months: "dive",
  my_gear: "dive",
  wetsuit_size: "dive",
  height_value: "dive",
  weight_value: "dive",
  shoe_size_value: "dive",
  weightbelt_value: "dive",
  referral_source: "feedback",
  allow_contact_for_experience: "feedback",
  instagram: "details",
  comment: "other",
  bcd_size: "dive",
} satisfies Record<string, keyof typeof participantFieldGroups>;

export type ParticipantFieldKey = keyof typeof participantFields;

export const lockedFields = ["first_name", "last_name"] satisfies ParticipantFieldKey[];

const DivingCertificateLevelSelect = (props: ComponentProps<"select"> & { context: Context }) => {
  const formCtx = useFormCtx();
  const defaultValue = props.defaultValue;
  const selectedOrgKey = formCtx.formdata?.get(fName("participant", "data.diving_certificate_organization")) as string | null;
  const certificateLevels = getDivingCertificateOrganization(selectedOrgKey)?.certificates || defaultCertificateNames;
  const validLevels = Object.entries(certificateLevels)
    .filter(([_, name]) => name)
    .map(([key, name]) => ({ key: key, name: name! }));
  return (
    <div key={selectedOrgKey || ""}>
      <select {...props} className={twMerge("select w-full", props.className)}>
        <option value="">
          <Trans>Select level</Trans>
        </option>
        {defaultValue && !validLevels.find((level) => level.key === defaultValue) && <option value={defaultValue}>{defaultValue}</option>}
        {validLevels.map((level) => (
          <option key={level.key} value={level.key || ""}>
            {level.name}
          </option>
        ))}
      </select>
    </div>
  );
};

const DivingCertificationOrganizationSelect = (props: ComponentProps<"select"> & { context: Context }) => {
  const defaultValue = props.defaultValue;
  const validOrgs = Object.entries(divingOrganizationszz)
    .filter(([_, org]) => org.certificates)
    .map(([orgKey, org]) => ({
      key: orgKey,
      name: org.name,
    }));
  return (
    <div>
      <select {...props} defaultValue={props.defaultValue} className={twMerge("select w-full", props.className)}>
        <option value="">
          <Trans>Select organisation</Trans>
        </option>
        {defaultValue && !validOrgs.find((org) => org.key === defaultValue) && <option value={defaultValue}>{defaultValue}</option>}
        {validOrgs.map((org) => (
          <option key={org.key} value={org.key} translate={"no"}>
            {org.name}
          </option>
        ))}
      </select>
    </div>
  );
};

const FileItem = (props: { fileTarget: FileItemType; fileTargetValue: FileTargetValue; hidden?: boolean; onToggle?: () => void }) => {
  const searchParams = useSearchParams2();
  const actionType = searchParams.state.action_type;
  // const deleteFile = useBoolean();
  const fileId = props.fileTarget.file_id;
  const fileTargetId = props.fileTarget.id;

  const filename = props.fileTarget.filename;
  const filenameSegements = filename.split("/");
  const displayFilename = filenameSegements.slice(filenameSegements.length > 1 ? 1 : 0).join("/");

  if (props.hidden)
    return (
      <Fragment>
        {fileTargetId && actionType !== "copy" && <RInput table={"file_target"} field={"id"} index={fileId} value={fileTargetId} />}
        <RInput table={"file_target"} field={"data.target_id"} index={fileId} value={tableIdRef("participant")} type={"hidden"} />
        <RInput table={"file_target"} field={"data.file_id"} index={fileId} value={fileId} type={"hidden"} />
        <RInput table={"file_target"} field={"data.sort_order"} index={fileId} value={props.fileTarget.sort_order + ""} type={"hidden"} />
        <RInput table={"file_target"} field={"data.target"} index={fileId} value={props.fileTargetValue} type={"hidden"} />
      </Fragment>
    );

  if (props.fileTarget.delete && !fileTargetId) return <Fragment />;
  return (
    <div>
      {fileTargetId && actionType !== "copy" && <RInput table={"file_target"} field={"id"} index={fileId} value={fileTargetId} />}
      {props.fileTarget.delete && <OperationInput table={"file_target"} index={fileId} value={"delete"} />}
      <RInput table={"file_target"} field={"data.target_id"} index={fileId} value={tableIdRef("participant")} type={"hidden"} />
      <RInput table={"file_target"} field={"data.file_id"} index={fileId} value={fileId} type={"hidden"} />
      <RInput table={"file_target"} field={"data.sort_order"} index={fileId} value={props.fileTarget.sort_order + ""} type={"hidden"} />
      <RInput table={"file_target"} field={"data.target"} index={fileId} value={props.fileTargetValue} type={"hidden"} />
      <BaseLink
        to={_file_download(filename)}
        target={"_blank"}
        className={twMerge("link", props.fileTarget.delete && "text-slate-500 line-through opacity-80")}
      >
        {displayFilename}
      </BaseLink>{" "}
      &nbsp;
      {props.onToggle && (
        <button
          type={"button"}
          className={twMerge(
            "hover:underline disabled:opacity-50 disabled:hover:no-underline",
            props.fileTarget.delete ? "text-slate-500" : "text-red-500",
          )}
          disabled={!isInteractive}
          onClick={props.onToggle}
        >
          {props.fileTarget.delete ? "undo" : "remove"}
        </button>
      )}
    </div>
  );
};

const NumberOrUploadComp = (props: {
  args: Args;
  field: "passport_number" | "diving_certificate_number";
  files_field: keyof DefaultFileUploads;
  file_target: FileTargetValue;
}) => {
  const form = useFormCtx();
  const args = props.args;
  const filledValue = getStr(form.formdata, fName("participant", `data.${props.field}`)) || props.args.defaultValue?.[props.field];
  const defaultFiles =
    args.defaultValue?.[props.files_field]?.map((fileTarget) =>
      args.defaultValue?.id
        ? fileTarget
        : {
            target_id: tableIdRef("participant"),
            file_id: fileTarget.file_id,
            filename: fileTarget.filename,
            sort_order: fileTarget.sort_order,
          },
    ) || [];
  const [allFiles, setAllFiles] = useState<FileItemType[]>(defaultFiles);

  const showingFiles = allFiles.filter((file) => !file.delete || !!file.id);
  const activerFiles = showingFiles.filter((file) => !file.delete);

  const status = args.status;

  if (!status && !args.edit)
    return (
      <Fragment>
        <RInput
          type={"hidden"}
          table={"participant"}
          field={`data.${props.field}`}
          disabled={args.disabled}
          value={args.defaultValue?.[props.field] ?? ""}
        />
        {defaultFiles.map((defaultFile) => (
          <FileItem key={defaultFile.id} fileTarget={defaultFile} fileTargetValue={props.file_target} hidden />
        ))}
      </Fragment>
    );

  const showNumber = isNot(status, "hide", "show_upload", "require_upload");
  const showUpload = isNot(status, "hide", "show_number", "require_number");
  return (
    <div className="space-y-3">
      {(showNumber || args.edit) && (
        <RInput
          disabled={args.disabled}
          required={
            status === getStatusIndex("require_number") ? true : status === getStatusIndex("require_or") ? activerFiles.length === 0 : false
          }
          table={"participant"}
          field={`data.${props.field}`}
          className={twMerge("input", !showNumber && "disabled:opacity-30")}
          placeholder={t(args.context.i18n.i18n)`Enter number`}
          defaultValue={args.defaultValue?.[props.field] ?? ""}
        />
      )}
      {(showUpload || args.edit) && (
        <div className={twMerge(!showUpload && "opacity-30")}>
          <div className="flex flex-row items-center justify-between gap-3">
            <UploadButton
              description={`${props.field} upload for ${args.defaultValue?.first_name} ${args.defaultValue?.last_name}`}
              disabled={args.disabled}
              required={
                activerFiles.length === 0 &&
                (status === getStatusIndex("require_upload")
                  ? true
                  : status === getStatusIndex("require_or")
                    ? !filledValue && activerFiles.length === 0
                    : false)
              }
              className={"aria-busy:loading-dots aria-busy:animate-pulse btn btn-secondary"}
              onUploaded={(files) => {
                setAllFiles([
                  ...allFiles,
                  ...files.map((file) => ({
                    file_id: file.id,
                    filename: file.filename,
                    sort_order: 0,
                  })),
                ]);
              }}
            >
              Upload Document
            </UploadButton>
          </div>
          {showingFiles.map((file, index) => (
            <FileItem
              key={index}
              onToggle={() =>
                setAllFiles(
                  allFiles.map((item) =>
                    item === file
                      ? {
                          ...item,
                          delete: !item.delete,
                        }
                      : item,
                  ),
                )
              }
              fileTarget={file}
              fileTargetValue={props.file_target}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export const participantFieldImpls: Record<ParticipantFieldKey, Field> = {
  first_name: {
    label: <Trans>First name</Trans>,
    Comp: (args) => (
      <RInput
        className="input"
        table={"person"}
        field={"data.first_name"}
        required={args.status > 1}
        disabled={args.disabled}
        // readOnly={args.readOnly}
        defaultValue={args.defaultValue?.first_name || ""}
      />
    ),
  },
  last_name: {
    label: <Trans>Last name</Trans>,
    Comp: (args) => (
      <RInput
        className="input"
        table={"person"}
        field={"data.last_name"}
        required={args.status > 1}
        disabled={args.disabled}
        // readOnly={args.readOnly}
        defaultValue={args.defaultValue?.last_name || ""}
      />
    ),
  },
  passport_number: {
    label: <Trans>Passport</Trans>,
    subText: <Trans>Or a valid local ID card</Trans>,
    statusList: fieldPlusUploadStatusList,
    Comp: (args) => (
      <NumberOrUploadComp args={args} field={"passport_number"} files_field={"files_passport"} file_target={"participant_passport"} />
    ),
  },
  country_code: {
    label: <Trans>Country</Trans>,
    field: "country_code",
    Comp: (args) => {
      const defaultValue = args.defaultValue?.country_code;

      if (!args.status && !args.edit)
        return <RInput table={"participant"} field={"data.country_code"} value={defaultValue || ""} type={"hidden"} />;

      return (
        <CountrySelect
          className={twMerge("select min-h-12 w-full", !args.status && "disabled:opacity-30")}
          name={fName("participant", "data.country_code")}
          defaultValue={defaultValue || ""}
          required={args.status > 1}
          disabled={args.disabled}
        />
      );
    },
  },
  address: {
    label: <Trans>Home/residential address</Trans>,
    field: "address",
    Comp: (args) =>
      args.status || args.edit ? (
        <AddressInput
          id={args.name}
          name={fName("participant", "data.address")}
          disabled={args.disabled}
          required={args.status > 1}
          defaultValue={args.defaultValue?.address || ""}
          className={twMerge("input", !args.status && "disabled:opacity-30")}
          autocompleteOptions={{ types: ["geocode", "establishment"] }}
        />
      ) : (
        <RInput
          type={"hidden"}
          table={"participant"}
          field={"data.address"}
          disabled={args.disabled}
          value={args.defaultValue?.address || ""}
        />
      ),
  },
  stay: {
    field: "stay",
    label: <Trans>Stay</Trans>,
    Comp: (args) =>
      args.status || args.edit ? (
        <AddressInput
          id={args.name}
          name={fName("participant", "data.stay")}
          required={args.status > 1}
          disabled={args.disabled}
          defaultValue={args.defaultValue?.stay || ""}
          placeholder={t(args.context.i18n.i18n)`e.g. Hotel name`}
          autocompleteOptions={{
            types: ["geocode", "establishment"],
            componentRestrictions: { country: args.context?.establishment?.country_code || defaultCountryCode },
          }}
          className={twMerge("input", !args.status && "disabled:opacity-30")}
        />
      ) : (
        <RInput type={"hidden"} table={"participant"} field={"data.stay"} disabled={args.disabled} value={args.defaultValue?.stay || ""} />
      ),
  },
  room: {
    field: "room",
    label: <Trans>Room number</Trans>,
    props: (args) => ({ placeholder: t(args.context.i18n.i18n)`Room number of your stay` }),
  },
  // { name: "birth_date", label:" <Trans></Date of birth", props: { type: "date" } },
  birth_date: {
    field: "birth_date",
    label: <Trans>Date of birth</Trans>,
    Comp: (args) => {
      const now = toUtc();
      const defaultValue = args.defaultValue?.birth_date;

      if (!args.status && !args.edit)
        return (
          <RInput type={"hidden"} disabled={args.disabled} table={"participant"} field={"data.birth_date"} value={defaultValue || ""} />
        );

      const finalDefaultValue = defaultValue ? formatISO(toUtc(defaultValue), { representation: "date" }) : "";
      return (
        <RInput
          className={twMerge("input", !args.status && "disabled:opacity-30")}
          type={"date"}
          required={args.status > 1}
          disabled={args.disabled}
          defaultValue={finalDefaultValue}
          max={format(now, "yyyy-MM-dd")}
          table={"participant"}
          field={"data.birth_date"}
        />
      );
    },
  },
  gender: {
    field: "gender",
    label: <Trans>Gender</Trans>,
    Comp: (args) => {
      if (!args.status && !args.edit) {
        return (
          <RInput
            type={"hidden"}
            disabled={args.disabled}
            table={"participant"}
            field={"data.gender"}
            value={args.defaultValue?.gender || ""}
          />
        );
      }
      return (
        <GenderSelect
          disabled={args.disabled}
          required={args.status > 1}
          className={twMerge("select w-full", !args.status && "disabled:opacity-30")}
          name={fName("participant", "data.gender")}
          defaultValue={args.defaultValue?.gender || ""}
        />
      );
    },
  },
  phone: {
    field: "phone",
    label: <Trans>Phone number</Trans>,
    subText: <Trans>WhatsApp preferred</Trans>,
    Comp: (args) => {
      if (!args.status && !args.edit) {
        return (
          <RInput
            type={"hidden"}
            disabled={args.disabled}
            table={"participant"}
            field={"data.phone"}
            value={args.defaultValue?.phone || ""}
          />
        );
      }
      return (
        <TelephoneInput
          disabled={args.disabled}
          required={args.status > 1}
          name={fName("participant", "data.phone")}
          defaultValue={args.defaultValue?.phone}
        />
      );
    },
  },
  diet: {
    field: "diet",
    label: <Trans>Meal preferences</Trans>,
    list: mealPreferences,
  },
  food_allergies: {
    field: "food_allergies",
    label: <Trans>Food allergies</Trans>,
    props: (args) => ({ placeholder: t(args.context.i18n.i18n)`e.g. nut allergy` }),
  },
  insurance: {
    field: "insurance",
    label: <Trans>Insurance</Trans>,
    subText: <Trans>Do you have an insurance that covers underwater activities? please fill your details below</Trans>,
    props: (args) => ({ placeholder: t(args.context.i18n.i18n)`Company and policy number` }),
  },
  emergency_contact_name: {
    field: "emergency_contact_name",
    label: <Trans>Emergency contact name</Trans>,
  },
  emergency_contact_phone: {
    field: "emergency_contact_phone",
    label: <Trans>Emergency contact phone number</Trans>,
    Comp: (args) => {
      if (!args.status && !args.edit) {
        return (
          <RInput
            type={"hidden"}
            disabled={args.disabled}
            table={"participant"}
            field={"data.emergency_contact_phone"}
            value={args.defaultValue?.emergency_contact_phone || ""}
          />
        );
      }
      return (
        <TelephoneInput
          disabled={args.disabled}
          required={args.status > 1}
          name={fName("participant", "data.emergency_contact_phone")}
          defaultValue={args.defaultValue?.emergency_contact_phone}
        />
      );
    },
  },
  emergency_contact_relationship: {
    field: "emergency_contact_relationship",
    label: <Trans>Relationship</Trans>,
    props: (args) => ({ placeholder: t(args.context.i18n.i18n)`e.g. friend, father or mother` }),
  },
  diving_certificate_organization: {
    label: <Trans>Certification organisation</Trans>,
    field: "diving_certificate_organization",
    Comp: (args) =>
      !args.status && !args.edit ? (
        <RInput
          type={"hidden"}
          table={"participant"}
          field={"data.diving_certificate_organization"}
          disabled={args.disabled}
          value={args.defaultValue?.diving_certificate_organization || ""}
        />
      ) : (
        <DivingCertificationOrganizationSelect
          disabled={args.disabled}
          className={twMerge(!args.status && "disabled:opacity-30")}
          name={fName("participant", "data.diving_certificate_organization")}
          required={args.status > 1}
          defaultValue={args.defaultValue?.diving_certificate_organization || ""}
          context={args.context}
        />
      ),
  },
  diving_certificate_level: {
    label: <Trans>Certificate level</Trans>,
    field: "diving_certificate_level",
    Comp: (args) =>
      args.status || args.edit ? (
        <DivingCertificateLevelSelect
          disabled={args.disabled}
          className={twMerge(!args.status && "disabled:opacity-30")}
          name={fName("participant", "data.diving_certificate_level")}
          required={args.status > 1}
          defaultValue={args.defaultValue?.diving_certificate_level || ""}
          context={args.context}
        />
      ) : (
        <RInput
          type={"hidden"}
          table={"participant"}
          field={"data.diving_certificate_level"}
          disabled={args.disabled}
          value={args.defaultValue?.diving_certificate_level || ""}
        />
      ),
  },
  diving_certificate_number: {
    label: <Trans>Certificate details</Trans>,
    statusList: fieldPlusUploadStatusList,
    Comp: (args) => (
      <NumberOrUploadComp
        args={args}
        field={"diving_certificate_number"}
        files_field={"file_diving_certificates"}
        file_target={"participant_diving_certificate"}
      />
    ),
  },
  number_of_dives: {
    field: "number_of_dives",
    list: nrOfDivesOptions,
    label: <Trans>Number of dives</Trans>,
  },
  years_of_experience: {
    field: "years_of_experience",
    list: experienceInYearsOptions,
    label: <Trans>Years of experience</Trans>,
  },
  last_dive_within_months: {
    label: <Trans>Last dive</Trans>,
    field: "last_dive_within_months",
    Comp: (args) => {
      const defaultValue = getLastDivedKey(args.defaultValue?.last_dive_within_months);
      if (!args.status && !args.edit) {
        return (
          <input
            type={"hidden"}
            disabled={args.disabled}
            name={fName("participant", "data.last_dive_within_months")}
            value={defaultValue || ""}
          />
        );
      }

      return (
        <RSelect
          required={args.status > 1}
          disabled={args.disabled}
          className={twMerge("select w-full", !args.status && "disabled:opacity-30")}
          table={"participant"}
          field={"data.last_dive_within_months"}
          defaultValue={defaultValue || ""}
        >
          <option value="">
            <Trans>select</Trans>
          </option>
          {Object.entries(lastDivedValues).map(([value, label]) => (
            <option key={value} value={value}>
              {label.long}
            </option>
          ))}
        </RSelect>
      );
    },
  },
  my_gear: {
    label: <Trans>Own gear</Trans>,
    subText: <Trans>Please select in case you bring your own gear.</Trans>,
    field: "my_gear",
    Comp: (args) => {
      const finalGears: string[] = [...gearKeys];
      args.defaultValue?.my_gear?.forEach((myGear) => {
        if (!gearKeys.find((gear) => gear === myGear)) {
          finalGears.push(myGear);
        }
      });
      if (!args.status && !args.edit)
        return (
          <Fragment>
            {args.defaultValue?.my_gear?.map((gear, index) => (
              <input
                key={index}
                disabled={args.disabled}
                type={"hidden"}
                name={fName("participant", "data.my_gear", 0, index)}
                value={gear}
              />
            ))}
          </Fragment>
        );

      return (
        <div className={twMerge("grid grid-flow-col gap-2 text-center", !args.status && "opacity-30")}>
          {finalGears.map((gearKey, index) => {
            const gearLabel = gears[gearKey as keyof typeof gears];
            const defaultChecked = args.defaultValue?.my_gear?.includes(gearKey);
            return (
              <div key={gearKey} className="min-w-0 overflow-hidden">
                <label>
                  <span className="capitalize">{gearLabel || gearKey}</span>
                  <br />
                  <input
                    disabled={args.disabled}
                    defaultChecked={defaultChecked}
                    value={gearKey}
                    type={"checkbox"}
                    className="checkbox"
                    name={fName("participant", "data.my_gear", 0, index)}
                  />
                </label>
              </div>
            );
          })}
        </div>
      );
    },
  },
  wetsuit_size: {
    field: "wetsuit_size",
    label: <Trans>Wetsuit size</Trans>,
    firstItem: <Trans>Select size</Trans>,
    subText: <Trans>Or t-shirt size if you don't know your wetsuit size.</Trans>,
    list: tshirtSizes.map((value) => [value, value]),
  },
  height_value: {
    label: <Trans>Height</Trans>,
    field: "height_value",
    Comp: (args) => {
      const form = useFormCtx();
      const [isUnitFixed, setIsUnitFixed] = useState(!!args.defaultValue);

      const defaultValue = args.defaultValue?.height_value;
      const unitSelectInputId = toInputId(fName("participant", "data.height_unit"));
      const selectedCountry = countries.find(
        (country) => country.country_code === getStr(form.formdata, fName("participant", "data.country_code")),
      );

      useEffect(() => {
        const heightUnit = selectedCountry?.height_unit;
        if (!heightUnit || isUnitFixed) return;
        const formElement = document.getElementById(unitSelectInputId);
        if (formElement instanceof HTMLSelectElement) {
          formElement.value = heightUnit;
          refreshFormdata();
        }
      }, [selectedCountry]);

      if (!args.status && !args.edit)
        return (
          <Fragment>
            <input
              type={"hidden"}
              disabled={args.disabled}
              name={fName("participant", "data.height_value")}
              defaultValue={args.defaultValue?.height_value || ""}
            />
            <input
              type={"hidden"}
              disabled={args.disabled}
              name={fName("participant", "data.height_unit")}
              defaultValue={args.defaultValue?.height_unit || ""}
            />
          </Fragment>
        );

      const defaultUnit =
        args.defaultValue?.height_unit || args.context.establishment?.default_height_unit || ("cm" satisfies keyof typeof heightMetrics);

      const heightValueRaw = getStr(form.formdata, fName("participant", "data.height_value")) as any;

      const heightUnitRaw = strToHeightUnit(getStr(form.formdata, fName("participant", "data.height_unit")) || defaultUnit);
      return (
        <div className={twMerge("flex flex-row gap-3", !args.status && "opacity-30")}>
          <div className="flex-1">
            <input
              type={"number"}
              step="0.01"
              id={args.name}
              disabled={args.disabled}
              required={args.status > 1}
              className="input"
              name={fName("participant", "data.height_value")}
              onChange={() => {
                setIsUnitFixed(true);
              }}
              defaultValue={defaultValue || ""}
            />
          </div>
          <div>
            <select
              className="select w-24"
              name={fName("participant", "data.height_unit")}
              id={unitSelectInputId}
              disabled={args.disabled}
              required={args.status > 1}
              defaultValue={defaultUnit}
              onChange={(e) => {
                const parsedNumber = Number.parseFloat(heightValueRaw || 0);
                const valueInput = document.getElementById(args.name);
                const toUnit = strToHeightUnit(e.target.value);
                setIsUnitFixed(true);
                if (parsedNumber && valueInput instanceof HTMLInputElement) {
                  const convertedHeightValue = roundToMaxDecimals(convertHeight(parsedNumber, heightUnitRaw, toUnit), 2);
                  valueInput.value = convertedHeightValue + "";
                  valueInput.focus();
                }
              }}
            >
              {keys(heightMetrics).map((heightUnit) => {
                const parsedNumber = Number.parseFloat(heightValueRaw || 0);
                const convertedHeight = convertHeight(parsedNumber, heightUnitRaw, heightUnit);
                const convertedHeightTxt =
                  !!parsedNumber && heightUnit !== heightUnitRaw && roundToMaxDecimals(convertedHeight, convertedHeight < 10 ? 1 : 0);

                return (
                  <option key={heightUnit} value={heightUnit}>
                    {heightUnit} {convertedHeightTxt}
                  </option>
                );
              })}
            </select>
          </div>
        </div>
      );
    },
  },
  weight_value: {
    label: <Trans>Weight</Trans>,
    field: "weight_value",
    Comp: (args) => {
      const form = useFormCtx();
      const [isUnitFixed, setIsUnitFixed] = useState(!!args.defaultValue);

      const defaultValue = args.defaultValue?.weight_value;
      const unitSelectInputId = toInputId(fName("participant", "data.weight_unit"));
      const selectedCountry = countries.find(
        (country) => country.country_code === getStr(form.formdata, fName("participant", "data.country_code")),
      );

      useEffect(() => {
        const weightUnit = selectedCountry?.weight_unit;
        if (!weightUnit || isUnitFixed) return;
        const formElement = document.getElementById(unitSelectInputId);
        if (formElement instanceof HTMLSelectElement) {
          formElement.value = weightUnit;
          refreshFormdata();
        }
      }, [selectedCountry]);

      if (!args.status && !args.edit)
        return (
          <Fragment>
            <input
              type={"hidden"}
              disabled={args.disabled}
              name={fName("participant", "data.weight_value")}
              defaultValue={args.defaultValue?.weight_value || ""}
            />
            <input
              type={"hidden"}
              disabled={args.disabled}
              name={fName("participant", "data.weight_unit")}
              defaultValue={args.defaultValue?.weight_unit || ""}
            />
          </Fragment>
        );

      const defaultUnit =
        args.defaultValue?.weight_unit || args.context.establishment?.default_weight_unit || ("kg" satisfies keyof typeof weightMetrics);

      const weightValueRaw = getStr(form.formdata, fName("participant", "data.weight_value")) as any;
      const weightUnitRaw = strToWeightUnit(getStr(form.formdata, fName("participant", "data.weight_unit")) || defaultUnit);

      return (
        <div className={twMerge("flex flex-row gap-3", !args.status && "opacity-30")}>
          <div className="flex-1">
            <input
              type={"number"}
              step="0.01"
              id={args.name}
              disabled={args.disabled}
              required={args.status > 1}
              className="input"
              name={fName("participant", "data.weight_value")}
              onChange={() => {
                setIsUnitFixed(true);
              }}
              defaultValue={defaultValue || ""}
            />
          </div>
          <div>
            <select
              className="select w-24"
              name={fName("participant", "data.weight_unit")}
              id={unitSelectInputId}
              disabled={args.disabled}
              required={args.status > 1}
              defaultValue={defaultUnit}
              onChange={(e) => {
                const parsedNumber = Number.parseFloat(weightValueRaw || 0);
                const valueInput = document.getElementById(args.name);
                const toUnit = strToWeightUnit(e.target.value);
                setIsUnitFixed(true);
                if (parsedNumber && valueInput instanceof HTMLInputElement) {
                  const convertedWeightValue = roundToMaxDecimals(convertWeight(parsedNumber, weightUnitRaw, toUnit), 2);
                  valueInput.value = convertedWeightValue + "";
                  valueInput.focus();
                }
              }}
            >
              {keys(weightMetrics).map((weightUnit) => {
                const parsedNumber = Number.parseFloat(weightValueRaw || 0);
                const convertedWeight = convertWeight(parsedNumber, weightUnitRaw, weightUnit);
                const convertedWeightTxt =
                  !!parsedNumber && weightUnit !== weightUnitRaw && roundToMaxDecimals(convertedWeight, convertedWeight < 10 ? 1 : 0);

                return (
                  <option key={weightUnit} value={weightUnit}>
                    {weightUnit} {convertedWeightTxt}
                  </option>
                );
              })}
            </select>
          </div>
        </div>
      );
    },
  },
  shoe_size_value: {
    label: <Trans>Shoe size</Trans>,
    field: "shoe_size_value",
    Comp: (args) => {
      const form = useFormCtx();
      const [isUnitFixed, setIsUnitFixed] = useState(!!args.defaultValue);

      const defaultValue = args.defaultValue?.shoe_size_value;
      const unitSelectInputId = toInputId(fName("participant", "data.shoe_size_unit"));
      const selectedCountry = countries.find(
        (country) => country.country_code === getStr(form.formdata, fName("participant", "data.country_code")),
      );

      useEffect(() => {
        const shoeUnit = selectedCountry?.shoe_size_unit;
        if (!shoeUnit || isUnitFixed) return;
        const formElement = document.getElementById(unitSelectInputId);
        if (formElement instanceof HTMLSelectElement) {
          formElement.value = shoeUnit;
          refreshFormdata();
        }
      }, [selectedCountry]);

      if (!args.status && !args.edit)
        return (
          <Fragment>
            <input
              disabled={args.disabled}
              type={"hidden"}
              name={fName("participant", "data.shoe_size_value")}
              defaultValue={args.defaultValue?.shoe_size_value || ""}
            />
            <input
              type={"hidden"}
              disabled={args.disabled}
              name={fName("participant", "data.shoe_size_unit")}
              defaultValue={args.defaultValue?.shoe_size_unit || ""}
            />
          </Fragment>
        );

      const defaultUnit =
        args.defaultValue?.shoe_size_unit ||
        args.context.establishment?.default_shoe_size_unit ||
        ("EU" satisfies keyof typeof shoeSizeUnits);

      const shoeSizeValueRaw = getStr(form.formdata, fName("participant", "data.shoe_size_value")) as any;
      const shoeSizeUnitRaw = strToShoesizeUnit(getStr(form.formdata, fName("participant", "data.shoe_size_unit")) || defaultUnit);

      return (
        <div className={twMerge("flex flex-row gap-3", !args.status && "opacity-30")}>
          <div className="flex-1">
            <input
              type={"number"}
              step="0.5"
              id={args.name}
              disabled={args.disabled}
              required={args.status > 1}
              className="input"
              name={fName("participant", "data.shoe_size_value")}
              onChange={() => {
                setIsUnitFixed(true);
              }}
              defaultValue={defaultValue || ""}
            />
          </div>
          <div>
            <select
              className="select w-24"
              name={fName("participant", "data.shoe_size_unit")}
              id={unitSelectInputId}
              disabled={args.disabled}
              required={args.status > 1}
              defaultValue={defaultUnit}
              onChange={(e) => {
                const parsedNumber = Number.parseFloat(shoeSizeValueRaw || 0);
                const valueInput = document.getElementById(args.name);
                const toUnit = strToShoesizeUnit(e.target.value);
                setIsUnitFixed(true);
                if (parsedNumber && valueInput instanceof HTMLInputElement) {
                  const convertedShoeSizeValue = convertShoesize(parsedNumber, shoeSizeUnitRaw, toUnit);
                  valueInput.value = convertedShoeSizeValue + "";
                  valueInput.focus();
                }
              }}
            >
              {keys(shoeSizeUnits).map((unit) => {
                const parsedNumber = Number.parseFloat(shoeSizeValueRaw || 0);
                const convertedShoeSize = convertShoesize(parsedNumber, shoeSizeUnitRaw, unit);
                const convertedShoeSizeTxt = !!parsedNumber && unit !== shoeSizeUnitRaw && convertedShoeSize;

                return (
                  <option key={unit} value={unit}>
                    {unit} {convertedShoeSizeTxt}
                  </option>
                );
              })}
            </select>
          </div>
        </div>
      );
    },
  },
  weightbelt_value: {
    label: <Trans>Weightbelt</Trans>,
    field: "weightbelt_value",
    Comp: (args) => {
      const form = useFormCtx();
      const [isUnitFixed, setIsUnitFixed] = useState(!!args.defaultValue);

      const defaultValue = args.defaultValue?.weightbelt_value;
      const unitSelectInputId = toInputId(fName("participant", "data.weightbelt_unit"));
      const selectedCountry = countries.find(
        (country) => country.country_code === getStr(form.formdata, fName("participant", "data.country_code")),
      );

      useEffect(() => {
        const weightUnit = selectedCountry?.weight_unit;
        if (!weightUnit || isUnitFixed) return;
        const formElement = document.getElementById(unitSelectInputId);
        if (formElement instanceof HTMLSelectElement) {
          formElement.value = weightUnit;
          refreshFormdata();
        }
      }, [selectedCountry]);

      if (!args.status && !args.edit) {
        return (
          <Fragment>
            <input
              disabled={args.disabled}
              type={"hidden"}
              name={fName("participant", "data.weightbelt_value")}
              defaultValue={args.defaultValue?.weightbelt_value || ""}
            />
            <input
              disabled={args.disabled}
              type={"hidden"}
              name={fName("participant", "data.weightbelt_unit")}
              defaultValue={args.defaultValue?.weightbelt_unit || ""}
            />
          </Fragment>
        );
      }

      const defaultUnit =
        args.defaultValue?.weightbelt_unit ||
        args.context.establishment?.default_weight_unit ||
        ("kg" satisfies keyof typeof weightMetrics);

      const weightBeltValueRaw = getStr(form.formdata, fName("participant", "data.weightbelt_value")) as any;
      const weightBeltUnitRaw = strToWeightUnit(getStr(form.formdata, fName("participant", "data.weightbelt_unit")) || defaultUnit);

      return (
        <div className={twMerge("flex flex-row gap-3", !args.status && "opacity-30")}>
          <div className="flex-1">
            <input
              type={"number"}
              step="0.01"
              id={args.name}
              disabled={args.disabled}
              required={args.status > 1}
              className="input"
              name={fName("participant", "data.weightbelt_value")}
              onChange={() => {
                setIsUnitFixed(true);
              }}
              defaultValue={defaultValue || ""}
            />
          </div>
          <div>
            <select
              className="select w-24"
              name={fName("participant", "data.weightbelt_unit")}
              id={unitSelectInputId}
              disabled={args.disabled}
              required={args.status > 1}
              defaultValue={defaultUnit}
              onChange={(e) => {
                const parsedNumber = Number.parseFloat(weightBeltValueRaw || 0);
                const valueInput = document.getElementById(args.name);
                const toUnit = strToWeightUnit(e.target.value);
                setIsUnitFixed(true);
                if (parsedNumber && valueInput instanceof HTMLInputElement) {
                  const convertedWeightValue = roundToMaxDecimals(convertWeight(parsedNumber, weightBeltUnitRaw, toUnit), 2);
                  valueInput.value = convertedWeightValue + "";
                  valueInput.focus();
                }
              }}
            >
              {keys(weightMetrics).map((weightUnit) => {
                const parsedNumber = Number.parseFloat(weightBeltValueRaw || 0);
                const convertedWeight = convertWeight(parsedNumber, weightBeltUnitRaw, weightUnit);
                const convertedWeightTxt =
                  !!parsedNumber && weightUnit !== weightBeltUnitRaw && roundToMaxDecimals(convertedWeight, convertedWeight < 10 ? 1 : 0);

                return (
                  <option key={weightUnit} value={weightUnit}>
                    {weightUnit} {convertedWeightTxt}
                  </option>
                );
              })}
            </select>
          </div>
        </div>
      );
    },
  },
  bcd_size: {
    field: "bcd_size",
    label: <Trans>BCD Size</Trans>,
    list: bcdSizes.map((value) => [value, value]),
  },
  referral_source: {
    field: "referral_source",
    label: <Trans>May we ask how you heard about us?</Trans>,
    list: entries(referralSources).map(([value, label]) => [value, label]),
  },
  allow_contact_for_experience: {
    label: <Trans>May we contact you to ask about your experience?</Trans>,
    field: "allow_contact_for_experience",
    Comp: (args) => {
      const defaultValue = args.defaultValue?.allow_contact_for_experience;
      if (!args.status && !args.edit) {
        return (
          <Fragment>
            <RInput
              disabled={args.disabled}
              table={"participant"}
              field={"data.allow_contact_for_experience"}
              type={"hidden"}
              hiddenType={"__boolean__"}
              defaultValue={defaultValue ? defaultValue + "" : ""}
            />
          </Fragment>
        );
      }
      return (
        <div className={twMerge("w-full flex flex-wrap gap-3 text-center", !args.status && "opacity-30")}>
          <HiddenTypeInput name={fName("participant", "data.allow_contact_for_experience")} value={"__boolean__"} />
          <label>
            <span>
              <Trans>Yes</Trans>
            </span>
            &nbsp;
            <input
              defaultChecked={defaultValue === true}
              value="yes"
              disabled={args.disabled}
              className="radio"
              type={"radio"}
              name={fName("participant", "data.allow_contact_for_experience")}
            />
          </label>
          <label>
            <span>
              <Trans>No</Trans>
            </span>
            &nbsp;
            <input
              defaultChecked={defaultValue === false}
              value="no"
              disabled={args.disabled}
              type={"radio"}
              className="radio"
              name={fName("participant", "data.allow_contact_for_experience")}
            />
          </label>
        </div>
      );
    },
  },
  instagram: {
    field: "instagram",
    label: <Trans>Your Instagram Handle</Trans>,
  },
  comment: {
    field: "comment",
    label: <Trans>Please fill any additional important info or questions below</Trans>,
    rows: 3,
  },
};

export const ParticipantInput = (props: {
  disabled?: boolean;
  readOnly?: boolean;
  status: number;
  field: Field;
  edit: boolean;
  name: ParticipantFieldKey;
  context: Context;
  defaultValue?: DefaultValue;
}) => {
  const args: Args = {
    name: props.name,
    disabled: props.disabled,
    // readOnly: props.readOnly,
    defaultValue: props.defaultValue,
    edit: props.edit,
    status: props.status,
    context: props.context,
  };

  if ("Comp" in props.field) {
    return props.field.Comp(args);
  }

  const inputName = fName("participant", `data.${props.field.field}`);
  const defaultValue = props.defaultValue?.[props.field.field];
  const defaultValueAsString = defaultValue ? defaultValue + "" : "";

  if (props.status < 1 && !props.edit)
    return <input type={"hidden"} disabled={args.disabled} value={defaultValueAsString} name={inputName} />;

  const required = props.status > 1;

  if ("list" in props.field)
    return (
      <select
        name={inputName}
        id={props.field.field}
        defaultValue={defaultValueAsString}
        className={twMerge("select w-full", props.edit && "disabled:opacity-100", !props.status && "disabled:opacity-30")}
        required={required}
        disabled={props.disabled}
        {...props.field.props?.(args)}
      >
        <option value="">{props.field.firstItem || <Trans>Select</Trans>}</option>
        {defaultValueAsString && !props.field.list.map(([value]) => value).includes(defaultValueAsString) && (
          <option value={defaultValueAsString}>{defaultValueAsString}</option>
        )}
        {props.field.list.map(([value, label]) => (
          <option key={value} value={value}>
            {label}
          </option>
        ))}
      </select>
    );
  if ("rows" in props.field)
    return (
      <textarea
        readOnly={props.readOnly}
        id={props.name}
        name={inputName}
        disabled={props.disabled}
        defaultValue={defaultValueAsString}
        className={twMerge("input", props.edit && "disabled:opacity-100", !props.status && "disabled:opacity-30")}
        required={required}
        rows={props.field.rows}
        {...props.field.props?.(args)}
      />
    );
  return (
    <input
      className={twMerge("input", props.edit && "disabled:opacity-100", !props.status && "disabled:opacity-30")}
      id={props.name}
      readOnly={props.readOnly}
      disabled={props.disabled}
      name={inputName}
      defaultValue={defaultValueAsString}
      required={required}
      type="text"
      {...props.field.props?.(args)}
    />
  );
};

interface Context {
  establishment?: {
    country_code?: string | null;
    default_weight_unit?: string | null;
    default_height_unit?: string | null;
    default_shoe_size_unit?: string | null;
  } | null;
  i18n: I18nContext;
  // diving_certificate_organizations: { id: string; name: string }[];
}

export const ParticipantFields = (props: {
  change?: boolean;
  readOnly?: boolean;
  fields: OverwriteField[];
  context: Context;
  defaultValue?: DefaultValue | null | undefined;
  onFieldsChange?: (fields: OverwriteField[]) => void;
}) => {
  const isloading = useIsLoading();
  let fieldIndex = 0;

  const fieldContainers = keys(participantFields).map((fieldName) => {
    const field = participantFieldImpls[fieldName];
    let status = 0;
    const locked = lockedFields.includes(fieldName);
    props.fields
      .filter((f) => f.name === fieldName)
      .forEach((f) => {
        if (status < f.status) {
          status = f.status;
        }
      });
    if (locked) {
      if (status < 2) {
        status = 2;
      }
    }
    return {
      field: field,
      name: fieldName,
      status: status,
      locked: locked,
    };
  });
  return (
    <Fragment>
      <div>
        {!props.change &&
          fieldContainers
            .filter((item) => item.status < 1)
            .map((item, index) => (
              <ParticipantInput
                key={index}
                name={item.name}
                field={item.field}
                edit={false}
                status={0}
                defaultValue={props.defaultValue}
                context={props.context}
              />
            ))}
      </div>
      <div className="space-y-6">
        {Object.entries(participantFieldGroups).map(([groupKey, group], groupIndex) => {
          const allGroupFields = fieldContainers.filter((gField) => participantFields[gField.name] === groupKey);
          const filteredFields = allGroupFields.filter((field) => props.change || !!field.status);

          if (filteredFields.length === 0) return <Fragment key={groupIndex} />;

          return (
            <section key={groupIndex} className="space-y-2">
              <div className="app-container">
                <h3 className="text-xl font-bold text-slate-600">{group.title}</h3>
              </div>
              {!!group.subTitle && <div className="app-container">{group.subTitle}</div>}
              <div className="bg-secondary-50">
                <div className="app-container">
                  <div className="grid grid-cols-1 gap-5 py-5 md:grid-cols-2">
                    {filteredFields.map((fieldContainer, index) => {
                      fieldIndex++;
                      const siblingIndex = index + (index % 2 ? -1 : 1);
                      const siblingSubtext = filteredFields[siblingIndex]?.field.subText;
                      const statusList = fieldContainer.field.statusList || defaultFieldStatusList;
                      const currentStatus = statusList[fieldContainer.status];
                      const statusTooltip = currentStatus?.labelTooltip;
                      const lastShowIndex = statusList.length > 3 ? 3 : 1;
                      const subText = fieldContainer.field.subText;
                      const finalSubtext = subText || siblingSubtext;
                      const fillWithSubtextPlaceholder = !!siblingSubtext && !subText;
                      return (
                        <ErrorBoundaryWithSuspense
                          key={fieldContainer.name + index}
                          errorFallback={<div>Could not render {fieldContainer.name}</div>}
                          fallback={<div>Could not render {fieldContainer.name}</div>}
                        >
                          <div>
                            <div>
                              <div className="inline-flex gap-2 items-center">
                                <label
                                  htmlFor={fieldContainer.name}
                                  className={twMerge("font-semibold", fieldContainer.status > lastShowIndex && "required")}
                                >
                                  {fieldContainer.field.label}
                                </label>
                                {statusTooltip && props.change && (
                                  <Tooltip description={statusTooltip}>
                                    <InfoIcon className="w-4 h-4" />
                                  </Tooltip>
                                )}
                              </div>
                              {finalSubtext && (
                                <p
                                  className={twMerge(
                                    "text-slate-400",
                                    fillWithSubtextPlaceholder && "max-md:hidden select-none opacity-0 invisible",
                                  )}
                                  aria-hidden={fillWithSubtextPlaceholder}
                                >
                                  {finalSubtext}
                                </p>
                              )}
                            </div>
                            <div className="flex flex-row items-start gap-2">
                              <div className="flex-1">
                                <div className="pt-1">
                                  <ParticipantInput
                                    readOnly={props.readOnly}
                                    disabled={props.change || isloading}
                                    edit={!!props.change}
                                    field={fieldContainer.field}
                                    name={fieldContainer.name}
                                    // required={isRequired}
                                    status={fieldContainer.status}
                                    defaultValue={props.defaultValue}
                                    context={props.context}
                                  />
                                </div>
                              </div>
                              {props.change && (
                                <div className={"min-w-[160px]"}>
                                  <Fragment>
                                    <RInput
                                      disabled={fieldContainer.locked}
                                      table={"field"}
                                      field={"data.name"}
                                      index={fieldIndex}
                                      value={fieldContainer.name}
                                      type={"hidden"}
                                    />
                                    <RInput
                                      disabled={fieldContainer.locked}
                                      table={"field"}
                                      field={"data.form_id"}
                                      index={fieldIndex}
                                      value={tableIdRef("form")}
                                      type={"hidden"}
                                    />
                                  </Fragment>
                                  <div>
                                    {/*<RLabel table={"field"} field={"data.status"} index={fieldIndex} className={"opacity-0"}>*/}
                                    {/*  Status*/}
                                    {/*</RLabel>*/}
                                    <div className="h-1"></div>
                                    <FloatingSelect
                                      className="bg-white text-left px-2 py-2 h-12 text-sm rounded-md block border border-slate-300 disabled:bg-slate-100"
                                      name={fName("field", "data.status", fieldIndex)}
                                      defaultValue={fieldContainer.status}
                                      disabled={fieldContainer.locked}
                                      onChange={(index) => {
                                        props.onFieldsChange?.(
                                          props.fields.map((field) =>
                                            field.name === fieldContainer.name
                                              ? {
                                                  name: fieldContainer.name,
                                                  status: index,
                                                }
                                              : field,
                                          ),
                                        );
                                      }}
                                      options={statusList.map((item, index) => ({
                                        value: index,
                                        ...item,
                                      }))}
                                      renderValue={(item) => (
                                        <div className="flex flex-row gap-2 justify-between items-center overflow-ellipsis">
                                          {item.item?.Comp || <span>Select...</span>}
                                          <div className="min-w-4">
                                            <ChevronDown
                                              className={twMerge("w-4 h-4 text-slate-500 transition-transform", item.open && "rotate-180")}
                                            />
                                          </div>
                                        </div>
                                      )}
                                      renderOption={(obj) => (
                                        <div className="flex flex-row justify-between gap-3 items-center px-1 py-2 text-sm bg-white">
                                          {obj.item.Comp}
                                        </div>
                                      )}
                                    />
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </ErrorBoundaryWithSuspense>
                      );
                    })}
                  </div>
                </div>
              </div>
            </section>
          );
        })}
      </div>
    </Fragment>
  );
};
