import { z, ZodString } from "zod";
import { arrayAgg, nowValue } from "~/kysely/kysely-helpers";
import { memberIsAdminQb, userSessionId } from "~/domain/member/member-queries.server";
import { referralSourceKeys } from "~/domain/participant/participant-data";
import { allowedParticipantIdsFor, myRegisteredParticipants } from "~/domain/participant/participant-auth-queries.server";
import { ResourceError } from "~/utils/error";
import { v4 } from "uuid";
import { questions } from "~/domain/medical/medical";
import { countries } from "~/data/countries";
import { entries, removeObjectKeys } from "~/misc/helpers";
import { FileTargetValue } from "~/domain/file/file-resource";
import { difference, unique } from "remeda";
import type { AddressType, GeocodingAddressComponentType } from "@googlemaps/google-maps-services-js";
import { Client } from "@googlemaps/google-maps-services-js";
import { <PERSON><PERSON>, <PERSON>, Participant, Person } from "~/kysely/db";
import { updateParticipantsQb } from "~/domain/participant/participant.queries.server";
import { Kysely } from "kysely";
import { updateActivityCache, updateBookingCache } from "~/domain/booking/booking-resource";
import { at_now_value } from "~/kysely/db-static-vars";
import { getCacheKey } from "~/server/cache/cache.planning.server";
import { Args, bustCacheAfter } from "~/server/resource/resource-helpers.server";
import { getWaiverType } from "~/domain/waiver/waiver-vars";
import { MeetingType } from "~/domain/planning/plannings-consts";
import { genderOptions } from "./GenderSelect";

const client = new Client({});

export const updateParticipantCache = async (trx: Kysely<DB>, participantId: string) => {
  await updateParticipantsQb(trx).where("participant.id", "=", participantId).returningAll().execute();
};

export const commentResource: Args<"comment"> = {
  authorize: (args) =>
    args.trx
      .selectFrom("comment")
      .where((eb) => {
        const commentCmpr = eb.or([
          eb.and([
            eb("comment.target", "=", "participation" satisfies keyof DB),
            eb(
              "comment.target_id",
              "in",
              eb
                .selectFrom("participation")
                .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
                .innerJoin("booking", "booking.id", "sale_item.booking_id")
                .select("participation.id")
                .where("booking.establishment_id", "in", memberIsAdminQb(args, "read").select("_member.establishment_id")),
            ),
          ]),
          eb.and([
            eb("comment.target", "=", "rentable" satisfies keyof DB),
            eb(
              "comment.target_id",
              "in",
              eb
                .selectFrom("rentable")
                .select("rentable.id")
                .where("rentable.establishment_id", "in", memberIsAdminQb(args, "write").select("_member.establishment_id")),
            ),
          ]),
        ]);

        return eb.or([
          eb.and([
            eb("comment.target", "=", "comment" satisfies keyof DB),
            eb("comment.target_id", "in", eb.selectFrom("comment").select("comment.id").where(commentCmpr)),
          ]),
          commentCmpr,
        ]);
      })
      .where("comment.id", "=", args.id)
      .executeTakeFirst(),
  // beforeMutate: async (args) => {
  //   console.log("args", args);
  //   if (args.id) return { id: args.id, operation: args.operation };
  //   const date = args.data?.date;
  //   const participationId = args.data?.participation_id;
  //   const content = args.data?.content;
  //   if (!date || !participationId) return { id: args.id || v4(), operation: args.operation };
  //
  //   const existingComment = await args.trx
  //     .selectFrom("comment")
  //     .where("comment.date", "=", date)
  //     .where("comment.participation_id", "=", participationId)
  //     .select("comment.id")
  //     .executeTakeFirst();
  //   console.log("exiom", existingComment, date, participationId, content);
  //
  //   return {
  //     id: existingComment?.id || v4(),
  //     operation: existingComment ? (content === null ? "delete" : "update") : content === null ? "ignore" : "insert",
  //   };
  // },
  insert: (args) => ({
    ...args.data,
    created_at: nowValue,
    created_by_user_session_id: userSessionId(args),
  }),
  update: (args) => removeObjectKeys(args.data, "created_by_user_session_id", "created_at"),
  delete: () => true,
};

export const addressResource: Args<"address"> = {
  disableAudit: () => true,
  authorize: () => true,
  insert: () => false,
  update: () => false,
  delete: () => false,
  beforeMutate: async (args) => {
    const placeId = args.data?.place_id;

    if (typeof placeId !== "string") {
      const address = await args.trx
        .insertInto("address")
        .values({
          postal_code: args.data?.postal_code || null,
          street_number: args.data?.street_number || null,
          route: args.data?.route || null,
          country_code: args.data?.country_code || null,
        })
        .onConflict((eb) =>
          eb
            .columns(["postal_code", "street_number", "route", "country_code"])
            .where("place_id", "is", null)
            .doUpdateSet({ place_id: null }),
        )
        .returningAll()
        .executeTakeFirst();

      if (!address) throw new ResourceError("Could not create or get address");

      return { id: address.id, operation: "ignore" };
    }
    const addresss = await args.trx.selectFrom("address").where("address.place_id", "=", placeId).select("address.id").executeTakeFirst();
    if (addresss) return { id: addresss.id, operation: "ignore" };
    const placeResponse = await client.placeDetails({
      params: {
        place_id: placeId,
        fields: ["address_components", "formatted_address", "geometry", "types"],
        key: "YOUR_API_KEY",
      },
    });
    if (placeResponse.status !== 200) throw new ResourceError("Invalid place");
    const placeDetails = placeResponse.data.result;
    const addressComponents = placeDetails.address_components;

    const getComponent = (type: `${AddressType}` | `${GeocodingAddressComponentType}`) =>
      addressComponents?.find((c) => c.types.includes(type)) || null;

    const newAddress = await args.trx
      .insertInto("address")
      .values({
        place_id: placeId,
        place_details_response: placeDetails,
        formatted_address: placeDetails.formatted_address,
        street_number: getComponent("street_number")?.long_name,
        route: getComponent("route")?.long_name,
        sublocality: getComponent("sublocality")?.long_name,
        locality: getComponent("locality")?.long_name,
        administrative_area_level_1: getComponent("administrative_area_level_1")?.long_name,
        administrative_area_level_2: getComponent("administrative_area_level_2")?.long_name,
        postal_code: getComponent("postal_code")?.long_name,
        country_code: getComponent("country")?.short_name,
        latitude: placeDetails.geometry?.location.lat,
        longitude: placeDetails.geometry?.location.lng,
        types: placeDetails.types || [],
      })
      .returningAll()
      .executeTakeFirstOrThrow();

    return { id: newAddress.id, operation: "ignore" };
  },
};

export const participationResource: Args<"participation"> = {
  authorize: async (args) => {
    const participation = await args.trx
      .selectFrom("participation")
      .where("participation.id", "=", args.id)
      .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
      .innerJoin("booking", "booking.id", "sale_item.booking_id")
      .innerJoin("establishment", "establishment.id", "booking.establishment_id")
      .leftJoin("participant", "participant.id", "participation.participant_id")
      .leftJoin("customer", "customer.id", "participant.customer_id")
      .select((eb) => [
        "participation.participant_id",
        "participation.id",
        "participation.sale_item_id",
        "sale_item.booking_id as sale_item_booking_id",
        "booking.establishment_id as establishment_id",
        "establishment.operator_id",
        eb.selectFrom("invoice").where("invoice.booking_id", "=", eb.ref("booking.id")).select("invoice.id").limit(1).as("invoice_id"),
      ])
      .where((eb) =>
        eb.or([eb("participation.participant_id", "is", null), eb("customer.establishment_id", "=", eb.ref("booking.establishment_id"))]),
      )
      .where((eb) => {
        const myRegistration = eb("participation.participant_id", "in", myRegisteredParticipants(args).select("_participant.id"));
        const allowedForManager = eb("booking.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"));
        if (args.action === "update")
          return eb.or([allowedForManager, eb.or([myRegistration, eb("participation.participant_id", "is", null)])]);
        return eb.or([
          allowedForManager,
          eb("booking.direct_booking", "=", true),
          eb("booking.cart_for_session_id", "=", args.ctx.session_id),
        ]);
      })
      .executeTakeFirst();

    if (!participation) return false;

    // the "0" at the start is to order, and therefore run, this mutation before other mutations that depend on the quantity
    // const updateActivityCacheKey = "0" + updateActivityQuantity.name + participation.sale_item_id;
    // args.after_mutations.insideTransaction.set(updateActivityCacheKey, () => updateActivityQuantity(args.trx, participation.sale_item_id));

    const participantId = participation.participant_id;
    if (participantId) {
      const cacheKey = updateParticipantCache.name + participantId;
      args.after_mutations.insideTransaction.set(cacheKey, () => updateParticipantCache(args.trx, participantId));
    }

    bustCacheAfter(args, getCacheKey({ establishmentId: participation.establishment_id }));

    const cacheKey = updateBookingCache.name + participation.sale_item_booking_id;
    args.after_mutations.insideTransaction.set(cacheKey, () => updateBookingCache(args.trx, participation.sale_item_booking_id));

    if (participation.invoice_id && args.action !== "update")
      throw new ResourceError("Booking is locked because invoice is already generated");

    return participation;
  },
  beforeMutate: async (args) => {
    if (args.id) return { id: args.id, operation: args.operation };
    const deleteOrUpdate = args.operation === "update" || args.operation === "delete";
    if (!deleteOrUpdate) return { id: v4(), operation: args.operation };
    const activityId = args.data?.sale_item_id;
    if (!activityId) throw new ResourceError("activity id required");
    const participation = await args.trx
      .selectFrom("participation")
      .select("participation.id")
      .where("participation.participant_id", "is", null)
      .where("participation.sale_item_id", "=", activityId)
      .orderBy("participation.created_at", args.operation === "delete" ? "desc" : "asc")
      .limit(1)
      .executeTakeFirst();
    if (!participation) throw new ResourceError("Sorry, all available registration slots have been filled.");
    // args.tr
    return { id: participation.id, operation: args.operation };
  },
  insert: (args) => ({
    ...args.data,
    created_by_user_session_id: userSessionId(args),
    created_at: nowValue,
  }),
  update: (args) => ({ participant_id: args.data.participant_id }),
  delete: () => true,
};

const parseBaseParticipants = z
  .object({
    my_gear: z
      .string()
      .nullish()
      .array()
      .nullish()
      .transform((value) => value?.filter((str): str is string => !!str)),
    referral_source: z
      .string()
      .nullish()
      .transform((value) => value && referralSourceKeys.find((source) => source === value)),
    country_code: z
      .string()
      .transform((value) => countries.find((country) => country.country_code === value)?.country_code)
      .nullish(),
    gender: z
      .string()
      .nullish()
      .transform((value) => {
        if (!value) return null;
        const lowerValue = value.toLowerCase();
        const gender = Object.keys(genderOptions).find((key) => key === lowerValue);
        return gender || value;
      }),
  } satisfies Partial<{ [Key in keyof Participant]: any }>)
  .partial()
  .passthrough();

export const personResoruce: Args<"person"> = {
  authorize: (args) => true,
  beforeMutate: async (args) => {
    const data = z
      .object({
        user_id: z.string(),
        first_name: z.string(),
        last_name: z.string(),
      } satisfies Partial<Record<keyof Person, ZodString>>)
      .parse(args.data);
    const existingPerson = await args.trx
      .selectFrom("person")
      .select("person.id")
      .where((eb) => eb.and(entries(data).map(([field, value]) => eb(field, "=", value))))
      .executeTakeFirst();
    return {
      id: existingPerson?.id || v4(),
      operation: existingPerson ? (args.operation === "delete" ? "delete" : "ignore") : args.operation === "insert" ? "insert" : "ignore",
    };
  },
  insert: (args) => args.data,
  update: (args) => false,
  delete: (args) => true,
};

export const customerResource: Args<"customer"> = {
  authorize: (args) => true,
  beforeMutate: async (args) => {
    const data = z
      .object({
        person_id: z.string(),
        establishment_id: z.string(),
      } satisfies Partial<Record<keyof Customer, ZodString>>)
      .parse(args.data);
    const existingCustomer = await args.trx
      .selectFrom("customer")
      .select("customer.id")
      .where((eb) => eb.and(entries(data).map(([field, value]) => eb(field, "=", value))))
      .executeTakeFirst();
    return {
      id: existingCustomer?.id || v4(),
      operation: existingCustomer ? (args.operation === "delete" ? "delete" : "ignore") : args.operation === "insert" ? "insert" : "ignore",
    };
  },
  insert: (args) => args.data,
  update: (args) => false,
  delete: (args) => true,
};

export const participantResource: Args<"participant"> = {
  authorize: async (args) => {
    if (args.trigger === "before" && args.action === "update") return true;
    const participant = await args.trx
      .selectFrom("participant")
      .innerJoin("customer", "customer.id", "participant.customer_id")
      .innerJoin("person", "person.id", "customer.person_id")
      .where("participant.id", "=", args.id)
      .where((eb) => {
        const allowedForMember = eb("customer.establishment_id", "in", memberIsAdminQb(args, "write").select("_member.establishment_id"));
        if (args.action === "delete") {
          return allowedForMember;
        }
        return eb("participant.id", "in", allowedParticipantIdsFor(args, "write").select("_participant.id"));
      })
      .select("customer.establishment_id")
      .executeTakeFirst();
    if (!participant) return false;

    bustCacheAfter(args, getCacheKey({ establishmentId: participant.establishment_id }));

    return participant;
  },
  insert: async (args) => {
    const userSession = await args.getExistingOrCreateAnonymousUserSession();
    return {
      ...parseBaseParticipants.parse(args.data),
      customer_id: args.data.customer_id,
      // user_id: args.data.user_id,
      // first_name: args.data.first_name,
      // last_name: args.data.last_name,
      // establishment_id: args.data.establishment_id,
      // booking_id: args.data.booking_id,
      // created_by_session_id: args.ctx.session_id,
      created_by_user_session_id: userSession.id,
      created_at: nowValue,
    };
  },
  update: async (args) => {
    const customerId = args.data.customer_id;
    if (typeof customerId === "string") {
      const newCustomer = await args.trx
        .selectFrom("customer")
        .innerJoin("person", "person.id", "customer.person_id")
        .select(["person.user_id", "customer.establishment_id"])
        .where("customer.id", "=", customerId)
        .executeTakeFirstOrThrow();
      const existingParticipant = await args.trx
        .selectFrom("participant")
        .innerJoin("customer", "customer.id", "participant.customer_id")
        .innerJoin("person", "person.id", "customer.person_id")
        .where("participant.id", "=", args.id)
        .select(["person.user_id", "participant.id", "customer.establishment_id"])
        .executeTakeFirstOrThrow();

      const establishmentIdChanged = existingParticipant.establishment_id !== newCustomer.establishment_id;
      if (establishmentIdChanged) throw new ResourceError("Cant change establishment for participant");
      const userIdChanged = existingParticipant.user_id !== newCustomer.user_id;
      if (userIdChanged) {
        const allowed = await memberIsAdminQb(args, "write")
          .where("_member.establishment_id", "=", existingParticipant.establishment_id)
          .executeTakeFirst();
        if (!allowed) throw new ResourceError("Not allowed to change user");
      }
    }
    const digitalSigningAgreedAtInput = args.data.digital_signing_agreed_at;
    return {
      ...parseBaseParticipants.parse(args.data),
      digital_signing_agreed_at:
        digitalSigningAgreedAtInput === at_now_value || digitalSigningAgreedAtInput === null
          ? digitalSigningAgreedAtInput && nowValue
          : undefined,
      verified_at: undefined,
      cache_stale: undefined,
      cache_signature_waivers_valid: undefined,
      created_at: undefined,
      created_by_session_id: undefined,
      created_by_user_session_id: undefined,
      establishment_id: undefined,
    };
  },
  onChanged: async (args) => {
    const bookingId = args.diff.after?.booking_id;
    const stayHasChanged = args.diff.diff?.stay;
    const finalStay = args.diff.after?.stay;
    const changedBookingId = args.diff.diff?.booking_id;
    const customerId = args.diff.after?.customer_id;

    if (bookingId && finalStay && stayHasChanged) {
      await args.trx
        .updateTable("booking")
        .where("booking.id", "=", bookingId)
        .where("booking.meeting_type", "=", "PICKUP" satisfies MeetingType)
        .where("booking.meeting_address", "is", null)
        .set({ meeting_address: finalStay })
        .execute();
    }

    if (changedBookingId && customerId) {
      const existingOtherSameCustomer = await args.trx
        .selectFrom("participant")
        .where("participant.id", "!=", args.id)
        .where("participant.booking_id", "=", changedBookingId)
        .where("participant.customer_id", "=", customerId)
        .limit(1)
        .select("participant.id")
        .executeTakeFirst();
      if (existingOtherSameCustomer) return "Same participant already exists for this booking";
    }

    args.after_mutations.insideTransaction.set(updateParticipantCache.name + args.id, () => updateParticipantCache(args.trx, args.id));
    return true;
  },
  delete: () => true,
};

export const participantTokenResource: Args<"participant_token"> = {
  disableAudit: () => true,
  authorize: (args) =>
    args.trx
      .selectFrom("participant_token")
      .innerJoin("participant", "participant.id", "participant_token.participant_id")
      .innerJoin("customer", "customer.id", "participant.customer_id")
      .innerJoin("establishment", "establishment.id", "customer.establishment_id")
      .where("establishment.id", "in", memberIsAdminQb(args).select("_member.establishment_id"))
      .where("participant_token.id", "=", args.id)
      .executeTakeFirst(),
  insert: (args) => ({
    participant_id: args.data.participant_id,
    created_by_user_session_id: userSessionId(args),
    created_at: nowValue,
  }),
  update: () => false,
  delete: () => true,
};

export const participationAddonResource: Args<"participation_addon"> = {
  authorize: async () => {
    // authorization in onChange
    return true;
  },
  insert: (args) => args.data,
  update: (args) => args.data,
  onChanged: async (args) => {
    const diff = args.diff.diff;
    const participationAddon = await args.trx
      .selectFrom("participation_addon")
      .innerJoin("participation", "participation.id", "participation_addon.participation_id")
      .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
      .leftJoin("invoice", "invoice.booking_id", "sale_item.booking_id")
      .where("participation_addon.id", "=", args.id)
      .where((eb) => {
        const myRegistrationsCmpr = eb("participation.participant_id", "in", myRegisteredParticipants(args).select("_participant.id"));
        const establishmentParticipantIds = memberIsAdminQb(args)
          .innerJoin("customer", "customer.establishment_id", "_member.establishment_id")
          .innerJoin("participant", "participant.customer_id", "customer.id")
          .select("participant.id");
        const establishmentAdminCmpr = eb("participation.participant_id", "in", establishmentParticipantIds);
        return eb.or([myRegistrationsCmpr, establishmentAdminCmpr]);
      })
      .select(["sale_item.booking_id", "invoice.id as invoice_id", "sale_item.id as sale_item_id"])
      .executeTakeFirst();

    if (!participationAddon) return false;

    const quantityBefore = args.diff.before?.quantity || 0;
    const quantityAfter = args.diff.after?.quantity || 0;
    if (quantityBefore !== quantityAfter) {
      const bookingId = participationAddon.booking_id;

      args.after_mutations.insideTransaction.set(updateBookingCache.name + bookingId, () => updateBookingCache(args.trx, bookingId));
      args.after_mutations.insideTransaction.set(updateActivityCache.name + participationAddon.sale_item_id, () =>
        updateActivityCache(args.trx, participationAddon.sale_item_id),
      );

      if (participationAddon.invoice_id) {
        return "Can't change addons because invoice is already generated";
      }
    }
    return true;
  },
  delete: () => true,
};

// export const answerResource: Args<"answer"> = {
//   authorize: (args) =>
//     myRegisteredParticipants(args)
//       .innerJoin("participant_waiver", "participant_waiver.participant_id", "_participant.id")
//       .innerJoin("answer", "answer.participant_waiver_id", "participant_waiver.id")
//       .$if(args.trigger === "before", (eb) =>
//         eb.where((eb) =>
//           eb.not(
//             eb.exists(eb.selectFrom("signature").where("signature.participant_waiver_id", "=", eb.ref("answer.participant_waiver_id"))),
//           ),
//         ),
//       )
//       // .where("answer.form_name", "=", medicalFormName)
//       .where("answer.id", "=", args.id)
//       .executeTakeFirst(),
//   // beforeMutate: (args) => {},
//   insert: (args) => {
//     return {
//       ...args.data,
//     };
//   },
//   update: (args) => args.data,
//   delete: () => true,
// };

const medicalRequired = (answers: Array<string | null>) => {
  let medical_evaluation_required = false;
  questions.forEach(([_, subQuestionsArr], quesetionIndex) => {
    const subQuestionsArrr = subQuestionsArr || [];
    const questionId = `q_${quesetionIndex}`;
    const answeredYes = answers.includes(questionId);

    if (answeredYes) {
      if (subQuestionsArrr.length > 0) {
        subQuestionsArrr.forEach((_, subQuestionIndex) => {
          const subQuestionAnswerdYes = answers.includes(`${questionId}_${subQuestionIndex}`);
          if (subQuestionAnswerdYes) {
            medical_evaluation_required = true;
          }
        });
      } else {
        medical_evaluation_required = true;
      }
    }
  });
  return medical_evaluation_required;
};

export const participantWaiverResource: Args<"participant_waiver"> = {
  authorize: async (args) => {
    const participantWaiver = await args.trx
      .selectFrom("participant_waiver")
      .innerJoin("participant", "participant.id", "participant_waiver.participant_id")
      .innerJoin("customer", "customer.id", "participant.customer_id")
      .select((eb) =>
        eb
          .selectFrom("participant as customer_participants")
          .where("customer_participants.customer_id", "=", eb.ref("customer.id"))
          .select((eb) => arrayAgg(eb.ref("customer_participants.id"), "uuid").as("ids"))
          .as("customer_participant_ids"),
      )
      .where("participant_waiver.id", "=", args.id)
      .where("participant_waiver.participant_id", "in", allowedParticipantIdsFor(args).select("_participant.id"))
      .executeTakeFirst();

    if (participantWaiver) {
      participantWaiver.customer_participant_ids?.forEach((id) =>
        args.after_mutations.insideTransaction.set(updateParticipantCache.name + id, () => updateParticipantCache(args.trx, id)),
      );
    }

    return participantWaiver;
  },
  insert: async (args) => {
    const yesAnswers = args.data.medical_yes_answers as string[] | null | undefined;

    const participantQb = args.trx
      .selectFrom("participant")
      .where("participant.id", "=", args.data.participant_id);

    const waiver = await args.trx
      .selectFrom("waiver")
      .select((eb) => [
        "waiver.type",
        "waiver.validity_duration",
        participantQb
          .innerJoin("customer", "customer.id", "participant.customer_id")
          .innerJoin("waiver_establishment", "waiver_establishment.establishment_id", "customer.establishment_id")
          .where("waiver_establishment.waiver_id", "=", eb.ref("waiver.id"))
          .select("waiver_establishment.validity_duration")
          .limit(1)
          .as("overwrite_validity_duration"),
      ])
      .where("waiver.id", "=", args.data.waiver_id as string)
      .executeTakeFirstOrThrow();

    const waiverType = getWaiverType(waiver.type);
    return {
      ...args.data,
      customer_id: participantQb.select("participant.customer_id"),
      validity_duration: waiver.overwrite_validity_duration || waiver.validity_duration,
      signature_required: waiverType.signature,
      waiver_type: waiverType.key,
      upload_required: yesAnswers ? medicalRequired(yesAnswers) : waiverType.upload,
      upload_approved: args.data.upload_approved || false,
    };
  },
  update: async (args) => {
    if (args.data.validity_duration !== undefined) {
      const allowedMember = await memberIsAdminQb(args).executeTakeFirst();
      if (!allowedMember) throw new ResourceError("Not allowed to overwrite validity duration");
    }

    let uploadApproved = args.data?.upload_approved;

    if (uploadApproved) {
      const allowedMember = await memberIsAdminQb(args).executeTakeFirst();
      if (!allowedMember) throw new ResourceError("Not allowed to approve upload ");
    }

    if (uploadApproved instanceof Array) {
      const existingFiles = await args.trx
        .selectFrom("file_target")
        .select("file_target.file_id")
        .where("file_target.target_id", "=", args.id)
        .where("file_target.target", "=", "participant_waiver" satisfies FileTargetValue)
        .execute();

      const existingFileIds = unique(existingFiles.map((fileTarget) => fileTarget.file_id));
      const filesToApprove = unique(uploadApproved);
      if (difference(existingFileIds, filesToApprove).length > 0) {
        throw new ResourceError("There were files uploaded in the meantime");
      }
      uploadApproved = true;
    }

    const yesAnswers = args.data.medical_yes_answers as string[] | null | undefined;
    return {
      // ...removeObjectKeys(args.data, "customer_id"),
      upload_required: yesAnswers ? medicalRequired(yesAnswers) : undefined,
      upload_approved: uploadApproved,
      validity_duration: args.data.validity_duration,
      // medical_evaluation_required: yesAnswers ? medicalRequired(yesAnswers) : undefined,
      // medical_approved: uploadApproved,
    };
  },
  delete: (args) => true,
  // afterMutated: async (args) => {},
};

export const participationWaiverResource: Args<"participation_waiver"> = {
  authorize: async (args) => {
    const participantWaiver = await args.trx
      .selectFrom("participation_waiver")
      .innerJoin("participant", "participant.id", "participation_waiver.participant_id")
      .innerJoin("customer", "customer.id", "participant.customer_id")
      .innerJoin("participant_waiver", "participant_waiver.id", "participation_waiver.participant_waiver_id")
      .innerJoin("customer as other_customer", "participant_waiver.customer_id", "other_customer.id")
      .select("participation_waiver.participant_id")
      .where("participation_waiver.id", "=", args.id)
      .where((eb) => {
        const myParticipantCmpr = eb("participation_waiver.participant_id", "in", myRegisteredParticipants(args).select("_participant.id"));
        const operatorCmpr = eb("customer.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"));
        return eb.or([myParticipantCmpr, operatorCmpr]);
      })
      .executeTakeFirst();

    if (participantWaiver) {
      const participantId = participantWaiver.participant_id;
      const cacheKey = updateParticipantCache.name + participantId;
      args.after_mutations.insideTransaction.set(cacheKey, () => updateParticipantCache(args.trx, participantId));
    }

    return participantWaiver;
  },
  beforeMutate: async (args) => {
    const data = args.data;
    if (args.id) return { id: args.id, operation: args.operation, data: data };
    if (!data) throw new ResourceError("required data for participation_waiver");

    const existing = await args.trx
      .selectFrom("participation_waiver")
      .select(["participation_waiver.id", "participation_waiver.manually_approved"])
      .where("participation_waiver.participant_id", "=", data.participant_id || "")
      .where("participation_waiver.participant_waiver_id", "=", data.participant_waiver_id || "")
      .where("participation_waiver.sale_item_id", "is not distinct from", data.sale_item_id || null)
      .executeTakeFirst();

    return {
      id: existing?.id || v4(),
      operation: args.operation === "delete" ? "delete" : existing ? "update" : "insert",
      data: args.data,
    };
  },
  insert: async (args) => {
    return args.data;
  },
  update: (args) => args.data,
  // onChanged: args => {
  //
  // },
  delete: (args) => true,
};

export const participantDayResource: Args<"participant_day"> = {
  authorize: async (args) =>
    args.trx
      .selectFrom("participant_day")
      .innerJoin("participant", "participant.id", "participant_day.participant_id")
      .innerJoin("customer", "customer.id", "participant.customer_id")
      .where("customer.establishment_id", "in", memberIsAdminQb(args, "read").select("_member.establishment_id"))
      .where("participant_day.id", "=", args.id)
      .executeTakeFirst(),
  beforeMutate: async (args) => {
    const data = args.data;
    if (args.id) return { id: args.id, operation: args.operation, data: data };
    const participantId = data?.participant_id;
    if (!participantId) throw new ResourceError("require participant_id for participant_day");
    const date = data?.date;
    if (!date) throw new ResourceError("require date for participant_day");

    const existing = await args.trx
      .selectFrom("participant_day")
      .select("participant_day.id")
      .where("participant_day.participant_id", "=", participantId)
      .where("participant_day.date", "=", date)
      .executeTakeFirst();

    return {
      id: existing?.id || v4(),
      operation: args.operation === "delete" ? "delete" : existing ? "update" : "insert",
      data: args.data,
    };
  },
  insert: async (args) => {
    return args.data;
  },
  update: (args) => args.data,
  delete: (args) => true,
};
