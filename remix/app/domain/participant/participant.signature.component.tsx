import { Trans } from "@lingui/react/macro";
import React, { useId } from "react";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { useAppContext } from "~/hooks/use-app-context";
import { RInput } from "~/components/ResourceInputs";
import { fName, tableIdRef } from "~/misc/helpers";
import { Signature } from "~/components/field/signature/signature";
import { Jsonify } from "@remix-run/server-runtime/dist/jsonify";
import { usePageOverwrites } from "~/utils/remix";
import { format } from "date-fns";
import { toUtc } from "~/misc/date-helpers";

export const SignatureDateInput = (props: { value?: string | null }) => {
  const context = useAppContext();
  const search = useSearchParams2();
  const overwrites = usePageOverwrites();
  const inputId = useId();
  const selectedDateFormatted =
    search.state.print_friendly && !props.value ? "" : format(toUtc(props.value || context.date.dateParam), "yyyy-MM-dd");
  return (
    <div>
      <label htmlFor={inputId}>
        <Trans>Today's date</Trans>
      </label>
      <input className="input" id={inputId} readOnly value={selectedDateFormatted} disabled />
    </div>
  );
};
const participantKey = "participant";
const representativeKey = "representative";

interface SimpleSignature {
  signed_at: string;
  signed_by: string | null;
  signature: any;
  signed_by_representative: boolean;
}

export interface SignatrueProps {
  participant?: { first_name: string; last_name: string; age?: number } | null;
  signatures?: Jsonify<SimpleSignature>[] | null;
}

export const SignatureFullComp = (props: SignatrueProps) => {
  const participant = props?.participant;
  const participantSignature = props.signatures?.find((signature) => !signature.signed_by_representative);
  const participantFullName = participant ? participant.first_name + " " + participant.last_name : "{participant_name}";
  const representativeSignature = props.signatures?.find((signature) => signature.signed_by_representative);

  const editable = !!participant && !props.signatures?.length;
  return (
    <div className="break-inside-avoid flex flex-wrap gap-6">
      <div className="space-y-3">
        <div>
          <RInput
            className="input"
            label={<Trans>Name participant</Trans>}
            // required
            table={"signature"}
            field={"data.signed_by"}
            index={participantKey}
            readOnly
            defaultValue={participantFullName}
          />
        </div>
        <RInput
          table={"signature"}
          field={"data.participant_waiver_id"}
          index={participantKey}
          type={"hidden"}
          value={tableIdRef("participant_waiver")}
          readOnly
        />
        <SignatureDateInput value={participantSignature?.signed_at} />
        {participantSignature ? (
          <div>
            <span>
              <Trans>Signature participant</Trans>
            </span>
            <img src={participantSignature?.signature.base64} className="border border-gray-300" alt={"signature"} />
          </div>
        ) : (
          <Signature
            label={<Trans>Signature participant</Trans>}
            disabled={!!participantSignature}
            required
            name={fName("signature", "data.signature", participantKey)}
          />
        )}
      </div>

      {Number(participant?.age || 0) < 18 && (
        <div className="space-y-3">
          <div>
            <RInput
              className="input"
              label={<Trans>Name of parent or guardian</Trans>}
              table={"signature"}
              field={"data.signed_by"}
              index={representativeKey}
              readOnly={!editable || !!representativeSignature}
              required
              defaultValue={representativeSignature?.signed_by || ""}
            />
          </div>
          <div>
            <RInput
              table={"signature"}
              field={"data.signed_by_representative"}
              index={representativeKey}
              hidden
              hiddenType={"__boolean__"}
              readOnly
              value={"true"}
            />
          </div>
          <div>
            <RInput
              table={"signature"}
              field={"data.participant_waiver_id"}
              index={representativeKey}
              type={"hidden"}
              readOnly
              value={tableIdRef("participant_waiver")}
            />
          </div>
          <SignatureDateInput value={representativeSignature?.signed_at} />
          {representativeSignature ? (
            <div>
              <span>
                <Trans>Signature of parent or guardian</Trans>
              </span>
              <img src={representativeSignature?.signature.base64} className="border border-gray-300" alt={"signature"} />
            </div>
          ) : (
            <Signature
              label={<Trans>Signature of parent or guardian</Trans>}
              disabled={!!representativeSignature}
              required
              name={fName("signature", "data.signature", representativeKey)}
            />
          )}
        </div>
      )}
    </div>
  );
};
