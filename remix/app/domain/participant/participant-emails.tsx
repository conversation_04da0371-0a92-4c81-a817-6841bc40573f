import type { Kysely } from "kysely";
import type { DB } from "~/kysely/db";
import { Head, Heading, Html, Link, Section, Text } from "@react-email/components";
import { StyledLink, TailwindBody } from "~/components/Mail";
import { _booking_detail, _participant_detail } from "~/misc/paths";
import { diversdeskNoReplyEmail, traveltrusterName } from "~/misc/consts";
import { createMim, getMailContext, getMailContextForPrefix } from "~/server/mail/email.client.server";
import React, { Fragment } from "react";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { bookingQb, bookingQbWithTotalDuration } from "~/domain/booking/booking-queries";
import { saleItemSimpleQb } from "~/domain/activity/activity-queries";
import { getFullUrl } from "~/misc/helpers";
import { participantSimpleQb } from "~/domain/participant/participant.queries.server";
import { getWhitelabelFromHost } from "~/utils/domain-helpers";
import { FunctionResponse } from "~/misc/types";
import { ActivityReminderEmail } from "~/domain/participant/participant-email-templates";
import { kysely } from "~/misc/database.server";
import { nowValue } from "~/kysely/kysely-helpers";
import { ParticipationActivityReminder } from "~/domain/email/participation-query.server";
import { getHost } from "~/misc/web-helpers";

export const sendToParticipant = async (trx: Kysely<DB>, participant_id: string, host: string | null): Promise<FunctionResponse> => {
  const participant = await participantSimpleQb(trx)
    .where("participant.id", "=", participant_id)
    .select((eb) => {
      const establishmentQb = eb
        .selectFrom("establishment")
        .innerJoin("operator", "establishment.operator_id", "operator.id")
        .leftJoin("spot", "spot.id", "establishment.spot_id")
        .selectAll("establishment")
        .select(["spot.name as spot_name", "operator.name as operator_name"]);
      return [
        "user.email",
        // "booking.establishment_id",
        // "booking.booking_reference",
        jsonArrayFrom(
          eb
            .selectFrom("participation")
            .whereRef("participation.participant_id", "=", "participant.id")
            .select((eb) => [
              "participation.id",
              jsonObjectFrom(saleItemSimpleQb.where("sale_item.id", "=", eb.ref("participation.sale_item_id"))).as("activity"),
            ]),
        ).as("participations"),
        jsonObjectFrom(
          bookingQbWithTotalDuration
            .select((eb) => [
              jsonObjectFrom(establishmentQb.where("establishment.id", "=", eb.ref("booking.establishment_id"))).as("establishment"),
              // jsonArrayFrom(activitySimpleQb.where("activity.booking_id", "=", eb.ref("booking.sale_item_id"))).as("activities"),
            ])
            .where(
              "booking.id",
              "in",
              eb
                .selectFrom("participation")
                .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
                .where("participation.participant_id", "=", eb.ref("participant.id"))
                .select("sale_item.booking_id"),
            )
            .limit(1),
        ).as("booking"),
        jsonObjectFrom(establishmentQb.where("establishment.id", "=", eb.ref("customer.establishment_id"))).as("establishment"),
      ];
    })
    .executeTakeFirst();

  if (!participant) return [false, "Participant not found"];

  const establishment = participant.booking?.establishment || participant.establishment;
  const booking = participant.booking;

  const operatorName = establishment?.operator_name;
  const activityAgg = booking?.activity_agg;

  const contactEmail = establishment?.email;
  const contactWhatsapp = establishment?.whatsapp;
  const bookingOrRegistration = activityAgg ? "activity" : "registration";
  const subject = booking?.direct_booking
    ? `Thanks! We are processing your booking`
    : `Thanks! We've received your registration at ${operatorName}`;
  const body = (
    <Html>
      <Head />
      <TailwindBody>
        <Section className="text-slate-800">
          <Heading className="text-xl mb-1">{operatorName}</Heading>
          {booking && <Text>Booking {booking.booking_reference || booking.sqid}</Text>}
          <Heading className="text-xl mb-1">
            Thanks {participant.first_name}!{" "}
            {booking?.direct_booking ? "We are processing your booking" : `We have successfully received your registration.`}
            {!!activityAgg && !booking?.direct_booking && " Let the excitement begin!"}
          </Heading>
          {booking?.direct_booking ? (
            <Text>We will keep you updated!</Text>
          ) : activityAgg ? (
            <Text>
              Your {participant.participations.length > 1 ? "activities are" : "activity is"} scheduled for {activityAgg.duration_start}{" "}
              {!!activityAgg.duration_in_days && `until ${activityAgg.duration_end}`}
            </Text>
          ) : (
            <Text>We will begin organizing your activity and will keep you updated.</Text>
          )}
          <Text>
            To <strong>secure your booking</strong>, please ensure all required paperwork and/or payments are complete.
          </Text>
          <Text>We appreciate your participation and look forward to welcoming you!</Text>
          <StyledLink href={getFullUrl(host) + _participant_detail(participant.id)}>View your registration</StyledLink>
          {/*<StyledLink href={getFullUrl(host) + _verify_participant(token.id)}>View your registration</StyledLink>*/}
          <Text>Kind regards,</Text>
          <Text>Team {operatorName || traveltrusterName}</Text>
        </Section>
        <Section className="bg-slate-50 rounded-md p-3">
          P.S. This is a no-reply email. To get in touch with us, please contact us via:
          <br />
          {operatorName || traveltrusterName}
          <br />
          {contactEmail && (
            <Fragment>
              Email: {contactEmail}
              <br />
            </Fragment>
          )}
          {contactWhatsapp && <Fragment>WhatsApp: {contactWhatsapp}</Fragment>}
        </Section>
      </TailwindBody>
    </Html>
  );

  const mailCtx = await getMailContextForPrefix(trx, getWhitelabelFromHost(host));
  const { msg, send } = await createMim(body);
  msg.setSubject(subject);
  msg.setSender(mailCtx.sender);
  msg.setTo({ addr: participant.email, name: participant.first_name + " " + participant.last_name });
  await send();

  return [true, `Sent registration email to participant "${participant.email}"`];
};

export const sendToParticipantRegisterToEstablishment = async (
  trx: Kysely<DB>,
  participant_id: string,
  host: string,
): Promise<FunctionResponse> => {
  const participant = await participantSimpleQb(trx)
    .innerJoin("establishment", "establishment.id", "customer.establishment_id")
    .where("participant.id", "=", participant_id)
    .selectAll("participant")
    .select((eb) => [
      "user.email",
      "establishment.notification_email as establishment_notification_email",
      "establishment.email as establishment_email",
      jsonObjectFrom(
        bookingQb(trx)
          .where(
            "booking.id",
            "in",
            eb
              .selectFrom("participation")
              .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
              .where("participation.participant_id", "=", eb.ref("participant.id"))
              .select("sale_item.booking_id"),
          )
          .limit(1),
      ).as("booking"),
    ])
    .executeTakeFirst();

  const establishmentEmail = participant?.establishment_email;
  if (!establishmentEmail) return [false, "No Establishment email configured"];
  const establishmentNotificationEmail = participant?.establishment_notification_email || establishmentEmail;
  const booking = participant.booking;

  const participantRegisteredEmailTemplateForAdmin = (
    <Html>
      <Head />
      <TailwindBody>
        <Section>
          {booking?.direct_booking && <Text className="uppercase font-bold">Direct Booking</Text>}
          {!booking && <Text className="uppercase font-bold">Pending participant</Text>}
          <Text>
            {participant.first_name} {participant.last_name} just registered.
          </Text>
          <Text>Email: {participant.email}</Text>
          <Text>Phone: {participant.phone}</Text>
          <Text>
            {booking ? (
              <Link className="text-primary" href={getFullUrl(host) + _booking_detail(booking.id)}>
                {booking.booking_reference || `booking ${booking.sqid}`}
              </Link>
            ) : (
              <Link className="text-primary" href={getFullUrl(host) + _participant_detail(participant.id)}>
                participant
              </Link>
            )}
          </Text>
        </Section>
      </TailwindBody>
    </Html>
  );

  const mailCtx = await getMailContextForPrefix(trx, getWhitelabelFromHost(host));
  const { msg, send } = await createMim(participantRegisteredEmailTemplateForAdmin);
  msg.setSubject(
    `${booking ? (booking.direct_booking ? "Direct Booking, " : "") : "Pending Participant, "}${participant.first_name} ${
      participant.last_name
    } registered`,
  );
  msg.setSender(mailCtx.sender);
  msg.setTo({ addr: establishmentNotificationEmail });

  await send();

  return [true, `Sent notification email to establishment "${establishmentNotificationEmail}"`];
};

export const sendToParticipantReminder = async (args: ParticipationActivityReminder) => {
  const { msg, send } = await createMim(<ActivityReminderEmail reminderTo={args} />);
  const registrationCompleted = !args.cached_incomplete_fields?.length && args.cached_read_waivers_valid;
  const signingCompleted = args.cached_signature_waivers_valid;
  if (!args.establishment_email) return [false, "Establishment mail not set"];
  // msg.setSubject(`Reminder: Your activity is scheduled for ${args.duration}`);
  const subject =  (registrationCompleted && signingCompleted) ? "Reminder for Your Upcoming Activity" : "Your Upcoming Activity & Important Reminder";
  msg.setSubject(subject);
  const emailCtx = getMailContext({ name: args.operator_name, email: args.establishment_email });
  msg.setSender(emailCtx.sender);
  msg.setTo({ addr: args.participant_email, name: args.participant_full_name });
  await send();
};

export const safeSendToParticipant = async (args: ParticipationActivityReminder): Promise<FunctionResponse> => {
  try {
    await sendToParticipantReminder(args);
    return [true, null];
  } catch (e) {
    console.error(e);
    return [false, e instanceof Error ? e.message : "Could not send mail"];
  }
};
