import type { EventInput } from "~/domain/event/event-parser";
import { _event, _ping } from "~/misc/paths";

export const ping = async () => {
  try {
    const result = await fetch(_ping);
    const json = await result.json();
    return true;
  } catch (e) {
    console.error("could not ping", e);
    return false;
  }
};

export const postEvent = (input: EventInput) => {
  const formData = new FormData();
  Object.entries(input).forEach(([key, value]) => {
    if (typeof value === "string") formData.set(key, value);
  });
  fetch(_event, { body: formData, method: "post" })
    .then((result) => {
      return result.json();
    })
    .then((json) => {
      console.log("visit regisiter", json);
    })
    .catch((e) => {
      console.error("could not track visit", e);
    });
};
