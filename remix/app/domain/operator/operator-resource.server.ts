import { isEditorQb } from "~/domain/member/member-queries.server";
import { defaultTabs } from "~/domain/view/view-components";
import { genRandomUuid } from "~/kysely/kysely-helpers";
import { Args } from "~/server/resource/resource-helpers.server";

export const operatorResource: Args<"operator"> = {
  authorize: (args) => isEditorQb(args).executeTakeFirst(),
  insert: (args) => args.data,
  update: (args) => args.data,
  delete: () => true,
  onChanged: async (args) => {
    if (args.action !== "insert") return true;
    const views = await args.trx.selectFrom("view").where("view.operator_id", "=", args.id).execute();
    if (views.length) return true;
    await args.trx
      .insertInto("view")
      .values(
        defaultTabs.map((tab, index) => ({
          id: genRandomUuid,
          name: tab.name,
          columns: tab.columns,
          sorts: JSON.stringify(tab.sorts),
          sort_order: index,
          operator_id: args.id,
        })),
      )
      .execute();
    return true;
  },
};
