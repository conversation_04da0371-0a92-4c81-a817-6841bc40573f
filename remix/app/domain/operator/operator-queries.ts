import { kysely } from "~/misc/database.server";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import { addToNow, formatDatetime, nowValue } from "~/kysely/kysely-helpers";
import { sql } from "kysely";

export const operatorQb = (state: { persist_operator_id: string | null; persist_establishment_id?: string | null }) =>
  kysely
    .selectFrom("operator")
    .where("operator.id", "=", state.persist_operator_id)
    .selectAll("operator")
    .select((eb) => [
      jsonArrayFrom(
        eb
          .selectFrom("establishment")
          .leftJoin("spot", "spot.id", "establishment.spot_id")
          .leftJoin("region", "region.id", "spot.region_id")
          .selectAll("establishment")
          .select((eb) => [
            "spot.name as spot_name",
            "region.timezone",
            formatDatetime(nowValue, "YYYY-MM-DD", eb.ref("region.timezone")).as("today_iso"),
            formatDatetime(nowValue, "DD Month YYYY", eb.ref("region.timezone")).as("today_formatted"),
            formatDatetime(addToNow(-1), "YYYY-MM-DD", eb.ref("region.timezone")).as("yesterday_iso"),
            formatDatetime(addToNow(1), "YYYY-MM-DD", eb.ref("region.timezone")).as("tomorrow_iso"),
            formatDatetime(addToNow(30), "YYYY-MM-DD", eb.ref("region.timezone")).as("plus30days_iso"),
            formatDatetime(addToNow(-30), "YYYY-MM-DD", eb.ref("region.timezone")).as("last30days_iso"),
            formatDatetime(addToNow(30), "YYYY-MM-DD", eb.ref("region.timezone")).as("next30days_iso"),
            sql<string>`(date_trunc('year', now() at time zone ${eb.ref("region.timezone")}):: date)`.as("start_year_iso"),
            sql<string>`(((date_trunc('year', now() at time zone ${eb.ref(
              "region.timezone",
            )}):: date) + interval '1 year' - interval '1 day'):: date)`.as("end_year_iso"),
          ])
          .whereRef("establishment.operator_id", "=", "operator.id")
          .$if(!!state.persist_establishment_id, (eb) => eb.where("establishment.id", "=", state.persist_establishment_id!)),
      ).as("establishments"),
    ]);
