import React, { Fragment } from "react";
import { RInput } from "~/components/ResourceInputs";
import { fName } from "~/misc/helpers";
import { HiddenTypeInput } from "~/components/form/DefaultInput";
import { Column<PERSON>ey, defaultOrderColumns, Sort } from "~/domain/participant/participant-columns";

export interface Tab {
  id: string;
  name: string;
  sorts: Sort[];
  columns: ColumnKey[];
  active?: boolean;
}

export const defaultTabs: Tab[] = [
  {
    id: "essentials",
    name: "Essentials",
    sorts: defaultOrderColumns,
    columns: [
      "name",
      "booking_reference",
      "country",
      "age",
      "dives",
      "level",
      "registration",
      "waivers",
      "activity",
      "comment",
      "payment_status",
      "contact_number",
      "instructor",
      "meet",
      "time",
      "location",
      "diet",
      "allergies",
      "participant_comment",
      "booking_internal_note",
    ],
  },
  {
    id: "dive_specific",
    name: "Dive specific",
    sorts: defaultOrderColumns,
    columns: ["name", "booking_reference", "country", "age", "dives", "level", "height", "weight", "wetsuit", "boots"],
  },
];

export const InitDefaultViewsInputs = (props: { operator_id: string }) => {
  return (
    <Fragment>
      {defaultTabs.map((tab, tabIndex) => {
        return (
          <Fragment>
            <RInput table={"view"} field={"data.name"} index={tabIndex} type={"hidden"} value={tab.name} />
            <RInput table={"view"} field={"data.operator_id"} index={tabIndex} type={"hidden"} value={props.operator_id} />
            <RInput table={"view"} field={"data.sort_order"} index={tabIndex} type={"hidden"} value={tabIndex} />
            <RInput table={"view"} field={"id"} />
            {tab.columns.map((column, index) => (
              <input type={"hidden"} name={fName("view", "data.columns", tabIndex, index)} key={column} value={column} />
            ))}
            <HiddenTypeInput name={fName("view", "data.sorts", tabIndex)} value={"__to_string__"} />
            {tab.sorts.map((orderby, index) => (
              <Fragment key={index}>
                <input type={"hidden"} name={fName("view", "data.sorts", tabIndex, index, "key")} value={orderby.key} />
                <input type={"hidden"} name={fName("view", "data.sorts", tabIndex, index, "direction")} value={orderby.direction} />
              </Fragment>
            ))}
          </Fragment>
        );
      })}
    </Fragment>
  );
};
