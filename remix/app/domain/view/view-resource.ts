import { isEditorQb, memberIsAdminOrOwnerQb } from "~/domain/member/member-queries.server";
import { Args } from "~/server/resource/resource-helpers.server";

export const viewResource: Args<"view"> = {
  authorize: (args) =>
    args.trx
      .selectFrom("view")
      .where("view.id", "=", args.id)
      .where((eb) =>
        eb.or([
          eb(
            "view.operator_id",
            "in",
            memberIsAdminOrOwnerQb(args)
              .innerJoin("establishment", "establishment.id", "_member.establishment_id")
              .select("establishment.operator_id"),
          ),
          eb.exists(isEditorQb(args)),
        ]),
      )
      .executeTakeFirst(),
  update: (args) => args.data,
  insert: (args) => args.data,
  delete: () => true,
};
