import { Selectable } from "kysely/dist/esm";
import { Item, Product } from "~/kysely/db";
import { RInput, RL<PERSON>l, RTextarea } from "~/components/ResourceInputs";
import React, { useState } from "react";
import { CategorySelect } from "~/domain/category/CategoryComponents";
import { fName } from "~/misc/helpers";
import { HiddenTypeInput } from "~/components/form/DefaultInput";
import { twMerge } from "tailwind-merge";
import { difference } from "remeda";
import { XMarkIcon } from "@heroicons/react/20/solid";
import { removeFromArray } from "~/misc/parsers/global-state-parsers";
import { capitalize } from "~/utils/formatters";

export const attributes = ["color", "size"] satisfies (keyof Product)[];

type Attribute = (typeof attributes)[number];

export const SimpleProductForm = (props: {
  product?: Partial<Selectable<Product> & { sales_count?: number | null }>;
  attributeValues?: Record<Attribute, string[]>;
  attributes?: Attribute[];
}) => {
  const [selectedAttributes, setSelectedAttributes] = useState(() => {
    const initAttributes = [...(props.attributes || [])];
    if (props.product?.color) {
      initAttributes.push("color");
    }
    if (props.product?.size) {
      initAttributes.push("size");
    }
    return initAttributes;
  });
  console.log(selectedAttributes);
  const product = props.product;
  const allSelected = !difference(attributes, selectedAttributes).length;
  // const attributes =
  const stock = product?.stock || 0;
  const currentStock = stock - (product?.sales_count || 0);
  const finalAttributes = attributes.filter((attr) => selectedAttributes.includes(attr));
  return (
    <div className={"space-y-3"}>
      {product?.id && <RInput table={"product"} field={"id"} value={product?.id} />}
      <RInput table={"product"} field={"data.item_id"} value={product?.item_id} type={"hidden"} />
      <div>
        <RInput className="input" label={"SKU"} table={"product"} field={"data.sku"} defaultValue={product?.sku || ""} />
      </div>
      <div className="pt-3">
        <select
          className="select w-full"
          value={""}
          disabled={allSelected}
          onChange={(e) => {
            const value = e.target.value;
            if (value) {
              setSelectedAttributes([...selectedAttributes, value as Attribute]);
            }
          }}
        >
          <option value="">
            {allSelected ? "" + finalAttributes.map((attr) => capitalize(attr)).join(", ") : "Add Attribute (optional)"}
          </option>
          {attributes.map((attr) => {
            return (
              <option disabled={selectedAttributes.includes(attr)} key={attr} value={attr} className="capitalize">
                {attr}
              </option>
            );
          })}
        </select>
      </div>

      {finalAttributes.map((attr) => (
        <div key={attr}>
          <div className="flex flex-wrap gap-3 items-center justify-between">
            <RLabel table={"product"} field={`data.${attr}`} className="capitalize">
              {attr}
            </RLabel>
            <button
              type={"button"}
              className="btn"
              onClick={() => {
                setSelectedAttributes(removeFromArray(selectedAttributes, attr));
              }}
            >
              <XMarkIcon className="w-4 h-4" />
            </button>
          </div>
          <RInput
            className="input"
            // label={<span className="capitalize">{attr}</span>}
            table={"product"}
            field={`data.${attr}`}
            defaultValue={product?.[attr] || ""}
          />
        </div>
      ))}
      {/*<div>*/}
      {/*  <RInput className="input" label={"Size"} table={"product"} field={"data.size"} defaultValue={product?.size || ""} />*/}
      {/*</div>*/}

      <div className="pt-6">
        <HiddenTypeInput name={fName("product", "data.stock")} value={"__sum__"} />
        <input type={"hidden"} name={fName("product", "data.stock", 0, 0)} value={stock || 0} />
        <RInput
          table={"product"}
          // hiddenType={"__sum__"}
          field={"data.stock"}
          nameKeys={[1]}
          type={"number"}
          label={
            product?.id ? (
              <span>
                Add Stock (
                <span className={twMerge(currentStock > 10 ? "text-green-600" : currentStock ? "text-slate-700" : "text-red-500")}>
                  {currentStock}
                </span>
                )
              </span>
            ) : (
              "Initial Stock"
            )
          }
          className="input"
          placeholder={"0"}
        />
      </div>
      <RInput table={"product"} field={"data.published"} defaultValue={"true"} hiddenType={"__boolean__"} hidden />
    </div>
  );
};

export const ItemForm = (props: { item?: Partial<Selectable<Item>>; index?: string | number; brands?: string[] }) => {
  const item = props.item;

  return (
    <div className={"space-y-3"}>
      {item?.id && <RInput table={"item"} field={"id"} index={props.index} value={item?.id} />}
      <CategorySelect name={fName("item", "data.category_id", props.index)} defaultValue={item?.category_id} />
      {/*<RInput table={"item"} field={"data.category_id"} index={props.index} value={item?.category_id} type={"hidden"} />*/}
      <RInput table={"item"} field={"data.activity_slug"} index={props.index} value={item?.activity_slug} type={"hidden"} />
      <RInput table={"item"} field={"data.establishment_id"} index={props.index} value={item?.establishment_id} type={"hidden"} />
      <div>
        <RInput
          className="input"
          required
          label={"Title"}
          table={"item"}
          field={"data.title"}
          index={props.index}
          defaultValue={item?.title || ""}
        />
      </div>
      <div>
        <RInput
          className="input"
          label={"Brand"}
          table={"item"}
          field={"data.brand"}
          index={props.index}
          defaultValue={item?.brand || ""}
          datalist={
            !!props.brands?.length &&
            props.brands.map((brand) => (
              <option key={brand} value={brand}>
                {brand}
              </option>
            ))
          }
        />
      </div>
      <div>
        <RLabel table={"item"} field={"data.description"} index={props.index}>
          Description
        </RLabel>
        <br />
        <RTextarea
          className="input min-h-32"
          style={{ fieldSizing: "content" } as any}
          table={"item"}
          field={"data.description"}
          index={props.index}
          defaultValue={item?.description || ""}
        />
      </div>
    </div>
  );
};
