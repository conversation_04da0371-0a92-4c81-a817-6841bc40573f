import { isEditorQb, memberIsAdminOrOwnerQb, userSessionId } from "~/domain/member/member-queries.server";

import { ascNullsLast, nowValue } from "~/kysely/kysely-helpers";
import { removeObjectKeys } from "~/misc/helpers";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { Kysely, sql } from "kysely";
import { DB } from "~/kysely/db";
import { Args, AuthArgs } from "~/server/resource/resource-helpers.server";
import { ResourceError } from "~/utils/error";
import { DivingCertificateLevelKey } from "~/domain/diving-course/diving-courses.data";

const setDefaultFormId = async (db: Kysely<DB>, itemId: string) =>
  db
    .updateTable("item")
    .where("item.id", "=", itemId)
    .set((eb) => ({
      form_root_id: eb
        .selectFrom("form")
        .where("form.deleted_at", "=", at_infinity_value)
        .where((eb) => eb.or([eb("form.establishment_id", "=", eb.ref("item.establishment_id")), eb("form.establishment_id", "is", null)]))
        .where((eb) => {
          const beginnerCourseProductsIdsQb = eb
            .selectFrom("diving_course")
            .where("diving_course.diving_certificate_level_key", "=", "beginner" satisfies DivingCertificateLevelKey)
            .innerJoin("item__diving_course", "diving_course.id", "item__diving_course.diving_course_id")
            .select("item__diving_course.item_id");
          return eb.and([
            eb("form.filter_activity_slug", "=", eb.ref("item.activity_slug")),
            eb.or([eb("form.filter_beginner_diver", "=", false), eb("item.id", "in", beginnerCourseProductsIdsQb)]),
          ]);
        })
        .orderBy("form.establishment_id", ascNullsLast)
        .orderBy("form.filter_beginner_diver", "desc")
        .select("form.root_id")
        .limit(1),
    }))
    .executeTakeFirstOrThrow();

export const itemResource: Args<"item"> = {
  authorize: (args) => {
    return args.trx
      .selectFrom("item")
      .where("item.establishment_id", "in", memberIsAdminOrOwnerQb(args).select("_member.establishment_id"))
      .where("item.id", "=", args.id)
      .executeTakeFirst();
  },
  insert: async (args) => {
    if (!args.data.form_root_id) {
      args.after_mutations.insideTransaction.set(setDefaultFormId.name + args.id, async () => {
        await setDefaultFormId(args.trx, args.id);
      });
    }
    return {
      ...args.data,
      created_at: nowValue,
    };
  },
  update: (args) => removeObjectKeys(args.data, "created_at"),
  delete: () => true,
};

export const productResource: Args<"product"> = {
  authorize: (args) => {
    const notAfterUpdate = !(args.trigger === "after" && args.action === "update");
    return args.trx
      .selectFrom("product")
      .innerJoin("item", "item.id", "product.item_id")
      .$if(notAfterUpdate, (eb) => eb.where("product.deleted_at", "=", at_infinity_value))
      .where("item.establishment_id", "in", memberIsAdminOrOwnerQb(args).select("_member.establishment_id"))
      .where("product.id", "=", args.id)
      .executeTakeFirst();
  },
  insert: async (args) => {
    return {
      ...args.data,
      root_id: args.data.root_id || args.id,
      cached_establishment_id: args.trx.selectFrom("item").select("item.establishment_id").where("item.id", "=", args.data.item_id),
      deleted_at: at_infinity_value,
      deleted_by_user_session_id: null,
    };
  },
  update: (args) => {
    if (args.data.deleted_at) return { deleted_at: nowValue, deleted_by_user_session_id: userSessionId(args) };
    return removeObjectKeys(args.data, "root_id");
    // return pickObjectKeys(args.data, "published", "form_root_id", "price", "price_usd", "tag_id");
  },
  delete: () => true,
};

const getAllowedProductIdsQb = (args: AuthArgs) => {
  // return args.trx.selectFrom("product");
  return args.trx
    .selectFrom("product")
    .innerJoin("item", "item.id", "product.item_id")
    .where((eb) => {
      const memberQb = memberIsAdminOrOwnerQb(args);
      return eb.or([eb("item.establishment_id", "in", memberQb.select("_member.establishment_id")), eb.exists(isEditorQb(args))]);
    });
};

export const priceResource: Args<"price"> = {
  authorize: (args) => true,
  disableAudit: (args) => true,
  beforeMutate: async (args) => {
    const amount = args.data?.amount;
    const currency_id = args.data?.currency_id;

    if (typeof amount !== "number" && typeof amount !== "string") throw new ResourceError("Price amount is required");
    if (typeof currency_id !== "string") throw new ResourceError("Price currency is required");

    const price = await args.trx
      .insertInto("price")
      .values((eb) => ({
        created_at: nowValue,
        created_by_user_session_id: userSessionId(args),
        amount: amount,
        currency_id: currency_id,
        amount_usd: sql<number>`(coalesce (
                                  nullif (${eb.lit(Number(amount))}, 0) / (${eb.selectFrom("currency").where("currency.id", "=", currency_id).limit(1).select("currency.conversion_rate_usd")}),
                                  0
                                  ))`,
      }))
      .onConflict((oc) => oc.columns(["amount", "currency_id"]).doUpdateSet((eb) => ({ amount: eb.ref("excluded.amount") })))
      .returning("price.id")
      .executeTakeFirstOrThrow();

    return { operation: "ignore", id: price.id };
  },
  insert: () => false,
  update: () => false,
  delete: () => false,
};

export const productPriceResource: Args<"product_price"> = {
  authorize: (args) =>
    getAllowedProductIdsQb(args)
      .innerJoin("product_price", "product_price.product_id", "product.id")
      .where("product_price.id", "=", args.id)
      .executeTakeFirst(),
  insert: (args) => args.data,
  update: (args) => args.data,
  delete: () => true,
};

export const productDivingLocationResource: Args<"product__diving_location"> = {
  authorize: (args) =>
    getAllowedProductIdsQb(args)
      .innerJoin("product__diving_location", "product__diving_location.product_id", "product.id")
      // .whereRef("product.created_at", "=", "product__diving_location.created_at")
      .where("product__diving_location.id", "=", args.id)
      .executeTakeFirst(),
  insert: (args) => ({ ...args.data, created_at: nowValue }),
  update: (args) => args.data,
  delete: () => true,
};

export const productDivingSiteResource: Args<"product__diving_site"> = {
  authorize: (args) =>
    getAllowedProductIdsQb(args)
      .innerJoin("product__diving_site", "product__diving_site.product_id", "product.id")
      // .whereRef("product.created_at", "=", "product__diving_site.created_at")
      .where("product__diving_site.id", "=", args.id)
      .executeTakeFirst(),
  insert: (args) => ({ ...args.data, created_at: nowValue }),
  update: (args) => args.data,
  delete: () => true,
};

export const itemDivingCourseResource: Args<"item__diving_course"> = {
  authorize: (args) =>
    getAllowedProductIdsQb(args)
      .innerJoin("item__diving_course", "item__diving_course.item_id", "item.id")
      // .whereRef("product.created_at", "=", "item__diving_course.created_at")
      .where("item__diving_course.id", "=", args.id)
      .executeTakeFirst(),
  insert: (args) => ({ ...args.data, created_at: nowValue }),
  update: (args) => args.data,
  delete: () => true,
};
