import { IdName } from "~/misc/models";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import React, { Fragment } from "react";
import { defaultDebounce, defaultNavOptions } from "~/misc/vars";
import { GreyTag, TagCloseButton } from "~/components/base/Tag";
import { toggleParamByKeyValue, toggleSingleParam } from "~/utils/searchparams";
import { useSubmit } from "@remix-run/react";
import { FilterKey } from "~/domain/activity/activity";

export const createFilterUrl = (name: string, value: string, checked: boolean, multi?: boolean) => {
  const url = new URL(window.location.href);
  if (multi) {
    toggleParamByKeyValue(url.searchParams, name, value, checked);
  } else {
    toggleSingleParam(url.searchParams, name, value, checked);
  }
  url.searchParams.delete("page");
  url.searchParams.delete("diving_course_tag");
  return url;
};

export const FilterSelect = (props: { label: string; description?: string; options: IdName[]; name: Filter<PERSON><PERSON> }) => {
  const { debounce, params } = useSearchParams2();
  const selectedIds = params.getAll(props.name);
  if (props.options.length === 0) return <Fragment />;
  return (
    <div className="flex flex-col gap-1">
      <p className="font-bold">{props.label}</p>
      {!!props.description && <p className="text-slate-600">{props.description}</p>}
      <select
        className="rounded border border-slate-400"
        value=""
        onChange={(e) => {
          const url = createFilterUrl(props.name, e.target.value, true, true);
          debounce(url, defaultDebounce, defaultNavOptions);
        }}
        placeholder="select"
      >
        <option value="">select</option>
        {props.options.map((option) => {
          const disabled = !!selectedIds.find((id) => id === option.id);
          return (
            <option key={option.id} value={option.id} disabled={disabled}>
              {option.name}
            </option>
          );
        })}
      </select>
      <div className="flex flex-col gap-1">
        {props.options
          .filter((option) => !!selectedIds.find((id) => id === option.id))
          .map((option) => (
            <GreyTag key={option.id}>
              <input type={"hidden"} name={props.name} value={option.id} />
              <span className="flex-1">{option.name}</span>
              <TagCloseButton
                onClick={() => {
                  const url = createFilterUrl(props.name, option.id, false, true);
                  debounce(url, defaultDebounce, defaultNavOptions);
                }}
              />
            </GreyTag>
          ))}
      </div>
    </div>
  );
};

export const FilterBooleanCheckbox = (props: {
  defaultChecked: boolean;
  name: FilterKey;
  value: string;
  total?: number;
  label: string;
}) => {
  const submit = useSubmit();
  return (
    <label className="flex items-center space-x-2 text-left">
      <input
        className={"rounded"}
        name={props.name}
        type="checkbox"
        value={props.value}
        defaultChecked={props.defaultChecked}
        onChange={(e) => {
          const form = e.currentTarget?.form;
          document.getElementsByName(props.name).forEach((el) => {
            if (el instanceof HTMLInputElement && props.value !== el.value) {
              el.checked = false;
            }
          });

          form && submit(form, { preventScrollReset: true });
        }}
      />
      <span>{props.label}</span>
      <div className="flex-1" />
    </label>
  );
};

export const FilterCheckbox = (props: { name: FilterKey; value: string; label: string; total?: number | bigint }) => {
  const { params } = useSearchParams2();
  const submit = useSubmit();
  return (
    <label className="flex items-center space-x-2 text-left">
      <input
        className={"rounded"}
        type="checkbox"
        name={props.name}
        value={props.value}
        defaultChecked={!!params.getAll(props.name).find((value) => value === props.value)}
        onChange={(e) => {
          const form = e.currentTarget?.form;
          form && submit(form, { preventScrollReset: true });
        }}
      />
      <span>{props.label}</span>
      <div className="flex-1" />
      {/*<span className="text-sm text-slate-600">({props.total})</span>*/}
    </label>
  );
};

export const BooleanFilter = (props: { label: string; value?: string; count?: { yes: number; no: number } | null; name: FilterKey }) => {
  const { params } = useSearchParams2();
  const value = params.get(props.name);
  return (
    <div className="flex flex-col gap-1">
      <p className="font-bold">{props.label}</p>
      <FilterBooleanCheckbox name={props.name} defaultChecked={!!value} value={"yes"} total={props.count?.yes || 0} label="Yes" />
      <FilterBooleanCheckbox name={props.name} defaultChecked={value === ""} value={""} total={props.count?.no || 0} label="No" />
    </div>
  );
};
