import React from "react";
import { twMerge } from "tailwind-merge";

export const StockBadge = (props: { stock: number }) => {
  const stock = props.stock;
  return (
    <span
      className={twMerge(
        "inline-flex items-center justify-center px-3 py-1 text-xs font-medium rounded-md",
        stock <= 0 && "bg-red-100 text-red-800 relative overflow-hidden",
        stock > 0 && stock <= 10 && "bg-amber-100 text-amber-800",
        stock > 10 && "bg-green-100 text-green-800",
      )}
    >
      {/*{stock <= 0 && (*/}
      {/*  <div className="absolute inset-0 overflow-hidden">*/}
      {/*    <div className="absolute top-0 left-0 w-[200%] h-[200%] -translate-x-1/2 -translate-y-1/2 bg-red-500/20 rotate-45"></div>*/}
      {/*  </div>*/}
      {/*)}*/}
      <span className="relative">{stock}</span>
    </span>
  );
};
