import React from "react";
import { BlueTag } from "~/components/base/Tag";
import { AccomodationIcon, DiverIcon, DivingGearIcon, SnorkelGearIcon, SnorkellerIcon, TransportIcon } from "~/components/Icons";
import { ActivitySlug } from "~/domain/activity/activity";

export const getCountText = (props: { count: number; name?: "session" | "dive" }) => {
  const name = props.name || "session";
  if (props.count > 1) return `${props.count} ${name}s`;
  if (props.count === 1) return `1 ${name}`;
  return "";
};

export const DivesTag = (props: { count: number }) => (
  <BlueTag>
    <DiverIcon />
    <span>{props.count}x</span>
  </BlueTag>
);

export const SnorkelSessionsTag = (props: { count: number }) => (
  <BlueTag>
    <SnorkellerIcon />
    <span>{props.count}x</span>
  </BlueTag>
);

const snorkelSlug = ["snorkeling", "freediving"] satisfies ActivitySlug[];
export const GearIcon = (props: { activity_slug: string; className?: string }) => {
  const slug = props.activity_slug;
  if (snorkelSlug.includes(slug)) {
    return <SnorkelGearIcon className={props.className} />;
  }
  return <DivingGearIcon className={props.className} />;
};

export const StayTag = () => {
  return (
    <BlueTag>
      <AccomodationIcon />
    </BlueTag>
  );
};

export const PickupTag = () => (
  <BlueTag>
    <TransportIcon />
  </BlueTag>
);
