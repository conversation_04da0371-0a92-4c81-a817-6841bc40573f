import { Selectable } from "kysely/dist/esm";
import { Price, ProductPrice } from "~/kysely/db";
import { useAppContext } from "~/hooks/use-app-context";
import { RInput, RSelect } from "~/components/ResourceInputs";
import { tableIdRef } from "~/misc/helpers";
import { disableScrollOnNumberInput } from "~/utils/component-utils";
import React, { useMemo, useState } from "react";

export const ProductPriceForm = (props: { productPrice?: Partial<Selectable<ProductPrice & Price>> }) => {
  const context = useAppContext();
  const productPrice = props.productPrice;
  const [selectedCurrency, setSelectedCurrency] = useState<string>(productPrice?.currency_id || "");

  const foundCurrency = context.currencies.find((c) => c.id === selectedCurrency);
  const currencyDecimals = foundCurrency?.decimals ?? 2;
  const step = Math.pow(10, -currencyDecimals);

  return (
    <fieldset key={productPrice?.id || ""} className="formcontrol flex flex-row items-center gap-1 pl-3 ">
      <span className="font-semibold">Price:</span>
      {productPrice?.id && <RInput table={"product_price"} field={"id"} value={productPrice.id} />}
      <RInput table={"product_price"} field={"data.product_id"} type={"hidden"} value={tableIdRef("product")} />
      <RInput table={"product_price"} field={"data.price_id"} type={"hidden"} value={tableIdRef("price")} />
      <RInput
        minLength={1}
        min={0}
        step={step}
        className="formcontrol-input w-32 py-2 px-2"
        required
        defaultValue={productPrice?.amount || ""}
        table={"price"}
        field={"data.amount"}
        onWheel={disableScrollOnNumberInput}
        type="number"
      />
      <RSelect
        required
        value={selectedCurrency || ""}
        onChange={(e) => setSelectedCurrency(e.target.value)}
        table={"price"}
        field={"data.currency_id"}
        className="formcontrol-input w-20 py-2 pr-2 pl-2"
      >
        {context.currencies.map((item) => (
          <option key={item.id} value={item.id}>
            {item.id}
          </option>
        ))}
      </RSelect>
    </fieldset>
  );
};
