import { activities, ActivitySlug, activitySlugs } from "~/domain/activity/activity";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { flat, unique } from "remeda";
import { DivingCertificateLevelKey, divingLevelKeys } from "~/domain/diving-course/diving-courses.data";

const orderPrefix = (category: string, sortingNumber: number) => `[${sortingNumber.toFixed().padStart(2, "0")}].${category}`;

export const getCategoryLabel = (category: string) => category.replace(/\[\d+]\./g, "");

const nrOfDivesEndNumbers = [1, 2, 3, 4, 5, 10, 15, 20] as const;

const getNrOfDivesCategory = (divingCount: number | null) => {
  if (divingCount === null) return "";
  const end = nrOfDivesEndNumbers.find((nr) => divingCount <= nr);
  if (typeof end !== "number") return orderPrefix(">20 Dives", nrOfDivesEndNumbers.length);
  const endIndex = nrOfDivesEndNumbers.indexOf(end);
  const start = (nrOfDivesEndNumbers[endIndex - 1] ?? -1) + 1;
  if (start === end) return orderPrefix(start + " Dives", endIndex);
  return orderPrefix(start + "-" + end + " Dives", endIndex);
};

const getDurationCategory = (max_duration_in_hours: number | null) => {
  const max = max_duration_in_hours ?? 26;
  return max < 26 ? "Day trip" : "Multi-Day";
};

interface Product {
  activity_slug: ActivitySlug;
  diving_certificate_levels: { diving_certificate_level_key: DivingCertificateLevelKey }[];
  diving_count: number | null;
  duration_in_hours_max: number | null;
  stay: boolean;
  tag_ids: string[] | null;
  tag_id: string | null;
  product_prices: { amount: number; currency_id: string }[];
}

const productTypes: Partial<Record<ActivitySlug, (product: Product) => string[]>> = {
  "fun-diving": (product) => [
    orderPrefix(getNrOfDivesCategory(product.diving_count), 1),
    product.stay ? orderPrefix("Dive & Stay", 4) : "",
  ],
  "diving-course": (product) =>
    product.diving_certificate_levels.map((dcl) => {
      const divingLevelIndex = divingLevelKeys.indexOf(dcl.diving_certificate_level_key);
      console.log("dclname", dcl.diving_certificate_level_key);
      return orderPrefix(orderPrefix(dcl.diving_certificate_level_key, divingLevelIndex), 2);
    }),
  snorkeling: (product) => [
    orderPrefix(getDurationCategory(product.duration_in_hours_max), 3),
    product.stay ? orderPrefix("Snorkeling & Stay", 5) : "",
  ],
  freediving: (product) => [
    orderPrefix(getDurationCategory(product.duration_in_hours_max), 3),
    product.stay ? orderPrefix("Freedive & Stay", 6) : "",
  ],
};

interface Args<T extends Product> {
  establishment: {
    products: Array<T>;
    tags: { id: string; name: string }[];
  };
}

export const useProductLists = <T extends Product>(args: Args<T>) => {
  const search = useSearchParams2();

  const products = args.establishment.products.map((product) => {
    const productTypeCategories = productTypes[product.activity_slug]?.(product) || [];
    return {
      ...product,
      productTypeCategories: productTypeCategories,
    };
  });

  const availableProductTypes = activitySlugs.filter((slug) => activities[slug].retail === search.state.retail);

  const productsFilteredByProductType = products
    .filter((product) => availableProductTypes.includes(product.activity_slug))
    .filter((product) => !search.state.activity_slug || product.activity_slug === search.state.activity_slug);

  const availableMainTagsIds = unique(
    productsFilteredByProductType.map((product) => product.tag_id).filter((tagId): tagId is string => !!tagId),
  ).filter((tagId) => productsFilteredByProductType.find((product) => product.tag_id === tagId));

  const filteredMainTagIds = availableMainTagsIds.filter((tagId) => search.state.main_tags.includes(tagId));
  const availableMainTags = args.establishment.tags
    .filter((tag) => availableMainTagsIds.includes(tag.id))
    .sort((a, b) => a.name.localeCompare(b.name));

  const productsFilteredByTypeAndMainTag = productsFilteredByProductType.filter(
    (product) => !filteredMainTagIds.length || filteredMainTagIds.includes(product.tag_id || ""),
  );

  const filteredProductTypeCategories = unique(flat(productsFilteredByTypeAndMainTag.map((product) => product.productTypeCategories)))
    .filter((cat) => !!cat)
    .sort((a, b) => a.localeCompare(b));

  const selectedProductTypeCategories = filteredProductTypeCategories.filter((cat) => search.state.categories.includes(cat));
  const productsFilteredByMainTagAndTypeCategories = productsFilteredByTypeAndMainTag.filter(
    (product) =>
      !selectedProductTypeCategories.length || product.productTypeCategories.find((cat) => selectedProductTypeCategories.includes(cat)),
  );

  const tagIds = flat(productsFilteredByMainTagAndTypeCategories.map((product) => product.tag_ids || []));
  const filterdTagIds = tagIds.filter((tagId) => search.state.tags.includes(tagId));
  const productTags = args.establishment.tags.filter((tag) => tagIds.includes(tag.id)).sort((a, b) => a.name.localeCompare(b.name));

  const productsFilteredByProductTypeAndAllTags = productsFilteredByMainTagAndTypeCategories.filter(
    (product) => !filterdTagIds.length || product.tag_ids?.find((tagId) => filterdTagIds.includes(tagId)),
  );

  return {
    all: products,
    filtered_product_types: availableProductTypes,
    mainTags: availableMainTags,
    filtered_product_type_categories: filteredProductTypeCategories,
    tags: productTags,
    filtered: productsFilteredByProductTypeAndAllTags,
  };
};
