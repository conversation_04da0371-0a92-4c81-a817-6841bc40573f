import { z } from "zod";

export const durationGroupKeyParser = z.union([z.literal("short"), z.literal("halfday"), z.literal("fullday"), z.literal("multiday")]);

export const durationGroupKeys = durationGroupKeyParser.options.map((option) => option.value);

type DurationGroupKey = z.infer<typeof durationGroupKeyParser>;

interface DurationGroup {
  name: string;
  range: string;
}

export const durationGroups: Record<DurationGroupKey, DurationGroup> = {
  short: { name: "Short, 1-3 hrs.", range: "[1,3]" },
  halfday: { name: "Half day, 4-6 hrs.", range: "[3,6]" },
  fullday: { name: "Full day", range: "[6,25]" },
  multiday: { name: "Multi day", range: "[25,]" },
};
