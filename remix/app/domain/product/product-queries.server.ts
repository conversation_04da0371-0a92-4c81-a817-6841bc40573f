import { SelectQ<PERSON><PERSON><PERSON><PERSON><PERSON>, sql } from "kysely";
import { kysely } from "~/misc/database.server";
import { durationGroups } from "~/domain/product/product-data";
import { jsonArrayFrom } from "kysely/helpers/postgres";
import type { Act<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "~/domain/activity/activity";
import { entries, keys } from "~/misc/helpers";
import { DivingCertificateLevelKey, divingLevelsz, divingOrganizationszz } from "~/domain/diving-course/diving-courses.data";
import {arrayAggDisinct, contextRef, lower, unnestArray} from "~/kysely/kysely-helpers";

export const selectMaxDurationInHours = kysely
  .fn<number | null>("upper", [contextRef("product", "duration_in_hours")])
  .as("duration_in_hours_max");

export const divingCoursesEb = kysely
  .selectFrom("diving_course as dc")
  .innerJoin("item__diving_course as pdc", "pdc.diving_course_id", "dc.id")
  .select("dc.name")
  .whereRef("pdc.item_id", "=", contextRef("item", "id"));

export const divingCoursesJsonEb = jsonArrayFrom(
  divingCoursesEb.select(["dc.id", "dc.name", "dc.tag", "dc.diving_certificate_organization_key", "dc.diving_certificate_level_key"]),
).as("diving_courses");

export const divingLocationsEb = kysely
  .selectFrom("diving_location as dl")
  .select("dl.name")
  .where((eb) =>
    eb.exists(
      eb
        .selectFrom("product__diving_location as pdl")
        .whereRef("pdl.product_id", "=", contextRef("product", "id"))
        .whereRef("pdl.diving_location_id", "=", "dl.id"),
    ),
  );

export const divingLocationsJsonEb = jsonArrayFrom(divingLocationsEb.select("dl.id")).as("diving_locations");

export const divingLevelsEb = kysely
  .selectFrom("item__diving_course")
  .innerJoin("diving_course", "diving_course.id", "item__diving_course.diving_course_id")
  .where("item__diving_course.item_id", "=", contextRef("item", "id"))
  .select("diving_course.diving_certificate_level_key as name")
  .distinct();

export const divingLevelsJsonEb = jsonArrayFrom(divingLevelsEb.select("diving_course.diving_certificate_level_key")).as(
  "diving_certificate_levels",
);

export const pricesJsonEb = jsonArrayFrom(
  kysely
    .selectFrom("product_price")
    .innerJoin("price", "price.id", "product_price.price_id")
    .where("product_price.product_id", "=", contextRef("product", "id"))
    .selectAll("product_price")
    .select(["price.amount", "price.currency_id", "price.amount_usd"]),
).as("product_prices");

const noOfCourses = kysely
  .selectFrom("item__diving_course as pdc")
  .select((eb) => eb.fn.count("pdc.diving_course_id").as("count"))
  .whereRef("pdc.item_id", "=", contextRef("product", "id"));

const productCourses = kysely
  .selectFrom("item__diving_course as pdc")
  .innerJoin("diving_course as dc", "dc.id", "pdc.diving_course_id")
  .whereRef("pdc.item_id", "=", contextRef("product", "id"));

const coursesLevelSortOrderSum = productCourses
  .innerJoin(unnestArray(keys(divingLevelsz)), "arr.key", "dc.diving_certificate_level_key")
  .select((eb) => eb.fn.sum("arr.pos").as("sum"));

const coursesSortOrderSum = productCourses.select((eb) => eb.fn.sum("dc.sort_order").as("sum"));

const coursesLevelSortOrder = sql`(case when (${noOfCourses} > 1) then null else (${coursesLevelSortOrderSum}) end)`;

export const coursesSortOrder = sql`(case when (${noOfCourses} > 1) then null else (${coursesSortOrderSum}) end)`;

export const productsWithOnePriceQb = kysely
  .selectFrom("product")
  .innerJoin("item", "item.id", "product.item_id")
  .leftJoin("product_price", "product_price.product_id", "product.id")
  .leftJoin("price", "price.id", "product_price.price_id");

export const baseProductQb = productsWithOnePriceQb
  .innerJoin("establishment as p_establishment", "p_establishment.id", "item.establishment_id")
  .leftJoin("spot", "spot.id", "p_establishment.spot_id")
  .leftJoin("region", "region.id", "spot.region_id");

export const validProductIds = kysely
  .selectFrom("product as valid_product")
  .innerJoin("item as valid_item", "valid_item.id", "valid_product.item_id")
  .select("valid_product.id")
  .where((eb) =>
    eb.or([
      eb.and([
        eb("valid_item.activity_slug", "=", "diving-course" satisfies ActivitySlug),
        eb.exists(eb.selectFrom("item__diving_course as pdc").select("pdc.diving_course_id").whereRef("pdc.item_id", "=", "valid_item.id")),
      ]),
      eb.and([
        eb.or([
          eb("valid_item.activity_slug", "=", "fun-diving" satisfies ActivitySlug),
          eb("valid_item.activity_slug", "=", "freediving" satisfies ActivitySlug),
        ]),
        eb("valid_product.diving_count", ">", eb.val(0)),
      ]),
      eb.and([
        eb("valid_item.activity_slug", "in", ["snorkeling" satisfies ActivitySlug, "freediving" satisfies ActivitySlug]),
        eb("valid_product.n_sessions", ">", eb.val(0)),
      ]),
      eb("valid_item.activity_slug", "=", "other" satisfies ActivitySlug),
      eb("valid_item.activity_slug", "=", "freediving-course" satisfies ActivitySlug),
    ]),
  );

export const baseValidProductQb = baseProductQb.where(lower(contextRef('product', 'duration_in_hours')), ">", 0 as any as string).where("product.id", "in", validProductIds);

export const baseProductWithSelect = baseProductQb
  .selectAll("item")
  .selectAll("product")
  .select([divingCoursesJsonEb, divingLocationsJsonEb, pricesJsonEb]);

export const baseProductQbExplore = baseValidProductQb
  .where("p_establishment.published", "=", true)
  .where("region.published", "=", true)
  .where("product.published", "=", true);

type BaseProductQb = typeof baseProductQb;

export const orderProduct = <T extends SelectQueryBuilder<any, any, any>>(qb: T) =>
  qb
    .orderBy(coursesLevelSortOrder)
    .orderBy(coursesSortOrder)
    .orderBy(contextRef("product", "stay"))
    .orderBy(sql`(lower (${contextRef("product", "duration_in_hours")}))`)
    .orderBy(contextRef("product", "diving_count"))
    .orderBy(contextRef("product", "n_sessions"))
    .orderBy(contextRef("price", "currency_id"))
    .orderBy(contextRef("price", "amount"))
    // .orderBy(contextRef("product", "price_currency"))
    // .orderBy(contextRef("product", "price"))
    .orderBy(contextRef("product", "root_id")) as T;

export const productFilterPerKey: Record<FilterKey, (qb: BaseProductQb, filterValues: string[]) => BaseProductQb> = {
  diving_certificate_level: (qb, filterValues) =>
    qb.where("item.id", "in", (eb) =>
      eb
        .selectFrom("item__diving_course as pdc")
        .innerJoin("diving_course as dc", "dc.id", "pdc.diving_course_id")
        .where("dc.diving_certificate_level_key", "in", filterValues as DivingCertificateLevelKey[])
        .select("pdc.item_id"),
    ),
  diving_certificate_organization: (qb, filterValues) =>
    qb.where("item.id", "in", (eb) =>
      eb
        .selectFrom("item__diving_course as pdc")
        .innerJoin("diving_course as dc", "dc.id", "pdc.diving_course_id")
        .where("dc.diving_certificate_organization_key", "in", filterValues as ReadonlyArray<keyof typeof divingOrganizationszz>)
        .select("pdc.item_id"),
    ),
  diving_courses: (qb, filterValues) =>
    qb.where("item.id", "in", (eb) =>
      eb.selectFrom("item__diving_course as pdc").where("pdc.diving_course_id", "in", filterValues).select("pdc.item_id"),
    ),
  diving_locations: (qb, filterValues) =>
    qb.where("product.id", "in", (eb) =>
      eb
        .selectFrom("product__diving_location")
        .where("product__diving_location.diving_location_id", "in", filterValues)
        .select("product__diving_location.product_id"),
    ),
  diving_sites: (qb, filterValues) =>
    qb.where("product.id", "in", (eb) =>
      eb
        .selectFrom("product__diving_site")
        .where("product__diving_site.diving_site_id", "in", filterValues)
        .select("product__diving_site.product_id"),
    ),
  stay: (qb, filterValues) => qb.where("product.stay", "=", !!filterValues[0]),
  gear_included: (qb, filterValues) => qb.where("product.gear_included", "=", !!filterValues[0]),
  pickup: (qb, filterValues) => qb.where("product.pickup", "=", !!filterValues[0]),
  spot_id: (qb, filterValues) => qb.where("p_establishment.spot_id", "in", filterValues),
  language_code: (qb, filterValues) =>
    qb.where("p_establishment.id", "in", (eb) =>
      eb
        .selectFrom("establishment__language")
        .where("establishment__language.language_code", "in", filterValues)
        .select("establishment__language.establishment_id"),
    ),
  duration_in_hours: (qb, filterValues) => {
    const filters = entries(durationGroups)
      .filter(([key]) => filterValues.includes(key))
      .map(([_, group]) => sql.raw<boolean>(`duration_in_hours <@ '${group.range}'::int4range`));
    return filters.length > 0 ? qb.where((eb) => eb.or(filters)) : qb;
  },
};

export const divingCertificatesInItemArray = kysely
  .selectFrom("item__diving_course")
  .innerJoin("diving_course", "diving_course.id", "item__diving_course.diving_course_id")
  .select((eb) => arrayAggDisinct(eb.ref("diving_course.diving_certificate_organization_key")).as("certs"))
  .limit(1);
