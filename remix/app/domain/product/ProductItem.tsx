import { DivesTag, GearIcon, PickupTag, SnorkelSessionsTag, StayTag } from "~/domain/product/ProductTags";
import React, { Fragment, ReactNode } from "react";
import { BlueTag } from "~/components/base/Tag";
import { DurationIcon } from "~/components/Icons";
import { formatRange, getDurationGroup, getRangeObject } from "~/components/field/duration_in_hours/duration_in_hours";
import { MoneyValue } from "~/components/field/MoneyValue";
import { twMerge } from "tailwind-merge";
import { ChevronRightIcon } from "@heroicons/react/20/solid";
import { activities, ActivitySlug } from "~/domain/activity/activity";
import {
  DivingCertificateLevelKey,
  divingLevelsz,
  DivingOrganizationKey,
  getDivingCertificateOrganization,
} from "~/domain/diving-course/diving-courses.data";
import { ParamLink, ParamStateLinkProps } from "~/components/meta/CustomComponents";
import { useCurrency } from "~/domain/currency/use-currency";
import { FileTargetModel } from "~/components/form/FilesInput";
import { IkImage } from "~/components/IkImage";

interface ProductTitleData {
  title?: string | null;
  diving_courses?:
    | {
        name: string;
        diving_certificate_level_key: DivingCertificateLevelKey;
        diving_certificate_organization_key: DivingOrganizationKey;
      }[]
    | null;
  diving_locations?: { name: string }[] | null;
  files?: FileTargetModel[] | null;
  duration_in_hours?: string | null;
  stay?: boolean | null;
  activity_slug?: ActivitySlug;
}

export const productTypes = ["activity", "retail"];

const stayPrefixes: Partial<Record<ActivitySlug, string>> = {
  "fun-diving": "Dive & stay",
  snorkeling: "Snorkel & stay",
};

const defaultTitleJoins = ["other", "fun-diving", "snorkeling", "freediving"] satisfies ActivitySlug[];

export const getGeneratedProductTitle = (product: Partial<ProductTitleData>) => {
  const divingCourseNames =
    product.diving_courses?.map((dc) => getDivingCertificateOrganization(dc.diving_certificate_organization_key)?.name + " - " + dc.name) ||
    [];
  const diveLocationNames = product.diving_locations?.map((dl) => dl.name) || [];

  const durationGroup = product.stay
    ? stayPrefixes[product.activity_slug as ActivitySlug] || "Stay"
    : getDurationGroup(product.duration_in_hours).product;

  const texts = defaultTitleJoins.includes(product.activity_slug || "") ? [durationGroup, ...diveLocationNames] : divingCourseNames;

  return texts.join(", ");
};

export const getItemTitle = (item: { id: string; title?: string | null; brand?: string | null }) => {
  const titleSegments = [item.brand, item.title];
  return titleSegments.filter((segment) => segment).join(" - ") || item.id;
};

export const getProductTitle = (product: Partial<ProductTitleData>) => (product.title ? product.title : getGeneratedProductTitle(product));
export const getFullProductTitle = (product: ProductTitleData) =>
  product.title ? product.title : activities[product.activity_slug || "other"].name + ", " + getGeneratedProductTitle(product);

export interface ProductItemModel extends ProductTitleData {
  subtitle?: string | null;
  published?: boolean | null;
  to_currency?: string | null;
  diving_count?: number | null;
  gear_included?: boolean | null;
  pickup?: boolean | null;
  n_sessions?: number | null;
  product_prices: {
    amount?: number | string | null;
    currency_id: string;
  }[];
}

interface Props {
  children?: ReactNode;
  className?: string;
  item: ProductItemModel;
  to_currency?: string;
  link?: ParamStateLinkProps;
}

export const ProductTags = (props: Props) => {
  const item = props.item;
  const divingCertificateLevels = Object.entries(divingLevelsz).filter(([levelKey]) =>
    item.diving_courses?.map((dc) => dc.diving_certificate_level_key).includes(levelKey),
  );
  return (
    <Fragment>
      {item.activity_slug === "diving-course" && divingCertificateLevels.length > 0 && (
        <BlueTag>{divingCertificateLevels.length > 1 ? "Package" : divingCertificateLevels[0]?.[1]}</BlueTag>
      )}
      {!!item.diving_count && <DivesTag count={item.diving_count} />}
      {!!item.n_sessions && <SnorkelSessionsTag count={item.n_sessions} />}
      {item.duration_in_hours && (
        <BlueTag>
          <DurationIcon />
          <span>{formatRange(getRangeObject(item.duration_in_hours))}</span>
        </BlueTag>
      )}
      {item.gear_included && item.activity_slug && (
        <BlueTag>
          <GearIcon activity_slug={item.activity_slug} />
        </BlueTag>
      )}
      {item.pickup && <PickupTag />}
      {item.stay && <StayTag />}
    </Fragment>
  );
};

const ProductInner = (props: Props & { locale: string | null }) => {
  const item = props.item;
  const { finalSelected } = useCurrency();

  const toCurrency = props.to_currency || finalSelected;
  const firstFile = item.files?.[0];

  const firstPrice = props.item.product_prices[0];

  const price = !!firstPrice && (
    <MoneyValue
      nativeAmount={firstPrice.amount ? (typeof firstPrice.amount === "string" ? Number(firstPrice.amount) : firstPrice.amount) : 0}
      nativeCurrency={firstPrice.currency_id}
      toCurrency={toCurrency}
      locale={props.locale}
    />
  );

  return (
    <div className="flex-row flex gap-3">
      {firstFile && (
        <div className="rounded-md overflow-hidden" style={{ width: 120, height: 100 }}>
          <IkImage w={120} h={100} className="object-cover w-full h-full" path={firstFile.filename} />
        </div>
      )}
      <div className="flex-1">
        <div className="flex flex-row justify-between space-x-1">
          <span className={"block font-bold line-clamp-1 first-letter:capitalize"}>{getProductTitle(item)}</span>
          {price && <span className={"block font-bold"}>{price}</span>}
        </div>
        <div className="flex flex-row items-center justify-between">
          <div className="flex flex-col gap-1">
            {item.subtitle && <div className="text-xs text-gray-500 line-clamp-1">{item.subtitle}</div>}
            <div className={"flex flex-wrap gap-2"}>
              <ProductTags item={item} />
              {props.children}
            </div>
          </div>
          {price && props.link && <ChevronRightIcon className="-mr-2 h-6 w-6" />}
        </div>
      </div>
    </div>
  );
};

export const ProductItem = (props: Props & { locale: string | null }) => {
  if (props.link)
    return (
      <ParamLink
        {...props.link}
        className={twMerge(
          `flex flex-col justify-between space-y-1 rounded-md border border-secondary-stroke bg-secondary-50 p-3 transition-all hover:opacity-80 active:opacity-80`,
          props.item.published === false && "border-slate-300 bg-gray-100 opacity-70",
          props.className,
        )}
      >
        <ProductInner {...props} />
      </ParamLink>
    );

  return (
    <div
      className={twMerge(
        `flex flex-col justify-between space-y-1 bg-secondary-50 p-3 `,
        props.item.published === false && "border-slate-300 bg-gray-100 opacity-70",
        props.className,
      )}
    >
      <ProductInner {...props} />
    </div>
  );
};
