import { isEditorQb } from "~/domain/member/member-queries.server";
import { getFullUrl, pickObjectKeys } from "~/misc/helpers";
import {
  createFixedVirtualAccount,
  createSplitRule,
  getAccount,
  setCallbackUrl,
  updateAccount,
} from "~/domain/payment/xendit-client.server";
import { ResourceError } from "~/utils/error";
import { v4 } from "uuid";
import { z } from "zod";
import { Operation } from "~/components/form/DefaultInput";
import { Args } from "~/server/resource/resource-helpers.server";
import { jsonObjectFrom } from "kysely/helpers/postgres";

const insertOrUpdate: Operation[] = ["insert", "update"];

export const xenditAccountResource: Args<"xendit_account"> = {
  authorize: (args) => isEditorQb(args).executeTakeFirst(),
  beforeMutate: async (args) => {
    // authorize before mutate, because we do api calls first
    const editor = await isEditorQb(args).select("_user.editor").executeTakeFirst();
    if (!editor) throw new ResourceError("Unauthorized");

    if (!insertOrUpdate.includes(args.operation)) return args;

    const xenditUserId = args.data?.xendit_user_id;
    const xenditPlatformId = args.data?.xendit_platform_id;
    const xenditProduction = args.data?.production;

    if (typeof xenditUserId !== "string") throw new ResourceError("xendit_user_id is required and should be a string");
    if (typeof xenditPlatformId !== "string") throw new ResourceError("xendit_platform_id is required");
    if (typeof xenditProduction !== "boolean") throw new ResourceError("xendit env (production/test) is required");

    const xenditEnvironment = await args.trx
      .selectFrom("xendit_platform")
      .innerJoin("xendit_environment", "xendit_environment.xendit_platform_id", "xendit_platform.id")
      .select(["xendit_platform.id", "xendit_environment.production", "xendit_environment.xendit_api_key"])
      .where("xendit_environment.production", "=", xenditProduction)
      .where("xendit_platform.id", "=", xenditPlatformId)
      .executeTakeFirst();

    if (!xenditEnvironment) throw new ResourceError("Could not find Xendit env");

    const accountUpdateInfoParser = z.object({
      email: z.string().email(),
      public_profile: z.object({ business_name: z.string() }).optional(),
    });

    const parsedAccountUpdateInfo = accountUpdateInfoParser.safeParse(args.data?.xendit_account_response);
    if (parsedAccountUpdateInfo.success) {
      await updateAccount({
        apiKey: xenditEnvironment.xendit_api_key,
        accountId: xenditUserId,
        request: {
          email: parsedAccountUpdateInfo.data.email,
          public_profile: parsedAccountUpdateInfo.data.public_profile && {
            business_name: parsedAccountUpdateInfo.data.public_profile.business_name,
          },
        },
      });
    }

    const existingAccount = await args.trx
      .selectFrom("xendit_account")
      .select(["xendit_account.id", "xendit_account.production", "xendit_account.xendit_platform_id"])
      .where("xendit_account.xendit_user_id", "=", xenditUserId)
      .executeTakeFirst();
    console.log("exsitngaccount", existingAccount);
    const xenditAccount = await getAccount({ apiKey: xenditEnvironment.xendit_api_key, accountId: xenditUserId });

    const fullUrl = getFullUrl(new URL(args.request.url).host);
    console.log("fullurl", fullUrl);

    const invoiceCallbackResult = await setCallbackUrl({
      apiKey: xenditEnvironment.xendit_api_key,
      url: fullUrl + "/xendit/invoice",
      headers: { "for-user-id": xenditUserId },
      type: "invoice",
    });

    console.log("xendit callback result", invoiceCallbackResult);
    const callbackToken = invoiceCallbackResult.callback_token;
    if (typeof callbackToken !== "string") {
      throw new ResourceError("could not create callback token");
    }

    return {
      id: existingAccount?.id || v4(),
      operation: existingAccount ? "update" : "insert",
      data: {
        xendit_user_id: xenditUserId,
        xendit_platform_id: xenditPlatformId,
        production: xenditProduction,
        xendit_account_response: xenditAccount,
        xendit_invoice_callback_response: invoiceCallbackResult,
        xendit_invoice_callback_token: callbackToken,
      },
    };
  },
  insert: async (args) => args.data,
  update: (args) => args.data,
  delete: () => true,
};

export const xenditEnvironmentResource: Args<"xendit_platform"> = {
  authorize: (args) => isEditorQb(args).executeTakeFirst(),
  insert: (args) => args.data,
  update: () => false,
  delete: () => false,
};

const xenditApiKeyPrefix = ["xnd_development_", "xnd_production_"];
export const xenditApiKeyResource: Args<"xendit_environment"> = {
  authorize: (args) => isEditorQb(args).executeTakeFirst(),
  insert: (args) => {
    const apiKey = args.data.xendit_api_key;
    const prefixIndex = xenditApiKeyPrefix.findIndex((prefix) => apiKey.startsWith(prefix));
    if (prefixIndex < 0) throw new ResourceError("Invalid ApiKey prefix");
    const production = !!prefixIndex;

    return {
      ...args.data,
      xendit_api_key: apiKey,
      production: production,
      xendit_platform_id: args.data.xendit_platform_id,
    };
  },
  update: () => false,
  delete: () => false,
};

export const xenditSplitRuleResource: Args<"xendit_split_rule"> = {
  authorize: (args) => isEditorQb(args).executeTakeFirst(),
  insert: async (args) => {
    const editor = await isEditorQb(args)
      .select((eb) => [
        jsonObjectFrom(
          eb
            .selectFrom("xendit_platform")
            .innerJoin("xendit_account", "xendit_account.xendit_platform_id", "xendit_platform.id")
            .innerJoin("xendit_environment", "xendit_environment.xendit_platform_id", "xendit_platform.id")
            .select(["xendit_environment.xendit_api_key", "xendit_account.id", "xendit_platform.currency_id"])
            .where("xendit_account.main", "=", true)
            .where("xendit_environment.id", "=", args.data.xendit_environment_id),
        ).as("xendit_main_account"),
      ])
      .executeTakeFirst();
    if (!editor) throw new ResourceError("Unauthorized");
    const xenditMainAccount = editor.xendit_main_account;
    if (!xenditMainAccount) throw new ResourceError("invalid xendit env or could not find main account");
    const response = await createSplitRule(xenditMainAccount.xendit_api_key, {
      name: "Commission Split2",
      description: "Split rule for platform commission",
      routes: [
        {
          percent_amount: 0.5, // Percentage of the transaction amount
          currency: xenditMainAccount.currency_id,
          destination_account_id: xenditMainAccount.id, // Replace with actual account ID
          reference_id: "route_1",
        },
      ],
    });

    return {
      ...args.data,
      xendit_response: JSON.stringify(response),
      xendit_split_rule_id: response.id,
    };
  },
  update: (args) => {
    return {
      active: args.data.active || null,
    };
  },
  delete: () => false,
};

export const xenditVirtualBankAccountResource: Args<"xendit_virtual_bank_account"> = {
  authorize: (args) => isEditorQb(args).executeTakeFirst(),
  beforeMutate: async (args) => {
    // authorize before mutate, because we do api calls first
    const editor = await isEditorQb(args).select("_user.editor").executeTakeFirst();
    if (!editor) throw new ResourceError("Unauthorized");
    // if (!args.operation !== 'insert') throw

    const xenditAccountId = args.data?.xendit_account_id;
    if (typeof xenditAccountId !== "string") throw new ResourceError("xendit account id is required");
    const xenditAccount = await args.trx
      .selectFrom("xendit_account")
      .innerJoin("xendit_platform", "xendit_platform.id", "xendit_account.xendit_platform_id")
      .innerJoin("xendit_environment", "xendit_environment.xendit_platform_id", "xendit_platform.id")
      .whereRef("xendit_account.production", "=", "xendit_environment.production")
      .where("xendit_account.id", "=", xenditAccountId)
      .select(["xendit_account.xendit_user_id", "xendit_account.id", "xendit_account.main", "xendit_environment.xendit_api_key"])
      .executeTakeFirstOrThrow();

    const mutateObj = pickObjectKeys(args.data || {}, "name", "bank_code");
    const internalId = v4();

    const newFixedAccount = await createFixedVirtualAccount({
      apiKey: xenditAccount.xendit_api_key,
      headers: xenditAccount.main ? {} : { "for-user-id": xenditAccount.xendit_user_id },
      request: {
        external_id: internalId,
        bank_code: mutateObj.bank_code as string,
        name: mutateObj.name as string,
        is_closed: false,
      },
    });

    return {
      id: internalId,
      operation: "insert",
      data: {
        xendit_account_id: xenditAccountId,
        xendit_virtual_bank_account_id: newFixedAccount.id,
        bank_code: newFixedAccount.bank_code,
        account_number: newFixedAccount.account_number,
        name: newFixedAccount.name,
        obj: newFixedAccount,
      },
    };
  },
  insert: async (args) => args.data,
  update: (args) => false,
  delete: () => false,
};

export const xenditVirtualBankAccountPaymentResource: Args<"xendit_virtual_bank_account_payment"> = {
  authorize: (args) => isEditorQb(args).executeTakeFirst(),
  beforeMutate: async (args) => {
    // authorize before mutate, because we do api calls first
    const editor = await isEditorQb(args).select("_user.editor").executeTakeFirst();
    if (!editor) throw new ResourceError("Unauthorized");

    // fetch("https://api.xendit.co/pool_virtual_accounts/simulate_payment");
    // if (!args.operation !== 'insert') throw

    return { id: "", operation: "ignore" };
  },
  insert: (args) => false,
  update: (args) => false,
  delete: () => false,
};
