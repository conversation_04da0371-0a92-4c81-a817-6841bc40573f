import { kysely } from "~/misc/database.server";
import { nowValue } from "~/kysely/kysely-helpers";
import { v4 } from "uuid";
import { activeUserSessionQb, userSessionId } from "~/domain/member/member-queries.server";
import { featureKeys } from "~/domain/feature/feature";
import { ResourceError } from "~/utils/error";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { emailParser } from "~/domain/user/email-parser";
import { isUuidRegex } from "~/misc/helpers";
import { Args } from "~/server/resource/resource-helpers.server";
import { createPasswordHash } from "~/server/auth/auth.password.server";

export const userResource: Args<"user"> = {
  authorize: () => true,
  beforeMutate: async (args) => {
    if (args.id) return args;
    if (args.operation !== "insert" && args.operation !== "ignore") return args;
    const emailInput = args.data?.email;
    if (!emailInput) return { id: "", operation: "ignore" };

    if (isUuidRegex.test(emailInput)) {
      const participant = await args.trx
        .selectFrom("participant")
        .innerJoin("customer", "customer.id", "participant.customer_id")
        .innerJoin("person", "person.id", "customer.person_id")
        .where("participant.id", "=", emailInput)
        .select("person.user_id")
        .executeTakeFirst();
      if (!participant) throw new ResourceError("Invalid ParticipantId to retrieve user email");
      return { operation: "ignore", id: participant.user_id };
    }

    const parsedEmail = emailParser.safeParse(emailInput);
    if (!parsedEmail.success) throw new ResourceError("Invalid email");
    const email = parsedEmail.data;
    const existingUser = await kysely
      .selectFrom("user")
      .where("user.email", "=", email.toLowerCase())
      .where("user.deleted_at", "=", at_infinity_value)
      .select("id")
      .executeTakeFirst();
    return existingUser
      ? { operation: "ignore", id: existingUser.id }
      : {
          operation: args.operation,
          id: args.operation === "insert" ? v4() : args.id,
        };
  },
  insert: (args) => {
    const parsedEmail = emailParser.safeParse(args.data.email);
    if (!parsedEmail.success || !parsedEmail.data) throw new ResourceError("Invalid email");
    return {
      email: parsedEmail.data,
      deleted_at: at_infinity_value,
      created_by_user_session_id: userSessionId(args),
    };
  },
  update: async (args) => {
    const activeUser = await activeUserSessionQb(args, true).selectAll("_user").executeTakeFirstOrThrow();
    const selfAllowed = activeUser.id === args.id;
    const isAdmin = activeUser.admin;
    const featuresRaw = args.data.features;
    const features = featuresRaw instanceof Array ? featureKeys.filter((feature) => featuresRaw.includes(feature)) : featuresRaw;

    const passwordInput = args.data.password_hash;
    const passwordHash = typeof passwordInput === "string" && selfAllowed ? createPasswordHash(passwordInput) : undefined;

    return {
      editor: isAdmin ? args.data.editor : undefined,
      deleted_at: (selfAllowed || activeUser.editor) && args.data.deleted_at ? nowValue : undefined,
      display_name: selfAllowed ? args.data.display_name : undefined,
      currency_id: selfAllowed ? args.data.currency_id : undefined,
      features: selfAllowed ? features : undefined,
      password_hash: passwordHash,
    };
  },
  delete: () => false,
};
