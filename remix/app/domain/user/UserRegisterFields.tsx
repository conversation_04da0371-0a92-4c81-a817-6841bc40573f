import React, { Fragment } from "react";
import { ParamLink } from "~/components/meta/CustomComponents";
import { PolicyLink, TermsLink } from "~/components/shared";
import { useNavigation } from "@remix-run/react";
import { AnimatingDiv } from "~/components/base/base";
import { RInput, toInputId } from "~/components/ResourceInputs";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { InformationCircleIcon } from "@heroicons/react/20/solid";
import { fName, tableIdRef } from "~/misc/helpers";
import { AuthzMethod } from "~/domain/user_session/user_session";
import { useAppContext } from "~/hooks/use-app-context";

const emailInputName = fName("user", "data.email");
const displayInputName = fName("user_session", "data.display_name");

export const RegisterTooltipButton = () => {
  const search = useSearchParams2();
  const isOpen = search.state.toggle_tooltip === "register";
  return (
    <ParamLink
      paramState={{ toggle_tooltip: isOpen ? undefined : "register" }}
      className={"group flex flex-row items-center gap-1"}
      aria-selected={isOpen}
    >
      <span className="cursor-pointer text-xs italic text-slate-600 group-hover:text-slate-800 group-aria-selected:text-slate-900">
        Why do we ask this?
      </span>
      <InformationCircleIcon className={"h-5 w-5 text-primary group-hover:text-primary-800 group-aria-selected:text-primary-800"} />
    </ParamLink>
  );
};

export const RegisterTooltipContentParticipant = () => {
  const search = useSearchParams2();
  if (search.state.toggle_tooltip === "register") {
    return (
      <div className="mb-3 space-y-2 rounded-md border border-primary p-3 text-slate-700">
        <p className="font-bold">Email and name</p>
        <p>
          We kindly request your email and name to send you the booking details and ensure a hassle-free process during your next visit.
        </p>
        <p>
          We greatly value your privacy and rest assured we’ll treat your personal info with utmost care. For further information, please
          refer to our <TermsLink />
        </p>
      </div>
    );
  }
  return <Fragment />;
};

export const RegisterTooltipContent = () => {
  const search = useSearchParams2();
  if (search.state.toggle_tooltip === "register") {
    return (
      <div className="mb-3 space-y-2 rounded-md border border-primary p-3 text-slate-700">
        <p className="font-bold">Email and name</p>
        <p>
          We request your name and email for better customer support and traceability for our operator partners.
          <br />
          Rest assured, we will treat your personal details with utmost care and privacy.
          <br />
          <br />
          Please refer to our <TermsLink /> and <PolicyLink /> to know more.
        </p>
      </div>
    );
  }
  return <Fragment />;
};

export const RegisterNameLabel = () => {
  return (
    <label className="required" htmlFor={toInputId(displayInputName)}>
      Your name
    </label>
  );
};

export const RegisterNameInput = () => {
  const navigation = useNavigation();
  const context = useAppContext();
  return (
    <input
      id={toInputId(displayInputName)}
      className="input"
      name={displayInputName}
      defaultValue={context.display_name || ""}
      key={context.user_session_id}
      readOnly={!!context.display_name}
      disabled={!!navigation.formData || !!context.user_session_id}
      required
    />
  );
};

export const RegisterEmailInput = () => {
  const navigation = useNavigation();
  const context = useAppContext();
  return (
    <input
      id={toInputId(emailInputName)}
      className="input"
      type="email"
      name={emailInputName}
      key={context.user_session_id}
      defaultValue={context.email || ""}
      readOnly={!!context.email}
      disabled={!!navigation.formData || !!context.user_id}
      required
    />
  );
};

export const UserRegisterFieldsForInquery = () => {
  const context = useAppContext();
  return (
    <Fragment>
      {!context.user_session_id && (
        <Fragment>
          <RInput table={"user_session"} field={"data.user_id"} value={tableIdRef("user")} type={"hidden"} />
          <RInput table={"user_session"} field={"data.method"} value={"email" satisfies AuthzMethod} type={"hidden"} />
          <RInput table={"session"} field={"data.selected_user_id"} value={tableIdRef("user")} type={"hidden"} />
          <RInput table={"one_time_password"} field={"data.user_id"} value={tableIdRef("user")} type={"hidden"} />
        </Fragment>
      )}
      <AnimatingDiv>
        <div className="flex flex-row items-center justify-between pb-1">
          <label className="required" htmlFor={toInputId(emailInputName)}>
            Email
          </label>
          <RegisterTooltipButton />
        </div>
        <RegisterTooltipContent />
        <RegisterEmailInput />
      </AnimatingDiv>
      <div>
        <RegisterNameLabel />
        <RegisterNameInput />
      </div>
    </Fragment>
  );
};
