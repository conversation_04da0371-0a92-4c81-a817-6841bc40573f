import React, { Fragment, useState } from "react";
import { formatAddonText } from "~/domain/addon/addon";
import { Button } from "~/components/base/Button";
import { useRevalidator } from "@remix-run/react";
import { _addon } from "~/misc/paths";
import { useAppContext } from "~/hooks/use-app-context";
import { useCurrency } from "~/domain/currency/use-currency";
import { refreshFormdata } from "~/components/form/form-hooks";
import { fName } from "~/misc/helpers";
import { HiddenTypeInput } from "~/components/form/DefaultInput";
import { ParamLink } from "~/components/meta/CustomComponents";
import { useRevalidatorWithPing } from "~/hooks/use-page-refresh";

export interface Addon {
  id: string;
  name: string;
  price: {
    amount: number;
    currency_id: string;
  };
  unit: string;
}

export const AddonCheckbox = (props: {
  addons: Addon[];
  defaultValue: string[] | null;
  locale: string | null;
  establishment_id?: string;
}) => {
  const [addonIds, setAddonIds] = useState(props.defaultValue || []);
  const context = useAppContext();
  const { finalSelected } = useCurrency();
  const revalidator = useRevalidatorWithPing();

  return (
    <div className="space-y-3">
      <div className="flex flex-wrap items-center gap-3">
        <select
          value=""
          className="select max-w-full"
          onChange={(e) => {
            if (e.target.value) {
              setAddonIds([...addonIds, e.target.value]);
              refreshFormdata();
            }
          }}
        >
          <option value="">Add add-on</option>
          {props.addons.map((addon) => (
            <option key={addon.id} value={addon.id} disabled={addonIds.includes(addon.id)}>
              {formatAddonText(context, finalSelected, props.locale, addon)}
            </option>
          ))}
        </select>
        {!!props.establishment_id && (
          <Fragment>
            <Button
              type="button"
              className="link disabled:text-slate-400"
              disabled={revalidator.state === "loading"}
              onClick={revalidator.revalidate}
            >
              {revalidator.state === "loading" ? "refreshing..." : "refresh"}
            </Button>
            <ParamLink className="link" path={_addon(props.establishment_id)} target="_blank">
              create
            </ParamLink>
          </Fragment>
        )}
      </div>
      {props.addons
        .filter((addon) => addonIds.includes(addon.id))
        .map((addon, index: number) => (
          <input key={index} name={fName("item", "data.addon_ids", 0, index)} value={addon.id} type={"hidden"} />
        ))}
      <HiddenTypeInput name={fName("item", "data.addon_ids")} value={"__empty_array__"} />
      {addonIds.length > 0 && (
        <ul className="space-y-2 rounded bg-secondary-50 p-2">
          {props.addons
            .filter((addon) => addonIds.includes(addon.id))
            .sort((a, b) => addonIds.indexOf(a.id) - addonIds.indexOf(b.id))
            .map((addon) => (
              <li key={addon.id}>
                &nbsp;
                <button
                  className="font-bold"
                  type="button"
                  onClick={() => {
                    setAddonIds(addonIds.filter((addonId) => addonId !== addon.id));
                    refreshFormdata();
                  }}
                >
                  x
                </button>
                &nbsp;-&nbsp;
                {formatAddonText(context, finalSelected, props.locale, addon)}
              </li>
            ))}
        </ul>
      )}
    </div>
  );
};
