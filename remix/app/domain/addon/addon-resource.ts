import { memberIsAdminQb } from "~/domain/member/member-queries.server";
import { removeObjectKeys } from "~/misc/helpers";
import { Args } from "~/server/resource/resource-helpers.server";

export const addonResource: Args<"addon"> = {
  authorize: (args) => {
    return memberIsAdminQb(args)
      .innerJoin("addon", "addon.establishment_id", "_member.establishment_id")
      .where("addon.id", "=", args.id)
      .executeTakeFirst();
  },
  insert: (args) => args.data,
  update: (args) => removeObjectKeys(args.data, "establishment_id"),
  delete: () => true,
};
