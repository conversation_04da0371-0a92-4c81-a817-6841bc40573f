import { useAppContext } from "~/hooks/use-app-context";
import React, { Fragment, useState } from "react";
import { ActionAlert } from "~/components/ActionAlert";
import { RInput, RLabel, RSelect } from "~/components/ResourceInputs";
import { addonUnits } from "~/domain/addon/addon";
import { Backbutton } from "~/components/base/base";
import { SubmitButton } from "~/components/base/Button";
import { DefaultInfoIcon, Tooltip } from "~/components/base/tooltip";
import { disableScrollOnNumberInput } from "~/utils/component-utils";
import { tableIdRef } from "~/misc/helpers";

export const AddonForm = (props: {
  addon?: Partial<{
    id: string;
    name: string;
    price: Partial<{
      amount: number;
      currency_id: string;
    }>;
    unit: string;
    quantity: number;
    allow_change: boolean;
  }>;
}) => {
  const context = useAppContext();
  const [selectedCurrency, setSelectedCurrency] = useState<string>(props.addon?.price?.currency_id || "");
  const foundCurrency = context.currencies.find((c) => c.id === selectedCurrency);
  const currencyDecimals = foundCurrency?.decimals ?? 2;
  const step = Math.pow(10, -currencyDecimals);
  return (
    <Fragment>
      <ActionAlert />
      {props.addon?.id && <RInput table={"addon"} field={"id"} value={props.addon.id} />}
      <RInput table={"addon"} field={"data.price_id"} type={"hidden"} value={tableIdRef("price")} />
      <div className="flex flex-col md:flex-wrap gap-3">
        <div className="flex flex-col md:flex-row gap-3">
          <div>
            <RInput
              table={"addon"}
              field={"data.name"}
              label="Add-on name"
              required
              defaultValue={props.addon?.name}
              className="input w-full md:w-fit"
            />
          </div>
          <div className="flex flex-wrap gap-3">
            <div className="max-md:flex-1">
              <RInput
                label="Price"
                table="price"
                step={step}
                field={"data.amount"}
                defaultValue={props.addon?.price?.amount}
                minLength={1}
                min={0}
                className="input w-full"
                required
                onWheel={disableScrollOnNumberInput}
                type="number"
              />
            </div>
            <div>
              <RLabel table={"price"} field={"data.currency_id"}>
                Currency
              </RLabel>
              <br />
              <RSelect
                table={"price"}
                field={"data.currency_id"}
                value={selectedCurrency}
                onChange={(e) => setSelectedCurrency(e.target.value)}
                required
                className="select"
              >
                {context.currencies.map((item) => (
                  <option key={item.id} value={item.id}>
                    {item.id}
                  </option>
                ))}
              </RSelect>
            </div>
            <div>
              <RLabel table={"addon"} field={"data.unit"} className="inline-flex flex-row gap-1 items-center">
                Basis
                <Tooltip
                  description={
                    <span>
                      Explains the basis for this charge to your customer.
                      <br />
                      Important: Must be entered manually; automatic calculations are not enabled.
                    </span>
                  }
                >
                  <DefaultInfoIcon />
                </Tooltip>
              </RLabel>
              <br />
              <RSelect table={"addon"} field={"data.unit"} className="select" defaultValue={props.addon?.unit}>
                {Object.entries(addonUnits).map(([key, addon]) => (
                  <option key={key} value={key}>
                    {addon.select_label}
                  </option>
                ))}
              </RSelect>
            </div>
          </div>
        </div>
        <div>
          <RInput
            className="input w-fit"
            label={
              <span className="inline-flex items-center gap-1">
                Default Quantity{" "}
                <Tooltip
                  description={
                    <span>Any activity or product with this add-on will automatically pre-set this quantity upon selection.</span>
                  }
                >
                  <DefaultInfoIcon />
                </Tooltip>
              </span>
            }
            table={"addon"}
            required
            min={0}
            onWheel={disableScrollOnNumberInput}
            type={"number"}
            field={"data.quantity"}
            defaultValue={props.addon?.quantity || 0}
          />
        </div>
        <div className="flex flex-row items-center gap-2">
          <div className="flex flex-row gap-2 items-center p-2 border border-slate-200 rounded-md bg-slate-100">
            <RInput
              type="checkbox"
              hiddenType={"__boolean__"}
              table={"addon"}
              field={"data.allow_change"}
              className="checkbox"
              defaultChecked={props.addon?.allow_change}
            />
            <RLabel table={"addon"} field={"data.allow_change"}>
              <span className="inline-flex items-center gap-1">Allow Participant Choice</span>
            </RLabel>
          </div>
          <Tooltip description={<span>Checking this box allows your customer to select this add-on during registration.</span>}>
            <DefaultInfoIcon />
          </Tooltip>
        </div>
      </div>
      {props.addon?.id ? (
        <div className="flex flex-row gap-3 justify-end">
          <Backbutton className="btn hover:underline">Cancel</Backbutton>
          <SubmitButton className="btn btn-primary">Save</SubmitButton>
        </div>
      ) : (
        <div className="flex flex-row gap-3 justify-end">
          <SubmitButton className="btn btn-primary">Create</SubmitButton>
        </div>
      )}
    </Fragment>
  );
};
