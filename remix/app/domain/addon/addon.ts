import type { MoneyContext } from "~/utils/money";
import { formatMoney } from "~/utils/money";
import { keys } from "~/misc/helpers";
import { defaultCurrency } from "~/misc/vars";

export const addonUnits = {
  DAY: {
    label: "day",
    select_label: "per day",
  },
  DIVE: {
    label: "dive",
    select_label: "per dive",
  },
  TRIP: {
    label: "trip",
    select_label: "per trip",
  },
  FEE: {
    label: "Fee",
    select_label: "one time fee",
  },
} as const;

export type AddonUnit = keyof typeof addonUnits;

export interface Addon {
  id: string;
  name: string;
  price: {
    amount: number;
    currency_id: string;
  };
  // price: number;
  // price_currency: string;
  unit: string;
}

export const getUnit = (unitKey: string) => {
  const key = keys(addonUnits).find((key) => unitKey === key);
  return key && addonUnits[key];
};

export const formatAddonText = (context: MoneyContext, currency: string, locale: string | null, addon: Addon) => {
  const price =
    formatMoney(context, {
      nativeAmount: addon.price?.amount || 0,
      nativeCurrency: addon.price.currency_id || defaultCurrency,
      toCurrency: currency,
      locale: locale,
    }) || "-";
  return `${addon.name} ${price} (${addonUnits[addon.unit as AddonUnit]?.select_label})`;
};
