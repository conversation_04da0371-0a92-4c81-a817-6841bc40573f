import { isEditorQb, memberQb, userSessionId } from "~/domain/member/member-queries.server";
import { nowValue } from "~/kysely/kysely-helpers";
import { ResourceError } from "~/utils/error";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { Args, AuthArgs } from "~/server/resource/resource-helpers.server";

const authFormQb = (args: AuthArgs) =>
  args.trx
    .selectFrom("form")
    .where((eb) =>
      eb.or([
        eb.and([eb("form.establishment_id", "is", null), eb.exists(isEditorQb(args))]),
        eb.and([eb("form.establishment_id", "in", memberQb(args).where("_member.admin", ">", 1).select("_member.establishment_id"))]),
      ]),
    );

export const formResource: Args<"form"> = {
  disableAudit: () => true,
  authorize: (args) => authFormQb(args).where("form.id", "=", args.id).executeTakeFirst(),
  insert: async (args) => {
    const rootId = args.data.root_id || args.id;

    const latestFormVersion = await args.trx
      .selectFrom("form")
      .where("form.root_id", "=", rootId)
      .orderBy("form.deleted_at desc")
      .limit(1)
      .selectAll("form")
      .select((eb) => eb.or([eb("form.deleted_at", "=", at_infinity_value), eb("form.deleted_at", "=", nowValue)]).as("isActive"))
      .executeTakeFirst();

    if (latestFormVersion) {
      console.log("deleteata", latestFormVersion);
      if (!latestFormVersion.isActive) {
        throw new ResourceError("You cannot change an inactive form");
      }
      if (latestFormVersion.establishment_id !== (args.data.establishment_id || null)) {
        throw new ResourceError("You cannot change the target/establishment of a form");
      }
    }

    return {
      ...args.data,
      root_id: rootId,
      created_by_user_session_id: userSessionId(args),
      created_at: nowValue,
    };
  },
  update: (args) => {
    if (args.data.deleted_at) {
      return {
        deleted_at: nowValue,
        deleted_by_user_session_id: userSessionId(args),
      };
    }
    return { sort_order: args.data.sort_order };
  },
  delete: () => true,
};
export const fieldResource: Args<"field"> = {
  disableAudit: () => true,
  authorize: (args) => authFormQb(args).innerJoin("field", "field.form_id", "form.id").where("field.id", "=", args.id).executeTakeFirst(),
  insert: (args) => ({
    ...args.data,
    created_by_user_session_id: userSessionId(args),
    created_at: nowValue,
  }),
  update: (args) => {
    const newData = { ...args.data };
    delete newData.created_at;
    delete newData.created_by_user_session_id;
    return newData;
  },
  delete: () => true,
};
export const formWaiverResource: Args<"form_waiver"> = {
  authorize: (args) =>
    authFormQb(args).innerJoin("form_waiver", "form_waiver.form_id", "form.id").where("form_waiver.id", "=", args.id).executeTakeFirst(),
  // beforeMutate: async (args) => {
  //   if (args.id) return args;
  //   const waiverId = args.data?.waiver_id;
  //   const languageCode = args.data?.language_code;
  //
  //   const defaultReturn = {
  //     id: v4(),
  //     operation: args.operation,
  //   };
  //
  //   if (!waiverId || !languageCode) return defaultReturn;
  //
  //   const existingWaiver = await args.trx
  //     .selectFrom("waiver_translation")
  //     .where("waiver_translation.waiver_id", "=", waiverId)
  //     .where("waiver_translation.language_code", "=", languageCode)
  //     .selectAll("waiver_translation")
  //     .executeTakeFirst();
  //
  //   if (!existingWaiver) return defaultReturn;
  //
  //   return {
  //     id: existingWaiver.id,
  //     operation: args.operation === "insert" ? "update" : args.operation,
  //   };
  // },
  insert: (args) => args.data,
  update: (args) => args.data,
  delete: (args) => true,
};
