import { ExpressionBuilder } from "kysely";
import type { <PERSON> } from "~/kysely/db";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { ascNullsLast, descNullsLast } from "~/kysely/kysely-helpers";
import { DivingCertificateLevelKey } from "~/domain/diving-course/diving-courses.data";

export const productFormsQb = (globalEb: Pick<ExpressionBuilder<DB, "item">, "selectFrom">) =>
  globalEb
    .selectFrom("form")
    .where("form.deleted_at", "=", at_infinity_value)
    .where((eb) => {
      const beginnerCourseProductsIdsQb = eb
        .selectFrom("diving_course")
        .where("diving_course.diving_certificate_level_key", "=", "beginner" satisfies DivingCertificateLevelKey)
        .innerJoin("item__diving_course", "diving_course.id", "item__diving_course.diving_course_id")
        .select("item__diving_course.item_id");
      return eb.or([
        eb("form.root_id", "=", eb.ref("item.form_root_id")),
        eb.and([
          eb("form.filter_activity_slug", "=", eb.ref("item.activity_slug")),
          eb.or([eb("form.filter_beginner_diver", "=", false), eb("item.id", "in", beginnerCourseProductsIdsQb)]),
        ]),
      ]);
    })
    // first get the explicit form, than the default
    .orderBy((eb) => eb("form.root_id", "=", eb.ref("item.form_root_id")), descNullsLast)
    .orderBy("form.establishment_id", ascNullsLast);
