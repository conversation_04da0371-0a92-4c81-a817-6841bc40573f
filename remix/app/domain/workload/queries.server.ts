import { kysely } from "~/misc/database.server";
import type { Role } from "~/domain/planning/plannings-consts";
import { sql } from "kysely";
import { coalesceTextArray } from "~/kysely/kysely-helpers";
import { defaultSites } from "~/domain/trip/trip.server";

export const workloadAssignmentsQb = kysely
  .selectFrom("trip_assignment")
  .innerJoin("trip", "trip.id", "trip_assignment.trip_id")
  .innerJoin("participation", "participation.id", "trip_assignment.participation_id")
  .innerJoin("participant", "participant.id", "participation.participant_id")
  .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
  .innerJoin("booking", "booking.id", "sale_item.booking_id")
  .innerJoin("product", "product.id", "sale_item.product_id")
  .innerJoin("item", "item.id", "product.item_id")
  .where("booking.cancelled_at", "is", null)
  .where("trip_assignment.role", "in", ["instructor", "divemaster"] satisfies Role[])
  .select((eb) => {
    const tripDateRef = eb.ref("trip.date");
    const activityDurRef = eb.ref("sale_item.duration");
    const tripMonth = sql`(extract ( month from ${tripDateRef}))`;
    const tripYear = sql`(extract ( year from ${tripDateRef}))`;
    const bookingStartedThisMonth = sql`(${tripMonth} = extract (month from lower (${activityDurRef})))`;
    const bookingStartedThisYear = sql`(${tripYear} = extract (year from lower (${activityDurRef})))`;
    const bookingCompletedThisMonth = sql`(${tripMonth} = extract (month from upper (${activityDurRef})))`;
    const bookingCompletedThisYear = sql`(${tripYear} = extract (year from upper (${activityDurRef})))`;
    return [
      "trip_assignment.trip_id",
      "trip_assignment.participation_id",
      "participation.participant_id",
      "item.activity_slug",
      coalesceTextArray(eb.ref("trip.sites"), defaultSites).as("sites"),
      "booking.id as booking_id",
      sql`(${bookingStartedThisMonth} and ${bookingStartedThisYear})`.as("started_this_month"),
      sql`(${bookingCompletedThisMonth} and ${bookingCompletedThisYear})`.as("completed_this_month"),
    ];
  });
