import { activeUserSessionSimple, memberIsAdminQb } from "~/domain/member/member-queries.server";

import { Head, Html, Link, Section, Text } from "@react-email/components";
import { ExpressionBuilder, Kysely, Selectable } from "kysely";
import { OperandExpression } from "kysely/dist/cjs/parser/expression-parser";
import { SqlBool } from "kysely/dist/cjs/util/type-utils";
import { v4 } from "uuid";
import { TailwindBody } from "~/components/Mail";
import { appConfig } from "~/config/config.server";
import { environment } from "~/config/environment.server";
import type { CallbackName } from "~/domain/callback/callback";
import { updateCurrencies } from "~/domain/currency/currency.server";
import { toBeSentRemindersQuery } from "~/domain/email/participation-query.server";
import { FileTargetValue } from "~/domain/file/file-resource";
import { generateAndSafeWaiverPdf, generateWaiverPdf } from "~/domain/participant-waiver/generate-waiver-pdf";
import {
  safeSendToParticipant,
  sendToParticipant,
  sendToParticipantRegisterToEstablishment,
} from "~/domain/participant/participant-emails";
import { participantWaiverWithFiles } from "~/domain/participant/participant.queries.server";
import { invoiceQb, sendInvoiceToIntuit } from "~/domain/quickbooks/send-invoice-to-intuit";
import type { Callback, DB } from "~/kysely/db";
import { nowValue, sqid } from "~/kysely/kysely-helpers";
import { pdfScreenshot } from "~/libs/pdf-service/pdf-service";
import { kysely } from "~/misc/database.server";
import { getErrorMsgFromCatch } from "~/misc/error-translations";
import { getFullUrl, keys, urlFromSegments } from "~/misc/helpers";
import { mergeStateToParams } from "~/misc/parsers/global-state-parsers";
import { _booking_detail, _participant_detail, _waiver_detail } from "~/misc/paths";
import { notFoundOrUnauthorzied } from "~/misc/responses";
import { FunctionResponse } from "~/misc/types";
import { getHost } from "~/misc/web-helpers";
import { createMim, getMailContextForPrefix } from "~/server/mail/email.client.server";
import { VerificationEmail } from "~/server/mail/email.templates";
import { Args } from "~/server/resource/resource-helpers.server";
import { createCallbacksAndSendCloudTasks, sendCLoudTasks } from "~/server/utils/google-task.server";
import { getWhitelabelFromHost } from "~/utils/domain-helpers";
import { ResourceError } from "~/utils/error";
import { createSignedWriteUrl, getAdminStorageSingapore, notifyPage, notifySession } from "~/utils/firebase.server";
import { sendToSentry } from "~/utils/sentry.server";
import { sendPostActivityEmail } from "../email/post-activity-email.server";
import { toBeSentPostActivityEmailsQuery } from "../email/post-activity-query.server";
import { EmailTemplateKey } from "~/domain/email/email";

interface HandleCallbackArgs {
  trx: Kysely<DB>;
  callbackObj: Selectable<Callback>;
}

export type CallbackImpl = {
  auth: (eb: ExpressionBuilder<DB, "callback">, session_id: string) => OperandExpression<SqlBool> | null;
  handle: (args: HandleCallbackArgs) => Promise<FunctionResponse> | FunctionResponse;
};

const directApi2PdfSafe = true;

export const callbacks: Record<CallbackName, CallbackImpl> = {
  participant_waiver_mutation: {
    auth: () => null,
    handle: async (args) => {
      const participantWaiverId = args.callbackObj.target_id;
      if (!participantWaiverId) return [false, null];
      const participantWaiver = await participantWaiverWithFiles(args.trx)
        .innerJoin("waiver", "waiver.id", "participant_waiver.waiver_id")
        .select("waiver.slug")
        .where("participant_waiver.id", "=", participantWaiverId)
        .executeTakeFirst();

      if (!participantWaiver) return [false, null];
      if (!participantWaiver.participant_id) return [false, "participant does not exist anymore or is disconnected from waiver"];
      const coreFiles = participantWaiver.files.filter((file) => file.sort_order === 0);
      if (coreFiles.length) return [false, null];

      const pdfUrl = new URL(getFullUrl(args.callbackObj.host) + _waiver_detail(participantWaiver.waiver_id));
      mergeStateToParams(pdfUrl.searchParams, { participant_waiver_id: participantWaiver.id, print_friendly: true });

      const fbStorage = await getAdminStorageSingapore();

      const uploadId = v4();
      const path = uploadId + "/" + participantWaiver.slug + ".pdf";
      const bucket = await fbStorage.bucket();

      if (directApi2PdfSafe) {
        const fileRef = bucket.file(path);

        const url = await fileRef.getSignedUrl({
          action: "write",
          version: "v4",
          expires: Date.now() + 1000 * 20,
        });

        const finalUrl = url && url[0];

        if (!finalUrl) throw notFoundOrUnauthorzied("could not create signed write url");

        const result = await generateAndSafeWaiverPdf({ pdfUrl: pdfUrl, presignedUploadUrl: finalUrl });
      } else {
        const result = await generateWaiverPdf({ pdfUrl: pdfUrl });
        const bf = Buffer.from(result.arrayBuffer);
        const uploadedFile = await fbStorage.bucket().file(path).save(bf, { contentType: "application/pdf" });
      }

      const fileRecord = await args.trx
        .insertInto("file")
        .values({
          id: v4(),
          filename: path,
          description: "Generated waiver " + participantWaiver.slug + " PDF for " + participantWaiver.participant_id,
          public: false,
        })
        .returningAll()
        .executeTakeFirstOrThrow();

      const fileTarget = await args.trx
        .insertInto("file_target")
        .values({
          id: v4(),
          file_id: fileRecord.id,
          sort_order: 0,
          target_id: participantWaiver.id,
          target: "participant_waiver" satisfies FileTargetValue,
        })
        .executeTakeFirstOrThrow();

      await notifyPage(_participant_detail(participantWaiver.participant_id));

      return [true, null];
    },
  },
  send_invoice_to_intuit: {
    auth: () => null,
    handle: async (args) => {
      const invoice = await invoiceQb(args.trx, args.callbackObj.target_id);
      if (!invoice) return [false, "Invoice or Quickbooks connection does not exist."];
      const trySendToIntuit = async () => {
        try {
          // Important to to await here instead of returning promise directly.
          // When return pomise direclty it will not go to catch block on error
          return await sendInvoiceToIntuit(args.trx, invoice);
        } catch (e) {
          const errorMsgFromCatch = getErrorMsgFromCatch(e);
          return [false, typeof errorMsgFromCatch === "string" ? errorMsgFromCatch : null] satisfies FunctionResponse;
        }
      };
      const result = await trySendToIntuit();
      await notifyPage(_booking_detail(invoice.booking_id), args.callbackObj.client_id);
      return result;
    },
  },
  generate_invoice_pdf: {
    auth: () => null,
    handle: async (args) => {
      const trx = args.trx;
      const invoice = await trx
        .selectFrom("invoice")
        .innerJoin("booking", "booking.id", "invoice.booking_id")
        .selectAll("invoice")
        .select((eb) => [sqid(eb.ref("booking.id_seq")).as("booking_sqid")])
        .where("invoice.id", "=", args.callbackObj.target_id)
        .executeTakeFirst();
      if (!invoice) return [false, null];

      // generate pdf
      const bookingUrl = new URL(urlFromSegments(getFullUrl(args.callbackObj.host), _booking_detail(invoice.booking_id)));
      mergeStateToParams(bookingUrl.searchParams, { print_friendly: true, print_token: appConfig.PRINT_TOKEN });

      const uploadPath = `${v4()}/invoice-${invoice.booking_sqid}.pdf`;
      const signedUrl = await createSignedWriteUrl(uploadPath);

      await pdfScreenshot({
        url: bookingUrl.toString(),
        options: {
          scale: 0.8,
        },
        useCustomStorage: true,
        storage: { url: signedUrl, method: "PUT", extraHTTPHeaders: { "content-type": "application/pdf" } },
      });

      await trx
        .deleteFrom("file_target")
        .where("file_target.target_id", "=", invoice.id)
        .where("file_target.sort_order", "=", 0)
        .where("file_target.target", "=", "invoice" satisfies FileTargetValue)
        .executeTakeFirstOrThrow();

      const fileRecord = await trx
        .insertInto("file")
        .values({
          id: v4(),
          filename: uploadPath,
          public: false,
        })
        .returningAll()
        .executeTakeFirstOrThrow();

      const fileTarget = await trx
        .insertInto("file_target")
        .values({
          id: v4(),
          file_id: fileRecord.id,
          sort_order: 0,
          target_id: invoice.id,
          target: "invoice" satisfies FileTargetValue,
        })
        .executeTakeFirstOrThrow();

      return [true, null];
    },
  },
  notify_booking_page: {
    auth: (eb, session_id) =>
      eb(
        "callback.target_id",
        "in",
        eb
          .selectFrom("booking")
          .select("booking.id")
          .where(
            "booking.establishment_id",
            "in",
            memberIsAdminQb({ trx: kysely, ctx: { session_id: session_id } }).select("_member.establishment_id"),
          ),
      ),
    handle: async (args) => {
      const booking = await args.trx
        .selectFrom("booking")
        .where("booking.id", "=", args.callbackObj.target_id)
        .select(["booking.id", "booking.establishment_id"])
        .executeTakeFirst();
      if (!booking) return [false, "Booking not found"];
      await notifyPage(_booking_detail(booking.id), args.callbackObj.client_id || "");
      return [true, null];
    },
  },
  send_participant_registration_email: {
    auth: () => null,
    handle: (args) => {
      const targetId = args.callbackObj.target_id;
      if (!targetId) return [false, null];
      return sendToParticipant(args.trx, targetId, args.callbackObj.host);
    },
  },
  send_participant_registration_email_to_establishment: {
    auth: () => null,
    handle: (args) => {
      const targetId = args.callbackObj.target_id;
      if (!targetId) return [false, null];
      return sendToParticipantRegisterToEstablishment(args.trx, targetId, args.callbackObj.host);
    },
  },
  notify_session: {
    auth: (eb, session_id) => eb("callback.target_id", "=", session_id),
    handle: async (args) => {
      const { target_id, client_id } = args.callbackObj;
      if (target_id) {
        await notifySession(target_id, client_id || "");
      }
      return [true, null];
    },
  },
  send_booking_email: {
    auth: (eb, session_id) => null,
    handle: async (args) => {
      const userSession = await args.trx
        .selectFrom("user_session")
        .innerJoin("user", "user.id", "user_session.user_id")
        .select(["user.email", "user_session.verification_token"])
        .where("user_session.id", "=", args.callbackObj.target_id)
        .executeTakeFirstOrThrow();

      const url = `${getFullUrl(args.callbackObj.host)}/verify/${userSession.verification_token}`;

      if (environment === "development") {
        console.log("send booking verification mail", url);
      }

      const operatorPrefix = getWhitelabelFromHost(args.callbackObj.host);
      const mailSender = await getMailContextForPrefix(args.trx, operatorPrefix);

      const { msg, send } = await createMim(<VerificationEmail url={url} ctx={mailSender} />);
      // msg.setSubject("Verify your email for " + mailSender.name);
      msg.setSubject("Verify your email");
      msg.setSender(mailSender.sender);
      msg.setTo(userSession.email);

      await send();
      return [true, null];
    },
  },
  update_currencies: {
    auth: (eb, sessionId) => eb.exists(activeUserSessionSimple(kysely, sessionId, true).where("_user.admin", "=", true).select("_user.id")),
    handle: async (args) => {
      await updateCurrencies(args.trx);

      // schedule again for next day
      await createCallbacksAndSendCloudTasks(args.trx, {
        name: "update_currencies",
        delay_in_seconds: 60 * 60 * 24,
        host: args.callbackObj.host,
      });
      return [true, null];
    },
  },
  send_notifications: {
    auth: (eb, sessionId) => eb.exists(activeUserSessionSimple(kysely, sessionId, true).where("_user.admin", "=", true).select("_user.id")),
    handle: async (args) => {
      if (args.callbackObj.target_id) return [false, "Not implemented for target_id"];
      const maxNrOfMails = 10;
      const activities = await toBeSentRemindersQuery.limit(maxNrOfMails).execute();

      for (const activity of activities) {
        const [success, msg] = await safeSendToParticipant({...activity, booking_host: activity.booking_host || args.callbackObj.host});
        await kysely
          .insertInto("mail")
          .values({
            created_at: nowValue,
            to_email: activity.participant_email,
            to_name: activity.participant_full_name,
            from_name: activity.operator_name,
            establishment_id: activity.establishment_id,
            // template_name: "activity_reminder" satisfies EmailTemplateKey,
            success: success,
            msg: msg,
            sale_item_id: activity.sale_item_id,
            participant_id: activity.participant_id,
          })
          .executeTakeFirst();
      }

      const secondUntilNextBatch = 5;
      const secondsUntilNextHour = 60 * (60 - new Date().getMinutes());
      await createCallbacksAndSendCloudTasks(args.trx, {
        name: "send_notifications",
        delay_in_seconds: activities.length < maxNrOfMails ? secondsUntilNextHour : secondUntilNextBatch,
        host: args.callbackObj.host,
      });
      return [true, null];
    },
  },
  send_push_notification: {
    auth: () => null,
    handle: async (args) => {
      return [true, null];
    },
  },
  notify_establishment_for_medical_evaluation: {
    auth: () => null,
    handle: async (args) => {
      const participantWaiver = await args.trx
        .selectFrom("participant_waiver")
        .innerJoin("participant", "participant.id", "participant_waiver.participant_id")
        .innerJoin("customer", "customer.id", "participant.customer_id")
        .innerJoin("establishment", "establishment.id", "customer.establishment_id")
        .innerJoin("person", "person.id", "customer.person_id")
        .where("participant_waiver.id", "=", args.callbackObj.target_id)
        .select((eb) => [
          "person.full_name",
          "participant_waiver.participant_id",
          "establishment.email as establishment_email",
          "establishment.notification_email as establishment_notification_email",
          eb
            .exists(
              eb
                .selectFrom("callback")
                .select("callback.id")
                .where("callback.target_id", "=", eb.ref("participant_waiver.id"))
                .where("callback.success", "=", true)
                .where("callback.started_at", "is not", null)
                .where("callback.handled_at", "is", null)
                .where("callback.id", "!=", args.callbackObj.id)
                .where("callback.name", "=", args.callbackObj.name),
            )
            .as("is_redundant"),
        ])
        .executeTakeFirstOrThrow();

      if (participantWaiver.is_redundant) {
        return [false, "mail already send"];
      }

      const establishmentEmail = participantWaiver.establishment_email;
      if (!establishmentEmail) {
        return [false, "Establishment email is missing"];
      }
      const establishmentNotificationEmail = participantWaiver.establishment_notification_email || establishmentEmail;
      const prefix = getWhitelabelFromHost(args.callbackObj.host);
      const mailCtx = await getMailContextForPrefix(args.trx, prefix);
      const participantUrl = getFullUrl(args.callbackObj.host) + _participant_detail(participantWaiver.participant_id);
      const { msg, send } = await createMim(
        <Html>
          <Head />
          <TailwindBody>
            <Section>
              <Text>{participantWaiver.full_name} has uploaded a Medical Examiner's Evaluation Form for your approval.</Text>
              <Text>You can view the uploaded form below.</Text>
              <Text>
                <Link className="text-primary" href={participantUrl}>
                  {participantUrl}
                </Link>
              </Text>
            </Section>
          </TailwindBody>
        </Html>,
      );
      msg.setSubject(`Diver Medical Evaluation Result submitted by ${participantWaiver.full_name} – Approval Needed`);
      msg.setSender(mailCtx.sender);
      msg.setTo(establishmentNotificationEmail);

      await send();
      return [true, "dive medical notification sent to " + establishmentNotificationEmail];
    },
  },
  // send_participant_activity_reminder: {
  //   auth: () => null,
  //   handle: async (args) => {
  //     const targetId = args.callbackObj.target_id;
  //     if (!targetId) return [false, null];
  //     return sendToParticipantActivityReminder(args.trx, targetId, args.callbackObj.host);
  //   },
  // },
  send_post_activity_emails: {
    auth: () => null,
    handle: async (args) => {
      const activities = await toBeSentPostActivityEmailsQuery.execute();
      const maxNrOfMails = 10;
      const activitiesToProcess = activities.slice(0, maxNrOfMails);

      for (const activity of activitiesToProcess) {
        let success = false;
        let msg = null;

        try {
          await sendPostActivityEmail(args.trx, {
            participant_id: activity.participant_id,
            sale_item_id: activity.sale_item_id,
            establishment_id: activity.establishment_id,
            participant_name: activity.participant_full_name,
            participant_email: activity.participant_email,
            activity_name: activity.duration || "Activity",
            activity_date: activity.activity_end_date_formatted,
            establishment_name: activity.establishment_email || "Establishment",
            establishment_email: activity.establishment_email,
            booking_id: activity.booking_id,
            operator_name: activity.operator_name,
            host: activity.booking_host || "",
          });
          success = true;
        } catch (error) {
          console.error("Failed to send post-activity email:", error);
          msg = error instanceof Error ? error.message : "Unknown error";
        }

        await args.trx
          .insertInto("mail")
          .values({
            created_at: nowValue,
            to_email: activity.participant_email,
            to_name: activity.participant_full_name,
            from_name: activity.operator_name,
            establishment_id: activity.establishment_id,
            // template_name: "post_activity",
            success: success,
            msg: msg,
            sale_item_id: activity.sale_item_id,
            participant_id: activity.participant_id,
          })
          .executeTakeFirst();
      }

      const secondUntilNextBatch = 5;
      const secondsUntilNextHour = 60 * (60 - new Date().getMinutes());
      await createCallbacksAndSendCloudTasks(args.trx, {
        name: "send_post_activity_emails",
        delay_in_seconds: activities.length < maxNrOfMails ? secondsUntilNextHour : secondUntilNextBatch,
        host: args.callbackObj.host,
      });
      return [true, null];
    },
  },
};

export const callbackResource: Args<"callback"> = {
  disableAudit: () => true,
  authorize: (args) =>
    args.trx
      .selectFrom("callback")
      .where("callback.id", "=", args.id)
      .where((eb) => {
        if (args.action === "delete") {
          return eb.exists(activeUserSessionSimple(args.trx, args.ctx.session_id, true).where("_user.admin", "=", true).select("_user.id"));
        }

        const cmprs = Object.entries(callbacks).map(([key, handle]) => {
          const callbackNameCmpr = eb("callback.name", "=", key as CallbackName);
          const callbackCmpr = handle.auth(eb, args.ctx.session_id);
          if (!callbackCmpr) return callbackNameCmpr;
          return eb.and([callbackNameCmpr, callbackCmpr]);
        });
        return eb.or(cmprs);
      })
      .executeTakeFirst(),
  beforeMutate: async (args) => {
    if (args.id) return { id: args.id, data: args.data, operation: args.operation };
    const upcomingCallback = await args.trx
      .selectFrom("callback")
      .where("callback.target_id", "=", args.data?.target_id || null)
      .where("callback.name", "=", args.data?.name || null)
      .where("callback.handled_at", "is", null)
      .where("callback.started_at", "is", null)
      .executeTakeFirst();
    if (upcomingCallback) return { id: "", operation: "ignore" };
    return { id: v4(), data: args.data, operation: "insert" };
  },
  insert: async (args) => {
    const callbackName = keys(callbacks).find((callbackKey) => callbackKey === args.data.name);
    if (!callbackName) throw new ResourceError(args.data.name + " is not a valid callback name");
    const targetId = args.data.target_id;
    const callback = {
      name: callbackName,
      delay_in_seconds: args.data.delay_in_seconds ? parseInt(args.data.delay_in_seconds + "") : 5,
      target_id: targetId,
      host: getHost(args.request),
    };
    if (callbackName === "send_notifications") {
      const existingCallback = await args.trx
        .selectFrom("callback")
        .where("callback.name", "=", callbackName)
        .where("callback.handled_at", "is", null)
        .executeTakeFirst();
      if (existingCallback) {
        throw new ResourceError("There can only be one planned task for " + callbackName);
      }
    }
    if (callbackName === "generate_invoice_pdf" && typeof targetId === "string") {
      args.after_mutations.insideTransaction.set("invalidate_existing_invoice_pdfs_for_" + args.id, async () => {
        await args.trx
          .updateTable("file_target")
          .set({ sort_order: 1 })
          .where("file_target.sort_order", "=", 0)
          .where("file_target.target_id", "=", targetId)
          .execute();
      });
    }
    args.after_mutations.outsideTransaction.set("callback" + args.id, async () => {
      console.time("sending cloud tasks");
      await sendCLoudTasks({ id: args.id, ...callback });
      console.timeEnd("sending cloud tasks");
    });
    return callback;
  },
  update: () => false,
  delete: () => true,
};

export const handleCallback = async (
  request: Request,
  db: Kysely<DB>,
  callback: CallbackImpl | undefined,
  callbackObj: Selectable<Callback>,
) => {
  // if (!callback) return [false, `Callback type "${callbackObj.name}" is not implemented`] as const;
  // return callback.handle({ trx: db, callbackObj: callbackObj });
  try {
    if (!callback) return [false, `Callback type "${callbackObj.name}" is not implemented`] as const;
    const callbackResponse = await callback.handle({ trx: db, callbackObj: callbackObj });
    return callbackResponse;
  } catch (error) {
    console.error("callback error", error);
    await sendToSentry(request, error);
    const errorMsgFromCatch = getErrorMsgFromCatch(error);

    return [false, typeof errorMsgFromCatch === "string" ? errorMsgFromCatch : null] satisfies FunctionResponse;
  }
};
