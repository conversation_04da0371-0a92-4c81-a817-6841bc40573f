export const callbacks = {
  participant_waiver_mutation: { name: "participant_waiver_mutation" },
  notify_session: { name: "notify_session" },
  notify_booking_page: { name: "notify_booking_page" },
  update_currencies: { name: "update_currencies" },
  send_booking_email: { name: "send_booking_email" },
  // send_participant_activity_reminder: { name: "send_participant_activity_reminder" },
  send_participant_registration_email: { name: "Participant registration mail" },
  send_participant_registration_email_to_establishment: { name: "send_participant_registration_email_to_establishment" },
  notify_establishment_for_medical_evaluation: { name: "notify_establishment_for_medical_evaluation" },
  generate_invoice_pdf: { name: "generate_invoice_pdf" },
  send_invoice_to_intuit: { name: "send_invoice_to_intuit" },
  send_push_notification: { name: "send_push_notification" },
  send_notifications: { name: "check" },
  send_post_activity_emails: { name: "send_post_activity_emails" },
} as const;

export type CallbackName = keyof typeof callbacks;
