import { CallbackName } from "~/domain/callback/callback";
import React, { Fragment } from "react";
import { RInput } from "~/components/ResourceInputs";

export const CallbackInput = (props: { callbackName: CallbackName; target_id: string; delay_in_seconds?: number }) => {
  const index = props.callbackName + props.target_id;
  return (
    <Fragment>
      <RInput table={"callback"} field={"data.target_id"} index={index} value={props.target_id} type={"hidden"} />
      <RInput table={"callback"} field={"data.name"} index={index} value={props.callbackName} type={"hidden"} />
      {!!props.delay_in_seconds && (
        <RInput table={"callback"} field={"data.delay_in_seconds"} index={index} value={props.delay_in_seconds} type={"hidden"} />
      )}
    </Fragment>
  );
};
