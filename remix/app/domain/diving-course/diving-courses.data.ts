import { DivingCourseTagKey } from "~/data/diving";
import { keys } from "~/misc/helpers";

export const divingCertificateIds = ["SD", "OW", "OW_DEEP", "AOW", "AOW_DEEP", "RD", "MSD", "DM", "IS", "OTHER"] as const;

export type DivingCertificateId = (typeof divingCertificateIds)[number];

export const formatDivingCertShort = (id?: DivingCertificateId | string | null) => id && id.replace("_", "+");

type SupportedCertificates = Record<DivingCertificateId, string | null>;

export const defaultCertificateNames: Record<DivingCertificateId, string> = {
  SD: "Scuba Diver",
  OW: "Open Water Diver",
  OW_DEEP: "Open Water + Deep Diver",
  AOW: "Advanced Open Water",
  AOW_DEEP: "Advanced + Deep Diver",
  RD: "Rescue Diver",
  MSD: "Master Scuba Diver",
  DM: "Divemaster",
  IS: "Instructor",
  OTHER: "Other",
};

export const divingOrganizationszz = {
  PADI: {
    name: "PADI",
    certificates: { ...defaultCertificateNames, AOW: "Advanced Open Water" },
  },
  SSI: {
    name: "SSI",
    certificates: { ...defaultCertificateNames, AOW: "Advanced adventurer", RD: "Diver Stress & Rescue" },
  },
  SDI: {
    name: "SDI",
    certificates: { ...defaultCertificateNames, SD: null, AOW: "Advanced diver", MSD: "Master Diver" },
  },
  NAUI: {
    name: "NAUI",
    certificates: {
      SD: null,
      OW: "Scuba diver",
      OW_DEEP: null,
      AOW: "Advanced Scuba Diver",
      AOW_DEEP: null,
      RD: "Rescue Scuba Diver",
      MSD: "Master Scuba Diver",
      DM: "Divemaster",
      IS: null,
      OTHER: null,
    },
  },
  CMAS: {
    name: "CMAS",
    certificates: {
      SD: null,
      OW: "One Star Diver",
      OW_DEEP: null,
      AOW: "Two Star Diver",
      AOW_DEEP: null,
      RD: null,
      MSD: null,
      DM: "Three Star Diver",
      IS: "Instructor",
      OTHER: null,
    },
  },
  PTRD: {
    name: "PTRD",
    certificates: {
      SD: "Supervised Diver",
      OW: "Open Water Diver",
      OW_DEEP: null,
      AOW: "Advanced Diver",
      AOW_DEEP: null,
      RD: "Rescue Diver",
      MSD: null,
      DM: "Divemaster",
      IS: "Diving Instructor",
      OTHER: null,
    },
  },
  RAID: {
    name: "RAID",
    certificates: {
      SD: "Scuba Diver",
      OW: "Open Water 20",
      OW_DEEP: null,
      AOW: "Explorer 30",
      AOW_DEEP: "Advanced 35",
      RD: "Master Rescue Diver",
      MSD: null,
      DM: "Divemaster",
      IS: "Instructor",
      OTHER: null,
    },
  },
  ADIP: {
    name: "ADIP",
    certificates: {
      SD: "Diver 1*",
      OW: "Diver 2*",
      OW_DEEP: null,
      AOW: "Diver 3*",
      AOW_DEEP: null,
      RD: null,
      MSD: null,
      DM: "Diver 4*",
      IS: null,
      OTHER: null,
    },
  },
  ANMP: {
    name: "ANMP",
    certificates: {
      SD: null,
      OW: "Level 1 Diver",
      OW_DEEP: null,
      AOW: "Level 2 Diver",
      AOW_DEEP: null,
      RD: "Level 3 Diver",
      MSD: null,
      DM: "Level 4 Diver",
      IS: "Instructor",
      OTHER: null,
    },
  },
  OTHER: {
    name: "Other",
    certificates: defaultCertificateNames,
  },
  REEF_CHECK: {
    name: "Reef Check",
    certificates: null,
  },
  CUSTOM: {
    name: "CUSTOM",
    certificates: null,
  },
} satisfies Record<string, { certificates: SupportedCertificates | null; name: string }>;

export const getDivingCertificateOrganization = (key?: DivingCourseTagKey | string | null) => {
  if (!key) return null;
  // const dcoKey = key.toLowerCase();
  return { ...divingOrganizationszz[key as DivingOrganizationKey], key: key };
};

export type DivingOrganizationKey = keyof typeof divingOrganizationszz;

// export const divingCourseOrganisations = [
//   { id: "padi", name: "PADI" },
//   { id: "ssi", name: "SSI" },
//   { id: "sdi", name: "SDI" },
//   { id: "tdi", name: "TDI" },
//   { id: "raid", name: "RAID" },
//   { id: "adip", name: "ADIP" },
//   { id: "cmas", name: "CMAS" },
// ];
//
export const divingLevelsz = {
  beginner: "Beginner",
  advanced: "Advanced",
  professional: "Professional",
  youth: "Youth",
  refresher: "Refresher",
  specialties: "Specialties",
} as const;

export const divingLevelKeys = keys(divingLevelsz);

export type DivingCertificateLevelKey = keyof typeof divingLevelsz;
