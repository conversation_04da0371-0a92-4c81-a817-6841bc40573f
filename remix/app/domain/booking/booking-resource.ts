import { activeSession, memberIsAdminQb, userSessionId } from "~/domain/member/member-queries.server";
import { arrayAgg, nowValue, tstzToDate } from "~/kysely/kysely-helpers";
import { Kysely, sql } from "kysely";
import { ResourceError } from "~/utils/error";
import { updateBookingsQb } from "~/domain/pricing/booking-pricing-queries";
import { DB } from "~/kysely/db";
import { updateParticipantCache } from "~/domain/participant/participant-resource";
import { getAdminMessaging } from "~/utils/firebase.server";
import { bookingCartKey, getModeIndex } from "~/domain/booking/booking-vars";
import { getAdminLevelIndex } from "~/domain/member/member-vars";
import { removeObjectKeys } from "~/misc/helpers";
import { Args, AuthArgs, bustCacheAfter } from "~/server/resource/resource-helpers.server";
import { getCacheKey } from "~/server/cache/cache.planning.server";
import { v4 } from "uuid";
import { keys } from "../../misc/helpers";
import { activityWithaddonsTotalPriceSelect, activityWithaddonsTotalTaxSelect } from "../pricing/activity-pricing-queries";
import { getHost } from "~/misc/web-helpers";

export const updateBookingCache = async (db: Kysely<DB>, bookingId: string) => {
  await updateBookingsQb(db).where("booking.id", "=", bookingId).executeTakeFirstOrThrow();
};

export const updateActivityCache = async (db: Kysely<DB>, activityId: string) => {
  await db
    .updateTable("sale_item")
    .from("booking")
    .set((eb) => ({
      cached_total_price_amount: activityWithaddonsTotalPriceSelect,
      cached_total_tax_amount: activityWithaddonsTotalTaxSelect,
    }))
    .where("sale_item.id", "=", activityId)
    .where("booking.id", "=", (eb) => eb.ref("sale_item.booking_id"))
    .execute();
};

const allowedBooking = (args: AuthArgs) =>
  args.trx.selectFrom("booking").where((eb) => {
    const isAdmin = eb("booking.establishment_id", "in", memberIsAdminQb(args, "read").select("_member.establishment_id"));
    const isDirectBooking = eb.and([eb("booking.direct_booking", "=", true)]);
    const isCart = eb("booking.cart_for_session_id", "=", args.ctx.session_id);
    return eb.or([isAdmin, isDirectBooking, isCart]);
  });

export const saleItemResource: Args<"sale_item"> = {
  authorize: async (args) => {
    const activity = await args.trx
      .selectFrom("booking")
      .innerJoin("sale_item", "sale_item.booking_id", "booking.id")
      .where((eb) => {
        const isAdmin = eb("booking.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"));
        const allowedForCartBookingCmpr = eb("booking.cart_for_session_id", "=", args.ctx.session_id);
        if (args.action !== "insert") return eb.or([isAdmin, allowedForCartBookingCmpr]);
        const productPriceQb = eb
          .selectFrom("product")
          .where("product.id", "=", eb.ref("sale_item.product_id"))
          .innerJoin("product_price", "product_price.product_id", "product.id")
          .innerJoin("price", "price.id", "product_price.price_id")
          .whereRef("price.currency_id", "=", "booking.currency_id")
          .select("price.amount");
        const allowedForDirectBookingCmpr = eb.and([
          eb("booking.direct_booking", "=", true),
          eb("sale_item.price_pp", ">=", productPriceQb),
        ]);
        return eb.or([isAdmin, allowedForDirectBookingCmpr, allowedForCartBookingCmpr]);
      })
      .select((eb) => [
        "booking.establishment_id",
        "sale_item.booking_id",
        eb
          .selectFrom("participation")
          .where("participation.sale_item_id", "=", eb.ref("sale_item.id"))
          .select((eb) => arrayAgg(eb.ref("participation.participant_id"), "uuid").as("participant_ids"))
          .as("participant_ids"),
      ])
      .where("sale_item.booking_id", "not in", args.trx.selectFrom("invoice").select("invoice.booking_id"))
      .where("sale_item.id", "=", args.id)
      .executeTakeFirst();

    if (activity) {
      const cacheKey = updateBookingCache.name + activity.booking_id;
      args.after_mutations.insideTransaction.set(cacheKey, () => updateBookingCache(args.trx, activity.booking_id));

      bustCacheAfter(args, getCacheKey({ establishmentId: activity.establishment_id }));

      activity.participant_ids?.forEach((participantId) => {
        args.after_mutations.insideTransaction.set(updateParticipantCache.name + participantId, () =>
          updateParticipantCache(args.trx, participantId),
        );
      });
    }

    return activity;
  },
  insert: async (args) => {
    return {
      ...args.data,
      created_at: nowValue,
      created_by_user_session_id: userSessionId(args),
    };
  },
  update: (args) => {
    console.log("changed quantity", args.data.quantity, args.id);
    const parsedData = {
      ...args.data,
    };
    delete parsedData.created_at;
    delete parsedData.created_by_user_session_id;
    return { ...parsedData };
  },
  delete: () => true,
  onChanged: async (args) => {
    const changedQuantity = args.diff.diff?.quantity;
    const changedDuration = args.diff.diff?.duration;

    const diff = args.diff.diff;
    if (diff && (keys(diff).includes("quantity") || keys(diff).includes("price_pp"))) {
      args.after_mutations.insideTransaction.set(updateActivityCache.name + args.id, () => updateActivityCache(args.trx, args.id));
    }

    if (typeof changedQuantity !== "number") return true;
    // Validate that quantity is non-negative
    if (changedQuantity < 0) {
      return "Activity quantity cannot be negative";
    }

    if (!args.diff.before?.duration && !args.diff.after?.duration) {
      // no need to update participations if its not an activity
      return true;
    }

    args.after_mutations.insideTransaction.set("add_participations" + args.id, async () => {
      // Get any participants associated with this booking
      const participants = await args.trx
        .selectFrom("sale_item")
        .innerJoin("booking", "booking.id", "sale_item.booking_id")
        .innerJoin("participant", "participant.booking_id", "booking.id")
        .select("participant.id")
        .where("sale_item.id", "=", args.id)
        .execute();

      const existingParticipations = await args.trx
        .selectFrom("participation")
        .selectAll("participation")
        .where("participation.sale_item_id", "=", args.id)
        .execute();

      const nrOfParticipationsToAdd = changedQuantity - existingParticipations.length;

      if (!nrOfParticipationsToAdd) return;

      if (nrOfParticipationsToAdd < 0) {
        const unregisteredParticipations = existingParticipations.filter((p) => !p.participant_id);
        if (unregisteredParticipations.length + nrOfParticipationsToAdd < 0)
          throw new ResourceError("Can't lower to this quantity, because there are already more slots filled");
        const participationIdsToBeRemoved = unregisteredParticipations
          .filter((_, index) => nrOfParticipationsToAdd + index < 0)
          .map((participation) => participation.id);
        if (!participationIdsToBeRemoved.length) throw new ResourceError("Could not remove slots");
        await args.trx.deleteFrom("participation").where("participation.id", "in", participationIdsToBeRemoved).execute();
      }

      const participations = Array.from({ length: nrOfParticipationsToAdd }, (_, i) => ({
        sale_item_id: args.id,
        participant_id: (!existingParticipations.length && participants[i]?.id) || null,
        created_at: nowValue,
        created_by_user_session_id: userSessionId(args),
      }));

      await args.trx.insertInto("participation").values(participations).execute();
    });
    return true;
  },
};

const pushBookingNotification = async (db: Kysely<DB>, bookingId: string) => {
  const messaging = await getAdminMessaging();
  const booking = await db
    .selectFrom("booking")
    .innerJoin("establishment", "establishment.id", "booking.establishment_id")
    .innerJoin("member", "member.id", "member.establishment_id")
    .innerJoin("user", "user.id", "member.user_id")
    .where("member.admin", ">=", getAdminLevelIndex("write"))
    .select("user.id")
    .where("booking.id", "=", bookingId)
    .executeTakeFirstOrThrow();
  await messaging.sendEach([{ token: "sdf", notification: { title: "New booking", body: "New booking" } }]);
};

export const bookingResource: Args<"booking"> = {
  authorize: (args) => allowedBooking(args).where("booking.id", "=", args.id).executeTakeFirst(),
  beforeMutate: async (args) => {
    const isCart = args.data?.cart_for_session_id === bookingCartKey;
    const establishmentId = args.data?.establishment_id;

    const existing =
      isCart && establishmentId
        ? await args.trx
            .selectFrom("booking")
            .where("booking.cart_for_session_id", "=", args.ctx.session_id)
            .where("booking.establishment_id", "=", establishmentId)
            .select("booking.id")
            .executeTakeFirst()
        : null;
    console.log("bookingid", args.id, args.operation);

    return {
      id: args.id || existing?.id || (args.operation === "insert" ? v4() : ""),
      operation: existing ? "update" : args.operation,
      data: args.data,
    };
  },
  insert: async (args) => {
    const establishmentId = args.data.establishment_id;
    const directBooking = args.data.direct_booking;

    const needsReview =
      directBooking &&
      (await args.trx
        .selectFrom("establishment")
        .select("establishment.direct_booking_mode")
        .where("establishment.direct_booking_mode", "<", getModeIndex("Open"))
        .where("establishment.id", "=", establishmentId)
        .executeTakeFirst());

    return removeObjectKeys(
      {
        ...args.data,
        accepted: !needsReview || null,
        host: getHost(args.request),
        reviewed_at: null,
        reviewed_by: null,
        created_at: nowValue,
        cart_for_session_id:
          args.data.cart_for_session_id === bookingCartKey ? activeSession(args.trx, args.ctx.session_id).select("_session.id") : null,
        created_by_user_session_id: userSessionId(args),
      },
      "cancelled_at",
      "cancelled_by_user_session_id",
    );
  },
  update: (args) => {
    const parsedData = {
      ...args.data,
    };
    delete parsedData.establishment_id;
    delete parsedData.created_at;
    delete parsedData.created_by_user_session_id;
    // delete parsedData.cart_for_session_id;
    const cancelledAt = args.data.cancelled_at;
    return {
      ...parsedData,
      cart_for_session_id: parsedData?.cart_for_session_id === null ? null : undefined,
      // invoice_generated_at: allowedInvoiceGeneratedValue.includes(args.data.invoice_generated_at)
      //   ? args.data.invoice_generated_at
      //   : undefined,
      ...(cancelledAt || cancelledAt === null
        ? {
            cancelled_at: cancelledAt && nowValue,
            cancelled_by_user_session_id: userSessionId(args),
          }
        : {
            cancelled_at: undefined,
            cancelled_by_user_session_id: undefined,
          }),
    };
  },
  onChanged: async (args) => {
    const establishmentId = args.diff.before?.establishment_id || args.diff.after?.establishment_id;
    if (args.diff.diff?.vat_rate) {
      const activities = await args.trx
        .selectFrom("sale_item")
        .where("sale_item.booking_id", "=", args.id)
        .select("sale_item.id")
        .execute();
      activities.forEach((activity) => {
        args.after_mutations.insideTransaction.set(updateActivityCache.name + activity.id, () =>
          updateActivityCache(args.trx, activity.id),
        );
      });
    }
    if (establishmentId) {
      bustCacheAfter(args, getCacheKey({ establishmentId: establishmentId }));
    }
    return true;
  },
  delete: () => true,
  // onChanged: async (args) => {
  //   if (args.diff.diff?.accepted) {
  //     args.after_mutations.outsideTransaction.set(pushBookingNotification.name + args.id, () => pushBookingNotification(kysely, args.id));
  //   }
  // },
};

export const invoiceResource: Args<"invoice"> = {
  authorize: (args) =>
    args.trx
      .selectFrom("invoice")
      .innerJoin("booking", "invoice.booking_id", "booking.id")
      .select("invoice.id")
      .where("booking.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"))
      .where("invoice.id", "=", args.id)
      .executeTakeFirst(),
  insert: async (args) => {
    const bookingId = args.data.booking_id;
    if (typeof bookingId !== "string") throw new ResourceError("booking id required for invoice");
    const bookingQb = args.trx.selectFrom("booking").where("booking.id", "=", bookingId);
    const invoiceCount = await args.trx
      .insertInto("invoice_count")
      .values({
        count: 1,
        establishment_id: bookingQb.select("booking.establishment_id"),
        year: sql<number>`EXTRACT(YEAR FROM NOW())`,
        // year: bookingQb.innerJoin("establishment", 'establishment.id', 'booking.establishment_id'),
      })
      .onConflict((oc) =>
        oc.columns(["establishment_id", "year"]).doUpdateSet((eb) => ({
          count: sql`(${eb.ref("invoice_count.count")} + 1)`,
        })),
      )
      .returningAll()
      .executeTakeFirstOrThrow();

    const invoiceCountPadded = invoiceCount.count.toString().padStart(5, "0");
    const invoiceNr = `INV-${invoiceCount.year}${invoiceCountPadded}`;

    return {
      ...args.data,
      invoice_nr: invoiceNr,
      created_at: nowValue,
      invoice_local_date: tstzToDate(
        nowValue,
        bookingQb
          .innerJoin("establishment", "establishment.id", "booking.establishment_id")
          .innerJoin("spot", "spot.id", "establishment.spot_id")
          .innerJoin("region", "region.id", "spot.region_id")
          .select("region.timezone") as any,
      ),
    };
  },
  update: () => false,
  delete: () => true,
};

export const activityAddonResource: Args<"activity_addon"> = {
  authorize: async (args) => {
    const activityAddon = await args.trx
      .selectFrom("activity_addon")
      .innerJoin("sale_item", "sale_item.id", "activity_addon.sale_item_id")
      .innerJoin("booking", "booking.id", "sale_item.booking_id")
      .where((eb) => {
        const isAdmin = eb("booking.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"));
        const isDirectBooking = eb("booking.direct_booking", "=", true);
        if (args.action === "insert") return eb.or([isAdmin, isDirectBooking]);
        return isAdmin;
      })
      .select(["activity_addon.id", "sale_item.booking_id", "activity_addon.sale_item_id"])
      .executeTakeFirst();

    if (activityAddon) {
      const cacheKey = updateBookingCache.name + activityAddon.booking_id;
      args.after_mutations.insideTransaction.set(updateActivityCache.name + activityAddon.sale_item_id, () =>
        updateActivityCache(args.trx, activityAddon.sale_item_id),
      );
      args.after_mutations.insideTransaction.set(cacheKey, async () => {
        await updateBookingCache(args.trx, activityAddon.booking_id)
      });
    }

    return activityAddon;
  },
  insert: (args) => args.data,
  update: (args) => args.data,
  delete: () => true,
};
