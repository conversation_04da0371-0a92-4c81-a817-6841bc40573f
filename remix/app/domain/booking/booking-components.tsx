import React, { Fragment, ReactNode, useState } from "react";
import { getMeetingType, meetingTypes } from "~/domain/planning/plannings-consts";
import { RInput, RLabel, RSelect, toInputId } from "~/components/ResourceInputs";
import { fName, keys } from "~/misc/helpers";
import { AddressInput, baseAutocompleteOptions } from "~/components/base/AddressInput";
import { AdvancedBooking } from "~/domain/booking/booking-queries";
import { LockClosedIcon } from "@heroicons/react/20/solid";
import { flat, unique } from "remeda";
import { formatPrice } from "~/utils/money";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { Jsonify } from "@remix-run/server-runtime/dist/jsonify";
import { twMerge } from "tailwind-merge";
import { defaultCountryCode } from "~/misc/vars";

import { paymentsStates, PaymentState } from "~/domain/booking/booking-payment";
import { useAppContext } from "~/hooks/use-app-context";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _booking_detail } from "~/misc/paths";

export const MeetingInput = (props: {
  default?: {
    meeting_time?: string | null;
    meeting_type?: string | null;
    meeting_address?: string | null;
    participant_stays?: string[] | null;
  } | null;
  country_code: string | null;
  operator_address?: string;
}) => {
  const defaultMeetingTypeKey = props.default?.meeting_type || ("DIVE_CENTER" satisfies keyof typeof meetingTypes);
  const [meetingTypeKey, setMeetingTypeKey] = useState(defaultMeetingTypeKey);
  const meetingAddress = defaultMeetingTypeKey === meetingTypeKey ? props.default?.meeting_address : undefined;
  const selectedMeetingType = getMeetingType(meetingTypeKey);
  const participantStays = unique(props.default?.participant_stays?.filter((stay) => !!stay) || []);
  const [showSelect, setShowSelect] = useState(participantStays.includes(props.default?.meeting_address || ""));

  if (!selectedMeetingType) return <div>Invalid meeting type {meetingTypeKey}</div>;

  const isPickupAndStaysAvailable = selectedMeetingType.key === "PICKUP" && !!participantStays.length;

  return (
    <Fragment>
      <div>
        <RLabel table={"booking"} field={"data.meeting_type"}>
          Meeting Location
        </RLabel>
        <br />
        <RSelect
          table={"booking"}
          field={"data.meeting_type"}
          className="select w-full"
          value={selectedMeetingType.key}
          onChange={(e) => {
            const meetingType = getMeetingType(e.target.value);
            if (meetingType) {
              setMeetingTypeKey(meetingType.key);
              setShowSelect(meetingType.key === "PICKUP");
            }
          }}
        >
          {keys(meetingTypes).map((meetingTypeKey) => (
            <option key={meetingTypeKey} value={meetingTypeKey}>
              {meetingTypes[meetingTypeKey].option_label}
            </option>
          ))}
        </RSelect>
      </div>
      <div className="flexl flex-row md:col-span-2">
        <div>
          <div className="flex flex-wrap gap-3">
            <div>
              <RLabel table={"booking"} field={"data.meeting_address"}>
                {(selectedMeetingType?.label || "") + " Address"}
              </RLabel>
            </div>
            {isPickupAndStaysAvailable && (
              <button type={"button"} onClick={() => setShowSelect(!showSelect)} className="link">
                {showSelect ? "Enter" : "Select from registration(s)"}
              </button>
            )}
          </div>
          {isPickupAndStaysAvailable && showSelect ? (
            <RSelect
              table={"booking"}
              field={"data.meeting_address"}
              defaultValue={props.default?.meeting_address || ""}
              className="select w-full"
            >
              {participantStays.map((stay) => (
                <option key={stay} value={stay}>
                  {stay}
                </option>
              ))}
            </RSelect>
          ) : (
            <AddressInput
              key={meetingTypeKey}
              id={toInputId(fName("booking", "data.meeting_address"))}
              name={fName("booking", "data.meeting_address")}
              defaultValue={meetingAddress || ""}
              className="input"
              placeholder={selectedMeetingType.key === "DIVE_CENTER" ? props.operator_address : ""}
              autocompleteOptions={{
                ...baseAutocompleteOptions,
                componentRestrictions: { country: props.country_code || defaultCountryCode },
              }}
            />
          )}
        </div>
      </div>
      <div>
        <RInput
          table={"booking"}
          required
          field={"data.meeting_time"}
          type={"time"}
          label={"Meeting time"}
          className="input"
          defaultValue={props.default?.meeting_time?.slice(0, 5) || ""}
        />
      </div>
    </Fragment>
  );
};

export const getRegistrationStatus = (booking: Jsonify<AdvancedBooking>) => {
  const activities = booking.activities || [];
  const participatons = flat(activities.map((activity) => activity.participations));
  const registredParticipations = participatons.filter((participation) => participation.participant_id);
  const incompleteParticipants =
    booking.participants?.filter(
      (participant) =>
        participant.cached_incomplete_fields?.length ||
        participant.cached_signature_waivers_valid === false ||
        participant.cached_read_waivers_valid === false,
    ) || [];

  if (booking.cancelled_at) return "Cancelled";
  if (participatons.length === registredParticipations.length && !incompleteParticipants.length) return "Registered";
  if (registredParticipations.length > 0) return "Pending";
  return "None";
};

type BookingRegistrationStatus = ReturnType<typeof getRegistrationStatus>;

const BookingRegistrationBadge = (props: { status: BookingRegistrationStatus }) => {
  return (
    <p
      className={twMerge(
        "whitespace-nowrap text-center text-xs px-3 py-0.5 rounded-sm",
        props.status === "None" && "text-white bg-red-500",
        props.status === "Cancelled" && "text-slate-200 bg-slate-600",
        props.status === "Pending" && "bg-orange-200",
        props.status === "Registered" && "text-white bg-green-500",
      )}
    >
      {props.status}
    </p>
  );
};

export const getPaymentState = (props: { cached_final_amount: number | null; cached_final_paid: number | null }) => {
  const priceTotalFinal = props.cached_final_amount || 0;
  const paidAmountRaw = props.cached_final_paid || 0;
  // return (
  //   paidAmountRaw ? (paidAmountRaw < priceTotalFinal ? "deposit" : paidAmountRaw > priceTotalFinal ? "overpaid" : "paid") : "open"
  // ) satisfies PaymentState;
  return (
    paidAmountRaw === priceTotalFinal ? "paid" : paidAmountRaw > priceTotalFinal ? "overpaid" : !paidAmountRaw ? "open" : "deposit"
  ) satisfies PaymentState;
};

export const BookingPaymentBadge = (props: { status: PaymentState | null; className?: string; children?: ReactNode }) => {
  const status = props.status;
  return (
    <p
      className={twMerge(
        "whitespace-nowrap text-xs px-3 py-0.5 text-white group-aria-selected:outline-offset-2 rounded-sm bg-red-500 text-center  outline outline-1 outline-[#E90B0B]",
        status === "paid" && "bg-[#4BB97C]  outline-[#46AA73]",
        status === "deposit" && "bg-[#FED7AA] outline-[#EACFB0] text-black",
        status === "overpaid" && "bg-[#8BB7C8] outline-[#8BB7C8] text-white",
        status === null && "bg-slate-800 text-white outline-black",
        props.className,
      )}
    >
      {props.children || (status ? paymentsStates[status] : "All")}
    </p>
  );
};
export const BookingItem = (props: { booking: Jsonify<AdvancedBooking> & { created_by?: string | null } }) => {
  // const booking = mapBookingTotals(booking);
  const booking = props.booking;
  const search = useSearchParams2();
  const multipleDays = (booking.activity_agg?.duration_in_days || 0) > 1;
  const activities = booking.activities || [];
  const participatons = flat(activities.map((activity) => activity.participations));
  const registredParticipations = participatons.filter((participation) => participation.participant_id);
  const incompleteParticipants =
    booking.participants?.filter(
      (participant) =>
        participant.cached_incomplete_fields?.length ||
        participant.cached_signature_waivers_valid === false ||
        participant.cached_read_waivers_valid === false,
    ) || [];
  const registeredParticipantIds = unique(registredParticipations.map((participation) => participation.participant_id));
  const openslots = participatons.filter((participation) => !participation.participant_id);

  return (
    <Fragment>
      <div className={twMerge("bg-white p-2 col-span-3 text-slate-500", booking.direct_booking && "bg-slate-700 text-white")}>
        <div className="flex items-center ">
          <p className="font-semibold">{booking.booking_reference || booking.sqid}</p>
          <div className="flex-1" />
          {booking.invoice_id && <LockClosedIcon className="w-5 h-5" />}
          <p className="font-semibold">{booking.sqid}</p>
        </div>
        <p className="text-xs">
          Book date {booking.created_at_formatted}
          {props.booking.created_by && ` by ${props.booking.created_by}`}
        </p>
      </div>
      <div className="bg-secondary-50 p-2 grid grid-cols-subgrid col-span-3">
        <div>Amount</div>
        <div className="font-semibold">
          {formatPrice(booking.cached_final_amount || 0, booking.currency_id, booking.establishment_locale)}
        </div>
        <div className="pr-2 text-right">
          <BookingPaymentBadge status={getPaymentState(booking)} />
        </div>
        <div>Pax</div>
        <div>
          <span className="font-semibold">{registeredParticipantIds.length + ""} </span>
          {!!openslots.length && (
            <span className="text-slate-500">
              ({openslots.length} open slot{openslots.length !== 1 && "s"})
            </span>
          )}
        </div>
        <div className="pr-2 text-right">
          <BookingRegistrationBadge status={getRegistrationStatus(booking)} />
        </div>
        <div className="whitespace-nowrap">Activity date{multipleDays && "s"}</div>
        <div className="font-semibold col-span-2 flex-row gap-2 flex ">
          {unique(activities.map((activity) => activity.duration_formatted)).join(", ")}
          {/*{booking.activity_agg ? formatDuration(booking.activity_agg) : ""}*/}
        </div>
      </div>
    </Fragment>
  );
};
export const ConnectToBookingBanner = () => {
  const ctx = useAppContext();

  const booking = ctx.establishment?.booking;

  if (!booking) return <Fragment />;
  return (
    <p className="px-3 py-4 rounded-md bg-secondary-50 text-slate-700 flex flex-row justify-between">
      Connect to booking/order {booking.booking_reference || booking.sqid}
      <ParamLink path={_booking_detail(booking.id)} className="">
        Cancel
      </ParamLink>
    </p>
  );
};
