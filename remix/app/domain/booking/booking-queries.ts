import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { Infer<PERSON><PERSON><PERSON>, Kysely, sql } from "kysely";
import type { DB } from "~/kysely/db";
import { kysely } from "~/misc/database.server";
import { Group } from "~/domain/booking/booking-vars";
import { dateRange, formatDate, formatDatetime, formatRange, lower, nowValue, sqid } from "~/kysely/kysely-helpers";
import { baseProductWithSelect } from "~/domain/product/product-queries.server";
import { at_infinity_value } from "~/kysely/db-static-vars";

export const bookingQb = (db: Kysely<DB>) =>
  db
    .selectFrom("booking")
    .selectAll("booking")
    .select((eb) => {
      return [
        sqid(eb.ref("booking.id_seq")).as("sqid"),
        jsonArrayFrom(
          eb
            .selectFrom("payment")
            .whereRef("payment.booking_id", "=", "booking.id")
            .where("payment.deleted_at", "=", at_infinity_value)
            .selectAll("payment"),
        ).as("payments"),
      ];
    });

const totalActivityDurationQb = kysely
  .selectFrom("sale_item")
  .where("sale_item.duration", "is not", null)
  .select((eb) => {
    const lowerQb = eb.fn<string | null>("min", [eb.fn("lower", [eb.ref("sale_item.duration")])]);
    const upperQb = sql<string | null>`(max ( upper (${eb.ref("sale_item.duration")})) - 1)`;
    return [
      lowerQb.as("duration_start"),
      upperQb.as("duration_end"),
      sql<number | null>`(${upperQb} - ${lowerQb})`.as("duration_in_days"),
      formatRange(eb, lowerQb, upperQb).as("duration_formatted"),
    ];
  });

export const bookingQbWithTotalDuration = kysely
  .selectFrom("booking")
  .selectAll("booking")
  .innerJoin("establishment", "establishment.id", "booking.establishment_id")
  .leftJoin("spot", "spot.id", "establishment.spot_id")
  .leftJoin("region", "region.id", "spot.region_id")
  .select((eb) => [
    sqid(eb.ref("booking.id_seq")).as("sqid"),
    formatDatetime(eb.ref("booking.created_at"), "YYYY-MM-DD", eb.ref("region.timezone")).as("created_at_formatted_iso"),
    jsonObjectFrom(totalActivityDurationQb.where("sale_item.booking_id", "=", eb.ref("booking.id"))).as("activity_agg"),
  ]);

export const advancedBookingQb = bookingQbWithTotalDuration.select((eb) => {
  const activitiesQb = eb.selectFrom("sale_item").select("sale_item.duration").whereRef("sale_item.booking_id", "=", "booking.id");
  const todayRef = sql<string>`(${formatDatetime(nowValue, "YYYY-MM-DD", eb.ref("region.timezone"))}:: date)`;
  const todayRange = dateRange(todayRef, todayRef);
  return [
    "establishment.locale as establishment_locale",
    formatDatetime(eb.ref("booking.created_at"), "DD Mon ''YY", eb.ref("region.timezone")).as("created_at_formatted"),
    eb.selectFrom("invoice").select("invoice.id").where("invoice.booking_id", "=", eb.ref("booking.id")).limit(1).as("invoice_id"),
    jsonArrayFrom(
      eb
        .selectFrom("participant")
        .innerJoin("participation", "participation.participant_id", "participant.id")
        .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
        .where("sale_item.booking_id", "=", eb.ref("booking.id"))
        .select([
          "participant.cached_incomplete_fields",
          "participant.cached_read_waivers_valid",
          "participant.cached_signature_waivers_valid",
        ]),
    ).as("participants"),
    jsonArrayFrom(
      eb
        .selectFrom("sale_item")
        .select((eb) => {
          const lowerQb = lower(eb.ref("sale_item.duration"));
          const upperQb = sql<string>`(upper (${eb.ref("sale_item.duration")}) - interval '1 days')`;
          return [
            formatRange(eb, lowerQb, upperQb).as("duration_formatted"),
            formatDate(lowerQb, "YYYY.MM.DD").as("date_start_formatted"),
            formatDate(upperQb, "YYYY.MM.DD").as("date_end_formatted"),
            formatRange(eb, lowerQb, upperQb).as("duration_formatted_for_export"),
            "sale_item.description",
            jsonObjectFrom(baseProductWithSelect.where("product.id", "=", eb.ref("sale_item.product_id"))).as("product"),
            jsonArrayFrom(
              eb.selectFrom("participation").where("participation.sale_item_id", "=", eb.ref("sale_item.id")).selectAll("participation"),
            ).as("participations"),
          ];
        })
        .where("sale_item.duration", "is not", null)
        .where("sale_item.booking_id", "=", eb.ref("booking.id")),
    ).as("activities"),
    eb
      .case()
      .when(eb("booking.cancelled_at", "is not", null))
      .then("Cancelled" satisfies Group)
      .when(eb.exists(activitiesQb.where("sale_item.duration", "&&", todayRange)))
      .then("Ongoing" satisfies Group)
      .when(eb.exists(activitiesQb.where("sale_item.duration", "<", todayRange)))
      .then("Completed" satisfies Group)
      .else("Upcoming" satisfies Group)
      .end()
      .as("status"),
  ];
});

export type AdvancedBooking = InferResult<typeof advancedBookingQb>[number];
