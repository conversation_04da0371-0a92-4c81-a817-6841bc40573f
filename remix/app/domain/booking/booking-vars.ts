export const groups = ["Upcoming", "Ongoing", "Completed", "Cancelled"] as const;
export type Group = (typeof groups)[number];

export const tabValue = (establishmentId: string, group: Group) => establishmentId + group;

export const directBookingModes = ["Disabled", "Review", "Open"] as const;
export type DirectBookingMode = (typeof directBookingModes)[number];
export const getModeIndex = (mode: DirectBookingMode) => directBookingModes.indexOf(mode);

export const bookingCartKey = "cart";
