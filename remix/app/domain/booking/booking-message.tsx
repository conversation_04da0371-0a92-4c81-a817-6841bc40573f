import React, { useState } from "react";
import { v4 } from "uuid";
import { AnimatingDiv } from "~/components/base/base";
import { InteractiveButton, SubmitButton } from "~/components/base/Button";
import { arraySwap } from "@dnd-kit/sortable";
import { ChevronUpIcon, PlusIcon, XMarkIcon } from "@heroicons/react/20/solid";
import { twMerge } from "tailwind-merge";
import { fName } from "~/misc/helpers";
import { CDialog } from "~/components/base/Dialog";
import { ActionForm } from "~/components/form/BaseFrom";
import { HiddenTypeInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { RInput, RLabel, RTextarea, toInputId } from "~/components/ResourceInputs";
import { ParamLink } from "~/components/meta/CustomComponents";
import { UndoIcon } from "lucide-react";

export const InnerMessageForm = (props: { templates: string[] }) => {
  const [templates, setTemplates] = useState<[string, string][]>(() => {
    const initTemplates = props.templates.map((tmp) => [v4(), tmp] satisfies [string, string]);
    return initTemplates.length > 0 ? initTemplates : [[v4(), ""] satisfies [string, string]];
  });
  return (
    <AnimatingDiv className="space-y-3">
      <HiddenTypeInput name={fName("establishment", "data.booking_message_templates")} value={"__empty_array__"} />
      {templates.map(([tmplId, tmpl], index) => (
        <div key={tmplId}>
          <div className="flex gap-1 items-center">
            {[-1, 1].map((direction) => {
              const finalIndex = index + direction;
              const enabled = finalIndex >= 0 && finalIndex < templates.length;
              return (
                <InteractiveButton
                  onClick={() => {
                    const reorderedTemplates = arraySwap(templates, index, finalIndex);
                    setTemplates(reorderedTemplates);
                  }}
                  disabled={!enabled}
                  className="px-1 disabled:text-slate-300 text-slate-700"
                >
                  <ChevronUpIcon className={twMerge("w-5 h-5", direction > 0 && "rotate-180")} />
                </InteractiveButton>
              );
            })}
            <InteractiveButton
              onClick={() => {
                const updatedTemplates = templates.filter((_, tmplIndex) => tmplIndex !== index);
                setTemplates(updatedTemplates);
              }}
              className="px-1 text-red-500"
            >
              <XMarkIcon className="w-5 h-5 " />
            </InteractiveButton>
          </div>
          <textarea
            name={fName("establishment", "data.booking_message_templates", 0, index)}
            value={tmpl}
            className="input min-h-28 resize-none"
            placeholder={`For example: Don't forget to bring a towel`}
            onChange={(e) => {
              const copyTemplates = [...templates];
              copyTemplates[index] = [tmplId, e.target.value];
              setTemplates(copyTemplates);
              e.target.style.height = "auto";
              e.target.style.height = `${e.target.scrollHeight}px`;
            }}
            ref={(el) => {
              if (el) {
                el.style.height = "auto";
                el.style.height = `${el.scrollHeight}px`;
              }
            }}
          />
        </div>
      ))}
      <button
        type={"button"}
        onClick={() => setTemplates([...(templates || []), [v4(), ""]])}
        className="inline-flex items-center  text-primary hover:underline"
      >
        add
        <PlusIcon className="w-5 h-5" />
      </button>
    </AnimatingDiv>
  );
};

export const BookingMessage = (props: {
  establishment: { id: string; booking_message_templates?: string[] | null };
  booking?: { message?: string | null } | null;
}) => {
  const [lastChange, setLastChange] = useState<null | string>(null);
  const templates = props.establishment.booking_message_templates?.filter((tmp): tmp is string => !!tmp?.trim()) || [];
  return (
    <div>
      <CDialog dialogname={"content"} className="w-full max-w-screen-md">
        <ActionForm className="space-y-3" preventScrollReset replace>
          <RedirectParamsInput path={"./"} paramState={{ toggle_modal: undefined }} />
          {/*<AfterSuccessComp onAfterSuccess={() => setTemplates(null)} />*/}
          <RInput table={"establishment"} field={"id"} value={props.establishment.id} />
          <p className="font-semibold">Message templates</p>
          <InnerMessageForm templates={templates} />
          <div className="justify-end flex flex-row gap-3">
            <ParamLink paramState={{ toggle_modal: undefined }} className="btn">
              Cancel
            </ParamLink>
            <SubmitButton className="btn btn-primary">Submit</SubmitButton>
          </div>
        </ActionForm>
      </CDialog>
      <div className="flex flex-row gap-2 items-center">
        <RLabel table={"booking"} field={"data.message"}>
          Message
        </RLabel>
        {templates.map((tmp, index) => (
          <InteractiveButton
            onClick={(e) => {
              e.preventDefault();
              const textArea = document.getElementById(toInputId(fName("booking", "data.message")));
              console.log("getting here?", textArea instanceof HTMLTextAreaElement, textArea);
              if (textArea instanceof HTMLTextAreaElement) {
                setLastChange(textArea.value);
                textArea.setRangeText(tmp, 0, textArea.value.length, "end");
                textArea.dispatchEvent(new Event("input", { bubbles: true }));
              }
            }}
            key={index}
            className="px-1 link disabled:opacity-50"
          >
            {index + 1}
          </InteractiveButton>
        ))}
        <InteractiveButton
          onClick={() => {
            const textArea = document.getElementById(toInputId(fName("booking", "data.message")));
            if (textArea instanceof HTMLTextAreaElement) {
              setLastChange(textArea.value);
              textArea.value = "";
            }
          }}
          className="px-1 link disabled:opacity-50"
        >
          Clear
        </InteractiveButton>
        {lastChange && (
          <InteractiveButton
            className="text-primary"
            onClick={(e) => {
              const textArea = document.getElementById(toInputId(fName("booking", "data.message")));
              if (textArea instanceof HTMLTextAreaElement) {
                textArea.value = lastChange;
                setLastChange(null);
              }
            }}
          >
            <UndoIcon className="w-5 h-5" />
          </InteractiveButton>
        )}
        <ParamLink className="text-primary disabled:text-slate-400" paramState={{ toggle_modal: "content" }}>
          Templates
        </ParamLink>
      </div>
      <RTextarea
        defaultValue={(props.booking ? props.booking.message : templates[0]) || ""}
        table={"booking"}
        field={"data.message"}
        className="input resize-none"
        rows={3}
        placeholder="Type an optional message here"
        onChange={(e) => {
          e.target.style.height = "auto";
          e.target.style.height = `${e.target.scrollHeight}px`;
        }}
        myref={(el) => {
          if (el) {
            el.style.height = "auto";
            el.style.height = `${el.scrollHeight}px`;
          }
        }}
      />
    </div>
  );
};
