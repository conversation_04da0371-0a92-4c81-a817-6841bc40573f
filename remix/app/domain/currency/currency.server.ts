import { z } from "zod";
import type { Kysely } from "kysely";
import type { DB } from "~/kysely/db";

const apiBaseUrl = "https://v6.exchangerate-api.com/v6";
const apiKey = "************************";
const exchangeRateApiResponseParser = z.object({
  result: z.literal("success"),
  base_code: z.string(),
  conversion_rates: z.record(z.number()),
});
export const updateCurrencies = async (db: Kysely<DB>) => {
  const apiPath = `latest/usd`;
  const apiUrl = apiBaseUrl + "/" + apiKey + "/" + apiPath;

  const response = await fetch(apiUrl);
  const body = await response.json();
  const result = exchangeRateApiResponseParser.parse(body);
  const inserts = Object.entries(result.conversion_rates).map(([code, rate]) => ({
    id: code,
    conversion_rate_usd: rate,
    decimals: 2,
  }));

  return db
    .insertInto("currency")
    .values(inserts)
    .onConflict((oc) => oc.column("id").doUpdateSet((eb) => ({ conversion_rate_usd: eb.ref("excluded.conversion_rate_usd") })))
    .returningAll()
    .execute();
};
