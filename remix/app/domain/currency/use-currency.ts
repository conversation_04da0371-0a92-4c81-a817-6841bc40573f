import { useAppContext } from "~/hooks/use-app-context";
import { usePageOverwrites } from "~/utils/remix";
import { useMemo } from "react";

export const useCurrency = () => {
  const ctx = useAppContext();
  const pageOverwrite = usePageOverwrites();
  return useMemo(() => {
    return {
      finalSelected: ctx.currency.switched ? ctx.currency.user : pageOverwrite.base_currency,
    };
  }, [ctx, pageOverwrite]);
};
