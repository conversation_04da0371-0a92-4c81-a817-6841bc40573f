import React, { Fragment } from "react";
import { RInput } from "~/components/ResourceInputs";
import { Backbutton } from "~/components/base/base";
import { SubmitButton } from "~/components/base/Button";
import { Selectable } from "kysely";
import { Boat } from "~/kysely/db";
import { ParamLink } from "~/components/meta/CustomComponents";
import { useSearchParams2 } from "~/hooks/use-search-params2";

export const SharedBoatFields = (props: { boat?: Selectable<Boat> | null }) => {
  const search = useSearchParams2();
  const boat = props.boat;

  return (
    <Fragment>
      <div className="space-y-3">
        <div>
          <RInput table={"boat"} field={"data.name"} label={"Name"} defaultValue={boat?.name} required className="input" type="text" />
        </div>
        <div>
          <RInput
            defaultValue={boat?.capacity}
            required
            label="Total capacity"
            type="number"
            table={"boat"}
            field={"data.capacity"}
            className="input"
          />
        </div>
        <div>
          <RInput defaultValue={boat?.location || ""} label="Location" table={"boat"} field={"data.location"} className="input" />
        </div>
        <div>
          <label>
            Display boat name instead of trip location in calendar &nbsp;
            <RInput
              table={"boat"}
              field={"data.calendar_display_boat_name"}
              hiddenType={"__boolean__"}
              type={"checkbox"}
              className="checkbox"
              defaultChecked={boat?.calendar_display_boat_name}
            />{" "}
            &nbsp;
          </label>
          <br />
          {/*<span className="text-xs ">by default the trip location is displayed</span>*/}
        </div>
        <div className="flex flex-wrap justify-end items-end gap-3 py-1">
          {search.state.toggle_modal ? (
            <ParamLink replace paramState={{ toggle_modal: undefined }} className="btn hover:underline">
              Cancel
            </ParamLink>
          ) : (
            <Backbutton className="btn hover:underline">Cancel</Backbutton>
          )}
          <SubmitButton type="submit" className="btn btn-primary">
            {boat ? "Save" : "Create"}
          </SubmitButton>
        </div>
      </div>
    </Fragment>
  );
};
