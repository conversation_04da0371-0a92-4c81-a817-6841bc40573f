import type { DB } from "~/kysely/db";
import type { Kysely, Transaction } from "kysely";
import { sql } from "kysely";
import type { Operation } from "~/components/form/DefaultInput";
import { ResourceError } from "~/utils/error";
import { nowValue } from "~/kysely/kysely-helpers";

export interface SimpleEntityAction<T extends keyof DB> {
  entity_id: string;
  entity_name: T;
  operation: Operation;
  // disableAudit?: boolean;
  data: Partial<Record<keyof DB[T], any>> | null;
}

const createDataDiff = (existing: Record<string, any>, changes: Record<string, any>) => {
  return Object.entries(changes).reduce((acc, [key, value]) => {
    const existingValue = existing[key];
    const comparableExistingValue = typeof existingValue === "object" ? JSON.stringify(existingValue) : existingValue;
    const comparableNewValue = typeof value === "object" ? JSON.stringify(value) : value;
    if (comparableExistingValue !== comparableNewValue) return { ...acc, [key]: value };
    return acc;
  }, {});
};

const forbiddenInputFields = ["id", "created_at"] as const;

export const executeActionAndCreateAuditInput = async <T extends keyof DB>(
  trx: Kysely<DB>,
  action: SimpleEntityAction<T>,
): Promise<{ auditInput: Object; diff: Object | null; before: Object | null; after: Object | null } | null> => {
  const entityName = action.entity_name as any;

  if (action.data) {
    forbiddenInputFields.forEach((field) => {
      if ((action.data as any)[field]) {
        delete (action.data as any)[field];
        console.warn(`action.data should not have ${field} as input value. deleted ${field} property from data`);
      }
    });
  }

  if (action.operation === "insert") {
    const newItem = await trx
      .insertInto(entityName)
      .values({ id: action.entity_id, ...action.data })
      .returning(sql<any>`(to_jsonb(${sql.table(entityName)}.*))`.as("json"))
      .executeTakeFirstOrThrow();
    return { auditInput: newItem.json, diff: newItem.json, before: null, after: newItem.json };
  }

  const existingItem = await trx
    .selectFrom(entityName)
    .where("id", "=", action.entity_id)
    .select(sql<any>`(to_jsonb(${sql.table(entityName)}.*))`.as("json"))
    .executeTakeFirstOrThrow();

  if (action.operation === "update") {
    const updatedItem = await trx
      .updateTable(entityName)
      .set(action.data || {})
      .where("id", "=", action.entity_id)
      .returning(sql<any>`(to_jsonb(${sql.table(entityName)}.*))`.as("json"))
      .executeTakeFirstOrThrow();
    const dataDiff = createDataDiff(existingItem.json, updatedItem.json);
    if (Object.keys(dataDiff).length === 0) return null;
    return { auditInput: dataDiff, diff: dataDiff, before: existingItem.json, after: updatedItem.json };
  }

  if (action.operation === "delete") {
    await trx
      .deleteFrom(entityName as any)
      .where("id", "=", action.entity_id)
      .executeTakeFirstOrThrow();
    return { auditInput: existingItem.json, diff: null, before: existingItem.json, after: null };
  }

  return null;
};

export const executeAction = async <T extends keyof DB>(trx: Transaction<DB>, action: SimpleEntityAction<T>) => {
  const auditChanges = await executeActionAndCreateAuditInput<T>(trx, action);
  if (!auditChanges) throw new ResourceError("could not create audit input");
  const event = await trx.insertInto("user_event").values({ created_at: nowValue }).returning("user_event.id").executeTakeFirstOrThrow();

  const entityAction = await trx
    .insertInto("entity_action")
    .values({
      data: auditChanges.auditInput,
      entity_name: action.entity_name,
      entity_id: action.entity_id,
      action_name: action.operation,
      user_event_id: event.id,
    })
    .returningAll()
    .executeTakeFirstOrThrow();

  return entityAction;
};
