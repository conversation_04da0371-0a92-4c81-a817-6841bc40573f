import {
  activeUserSessionQb,
  isEditor<PERSON><PERSON>,
  memberIsAdminOrOwnerQb,
  memberIsAdminQb,
  userSessionId
} from "~/domain/member/member-queries.server";
import { nowValue } from "~/kysely/kysely-helpers";
import { z } from "zod";
import { ResourceError } from "~/utils/error";
import { createInvoice, getInvoice } from "~/domain/payment/xendit-client.server";
import { actionValues } from "~/misc/vars";
import { Kysely } from "kysely";
import { DB } from "~/kysely/db";
import { v4 } from "uuid";
import { at_infinity_value, at_now_value } from "~/kysely/db-static-vars";
import { getFullUrl, removeObjectKeys, urlFromSegments } from "~/misc/helpers";
import { updateBookingCache } from "~/domain/booking/booking-resource";
import { Args, bustCacheAfter } from "~/server/resource/resource-helpers.server";
import { getCache<PERSON><PERSON> } from "~/server/cache/cache.planning.server";
import { asPaymentTotalAmount } from "~/domain/payment/payment-queries.server";
import { convertMoney } from "~/utils/money";
import { XenditHeadersWithSplit } from "~/domain/payment/xendit-types";
import { _booking_detail } from "~/misc/paths";
import { getHost } from "~/misc/web-helpers";

export const createXenditPaymentUpdateData = async (db: Kysely<DB>, paymentId: string) => {
  const payment = await db
    .selectFrom("payment")
    .innerJoin("xendit_account", "xendit_account.id", "payment.xendit_account_id")
    .innerJoin("xendit_platform", "xendit_platform.id", "xendit_account.xendit_platform_id")
    .innerJoin("xendit_environment", "xendit_environment.xendit_platform_id", "xendit_platform.id")
    .whereRef("xendit_environment.production", "=", "xendit_account.production")
    .where("payment.id", "=", paymentId)
    .selectAll("payment")
    .select(["xendit_account.xendit_user_id", "xendit_environment.xendit_api_key"])
    .executeTakeFirst();
  if (!payment) return {};
  if (!payment.xendit_user_id) throw new ResourceError("No xendit user or api key");
  if (!payment.xendit_invoice_id || !!payment.payed_at) return {};
  const invoice = await getInvoice({
    apiKey: payment.xendit_api_key,
    invoiceId: payment.xendit_invoice_id,
    headers: { "for-user-id": payment.xendit_user_id }
  });

  if (invoice.status === "SETTLED" || invoice.status === "PAID")
    return {
      payed_at: nowValue
    };
  return {
    refreshed_at: nowValue
  };
};

const createXenditInvoice = async (args: { payment_id: string; trx: Kysely<DB> }) => {
  const payment = await args.trx
    .selectFrom("payment")
    .selectAll("payment")
    .innerJoin("payment_method", "payment_method.id", "payment.payment_method_id")
    .innerJoin("booking", "booking.id", "payment.booking_id")
    .innerJoin("establishment", "establishment.id", "booking.establishment_id")
    .innerJoin("xendit_account", "xendit_account.id", "establishment.xendit_account_id")
    .innerJoin("xendit_platform", "xendit_platform.id", "xendit_account.xendit_platform_id")
    .innerJoin("xendit_environment", "xendit_environment.xendit_platform_id", "xendit_platform.id")
    .whereRef("xendit_environment.production", "=", "xendit_account.production")
    .select((eb) => [
      "booking.establishment_id",
      "payment.host",
      "establishment.xendit_account_id",
      "xendit_account.xendit_user_id",
      "xendit_platform.currency_id as xendit_currency_id",
      "xendit_environment.xendit_api_key",
      eb
        .selectFrom("xendit_split_rule")
        .select("xendit_split_rule.xendit_split_rule_id")
        .where("xendit_split_rule.active", "=", true)
        .whereRef("xendit_split_rule.xendit_environment_id", "=", "xendit_environment.id")
        .as("xendit_split_rule_id"),
      "booking.currency_id",
      "payment_method.xendit as payment_method_xendit",
      asPaymentTotalAmount
    ])
    // .innerJoin("product", "product.id", "booking.product_id")
    .where("payment.id", "=", args.payment_id)
    .executeTakeFirst();

  if (!payment) return `Payment or Xendit Env does not exist anymore ${args.payment_id}`;

  if (payment.payment_method_xendit && !payment.xendit_user_id) throw new ResourceError("xendit user is required to create invoice");

  if (!payment.payment_method_xendit || payment.url || !payment.xendit_user_id) {
    return `Callback action not needed for payment ${payment.id}`;
  }

  const currencies = await args.trx.selectFrom("currency").selectAll("currency").execute();

  const paymentAmountInXenditCurrency = convertMoney(
    { currencies: currencies },
    {
      nativeAmount: payment.derived_amount_total,
      nativeCurrency: payment.currency_id,
      toCurrency: payment.xendit_currency_id
    }
  );

  const sixWeeksInSeconds = 60 * 60 * 24 * 7 * 6;
  const xenditApiKey = payment.xendit_api_key;
  const xenditHeaders: XenditHeadersWithSplit = {
    "for-user-id": payment.xendit_user_id
  };
  if (payment.xendit_split_rule_id) {
    xenditHeaders["with-split-rule"] = payment.xendit_split_rule_id;
  }
  try {
    const result = await createInvoice(
      xenditApiKey,
      {
        external_id: payment.id,
        // payment_methods: allPossiblePaymentMethods,
        description: urlFromSegments(getFullUrl(payment.host), _booking_detail(payment.booking_id)),
        currency: payment.xendit_currency_id,
        amount: Number(paymentAmountInXenditCurrency),
        invoice_duration: sixWeeksInSeconds
      },
      xenditHeaders
    );
    // const result: any = await xendit.invoice.createInvoice({
    //   externalID: payment.id,
    //   forUserID: payment.xendit_user_id,
    //   paymentMethods: possiblePaymentMethodsValuesIndionesia,
    //   description: urlFromSegments(getFullUrl(args.callbackObj.host), _booking_detail(payment.booking_id)),
    //   currency: defaultXenditCurrency,
    //   amount: Number(paymentAmountInXenditCurrency),
    //   invoiceDuration: sixWeeksInSeconds,
    // });

    const xenditInvoiceId = result.id;
    const xenditPaymentUrl = result.invoice_url;

    await args.trx
      .updateTable("payment")
      .where("payment.id", "=", payment.id)
      .set({
        url: xenditPaymentUrl,
        xendit_invoice_id: xenditInvoiceId,
        xendit_account_id: payment.xendit_account_id,
        payment_currency: payment.xendit_currency_id,
        payment_amount: paymentAmountInXenditCurrency
      })
      .executeTakeFirstOrThrow();
  } catch (e: any) {
    console.error("xendit invoice creation error", e);
    await args.trx
      .updateTable("payment")
      .where("payment.id", "=", payment.id)
      .set({
        error: e?.message
      })
      .executeTakeFirstOrThrow();
  }

  // await notifyPage(_booking_detail(payment.booking_id), args.callbackObj.client_id || "");

  return true;
};

export const paymentResource: Args<"payment"> = {
  authorize: async (args) => {
    const payment = await args.trx
      .selectFrom("payment")
      .where("payment.id", "=", args.id)
      .innerJoin("booking", "booking.id", "payment.booking_id")
      .select(["payment.booking_id", "booking.establishment_id"])
      .$if(args.action === "update" && args.trigger === "before", (eb) => eb.where("payment.deleted_at", "=", at_infinity_value))
      .where("booking.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"))
      .executeTakeFirst();

    if (payment) {
      const bookingCacheKey = updateBookingCache.name + payment.booking_id;
      args.after_mutations.insideTransaction.set(bookingCacheKey, () => updateBookingCache(args.trx, payment.booking_id));
      bustCacheAfter(args, getCacheKey({ establishmentId: payment.establishment_id }));
    }

    return payment;
  },
  insert: async (args) => {
    // args.callbacks.push({
    //   name: "xendit_invoice_create",
    //   target_id: args.id,
    // });
    if (!args.data.amount || Number(args.data.amount) < 1) {
      throw new ResourceError("Amount should be higher than 0");
    }

    args.after_mutations.insideTransaction.set(createXenditInvoice.name + args.id, async () => {
      await createXenditInvoice({ trx: args.trx, payment_id: args.id });
    });

    return {
      ...args.data,
      url: z.string().url().nullish().parse(args.data.url),
      payed_at: args.data.payed_at === undefined ? undefined : args.data.payed_at ? nowValue : null,
      host: getHost(args.request),
      // xendit_account_id: args.trx
      //   .selectFrom("payment_method")
      //   .where("payment_method.xendit", "=", true)
      //   .innerJoin("establishment", "establishment.id", "payment_method.establishment_id")
      //   .select("establishment.xendit_account_id"),
      xendit_account_id: undefined,
      xendit_invoice_id: undefined,
      refreshed_at: undefined,
      created_at: nowValue,
      created_by_user_session_id: userSessionId(args),
      error: null,
      deleted_at: at_infinity_value,
      deleted_by_user_session_id: null
    };
  },
  update: async (args) => {
    const payedAt = args.data.payed_at;
    if (payedAt === actionValues.payedAtXendit) {
      return createXenditPaymentUpdateData(args.trx, args.id);
    }

    const getPayedAt = async () => {
      if (!payedAt) return payedAt;
      const establishment = await args.trx
        .selectFrom("payment")
        .innerJoin("booking", "booking.id", "payment.booking_id")
        .innerJoin("establishment", "establishment.id", "booking.establishment_id")
        .innerJoin("spot", "spot.id", "establishment.spot_id")
        .innerJoin("region", "region.id", "spot.region_id")
        .where("payment.id", "=", args.id)
        .select(["region.timezone"])
        .executeTakeFirst();
      const timezone = establishment?.timezone;
      if (!timezone) return payedAt;
      return payedAt + " " + timezone;
    };

    const payedAtFinal = payedAt === at_now_value ? nowValue : await getPayedAt();

    return {
      ...args.data,
      url: z.string().url().nullish().parse(args.data.url),
      payed_at: payedAtFinal,
      error: args.data.error === null ? null : undefined,
      xendit_invoice_id: undefined,
      created_at: undefined,
      created_by_user_session_id: undefined,
      refreshed_at: undefined,
      deleted_at: args.data.deleted_at ? nowValue : undefined,
      deleted_by_user_session_id: args.data.deleted_at ? userSessionId(args) : undefined
    };
  },
  delete: () => false
};

export const paymentMethodResource: Args<"payment_method"> = {
  authorize: (args) =>
    args.trx
      .selectFrom("payment_method")
      .where("payment_method.id", "=", args.id)
      .where((eb) =>
        eb.or([
          eb("payment_method.establishment_id", "in", memberIsAdminOrOwnerQb(args).select("_member.establishment_id")),
          eb.exists(isEditorQb(args))
        ])
      )
      .executeTakeFirst(),
  beforeMutate: async (args) => {
    const name = args.data?.name;
    const finalName = typeof name === "string" && name && name.trim();
    const finalKey = finalName && finalName.toLowerCase();

    const intuitConnectionId = args.data?.intuit_connection_id;
    const establishmentId = args.data?.establishment_id;

    if (typeof intuitConnectionId === "string") {
      const allowedIntuitConnection = await args.trx
        .selectFrom("intuit_connection")
        .innerJoin("user_session", "user_session.id", "intuit_connection.created_by_user_session_id")
        .where("intuit_connection.id", "=", intuitConnectionId)
        .where((eb) =>
          eb.or([
            eb("user_session.user_id", "in", activeUserSessionQb(args, true).select("_user_session.user_id")),
            eb(
              "intuit_connection.id",
              "in",
              eb
                .selectFrom("payment_method")
                .where("payment_method.establishment_id", "=", establishmentId || null)
                .select("payment_method.intuit_connection_id")
            )
          ])
        )
        .select("intuit_connection.id")
        .executeTakeFirst();

      if (!allowedIntuitConnection) {
        throw new ResourceError("You are not allowed to connect the payment method with this quickbooks account");
      }
    }

    return {
      operation: args.operation,
      id: args.id || v4(),
      data: args.data && {
        ...args.data,
        default_surcharge_percentage: args.data?.default_surcharge_percentage === null ? 0 : args.data?.default_surcharge_percentage,
        key: finalKey || undefined,
        name: finalName || undefined
      }
    };
  },
  insert: (args) => ({ ...args.data, deleted_at: at_infinity_value, created_by_user_session_id: userSessionId(args) }),
  update: (args) => removeObjectKeys(args.data, "created_by_user_session_id", "created_at"),
  delete: () => true
};
