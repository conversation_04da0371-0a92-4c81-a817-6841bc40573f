import { unique } from "remeda";

const defaultSurcharges = [0, 1, 2, 3, 4, 5, 6] as const;

export const getDefaultSurcharges = (customSurcharge?: number | null) =>
  unique([...defaultSurcharges, customSurcharge || 0]).sort((a, b) => a - b);

type PaymentMethod = { label: string; short: string; surcharge: number };

export const paymentMethods = {
  xendit: { label: "Xendit", surcharge: 3, short: "" },
  wise: { label: "Wise", surcharge: 0, short: "" },
  cash: { label: "Cash", surcharge: 0, short: "" },
  paypal: { label: "Paypal", surcharge: 4, short: "" },
  banktransfer: { label: "Bank Transfer", surcharge: 0, short: "" },
  creditcard: { label: "Credit Card", short: "CC", surcharge: 3 },
  other: { label: "Other", surcharge: 0, short: "" },
} satisfies Record<string, PaymentMethod>;
