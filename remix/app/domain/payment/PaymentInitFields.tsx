import { Fragment } from "react";
import { entries, tableIdRef } from "~/misc/helpers";
import { paymentMethods } from "~/domain/payment/payment";
import { RInput } from "~/components/ResourceInputs";

export const PaymentInitFields = (props: { establishment_index?: number | string }) => {
  return (
    <Fragment>
      {entries(paymentMethods).map(([key, method], index) => (
        <Fragment key={key}>
          <RInput
            table={"payment_method"}
            field={"data.establishment_id"}
            index={index}
            type={"hidden"}
            value={tableIdRef("establishment", props.establishment_index)}
          />
          <RInput table={"payment_method"} field={"data.name"} index={index} type={"hidden"} value={method.label} />
          <RInput table={"payment_method"} field={"data.key"} index={index} type={"hidden"} value={key} />
          <RInput
            table={"payment_method"}
            field={"data.default_surcharge_percentage"}
            index={index}
            type={"hidden"}
            value={method.surcharge}
          />
          <RInput table={"payment_method"} field={"data.fixed"} index={index} type={"hidden"} hiddenType={"__boolean__"} value={"true"} />
          <RInput table={"payment_method"} field={"data.short"} index={index} type={"hidden"} value={method.short} />
        </Fragment>
      ))}
    </Fragment>
  );
};
