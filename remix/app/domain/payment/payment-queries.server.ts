import { sql } from "kysely";
import { contextRef, round } from "~/kysely/kysely-helpers";
import { kysely } from "~/misc/database.server";

const surcharchePercentage = contextRef("payment", "surcharge_percentage");
const amount = contextRef("payment", "amount");

const currencyDecimals = kysely
  .selectFrom("currency")
  .select("currency.decimals")
  .where("currency.id", "=", contextRef("booking", "currency_id"));

export const paymentSurcharcheAmountSelect = round(sql<number>`(${amount} * (${surcharchePercentage}:: numeric / 100))`, currencyDecimals);
export const asPaymentSurcharcheAmount = paymentSurcharcheAmountSelect.as("derived_amount_surcharge");

export const asPaymentTotalAmount = sql<number>`(${amount} + ${paymentSurcharcheAmountSelect})`.as("derived_amount_total");
