export interface XenditHeaders {
  "for-user-id": string;
}

export interface XenditHeadersWithSplit extends XenditHeaders {
  "with-split-rule"?: string;
}

export interface XenditInvoiceCreateRequest {
  external_id: string;
  amount: number;
  payer_email?: string;
  description?: string;
  invoice_duration?: number;
  currency?: string;
  customer?: XenditCustomer;
  items?: XenditInvoiceItem[];
  fees?: XenditInvoiceFee[];
  payment_methods?: string[];
  customer_notification_preference?: CustomerNotificationPreference;
  success_redirect_url?: string;
  failure_redirect_url?: string;
  should_send_email?: boolean;
  should_send_sms?: boolean;
  should_send_whatsapp?: boolean;
  reminder_time_unit?: "hour" | "day";
  reminder_time_value?: number;
}

export interface XenditCustomer {
  given_names?: string;
  surname?: string;
  email?: string;
  mobile_number?: string;
  addresses?: XenditCustomerAddress[];
}

export interface XenditCustomerAddress {
  country?: string;
  street_line1?: string;
  street_line2?: string;
  city?: string;
  province?: string;
  state?: string;
  postal_code?: string;
}

export interface XenditInvoiceItem {
  name: string;
  quantity: number;
  price: number;
  category?: string;
  url?: string;
}

export interface XenditInvoiceFee {
  type: string;
  value: number;
}

export interface CustomerNotificationPreference {
  invoice_created?: string[];
  invoice_reminder?: string[];
  invoice_paid?: string[];
  invoice_expired?: string[];
}

export interface XenditInvoiceBaseResponse {
  id: string;
  external_id: string;
  user_id: string;
  status: string;
  merchant_name: string;
  amount: number;
  payer_email?: string;
  description?: string;
  expiry_date: string;
  invoice_url: string;
  available_banks: AvailableBank[];
  available_retail_outlets: AvailableRetailOutlet[];
  available_ewallets: AvailableEwallet[];
  available_qr_codes: AvailableQrCode[];
  available_direct_debits: AvailableDirectDebit[];
  available_paylaters: AvailablePayLater[];
  should_exclude_credit_card: boolean;
  should_send_email: boolean;
  created: string;
  updated: string;
  currency: string;
}

export interface XenditInvoiceGetResponse extends XenditInvoiceBaseResponse {
  status: "PENDING" | "PAID" | "EXPIRED" | "SETTLED" | "FAILED";
  updated: string;
  paid_at?: string;
  payment_method?: string;
  fees_paid_amount?: number;
}

export interface AvailableBank {
  bank_code: string;
  collection_type: string;
  transfer_amount?: number;
  bank_branch?: string;
  account_holder_name?: string;
  identity_amount?: number;
}

export interface AvailableRetailOutlet {
  retail_outlet_name: string;
  transfer_amount?: number;
}

export interface AvailableEwallet {
  ewallet_type: string;
}

export interface AvailableQrCode {
  qr_code_type: string;
}

export interface AvailableDirectDebit {
  direct_debit_type: string;
}

export interface AvailablePayLater {
  paylater_type: string;
}

export interface XenditSplitRuleCreateRequest {
  name: string;
  description?: string;
  routes: XenditSplitRoute[];
  reference_id?: string;
}

export interface XenditSplitRoute {
  destination_account_id: string;
  currency: string;
  flat_amount?: number;
  percent_amount?: number;
  reference_id?: string;
}

export interface XenditSplitRuleCreateResponse {
  id: string;
  name: string;
  description?: string;
  reference_id?: string;
  status: string;
  routes: XenditSplitRuleRouteResponse[];
  created: string;
  updated: string;
}

export interface XenditSplitRuleRouteResponse {
  id: string;
  destination_account_id: string;
  currency: string;
  flat_amount?: number;
  percent_amount?: number;
  reference_id?: string;
  created: string;
  updated: string;
}

export interface XenditUpdateAccountRequest {
  email?: string;
  business_profile?: {
    business_name?: string;
    business_description?: string;
    business_type?: string;
    business_industry?: string;
  };
  public_profile?: {
    business_name?: string;
    business_logo?: string;
  };
  status?: "ACTIVE" | "INACTIVE";
}

export interface XenditAccountResponse {
  id: string;
  email: string;
  type: "OWNED" | "MANAGED";
  status: "ACTIVE" | "INACTIVE";
  created: string;
  updated: string;
  business_profile?: {
    business_name?: string;
    business_description?: string;
    business_type?: string;
    business_industry?: string;
  };
  public_profile?: {
    business_name?: string;
    business_logo?: string;
  };
}

export interface XenditAccountHolder {
  id: string; // The unique identifier for the account holder
  business_name?: string; // Business name if applicable
  type: "INDIVIDUAL" | "BUSINESS"; // Account type
  country: string; // Country code (e.g., "ID", "PH", "SG")
  created: string; // ISO timestamp of creation
  status: "PENDING" | "VERIFIED" | "REJECTED"; // Verification status
  rejection_reason?: string; // If rejected, contains the reason
  owner_id: string; // The Xendit account that owns this entity
  metadata?: Record<string, any>; // Additional metadata
}

export type XenditWebhookType = "invoice" | "disbursement" | "virtual_account" | "ewallet" | "payment_method" | "qr_code";

export interface XenditSetWebhookUrlResponse {
  url: string;
  user_id: string;
  updated: string;
  environment: "TEST" | "LIVE";
  callback_token: string;
}

export interface XenditVABank {
  code: string; // Bank code (e.g., "BCA", "BNI", "MANDIRI")
  name: string; // Bank name (e.g., "Bank Central Asia", "Bank Negara Indonesia")
  is_active: boolean; // Whether the bank is currently active for Virtual Accounts
  can_disburse: boolean; // Whether disbursement is allowed
  can_name_match: boolean; // Whether the bank supports name matching
  is_direct: boolean; // Whether the bank supports direct VA creation
  supports_payment: boolean; // Whether payments are supported via this VA bank
}

export interface XenditCreateFixedVARequest {
  external_id: string; // Unique reference ID for your system
  bank_code: string; // Bank code (e.g., "BCA", "BNI", "MANDIRI")
  name: string; // Customer name
  is_closed: boolean; // Whether the VA is closed (true) or open-ended (false)
  expected_amount?: number; // (Optional) Fixed amount expected for payment
  expiration_date?: string; // (Optional) Expiration timestamp (ISO 8601 format)
  is_single_use?: boolean; // (Optional) Whether this VA is single-use
  description?: string; // (Optional) Description for the VA
}

export interface XenditCreateFixedVAResponse {
  id: string; // Unique Xendit-generated ID for this VA
  owner_id: string; // Xendit account ID
  external_id: string; // Reference ID from your system
  bank_code: string; // Bank code (e.g., "BCA", "BNI")
  merchant_code: string; // Merchant code assigned by the bank
  account_number: string; // Generated Virtual Account number
  name: string; // Customer name
  is_closed: boolean; // Whether the VA is closed or open-ended
  expected_amount?: number; // Expected amount (if set)
  expiration_date?: string; // Expiration timestamp (if set)
  status: "ACTIVE" | "INACTIVE"; // Status of the VA
  is_single_use?: boolean; // Whether this VA is single-use
  created: string; // Creation timestamp
  updated: string; // Last update timestamp
}
