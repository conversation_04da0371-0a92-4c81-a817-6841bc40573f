import {
  XenditAccountHolder,
  XenditAccountResponse,
  XenditCreateFixedVARequest,
  XenditCreateFixedVAResponse,
  XenditHeaders,
  XenditHeadersWithSplit,
  XenditInvoiceBaseResponse,
  XenditInvoiceCreateRequest,
  XenditInvoiceGetResponse,
  XenditSetWebhookUrlResponse,
  XenditSplitRuleCreateRequest,
  XenditSplitRuleCreateResponse,
  XenditUpdateAccountRequest,
  XenditVABank,
  XenditWebhookType,
} from "~/domain/payment/xendit-types";
import { ResourceError } from "~/utils/error";

export const createSplitRule = async (apiKey: string, input: XenditSplitRuleCreateRequest) => {
  const authHeader = Buffer.from(apiKey + ":").toString("base64");
  const response = await fetch("https://api.xendit.co/split_rules", {
    method: "post",
    body: JSON.stringify(input),
    headers: {
      Authorization: `Basic ${authHeader}`,
      "Content-Type": "application/json",
    },
  });
  console.log("response", response);
  if (!response.ok) throw new ResourceError(`could not creat split rule ${response.status} ${response.statusText}`);
  const body = await response.json();

  return body as XenditSplitRuleCreateResponse;
};

export const createInvoice = async (apiKey: string, request: XenditInvoiceCreateRequest, headers: XenditHeadersWithSplit) => {
  // console.log("apikey", apiKey);
  const authHeader = Buffer.from(apiKey + ":").toString("base64");
  const response = await fetch("https://api.xendit.co/v2/invoices", {
    method: "post",
    body: JSON.stringify(request),
    headers: {
      ...headers,
      Authorization: `Basic ${authHeader}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) throw new Error(`could not create invoice ${response.status} ${response.statusText}`);
  const body = await response.json();
  return body as XenditInvoiceBaseResponse;
};

export const getInvoice = async (args: { apiKey: string; invoiceId: string; headers: XenditHeaders }) => {
  const authHeader = Buffer.from(args.apiKey + ":").toString("base64");

  const response = await fetch(`https://api.xendit.co/v2/invoices/${args.invoiceId}`, {
    method: "get",
    headers: {
      ...args.headers,
      Authorization: `Basic ${authHeader}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) throw new Error(`could not get invoice ${args.invoiceId} ${response.status} ${response.statusText}`);
  const body = await response.json();
  return body as XenditInvoiceGetResponse;
};

export const updateAccount = async (args: { apiKey: string; accountId: string; request: XenditUpdateAccountRequest }) => {
  const authHeader = Buffer.from(args.apiKey + ":").toString("base64");
  const response = await fetch(`https://api.xendit.co/v2/accounts/${args.accountId}`, {
    method: "patch",
    body: JSON.stringify(args.request),
    headers: {
      // ...args.headers,
      Authorization: `Basic ${authHeader}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) throw new Error(`could not update account ${args.accountId} ${response.status} ${response.statusText}`);
  const body = await response.json();
  return body as XenditAccountResponse;
};

export const getAccountHolder = async (args: { apiKey: string; accountHolderId: string }) => {
  const authHeader = Buffer.from(args.apiKey + ":").toString("base64");

  const response = await fetch(`https://api.xendit.co/v2/account_holders/${args.accountHolderId}`, {
    method: "get",
    headers: {
      Authorization: `Basic ${authHeader}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) throw new Error(`could not get accountholder ${args.accountHolderId} ${response.status} ${response.statusText}`);
  const body = await response.json();
  return body as XenditAccountHolder;
};

export const getAccount = async (args: { apiKey: string; accountId: string }) => {
  const authHeader = Buffer.from(args.apiKey + ":").toString("base64");

  const response = await fetch(`https://api.xendit.co/v2/accounts/${args.accountId}`, {
    method: "get",
    headers: {
      Authorization: `Basic ${authHeader}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) throw new Error(`could not get account ${args.accountId} ${response.status} ${response.statusText}`);
  const body = await response.json();
  return body as XenditAccountResponse;
};

export const setCallbackUrl = async (args: { apiKey: string; url: string; type: XenditWebhookType; headers: XenditHeaders }) => {
  const authHeader = Buffer.from(args.apiKey + ":").toString("base64");
  const response = await fetch(`https://api.xendit.co/callback_urls/${args.type}`, {
    method: "post",
    body: JSON.stringify({ url: args.url }),
    headers: {
      ...args.headers,
      Authorization: `Basic ${authHeader}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) throw new Error(`could not create callback url ${response.status} ${response.statusText}`);
  const body = await response.json();
  return body as XenditSetWebhookUrlResponse;
};

export const getVirtualBankAccounts = async (args: { apiKey: string; headers: Partial<XenditHeaders> }) => {
  const authHeader = Buffer.from(args.apiKey + ":").toString("base64");

  const response = await fetch(`https://api.xendit.co/available_virtual_account_banks`, {
    method: "get",
    headers: {
      ...args.headers,
      Authorization: `Basic ${authHeader}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) throw new Error(`could not get virtual bank accounts ${response.status} ${response.statusText}`);
  const body = await response.json();
  return body as XenditVABank[];
};

export const createFixedVirtualAccount = async (args: {
  apiKey: string;
  request: XenditCreateFixedVARequest;
  headers: Partial<XenditHeadersWithSplit>;
}) => {
  const authHeader = Buffer.from(args.apiKey + ":").toString("base64");
  const response = await fetch("https://api.xendit.co/callback_virtual_accounts", {
    method: "post",
    body: JSON.stringify(args.request),
    headers: {
      ...args.headers,
      Authorization: `Basic ${authHeader}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) throw new Error(`could not create virtual account ${response.status} ${response.statusText}`);
  const body = await response.json();
  return body as XenditCreateFixedVAResponse;
};
