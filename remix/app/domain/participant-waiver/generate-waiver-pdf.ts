import { mergeStateToParams } from "~/misc/parsers/global-state-parsers";
import { appConfig } from "~/config/config.server";
import { pdfScreenshot, PdfScreenshotArgs } from "~/libs/pdf-service/pdf-service";

const headerTemplate = `<div style="display: inline-block; width: 75%; margin: 0 2cm">
                            <div style="float: right; font-size: 7pt; text-align: right;">Page <span class="pageNumber"></span></div>
                        </div>`;
const footerTemplate = `<div style="margin: 0 2cm; width: 75%; font-size: 7px; text-align: center;">discalimer bla</div>`;

const genreateWaiverPdfSimple = async (args: { pdfUrl: URL; overwritePdfScreenshotArgs?: Partial<PdfScreenshotArgs> }) => {
  const url = new URL(args.pdfUrl);
  mergeStateToParams(url.searchParams, { print_token: appConfig.PRINT_TOKEN });
  const fullUrl = url.toString();
  console.log("fullurl", fullUrl);

  const response = await pdfScreenshot({
    url: fullUrl,
    // fileName: "waiver.pdf",
    options: {
      displayHeaderFooter: true,
      headerTemplate: "",
      footerTemplate: `<div style="padding: 0 30pt 0 30pt; font-size: 7pt; text-align: right;" >
                            Page <span class="pageNumber"></span>/<span class="totalPages"></span>
                        </div>`,
      printBackground: true,
      scale: 0.8,
      margin: { top: 30, bottom: 40, left: 30, right: 30 },
    },
    ...args.overwritePdfScreenshotArgs,
  });
  if (!response.ok) {
    console.error("error", response.statusText);
    throw new Error("Could not visit website or generate pdf");
  }
  const body = await response.json();
  console.log("api2pdf result", body);
  return body;
};

export const generateWaiverPdf = async (args: { pdfUrl: URL }) => {
  const body = await genreateWaiverPdfSimple(args);

  const fileUrl = body.FileUrl;

  const fileResponse = await fetch(fileUrl);

  const arraypuffer = await fileResponse.arrayBuffer();
  return {
    arrayBuffer: arraypuffer,
    // participantWaiver: participantWaiver,
  };
};

export const generateAndSafeWaiverPdf = async (args: { pdfUrl: URL; presignedUploadUrl: string }) => {
  const response = await genreateWaiverPdfSimple({
    pdfUrl: args.pdfUrl,
    overwritePdfScreenshotArgs: {
      inline: true,
      useCustomStorage: true,
      storage: { method: "PUT", url: args.presignedUploadUrl, extraHTTPHeaders: { "content-type": "application/pdf" } },
    },
  });
  return response;
};
