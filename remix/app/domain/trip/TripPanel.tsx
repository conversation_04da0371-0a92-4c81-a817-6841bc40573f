import type { TripModel } from "~/domain/trip/trip-components";
import { getTripTotals } from "~/domain/trip/trip-components";
import React, { Fragment, useState } from "react";
import { getTripType, isRole, Role, roles, tripTypes } from "~/domain/planning/plannings-consts";
import { fName, keys, myGroupBy2 } from "~/misc/helpers";
import { AnimatingDiv } from "~/components/base/base";
import { twMerge } from "tailwind-merge";
import { DividerWithText } from "~/components/Divider";
import { flat, uniqueBy } from "remeda";
import { DeleteButtonForm, FInput, RInput } from "~/components/ResourceInputs";
import { ActionAlert } from "~/components/ActionAlert";
import { MemberSelect } from "~/domain/planning/MemberSelect";
import { SubmitButton } from "~/components/base/Button";
import { ActionForm } from "~/components/form/BaseFrom";
import { SharedTripFields } from "~/domain/trip/TripForm";
import { HiddenTypeInput, OperationInput, RedirectParamsInput, ResponseIdentifierInput } from "~/components/form/DefaultInput";
import { TrashIcon } from "@heroicons/react/20/solid";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { ParamLink } from "~/components/meta/CustomComponents";
import { removeFromArray, toggleArray } from "~/misc/parsers/global-state-parsers";
import { useCanEdit } from "~/domain/member/member-hooks";
import { toSitesStr } from "~/domain/trip/trip-helpers";
import { usePlanningDayLoader } from "~/routes/_all._catch._w.planning._index";
import { Checker } from "~/components/Checker";
import { getProductTitle } from "~/domain/product/ProductItem";
import { ChevronDown } from "lucide-react";
import { TripParticipants } from "~/domain/trip/TripParticipants";

const TripSite = (props: { trip_id: string; sites: string[]; index: number; readonly?: boolean }) => {
  const search = useSearchParams2();
  const sites = props.sites;
  const name = sites[props.index] || "";
  const tripSiteKey = props.trip_id + "-" + props.index;

  if (search.state.toggle_trip_sites.includes(tripSiteKey) && !props.readonly)
    return (
      <ActionForm className={"flex flex-row items-center gap-3"}>
        <RedirectParamsInput path={"./"} paramState={{ toggle_trip_sites: toggleArray(search.state.toggle_trip_sites, tripSiteKey) }} />
        {sites.map((site, index) => (
          <input key={index} type={"hidden"} name={fName("trip", "data.sites", 0, index)} value={site} />
        ))}
        <RInput table={"trip"} field={"id"} value={props.trip_id} />
        <FInput
          className="input"
          name={fName("trip", "data.sites", 0, props.index)}
          autoFocus
          placeholder={"site name"}
          defaultValue={name || ""}
        />
        <ParamLink paramState={{ toggle_trip_sites: toggleArray(search.state.toggle_trip_sites, tripSiteKey) }}>Cancel</ParamLink>
        <SubmitButton className={"link disabled:loading-dots"}>Submit</SubmitButton>
      </ActionForm>
    );

  return (
    <div className="flex flex-wrap items-center gap-3 pl-2">
      {props.readonly ? (
        <span className="font-semibold">{props.sites[props.index]}</span>
      ) : (
        <ParamLink
          className="link"
          type={"button"}
          paramState={{ toggle_trip_sites: toggleArray(search.state.toggle_trip_sites, tripSiteKey) }}
        >
          {props.sites[props.index] || "name site"}
        </ParamLink>
      )}
      {!props.readonly && (
        <ActionForm>
          <HiddenTypeInput name={fName("trip", "data.sites", 0)} value={"__empty_array__"} />
          {sites
            .filter((_, index) => index !== props.index)
            .map((site, index) => (
              <input key={index} type={"hidden"} name={fName("trip", "data.sites", 0, index)} value={site} />
            ))}
          <RInput table={"trip"} field={"id"} value={props.trip_id} />
          <SubmitButton className={"spinner-dark text-slate-500"}>
            <TrashIcon className={"h-4 w-4"} />
          </SubmitButton>
        </ActionForm>
      )}
    </div>
  );
};

export const TripPanelBasic = (props: { trip: TripModel; readonly?: boolean }) => {
  const trip = props.trip;
  const tripType = getTripType(trip.type);
  const sites = tripType?.showSites && trip.sites ? trip.sites : [];
  // const tripEditIsOn = useBoolean();
  const search = useSearchParams2();
  const isTripEdit = search.state.toggle_trip_edit.includes(trip.id);
  const memberState = search.state.toggle_member_panel || "";
  const tripTotals = getTripTotals(trip);

  const selectedAssignment = trip.assignments?.find((assignment) => assignment.id === memberState);

  const addMemberRole = keys(roles).find((role) => role === (selectedAssignment?.role || memberState));

  const captainAssigns = trip.assignments?.filter(isRole("captain")) || [];
  const crewAssigns = trip.assignments?.filter(isRole("crew")) || [];
  const instructorAssigns = trip.assignments?.filter(isRole("instructor", "divemaster")) || [];

  return (
    <Fragment>
      {!isTripEdit && (
        <div className="rounded-md border border-slate-800 p-2 grid gap-x-3" style={{ gridTemplateColumns: "auto minmax(auto, 1fr)" }}>
          <div className="whitespace-nowrap">{tripType?.key === "boat" ? "Departure time" : "Start time"}</div>
          <div className="w-full pl-2 font-semibold">{trip.start_time?.slice(0, 5)}</div>
          <div className="whitespace-nowrap">Trip location / title</div>
          <div className="pl-2 font-semibold">{trip.activity_location}</div>
          {tripType?.key === "boat" && (
            <Fragment>
              <div>Boat</div>
              <div className="pl-2 font-semibold">{trip.boat_name}</div>
            </Fragment>
          )}
          <div className="whitespace-nowrap">{tripType?.key === "boat" ? "Boat location" : "Activity address"}</div>
          <div className="pl-2 font-semibold">{trip.address}</div>
          {trip.capacity && (
            <Fragment>
              <div>Max. capacity</div>
              <div className="pl-2 font-semibold">{trip.capacity}</div>
            </Fragment>
          )}
          {captainAssigns.map((assignment, index) => (
            <Fragment key={assignment.id}>
              <div>Boat captain {captainAssigns.length === 1 ? "" : index + 1}</div>
              <div className="pl-2">
                {!props.readonly ? (
                  <ParamLink className="link" paramState={{ toggle_member_panel: assignment.id }}>
                    {assignment.member?.name}
                  </ParamLink>
                ) : (
                  <span>{assignment.member?.name}</span>
                )}
              </div>
            </Fragment>
          ))}
          {!props.readonly && tripType?.key === "boat" && captainAssigns.length === 0 && (
            <Fragment>
              <div className="col-span-2">
                <ParamLink className="link" paramState={{ toggle_member_panel: "captain" satisfies Role }}>
                  +add captain
                </ParamLink>
              </div>
            </Fragment>
          )}
          {sites.map((_, index) => (
            <Fragment key={index}>
              <div>Site {index + 1}</div>
              <div>
                <TripSite trip_id={trip.id} sites={sites} index={index} readonly={props.readonly} />
              </div>
            </Fragment>
          ))}
          {!props.readonly && tripType?.showSites && (
            <Fragment>
              <div className="col-span-2">
                <ActionForm>
                  <RInput table={"trip"} field={"id"} value={trip.id} />
                  {[...sites, ""].map((site, index) => (
                    <input key={index} type={"hidden"} name={fName("trip", "data.sites", 0, index)} value={site} />
                  ))}
                  <SubmitButton className="link aria-busy:loading-dots">+add site</SubmitButton>
                </ActionForm>
              </div>
            </Fragment>
          )}
          <div className="whitespace-nowrap text-secondary-500 col-span-2">
            <DividerWithText>{tripTotals.seatsTaken} spots taken</DividerWithText>
          </div>
          {uniqueBy(crewAssigns, (crew) => crew.member_id).map((assignment, index) => (
            <Fragment key={assignment.id}>
              <div>Crew {crewAssigns.length === 1 ? "" : index + 1}</div>
              <div className="pl-2">
                {!props.readonly ? (
                  <ParamLink className="link" paramState={{ toggle_member_panel: assignment.id }}>
                    {assignment.member?.name}
                  </ParamLink>
                ) : (
                  <span>{assignment.member?.name}</span>
                )}
              </div>
            </Fragment>
          ))}
          {tripType?.key !== "rest" &&
            uniqueBy(instructorAssigns, (instructor) => instructor.member_id).map((assignment, index) => (
              <Fragment key={assignment.id}>
                <div>Instructor {instructorAssigns.length === 1 ? "" : index + 1}</div>
                <div className="pl-2 font-semibold">
                  {assignment.member?.name || <span className="opacity-40 font-normal">Not scheduled</span>}
                </div>
              </Fragment>
            ))}
          <div className="whitespace-nowrap">Participants</div>
          <div className="pl-2 flex flex-row gap-2">
            <span className="font-semibold">{tripTotals.participants}</span> &nbsp;
          </div>
          {!props.readonly && (
            <div className="flex flex-row justify-between gap-3 col-span-2">
              <div>
                {(tripType?.key === "boat" || tripType?.key === "shore") && (
                  <ParamLink className="link" paramState={{ toggle_member_panel: "crew" satisfies Role }}>
                    +add crew
                  </ParamLink>
                )}
              </div>
              <div className="flex flex-row justify-end gap-3">
                <DeleteButtonForm table={"trip"} values={[trip.id]} />
                <ParamLink
                  className="link"
                  paramState={{ toggle_trip_edit: toggleArray(search.state.toggle_trip_edit, trip.id), boat_id: null }}
                >
                  edit
                </ParamLink>
              </div>
            </div>
          )}
        </div>
      )}
      {addMemberRole && (
        <div key={memberState} className="space-y-3 rounded-md border border-primary p-2">
          <div className="flex w-full flex-row items-center justify-between gap-2">
            <h1 className="text-xl">{addMemberRole ? `Add ${addMemberRole}` : `Change ${selectedAssignment?.role}`}</h1>
            {selectedAssignment && (
              <ActionForm>
                <ResponseIdentifierInput />
                <RInput table={"trip_assignment"} field={"id"} value={selectedAssignment.id} />
                <OperationInput table={"trip_assignment"} value={"delete"} />
                <SubmitButton className="link aria-busy:loading-dots text-red-500 underline aria-busy:animate-pulse">Delete</SubmitButton>
              </ActionForm>
            )}
          </div>
          <ActionForm className="space-y-3">
            <RedirectParamsInput path={"./"} paramState={{ toggle_member_panel: null }} />
            <ActionAlert />
            {addMemberRole && <RInput table={"trip_assignment"} field={"data.role"} type={"hidden"} value={addMemberRole} />}
            {selectedAssignment ? (
              <RInput table={"trip_assignment"} field={"id"} value={selectedAssignment.id} />
            ) : (
              <RInput table={"trip_assignment"} field={"data.trip_id"} type={"hidden"} value={trip.id} />
            )}
            <MemberSelect
              role={addMemberRole || "crew"}
              name={fName("trip_assignment", "data.member_id")}
              establishment_id={trip.establishment_id}
              defaultValue={selectedAssignment?.member_id || ""}
            />
            <div className={"flex justify-end gap-3 items-center"}>
              <ParamLink className="link whitespace-nowrap" paramState={{ toggle_member_panel: null }}>
                Cancel
              </ParamLink>
              <SubmitButton className="btn btn-primary">Done</SubmitButton>
            </div>
          </ActionForm>
        </div>
      )}
      {isTripEdit && (
        <ActionForm className="space-y-3 rounded-md border border-primary p-2">
          <RedirectParamsInput path={"./"} paramState={{ toggle_trip_edit: toggleArray(search.state.toggle_trip_edit, trip.id) }} />
          <ActionAlert />
          <h1 className="text-xl">Edit trip</h1>
          <RInput table={"trip"} field={"id"} index={0} value={trip.id} />
          <RInput table={"establishment"} field={"id"} index={0} value={trip.establishment_id} />
          <OperationInput table={"establishment"} index={0} value={"ignore"} />
          <SharedTripFields identifier={0} trip={trip} />
          <div className={"flex justify-end gap-3 items-center"}>
            <ParamLink
              className=" link whitespace-nowrap"
              paramState={{ toggle_trip_edit: toggleArray(search.state.toggle_trip_edit, trip.id) }}
            >
              Cancel
            </ParamLink>
            <SubmitButton className="btn btn-primary">Done</SubmitButton>
          </div>
        </ActionForm>
      )}
    </Fragment>
  );
};

export const TripPanel = (props: { trip: TripModel; readonly?: boolean }) => {
  const trip = props.trip;
  const tripType = getTripType(trip.type);
  const sites = tripType?.showSites && trip.sites ? trip.sites : [];
  // const tripEditIsOn = useBoolean();
  const search = useSearchParams2();
  const isTripEdit = search.state.toggle_trip_edit.includes(trip.id);
  const memberState = search.state.toggle_member_panel || "";
  const tripTotals = getTripTotals(trip);

  const selectedAssignment = trip.assignments?.find((assignment) => assignment.id === memberState);

  const addMemberRole = keys(roles).find((role) => role === (selectedAssignment?.role || memberState));

  const captainAssigns = trip.assignments?.filter(isRole("captain")) || [];
  const crewAssigns = trip.assignments?.filter(isRole("crew")) || [];
  const instructorAssigns = trip.assignments?.filter(isRole("instructor", "divemaster")) || [];

  return (
    <Fragment>
      {!isTripEdit && (
        <div className="rounded-md border border-slate-800 p-2 grid gap-x-3" style={{ gridTemplateColumns: "auto minmax(auto, 1fr)" }}>
          <div className="whitespace-nowrap">{tripType?.key === "boat" ? "Departure time" : "Start time"}</div>
          <div className="w-full pl-2 font-semibold">{trip.start_time?.slice(0, 5)}</div>
          <div className="whitespace-nowrap">Trip location / title</div>
          <div className="pl-2 font-semibold">{trip.activity_location}</div>
          {tripType?.key === "boat" && (
            <Fragment>
              <div>Boat</div>
              <div className="pl-2 font-semibold">{trip.boat_name}</div>
            </Fragment>
          )}
          <div className="whitespace-nowrap">{tripType?.key === "boat" ? "Boat location" : "Activity address"}</div>
          <div className="pl-2 font-semibold">{trip.address}</div>
          {trip.capacity && (
            <Fragment>
              <div>Max. capacity</div>
              <div className="pl-2 font-semibold">{trip.capacity}</div>
            </Fragment>
          )}
          {captainAssigns.map((assignment, index) => (
            <Fragment key={assignment.id}>
              <div>Boat captain {captainAssigns.length === 1 ? "" : index + 1}</div>
              <div className="pl-2">
                {!props.readonly ? (
                  <ParamLink className="link" paramState={{ toggle_member_panel: assignment.id }}>
                    {assignment.member?.name}
                  </ParamLink>
                ) : (
                  <span>{assignment.member?.name}</span>
                )}
              </div>
            </Fragment>
          ))}
          {!props.readonly && tripType?.key === "boat" && captainAssigns.length === 0 && (
            <Fragment>
              <div className="col-span-2">
                <ParamLink className="link" paramState={{ toggle_member_panel: "captain" satisfies Role }}>
                  +add captain
                </ParamLink>
              </div>
            </Fragment>
          )}
          {sites.map((_, index) => (
            <Fragment key={index}>
              <div>Site {index + 1}</div>
              <div>
                <TripSite trip_id={trip.id} sites={sites} index={index} readonly={props.readonly} />
              </div>
            </Fragment>
          ))}
          {!props.readonly && tripType?.showSites && (
            <Fragment>
              <div className="col-span-2">
                <ActionForm>
                  <RInput table={"trip"} field={"id"} value={trip.id} />
                  {[...sites, ""].map((site, index) => (
                    <input key={index} type={"hidden"} name={fName("trip", "data.sites", 0, index)} value={site} />
                  ))}
                  <SubmitButton className="link aria-busy:loading-dots">+add site</SubmitButton>
                </ActionForm>
              </div>
            </Fragment>
          )}
          <div className="whitespace-nowrap text-secondary-500 col-span-2">
            <DividerWithText>{tripTotals.seatsTaken} spots taken</DividerWithText>
          </div>
          {uniqueBy(crewAssigns, (crew) => crew.member_id).map((assignment, index) => (
            <Fragment key={assignment.id}>
              <div>Crew {crewAssigns.length === 1 ? "" : index + 1}</div>
              <div className="pl-2">
                {!props.readonly ? (
                  <ParamLink className="link" paramState={{ toggle_member_panel: assignment.id }}>
                    {assignment.member?.name}
                  </ParamLink>
                ) : (
                  <span>{assignment.member?.name}</span>
                )}
              </div>
            </Fragment>
          ))}
          {tripType?.key !== "rest" &&
            uniqueBy(instructorAssigns, (instructor) => instructor.member_id).map((assignment, index) => (
              <Fragment key={assignment.id}>
                <div>Instructor {instructorAssigns.length === 1 ? "" : index + 1}</div>
                <div className="pl-2 font-semibold">
                  {assignment.member?.name || <span className="opacity-40 font-normal">Not scheduled</span>}
                </div>
              </Fragment>
            ))}
          <TripParticipants trip={trip} />
          {!props.readonly && (
            <div className="flex flex-row justify-between gap-3 col-span-2">
              <div>
                {(tripType?.key === "boat" || tripType?.key === "shore") && (
                  <ParamLink className="link" paramState={{ toggle_member_panel: "crew" satisfies Role }}>
                    +add crew
                  </ParamLink>
                )}
              </div>
              <div className="flex flex-row justify-end gap-3">
                <DeleteButtonForm table={"trip"} values={[trip.id]} />
                <ParamLink
                  className="link"
                  paramState={{ toggle_trip_edit: toggleArray(search.state.toggle_trip_edit, trip.id), boat_id: null }}
                >
                  edit
                </ParamLink>
              </div>
            </div>
          )}
        </div>
      )}
      {addMemberRole && (
        <div key={memberState} className="space-y-3 rounded-md border border-primary p-2">
          <div className="flex w-full flex-row items-center justify-between gap-2">
            <h1 className="text-xl">{addMemberRole ? `Add ${addMemberRole}` : `Change ${selectedAssignment?.role}`}</h1>
            {selectedAssignment && (
              <ActionForm>
                <ResponseIdentifierInput />
                <RInput table={"trip_assignment"} field={"id"} value={selectedAssignment.id} />
                <OperationInput table={"trip_assignment"} value={"delete"} />
                <SubmitButton className="link aria-busy:loading-dots text-red-500 underline aria-busy:animate-pulse">Delete</SubmitButton>
              </ActionForm>
            )}
          </div>
          <ActionForm className="space-y-3">
            <RedirectParamsInput path={"./"} paramState={{ toggle_member_panel: null }} />
            <ActionAlert />
            {addMemberRole && <RInput table={"trip_assignment"} field={"data.role"} type={"hidden"} value={addMemberRole} />}
            {selectedAssignment ? (
              <RInput table={"trip_assignment"} field={"id"} value={selectedAssignment.id} />
            ) : (
              <RInput table={"trip_assignment"} field={"data.trip_id"} type={"hidden"} value={trip.id} />
            )}
            <MemberSelect
              role={addMemberRole || "crew"}
              name={fName("trip_assignment", "data.member_id")}
              establishment_id={trip.establishment_id}
              defaultValue={selectedAssignment?.member_id || ""}
            />
            <div className={"flex justify-end gap-3 items-center"}>
              <ParamLink className="link whitespace-nowrap" paramState={{ toggle_member_panel: null }}>
                Cancel
              </ParamLink>
              <SubmitButton className="btn btn-primary">Done</SubmitButton>
            </div>
          </ActionForm>
        </div>
      )}
      {isTripEdit && (
        <ActionForm className="space-y-3 rounded-md border border-primary p-2">
          <RedirectParamsInput path={"./"} paramState={{ toggle_trip_edit: toggleArray(search.state.toggle_trip_edit, trip.id) }} />
          <ActionAlert />
          <h1 className="text-xl">Edit trip</h1>
          <RInput table={"trip"} field={"id"} index={0} value={trip.id} />
          <RInput table={"establishment"} field={"id"} index={0} value={trip.establishment_id} />
          <OperationInput table={"establishment"} index={0} value={"ignore"} />
          <SharedTripFields identifier={0} trip={trip} />
          <div className={"flex justify-end gap-3 items-center"}>
            <ParamLink
              className="link whitespace-nowrap"
              paramState={{ toggle_trip_edit: toggleArray(search.state.toggle_trip_edit, trip.id), boat_id: null }}
            >
              Cancel
            </ParamLink>
            <SubmitButton className="btn btn-primary">Done</SubmitButton>
          </div>
        </ActionForm>
      )}
    </Fragment>
  );
};

export const TripCarousel = (props: { trip: TripModel; readonly?: boolean }) => {
  const trip = props.trip;
  const canEdit = useCanEdit(trip?.establishment_id);
  const search = useSearchParams2();
  const openedTripPanels = search.state.persist_toggle_trip_panel_id || [];
  const toggledArray = toggleArray(openedTripPanels, trip.id);
  const panelToggled = openedTripPanels.includes(trip.id);
  const tripTotals = getTripTotals(trip);
  const seatsTakenPercentage = (tripTotals.seatsTaken / tripTotals.capacity) * 100;
  const seatsTakenPercentageAsString = seatsTakenPercentage.toFixed(0);

  const tripEntry = tripTypes[trip.type as keyof typeof tripTypes];
  const sites = tripEntry?.showSites && trip.sites ? trip.sites : [];
  // console.log("booking", trip.start_time);
  // const startTime = formatInTimeZone(trip.start_time, defaultTimezone, "HH:mm");

  return (
    <AnimatingDiv className={twMerge("space-y-3")}>
      <div className={twMerge(!trip.date && "opacity-70")}>
        <ParamLink
          paramState={{
            persist_toggle_trip_panel_id: toggledArray,
            toggle_trip_sites: search.state.toggle_trip_sites.filter((site) => !site.startsWith(trip.id)),
            toggle_trip_edit: removeFromArray(search.state.toggle_trip_edit, trip.id),
          }}
          className="w-full"
        >
          <h3 className="text-left line-clamp-1">
            <span className="text-xl font-semibold">{trip.activity_location}</span>
            {!!toSitesStr(sites) && <span className="max-md:text-xs">&nbsp;| {toSitesStr(sites)}</span>}
          </h3>
          <div className="flex flex-wrap items-center justify-center gap-2">
            <span className="flex-1">{trip.boat_name || tripEntry?.name}</span>
            <span>{trip.start_time?.slice(0, 5)}</span>
            <span className={twMerge("flex-1 text-right", seatsTakenPercentage >= 100 && "text-red-500")}>
              {tripTotals.capacity ? `${tripTotals.available} available` : ""}
            </span>
          </div>
          <div className="flex w-full flex-wrap items-center gap-3">
            <div className="h-10 w-12 text-secondary-tag">{tripEntry ? tripEntry.icon : "?"}</div>
            <div className="h-8 flex-1 overflow-hidden rounded-md bg-primary-100">
              {!!tripTotals.capacity && (
                <div
                  style={{
                    minWidth: "25px",
                    width: trip.date ? `${seatsTakenPercentageAsString}%` : "fit-content",
                    maxWidth: "100%",
                  }}
                  className={twMerge(
                    "flex h-full items-center justify-end bg-primary p-1 text-right text-xs text-white transition-width",
                    seatsTakenPercentage >= 100 && "bg-red-500",
                  )}
                >
                  {trip.date ? <span>{seatsTakenPercentageAsString}%</span> : <span>template</span>}
                </div>
              )}
              {!trip.date && <span>template</span>}
            </div>
          </div>
        </ParamLink>
      </div>
      {panelToggled && <TripPanel trip={trip} readonly={!canEdit} />}
    </AnimatingDiv>
  );
};
