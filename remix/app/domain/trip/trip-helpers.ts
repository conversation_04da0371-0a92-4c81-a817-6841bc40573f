export const toSitesStr = (sites?: Array<string | null | undefined> | null) => {
  if (!sites || sites.length === 0) return "";
  const sitesWithName = sites.filter((site) => !!site);
  const sitesCountStr = `${sites.length} site${sites.length > 1 ? "s" : ""}`;
  const siteNamesStr = sitesWithName.length > 0 ? ` (${sites.map((site) => site || "?").join(", ")})` : "";
  return sitesCountStr + siteNamesStr;
};
