import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import type { Role, TripType } from "~/domain/planning/plannings-consts";
import { kysely } from "~/misc/database.server";
import { coalesceTextArray } from "~/kysely/kysely-helpers";

export const defaultSites = ["", ""];

export const tripQb = kysely
  .selectFrom("trip")
  .selectAll("trip")
  .innerJoin("establishment as _trip_establishment", "_trip_establishment.id", "trip.establishment_id")
  .leftJoin("boat", (eb) => eb.onRef("boat.id", "=", "trip.boat_id").on("trip.type", "=", "boat" satisfies TripType))
  .select((eb) => [
    eb.fn.coalesce("trip.start_location", "boat.location", "_trip_establishment.address").as("address"),
    eb.fn.coalesce("trip.capacity", "boat.capacity").as("capacity"),
    coalesceTextArray(eb.ref("trip.sites"), defaultSites).as("sites"),
    "boat.name as boat_name",
    jsonObjectFrom(eb.selectFrom("boat").whereRef("boat.id", "=", "trip.boat_id").selectAll("boat")).as("boat"),
  ]);

export const tripCalendarQb = tripQb
  .where("trip.type", "!=", "rest" satisfies TripType)
  .orderBy("trip.start_time")
  .orderBy("trip.created_at")
  .select((eb) => {
    const assignmentQb = eb
      .selectFrom("trip_assignment")
      .leftJoin("participation", "participation.id", "trip_assignment.participation_id")
      .leftJoin("sale_item", "sale_item.id", "participation.sale_item_id")
      .leftJoin("booking", "booking.id", "sale_item.booking_id")
      .where("booking.cancelled_at", "is", null)
      .whereRef("trip.id", "=", "trip_assignment.trip_id");

    return [
      jsonObjectFrom(
        eb
          .selectFrom("boat")
          .selectAll("boat")
          .whereRef("trip.boat_id", "=", "boat.id")
          .where("trip.type", "=", "boat" satisfies TripType),
      ).as("boat"),
      jsonArrayFrom(
        assignmentQb
          .leftJoin("product", "product.id", "sale_item.product_id")
          .innerJoin("item", "item.id", "product.item_id")
          .where("trip_assignment.role", "in", ["instructor", "divemaster"] satisfies Role[])
          .groupBy("item.activity_slug")
          .select((eb) => ["item.activity_slug", eb.fn.count("trip_assignment.id").as("total")])
          .orderBy((eb) => eb.fn.count("trip_assignment.id"), "desc"),
      ).as("assignment_totals"),
      jsonArrayFrom(assignmentQb.selectAll("trip_assignment")).as("assignments"),
    ];
  });
