import React, { Fragment, useState } from "react";
import { twMerge } from "tailwind-merge";
import { ChevronDown } from "lucide-react";
import { AnimatingDiv } from "~/components/base/base";
import { ActionForm } from "~/components/form/BaseFrom";
import { RInput } from "~/components/ResourceInputs";
import { OperationInput } from "~/components/form/DefaultInput";
import { SubmitButton } from "~/components/base/Button";
import { Checker } from "~/components/Checker";
import { getProductTitle } from "~/domain/product/ProductItem";
import type { Role } from "~/domain/planning/plannings-consts";
import { getTripType } from "~/domain/planning/plannings-consts";
import type { TripModel } from "~/domain/trip/trip-components";
import { getTripTotals } from "~/domain/trip/trip-components";
import { usePlanningDayLoader } from "~/routes/_all._catch._w.planning._index";
import { flat } from "remeda";
import { myGroupBy2, symmetricDifference } from "~/misc/helpers";
import { toggleArray } from "~/misc/parsers/global-state-parsers";

export const TripParticipants = (props: { trip: TripModel }) => {
  const trip = props.trip;
  const [showParticipations, setShowParticipations] = useState(0);
  const data = usePlanningDayLoader();
  const tripTotals = getTripTotals(trip);

  const participations = data.allParticipations;
  const trips = flat(data.establishments.map((establishment) => establishment.trips));
  const products = data.allProducts;

  const participationActivityGroups = myGroupBy2(participations, (participation) => participation.id);

  // Process all participation groups once
  const processedParticipations = participationActivityGroups.map((group) => {
    const participationTrips = group.items
      .map((participation) => participation.trip_assignment)
      .filter((tripAssignment): tripAssignment is NonNullable<typeof tripAssignment> => !!tripAssignment);

    const participation = group.items[0];
    const participant = participation.participant;
    const booking = participation.booking;
    const product = products.find((product) => product.id === participation.activity?.product_id);

    const allParticipantParticipations = participationActivityGroups.filter(
      (participation) => participant && participant.id === participation.items[0]?.participant_id,
    );

    const participationIndex = allParticipantParticipations.findIndex((item) => item.groupKey === participation.id) + 1;
    const assignmentsForThisTrip = participationTrips.filter((assignment) => assignment.trip_id === trip.id);

    return {
      id: group.groupKey,
      participationTrips: participationTrips,
      participation: participation,
      participant: participant,
      booking: booking,
      product: product,
      allParticipantParticipations: allParticipantParticipations,
      participationIndex: participationIndex,
      assignmentsForThisTrip: assignmentsForThisTrip,
    };
  });

  // Find all participations that are already assigned to this trip
  const initialAssignedParticipations = processedParticipations
    .filter((item) => item.assignmentsForThisTrip.length > 0)
    .map((item) => item.id);

  const [selectedParticipations, setSelectedParticipations] = useState<string[]>(initialAssignedParticipations);

  // Check if there are changes between the initial state and the current state
  // Using symmetric difference to find elements that are in either array but not in both
  const diff = symmetricDifference(selectedParticipations, initialAssignedParticipations);
  const hasChanges = !!diff.length;

  return (
    <Fragment>
      {(!!trip.date || !!tripTotals.participants) && (
        <Fragment>
          <div className="whitespace-nowrap">Participants</div>
          <div className="pl-2 flex flex-row gap-2">
            <span className="font-semibold">{tripTotals.participants}</span> &nbsp;
            <button
              type={"button"}
              className="link flex flex-row gap-2 items-center"
              onClick={() => {
                setShowParticipations(
                  showParticipations === 0
                    ? initialAssignedParticipations.length && initialAssignedParticipations.length !== processedParticipations.length
                      ? 1
                      : 2
                    : 0,
                );
              }}
            >
              {showParticipations ? <span>Hide</span> : <span>View</span>}
              <ChevronDown className={twMerge("size-4 transition-transform", !!showParticipations && "rotate-180")} />
            </button>
          </div>
        </Fragment>
      )}
      <div className="col-span-2 overflow-auto">
        <div className={twMerge(!!showParticipations && "rounded-md border my-3 border-slate-200 pb-3")}>
          {!!showParticipations && (
            <ActionForm className="w-full">
              <AnimatingDiv
                className={"grid gap-y-2 gap-x-3 overflow-auto"}
                style={{ gridTemplateColumns: "auto auto auto minmax(auto, 1fr)" }}
              >
                <div className="pl-9 pt-2 font-semibold">Name</div>
                <div className="pt-2 font-semibold">Booking</div>
                <div className="pt-2 font-semibold">Activity</div>
                <div className="pt-2 font-semibold">Trips</div>
                {(!tripTotals.participants || !participations.length) && (
                  <div className="col-span-4 pl-9 bg-slate-50 py-2">
                    No participants {!participations.length ? "available" : "connected"}
                  </div>
                )}
                {processedParticipations.map((processed) => {
                  if (showParticipations < 2 && !processed.assignmentsForThisTrip.length) return <Fragment key={processed.id} />;

                  return (
                    <div key={processed.id} className={twMerge("contents", !!processed.assignmentsForThisTrip.length && "bg-slate-100")}>
                      <div className="whitespace-nowrap pl-2">
                        {/*{diff.includes(processed.id) && `changed: do ${processed.assignmentsForThisTrip.length ? 'delete' : 'add'}`}*/}
                        {diff.includes(processed.id) && (
                          <Fragment>
                            {processed.assignmentsForThisTrip.length ? (
                              <Fragment>
                                {processed.assignmentsForThisTrip.map((assignment) => (
                                  <Fragment key={assignment?.id || ""}>
                                    <RInput table={"trip_assignment"} field={"id"} index={assignment.id} value={assignment?.id} />
                                    <OperationInput table={"trip_assignment"} index={assignment.id} value={"delete"} />
                                  </Fragment>
                                ))}
                              </Fragment>
                            ) : (
                              <Fragment>
                                <RInput
                                  table={"trip_assignment"}
                                  field={"data.trip_id"}
                                  index={processed.id}
                                  value={trip.id}
                                  type={"hidden"}
                                />
                                <RInput
                                  table={"trip_assignment"}
                                  field={"data.participation_id"}
                                  index={processed.id}
                                  value={processed.id}
                                  type={"hidden"}
                                />
                                <RInput
                                  table={"trip_assignment"}
                                  field={"data.role"}
                                  index={processed.id}
                                  value={"instructor" satisfies Role}
                                  type={"hidden"}
                                />
                              </Fragment>
                            )}
                          </Fragment>
                        )}

                        <button
                          type="button"
                          className={"group flex flex-row gap-2 items-center"}
                          aria-selected={selectedParticipations.includes(processed.id) || !!processed.participationTrips.length}
                          onClick={() => setSelectedParticipations(toggleArray(selectedParticipations, processed.id))}
                        >
                          <Checker
                            className={twMerge(
                              "border border-[#C7C7C7] group-aria-selected:bg-[#E8E6E6]",
                              selectedParticipations.includes(processed.id) &&
                                "group-aria-selected:border-primary-700 group-aria-selected:bg-primary",
                            )}
                          />
                          {processed.participant ? (
                            <span className={twMerge(processed.allParticipantParticipations.length > 1 && "font-semibold")}>
                              {processed.participant.first_name + " " + processed.participant.last_name}
                            </span>
                          ) : (
                            <span className="opacity-50">Not registered</span>
                          )}
                          {processed.allParticipantParticipations.length > 1 && (
                            <span className="text-xs">
                              {processed.participationIndex}/{processed.allParticipantParticipations.length}
                            </span>
                          )}
                          {processed.participationTrips.length > 1 && (
                            <span className="text-primary font-semibold">{processed.participationTrips.length}T</span>
                          )}
                        </button>
                      </div>
                      <div className="whitespace-nowrap">
                        {processed.booking?.booking_reference || processed.booking?.sqid || "booking"}
                      </div>
                      <div className="whitespace-nowrap">{processed.product && getProductTitle(processed.product)}</div>
                      <div>
                        <div className="flex flex-row gap-3">
                          {processed.participationTrips.map((tripAssignment) => {
                            const trip = trips.find((trip) => trip.id === tripAssignment.trip_id);

                            if (!trip) return <span key={tripAssignment.id}>{tripAssignment.id}</span>;

                            const tripTotals = getTripTotals(trip);

                            return (
                              <span key={tripAssignment.id} className="flex flex-row gap-2 items-center whitespace-nowrap">
                                <div
                                  className={twMerge(
                                    "h-3.5 w-3.5 rounded-full bg-green-500 text-white text-xs text-center",
                                    tripTotals.seatsTaken > 0 && "bg-primary",
                                    tripTotals.available <= 0 && "bg-red-500",
                                  )}
                                >
                                  {/*{establishment?.short || ""}*/}
                                </div>
                                {trip.start_time?.slice(0, 5)} {trip.activity_location} {trip.boat?.name} ({getTripType(trip?.type)?.name})
                              </span>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </AnimatingDiv>
              {hasChanges && (
                <div className="mt-4 flex justify-end px-3 gap-3">
                  <button type={"button"} className="btn" onClick={() => setSelectedParticipations(initialAssignedParticipations)}>
                    Cancel
                  </button>
                  <SubmitButton className="btn btn-primary">
                    {selectedParticipations.length > initialAssignedParticipations.length
                      ? `Assign ${selectedParticipations.length} participant${selectedParticipations.length !== 1 ? "s" : ""}`
                      : selectedParticipations.length < initialAssignedParticipations.length
                        ? `Remove ${initialAssignedParticipations.length - selectedParticipations.length} participant${initialAssignedParticipations.length - selectedParticipations.length !== 1 ? "s" : ""}`
                        : `Update participants`}
                  </SubmitButton>
                </div>
              )}
            </ActionForm>
          )}
        </div>
        {showParticipations === 1 && (
          <div>
            <button
              className="w-full text-center link pt-3 pb-5"
              onClick={() => {
                setShowParticipations(2);
              }}
            >
              Show all participants
            </button>
          </div>
        )}
      </div>
    </Fragment>
  );
};
