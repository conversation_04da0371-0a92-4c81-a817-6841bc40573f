import React, { Fragment, useEffect, useState } from "react";
import type { TripType } from "~/domain/planning/plannings-consts";
import { tripTypes, tripTypesList } from "~/domain/planning/plannings-consts";
import { RInput, RLabel, RSelect } from "~/components/ResourceInputs";
import { entries, fName } from "~/misc/helpers";
import { BoatSelect } from "~/domain/planning/BoatSelect";
import { usePlanningDayLoader } from "~/routes/_all._catch._w.planning._index";
import { flat, unique } from "remeda";
import { AddressInput, baseAutocompleteOptions } from "~/components/base/AddressInput";
import { defaultCountryCode } from "~/misc/vars";
import { Selectable } from "kysely";
import { Trip } from "~/kysely/db";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { AlertTriangleIcon } from "lucide-react";
// import { useBoolean } from "~/hooks/use-boolean";

export const SharedTripFields = (props: { identifier: string | number; trip?: Partial<Selectable<Trip>> | null }) => {
  const data = usePlanningDayLoader();
  const trip = props.trip;
  const search = useSearchParams2();
  // const showFieldUpdateMsg = useBoolean();
  const [capacity, setCapacity] = useState(typeof trip?.capacity === "number" ? trip.capacity + "" : null);
  const [tripTypeKey, setTripTypeKey] = useState<string | undefined>(trip?.type);
  const boatId = typeof search.state.boat_id === "string" ? search.state.boat_id : trip?.boat_id;
  const countryCodes = unique(data.establishments.map((establishment) => establishment.country_code || ""));
  const boats = flat(data.establishments.map((establishment) => establishment.boats));
  const boat = boats.find((boat) => boat.id === boatId);
  const boatCapacity = boat?.capacity;
  const finalTripTypeKey = tripTypesList.find((key) => key === tripTypeKey) || ("boat" satisfies TripType);
  const finalCapacity = typeof capacity === "string" ? capacity : boatCapacity ? boatCapacity + "" : "";

  useEffect(() => {
    if (!search.state.boat_id || !boat) return;
    // if (capacity !== null) {
    setCapacity(null);
    // }
    // if (capacity) {
    //   showFieldUpdateMsg.on();
    // }
  }, [search.state.boat_id]);

  const sites = props.trip?.sites;
  return (
    <Fragment>
      <div>
        <RLabel table={"trip"} field={"data.type"} index={props.identifier}>
          Type
        </RLabel>
        <br />
        <RSelect
          table={"trip"}
          field={"data.type"}
          index={props.identifier}
          className="select w-full"
          value={finalTripTypeKey}
          onChange={(e) => setTripTypeKey(e.target.value)}
        >
          {entries(tripTypes).map(([key, entry]) => (
            <option key={key} value={key}>
              {entry.name}
            </option>
          ))}
        </RSelect>
      </div>
      <div className="space-y-1">
        <RInput
          required
          table={"trip"}
          field={"data.activity_location"}
          index={props.identifier}
          label={"Trip Location / Title"}
          placeholder="Enter a trip location or title"
          className="input"
          defaultValue={trip?.activity_location || (finalTripTypeKey === "pool" || finalTripTypeKey === "dry" ? "Dive center" : "")}
          datalist={
            <Fragment>
              {(finalTripTypeKey === "pool" || finalTripTypeKey === "dry") && <option value="Dive center">Dive center</option>}
              {data.diving_locations.map((divingLocation) => (
                <option value={divingLocation.name} key={divingLocation.name}>
                  {divingLocation.name}
                </option>
              ))}
            </Fragment>
          }
        />
      </div>
      {finalTripTypeKey === "boat" ? (
        <BoatSelect name={fName("trip", "data.boat_id", props.identifier)} defaultValue={boatId} />
      ) : (
        <RInput table={"trip"} field={"data.boat_id"} index={props.identifier} value="" type={"hidden"} />
      )}
      <div className="space-y-1">
        <RLabel table={"trip"} field={"data.start_location"} index={props.identifier}>
          {finalTripTypeKey === "boat" ? "Boat location" : "Activity address"}
        </RLabel>
        <AddressInput
          autocompleteOptions={{
            ...baseAutocompleteOptions,
            componentRestrictions: { country: countryCodes[0] || defaultCountryCode },
          }}
          name={fName("trip", "data.start_location", props.identifier)}
          className="input"
          defaultValue={trip?.start_location || ""}
          placeholder={boat?.location || ""}
        />
      </div>
      {sites?.length &&
        sites.map((site, index) => (
          <RInput key={index} table={"trip"} field={"data.sites"} index={0} nameKeys={[index]} value={site || ""} type={"hidden"} />
        ))}
      <div className="space-y-1">
        <RLabel table={"trip"} field={"data.start_time"} index={props.identifier} className="required">
          {finalTripTypeKey === "boat" ? "Departure time" : "Start time"}
        </RLabel>
        <br />
        <RInput
          required
          table={"trip"}
          field={"data.start_time"}
          index={props.identifier}
          type={"time"}
          className={"input"}
          defaultValue={trip?.start_time || ""}
        />
      </div>
      <div>
        <div className="flex flex-wrap gap-3 items-center">
          <RLabel table={"trip"} field={"data.capacity"}>
            Max. capacity
          </RLabel>
          {/*{showFieldUpdateMsg.isOn && capacity === null && !!search.state.boat_id && (*/}
          {capacity === null && !!search.state.boat_id && (
            <span className="text-slate-600 inline-flex items-center gap-1 text-sm">
              <AlertTriangleIcon className="w-4 h-4" />
              Field Updated
            </span>
          )}
        </div>
        <RInput
          table={"trip"}
          field={"data.capacity"}
          index={props.identifier}
          type={"number"}
          className="input"
          onChange={(e) => {
            // showFieldUpdateMsg.off();
            setCapacity(e.target.value);
          }}
          value={finalCapacity}
        />
      </div>
    </Fragment>
  );
};
