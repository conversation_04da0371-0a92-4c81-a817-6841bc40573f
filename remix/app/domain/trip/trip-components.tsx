import type { Selectable } from "kysely";
import type { Member, Trip, TripAssignment } from "~/kysely/db";
import type { Role } from "~/domain/planning/plannings-consts";
import { unique } from "remeda";
import type { SerializeFrom } from "@remix-run/server-runtime";

export type TripModel = SerializeFrom<
  Selectable<Trip> & {
    address?: string | null;
    boat_name: string | null;
    assignments?: Array<Selectable<TripAssignment> & { member?: Selectable<Member> | null }>;
  }
>;

export const getTripTotals = (trip: TripModel) => {
  const totalCapacity = trip?.capacity || 0;
  const assignments = trip.assignments || [];
  const assignmentsWithoutCaptain = assignments.filter((assignment) => assignment.role !== ("captain" satisfies Role));
  const memberIds = unique(assignmentsWithoutCaptain.map((assignment) => assignment.member_id));
  const participationIds = unique(
    assignments.map((assignment) => assignment.participation_id).filter((participantId): participantId is string => !!participantId),
  );

  const totalSeatsTaken = participationIds.length + memberIds.length;
  const available = totalCapacity - totalSeatsTaken;

  return {
    capacity: totalCapacity,
    participants: participationIds.length,
    seatsTaken: totalSeatsTaken,
    available: available,
  };
};
