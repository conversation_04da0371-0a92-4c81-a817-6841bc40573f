import type { DivingSite } from "~/kysely/db";
import {
  BoatEntryIcon,
  CaveSiteOutlineIcon,
  MuckSiteIcon,
  ReefSiteIcon,
  ShoreEntryIcon,
  WallSiteIcon,
  WreckSiteIcon,
} from "~/components/Icons";
import { mapValueToRange } from "~/components/form/RangeInput";
import { capitalize } from "~/utils/formatters";

export const types = {
  wreck: {
    Icon: WreckSiteIcon,
  },
  cave: {
    Icon: CaveSiteOutlineIcon,
  },
  wall: {
    Icon: WallSiteIcon,
  },
  reef: {
    Icon: ReefSiteIcon,
  },
  muck: {
    Icon: MuckSiteIcon,
  },
} satisfies Partial<Record<keyof DivingSite, { Icon: unknown }>>;

export const tags = {
  open_water: "open water",
  night: null,
  drift: null,
  macro: null,
  exploration: null,
  slope: null,
  snorkeling: null,
  ice: null,
  technical: null,
  altitude: null,
} satisfies Partial<Record<keyof DivingSite, string | null>>;

export const accessChecks = {
  access_via_boat: {
    label: "boat",
    Icon: BoatEntryIcon,
  },
  access_via_shore: {
    label: "shore",
    Icon: ShoreEntryIcon,
  },
} satisfies Partial<Record<keyof DivingSite, { label: string; Icon: unknown }>>;

export const gradingDisclaimer = 'This ranking system is a guide only. Please always connect and get advice from the operator and dive instructors.'

export const difficultyLevels = [
  {
    color: "#0ab134",
    title: "Suitable for all levels",
    description: `Dive site is easily accessible; has little to no current and is perfectly suited for diving within 18 m/60 ft max depth
Great for (but not limited to) beginner divers
Recommended site for divers at any experience level`,
  },
  {
    color: "#f4c50d",
    title: "Experience required",
    description: `Access to site may be challenging; may experience light to moderate current and depth may extend beyond 18 m/60 ft max depth
May include drift or steep wall dives 
Site is for intermediate to experienced divers; suitable for open water certified divers, although with advanced training i.e., deep, drift, etc. and good buoyancy are preferred`,
  },
  {
    color: "#e37f09",
    title: "Challenging / advanced",
    description: `Access to the site may be difficult; may experience moderate to strong current and obstacles. The site’s bottom may exceed 30 m/100 ft depth.
Divers should have experience from multiple dives down 30 m/100 ft
Only for advanced certified divers with proven experience, including drift dives`,
  },
  {
    color: "#da4012",
    title: "Extremely difficult",
    description: `Access to site may be extremely difficult; may experience extremely strong and unpredictable (down) current and the site’s bottom may reach beyond 40 m/130 ft depth
Only for divers with exceptional buoyancy while diving in current and should have minimum 25 dives in the last year or 100 dives in the last 5 years
Rescue training highly recommended`,
  },
] satisfies ReadonlyArray<{ color: string; title: string; description: string }>;

export const units = {
  m: "meters",
  c: "°C",
  current: "current",
};

export const currents = ["none", "mild", "strong", "extreme"] as const;

export const ranges = {
  depth_range_in_meters: { unit: "m", label: "Depth range" },
  visibility_range_in_meters: { unit: "m", label: "Visibility range" },
  temperature_range_in_celsius: { unit: "c", label: "Temperature range" },
  current_range_in_level: {
    unit: "current",
    label: "Current range, " + currents.map((current, index) => index + " = " + current).join(", "),
  },
} satisfies Partial<Record<keyof DivingSite, { unit: keyof typeof units; label: string }>>;

export const currentRangeValueToText = (value?: string | null) => {
  const [lowest, highest] = mapValueToRange(value);
  const lower = currents.find((_, index) => index + "" === lowest);
  const high = currents.find((_, index) => index + "" === highest);

  if (!lower && !high) return "";
  if (lower && high && lower === high) return `${capitalize(lower)}`;
  if (lower && high) return `${capitalize(lower)} to ${capitalize(high)} `;
  if (lower) return `${capitalize(lower)} or higher`;
  if (high) return `${capitalize(high)} or lower`;
  return "";
};
