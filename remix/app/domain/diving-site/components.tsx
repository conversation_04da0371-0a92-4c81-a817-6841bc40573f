import React from "react";
import { accessChecks, difficultyLevels, types } from "~/domain/diving-site/diving-site";
import { toArray } from "~/misc/helpers";
import { twMerge } from "tailwind-merge";

export const DifficultyLevel = (props: { level?: number | null; className?: string }) => {
  return (
    <div className="flex flex-row items-center">
      {difficultyLevels.map((difficultyLevel, index) => {
        const level = index + 1;
        const isSelected = level === props.level;
        return (
          <div
            key={index}
            aria-selected={isSelected}
            className={`relative block py-1 px-3 text-white
                 ${index === 0 ? "rounded-l-md" : ""}
                 ${level === difficultyLevels.length ? "rounded-r-md" : ""}
                 ${isSelected ? "z-10 scale-x-125 scale-y-150 rounded-md font-bold ring-1 ring-white" : ""}
                 ${props.className ? props.className : ""}
                 `}
            style={{ backgroundColor: difficultyLevel.color }}
          >
            {level}
          </div>
        );
      })}
    </div>
  );
};

export const DivingSiteAccessEntries = (props: {
  divingsite: Record<keyof typeof accessChecks, boolean | null | undefined>;
  className?: string;
}) => {
  const selectedAccessEntries = toArray(accessChecks).filter((entry) => props.divingsite[entry.key]);
  const firstAccesEntry = selectedAccessEntries[0];
  return (
    <div className={twMerge("flex flex-row items-center gap-2", props.className)}>
      <div className="h-7 w-10 rounded-md bg-secondary-500 text-white">
        {firstAccesEntry && <firstAccesEntry.Icon className="h-full w-full text-white" />}
      </div>
      <div>
        <span className="capitalize">
          {selectedAccessEntries.map((entry) => entry.label).join(" & ")}
          {selectedAccessEntries.length === 0 && "unkown"}
        </span>
        &nbsp;entry
      </div>
    </div>
  );
};

export const DivingSiteTypes = (props: { divingsite: Record<keyof typeof types, boolean | null | undefined>; className?: string }) => {
  const selectedTypes = toArray(types).filter((type) => props.divingsite[type.key]);
  const firstType = selectedTypes[0];
  return (
    <div className={twMerge(`flex flex-row items-center gap-2`, props.className)}>
      <div className="h-7 w-10 rounded-md bg-secondary-500">{firstType && <firstType.Icon className="h-full w-full text-white" />}</div>
      <span className="capitalize">{selectedTypes.map((type) => type.key).join(", ")}</span>
    </div>
  );
};
