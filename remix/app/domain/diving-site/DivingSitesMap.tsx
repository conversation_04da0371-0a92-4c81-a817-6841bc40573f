import React, { Fragment, useEffect, useState } from "react";
import { MapPortal } from "~/components/MapPortal";
import { useMapContext } from "~/hooks/use-map-context";
import buffer from "@turf/buffer";
import { bbox, feature, featureCollection } from "@turf/turf";
import { useDivingLocationAndSites } from "~/routes/_all._catch.diving-location.$diving_location_id";
import { _diving_site } from "~/misc/paths";
import { MapMarker } from "~/components/map/Marker";
import type mapboxgl from "mapbox-gl";
import { ParamLink } from "~/components/meta/CustomComponents";

export const DivingSitesMap = () => {
  const { map } = useMapContext();
  const [zoom, setZoom] = useState<null | number>(null);

  const { sites, site } = useDivingLocationAndSites();
  const features = sites.filter((site) => site.geom && site.valid_point).map((site) => feature(site.geom, { id: site.id }));
  const collection = featureCollection(features);

  const fit = (map: mapboxgl.Map) => {
    const bufferedCollection =
      collection.features.length > 1
        ? collection
        : buffer(collection, 1, {
            units: "kilometers",
          });
    const [minLng, minLat, maxLng, maxLat] = bbox(bufferedCollection);
    try {
      map.fitBounds(
        [
          [minLng, minLat],
          [maxLng, maxLat],
        ],
        { padding: 40, duration: 0 },
      );
    } catch (e) {
      console.log("could not fit map, probably because one of the coordinates is invalid", features, e);
    }
  };

  useEffect(() => {
    if (map && collection.features.length > 0) {
      map.on("zoomend", (e) => {
        setZoom(e.target.getZoom());
      });
      setZoom(map.getZoom());
      fit(map);
    }
  }, [map, features.length]);

  return (
    <section className="app-container space-y-3">
      <h2 className="text-xl font-bold">Map</h2>
      <div
        className="h-[220px] overflow-hidden rounded-md md:h-[330px]"
        style={{
          position: "relative",
          width: "100%",
        }}
      >
        <MapPortal
          onFit={(map) => {
            if (features.length > 0) {
              fit(map);
            }
          }}
        >
          {sites.map((item) => {
            if (!item.geom || !item.valid_point) return <Fragment key={item.id} />;
            return (
              <MapMarker key={item.id} coordinates={item.geom.coordinates}>
                <div
                  className={`relative transition-transform transition-opacity
                ${!site ? "hover:scale-110" : ""}
             ${site && site !== item ? "opacity-50 hover:scale-110 hover:opacity-100" : ""}`}
                >
                  <ParamLink
                    path={_diving_site(item)}
                    className={`absolute flex h-4 w-4 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-red-500 `}
                  >
                    <div className="h-[2px] w-full rotate-[35deg] bg-white" />
                  </ParamLink>
                  <ParamLink
                    path={_diving_site(item)}
                    hidden={(zoom === null || zoom < 12.8) && item !== site}
                    className={"absolute -translate-x-1/2 translate-y-[4px] whitespace-nowrap text-black"}
                  >
                    {item.name}
                  </ParamLink>
                </div>
              </MapMarker>
            );
          })}
        </MapPortal>
      </div>
    </section>
  );
};
