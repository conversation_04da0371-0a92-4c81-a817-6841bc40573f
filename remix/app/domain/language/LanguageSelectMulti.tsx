import React, { Fragment, useEffect, useRef, useState } from "react";
import { languages } from "~/data/languages";
import { BlueTag } from "~/components/base/Tag";
import { AnimatingDiv } from "~/components/base/base";
import { twMerge } from "tailwind-merge";
import { tableIdRef } from "~/misc/helpers";
import { RInput } from "~/components/ResourceInputs";
import { OperationInput } from "~/components/form/DefaultInput";
import { setState, useSearchParams2 } from "~/hooks/use-search-params2";
import { useIsInterative } from "~/hooks/hooks";
import {
  FloatingFocusManager,
  FloatingOverlay,
  FloatingPortal,
  useDismiss,
  useFloating,
  useInteractions,
  useListNavigation,
  useRole,
  useTransitionStatus,
} from "@floating-ui/react";
import { toggleArray } from "~/misc/parsers/global-state-parsers";
import { ParamLink } from "~/components/meta/CustomComponents";
import { XMarkIcon } from "@heroicons/react/20/solid";
import { CgClose } from "react-icons/cg";

export const LanguageDialog = (props: { initialSelectedLanguageCode: string[] }) => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [searchValue, setSearchValue] = useState("");
  const search = useSearchParams2();
  const isInteractive = useIsInterative();
  const isMenuOpen = search.state.toggle_modal === "page_language";

  const searchInputRef = useRef<HTMLInputElement | null>(null);

  const listRef = useRef<HTMLAnchorElement[]>([]);

  const { refs, context, floatingStyles } = useFloating({
    open: isMenuOpen,
    strategy: "fixed",
    onOpenChange: (open) => {
      setState({ toggle_modal: open ? "page_language" : undefined }, { replaceRoute: true });
    },
  });

  const { isMounted, status } = useTransitionStatus(context, { duration: 200 });

  const filteredCountries = languages.filter(
    (country) =>
      !searchValue ||
      country.code.toLowerCase().startsWith(searchValue.toLowerCase()) ||
      country.name.toLowerCase().startsWith(searchValue.toLowerCase()),
  );

  const listNavigation = useListNavigation(context, { listRef: listRef, activeIndex, onNavigate: setActiveIndex });
  const dismiss = useDismiss(context, { outsidePressEvent: "pointerdown" });
  const role = useRole(context, { role: "dialog" });
  const { getReferenceProps, getFloatingProps, getItemProps } = useInteractions([dismiss, role, listNavigation]);

  const selectedLanguages = languages.filter((language) => {
    return props.initialSelectedLanguageCode.includes(language.code) !== search.state.language_codes.includes(language.code);
  });

  // in the case of ssr the status is always unmounted, but could still be opened, thats why the check status === "unmounted" is there.
  const isMountedAndOpen = (!isInteractive && isMenuOpen) || status === "open";

  useEffect(() => {
    setSearchValue("");
  }, [isMountedAndOpen]);

  if (!isMounted) return <Fragment />;

  return (
    <FloatingPortal>
      <FloatingFocusManager context={context} modal={true} initialFocus={-1}>
        <FloatingOverlay
          lockScroll
          className={twMerge("fixed inset-0 z-20 flex bg-black/0 transition-colors p-6", isMountedAndOpen && "bg-black/20")}
        >
          <div
            className={twMerge(
              "left-0 z-30 m-auto max-w-md rounded-2xl bg-white p-6 align-middle opacity-0 shadow-2xl transition-all ease-in-out",
              isMountedAndOpen && "opacity-100",
              "h-full md:h-96 w-full",
            )}
          >
            <div className="space-y-3 relative h-full flex flex-col">
              <div className="flex flex-row gap-3 justify-between items-center">
                <p className="text-xl text-slate-600">Select language</p>
                <ParamLink
                  paramState={{ toggle_modal: undefined }}
                  aria-label="close"
                  className={"inline-block rounded-xl p-2 text-right hover:bg-slate-100 active:bg-slate-100"}
                >
                  <XMarkIcon className="h-5 w-5" />
                </ParamLink>
              </div>
              <div className="sticky top-0">
                <input
                  className="input"
                  ref={(e) => {
                    refs.setReference(e);
                    searchInputRef.current = e;
                  }}
                  {...getReferenceProps}
                  type={"search"}
                  placeholder={"search"}
                  onKeyDown={(e) => {
                    if (e.key === "ArrowDown") {
                      setActiveIndex((activeIndex || 0) + 1);
                    }
                    if (e.key === "Enter") {
                      // prevent form submit when selecting a suggested place by google maps by pressing enter
                      e.preventDefault();
                    }
                  }}
                  value={searchValue}
                  onChange={(e) => {
                    setSearchValue(e.target.value);
                    setActiveIndex(0);
                  }}
                />
              </div>
              <div className="flex-1 overflow-y-scroll relative" {...getFloatingProps()} ref={refs.setFloating}>
                {filteredCountries.map((item, index) => {
                  if (selectedLanguages.includes(item))
                    return (
                      <span key={item.code} className="block p-1 opacity-50" {...getItemProps({ disabled: true })}>
                        {item.name}
                      </span>
                    );

                  return (
                    <ParamLink
                      key={item.code}
                      ref={(node) => {
                        listRef.current[index] = node!;
                        // languageListRef.current[index] = item.code;
                      }}
                      aria-selected={activeIndex === index}
                      tabIndex={activeIndex === index ? 0 : -1}
                      className="block aria-selected:bg-slate-200 hover:bg-slate-100 p-1 aria-selected:outline-none"
                      paramState={{
                        toggle_modal: undefined,
                        language_codes: toggleArray(search.state.language_codes, item.code),
                      }}
                      {...getItemProps()}
                    >
                      <span>{item.name}</span>
                    </ParamLink>
                  );
                })}
              </div>
            </div>
          </div>
        </FloatingOverlay>
      </FloatingFocusManager>
    </FloatingPortal>
  );
};

export const LanguageSelectMulti = (props: { label: string; initialLanguageCodes: string[] }) => {
  const search = useSearchParams2();
  const selectedLanguages = languages.filter((language) => {
    return props.initialLanguageCodes.includes(language.code) !== search.state.language_codes.includes(language.code);
  });
  const languagesToRemove = props.initialLanguageCodes.filter(
    (languageCode) => !selectedLanguages.find((language) => language.code === languageCode),
  );
  return (
    <div className="space-y-3">
      <LanguageDialog initialSelectedLanguageCode={props.initialLanguageCodes || []} />
      <label className="fieldcontrol flex flex-wrap items-center gap-2">
        <span>{props.label}</span>
        {languagesToRemove.map((languageCode) => (
          <Fragment key={languageCode}>
            <RInput
              table={"establishment__language"}
              field={"data.establishment_id"}
              index={languageCode}
              type={"hidden"}
              value={tableIdRef("establishment")}
            />
            <RInput
              table={"establishment__language"}
              field={"data.language_code"}
              index={languageCode}
              value={languageCode}
              type={"hidden"}
            />
            <OperationInput table={"establishment__language"} index={languageCode} value={"delete"} />
          </Fragment>
        ))}
      </label>
      <AnimatingDiv className={twMerge("flex w-fit flex-wrap gap-3 rounded-md bg-secondary-50 p-3")}>
        {selectedLanguages.map((language) => {
          return (
            <BlueTag key={language.code} className="gap-1 items-center">
              {language?.name}
              <ParamLink paramState={{ language_codes: toggleArray(search.state.language_codes, language.code) }}>
                <CgClose />
              </ParamLink>
              <RInput
                table={"establishment__language"}
                field={"data.establishment_id"}
                index={language.code}
                value={tableIdRef("establishment")}
                type={"hidden"}
              />
              <RInput
                table={"establishment__language"}
                field={"data.language_code"}
                index={language.code}
                value={language.code}
                type={"hidden"}
              />
            </BlueTag>
          );
        })}
        <ParamLink
          paramState={{ toggle_modal: "page_language" }}
          className="p-1 text-slate-700 hover:text-black transition-all px-3 border-slate-300 hover:border-slate-400 border rounded-md"
        >
          add a language...
        </ParamLink>
      </AnimatingDiv>
    </div>
  );
};
