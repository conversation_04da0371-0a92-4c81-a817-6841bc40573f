import React, { Fragment, useEffect, useRef, useState } from "react";
import { languages } from "~/data/languages";
import { BlueTag } from "~/components/base/Tag";
import { AnimatingDiv } from "~/components/base/base";
import { twMerge } from "tailwind-merge";
import { tableIdRef } from "~/misc/helpers";
import { RInput } from "~/components/ResourceInputs";
import { OperationInput } from "~/components/form/DefaultInput";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { Composite, CompositeItem } from "@floating-ui/react";
import { toggleArray } from "~/misc/parsers/global-state-parsers";
import { ParamLink } from "~/components/meta/CustomComponents";
import { XMarkIcon } from "@heroicons/react/20/solid";
import { CgClose } from "react-icons/cg";
import { CDialog } from "~/components/base/Dialog";

export const LanguageDialog = (props: { initialSelectedLanguageCode: string[] }) => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [searchValue, setSearchValue] = useState("");
  const search = useSearchParams2();
  const searchRef = useRef<HTMLInputElement>(null);

  const filteredCountries = languages.filter(
    (country) =>
      !searchValue ||
      country.code.toLowerCase().startsWith(searchValue.toLowerCase()) ||
      country.name.toLowerCase().startsWith(searchValue.toLowerCase()),
  );

  const selectedLanguages = languages.filter((language) => {
    return props.initialSelectedLanguageCode.includes(language.code) !== search.state.language_codes.includes(language.code);
  });

  useEffect(() => {
    searchRef.current?.focus();
  }, []);

  return (
    <div className="space-y-3 relative h-full  flex flex-col w-full">
      <div className="flex flex-row gap-3 justify-between items-center">
        <p className="text-xl text-slate-600">Select language</p>
        <ParamLink
          paramState={{ toggle_modal: undefined }}
          aria-label="close"
          className={"inline-block rounded-xl p-2 text-right hover:bg-slate-100 active:bg-slate-100"}
        >
          <XMarkIcon className="h-5 w-5" />
        </ParamLink>
      </div>
      <div className="sticky top-0 bg-white">
        <input
          ref={searchRef}
          className="input"
          type={"search"}
          placeholder={"search"}
          onKeyDown={(e) => {
            if (e.key === "ArrowDown") {
              setActiveIndex((activeIndex || 0) + 1);
            }
            if (e.key === "Enter") {
              // prevent form submit when selecting a suggested place by google maps by pressing enter
              e.preventDefault();
            }
          }}
          value={searchValue}
          onChange={(e) => {
            setSearchValue(e.target.value);
            setActiveIndex(0);
          }}
        />
      </div>
      <Composite className="flex-1 overflow-y-scroll relative">
        {filteredCountries.map((item, index) => {
          if (selectedLanguages.includes(item))
            return (
              <CompositeItem key={item.code} disabled={true} className="block p-1 opacity-50">
                {item.name}
              </CompositeItem>
            );

          return (
            <CompositeItem key={item.code}>
              {/*{item.name}*/}
              <ParamLink
                key={item.code}
                aria-selected={activeIndex === index}
                tabIndex={activeIndex === index ? 0 : -1}
                className="block aria-selected:bg-slate-200 hover:bg-slate-100 p-1 aria-selected:outline-none"
                paramState={{
                  toggle_modal: undefined,
                  language_codes: toggleArray(search.state.language_codes, item.code),
                }}
              >
                <span>{item.name}</span>
              </ParamLink>
            </CompositeItem>
          );
        })}
      </Composite>
    </div>
  );
};

export const LanguageSelectMultiSimple = (props: { label: string; initialLanguageCodes: string[] }) => {
  const search = useSearchParams2();
  const selectedLanguages = languages.filter((language) => {
    return props.initialLanguageCodes.includes(language.code) !== search.state.language_codes.includes(language.code);
  });
  const languagesToRemove = props.initialLanguageCodes.filter(
    (languageCode) => !selectedLanguages.find((language) => language.code === languageCode),
  );
  return (
    <div className="space-y-3">
      <CDialog dialogname={"page_language"} className="h-full w-full md:h-96">
        <LanguageDialog initialSelectedLanguageCode={props.initialLanguageCodes || []} />
      </CDialog>
      <label className="fieldcontrol flex flex-wrap items-center gap-2">
        <span>{props.label}</span>
        {languagesToRemove.map((languageCode) => (
          <Fragment key={languageCode}>
            <RInput
              table={"establishment__language"}
              field={"data.establishment_id"}
              index={languageCode}
              type={"hidden"}
              value={tableIdRef("establishment")}
            />
            <RInput
              table={"establishment__language"}
              field={"data.language_code"}
              index={languageCode}
              value={languageCode}
              type={"hidden"}
            />
            <OperationInput table={"establishment__language"} index={languageCode} value={"delete"} />
          </Fragment>
        ))}
      </label>
      <AnimatingDiv className={twMerge("flex w-fit flex-wrap gap-3 rounded-md bg-secondary-50 p-3")}>
        {selectedLanguages.map((language) => {
          return (
            <BlueTag key={language.code} className="gap-1 items-center">
              {language?.name}
              <ParamLink paramState={{ language_codes: toggleArray(search.state.language_codes, language.code) }}>
                <CgClose />
              </ParamLink>
              <RInput
                table={"establishment__language"}
                field={"data.establishment_id"}
                index={language.code}
                value={tableIdRef("establishment")}
                type={"hidden"}
              />
              <RInput
                table={"establishment__language"}
                field={"data.language_code"}
                index={language.code}
                value={language.code}
                type={"hidden"}
              />
            </BlueTag>
          );
        })}
        <ParamLink
          paramState={{ toggle_modal: "page_language" }}
          className="p-1 text-slate-700 hover:text-black transition-all px-3 border-slate-300 hover:border-slate-400 border rounded-md"
        >
          add a language...
        </ParamLink>
      </AnimatingDiv>
    </div>
  );
};
