import { z } from "zod";
import { signatureParser } from "~/components/field/signature/signature-parser";
import { arrayAgg, nowValue } from "~/kysely/kysely-helpers";

import { allowedParticipantIdsFor } from "~/domain/participant/participant-auth-queries.server";
import { updateParticipantCache } from "~/domain/participant/participant-resource";
import { Args } from "~/server/resource/resource-helpers.server";

// const auths = {
//   participant_waiver: (args: AuthArgs) =>
//     args.trx
//       .selectFrom("participant_waiver")
//       .where("participant_waiver.participant_id", "in", myRegisteredParticipants(args).select("_participant.id"))
//       .select("participant_waiver.id"),
//   participant_medical: (args: AuthArgs) => myRegisteredParticipants(args).select("_participant.id"),
// } satisfies Record<SignatureTarget, unknown>;

const signatureValue = z
  .string()
  .transform((str) => JSON.parse(str))
  .pipe(signatureParser.strict())
  .transform((obj) => JSON.stringify(obj))
  .nullish();

export const signatureResource: Args<"signature"> = {
  authorize: async (args) => {
    const signature = await args.trx
      .selectFrom("signature")
      .where("signature.id", "=", args.id)
      .innerJoin("participant_waiver", "signature.participant_waiver_id", "participant_waiver.id")
      .innerJoin("customer", "customer.id", "participant_waiver.customer_id")
      .innerJoin("establishment", "establishment.id", "customer.establishment_id")
      .select((eb) => [
        "participant_waiver.participant_id",
        "participant_waiver.customer_id",
        eb
          .selectFrom("participant")
          .where("participant.customer_id", "=", eb.ref("customer.id"))
          .select((eb) => arrayAgg(eb.ref("participant.id"), "uuid").as("ids"))
          .as("participant_ids"),
      ])
      .where((eb) => {
        const allowedParticipantCmpr = eb(
          "participant_waiver.participant_id",
          "in",
          allowedParticipantIdsFor(args).select("_participant.id"),
        );
        const digitalSigningAgreed = eb
          .selectFrom("participant")
          .where("participant.id", "=", eb.ref("participant_waiver.participant_id"))
          .select("participant.digital_signing_agreed_at");
        const digitalSigningAgreedCmpr = eb.or([
          eb("establishment.require_email_verification_for_signing", "=", false),
          eb(digitalSigningAgreed, "is not", null),
        ]);
        if (args.action === "delete") return allowedParticipantCmpr;
        return eb.and([allowedParticipantCmpr, digitalSigningAgreedCmpr]);
      })
      .executeTakeFirst();

    signature?.participant_ids?.map((participantId) => {
      args.after_mutations.insideTransaction.set(updateParticipantCache.name + participantId, () =>
        updateParticipantCache(args.trx, participantId),
      );
    });

    return signature;
  },
  insert: (args) => {
    return {
      ...args.data,
      signed_at: nowValue,
      signature: signatureValue.parse(args.data.signature),
    };
  },
  update: () => false,
  delete: () => true,
};
