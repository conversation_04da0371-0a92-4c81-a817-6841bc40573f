import { Member } from "~/kysely/db";

export const checks: Partial<
  Record<
    keyof Member,
    {
      label: string;
      options: string[] | null;
    }
  >
> = {
  owner: { label: "Owner", options: null },
  admin: { label: "Role", options: ["none", "Staff+", "Manager"] },
  diving_level: { label: "Diving level", options: ["none", "Divemaster", "Instructor"] },
  // read: "Staff+",
  // instructor: "Instructor",
  crew: { label: "Crew", options: null },
  captain: { label: "Boat captain", options: null },
  freelancer: { label: "Freelancer", options: null },
};
