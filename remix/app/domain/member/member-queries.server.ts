import type { <PERSON>, Member } from "~/kysely/db";
import type { Kysely } from "kysely";
import { atInfinityLiteralFn } from "~/kysely/kysely-helpers";
import { AdminLevel, getAdminLevelIndex } from "~/domain/member/member-vars";
import { PermissionKey } from "~/domain/permission/permission";
import { at_infinity_value } from "~/kysely/db-static-vars";

const roles = ["owner", "captain", "diving_level", "admin", "crew"] satisfies Array<keyof Member>;
type Role = (typeof roles)[number];

export type QbArgs = {
  trx: Kysely<DB>;
  ctx: {
    session_id: string | null;
  };
};

export const toArgs = (trx: Kysely<DB>, session_id: string | null): QbArgs => ({
  trx: trx,
  ctx: {
    session_id: session_id,
  },
});

export const activeSession = (db: Kysely<DB>, session_id: string | null) =>
  db.selectFrom("session as _session").where("_session.id", "=", session_id || null);

export const activeUserSessionSimple = (db: Kysely<DB>, session_id: string | null, verified: boolean) =>
  activeSession(db, session_id)
    .innerJoin("user_session as _user_session", "_user_session.session_id", "_session.id")
    .leftJoin("user as _user", "_user.id", "_user_session.user_id")
    .where("_user_session.destroyed_at", "=", atInfinityLiteralFn)
    .where((eb) =>
      eb.or([
        eb.and([eb("_user_session.user_id", "is", null), eb("_session.selected_user_id", "is", null)]),
        eb.and([eb("_user.deleted_at", "=", atInfinityLiteralFn), eb("_session.selected_user_id", "=", eb.ref("_user.id"))]),
      ]),
    )
    .$if(verified, (eb) => eb.where("_user_session.verified_at", "is not", null))
    .orderBy("_user_session.created_at");

export const activeUserSessionQb = (args: QbArgs, verified: boolean) => activeUserSessionSimple(args.trx, args.ctx.session_id, verified);

export const isEditorQb = (args: QbArgs) => activeUserSessionSimple(args.trx, args.ctx.session_id, true).where("_user.editor", "=", true);

export const userSessionId = (args: QbArgs, verified: boolean = false) => activeUserSessionQb(args, verified).select("_user_session.id");

export const memberQb = (args: QbArgs) =>
  activeUserSessionQb(args, true)
    .innerJoin("member as _member", "_member.user_id", "_user.id")
    .where("_member.deleted_at", "=", at_infinity_value);

export const memberIsAdminQb = (args: QbArgs, minLevel: AdminLevel = "write") =>
  memberQb(args).where("_member.admin", ">=", getAdminLevelIndex(minLevel));

export const memberIsAdminOrOwnerQb = (args: QbArgs, minLevel: AdminLevel = "write") =>
  memberQb(args).where((eb) => eb.or([eb("_member.admin", ">=", getAdminLevelIndex(minLevel)), eb("_member.owner", "=", true)]));

export const memberHasPermission = (args: QbArgs, permission: PermissionKey) =>
  memberQb(args).where((eb) => eb(eb.val(permission), "=", eb.fn.any("_member.permissions") as any));
