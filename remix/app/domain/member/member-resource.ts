import { isEditorQb, memberQb, userSessionId } from "~/domain/member/member-queries.server";
import { removeObjectKeys } from "~/misc/helpers";
import { at_now_value } from "~/kysely/db-static-vars";
import { nowValue } from "~/kysely/kysely-helpers";
import { Args, bustCacheAfter } from "~/server/resource/resource-helpers.server";
import { getCacheKey } from "~/server/cache/cache.planning.server";
import { v4 } from "uuid";
import { getAdminLevelIndex } from "~/domain/member/member-vars";

export const memberResource: Args<"member"> = {
  authorize: () => true,
  beforeMutate: async (args) => {
    if (args.operation === "delete" && args.id) {
      const total = await args.trx
        .selectFrom("trip_assignment")
        .select((eb) => eb.fn.count<number>("trip_assignment.id").as("count"))
        .where("trip_assignment.member_id", "=", args.id)
        .executeTakeFirstOrThrow();
      if (total.count) {
        return {
          id: args.id,
          operation: "update",
          data: {
            deleted_at: at_now_value,
          },
        };
      }
    }
    return {
      id: args.operation === "insert" && !args.id ? v4() : args.id,
      data: args.data,
      operation: args.operation,
    };
  },
  insert: (args) => removeObjectKeys(args.data, "deleted_at", "deleted_by_user_session_id"),
  update: (args) => {
    if (args.data.deleted_at === at_now_value)
      return {
        deleted_at: nowValue,
        deleted_by_user_session_id: userSessionId(args),
      };
    return removeObjectKeys(args.data, "establishment_id", "deleted_at", "deleted_by_user_session_id");
  },
  delete: () => true,
  onChanged: async (args) => {
    const establishmentId = args.diff.after?.establishment_id || args.diff.before?.establishment_id;
    if (!establishmentId) return "require establishment_id to do auth check";
    const diff = args.diff.diff || ({} as const);
    // const isChanged = (key: keyof Member) => key in diff;

    const iAmEditor = await isEditorQb(args).executeTakeFirst();
    const iAmMember = await memberQb(args).selectAll("_member").where("_member.establishment_id", "=", establishmentId).executeTakeFirst();

    if (diff.owner && !iAmEditor) {
      return "Only DD Editor is allowed to set owner";
    }

    if (diff.permissions?.length && !iAmMember?.owner && !iAmEditor) {
      return "Only Owner is allowed to set metrics";
    }

    if (establishmentId) {
      bustCacheAfter(args, getCacheKey({ establishmentId: establishmentId }));
    }
    return iAmMember?.admin === getAdminLevelIndex("write") || !!iAmEditor;
  },
};
