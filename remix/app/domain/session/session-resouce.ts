import { ResourceError } from "~/utils/error";
import { appConfig } from "~/config/config.server";
import { atInfinityLiteralFn, nowValue } from "~/kysely/kysely-helpers";
import { sql } from "kysely";
import { Args } from "~/server/resource/resource-helpers.server";

export const sessionResource: Args<"session"> = {
  authorize: ({ ctx, id }) => ctx.session_id === id,
  // beforeMutate: async (args) => ({ id: args.ctx.session_id, operation: "update" }),
  // beforeMutate: async (args) => ({ id: args.ctx.session_id, operation: args.operation === "update" ? args.operation : "ignore" }),
  beforeMutate: async (args) => ({ id: args.ctx.session_id, operation: args.data ? "update" : "ignore" }),
  disableAudit: () => true,
  insert: (args) => false,
  update: (args) => {
    let envVerified = args.data.env_verified as any;
    if (envVerified) {
      if (envVerified !== appConfig.ENV_SECRET) {
        throw new ResourceError("Invalid secret");
      }
      envVerified = true;
    }

    return {
      env_verified: envVerified,
      features: args.data.features,
      selected_user_id: args.data.selected_user_id,
      currency_switched: args.data.currency_switched,
      main_menu_pinned: args.data.main_menu_pinned,
      // flash_success_message: args.data.flash_success_message,
    };
  },
  onChanged: async (args) => {
    const changedUserId = args.diff.diff?.selected_user_id;
    if (changedUserId) {
      await args.trx
        .updateTable("session")
        .set((eb) => ({
          flash_success_message: eb
            .selectFrom("user")
            .where("user.id", "=", eb.ref("session.selected_user_id"))
            .select((eb) => sql<string>`'Logged in as ' || ${eb.ref("user.email")}`.as("msg")),
        }))
        .where("session.id", "=", args.id)
        .executeTakeFirstOrThrow();
    }
    return true;
  },
  delete: () => false,
};

export const sessionLinkResource: Args<"session_link"> = {
  authorize: ({ ctx, id }) => true,
  disableAudit: () => true,
  insert: async (args) => {
    const participantId = args.data.participant_id;
    if (!participantId) throw new ResourceError("participant_id required");
    const otp = await args.trx
      .selectFrom("one_time_password")
      .innerJoin("user", "user.id", "one_time_password.user_id")
      .innerJoin("person", "person.user_id", "user.id")
      .innerJoin("customer", "customer.person_id", "person.id")
      .innerJoin("participant", "participant.customer_id", "customer.id")
      .where("user.deleted_at", "=", atInfinityLiteralFn)
      .where("one_time_password.verified_at", "=", nowValue)
      .where("one_time_password.session_id", "=", args.ctx.session_id)
      .where("participant.id", "=", participantId)
      .executeTakeFirst();
    if (!otp) throw new ResourceError("Cannot create session link, invalid OTP");
    return {
      session_id: args.ctx.session_id,
      participant_id: participantId,
      created_at: nowValue,
    };
  },
  update: () => false,
  delete: () => false,
};
