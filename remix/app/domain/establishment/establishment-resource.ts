import { activeUserSessionQb, memberIsAdminOrOwnerQb } from "~/domain/member/member-queries.server";
import type { Establishment } from "~/kysely/db";
import { jsonObjectFrom } from "kysely/helpers/postgres";
import { ResourceError } from "~/utils/error";
import { v4 } from "uuid";
import { Operation } from "~/components/form/DefaultInput";
import { Args } from "~/server/resource/resource-helpers.server";

const ownerAllowedFields: Array<keyof Establishment> = [
  "whatsapp",
  "address",
  "email",
  "telephone",
  "website",
  "bio",
  "about",
  "require_email_verification_for_signing",
  "default_currency",
  "default_shoe_size_unit",
  "default_height_unit",
  "default_weight_unit",
  "booking_message_templates",
  "whatsapp_message_template",
  "default_trip_start_time",
  "default_booking_meeting_time",
  "default_booking_meeting_type",
  "activity_reminder_in_days_before_start",
  "notification_email",
  "tanks",
  'blends'
];

export const establishmentResource: Args<"establishment"> = {
  authorize: (args) =>
    activeUserSessionQb(args, true)
      .$if(args.action !== "update", (eb) => eb.where("_user.editor", "=", true))
      .executeTakeFirst(),
  insert: (args) => args.data,
  update: async (args) => {
    // args.callbacks.push({ name: "establishment_mutation", target_id: args.id });
    const acitveUser = await activeUserSessionQb(args, true)
      .select((eb) => [
        "_user.editor",
        jsonObjectFrom(
          eb
            .selectFrom("member")
            .whereRef("member.user_id", "=", "_user.id")
            .where("member.establishment_id", "=", args.id)
            .where((eb) => eb.or([eb("member.admin", ">", 1), eb("member.owner", "=", true)]))
            .limit(1)
            .select("member.id"),
        ).as("valid_member"),
      ])
      .executeTakeFirst();
    if (acitveUser?.editor) return args.data;
    if (acitveUser?.valid_member) {
      return ownerAllowedFields.reduce((acc, field) => ({ ...acc, [field]: args.data[field] }), {});
    }
    return false;
  },
  delete: (args) => true,
};

export const establishmentLanguageResource: Args<"establishment__language"> = {
  authorize: (args) =>
    args.trx
      .selectFrom("establishment__language")
      .where("establishment__language.id", "=", args.id)
      .where((eb) =>
        eb.or([
          eb.exists(activeUserSessionQb(args, true).where("_user.editor", "=", true)),
          eb("establishment__language.establishment_id", "in", memberIsAdminOrOwnerQb(args).select("_member.establishment_id")),
        ]),
      )
      .executeTakeFirst(),
  beforeMutate: async (args) => {
    const establishmentId = args.data?.establishment_id;
    const languageCode = args.data?.language_code;
    if (!establishmentId || !languageCode)
      throw new ResourceError("EstablishmentId and language code are required for establishmentLanguageResource");

    const existing = await args.trx
      .selectFrom("establishment__language")
      .select("establishment__language.id")
      .where("establishment__language.establishment_id", "=", establishmentId)
      .where("establishment__language.language_code", "=", languageCode)
      .executeTakeFirst();

    const insertOrUpdate = (["insert", "update"] satisfies Operation[]).includes(args.operation);

    return existing
      ? { id: existing.id, operation: insertOrUpdate ? "ignore" : args.operation }
      : { id: v4(), operation: insertOrUpdate ? "insert" : "ignore" };
  },
  insert: (args) => args.data,
  update: (args) => false,
  delete: () => true,
};
