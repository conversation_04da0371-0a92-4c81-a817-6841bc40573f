import { IkImage } from "~/components/IkImage";
import { IoLocationSharp } from "react-icons/io5";
import React, { Fragment } from "react";
import { ratingsToAvg, StarsOrangeSmall } from "~/components/field/rating/rating-input";

interface EstablishmentModel {
  id: string;
  files?: { filename: string }[] | null;
  operator_name: string;
  location_name?: string | null;
  ratings?: number[] | null;
  spot_name?: string | null;
  bio?: string | null;
}

export const EstablishmentItem = (props: { item: EstablishmentModel }) => {
  const item = props.item;
  return (
    <Fragment>
      <div className="h-[150px] overscroll-none">
        <IkImage path={item.files?.[0]?.filename} w={540} h={150} className="h-[150px] w-full object-cover md:rounded-md" />
      </div>
      <div className="flex-col p-3 px-3">
        <div className="flex flex-row items-center justify-between gap-1">
          <h4 className="text-xl font-bold line-clamp-1">
            {item.operator_name}
            {!!item.location_name && " - " + item.location_name}
          </h4>
          <p className="btn btn-primary w-fit p-1 px-3">View</p>
        </div>
        {item.bio && (
          <div className="py-2">
            <span className={"block text-xs text-slate-600 line-clamp-2"}>{item.bio}</span>
          </div>
        )}
        <div className="flex flex-row items-center justify-between gap-3">
          {item.ratings?.length ? (
            <div className="flex flex-row text-sm text-slate-500">
              <StarsOrangeSmall rating={ratingsToAvg(item.ratings)} />
              &nbsp;({item.ratings.length})
            </div>
          ) : (
            <div />
          )}
          <div className="flex flex-row items-center">
            <IoLocationSharp />
            <span className={"font-semibold line-clamp-1"}>{item.spot_name || "unspecified"}</span>
          </div>
        </div>
      </div>
    </Fragment>
  );
};
