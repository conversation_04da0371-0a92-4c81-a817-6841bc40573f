import { FaPhoneAlt, FaWhatsapp } from "react-icons/fa";
import { t } from "~/misc/trans";
import { MdEmail } from "react-icons/md";
import { TbWorld } from "react-icons/tb";
import { Establishment } from "~/kysely/db";

export const contactMetas = {
  whatsapp: {
    Icon: FaWhatsapp,
    label: t`Whatsapp`,
  },
  telephone: {
    Icon: FaPhoneAlt,
    label: t`Telephone`,
  },
  email: {
    Icon: MdEmail,
    label: t`Email`,
  },
  website: {
    Icon: TbWorld,
    label: t`Website`,
  },
} satisfies Partial<Record<keyof Establishment, unknown>>;

export type ContactMetaKey = keyof typeof contactMetas;

export function orNull<T>(value: T | null): T | null {
  return value ?? null;
}

export const getContactMetaKey = (str?: string | null) => {};

export const getContactMeta = (str?: string | null) => {
  const contactMetaKey = str as ContactMetaKey;
  const value = orNull(contactMetas[contactMetaKey]);
  return (
    value && {
      key: contact<PERSON>eta<PERSON><PERSON>,
      value: value,
    }
  );
};
