import React from "react";

export const EstablishmentSeperator = (props: {
  establishment: {
    spot_name?: string | null;
    short?: string | null;
    location_name?: string | null;
  };
}) => {
  return (
    <div className="flex flex-row gap-2 items-center ">
      <span className="bg-primary rounded-md py-1 px-2 text-xs text-white">{props.establishment.short || "?"}</span>
      <span className="text-xl">
        {props.establishment.spot_name} {props.establishment.location_name ? ` - ` + props.establishment.location_name : ""}
      </span>
    </div>
  );
};
