export const directBookingOptions = ["Disabled", "Simple form", "Normal Registration"] as const;
export const workflowOptions = ["Disabled", "CRM", "CRM + Planning"] as const;

interface OperatorLocation {
  establishment_name?: string | null;
  operator_name: string;
  spot_name?: string | null;
  location_name?: string | null;
}

export const getEstablishmentShort = (item: OperatorLocation) => {
  const locationName = item.establishment_name ? " - " + item.establishment_name : "";
  const spotName = item.spot_name ? ", " + item.spot_name : "";
  return item.operator_name + locationName + spotName;
};

export const getEstablishmentName = (item?: OperatorLocation | null) =>
  item ? item.operator_name + " - " + (item.spot_name || "unkown") + (item.location_name ? " - " + item.location_name : "") : "unkown";
