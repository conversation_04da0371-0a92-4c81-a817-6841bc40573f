import type { Establishment } from "~/kysely/db";
import { z } from "zod";
import { NEGATIVES, POSITIVES } from "~/domain/review/review-data";
import { keys } from "~/misc/helpers";
import { activeUserSessionQb, userSessionId } from "~/domain/member/member-queries.server";
import { nowValue } from "~/kysely/kysely-helpers";
import { Args } from "~/server/resource/resource-helpers.server";

const allowedCommunicatioMethods = ["whatsapp", "email"] satisfies Array<keyof Establishment>;

export const inquiryResource: Args<"inquiry"> = {
  authorize: (args) => userSessionId(args).where('_user_session.user_id', 'is not', null).executeTakeFirst(),
  insert: (args) => {
    if (allowedCommunicatioMethods.includes(args.data.communication_method as string))
      return {
        ...args.data,
        created_by_user_session_id: userSessionId(args),
      };
    return false;
  },
  update: () => false,
  delete: () => true,
};

const reviewInputParser = z
  .object({
    positives: z
      .any()
      .array()
      .transform((arr) => keys(POSITIVES).filter((key) => arr.includes(key))),
    negatives: z
      .any()
      .array()
      .transform((arr) => keys(NEGATIVES).filter((key) => arr.includes(key))),
  })
  .partial()
  .passthrough();

export const reviewResource: Args<"review"> = {
  authorize: (args) => {
    const requiredEditor = args.action !== "insert";
    return (
      activeUserSessionQb(args, requiredEditor)
        .$if(requiredEditor, (eb) => eb.where("_user.editor", "=", true))
        // .$if(args.trigger === "after" && , )
        .executeTakeFirst()
    );
  },
  insert: (args) => {
    return (
      reviewInputParser.parse(args.data) && {
        ...args.data,
        created_by_user_session_id: userSessionId(args),
      }
    );
  },
  update: (args) => {
    return (
      reviewInputParser.parse(args.data) &&
      args.data && {
        ...args.data,
        published_at: args.data.published_at ? nowValue : args.data.published_at,
        published_by: args.data.published_at ? userSessionId(args) : args.data.published_at,
      }
    );
  },
  delete: () => true,
};
