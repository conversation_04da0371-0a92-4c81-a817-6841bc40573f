import type { Qb<PERSON>rgs } from "~/domain/member/member-queries.server";
import { activeUserSessionQb, memberIsAdminQb, userSessionId } from "~/domain/member/member-queries.server";
import type { Expression, ExpressionBuilder, Kysely } from "kysely";
import type { DB, FileTarget } from "~/kysely/db";
import {
  allowedCustomerIdsFor,
  allowedParticipantIdsFor,
  allowedParticipantsForMember,
  myRegisteredParticipants,
} from "~/domain/participant/participant-auth-queries.server";
import type { Nullable } from "kysely/dist/esm/util/type-utils";
import { ascNullsLast, ascNullsFirst } from "~/kysely/kysely-helpers";
import { Operation } from "~/components/form/DefaultInput";
import { Args } from "~/server/resource/resource-helpers.server";

export const fileTargetsBaseQb = (db: Kysely<DB>) =>
  db
    .selectFrom("file")
    .innerJoin("file_target", "file_target.file_id", "file.id")
    .where("file.deleted_at", "is", null)
    .orderBy("file_target.sort_order", ascNullsFirst)
    .orderBy("file_target.created_at", ascNullsLast)
    .select([
      "file_target.id",
      "file_target.target_id",
      "file_target.file_id",
      "file_target.target",
      "file_target.sort_order",
      "file.filename",
    ]);

export const fileTargetsQb = (db: Kysely<DB>, fileTarget: FileTargetValue, ref: Expression<string | null>) =>
  fileTargetsBaseQb(db).where("file_target.target", "=", fileTarget).where("file_target.target_id", "=", ref);

type FileUploadFuncFunc = (
  eb: ExpressionBuilder<DB, "file_target" | "file">,
  args: QbArgs & {
    request: Request;
    action: Operation;
  },
) => unknown;

type FileDownloadFuncFunc = (
  eb: ExpressionBuilder<Omit<DB, "file_target"> & { file_target: Nullable<FileTarget> }, "file_target" | "file">,
  args: QbArgs & { request: Request },
) => unknown;

export const fileTargets = {
  invoice: {
    upload: (eb, args) => null,
    download: (eb, args) => {
      const participantBookingIdsQb = myRegisteredParticipants(args).select("_participant.booking_id");
      const participatingBookingIdsQb = myRegisteredParticipants(args)
        .innerJoin("participation", "participation.participant_id", "_participant.id")
        .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
        .innerJoin("booking", "booking.id", "sale_item.booking_id")
        .innerJoin("invoice", "invoice.booking_id", "booking.id")
        .select("invoice.id");
      const memberBookingIdsQb = memberIsAdminQb(args)
        .innerJoin("booking", "booking.establishment_id", "_member.establishment_id")
        .innerJoin("invoice", "invoice.booking_id", "booking.id")
        .select("invoice.id");
      return eb.or([
        eb("file_target.target_id", "in", participatingBookingIdsQb),
        eb("file_target.target_id", "in", memberBookingIdsQb),
        eb("file_target.target_id", "in", participantBookingIdsQb),
      ]);
    },
  },
  establishment_waiver: {
    upload: (eb, args) => eb("file_target.target_id", "in", memberIsAdminQb(args).select("_member.establishment_id")),
    download: null,
  },
  diving_site: { upload: () => null, download: null },
  diving_location: { upload: () => null, download: null },
  spot: { upload: () => null, download: null },
  region: { upload: () => null, download: null },
  form: {
    upload: (eb, args) =>
      eb(
        "file_target.target_id",
        "in",
        memberIsAdminQb(args).innerJoin("form", "form.establishment_id", "_member.establishment_id").select("form.id"),
      ),
    download: null,
  },
  establishment: {
    upload: (eb, args) =>
      eb.or([
        eb("file_target.target_id", "in", memberIsAdminQb(args).select("_member.establishment_id")),
        eb.exists(activeUserSessionQb(args, true).where("_user.editor", "=", true)),
      ]),
    download: null,
  },
  operator_favicon: {
    upload: (eb, args) =>
      eb.or([
        eb(
          "file_target.target_id",
          "in",
          memberIsAdminQb(args)
            .innerJoin("establishment", "establishment.id", "_member.establishment_id")
            .select("establishment.operator_id"),
        ),
        eb.exists(activeUserSessionQb(args, true).where("_user.editor", "=", true)),
      ]),
    download: null,
  },
  operator_logo: {
    upload: (eb, args) =>
      eb.or([
        eb(
          "file_target.target_id",
          "in",
          memberIsAdminQb(args)
            .innerJoin("establishment", "establishment.id", "_member.establishment_id")
            .select("establishment.operator_id"),
        ),
        eb.exists(activeUserSessionQb(args, true).where("_user.editor", "=", true)),
      ]),
    download: null,
  },
  category: {
    upload: (eb, args) =>
      eb(
        "file_target.target_id",
        "in",
        memberIsAdminQb(args).innerJoin("category", "category.establishment_id", "_member.establishment_id").select("category.id"),
      ),
    download: null,
  },
  product: {
    upload: (eb, args) => {
      const myProductIdsQb = memberIsAdminQb(args)
        .innerJoin("item", "item.establishment_id", "_member.establishment_id")
        .innerJoin("product", "product.item_id", "item.id")
        .select("product.id");

      return eb("file_target.target_id", "in", myProductIdsQb);
    },
    download: null,
  },
  review: {
    upload: (eb, args) =>
      eb(
        "file_target.target_id",
        "=",
        eb
          .selectFrom("review")
          .select("review.id")
          .whereRef("review.id", "=", "file_target.target_id")
          .where("review.created_by_user_session_id", "=", userSessionId(args)),
      ),
    download: null,
  },
  participant_waiver: {
    upload: (eb, args) =>
      eb(
        "file_target.target_id",
        "in",
        allowedParticipantIdsFor(args)
          .innerJoin("participant_waiver", "participant_waiver.participant_id", "_participant.id")
          .where("participant_waiver.upload_approved", "=", false)
          .select("participant_waiver.id"),
      ),
    download: (eb, args) =>
      eb.exists(
        eb
          .selectFrom("participant_waiver")
          .where("participant_waiver.id", "=", eb.ref("file_target.target_id"))
          .where((eb) =>
            eb.or([
              eb("participant_waiver.participant_id", "in", allowedParticipantIdsFor(args, "read").select("_participant.id")),
              eb("participant_waiver.customer_id", "in", allowedCustomerIdsFor(args, "read").select("_customer.id")),
            ]),
          ),
      ),
  },
  participant: {
    upload: (eb, args) => eb("file_target.target_id", "in", allowedParticipantIdsFor(args).select("_participant.id")),
    download: (eb, args) => eb("file_target.target_id", "in", allowedParticipantIdsFor(args, "read").select("_participant.id")),
  },
  participant_passport: {
    upload: (eb, args) => eb("file_target.target_id", "in", allowedParticipantIdsFor(args).select("_participant.id")),
    download: (eb, args) => eb("file_target.target_id", "in", allowedParticipantIdsFor(args, "read").select("_participant.id")),
  },
  participant_diving_certificate: {
    upload: (eb, args) => eb("file_target.target_id", "in", allowedParticipantIdsFor(args).select("_participant.id")),
    download: (eb, args) => eb("file_target.target_id", "in", allowedParticipantIdsFor(args, "read").select("_participant.id")),
  },
} satisfies Record<string, { upload: FileUploadFuncFunc; download: null | FileDownloadFuncFunc }>;

export type FileTargetValue = keyof typeof fileTargets;

export const fileResource: Args<"file"> = {
  authorize: () => true,
  insert: (args) => false,
  update: () => false,
  delete: () => false,
};

export const fileTargetResource: Args<"file_target"> = {
  authorize: (args) =>
    args.trx
      .selectFrom("file_target")
      .where("file_target.id", "=", args.id)
      .innerJoin("file", "file.id", "file_target.file_id")
      .where((eb) =>
        eb.or(
          Object.entries(fileTargets).map(([target, ebFn]) => {
            const editorCmpr = eb.exists(activeUserSessionQb(args, true).where("_user.editor", "=", true));
            const fileTargetCmpr = eb("file_target.target", "=", target);
            const ebFnResult = ebFn.upload(eb, args);
            if (!ebFnResult) return eb.and([fileTargetCmpr, editorCmpr]);
            return eb.or([
              eb.and([editorCmpr, eb("file_target.target_id", "is", null)]),
              eb.and([fileTargetCmpr, ebFnResult, eb("file_target.target_id", "is not", null)]),
            ]);
          }),
        ),
      )
      .executeTakeFirst(),
  // beforeMutate: args => {
  //   if (args.operation === "insert" && )
  // },
  insert: (args) => {
    return {
      ...args.data,
      file_id: args.data.file_id,
      created_by_user_session_id: userSessionId(args),
    };
  },
  update: (args) => ({
    sort_order: args.data.sort_order,
  }),
  delete: () => true,
};
