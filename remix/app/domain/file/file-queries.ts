import type { FileTargetValue } from "~/domain/file/file-resource";
import { kysely } from "~/misc/database.server";

export const filesQuery = (type: FileTargetValue) =>
  kysely
    .selectFrom("file")
    .innerJoin("file_target", "file_target.file_id", "file.id")
    .where("file_target.target", "=", type)
    .where("file.deleted_at", "is", null)
    .orderBy("file_target.sort_order")
    .select(["file_target.id", "file_target.file_id", "file_target.target", "file_target.sort_order", "file.filename"]);
