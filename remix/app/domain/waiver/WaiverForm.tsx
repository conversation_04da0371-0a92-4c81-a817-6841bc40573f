import { Selectable } from "kysely";
import React, { Fragment } from "react";
import { RInput, RLabel, RSelect } from "~/components/ResourceInputs";
import { divingOrganizationszz } from "~/domain/diving-course/diving-courses.data";
import { getWaiverType, WaiverType, waiverTypes } from "~/domain/waiver/waiver-vars";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { Waiver } from "~/kysely/db";
import { fName } from "~/misc/helpers";
import { ValidityDurationField } from "~/components/duration";

export const WaiverForm = (props: { waiver?: Partial<Selectable<Waiver>>; disabled?: boolean }) => {
  const waiver = props.waiver;
  const waiverType = getWaiverType(waiver?.type || ("signature" satisfies WaiverType));
  const isMedicalWaiver = waiverType.key === "medical";

  const search = useSearchParams2();

  return (
    <Fragment>
      {props.waiver?.id && <RInput disabled={props.disabled} table="waiver" field="id" value={props.waiver.id} />}
      <div>
        <RInput
          label={"Identifier"}
          table={"waiver"}
          field={"data.slug"}
          required
          disabled={props.disabled}
          defaultValue={waiver?.slug || ""}
          className="input"
        />
      </div>
      {!isMedicalWaiver && (
        <div>
          <RLabel table={"waiver"} field={"data.type"}>
            Type
          </RLabel>
          <br />
          <RSelect table={"waiver"} field={"data.type"} required value={waiverType.key} className="select w-full" disabled={props.disabled}>
            {Object.entries(waiverTypes)
              .filter(([key]) => key !== ("medical" satisfies WaiverType))
              .map(([key, waiverType]) => {
                return (
                  <option key={key} value={key}>
                    {key}
                  </option>
                );
              })}
          </RSelect>
        </div>
      )}
      {!waiver?.establishment_id && (
        <div>
          <RLabel table={"waiver"} field={"data.diving_certificate_organization_key"}>
            Diving organization
          </RLabel>
          <br />
          <RSelect
            table={"waiver"}
            field={"data.diving_certificate_organization_key"}
            defaultValue={waiver?.diving_certificate_organization_key || ""}
            className="select w-full"
            disabled={props.disabled}
          >
            <option value="">n/a</option>
            {Object.entries(divingOrganizationszz).map(([key, org]) => (
              <option key={key} value={key}>
                {org.name}
              </option>
            ))}
          </RSelect>
        </div>
      )}
      {!!waiver?.establishment_id && (
        <RInput
          disabled={props.disabled}
          table={"waiver"}
          field={"data.establishment_id"}
          type={"hidden"}
          defaultValue={waiver.establishment_id}
        />
      )}
      <div>
        <ValidityDurationField
          name={fName("waiver", "data.validity_duration")}
          label={"Valid term"}
          defaultValue={waiver?.validity_duration}
          disabled={props.disabled}
        />
      </div>
    </Fragment>
  );
};
