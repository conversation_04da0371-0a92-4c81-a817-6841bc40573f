export const waiverTypes = {
  read: {
    verb: "read",
    description: "read before registration",
    signature: false,
    upload: false,
  },
  upload: {
    verb: "upload",
    description: "upload",
    signature: false,
    upload: true,
  },
  signature: {
    verb: "sign",
    description: "sign",
    signature: true,
    upload: false,
  },
  medical: {
    verb: "sign",
    description: "medical check",
    signature: true,
    upload: false,
  },
} satisfies Record<string, { signature: boolean; upload: boolean; verb: string; description: string }>;

export type WaiverType = keyof typeof waiverTypes;

export const getWaiverType = (type: string) => {
  const waiverTypeKey = type as WaiverType;
  const waiverType = waiverTypes[waiverTypeKey] || waiverTypes.read;

  return {
    key: waiverTypeKey,
    ...waiverType,
  };
};

// export const waiverTypes = ["read", "upload", "signature", "medical"] as const;
