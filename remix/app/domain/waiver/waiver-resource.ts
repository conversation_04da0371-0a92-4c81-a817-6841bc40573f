import { ResourceError } from "~/utils/error";
import { isEditorQb, memberIsAdminQb } from "~/domain/member/member-queries.server";
import { lower, nowValue } from "~/kysely/kysely-helpers";
import { participatingParticipantIdQb, updateParticipantsQb } from "~/domain/participant/participant.queries.server";
import { Kysely } from "kysely";
import { DB, Waiver } from "~/kysely/db";
import { Args, AuthArgs } from "~/server/resource/resource-helpers.server";
import { getWaiverType, WaiverType } from "~/domain/waiver/waiver-vars";
import { keys } from "~/misc/helpers";
import { v4 } from "uuid";
import { kysely } from "~/misc/database.server";

const authorizeWaiver = (args: AuthArgs) =>
  args.trx
    .selectFrom("waiver")
    .where((eb) =>
      eb.and([
        eb.or([
          eb.and([
            eb("waiver.establishment_id", "is not", null),
            eb("waiver.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id")),
          ]),
          eb.and([eb("waiver.establishment_id", "is", null), eb.exists(isEditorQb(args))]),
        ]),
        eb.or([eb("waiver.type", "!=", "medical" satisfies WaiverType), eb("waiver.establishment_id", "is", null)]),
      ]),
    );

export const updateParticipantCacheFromWaiver = async (trx: Kysely<DB>, waiverId: string) => {
  const upcomingActivities = trx
    .selectFrom("participation")
    .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
    .innerJoin("form", "form.id", "sale_item.form_id")
    .innerJoin("form_waiver", "form_waiver.form_id", "form.id")
    .where("form_waiver.waiver_id", "=", waiverId)
    .where((eb) => eb(lower(eb.ref("sale_item.duration")), ">=", nowValue));

  await updateParticipantsQb(trx)
    .where("participant.id", "in", upcomingActivities.select("participation.participant_id"))
    .returningAll()
    .execute();

  await updateParticipantsQb(trx)
    .where("participant.booking_id", "is", null)
    .where("participant.id", "not in", participatingParticipantIdQb)
    .where(
      "participant.form_id",
      "in",
      trx.selectFrom("form_waiver").where("form_waiver.waiver_id", "=", waiverId).select("form_waiver.form_id"),
    )
    .returningAll()
    .execute();
};

export const waiverResource: Args<"waiver"> = {
  // disableAudit: () => true,
  authorize: async (args) => {
    const waiver = await authorizeWaiver(args).selectAll("waiver").where("waiver.id", "=", args.id).executeTakeFirst();

    if (!waiver) return false;

    if (waiver.establishment_id && waiver.diving_certificate_organization_key)
      throw new ResourceError("You cannot have a diving certificate waiver for establishment");

    return true;
  },
  insert: (args) => ({ ...args.data, type: getWaiverType(args.data.type).key }),
  update: (args) => {
    const waiverType = args.data.type;
    return {
      diving_certificate_organization_key: args.data.diving_certificate_organization_key,
      slug: args.data.slug,
      description: args.data.description,
      sort_order: args.data.sort_order,
      validity_duration: args.data.validity_duration,
      type: typeof waiverType === "string" ? getWaiverType(waiverType).key : waiverType,
    };
  },
  onChanged: async (args) => {
    const diff = args.diff.diff;
    if (!diff) return true;
    const keysDiff = keys(diff);
    if (!keysDiff.length) return true;
    if (keysDiff.length === 1 && keysDiff[0] === ("sort_order" satisfies keyof Waiver)) return true;

    args.after_mutations.insideTransaction.set(updateParticipantCacheFromWaiver.name + args.id, () =>
      updateParticipantCacheFromWaiver(args.trx, args.id),
    );
    return true;
  },
  delete: () => true,
};

export const waiverTranslationResource: Args<"waiver_translation"> = {
  authorize: (args) =>
    authorizeWaiver(args)
      .innerJoin("waiver_translation", "waiver_translation.waiver_id", "waiver.id")
      .where("waiver_translation.id", "=", args.id)
      .executeTakeFirst(),
  // beforeMutate: async (args) => {
  //   if (args.id) return args;
  //   const waiverId = args.data?.waiver_id;
  //   const languageCode = args.data?.language_code;
  //
  //   const defaultReturn = {
  //     id: v4(),
  //     operation: args.operation,
  //   };
  //
  //   if (!waiverId || !languageCode) return defaultReturn;
  //
  //   const existingWaiver = await args.trx
  //     .selectFrom("waiver_translation")
  //     .where("waiver_translation.waiver_id", "=", waiverId)
  //     .where("waiver_translation.language_code", "=", languageCode)
  //     .selectAll("waiver_translation")
  //     .executeTakeFirst();
  //
  //   if (!existingWaiver) return defaultReturn;
  //
  //   return {
  //     id: existingWaiver.id,
  //     operation: args.operation === "insert" ? "update" : args.operation,
  //   };
  // },
  insert: (args) => {
    return { ...args.data, questions: args.data.questions && (JSON.stringify(args.data.questions) as any) };
  },
  update: (args) => {
    return {
      markdoc: args.data.markdoc,
      name: args.data.name,
      sort_order: args.data.sort_order,
      language_code: args.data.language_code,
      questions: args.data.questions && (JSON.stringify(args.data.questions) as any),
    };
  },
  delete: (args) => true,
};

export const waiverEstablishmentResource: Args<"waiver_establishment"> = {
  authorize: (args) =>
    args.trx
      .selectFrom("waiver_establishment")
      .where("waiver_establishment.id", "=", args.id)
      .where("waiver_establishment.establishment_id", "in", memberIsAdminQb(args, "write").select("_member.establishment_id"))
      .executeTakeFirst(),
  beforeMutate: async (args) => {
    const establishmentId = args.data?.establishment_id;
    const waiverId = args.data?.waiver_id;
    if (!establishmentId) throw new ResourceError("Establishment ID is required");
    if (!waiverId) throw new ResourceError("Waiver ID is required");
    const existingWaiver = await args.trx
      .selectFrom("waiver_establishment")
      .select("id")
      .where("waiver_establishment.establishment_id", "=", establishmentId)
      .where("waiver_establishment.waiver_id", "=", waiverId)
      .executeTakeFirst();
    return {
      id: existingWaiver?.id || v4(),
      operation: args.operation === "insert" && existingWaiver ? "update" : args.operation,
      data: args.data,
    };
  },
  insert: (args) => args.data,
  update: (args) => args.data,
  delete: (args) => true,
};
