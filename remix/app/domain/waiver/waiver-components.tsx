import { Config, parse, renderers, transform, validate } from "@markdoc/markdoc";
import React from "react";
import { fallbackN<PERSON>, MarkdocCom<PERSON>, markDocNodes, markdocTags } from "~/domain/waiver/waiver-markdoc";
import { entries, keys } from "~/misc/helpers";
import { WaiverContext, WaiverProps } from "~/domain/waiver/waiver-context";

interface Vars {
  participant: string;
  operator: string;
}

// dont execute on clientside because negative lookbehinds are not supported in older browsers
export const replacePlainUrlWithMarkdownUrl = (content: string) => {
  return content.replace(/(?<!]|]\()https?:\/\/[^\s\/$.?#].[^\s]*/g, "[$&]($&)");
};

export const MarkdocComp = (props: { content: string; vars: WaiverProps; comps: Partial<MarkdocComps> }) => {
  const participant = props.vars.participant;
  const participantName = participant ? participant.first_name + " " + participant.last_name : "{participant}";
  const operator = props.vars.operator || "{operator}";
  const vars: Vars = {
    participant: participantName,
    operator: operator,
  };

  const ast2 = parse(props.content);

  const compInWaiver: Partial<Record<keyof typeof fallbackNodes, boolean>> = {};
  for (let node of ast2.walk()) {
    keys(fallbackNodes).forEach((key) => {
      if (node.tag === key) {
        compInWaiver[key] = true;
      }
    });
  }

  const finalNodes = { ...markDocNodes };
  entries(markDocNodes).forEach(([key, node]) => {
    const renderName = node.render;
    if (renderName && !props.comps[renderName as keyof typeof props.comps]) {
      delete finalNodes[key];
    }
  });

  const finalTags = { ...markdocTags };
  entries(markdocTags).forEach(([key, node]) => {
    const renderName = node.render;
    if (renderName && !props.comps[renderName as keyof typeof props.comps]) {
      delete finalTags[key];
    }
  });

  const config: Config = {
    variables: vars,
    nodes: finalNodes,
    tags: finalTags,
  };

  const validatedas = validate(ast2, config);
  console.log("validation", validatedas);
  const rendereableNodes = transform(ast2, config);

  return (
    <WaiverContext.Provider value={props.vars}>
      <div className="space-y-6">
        <div className="space-y-3">{renderers.react(rendereableNodes, React, { components: props.comps })}</div>
        {!compInWaiver["medical-questions"] && props.comps?.MedicalQuestions && <props.comps.MedicalQuestions />}
        {!compInWaiver["signature"] && props.comps?.Signature && <props.comps.Signature />}
        {!compInWaiver["medical-evaluation"] && props.comps?.MedicalEvaluation && <props.comps.MedicalEvaluation />}
      </div>
    </WaiverContext.Provider>
  );
};
