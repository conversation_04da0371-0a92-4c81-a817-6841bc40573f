import { activeUserSessionQb } from "~/domain/member/member-queries.server";
import { nowValue } from "~/kysely/kysely-helpers";
import { at_now_value } from "~/kysely/db-static-vars";
import { Args } from "~/server/resource/resource-helpers.server";

export const intuitConnectionResource: Args<"intuit_connection"> = {
  authorize: (args) =>
    args.trx
      .selectFrom("intuit_connection")
      .where("intuit_connection.id", "=", args.id)
      .innerJoin("user_session", "user_session.id", "intuit_connection.created_by_user_session_id")
      .where("user_session.user_id", "=", activeUserSessionQb(args, true).select("_user.id"))
      .executeTakeFirst(),
  insert: () => false,
  update: (args) => {
    if (args.data.deleted_at === at_now_value) {
      return { deleted_at: nowValue, deleted_by_user_session_id: activeUserSessionQb(args, true).select("_user_session.id") };
    }
    return { default_intuit_tax_code_id: args.data.default_intuit_tax_code_id };
  },
  delete: () => true,
};

// export const intuitInvoiceResource: Args<"intuit_invoice"> = {
//   authorize: (args) =>
//     args.trx
//       .selectFrom("intuit_invoice")
//       .innerJoin("invoice", "invoice.id", "intuit_invoice.invoice_id")
//       .innerJoin("booking", "booking.id", "invoice.booking_id")
//       .where("intuit_invoice.id", "=", args.id)
//       .where("booking.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"))
//       .executeTakeFirst(),
//   beforeMutate: async (args) => {
//     const invoiceId = args.data?.invoice_id;
//     if (!args.id && typeof invoiceId === "string" && args.operation === "delete") {
//       const existingIntuitInvoice = await args.trx
//         .selectFrom("intuit_invoice")
//         .select("intuit_invoice.id")
//         .where("intuit_invoice.invoice_id", "=", invoiceId)
//         .executeTakeFirst();
//       return {
//         id: existingIntuitInvoice?.id || "",
//         operation: existingIntuitInvoice ? "delete" : "ignore",
//       };
//     }
//     return { id: args.id || v4(), operation: args.operation, data: args.data };
//   },
//   insert: () => false,
//   update: () => false,
//   delete: () => true,
// };
