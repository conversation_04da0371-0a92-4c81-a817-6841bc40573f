import { Kysely, sql } from "kysely";
import { DB } from "~/kysely/db";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { notNull, sqid } from "~/kysely/kysely-helpers";
import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { bookingPriceSelects } from "~/domain/pricing/booking-pricing-queries";
import { asPaymentTotalAmount } from "~/domain/payment/payment-queries.server";
import { baseProductWithSelect } from "~/domain/product/product-queries.server";
import {
  deleteFetch,
  getAccessTokenAndRefresh,
  getDetail,
  getQuery,
  postCreate,
  postSparseUpdate,
} from "~/domain/quickbooks/quickbooks-client.server";
import { kysely } from "~/misc/database.server";
import { getCountry } from "~/data/countries";
import { getFullProductTitle } from "~/domain/product/ProductItem";
import { defaultSurchargeLineText } from "~/domain/invoice/invoice-vars";
import { AsyncReturnType, FunctionResponse } from "~/misc/types";
import { getActivity } from "~/domain/activity/activity";

// virual accoiutn paid: https://www.traveltruster.com/xendit/virtual_account/payment
// virtual account created: https://www.traveltruster.com/xendit/virtual_account/mutation
// invoice callback https://www.traveltruster.com/xendit/invoice

export const invoiceQb = (trx: Kysely<DB>, invoiceId: string | null) =>
  trx
    .selectFrom("invoice")
    .innerJoin("booking", "booking.id", "invoice.booking_id")
    .innerJoin("intuit_connection", "intuit_connection.id", "invoice.intuit_connection_id")
    .where("invoice.id", "=", invoiceId)
    .selectAll("invoice")
    .select((eb) => [
      sqid(eb.ref("booking.id_seq")).as("booking_sqid"),
      "intuit_connection.id as intuit_connection_id",
      "intuit_connection.realm_id",
      "intuit_connection.refresh_token",
      "intuit_connection.default_intuit_tax_code_id",
      jsonObjectFrom(
        eb
          .selectFrom("participant")
          .innerJoin("customer", "customer.id", "participant.customer_id")
          .innerJoin("person", "person.id", "customer.person_id")
          .innerJoin("user", "user.id", "person.user_id")
          .select(["person.first_name", "person.last_name", "user.email", "participant.phone", "participant.country_code"])
          .where(
            "participant.id",
            "in",
            eb
              .selectFrom("participation")
              .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
              .where("sale_item.booking_id", "=", eb.ref("invoice.booking_id"))
              .select("participation.participant_id"),
          )
          .where((eb) =>
            eb(sql<string>`(${eb.ref("person.first_name")} || ' ' || ${eb.ref("person.last_name")})`, "=", eb.ref("invoice.customer_name")),
          )
          .limit(1),
      ).as("customer"),
      notNull(
        jsonObjectFrom(
          eb
            .selectFrom("booking")
            .where("booking.id", "=", eb.ref("invoice.booking_id"))
            .select(bookingPriceSelects)
            .select((eb) => [
              "booking.id",
              jsonArrayFrom(
                eb
                  .selectFrom("payment")
                  .innerJoin("payment_method", "payment_method.id", "payment.payment_method_id")
                  .selectAll("payment")
                  .select(["payment_method.name as payment_method_name", asPaymentTotalAmount])
                  .where("payment.booking_id", "=", eb.ref("booking.id")),
              ).as("payments"),
              jsonArrayFrom(
                eb
                  .selectFrom("sale_item")
                  .where("sale_item.booking_id", "=", eb.ref("booking.id"))
                  .select((eb) => [
                    "sale_item.id",
                    "sale_item.description",
                    "sale_item.quantity",
                    "sale_item.cached_total_price_amount",
                    jsonObjectFrom(baseProductWithSelect.where("product.id", "=", eb.ref("sale_item.product_id"))).as("product"),
                  ]),
              ).as("activities"),
            ]),
        ),
      ).as("booking"),
    ])
    .executeTakeFirst();

export const sendInvoiceToIntuit = async (trx: Kysely<DB>, invoice: AsyncReturnType<typeof invoiceQb>): Promise<FunctionResponse> => {
  if (!invoice) return [false, "Invoice is missing"];

  const tokenResponse = await getAccessTokenAndRefresh(kysely, invoice.intuit_connection_id);
  // if (environment === "development") {
  // console.log("realmId", tokenResponse.realm_id);
  // console.log(tokenResponse.access_token);
  // }

  const customerQueryResponse = await getQuery(tokenResponse, "Customer", { DisplayName: invoice.customer_name });
  const taxcodesResult = await getQuery(tokenResponse, "TaxCode");
  const defaultTaxCode = taxcodesResult.QueryResponse.TaxCode?.find((taxCode) => taxCode.Id === invoice.default_intuit_tax_code_id) ||
    taxcodesResult.QueryResponse.TaxCode?.find((taxCode) => taxCode.Id === "NON") ||
    taxcodesResult.QueryResponse.TaxCode?.find((taxCode) => taxCode.Id === "10");

  if (!defaultTaxCode) return [false, "Could not find default taxccode"];

  const messages: string[] = [];
  let customerId: string = "";

  const getInvoiceAddress = () => {
    const countryCode = getCountry(invoice.customer?.country_code || "")?.country_name;
    if (!invoice.customer_address && !countryCode) return undefined;
    return {
      Line1: invoice.customer_address || countryCode || "",
      Country: countryCode,
    } satisfies Address;
  };
  const invoiceAddress = getInvoiceAddress();

  const firstCustomer = customerQueryResponse.QueryResponse.Customer?.[0];
  if (firstCustomer) {
    customerId = firstCustomer.Id;
  }
  if (!customerId) {
    const createdCustomer = await postCreate(tokenResponse, "Customer", {
      DisplayName: invoice.customer_name,
      GivenName: invoice.customer?.first_name || undefined,
      FamilyName: invoice.customer?.last_name || undefined,
      BillAddr: invoiceAddress,
      PrimaryPhone: invoice.customer?.phone
        ? {
            FreeFormNumber: invoice.customer.phone,
          }
        : undefined,
      PrimaryEmailAddr: invoice.customer?.email
        ? {
            Address: invoice.customer.email,
          }
        : undefined,
    });
    console.log("created customer", createdCustomer.Customer.Id);
    customerId = createdCustomer.Customer.Id;
  }

  if (!customerId) {
    throw new Error("Could not create customer");
  }

  const qbLineItems: Array<LineItem> = [];
  for (const activity of invoice.booking.activities) {
    const getOrCreateQbItem = async () => {
      if (!activity.product) return null;
      const productType = getActivity(activity.product.activity_slug);
      const skuOrId = activity.product.external_identifier || activity.product.sku || activity.product.root_id || activity.product.id;
      const productTitle = getFullProductTitle(activity.product);
      const itemsBySkuResponse = await getQuery(tokenResponse, "Item", { Sku: skuOrId.replace(/-/g, "\\-") });
      const firstItemBySku = itemsBySkuResponse.QueryResponse.Item?.[0];
      if (firstItemBySku) return firstItemBySku;

      const itemsByNameQueryResponse = await getQuery(tokenResponse, "Item", { Name: productTitle });
      const firstItemByName = itemsByNameQueryResponse.QueryResponse.Item?.[0];
      if (firstItemByName) return firstItemByName;

      const incomeAccountResponse = await getQuery(tokenResponse, "Account", { AccountType: "Income" });
      const createItemResponse = await postCreate(tokenResponse, "Item", {
        Sku: skuOrId,
        // Type: productType.retail ? "Inventory" : "Service",
        Type: "Service",
        UnitPrice: activity.product.product_prices[0]?.amount || 0,
        Name: productTitle,
        IncomeAccountRef: {
          value: incomeAccountResponse.QueryResponse.Account?.[0]?.Id || "",
        },
      });
      return createItemResponse.Item;
    };
    const qbItem = await getOrCreateQbItem();

    qbLineItems.push({
      Amount: Number(activity.cached_total_price_amount),
      DetailType: "SalesItemLineDetail",
      Description: activity.description || undefined,
      SalesItemLineDetail: {
        ItemRef: qbItem
          ? {
              value: qbItem.Id,
            }
          : undefined,
        Qty: activity.quantity,
        TaxCodeRef: qbItem?.SalesTaxCodeRef || {
          value: defaultTaxCode.Id,
        },
      },
    });
  }

  if (invoice.booking.payed_amount_surcharge) {
    qbLineItems.push({
      Amount: invoice.booking.payed_amount_surcharge,
      DetailType: "SalesItemLineDetail",
      Description: defaultSurchargeLineText,
      SalesItemLineDetail: {
        TaxCodeRef: {
          value: defaultTaxCode.Id,
        },
      },
    });
  }

  const getOrCreateInvoice = async () => {
    if (invoice.intuit_invoice_id) {
      const detailResponse = await getDetail(tokenResponse, "Invoice", invoice.intuit_invoice_id);
      if (detailResponse.Invoice) {
        const updateResult = await postSparseUpdate(tokenResponse, "Invoice", {
          Id: detailResponse.Invoice.Id,
          SyncToken: detailResponse.Invoice.SyncToken,
          CustomerRef: { value: customerId },
          Line: qbLineItems,
        });
        return updateResult.Invoice;
      }
    }
    const invoiceCreatedResponse = await postCreate(tokenResponse, "Invoice", {
      CustomerRef: { value: customerId },
      BillAddr: invoiceAddress,
      Line: qbLineItems,
    });
    return invoiceCreatedResponse.Invoice;
  };

  const intuitInvoice = await getOrCreateInvoice();

  const accountsResult = await getQuery(tokenResponse, "Account", { Name: "Undeposited Funds" });
  const firstAccount = accountsResult.QueryResponse.Account?.[0];

  if (firstAccount) {
    const paymentMethodsResponse = await getQuery(tokenResponse, "PaymentMethod");
    const paymentMethods = paymentMethodsResponse.QueryResponse.PaymentMethod || [];

    const paymentLinks = (intuitInvoice.LinkedTxn || []).filter((txn) => txn.TxnType === "Payment");
    // for (const linkedTxn of paymentLinks) {
    //   const existingIntuitPayment = await getDetail(tokenResponse, "Payment", linkedTxn.TxnId);
    //   if (existingIntuitPayment) {
    //     await deleteFetch(tokenResponse, "Payment", {
    //       Id: existingIntuitPayment.Payment.Id,
    //       SyncToken: existingIntuitPayment.Payment.SyncToken,
    //     });
    //   }
    // }

    const foundPaymentIds: string[] = [];
    const existingIntuitPayments = await Promise.all(paymentLinks.map((linkedTxn) => getDetail(tokenResponse, "Payment", linkedTxn.TxnId)));
    const paidPayments = invoice.booking.payments.filter((payment) => payment.payed_at);
    for (const payment of paidPayments) {
      const lowerPaymentMethod = payment.payment_method_name.toLowerCase();
      const foundPaymentMethod = paymentMethods.find((intuitPaymentMethod) => {
        const intuitLower = intuitPaymentMethod.Name.toLowerCase();
        return intuitLower.includes(lowerPaymentMethod) || lowerPaymentMethod.includes(intuitLower);
      });

      const foundIntuitPayment = existingIntuitPayments.find(
        (inuitPaymnebt) =>
          !foundPaymentIds.includes(inuitPaymnebt.Payment.Id) &&
          ((!!foundPaymentMethod && !!inuitPaymnebt.Payment.PaymentMethodRef) ||
            inuitPaymnebt.Payment.PaymentMethodRef?.value === foundPaymentMethod?.Id) &&
          inuitPaymnebt.Payment.TotalAmt === (payment.derived_amount_total || 0),
      );

      if (foundIntuitPayment) {
        foundPaymentIds.push(foundIntuitPayment.Payment.Id);
      }

      const isActivePayment = payment.deleted_at === at_infinity_value && !!payment.payed_at;

      if (!foundIntuitPayment && isActivePayment) {
        await postCreate(tokenResponse, "Payment", {
          TotalAmt: payment.derived_amount_total || 0,
          CustomerRef: { value: customerId },
          DepositToAccountRef: {
            value: firstAccount.Id,
          },
          PaymentMethodRef: foundPaymentMethod && {
            value: foundPaymentMethod.Id,
          },
          Line: [
            {
              Amount: payment.derived_amount_total || 0,
              LinkedTxn: [
                {
                  TxnId: intuitInvoice.Id,
                  TxnType: "Invoice",
                },
              ],
            },
          ],
        });
      } else if (foundIntuitPayment && !isActivePayment) {
        if (foundIntuitPayment.Payment.DepositToAccountRef) {
          messages.push(`Could not update payment "${foundIntuitPayment.Payment.Id}" because already conciliated`);
        } else {
          await deleteFetch(tokenResponse, "Payment", {
            Id: foundIntuitPayment.Payment.Id,
            SyncToken: foundIntuitPayment.Payment.SyncToken,
          });
        }
      }
    }
  } else {
    messages.push('Did not register payments because there was no "Undeposited Funds" account');
  }

  await trx
    .updateTable("invoice")
    .set({
      intuit_invoice_id: intuitInvoice.Id,
      intuit_invoice_response: intuitInvoice,
      intuit_connection_id: invoice.intuit_connection_id,
    })
    .where("invoice.id", "=", invoice.id)
    .executeTakeFirstOrThrow();

  return [true, messages.length ? messages.join(";\n") : null];
};
