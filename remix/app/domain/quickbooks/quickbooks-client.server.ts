import { appConfig } from "~/config/config.server";
import { ResourceError } from "~/utils/error";
import { z } from "zod";
import { Kysely } from "kysely";
import { DB } from "~/kysely/db";

// https://developer.intuit.com/app/developer/qbo/docs/develop/authentication-and-authorization/oauth-2.0
// https://developer.api.intuit.com/.well-known/openid_configuration

export const intuit_base_url = {
  PRODUCTION: {
    account: "https://accounts.platform.intuit.com",
    api: "https://quickbooks.api.intuit.com",
  },
  SANDBOX: {
    account: "https://sandbox-accounts.platform.intuit.com",
    api: "https://sandbox-quickbooks.api.intuit.com",
  },
};

export type IntuitEnv = keyof typeof intuit_base_url;

export const getAccessToken = async (
  env: IntuitEnv,
  args:
    | { grant_type: "authorization_code"; redirect_uri: string; code: string }
    | {
        grant_type: "refresh_token";
        refresh_token: string;
      },
) => {
  const clientSecretKey = `INTUIT_${env}_CLIENT_SECRET` as const;
  const clientIdKey = `INTUIT_${env}_CLIENT_ID` as const;
  const clientSecret = appConfig[clientSecretKey];
  const clientId = appConfig.PUBLIC[clientIdKey];
  if (!clientId) throw new ResourceError(`${clientIdKey} is required`);
  if (!clientSecret) throw new ResourceError(`${clientSecretKey} is required`);

  const params = new URLSearchParams(args);

  const headers = new Headers();
  headers.append("Content-Type", "application/x-www-form-urlencoded");
  headers.append("Authorization", `Basic ${btoa(`${clientId}:${clientSecret}`)}`);

  const response = await fetch("https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer", {
    method: "POST",
    body: params.toString(),
    headers,
  });
  console.log("resp", response);
  if (!response.ok) {
    console.error("response", response.status, response.statusText);
    throw new Error("Failed to fetch access token");
  }

  const result = await response.json();

  return z
    .object({
      access_token: z.string(),
      refresh_token: z.string(),
    })
    .parse(result);
};

interface DefaultArgs {
  access_token: string;
  realm_id: string;
  environment: IntuitEnv;
}

interface IdAndSync {
  Id: string;
  SyncToken: string;
}

const qbFetch = async <OUTPUT = any, INPUT extends object = any>(args: {
  endpoint: string;
  access_token: string;
  input?: INPUT;
  method: "GET" | "POST";
}) => {
  console.log("try", args.method, args.endpoint);

  const headers = new Headers();
  headers.append("Authorization", `Bearer ${args.access_token}`);
  headers.append("Accept", "application/json");

  const requestInit: RequestInit = {
    method: args.method,
    headers: headers,
  };

  if (args.input) {
    requestInit.body = JSON.stringify(args.input);
    headers.append("Content-Type", "application/json");
  }

  const response = await fetch(args.endpoint, requestInit);

  if (!response.ok) {
    console.error(args.input && JSON.stringify(args.input));
    let error = "";
    try {
      error = JSON.stringify(await response.json());
    } catch (e) {
      error = "could not json stringy error from response";
    }
    throw new Error(`Failed to fetch ${args.endpoint}, ${response.status}, ${response.statusText}; \n${error}`);
  }

  const result = await response.json();
  return result as OUTPUT;
};

export const getUserInfo = async (args: { access_token: string; environment: IntuitEnv }) => {
  const endpoint = `${intuit_base_url[args.environment].account}/v1/openid_connect/userinfo`;
  return qbFetch<UserInfoResponse>({ endpoint: endpoint, access_token: args.access_token, method: "GET" });
};

type Query = {
  Invoice: {
    item: Invoice;
    // filter: Partial<Record<keyof Invoice, string>>;
    // queryOutput: InvoiceQueryResponse;
    postInput: CreateInvoiceRequest;
    // postOutput: CreateInvoiceResponse;
  };
  Item: {
    item: Item;
    // filter: Partial<Record<keyof Item, string>>;
    // queryOutput: ItemQueryResponse;
    postInput: CreateItemRequest;
    // postOutput: CreateItemResponse;
  };
  Customer: {
    item: Customer;
    // filter: Partial<Record<keyof Customer, string>>;
    // queryOutput: CustomerQueryResponse;
    postInput: CreateCustomerRequest;
    // postOutput: CreateCustomerResponse;
  };
  Account: {
    item: Account;
    // filter: Partial<Record<keyof Account, string>>;
    // queryOutput: AccountQueryResponse;
    postInput: CreateCustomerRequest;
    // postOutput: CreateCustomerResponse;
  };
  Payment: {
    item: Payment;
    // filter: Partial<Record<keyof Payment, string>>;
    // queryOutput: PaymentQueryResponse;
    postInput: CreatePaymentRequest;
    // postOutput: CreatePaymentResponse;
  };
  PaymentMethod: {
    item: PaymentMethod;
    // filter: Partial<Record<keyof PaymentMethod, string>>;
    // queryOutput: PaymentMethodQueryResponse;
    postInput: never;
    // postOutput: never;
  };
  TaxCode: {
    item: TaxCode;
    // filter: Partial<Record<keyof TaxCode, string>>;
    // queryOutput: TaxCodeQueryResponse;
    postInput: never;
    // postOutput: never;
  };
};

export const getDetail = async <QKey extends keyof Query>(args: DefaultArgs, queryKey: QKey, id: string) => {
  const endpoint = `${intuit_base_url[args.environment].api}/v3/company/${args.realm_id}/${queryKey.toLowerCase()}/${id}`;
  return qbFetch<Record<QKey, Query[QKey]["item"]>>({ endpoint, access_token: args.access_token, method: "GET" });
};

export const deleteFetch = async <QKey extends keyof Query>(args: DefaultArgs, queryKey: QKey, body: IdAndSync) => {
  const base = `${intuit_base_url[args.environment].api}/v3/company/${args.realm_id}`;
  const endpoint = `${base}/${queryKey.toLowerCase()}?operation=delete&minorversion=75`;
  return qbFetch<Record<QKey, Query[QKey]["item"]>>({
    endpoint: endpoint,
    access_token: args.access_token,
    method: "POST",
    input: body,
  });
};

export const getQuery = async <QKey extends keyof Query>(
  args: DefaultArgs,
  queryKey: QKey,
  filter?: Partial<Record<keyof Query[QKey]["item"], string>>,
) => {
  const where =
    (filter &&
      ` Where ${Object.entries(filter)
        .map(([key, value]) => `${key} = '${value}'`)
        .join(" AND ")}`) ||
    "";
  // const path = encodeURIComponent(`/v3/company/${companyId}/query?query=select * from ${queryKey}${where}`);
  // const endpoint = `${appConfig.INTUIT_BASE_URL.api}${path}`;
  const uriParams = encodeURIComponent(`select *
                                        from ${queryKey} ${where}`);
  const pagingParams = " STARTPOSITION 1 MAXRESULTS 10";
  const endpoint = `${intuit_base_url[args.environment].api}/v3/company/${args.realm_id}/query?query=` + uriParams;
  return qbFetch<{
    QueryResponse: Partial<Record<QKey, Array<Query[QKey]["item"]>>> & {
      startPosition: number; // Starting position in the overall dataset
      maxResults: number; // Maximum number of results returned
      totalCount?: number; // Total count of invoices matching the query (if more than maxResults)
    };
    time: string; // ISO 8601 date format for response time
  }>({ endpoint, access_token: args.access_token, method: "GET" });
};

export const postSparseUpdate = async <QKey extends keyof Query>(
  args: DefaultArgs,
  queryKey: QKey,
  input: Partial<Query[QKey]["postInput"]> & IdAndSync,
) =>
  qbFetch<Record<QKey, Query[QKey]["item"]> & { time: string }>({
    endpoint: `${intuit_base_url[args.environment].api}/v3/company/${args.realm_id}/${queryKey.toLowerCase()}?minorversion=75`,
    access_token: args.access_token,
    method: "POST",
    input: { ...input, sparse: true },
  });

export const postCreate = async <QKey extends keyof Query>(args: DefaultArgs, queryKey: QKey, input: Query[QKey]["postInput"]) =>
  qbFetch<Record<QKey, Query[QKey]["item"]> & { time: string }>({
    endpoint: `${intuit_base_url[args.environment].api}/v3/company/${args.realm_id}/${queryKey.toLowerCase()}?minorversion=75`,
    access_token: args.access_token,
    method: "POST",
    input: input,
  });

export const getCompanryInfo = async (args: DefaultArgs) => {
  const endpoint = `${intuit_base_url[args.environment].api}/v3/company/${args.realm_id}/companyinfo/${args.realm_id}`;
  return qbFetch<CompanyInfoResponse>({ endpoint, access_token: args.access_token, method: "GET" });
};

export const getAccessTokenAndRefresh = async (db: Kysely<DB>, intuitConnectionId: string) => {
  const connectionRow = await db
    .selectFrom("intuit_connection")
    .selectAll("intuit_connection")
    .where("intuit_connection.id", "=", intuitConnectionId)
    .executeTakeFirstOrThrow();

  const env: IntuitEnv = connectionRow.sandbox ? "SANDBOX" : "PRODUCTION";

  const result = await getAccessToken(env, {
    grant_type: "refresh_token",
    refresh_token: connectionRow.refresh_token,
  });

  await db
    .updateTable("intuit_connection")
    .where("intuit_connection.id", "=", intuitConnectionId)
    .set({ refresh_token: result.refresh_token })
    .executeTakeFirstOrThrow();

  return { ...connectionRow, ...result, environment: env };
};
