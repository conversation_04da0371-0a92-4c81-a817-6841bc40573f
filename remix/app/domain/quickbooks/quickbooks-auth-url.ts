import { useAppContext } from "~/hooks/use-app-context";
import { getFullUrl } from "~/misc/helpers";
import { _connect } from "~/misc/paths";
import { IntuitEnv } from "~/domain/quickbooks/quickbooks-client.server";

// https://help.developer.intuit.com/s/question/0D54R00009QUAirSAH/are-you-working-on-a-solution-to-allow-users-to-connect-via-the-api-without-requiring-that-they-are-administrators
// Would like to be able to connect with a normal user instead of an admin account in quickbooks, sadly not possible in quickbooks.
// now when someone else connects to quickbooks it asks to switch the admin to the new person.
// also, we have way more permissions than we need

const INTUIT_OAUTH_URL = "https://appcenter.intuit.com/connect/oauth2";

export const useIntuitAuthUrl = (env: IntuitEnv) => {
  const ctx = useAppContext();
  const params = new URLSearchParams({
    client_id: ctx.env[`INTUIT_${env}_CLIENT_ID`] || "",
    response_type: "code",
    scope: "com.intuit.quickbooks.accounting openid profile email",
    redirect_uri: getFullUrl(ctx.host) + _connect(env),
    state: ctx.user_session_id,
  });

  return `${INTUIT_OAUTH_URL}?${params.toString()}`;
};
