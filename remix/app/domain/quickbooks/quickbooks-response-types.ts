type Ref = {
  value: string;
  name?: string;
};

type UserInfoResponse = {
  sub: string;
  email: string;
  email_verified: boolean;
  given_name?: string;
  family_name?: string;
  phone_number?: string;
  phone_number_verified?: boolean;
  address?: {
    street_address: string;
    locality: string;
    region: string;
    postal_code: string;
    country: string;
  };
  birthdate?: string;
  locale?: string;
  zoneinfo?: string;
  updated_at?: number;
};

type Address = {
  Id?: string;
  Line1: string;
  Line2?: string;
  Line3?: string;
  Line4?: string;
  City?: string;
  CountrySubDivisionCode?: string;
  PostalCode?: string;
  Lat?: string;
  Long?: string;
  Country?: string;
};

type CompanyInfo = {
  CompanyName: string;
  LegalName: string;
  CompanyAddr: Address;
  CustomerCommunicationAddr?: Address;
  LegalAddr: Address;
  PrimaryPhone: {
    FreeFormNumber: string;
  };
  CompanyEmail: {
    Address: string;
  };
  WebAddr?: {
    URI: string;
  };
  FiscalYearStartMonth: string;
  Country: string;
  SupportedLanguages: string;
  NameValue?: Array<{ Name: string; Value: string }>;
  Id: string;
  SyncToken: string;
  MetaData: {
    CreateTime: string;
    LastUpdatedTime: string;
  };
};

type CompanyInfoResponse = {
  CompanyInfo: CompanyInfo;
  time: string;
};

type CreateCustomerRequest = {
  DisplayName: string;
  CompanyName?: string;
  GivenName?: string;
  MiddleName?: string;
  FamilyName?: string;
  Suffix?: string;
  FullyQualifiedName?: string;
  PrimaryPhone?: {
    FreeFormNumber: string;
  };
  PrimaryEmailAddr?: {
    Address: string;
  };
  BillAddr?: Address;
  ShipAddr?: Address;
  Notes?: string;
  Title?: string;
  Active?: boolean;
  Job?: boolean;
  BillWithParent?: boolean;
  CurrencyRef?: Ref;
  PreferredDeliveryMethod?: "Print" | "Email" | "None";
  Taxable?: boolean;
  TaxExemptionReasonId?: string;
  ResaleNum?: string;
  Balance?: number;
  OpenBalanceDate?: string; // ISO 8601 date format
  PaymentMethodRef?: Ref;
  TermRef?: Ref;
  PriceLevelRef?: Ref;
  PreferredPaymentMethodRef?: Ref;
};

type Customer = {
  Id: string;
  SyncToken: string;
  MetaData: {
    CreateTime: string; // ISO 8601 date format
    LastUpdatedTime: string; // ISO 8601 date format
  };
  DisplayName: string;
  CompanyName?: string;
  GivenName?: string;
  MiddleName?: string;
  FamilyName?: string;
  Suffix?: string;
  FullyQualifiedName?: string;
  PrimaryPhone?: {
    FreeFormNumber: string;
  };
  PrimaryEmailAddr?: {
    Address: string;
  };
  BillAddr?: Address;
  ShipAddr?: Address;
  Notes?: string;
  Title?: string;
  Active?: boolean;
  Job?: boolean;
  BillWithParent?: boolean;
  CurrencyRef?: Ref;
  PreferredDeliveryMethod?: "Print" | "Email" | "None";
  Taxable?: boolean;
  TaxExemptionReasonId?: string;
  ResaleNum?: string;
  Balance: number;
  OpenBalanceDate?: string; // ISO 8601 date format
  PaymentMethodRef?: Ref;
  TermRef?: Ref;
  PriceLevelRef?: Ref;
  PreferredPaymentMethodRef?: Ref;
};

type CreateCustomerResponse = {
  Customer: Customer;
  time: string; // ISO 8601 date format
};

type SalesItemLineDetail = {
  DetailType: "SalesItemLineDetail";
  Amount: number;
  Description?: string;
  SalesItemLineDetail: {
    ItemRef?: Ref;
    TaxCodeRef?: Ref;
    UnitPrice?: number;
    Qty?: number;
  };
};

type DescriptionOnlyLine = {
  DetailType: "DescriptionOnly";
  Amount: number;
  Description?: string;
  DescriptionLineDetail?: {
    TaxCodeRef?: Ref;
  };
  TaxCodeRef?: {
    value: string;
  };
  LineNum?: number;
};

type LineItem = SalesItemLineDetail | DescriptionOnlyLine;

type CreateInvoiceRequest = {
  Line: Array<LineItem>;
  CustomerRef: Ref;
  CurrencyRef?: Ref;
  BillEmail?: {
    Address: string;
  };
  BillAddr?: Address;
  ShipAddr?: Address;
  TermsRef?: Ref;
  DueDate?: string; // ISO 8601 date format, e.g., "2023-08-21"
  PrivateNote?: string;
  TxnDate?: string; // ISO 8601 date format
  CustomField?: Array<{
    DefinitionId: string;
    Name: string;
    Type: string;
    StringValue: string;
  }>;
};

type Invoice = {
  Id: string;
  SyncToken: string;
  MetaData: {
    CreateTime: string; // ISO 8601 date format
    LastUpdatedTime: string; // ISO 8601 date format
  };
  CustomField?: Array<{
    DefinitionId: string;
    Name: string;
    Type: string;
    StringValue: string;
  }>;
  DocNumber: string;
  TxnDate: string; // ISO 8601 date format
  CurrencyRef: {
    value: string;
    name: string;
  };
  Line: Array<{
    Id: string;
    LineNum: number;
    Amount: number;
    DetailType: "SalesItemLineDetail" | string;
    SalesItemLineDetail: {
      ItemRef: {
        value: string;
        name: string;
      };
      TaxCodeRef?: {
        value: string;
      };
      UnitPrice?: number;
      Qty?: number;
    };
  }>;
  CustomerRef: Ref;
  BillEmail?: {
    Address: string;
  };
  BillAddr?: Address;
  ShipAddr?: Address;
  TermsRef?: Ref;
  DueDate?: string;
  PrivateNote?: string;
  TxnTaxDetail?: {
    TxnTaxCodeRef?: {
      value: string;
    };
    TotalTax: number;
    TaxLine: Array<{
      Amount: number;
      DetailType: string;
      TaxLineDetail: {
        TaxRateRef: {
          value: string;
          name?: string;
        };
        PercentBased: boolean;
        NetAmountTaxable: number;
        TaxInclusiveAmount: number;
        OverrideDeltaAmount: number;
      };
    }>;
  };
  ApplyTaxAfterDiscount?: boolean;
  TotalAmt: number;
  HomeTotalAmt?: number;
  PrintStatus?: "NeedToPrint" | "PrintComplete";
  EmailStatus?: "NotSet" | "NeedToSend" | "EmailSent";
  Balance: number;
  LinkedTxn?: Array<{
    TxnId: string;
    TxnType: string;
  }>;
};

type Item = {
  Id: string;
  Sku?: string;
  Name: string;
  SyncToken: string;
  MetaData: {
    CreateTime: string; // ISO 8601 date format
    LastUpdatedTime: string; // ISO 8601 date format
  };
  Description?: string;
  Active: boolean;
  SubItem?: boolean;
  ParentRef?: Ref;
  FullyQualifiedName: string;
  Taxable?: boolean;
  UnitPrice?: number;
  Type: "Service" | "Inventory" | "NonInventory";
  IncomeAccountRef: Ref;
  PurchaseDesc?: string;
  PurchaseCost?: number;
  ExpenseAccountRef?: Ref;
  AssetAccountRef?: Ref;
  TrackQtyOnHand?: boolean;
  QtyOnHand?: number;
  SalesTaxCodeRef?: Ref;
  PurchaseTaxCodeRef?: Ref;
  InvStartDate?: string; // ISO 8601 date format
};

type PaymentLine = {
  Amount: number; // Amount applied to the invoice
  LinkedTxn: Array<LinkedTransaction>; // Reference to the transactions (e.g., Invoices) being paid
};

type LinkedTransaction = {
  TxnId: string; // ID of the linked transaction (e.g., Invoice ID)
  TxnType: string; // Type of the transaction (e.g., "Invoice")
};

type CreateItemRequest = {
  Name: string; // Name of the item (required)
  Sku?: string;
  Description?: string; // Description of the item (optional)
  Active?: boolean; // Is the item active? (default: true)
  FullyQualifiedName?: string; // Fully qualified name (optional)
  Taxable?: boolean; // Is the item taxable? (optional)
  UnitPrice?: number; // Default price per unit (optional)
  Type: "Service" | "Inventory" | "NonInventory"; // Type of the item (required)
  IncomeAccountRef: {
    value: string; // Account ID (required)
    name?: string; // Account name (optional)
  };
  ExpenseAccountRef?: {
    value: string; // Account ID (required for Inventory items)
    name?: string; // Account name (optional)
  };
  AssetAccountRef?: {
    value: string; // Account ID (required for Inventory items)
    name?: string; // Account name (optional)
  };
  TrackQtyOnHand?: boolean; // Should the quantity on hand be tracked? (optional, required for Inventory items)
  QtyOnHand?: number; // Quantity on hand (required for Inventory items if tracking is enabled)
  InvStartDate?: string; // Inventory start date in ISO 8601 format (required if TrackQtyOnHand is true)
};

type CreatePaymentRequest = {
  CustomerRef: {
    value: string; // Customer ID
    name?: string; // Optional, Customer name
  };
  TotalAmt: number; // Total payment amount
  Line: Array<PaymentLine>;
  PaymentMethodRef?: {
    value: string; // Payment method ID
    name?: string; // Optional, Payment method name
  };
  DepositToAccountRef: {
    value: string; // Account ID where the payment will be deposited
    name?: string; // Optional, Account name
  };
  PrivateNote?: string; // Optional, note for internal use
};

type Account = {
  Id: string; // Unique identifier for the account
  Name: string; // Name of the account
  SyncToken: string; // Sync token for preventing update conflicts
  MetaData: {
    CreateTime: string; // ISO 8601 date format for creation time
    LastUpdatedTime: string; // ISO 8601 date format for last update time
  };
  AccountType: string; // Type of the account (e.g., "Income", "Expense", "Bank", etc.)
  AccountSubType?: string; // Subtype of the account, providing more specific categorization
  Classification: "Asset" | "Liability" | "Equity" | "Revenue" | "Expense"; // Account classification
  FullyQualifiedName: string; // Fully qualified name of the account
  Active: boolean; // Is the account active?
  CurrentBalance?: number; // Current balance of the account
  CurrentBalanceWithSubAccounts?: number; // Current balance including sub-accounts
  CurrencyRef?: {
    value: string; // Currency code (e.g., "USD")
    name?: string; // Currency name (optional)
  };
  Description?: string; // Description of the account (optional)
  SubAccount?: boolean; // Indicates if the account is a sub-account
  ParentRef?: {
    value: string; // ID of the parent account if this is a sub-account
    name?: string; // Name of the parent account (optional)
  };
};

type AccountQueryResponse = {
  QueryResponse: {
    Account?: Array<Account>; // Array of accounts that match the query
    startPosition: number; // Starting position in the overall dataset
    maxResults: number; // Maximum number of results returned
    totalCount?: number; // Total count of accounts matching the query (if more than maxResults)
  };
  time: string; // ISO 8601 date format for response time
};

type PaymentMethod = {
  Id: string; // Unique identifier for the payment method
  Name: string; // Name of the payment method (e.g., "Credit Card", "Cash")
  Type?: string; // Type of the payment method (e.g., "CreditCard", "Bank", "Cash")
  Active: boolean; // Indicates if the payment method is active
};

type PaymentMethodQueryResponse = {
  QueryResponse: {
    PaymentMethod?: Array<PaymentMethod>; // Array of payment methods
    startPosition: number; // Starting position in the overall dataset
    maxResults: number; // Maximum number of results returned
    totalCount?: number; // Total count of payment methods matching the query (if more than maxResults)
  };
  time: string; // ISO 8601 date format for response time
};

type Payment = {
  Id: string; // Unique identifier for the payment
  SyncToken: string; // Sync token for preventing update conflicts
  MetaData: {
    CreateTime: string; // ISO 8601 date format for creation time
    LastUpdatedTime: string; // ISO 8601 date format for last update time
  };
  TotalAmt: number; // Total amount of the payment
  CustomerRef: Ref;
  TxnDate: string;
  Line: Array<PaymentLine>; // Payment lines
  PaymentMethodRef?: Ref;
  DepositToAccountRef: Ref;
  PrivateNote?: string; // Optional, internal note
};

interface TaxCodeRef {
  value: string;
  name: string;
}

interface TaxRateDetail {
  TaxRateRef: TaxCodeRef;
  RateValue: number;
  TaxTypeApplicability: string;
  TaxOrder: number;
}

interface TaxCode {
  Id: string;
  SyncToken: string;
  MetaData: {
    CreateTime: string;
    LastUpdatedTime: string;
  };
  Name: string;
  Description: string;
  Active: boolean;
  TaxRateDetails: TaxRateDetail[];
}
