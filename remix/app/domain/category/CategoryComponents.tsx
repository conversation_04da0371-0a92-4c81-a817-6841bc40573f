import { useAppContext } from "~/hooks/use-app-context";
import React, { Fragment, useId } from "react";
import { ParamLink } from "~/components/meta/CustomComponents";
import { _retail } from "~/misc/paths";
import { Selectable } from "kysely";
import { Category } from "~/kysely/db";

const ParentCategoryOptions = (props: {
  category: { id: string; name: string; parent_category_id: string | null };
  disabledCategoryId?: string | null;
  prefixNames: string[];
}) => {
  const ctx = useAppContext();
  // if (props.disabledCategoryId === props.category.id) return <Fragment />;
  const subCategories = ctx.establishment?.categories.filter((subCat) => subCat.parent_category_id === props.category.id);
  const names = [...props.prefixNames, props.category.name];
  const disabled = props.disabledCategoryId === props.category.id;
  return (
    <Fragment>
      <option disabled={disabled} value={props.category.id}>
        {names.join(" -> ")}
      </option>
      {subCategories?.map((cat) => (
        <ParentCategoryOptions
          key={cat.id}
          category={cat}
          prefixNames={names}
          disabledCategoryId={disabled ? cat.id : props.disabledCategoryId}
        />
      ))}
    </Fragment>
  );
};
export const CategorySelect = (props: { name: string; defaultValue?: string | null; parent?: boolean; disabledCategoryId?: string }) => {
  const ctx = useAppContext();
  const id = useId();
  return (
    <div className="form-control">
      <label htmlFor={id}>{props.parent ? "Parent Category" : "Category"}</label>
      <select id={id} name={props.name} className="select w-full" defaultValue={props.defaultValue || ""}>
        <option value="">-- {props.parent ? "No Parent" : "No Category"} --</option>
        {ctx.establishment?.categories
          .filter((cat) => !cat.parent_category_id)
          .map((cat) => (
            <ParentCategoryOptions key={cat.id} category={cat} prefixNames={[]} disabledCategoryId={props.disabledCategoryId} />
          ))}
      </select>
      {/*{props.parent && <p className="text-sm text-gray-500 mt-1">Select a parent category to create a hierarchical structure</p>}*/}
    </div>
  );
};

export const getCategorySegments = (props: {
  category_id?: string | null;
  categories: Selectable<Category>[];
  acc_cat_names: string[];
}): string[] => {
  const category = props.categories.find((cat) => cat.id === props.category_id);

  if (!category) return props.acc_cat_names;
  return [
    ...getCategorySegments({
      category_id: category.parent_category_id,
      acc_cat_names: props.acc_cat_names,
      categories: props.categories,
    }),
    category.name,
  ];
};

const Seperator = () => <span className="text-slate-700">{">"}</span>;

export const CategoryBreadCrumb = (props: { category_id?: string | null }) => {
  const ctx = useAppContext();
  const establishment = ctx.establishment;
  const category = establishment?.categories.find((cat) => cat.id === props.category_id);

  if (!category) return <Fragment />;

  return (
    <Fragment>
      {category.parent_category_id && <CategoryBreadCrumb category_id={category.parent_category_id} />}
      <Seperator />
      <ParamLink className="text-slate-700 underline last:no-underline" path={_retail} paramState={{ category_id: category.id }}>
        {category.name}
      </ParamLink>
    </Fragment>
  );
};

export const RetailBreadCrumb = (props: { category_id?: string | null; children?: React.ReactNode }) => {
  return (
    <div className="flex flex-wrap gap-2">
      <ParamLink path={_retail} paramState={{ category_id: null }} className="text-slate-700 underline">
        Retail
      </ParamLink>
      <CategoryBreadCrumb category_id={props.category_id} />
      {props.children && (
        <Fragment>
          <Seperator />
          {props.children}
        </Fragment>
      )}
    </div>
  );
};
