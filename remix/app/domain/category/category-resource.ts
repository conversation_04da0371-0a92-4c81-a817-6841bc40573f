import { memberIsAdminQb } from "~/domain/member/member-queries.server";
import { removeObjectKeys } from "~/misc/helpers";
import { Args } from "~/server/resource/resource-helpers.server";
import { v4 } from "uuid";

export const categoryResource: Args<"category"> = {
  authorize: (args) => {
    return memberIsAdminQb(args)
      .innerJoin("category", "category.establishment_id", "_member.establishment_id")
      .leftJoin("category as parentCategory", "parentCategory.id", "category.parent_category_id")
      .where((eb) =>
        eb.or([
          eb("parentCategory.establishment_id", "is", null),
          eb("parentCategory.establishment_id", "=", eb.ref("category.establishment_id")),
        ]),
      )
      .where((eb) => eb.or([eb("category.parent_category_id", "is", null), eb("category.parent_category_id", "!=", eb.ref("category.id"))]))
      .where("category.id", "=", args.id)
      .executeTakeFirst();
  },
  beforeMutate: async (args) => {
    const name = args.data?.name;
    const finalName = typeof name === "string" && name && name.trim();
    const finalKey = finalName && finalName.toLowerCase();
    return {
      operation: args.operation,
      id: args.id || v4(),
      data: args.data && {
        ...args.data,
        key: finalKey || undefined,
        name: finalName || undefined,
      },
    };
  },
  insert: (args) => args.data,
  update: (args) => removeObjectKeys(args.data, "establishment_id"),
  delete: () => true,
};
