import React from "react";
import type { Kysely } from "kysely";
import type { DB } from "~/kysely/db";
import { createMim } from "~/server/mail/email.client.server";
import { getMailContextForPrefix } from "~/server/mail/email.client.server";
import { getWhitelabelFromHost } from "~/utils/domain-helpers";
import { getFullUrl } from "~/misc/helpers";
import { _booking_detail } from "~/misc/paths";
import { EmailMarkdocComp } from "~/domain/email/email-components";
import { baseEmailMarkdocComps } from "~/domain/email/email-markdoc";
import { ActivityDetails, ParticipantInfo, BookingLink, EstablishmentContact } from "~/domain/email/email-template-components";
import { defaultPostActivityEmailTemplate } from "~/domain/email/post-activity-email-template";

export interface PostActivityEmailData {
  participant_id: string;
  sale_item_id: string;
  establishment_id: string;
  participant_name: string;
  participant_email: string;
  activity_name: string;
  activity_date: string | null;
  establishment_name: string;
  establishment_email: string | null;
  booking_id: string;
  operator_name: string;
  host: string;
}

export const sendPostActivityEmail = async (trx: Kysely<DB>, data: PostActivityEmailData) => {
  const establishment = await trx
    .selectFrom("establishment")
    .innerJoin("operator", "operator.id", "establishment.operator_id")
    .where("establishment.id", "=", data.establishment_id)
    .select([
      // "establishment.post_activity_email_template",
      // "establishment.post_activity_email_subject",
      "establishment.email as establishment_email",
      "operator.name as operator_name",
    ])
    .executeTakeFirst();

  if (!establishment) {
    throw new Error("Establishment not found");
  }

  // const template = establishment.post_activity_email_template || defaultPostActivityEmailTemplate;
  // const subject = establishment.post_activity_email_subject || "Thank you for your activity - How was your experience?";

    const template =  defaultPostActivityEmailTemplate;
  const subject = "Thank you for your activity - How was your experience?";

  const bookingUrl = getFullUrl(data.host) + _booking_detail(data.booking_id);

  const emailVars = {
    participant_name: data.participant_name,
    participant_email: data.participant_email,
    activity_name: data.activity_name,
    activity_date: data.activity_date,
    establishment_name: data.establishment_name,
    establishment_email: establishment.establishment_email || data.establishment_email,
    booking_id: data.booking_id,
    booking_url: bookingUrl,
    operator_name: establishment.operator_name || data.operator_name,
  };

  const emailBody = (
    <EmailMarkdocComp
      content={template}
      comps={{
        ...baseEmailMarkdocComps,
        ActivityDetails: () => <ActivityDetails vars={emailVars} />,
        ParticipantInfo: () => <ParticipantInfo vars={emailVars} />,
        BookingLink: () => <BookingLink vars={emailVars} />,
        EstablishmentContact: () => <EstablishmentContact vars={emailVars} />,
      }}
      vars={emailVars}
    />
  );

  const prefix = getWhitelabelFromHost(data.host);
  const mailCtx = await getMailContextForPrefix(trx, prefix);
  const { msg, send } = await createMim(emailBody);

  msg.setSubject(subject);
  msg.setSender(mailCtx.sender);
  msg.setTo({ addr: data.participant_email, name: data.participant_name });

  await send();

  return [true, `Sent post-activity email to participant "${data.participant_email}"`];
};
