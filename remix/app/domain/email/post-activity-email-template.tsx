import React from "react";
import { EmailMarkdocComp } from "~/domain/email/email-components";
import { baseEmailMarkdocComps } from "~/domain/email/email-markdoc";
import { ActivityDetails, ParticipantInfo, BookingLink, EstablishmentContact } from "~/domain/email/email-template-components";
import { EmailProps } from "~/domain/email/email-components";

export const defaultPostActivityEmailTemplate = `# Thank You for Your Activity!

Dear {% $participant_name %},

Thank you for participating in our **{% $activity_name %}** activity on **{% $activity_date %}**. We hope you had an amazing experience!

## How Was Your Experience?

We'd love to hear about your adventure! Your feedback helps us improve and helps other travelers make informed decisions.

{% button href="{% $booking_url %}" %}Leave a Review{% /button %}

## Activity Summary

{% activity-details /%}

## Your Information

{% participant-info /%}

## Need Help?

If you have any questions about your activity or need assistance with anything else, please don't hesitate to reach out to us.

{% establishment-contact /%}

## Stay Connected

Follow us on social media to see photos from recent activities and stay updated on upcoming adventures!

We look forward to welcoming you back for more exciting experiences!

Best regards,  
The {% $operator_name %} Team`;

export const defaultPostActivityEmailSubject = "Thank you for your activity - How was your experience?";

export const PostActivityEmailTemplate = (props: { vars: EmailProps }) => {
  return (
    <EmailMarkdocComp
      content={defaultPostActivityEmailTemplate}
      comps={{
        ...baseEmailMarkdocComps,
        ActivityDetails: () => <ActivityDetails vars={props.vars} />,
        ParticipantInfo: () => <ParticipantInfo vars={props.vars} />,
        BookingLink: () => <BookingLink vars={props.vars} />,
        EstablishmentContact: () => <EstablishmentContact vars={props.vars} />,
      }}
      vars={props.vars}
    />
  );
};
