import { kysely } from "~/misc/database.server";
import { type InferResult, sql } from "kysely";
import {
  addToNow,
  contextRef,
  formatDate,
  formatDatetime,
  localToUtc,
  lower,
  multiply,
  nowValue,
  pInterval,
  subtract,
} from "~/kysely/kysely-helpers";

export const activityLocalStartDateFixedTime = sql<string>`(${lower(contextRef("sale_item", "duration"))} + time '09:00')`;

export const activityUTCStartDateFixedTime = localToUtc(activityLocalStartDateFixedTime, contextRef("region", "timezone"));

export const participantActivityReminderQuery = kysely
  .selectFrom("sale_item")
  .innerJoin("booking", "booking.id", "sale_item.booking_id")
  .innerJoin("participation", "participation.sale_item_id", "sale_item.id")
  .innerJoin("participant", "participant.id", "participation.participant_id")
  .innerJoin("customer", "customer.id", "participant.customer_id")
  .innerJoin("person", "person.id", "customer.person_id")
  .innerJoin("user", "user.id", "person.user_id")
  .innerJoin("establishment", "establishment.id", "booking.establishment_id")
  .innerJoin("operator", "operator.id", "establishment.operator_id")
  .innerJoin("spot", "spot.id", "establishment.spot_id")
  .innerJoin("region", "region.id", "spot.region_id")
  .select((eb) => [
    "participation.id as participation_id",
    "participant.id as participant_id",
    "participant.cached_read_waivers_valid",
    "participant.cached_incomplete_fields",
    "participant.cached_signature_waivers_valid",
    "person.full_name as participant_full_name",
    "establishment.activity_reminder_in_days_before_start",
    "sale_item.duration",
    "operator.name as operator_name",
    "establishment.email as establishment_email",
    "booking.id as booking_id",
    "booking.host as booking_host",
    "sale_item.id as sale_item_id",
    "operator.name",
    "operator.slug",
    "user.id as user_id",
    "user.email as participant_email",
    "customer.establishment_id",
    "region.timezone",
    activityLocalStartDateFixedTime.as("activity_local_start_date_fixed_time"),
    formatDate(lower(eb.ref("sale_item.duration")), "DD Mon YYYY").as("activity_start_date_formatted"),
    activityUTCStartDateFixedTime.as("activity_utc_start_date_fixed_time"),
    formatDatetime(
      subtract(activityUTCStartDateFixedTime, multiply(eb.ref("establishment.activity_reminder_in_days_before_start"), pInterval("days"))),
      "DD Mon YYYY, HH24:MI",
      eb.val("UTC"),
    ).as("scheduled_date_time_in_utc"),
    formatDatetime(
      subtract(activityUTCStartDateFixedTime, multiply(eb.ref("establishment.activity_reminder_in_days_before_start"), pInterval("days"))),
      "DD Mon YYYY, HH24:MI",
      contextRef("region", "timezone"),
    ).as("scheduled_local_datetime_formatted"),
  ]);

export const upcomingRemindersQuery = participantActivityReminderQuery
  .where("sale_item.duration", "is not", null)
  .where("establishment.activity_reminder_in_days_before_start", "is not", null)
  .where((eb) =>
    eb.not(
      eb.exists(
        eb
          .selectFrom("mail")
          .where("mail.participant_id", "=", eb.ref("participant.id"))
          .where("mail.sale_item_id", "=", eb.ref("sale_item.id")),
      ),
    ),
  )
  .select((eb) =>
    eb(activityUTCStartDateFixedTime, "<", addToNow(contextRef("establishment", "activity_reminder_in_days_before_start"), "days")).as(
      "ready_to_send",
    ),
  )
  .where(activityUTCStartDateFixedTime, ">", nowValue)
  .orderBy(activityUTCStartDateFixedTime, "asc");

export const toBeSentRemindersQuery = upcomingRemindersQuery.where(
  activityUTCStartDateFixedTime,
  "<",
  addToNow(contextRef("establishment", "activity_reminder_in_days_before_start"), "days"),
);

export type ParticipationActivityReminder = InferResult<typeof participantActivityReminderQuery>[0];
