import { Config, parse, renderers, transform, validate } from "@markdoc/markdoc";
import React from "react";
import { fallbackNodes, EmailMarkdocComps, emailMarkDocNodes, emailMarkdocTags } from "~/domain/email/email-markdoc";
import { entries, keys } from "~/misc/helpers";
import { Head, Html } from "@react-email/components";
import { TailwindBody } from "~/components/Mail";

interface EmailVars {
  participant_name: string;
  participant_email: string;
  activity_name: string;
  activity_date: string;
  establishment_name: string;
  establishment_email: string;
  booking_id: string;
  booking_url: string;
  operator_name: string;
}

export interface EmailProps {
  participant_name?: string;
  participant_email?: string;
  activity_name?: string;
  activity_date?: string | null;
  establishment_name?: string;
  establishment_email?: string | null;
  booking_id?: string;
  booking_url?: string;
  operator_name?: string;
}

export const EmailMarkdocComp = (props: { content: string; vars: EmailProps; comps: Partial<EmailMarkdocComps> }) => {
  const participantName = props.vars.participant_name || "{participant_name}";
  const participantEmail = props.vars.participant_email || "{participant_email}";
  const activityName = props.vars.activity_name || "{activity_name}";
  const activityDate = props.vars.activity_date || "{activity_date}";
  const establishmentName = props.vars.establishment_name || "{establishment_name}";
  const establishmentEmail = props.vars.establishment_email || "{establishment_email}";
  const bookingId = props.vars.booking_id || "{booking_id}";
  const bookingUrl = props.vars.booking_url || "{booking_url}";
  const operatorName = props.vars.operator_name || "{operator_name}";

  const vars: EmailVars = {
    participant_name: participantName,
    participant_email: participantEmail,
    activity_name: activityName,
    activity_date: activityDate,
    establishment_name: establishmentName,
    establishment_email: establishmentEmail,
    booking_id: bookingId,
    booking_url: bookingUrl,
    operator_name: operatorName,
  };

  const ast2 = parse(props.content);

  const compInEmail: Partial<Record<keyof typeof fallbackNodes, boolean>> = {};
  for (let node of ast2.walk()) {
    keys(fallbackNodes).forEach((key) => {
      if (node.tag === key) {
        compInEmail[key] = true;
      }
    });
  }

  const finalNodes = { ...emailMarkDocNodes };
  entries(emailMarkDocNodes).forEach(([key, node]) => {
    const renderName = node.render;
    if (renderName && !props.comps[renderName as keyof typeof props.comps]) {
      delete finalNodes[key];
    }
  });

  const finalTags = { ...emailMarkdocTags };
  entries(emailMarkdocTags).forEach(([key, node]) => {
    const renderName = node.render;
    if (renderName && !props.comps[renderName as keyof typeof props.comps]) {
      delete finalTags[key];
    }
  });

  const config: Config = {
    variables: vars,
    nodes: finalNodes,
    tags: finalTags,
  };

  const validatedas = validate(ast2, config);
  console.log("validation", validatedas);
  const rendereableNodes = transform(ast2, config);

  return (
    <Html>
      <Head />
      <TailwindBody>
        <div className="space-y-6">
          <div className="space-y-3">{renderers.react(rendereableNodes, React, { components: props.comps })}</div>
          {!compInEmail["activity-details"] && props.comps?.ActivityDetails && <props.comps.ActivityDetails />}
          {!compInEmail["participant-info"] && props.comps?.ParticipantInfo && <props.comps.ParticipantInfo />}
          {!compInEmail["booking-link"] && props.comps?.BookingLink && <props.comps.BookingLink />}
          {!compInEmail["establishment-contact"] && props.comps?.EstablishmentContact && <props.comps.EstablishmentContact />}
        </div>
      </TailwindBody>
    </Html>
  );
};
