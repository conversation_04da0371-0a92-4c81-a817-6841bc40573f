import React from "react";
import { Section, Text, Link, Heading } from "@react-email/components";
import { EmailProps } from "~/domain/email/email-components";
import { getFullUrl } from "~/misc/helpers";
import { _booking_detail } from "~/misc/paths";

export const ActivityDetails = (props: { vars: EmailProps }) => {
  const { vars } = props;
  return (
    <Section className="bg-slate-50 p-4 rounded-md">
      <Heading as="h3" className="text-lg font-semibold mb-2">
        Activity Details
      </Heading>
      <Text className="mb-1">
        <strong>Activity:</strong> {vars.activity_name}
      </Text>
      <Text className="mb-1">
        <strong>Date:</strong> {vars.activity_date}
      </Text>
      <Text className="mb-1">
        <strong>Booking ID:</strong> {vars.booking_id}
      </Text>
    </Section>
  );
};

export const ParticipantInfo = (props: { vars: EmailProps }) => {
  const { vars } = props;
  return (
    <Section className="bg-blue-50 p-4 rounded-md">
      <Heading as="h3" className="text-lg font-semibold mb-2">
        Participant Information
      </Heading>
      <Text className="mb-1">
        <strong>Name:</strong> {vars.participant_name}
      </Text>
      <Text className="mb-1">
        <strong>Email:</strong> {vars.participant_email}
      </Text>
    </Section>
  );
};

export const BookingLink = (props: { vars: EmailProps }) => {
  const { vars } = props;
  const bookingUrl = vars.booking_url || getFullUrl("") + _booking_detail(vars.booking_id || "");

  return (
    <Section className="text-center">
      <Link href={bookingUrl} className="bg-primary text-white px-6 py-3 rounded-md font-semibold no-underline inline-block">
        View Your Booking
      </Link>
    </Section>
  );
};

export const EstablishmentContact = (props: { vars: EmailProps }) => {
  const { vars } = props;
  return (
    <Section className="bg-slate-50 p-4 rounded-md">
      <Heading as="h3" className="text-lg font-semibold mb-2">
        Contact Information
      </Heading>
      <Text className="mb-1">
        <strong>Establishment:</strong> {vars.establishment_name}
      </Text>
      <Text className="mb-1">
        <strong>Email:</strong> {vars.establishment_email}
      </Text>
      <Text className="mb-1">
        <strong>Operator:</strong> {vars.operator_name}
      </Text>
    </Section>
  );
};
