import { kysely } from "~/misc/database.server";
import { addToNow, formatDate, formatDatetime, multiply, pInterval, subtract } from "~/kysely/kysely-helpers";
import { contextRef } from "~/kysely/kysely-helpers";
import { upper } from "../../kysely/kysely-helpers";

export interface PostActivityEmailData {
  participant_id: string;
  sale_item_id: string;
  establishment_id: string;
  participant_name: string;
  participant_email: string;
  activity_name: string;
  activity_date: string;
  establishment_name: string;
  establishment_email: string;
  booking_id: string;
  operator_name: string;
  host: string;
}

const activityEndDateFixedTime = upper(contextRef("sale_item", "duration"));

export const postActivityEmailQuery = kysely
  .selectFrom("sale_item")
  .innerJoin("booking", "booking.id", "sale_item.booking_id")
  .innerJoin("participation", "participation.sale_item_id", "sale_item.id")
  .innerJoin("participant", "participant.id", "participation.participant_id")
  .innerJoin("customer", "customer.id", "participant.customer_id")
  .innerJoin("person", "person.id", "customer.person_id")
  .innerJoin("user", "user.id", "person.user_id")
  .innerJoin("establishment", "establishment.id", "booking.establishment_id")
  .innerJoin("operator", "operator.id", "establishment.operator_id")
  .innerJoin("spot", "spot.id", "establishment.spot_id")
  .innerJoin("region", "region.id", "spot.region_id")
  .select((eb) => {
    return [
      "participation.id as participation_id",
      "participant.id as participant_id",
      "person.full_name as participant_full_name",
      "establishment.post_activity_email_days_after",
      "sale_item.duration",
      "operator.name as operator_name",
      "establishment.email as establishment_email",
      "booking.id as booking_id",
      "booking.host as booking_host",
      "sale_item.id as sale_item_id",
      "establishment.id as establishment_id",
      "user.id as user_id",
      "user.email as participant_email",
      "region.timezone",
      activityEndDateFixedTime.as("activity_end_date_fixed_time"),
      formatDate(activityEndDateFixedTime, "DD Mon YYYY").as("activity_end_date_formatted"),
      formatDatetime(addToNow(eb.ref("establishment.post_activity_email_days_after"), "days"), "DD Mon YYYY, HH24:MI", eb.val("UTC")).as(
        "scheduled_date_time_in_utc",
      ),
      formatDatetime(
        addToNow(eb.ref("establishment.post_activity_email_days_after"), "days"),
        "DD Mon YYYY, HH24:MI",
        contextRef("region", "timezone"),
      ).as("scheduled_local_datetime_formatted"),
    ];
  });

export const upcomingPostActivityEmailsQuery = postActivityEmailQuery
  .where("sale_item.duration", "is not", null)
  .where("establishment.post_activity_email_days_after", "is not", null)
  .where((eb) =>
    eb.not(
      eb.exists(
        eb
          .selectFrom("mail")
          .where("mail.participant_id", "=", eb.ref("participant.id"))
          .where("mail.sale_item_id", "=", eb.ref("sale_item.id"))
          // .where("mail.template_name", "=", "post_activity"),
      ),
    ),
  )
  .select((eb) =>
    eb(activityEndDateFixedTime, "<", addToNow(eb.ref("establishment.post_activity_email_days_after"), "days")).as("ready_to_send"),
  )
  .where(activityEndDateFixedTime, "<", addToNow(0, "days"))
  .orderBy(activityEndDateFixedTime, "asc");

export const toBeSentPostActivityEmailsQuery = upcomingPostActivityEmailsQuery.where(activityEndDateFixedTime, "<", addToNow(0, "days"));
