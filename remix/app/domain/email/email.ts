export const emailCancelledMsg = "Email cancelled";

const emails = {
  activity_reminder: {
    name: "Activity reminder",
  },
  post_activity: {
    name: "Post activity",
  },
};

export type EmailKey = keyof typeof emails;

export const getEmail = (email: keyof typeof emails) => {
  return emails[email];
};

export const emailTemplates = {
  activity_reminder: 'Activity Reminder'
} as const;

export type EmailTemplateKey = keyof typeof emailTemplates;
