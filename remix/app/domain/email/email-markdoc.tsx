import React, { Fragment, ReactNode } from "react";
import { NodeType, Schema } from "@markdoc/markdoc";
import { Head, Html, Section, Text, Link, Heading, Hr, Button, Container, Body } from "@react-email/components";
import { TailwindBody } from "~/components/Mail";
import { twMerge } from "tailwind-merge";

const formatStringToCamelCase = (str: string) => {
  const splitted = str.split("-");
  if (splitted.length === 1) return splitted[0] || "";
  return (
    splitted[0] +
      splitted
        .slice(1)
        .map((word) => (word[0]?.toUpperCase() || "") + word.slice(1))
        .join("") || ""
  );
};

export const getStyleObjectFromString = (str: string) => {
  const style: Record<string, string> = {};
  str.split(";").forEach((el) => {
    const [property, value] = el.split(":");
    if (!property) return;

    const formattedProperty = formatStringToCamelCase(property.trim());
    style[formattedProperty] = value!.trim();
  });
  return style;
};

export const EmailMarkDocLink = (props: { children: ReactNode; href: string; title?: string; className?: string }) => {
  return (
    <Link className={twMerge("text-primary", props.className)} target="_blank" href={props.href} title={props.title}>
      {props.children}
    </Link>
  );
};

export const EmailMarkDocHeading = (props: { children: ReactNode; level: number; className?: string }) => {
  const levels = [
    <Heading as="h1" className={twMerge("text-2xl font-bold", props.className)}>
      {props.children}
    </Heading>,
    <Heading as="h2" className={twMerge("text-xl font-semibold", props.className)}>
      {props.children}
    </Heading>,
    <Heading as="h3" className={twMerge("font-semibold", props.className)}>
      {props.children}
    </Heading>,
    <Heading as="h4" className={props.className}>
      {props.children}
    </Heading>,
    <Heading as="h5" className={props.className}>
      {props.children}
    </Heading>,
    <Heading as="h6" className={props.className}>
      {props.children}
    </Heading>,
  ];
  return levels[props.level - 1] || props.children;
};

export const EmailMarkDocParagraph = (props: { children: ReactNode; className?: string }) => {
  return <Text className={twMerge("text-slate-600", props.className)}>{props.children}</Text>;
};

export const EmailMarkDocHr = (props: { className?: string }) => {
  return <Hr className={twMerge("border-slate-200", props.className)} />;
};

export const EmailMarkDocList = (props: { ordered?: boolean; children?: ReactNode; className?: string }) => {
  return props.ordered ? (
    <Text className={twMerge("list-decimal list-inside", props.className)}>{props.children}</Text>
  ) : (
    <Text className={twMerge("list-disc list-inside", props.className)}>{props.children}</Text>
  );
};

export const EmailMarkDocItem = (props: { children: ReactNode; className?: string }) => {
  return <Text className={twMerge("pl-3", props.className)}>{props.children}</Text>;
};

export const EmailMarkDocButton = (props: { children: ReactNode; href: string; className?: string }) => {
  return (
    <Button className={twMerge("bg-primary text-white px-6 py-3 rounded-md font-semibold no-underline", props.className)} href={props.href}>
      {props.children}
    </Button>
  );
};

export const EmailMarkDocContainer = (props: { children: ReactNode; className?: string }) => {
  return <Container className={props.className}>{props.children}</Container>;
};

export const EmailMarkDocSection = (props: { children: ReactNode; className?: string }) => {
  return <Section className={props.className}>{props.children}</Section>;
};

export const EmailMarkDocDiv = (props: { children: ReactNode; className?: string; style?: string }) => {
  return (
    <div className={props.className} style={props.style ? getStyleObjectFromString(props.style) : undefined}>
      {props.children}
    </div>
  );
};

export const EmailMarkDocBr = () => <br />;

export const EmailMarkDocU = (props: { children: ReactNode }) => {
  return <span className="underline">{props.children}</span>;
};

export const EmailMarkDocStrong = (props: { children: ReactNode }) => {
  return <strong>{props.children}</strong>;
};

export const EmailMarkDocEm = (props: { children: ReactNode }) => {
  return <em>{props.children}</em>;
};

export const fallbackNodes = {
  "activity-details": { render: "ActivityDetails", selfClosing: true },
  "participant-info": { render: "ParticipantInfo", selfClosing: true },
  "booking-link": { render: "BookingLink", selfClosing: true },
  "establishment-contact": { render: "EstablishmentContact", selfClosing: true },
} satisfies Record<string, Schema>;

export const emailMarkDocNodes = {
  link: {
    render: "Link",
    attributes: { href: { type: "String" } },
  },
  heading: {
    render: "Heading",
    attributes: { level: { type: "Number" } },
  },
  paragraph: {
    render: "Paragraph",
  },
  hr: {
    render: "Hr",
  },
  list: {
    render: "List",
    attributes: { ordered: { type: "Boolean" } },
  },
  item: {
    render: "Item",
  },
} satisfies Partial<Record<NodeType, Schema>>;

export const emailMarkdocTags = {
  u: {
    render: "EmailMarkDocU",
  },
  strong: {
    render: "EmailMarkDocStrong",
  },
  em: {
    render: "EmailMarkDocEm",
  },
  div: {
    render: "EmailMarkDocDiv",
    attributes: { className: { type: "String" }, style: { type: "String" } },
  },
  br: {
    selfClosing: true,
    render: "EmailMarkDocBr",
  },
  button: {
    render: "EmailMarkDocButton",
    attributes: {
      href: { type: "String" },
      className: { type: "String" },
    },
  },
  container: {
    render: "EmailMarkDocContainer",
    attributes: { className: { type: "String" } },
  },
  section: {
    render: "EmailMarkDocSection",
    attributes: { className: { type: "String" } },
  },
  ...fallbackNodes,
} satisfies Record<string, Schema>;

const ActivityDetailsFallbackComp = () => {
  return <div className="py-3 text-rose-500">-- activity details component placeholder --</div>;
};

const ParticipantInfoFallbackComp = () => {
  return <div className="py-3 text-rose-500">-- participant info component placeholder --</div>;
};

const BookingLinkFallbackComp = () => {
  return <div className="py-3 text-rose-500">-- booking link component placeholder --</div>;
};

const EstablishmentContactFallbackComp = () => {
  return <div className="py-3 text-rose-500">-- establishment contact component placeholder --</div>;
};

export const baseEmailMarkdocComps = {
  EmailMarkDocU: EmailMarkDocU,
  EmailMarkDocStrong: EmailMarkDocStrong,
  EmailMarkDocEm: EmailMarkDocEm,
  EmailMarkDocDiv: EmailMarkDocDiv,
  EmailMarkDocBr: EmailMarkDocBr,
  EmailMarkDocButton: EmailMarkDocButton,
  EmailMarkDocContainer: EmailMarkDocContainer,
  EmailMarkDocSection: EmailMarkDocSection,
  Link: EmailMarkDocLink,
  Heading: EmailMarkDocHeading,
  Paragraph: EmailMarkDocParagraph,
  Hr: EmailMarkDocHr,
  List: EmailMarkDocList,
  Item: EmailMarkDocItem,
};

export const allEmailMarkdocComps = {
  ...baseEmailMarkdocComps,
  ActivityDetails: ActivityDetailsFallbackComp,
  ParticipantInfo: ParticipantInfoFallbackComp,
  BookingLink: BookingLinkFallbackComp,
  EstablishmentContact: EstablishmentContactFallbackComp,
};

export type EmailMarkdocComps = Record<keyof typeof allEmailMarkdocComps, any>;
