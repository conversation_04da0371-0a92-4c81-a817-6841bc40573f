import type { ReactNode } from "react";
import { Tailwind } from "@react-email/components";

export const TailwindCustom = (props: { children: ReactNode }) => {
  return (
    <Tailwind
      config={{
        theme: {
          colors: {
            primary: {
              light: "#FF9780",
              DEFAULT: "#FF7557",
              100: "#ffe0db",
              200: "#fcd5cf",
              300: "#ffb9b0",
              400: "#ffa091",
              600: "#FF471F",
              700: "#ec360b",
              800: "#E62900",
              dark: "#E62900",
            },
            secondary: {
              50: "#E8F2F6",
              100: "#BCDCFB",
              200: "#b4d9fc",
              300: "#9ecaef",
              500: "#8BB7C9",
              600: "#77b3cb",
              700: "#5ca9c7",
              stroke: "#CCD6EA",
              tag: "#8BB7C9",
              DEFAULT: "#8BB7C9",
            },
          },
        },
      }}
    >
      {props.children}
    </Tailwind>
  );
};
