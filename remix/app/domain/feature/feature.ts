import { keys } from "~/misc/helpers";

export const features = {
  // whitelabel: `-white label`,
  // participant_registration: `- participant without booking
  // - connect booking later`,
  // boatsharing: "- owner of operator location will see a boatsharing link on the top operator location page, from there it shows itself.",
  i18: "i18",
  views: "views for detailed day view",
} satisfies Record<string, string>;

export type FeatureKey = keyof typeof features;
export const featureKeys = keys(features);
