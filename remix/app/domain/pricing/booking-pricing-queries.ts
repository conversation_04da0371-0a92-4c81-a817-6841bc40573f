import { kysely } from "~/misc/database.server";
import { contextRef, multiply } from "~/kysely/kysely-helpers";
import { activityFinalPriceSelect } from "~/domain/pricing/pricing-queries";
import { activityAddonTotalFinalPriceSelect, activityAddonTotalRawPriceSelect } from "~/domain/pricing/activity-addon-pricing-queries";
import { activityWithaddonsTotalTaxSelect } from "~/domain/pricing/activity-pricing-queries";
import { Kysely, sql } from "kysely";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { paymentSurcharcheAmountSelect } from "~/domain/payment/payment-queries.server";
import { DB } from "~/kysely/db";

const activityAddonQb = kysely
  .selectFrom("sale_item")
  .innerJoin("activity_addon", "sale_item.id", "activity_addon.sale_item_id")
  .where("sale_item.booking_id", "=", contextRef("booking", "id"));

const activitiesQb = kysely.selectFrom("sale_item").where("sale_item.booking_id", "=", contextRef("booking", "id"));

const paymentsQb = kysely
  .selectFrom("payment")
  .where("payment.deleted_at", "=", at_infinity_value)
  .where("payment.booking_id", "=", contextRef("booking", "id"));

const paymentsPayedQb = paymentsQb.where("payment.payed_at", "is not", null);

const payedSurchargeQb = paymentsPayedQb.select(paymentSurcharcheAmountSelect.as("price"));
const activityFinalPriceQb = activitiesQb.select((eb) => multiply(eb.ref("sale_item.quantity"), activityFinalPriceSelect).as("price"));

const activityRawPriceQb = activitiesQb.select((eb) => multiply(eb.ref("sale_item.quantity"), eb.ref("sale_item.price_pp")).as("price"));
const addonRawPricesQb = activityAddonQb.select(activityAddonTotalRawPriceSelect.as("price"));
const addonFinalPriceQb = activityAddonQb.select(activityAddonTotalFinalPriceSelect.as("price"));

const bookingRawAmountSelect = kysely.fn.coalesce(
  kysely
    .selectFrom(activityRawPriceQb.unionAll(addonRawPricesQb).as("prices"))
    .select((eb) => eb.fn.sum<number>("prices.price").as("price")),
  sql.lit(0),
);

export const bookingFinalAmountSelect = kysely.fn.coalesce(
  kysely
    .selectFrom(activityFinalPriceQb.unionAll(addonFinalPriceQb).as("prices"))
    .select((eb) => eb.fn.sum<number>("prices.price").as("price")),
  sql.lit(0),
);

export const bookingPayedSurchargeAmountSelect = kysely.fn.coalesce(
  kysely.selectFrom(payedSurchargeQb.as("prices")).select((eb) => eb.fn.sum<number>("prices.price").as("price")),
  sql.lit(0),
);

const bookingPaymentAmountSelect = paymentsQb.select((eb) =>
  eb.fn.coalesce(eb.fn.sum<number>(eb.ref("payment.amount")), sql.lit(0)).as("summed_amount"),
);

export const bookingPayedTotalAmountRawSelect = kysely.fn.coalesce(
  kysely
    .selectFrom(paymentsPayedQb.select("payment.amount as price").as("prices"))
    .select((eb) => eb.fn.sum<number>("prices.price").as("price")),
  sql.lit(0),
);

const activityTaxQb = activitiesQb.select((eb) => activityWithaddonsTotalTaxSelect.as("tax"));

export const bookingTotalTaxSelect = kysely.fn.coalesce(
  kysely.selectFrom(activityTaxQb.as("taxes")).select((eb) => eb.fn.sum<number>("taxes.tax").as("tax")),
  sql.lit(0),
);

export const bookingPriceSelects = [
  bookingFinalAmountSelect.as("price_total_final"),
  bookingRawAmountSelect.as("price_total_raw"),
  bookingPaymentAmountSelect.as("payment_amount_raw"),
  bookingPayedTotalAmountRawSelect.as("payed_amount_raw"),
  bookingPayedSurchargeAmountSelect.as("payed_amount_surcharge"),
  bookingPaymentAmountSelect.where("payment.payed_at", "is not", null).as("payed_amount"),
  bookingTotalTaxSelect.as("price_total_tax"),
];

export const updateBookingsQb = (db: Kysely<DB>) => {
  return db.updateTable("booking").set((eb) => ({
    cached_final_amount: bookingFinalAmountSelect,
    cached_final_paid: bookingPayedTotalAmountRawSelect,
    cached_duration: db
      .selectFrom("sale_item")
      .where("sale_item.booking_id", "=", eb.ref("booking.id"))
      .where("sale_item.duration", "is not", null)
      .select((eb) => eb.fn<string | null>("range_agg", [eb.ref("sale_item.duration")]).as("multidaterange")),
  }));
};
