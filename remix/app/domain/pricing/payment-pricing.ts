import { kysely } from "~/misc/database.server";
import { contextRef } from "~/kysely/kysely-helpers";
import { sql } from "kysely";
import { at_infinity_value } from "~/kysely/db-static-vars";

const summedPayments = kysely
  .selectFrom("payment as _summed_payment")
  .where("_summed_payment.deleted_at", "=", at_infinity_value)
  .where("_summed_payment.created_at", "<=", contextRef("payment", "created_at"))
  .where("_summed_payment.booking_id", "=", contextRef("payment", "booking_id"));

export const summedPaymentAmount = kysely.fn.coalesce(
  summedPayments.select((eb) => eb.fn.sum<number>(eb.ref("_summed_payment.amount")).as("summed_amount")),
  sql.lit(0),
);
