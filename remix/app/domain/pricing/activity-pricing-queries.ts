import { kysely } from "~/misc/database.server";
import { contextRef, divide, multiply, plus } from "~/kysely/kysely-helpers";
import {
  activityAddonFinalPriceSelect,
  activityFinalPriceSelect,
  participationAddonFinalPriceSelect,
} from "~/domain/pricing/pricing-queries";
import { Expression, sql } from "kysely";

const activityAddonsQb = kysely.selectFrom("activity_addon").where("activity_addon.sale_item_id", "=", contextRef("sale_item", "id"));

const activityAddonPricesQb = activityAddonsQb.select(activityAddonFinalPriceSelect.as("price"));

const participationAddonPricesQb = activityAddonsQb
  .innerJoin("participation", "participation.sale_item_id", "activity_addon.sale_item_id")
  .innerJoin("participation_addon", (join) =>
    join
      .onRef("participation_addon.participation_id", "=", "participation.id")
      .onRef("participation_addon.addon_id", "=", "activity_addon.addon_id"),
  )
  .select((eb) => participationAddonFinalPriceSelect.as("price"));

export const activityTotalPriceRaw = sql<number>`(${contextRef("sale_item", "quantity")} * ${contextRef("sale_item", "price_pp")})`;

const addonsPrices = kysely.selectFrom(participationAddonPricesQb.unionAll(activityAddonPricesQb).as("prices"));

export const activityWithaddonsTotalPriceSelect = plus(
  multiply(contextRef("sale_item", "quantity"), activityFinalPriceSelect),
  addonsPrices.select((eb) => eb.fn.coalesce(eb.fn.sum<number>("prices.price"), eb.lit(0)).as("price")) as Expression<number>,
);

export const activityWithaddonsTotalTaxSelect = multiply(
  activityWithaddonsTotalPriceSelect,
  divide(kysely.fn.coalesce(contextRef("booking", "vat_rate"), sql.lit(0)), sql.lit(100)),
);
