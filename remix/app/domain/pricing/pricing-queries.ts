import { kysely } from "~/misc/database.server";
import { contextRef, round, subtractPercentage } from "~/kysely/kysely-helpers";
import { sql } from "kysely";

export const addonDiscount = kysely
  .case()
  .when(contextRef("sale_item", "exclude_discount_for_addons"), "=", true)
  .then(sql.lit(0))
  .else(contextRef("sale_item", "discount_percentage"))
  .end();

const currencyDecimals = kysely
  .selectFrom("currency")
  .select("currency.decimals")
  .where("currency.id", "=", contextRef("booking", "currency_id"));

export const activityFinalPriceSelect = round(
  subtractPercentage(contextRef("sale_item", "price_pp"), contextRef("sale_item", "discount_percentage")),
  currencyDecimals,
);

export const activityAddonFinalPriceSelect = sql<number>`(${contextRef("activity_addon", "quantity")} * ${round(
  subtractPercentage(contextRef("activity_addon", "price_amount"), addonDiscount),
  currencyDecimals,
)})`;

export const participationAddonFinalPriceSelect = sql<number>`(${contextRef("participation_addon", "quantity")} * ${round(
  subtractPercentage(contextRef("activity_addon", "price_amount"), addonDiscount),
  currencyDecimals,
)})`;
