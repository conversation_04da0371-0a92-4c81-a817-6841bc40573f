import { kysely } from "~/misc/database.server";
import { contextRef, multiply, subtractPercentage, plus } from "~/kysely/kysely-helpers";
import { sql } from "kysely";
import { addonDiscount } from "~/domain/pricing/pricing-queries";

const participationAddonsQuantityQb = kysely
  .selectFrom("participation")
  .innerJoin("participation_addon", "participation_addon.participation_id", "participation.id")
  .where("participation_addon.addon_id", "=", contextRef("activity_addon", "addon_id"))
  .where("participation.sale_item_id", "=", contextRef("activity_addon", "sale_item_id"))
  .where(contextRef("activity_addon", "allow_change"), "=", true)
  .select((eb) => eb.fn.sum<number | null>("participation_addon.quantity").as("quantity"));

export const activityAddonQuantityQb = plus(
  contextRef("activity_addon", "quantity"),
  kysely.fn.coalesce(participationAddonsQuantityQb, sql.lit(0)),
);

export const activityAddonTotalRawPriceSelect = multiply(activityAddonQuantityQb, contextRef("activity_addon", "price_amount"));

export const activityAddonTotalFinalPriceSelect = multiply(
  activityAddonQuantityQb,
  subtractPercentage(contextRef("activity_addon", "price_amount"), addonDiscount),
);
