import { createMim } from "~/server/mail/email.client.server";
import { z } from "zod";
import { kysely } from "~/misc/database.server";
import { entries } from "~/misc/helpers";
import { addToNow, nowValue } from "~/kysely/kysely-helpers";
import { diversdeskNoReplyEmail } from "~/misc/consts";
import { Col<PERSON><PERSON>, Head, Html, Link, Section, Text } from "@react-email/components";
import { TailwindBody } from "~/components/Mail";
import React from "react";
import { fields, getSignupType } from "~/domain/signup_submission/signup-submission";
import { ResourceError } from "~/utils/error";
import { Args } from "~/server/resource/resource-helpers.server";

const diversDeskInfoAddress = "<EMAIL>";

export const signupSubmissionResource: Args<"signup_submission"> = {
  authorize: () => true,
  insert: async (args) => {
    const email = z.string().toLowerCase().email().parse(args.data.email);
    console.log("captcha score", args.captcha_score);
    if (args.captcha_score < 0.7) throw new Error("error");

    const existingSignupWithin5Min = await args.trx
      .selectFrom("signup_submission")
      .where("signup_submission.created_at", ">", addToNow(-5, "minutes"))
      .where("signup_submission.email", "=", email)
      .executeTakeFirst();

    if (existingSignupWithin5Min) throw new ResourceError("You already signed up");

    args.after_mutations.outsideTransaction.set("signup_submission" + args.id, async () => {
      const submission = args.data;

      const signupType = getSignupType(args.data.type);

      const infoTable = (
        <table>
          {entries(fields).map(([field, fieldImpl]) => {
            return (
              <tr key={field}>
                <td>{fieldImpl.label}</td>
                <Column>{fieldImpl.value(submission[field]) + ""}</Column>
              </tr>
            );
          })}
        </table>
      );

      const { msg, send } = await createMim(<div>{infoTable}</div>);
      msg.setSubject(`${submission.full_name} would like to ${signupType?.subject || "unkown"}`);
      msg.setSender(diversdeskNoReplyEmail);
      msg.setTo(diversDeskInfoAddress);

      const verifyMsg = await createMim(
        <Html>
          <Head />
          <TailwindBody>
            <Section>
              <Text>Hi there,</Text>
              <Text>Thank you for choosing Diversdesk.</Text>
              <Text>
                We have received your sign-up form and will right away get to work setting up your environment. We will get back to you
                within 24 hours.
              </Text>
              <Text>
                Feel free to take a dive into our knowledge base at{" "}
                <Link href={"https://www.diversdesk.com/welcome-to-docs/"}>https://www.diversdesk.com/welcome-to-docs/</Link> to get a head
                start.
              </Text>
            </Section>
            <Section>
              <Text>The sign up data we received from you:</Text>
              <Text>---</Text>
              {infoTable}
              <Text>
                I acknowledge that I have read, understood, and agreed to the Diversdesk{" "}
                <Link href={"https://diversdesk.com/terms-conditions"}>Terms of Service</Link> and{" "}
                <Link href={"https://diversdesk.com/privacy-policy"}>Privacy Policy</Link>
              </Text>
              <Text>---</Text>
            </Section>
            <Section>
              <Text>
                if you have any questions in the meantime, feel free to reach out to us at{" "}
                <Link href={`mailto:${diversDeskInfoAddress}`}>{diversDeskInfoAddress}</Link>. We’re happy to assist.
              </Text>
              <Text>Warm regards,</Text>
              <Text>Team Diversdesk</Text>
            </Section>
          </TailwindBody>
        </Html>,
      );
      verifyMsg.msg.setSubject(`Your sign-up with Diversdesk`);
      verifyMsg.msg.setSender(diversdeskNoReplyEmail);
      verifyMsg.msg.setTo(email);

      await Promise.all([await verifyMsg.send([diversDeskInfoAddress]), await send([email])]);

      await kysely
        .updateTable("signup_submission")
        .where("signup_submission.id", "=", args.id)
        .set({ email_send_at: nowValue })
        .executeTakeFirstOrThrow();
    });

    return {
      ...args.data,
      email_send_at: undefined,
      email: email,
    };
  },
  update: () => false,
  delete: () => false,
};
