import { SignupSubmission } from "~/kysely/db";
import { orNull } from "~/domain/establishment/EstablishmentContact";

export const signupTypes = {
  trial: {
    subject: "14 day trial",
  },
  form: {
    subject: "signup",
  },
} as const;

export const getSignupType = (key: string | null) => orNull(signupTypes[(key as keyof typeof signupTypes) || ""]);

interface FieldImpl {
  label: string;
  value: (val: string | null) => string | null | undefined;
}

export const fields = {
  type: { label: "Type", value: (val) => getSignupType(val)?.subject as string },
  full_name: { label: "Full Name", value: (val) => val },
  job_title: { label: "Position/Job Title", value: (val) => val },
  email: { label: "Email", value: (val) => val },
  phone: { label: "Phone Number", value: (val) => val },
  company: { label: "Company Name", value: (val) => val },
  website: { label: "Company Website", value: (val) => val },
  country: { label: "Country of Operation", value: (val) => val },
  vat_number: { label: "VAT Number", value: (val) => val },
  comment: { label: "Additional Info or Questions", value: (val) => val },
} satisfies Partial<Record<keyof SignupSubmission, FieldImpl>>;
