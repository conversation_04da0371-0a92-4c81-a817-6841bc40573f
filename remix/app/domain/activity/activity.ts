import { keys } from "~/misc/helpers";

const filterKeys = [
  "spot_id",
  "stay",
  "pickup",
  "diving_courses",
  "diving_certificate_level",
  "diving_certificate_organization",
  "diving_locations",
  "diving_sites",
  "gear_included",
  "duration_in_hours",
  "language_code",
] as const;
// export const activitySlugs
export type FilterKey = (typeof filterKeys)[number];

interface Activity {
  name: string;
  totals_name: string | null;
  short: string;
  color: string;
  count_tanks: boolean;
  show_on_marketplace: boolean;
  retail: boolean;
  filters: FilterKey[];
}

export const activities = {
  "fun-diving": {
    name: "Fun diving",
    totals_name: "FD trips",
    short: "FD",
    color: "#8BB7C8",
    count_tanks: true,
    show_on_marketplace: true,
    retail: false,
    filters: ["diving_locations", "diving_sites", "duration_in_hours", "stay", "pickup", "gear_included", "language_code", "spot_id"],
  },
  "diving-course": {
    name: "Diving course",
    totals_name: "Courses",
    short: "CO",
    color: "#B88BC8",
    count_tanks: true,
    show_on_marketplace: true,
    retail: false,
    filters: [
      "diving_certificate_level",
      "diving_certificate_organization",
      "diving_courses",
      "stay",
      "pickup",
      "language_code",
      "spot_id",
    ],
  },
  snorkeling: {
    name: "Snorkeling",
    totals_name: "Snorkel trips",
    short: "SN",
    color: "#8BC8A3",
    count_tanks: false,
    show_on_marketplace: true,
    retail: false,
    filters: ["diving_locations", "diving_sites", "duration_in_hours", "pickup", "language_code", "spot_id"],
  },
  freediving: {
    name: "Freediving",
    totals_name: "Freediving",
    short: "FR",
    color: "#C88B8B",
    count_tanks: false,
    show_on_marketplace: true,
    retail: false,
    filters: ["language_code", "spot_id"],
  },
  "freediving-course": {
    name: "Freediving course",
    totals_name: "Freediving courses",
    short: "FRC",
    color: "brown",
    count_tanks: false,
    show_on_marketplace: false,
    retail: false,
    filters: [],
  },
  other: {
    name: "Other",
    totals_name: null,
    short: "?",
    color: "#ccc",
    count_tanks: false,
    show_on_marketplace: false,
    retail: false,
    filters: [],
  },
  retail: {
    name: "Retail",
    totals_name: null,
    short: "?",
    color: "#ccc",
    count_tanks: false,
    show_on_marketplace: false,
    retail: true,
    filters: [],
  },
  // golf: {
  //   name: "golf",
  //   published: false,
  // },
  // "hydrofoil-surfing": {
  //   name: "hydrofoil surfing",
  //   published: false,
  // },
  // "island-hopping": {
  //   name: "Island hopping",
  //   published: false,
  // },
  // kayaking: {
  //   name: "kayaking",
  //   published: false,
  // },
  // kitesurfing: {
  //   name: "kitesurfing",
  //   published: false,
  // },
  // "quad-riding": {
  //   name: "quad riding",
  //   published: false,
  // },
  // "standup-paddle-boarding": {
  //   name: "stand up paddle boarding",
  //   published: false,
  // },
  // subwing: {
  //   name: "subwing",
  //   published: false,
  //   image: false,
  // },
  // hiking: {
  //   name: "hiking/trekking",
  //   published: false,
  //   image: false,
  // },
  // wakeboarding: {
  //   name: "wakeboarding",
  //   published: false,
  // },
  // waterpark: {
  //   name: "waterpark",
  //   published: false,
  //   image: false,
  // },
  // windsurfing: {
  //   name: "windsurfing",
  //   published: false,
  //   image: false,
  // },
} satisfies Record<string, Activity>;

export type ActivitySlug = keyof typeof activities;

export const activitySlugs = keys(activities);

export const getActivitySlug = (slug?: string | null) => activitySlugs.find((key) => key === slug);

export const getActivityImage = (slug: ActivitySlug) => `images/activities/${slug}.webp`;

export const getActivity = (slug?: string) => (slug && activities[slug as ActivitySlug]) || activities.other;

const defaultFilters: FilterKey[] = ["spot_id"];

export const getFilters = (type?: ActivitySlug) => (type ? activities[type]?.filters : defaultFilters);

export const activeActivitySlugs = keys(activities).filter((slug) => activities[slug].show_on_marketplace);
