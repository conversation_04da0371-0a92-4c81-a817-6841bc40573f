import { format } from "date-fns";
import { toUtc } from "~/misc/date-helpers";

interface DurationObj {
  duration_start: string | null;
  duration_end: string | null;
}

export const formatDuration = (durationObj: DurationObj) => {
  const fromStr = durationObj.duration_start || null;
  const toStr = durationObj.duration_end || null;

  if (!fromStr || !toStr) return "";

  try {
    const from = toUtc(fromStr);
    const to = toUtc(toStr);

    const toFull = format(to, "d MMM yyyy");

    if (fromStr === toStr) return toFull;

    if (from.getFullYear() === to.getFullYear()) {
      if (from.getMonth() === to.getMonth()) {
        return format(from, "d") + " - " + toFull;
      }
      return format(from, "d MMM") + " - " + toFull;
    }

    const fromFull = format(from, "d MMM yyyy");

    return fromFull + " - " + toFull;
  } catch (e) {
    console.error("could not format the date, because method (old browser maybe?) is not suppoerted", e);
    if (fromStr === toStr) return toStr;
    return fromStr + " - " + toStr;
  }
};
