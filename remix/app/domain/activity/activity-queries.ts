import { jsonArrayFrom, jsonObjectFrom } from "kysely/helpers/postgres";
import { sql } from "kysely";
import { baseProductWithSelect, divingCertificatesInItemArray } from "~/domain/product/product-queries.server";
import { kysely } from "~/misc/database.server";
import { activityAddonQuantityQb, activityAddonTotalRawPriceSelect } from "~/domain/pricing/activity-addon-pricing-queries";
import { descNullsLast, formatDatetime } from "~/kysely/kysely-helpers";

export const registrationFormWithFormattedDate = kysely
  .selectFrom("form")
  .selectAll("form")
  .select((eb) => [
    formatDatetime(
      eb.ref("form.created_at"),
      "YYYY.MM.DD HH24:MI:SS",
      eb
        .selectFrom("establishment")
        .innerJoin("spot", "spot.id", "establishment.spot_id")
        .innerJoin("region", "region.id", "spot.region_id")
        .where("establishment.id", "=", eb.ref("form.establishment_id"))
        .select("region.timezone")
        .limit(1),
    ).as("created_at_formatted"),
  ]);

const registrationFormQb = registrationFormWithFormattedDate.select((eb) =>
  jsonArrayFrom(eb.selectFrom("field").whereRef("field.form_id", "=", "form.id").selectAll("field")).as("fields"),
);

export const saleItemSimpleQb = kysely
  .selectFrom("sale_item")
  .selectAll("sale_item")
  .select((eb) => {
    const durationRef = eb.ref("sale_item.duration");
    return [
      sql<number | null>`(upper (${durationRef}) - lower (${durationRef}))`.as("duration_in_days"),
      sql<string | null>`(lower (${durationRef}):: date)`.as("duration_start"),
      sql<string | null>`(upper (${durationRef}):: date - 1)`.as("duration_end"),
    ];
  });

export const activityWithAddons = saleItemSimpleQb.select((eb) => [
  jsonArrayFrom(
    eb
      .selectFrom("activity_addon")
      .where("activity_addon.sale_item_id", "=", eb.ref("sale_item.id"))
      .selectAll("activity_addon")
      .select([activityAddonQuantityQb.as("total_quantity"), activityAddonTotalRawPriceSelect.as("total_price")]),
  ).as("addons"),
  jsonArrayFrom(
    eb
      .selectFrom("participation")
      .where("participation.sale_item_id", "=", eb.ref("sale_item.id"))
      .selectAll("participation")
      .select((eb) =>
        jsonArrayFrom(eb.selectFrom("participation_addon").whereRef("participation_addon.participation_id", "=", "participation.id")).as(
          "addons",
        ),
      ),
  ).as("participations"),
]);

export const saleItemWithProductQb = saleItemSimpleQb.select((eb) => {
  return [
    jsonObjectFrom(registrationFormQb.where("form.id", "=", eb.ref("sale_item.form_id"))).as("registration_form"),
    jsonObjectFrom(baseProductWithSelect.where("product.id", "=", eb.ref("sale_item.product_id"))).as("product"),
    divingCertificatesInItemArray
      .innerJoin("item", "item.id", "item__diving_course.item_id")
      .innerJoin("product", "product.item_id", "item.id")
      .where("product.id", "=", eb.ref("sale_item.product_id"))
      .as("diving_certificate_organizations"),
    jsonArrayFrom(
      eb
        .selectFrom("activity_addon")
        .selectAll("activity_addon")
        .innerJoin("addon", "addon.id", "activity_addon.addon_id")
        .whereRef("activity_addon.sale_item_id", "=", "sale_item.id"),
    ).as("activity_addons"),
  ];
});

export const salesCountSelect = kysely
  .selectFrom("sale_item")
  .innerJoin("booking", "booking.id", "sale_item.booking_id")
  .where("booking.cart_for_session_id", "is", null)
  .where("booking.cancelled_at", "is", null)
  .select((eb) => eb.fn.sum<number>("sale_item.quantity").as("sum"));
