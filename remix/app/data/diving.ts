import { z } from "zod";

export const divingCourseTagKeyParser = z.union([z.literal("first_time_diver"), z.literal("ready_to_start")]);

export type DivingCourseTagKey = z.infer<typeof divingCourseTagKeyParser>;

export const divingCourseTagKeys = divingCourseTagKeyParser.options.map((option) => option.value);

export const divingCourseTagsObj: Record<DivingCourseTagKey, { name: string }> = {
  first_time_diver: { name: "First time diver" },
  ready_to_start: { name: "Ready to start" },
};

export const divingCourseTags = divingCourseTagKeys.map((key) => ({
  id: key,
  ...divingCourseTagsObj[key],
}));
