import type { IdName } from "~/misc/models";

interface Currency {
  code: string;
  description: string;
  decimals?: number;
}

export const currencies: Currency[] = [
  {
    code: "AED",
    description: "United Arab Emirates Dirham",
    decimals: 2,
  },
  {
    code: "AFN",
    description: "Afghan Afghani",
    decimals: 2,
  },
  {
    code: "ALL",
    description: "Albanian Lek",
    decimals: 2,
  },
  {
    code: "AMD",
    description: "Armenian Dram",
    decimals: 2,
  },
  {
    code: "ANG",
    description: "Netherlands Antillean Gulden",
    decimals: 2,
  },
  {
    code: "AOA",
    description: "Angolan Kwanza**",
    decimals: 2,
  },
  {
    code: "ARS",
    description: "Argentine Peso**",
    decimals: 2,
  },
  {
    code: "AUD",
    description: "Australian Dollar",
    decimals: 2,
  },
  {
    code: "AWG",
    description: "Aruban Florin",
    decimals: 2,
  },
  {
    code: "AZN",
    description: "Azerbaijani Manat",
    decimals: 2,
  },
  {
    code: "BAM",
    description: "Bosnia & Herzegovina Convertible Mark",
    decimals: 2,
  },
  {
    code: "BBD",
    description: "Barbadian Dollar",
    decimals: 2,
  },
  {
    code: "BDT",
    description: "Bangladeshi Taka",
    decimals: 2,
  },
  {
    code: "BGN",
    description: "Bulgarian Lev",
    decimals: 2,
  },
  {
    code: "BIF",
    description: "Burundian Franc",
    decimals: 0,
  },
  {
    code: "BMD",
    description: "Bermudian Dollar",
    decimals: 2,
  },
  {
    code: "BND",
    description: "Brunei Dollar",
    decimals: 2,
  },
  {
    code: "BOB",
    description: "Bolivian Boliviano**",
    decimals: 2,
  },
  {
    code: "BRL",
    description: "Brazilian Real**",
    decimals: 2,
  },
  {
    code: "BSD",
    description: "Bahamian Dollar",
    decimals: 2,
  },
  {
    code: "BWP",
    description: "Botswana Pula",
    decimals: 2,
  },
  {
    code: "BZD",
    description: "Belize Dollar",
    decimals: 2,
  },
  {
    code: "CAD",
    description: "Canadian Dollar",
    decimals: 2,
  },
  {
    code: "CDF",
    description: "Congolese Franc",
    decimals: 2,
  },
  {
    code: "CHF",
    description: "Swiss Franc",
    decimals: 2,
  },
  {
    code: "CLP",
    description: "Chilean Peso**",
    decimals: 0,
  },
  {
    code: "CNY",
    description: "Chinese Renminbi Yuan",
    decimals: 2,
  },
  {
    code: "COP",
    description: "Colombian Peso**",
    decimals: 2,
  },
  {
    code: "CRC",
    description: "Costa Rican Colón**",
    decimals: 2,
  },
  {
    code: "CVE",
    description: "Cape Verdean Escudo**",
    decimals: 2,
  },
  {
    code: "CZK",
    description: "Czech Koruna**",
    decimals: 2,
  },
  {
    code: "DJF",
    description: "Djiboutian Franc**",
    decimals: 0,
  },
  {
    code: "DKK",
    description: "Danish Krone",
    decimals: 2,
  },
  {
    code: "DOP",
    description: "Dominican Peso",
    decimals: 2,
  },
  {
    code: "DZD",
    description: "Algerian Dinar",
    decimals: 2,
  },
  {
    code: "EGP",
    description: "Egyptian Pound",
    decimals: 2,
  },
  {
    code: "ETB",
    description: "Ethiopian Birr",
    decimals: 2,
  },
  {
    code: "EUR",
    description: "Euro",
    decimals: 2,
  },
  {
    code: "FJD",
    description: "Fijian Dollar",
    decimals: 2,
  },
  {
    code: "FKP",
    description: "Falkland Islands Pound**",
    decimals: 2,
  },
  {
    code: "GBP",
    description: "British Pound",
    decimals: 2,
  },
  {
    code: "GEL",
    description: "Georgian Lari",
    decimals: 2,
  },
  {
    code: "GIP",
    description: "Gibraltar Pound",
    decimals: 2,
  },
  {
    code: "GMD",
    description: "Gambian Dalasi",
    decimals: 2,
  },
  {
    code: "GNF",
    description: "Guinean Franc**",
    decimals: 0,
  },
  {
    code: "GTQ",
    description: "Guatemalan Quetzal**",
    decimals: 2,
  },
  {
    code: "GYD",
    description: "Guyanese Dollar",
    decimals: 2,
  },
  {
    code: "HKD",
    description: "Hong Kong Dollar",
    decimals: 2,
  },
  {
    code: "HNL",
    description: "Honduran Lempira**",
    decimals: 2,
  },
  {
    code: "HRK",
    description: "Croatian Kuna",
    decimals: 2,
  },
  {
    code: "HTG",
    description: "Haitian Gourde",
    decimals: 2,
  },
  {
    code: "HUF",
    description: "Hungarian Forint**",
    decimals: 2,
  },
  {
    code: "IDR",
    description: "Indonesian Rupiah",
    decimals: 0,
  },
  {
    code: "ILS",
    description: "Israeli New Sheqel",
    decimals: 2,
  },
  {
    code: "INR",
    description: "Indian Rupee**",
    decimals: 2,
  },
  {
    code: "ISK",
    description: "Icelandic Króna",
    decimals: 0,
  },
  {
    code: "JMD",
    description: "Jamaican Dollar",
    decimals: 2,
  },
  {
    code: "JPY",
    description: "Japanese Yen",
    decimals: 0,
  },
  {
    code: "KES",
    description: "Kenyan Shilling",
    decimals: 2,
  },
  {
    code: "KGS",
    description: "Kyrgyzstani Som",
    decimals: 2,
  },
  {
    code: "KHR",
    description: "Cambodian Riel",
    decimals: 2,
  },
  {
    code: "KMF",
    description: "Comorian Franc",
    decimals: 0,
  },
  {
    code: "KRW",
    description: "South Korean Won",
    decimals: 0,
  },
  {
    code: "KYD",
    description: "Cayman Islands Dollar",
    decimals: 2,
  },
  {
    code: "KZT",
    description: "Kazakhstani Tenge",
    decimals: 2,
  },
  {
    code: "LAK",
    description: "Lao Kip**",
    decimals: 2,
  },
  {
    code: "LBP",
    description: "Lebanese Pound",
    decimals: 2,
  },
  {
    code: "LKR",
    description: "Sri Lankan Rupee",
    decimals: 2,
  },
  {
    code: "LRD",
    description: "Liberian Dollar",
    decimals: 2,
  },
  {
    code: "LSL",
    description: "Lesotho Loti",
    decimals: 2,
  },
  {
    code: "MAD",
    description: "Moroccan Dirham",
    decimals: 2,
  },
  {
    code: "MDL",
    description: "Moldovan Leu",
    decimals: 2,
  },
  {
    code: "MGA",
    description: "Malagasy Ariary",
    decimals: 2,
  },
  {
    code: "MKD",
    description: "Macedonian Denar",
    decimals: 2,
  },
  {
    code: "MNT",
    description: "Mongolian Tögrög",
    decimals: 2,
  },
  {
    code: "MOP",
    description: "Macanese Pataca",
    decimals: 2,
  },
  {
    code: "MRO",
    description: "Mauritanian Ouguiya",
    decimals: 2,
  },
  {
    code: "MUR",
    description: "Mauritian Rupee**",
    decimals: 2,
  },
  {
    code: "MVR",
    description: "Maldivian Rufiyaa",
    decimals: 2,
  },
  {
    code: "MWK",
    description: "Malawian Kwacha",
    decimals: 2,
  },
  {
    code: "MXN",
    description: "Mexican Peso**",
    decimals: 2,
  },
  {
    code: "MYR",
    description: "Malaysian Ringgit",
    decimals: 2,
  },
  {
    code: "MZN",
    description: "Mozambican Metical",
    decimals: 2,
  },
  {
    code: "NAD",
    description: "Namibian Dollar",
    decimals: 2,
  },
  {
    code: "NGN",
    description: "Nigerian Naira",
    decimals: 2,
  },
  {
    code: "NIO",
    description: "Nicaraguan Córdoba**",
    decimals: 2,
  },
  {
    code: "NOK",
    description: "Norwegian Krone",
    decimals: 2,
  },
  {
    code: "NPR",
    description: "Nepalese Rupee",
    decimals: 2,
  },
  {
    code: "NZD",
    description: "New Zealand Dollar",
    decimals: 2,
  },
  {
    code: "PAB",
    description: "Panamanian Balboa**",
    decimals: 2,
  },
  {
    code: "PEN",
    description: "Peruvian Nuevo Sol**",
    decimals: 2,
  },
  {
    code: "PGK",
    description: "Papua New Guinean Kina",
    decimals: 2,
  },
  {
    code: "PHP",
    description: "Philippine Peso",
    decimals: 2,
  },
  {
    code: "PKR",
    description: "Pakistani Rupee",
    decimals: 2,
  },
  {
    code: "PLN",
    description: "Polish Złoty",
    decimals: 2,
  },
  {
    code: "PYG",
    description: "Paraguayan Guaraní**",
    decimals: 0,
  },
  {
    code: "QAR",
    description: "Qatari Riyal",
    decimals: 2,
  },
  {
    code: "RON",
    description: "Romanian Leu",
    decimals: 2,
  },
  {
    code: "RSD",
    description: "Serbian Dinar",
    decimals: 2,
  },
  {
    code: "RUB",
    description: "Russian Ruble",
    decimals: 2,
  },
  {
    code: "RWF",
    description: "Rwandan Franc",
    decimals: 0,
  },
  {
    code: "SAR",
    description: "Saudi Riyal",
    decimals: 2,
  },
  {
    code: "SBD",
    description: "Solomon Islands Dollar",
    decimals: 2,
  },
  {
    code: "SCR",
    description: "Seychellois Rupee",
    decimals: 2,
  },
  {
    code: "SEK",
    description: "Swedish Krona",
    decimals: 2,
  },
  {
    code: "SGD",
    description: "Singapore Dollar",
    decimals: 2,
  },
  {
    code: "SHP",
    description: "Saint Helenian Pound**",
    decimals: 2,
  },
  {
    code: "SLL",
    description: "Sierra Leonean Leone",
    decimals: 2,
  },
  {
    code: "SOS",
    description: "Somali Shilling",
    decimals: 2,
  },
  {
    code: "SRD",
    description: "Surinamese Dollar**",
    decimals: 2,
  },
  {
    code: "STD",
    description: "São Tomé and Príncipe Dobra",
    decimals: 2,
  },
  {
    code: "SVC",
    description: "Salvadoran Colón**",
    decimals: 2,
  },
  {
    code: "SZL",
    description: "Swazi Lilangeni",
    decimals: 2,
  },
  {
    code: "THB",
    description: "Thai Baht",
    decimals: 2,
  },
  {
    code: "TJS",
    description: "Tajikistani Somoni",
    decimals: 2,
  },
  {
    code: "TOP",
    description: "Tongan Paʻanga",
    decimals: 2,
  },
  {
    code: "TRY",
    description: "Turkish Lira",
    decimals: 2,
  },
  {
    code: "TTD",
    description: "Trinidad and Tobago Dollar",
    decimals: 2,
  },
  {
    code: "TWD",
    description: "New Taiwan Dollar",
    decimals: 2,
  },
  {
    code: "TZS",
    description: "Tanzanian Shilling",
    decimals: 2,
  },
  {
    code: "UAH",
    description: "Ukrainian Hryvnia",
    decimals: 2,
  },
  {
    code: "UGX",
    description: "Ugandan Shilling",
    decimals: 0,
  },
  {
    code: "USD",
    description: "United States Dollar",
    decimals: 2,
  },
  {
    code: "UYU",
    description: "Uruguayan Peso**",
    decimals: 2,
  },
  {
    code: "UZS",
    description: "Uzbekistani Som",
    decimals: 2,
  },
  {
    code: "VND",
    description: "Vietnamese Đồng",
    decimals: 0,
  },
  {
    code: "VUV",
    description: "Vanuatu Vatu",
    decimals: 0,
  },
  {
    code: "WST",
    description: "Samoan Tala",
    decimals: 2,
  },
  {
    code: "XAF",
    description: "Central African Cfa Franc",
    decimals: 0,
  },
  {
    code: "XCD",
    description: "East Caribbean Dollar",
    decimals: 2,
  },
  {
    code: "XOF",
    description: "West African Cfa Franc**",
    decimals: 0,
  },
  {
    code: "XPF",
    description: "Cfp Franc**",
    decimals: 0,
  },
  {
    code: "YER",
    description: "Yemeni Rial",
    decimals: 2,
  },
  {
    code: "ZAR",
    description: "South African Rand",
    decimals: 2,
  },
  {
    code: "ZMW",
    description: "Zambian Kwacha",
    decimals: 2,
  },
];

export const currencyCodes = currencies.map((currency) => currency.code);

// A list of currency codes that do not require translation into smaller units (e.g. into cents)
export const zeroDecimalCodes = ["BIF", "CLP", "DJF", "GNF", "JPY", "KMF", "KRW", "MGA", "PYG", "RWF", "VND", "VUV", "XAF", "XOF", "XPF"];
export const currencyOptions: IdName[] = currencyCodes.map((code) => ({
  id: code,
  name: code,
}));
