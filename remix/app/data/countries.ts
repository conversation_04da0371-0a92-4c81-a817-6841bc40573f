import { defaultCountryCode } from "~/misc/vars";

export const tshirtSizes = ["2XS", "XS", "S", "M", "L", "XL", "2XL", "3XL"];
export const bcdSizes = ["2XS", "XS", "S", "M", "L", "XL", "2XL", "3XL"];

export const weightMetrics = {
  kg: {
    conversion_to_kg: 1,
  },
  lbs: {
    conversion_to_kg: 0.453592,
  },
  st: {
    conversion_to_kg: 6.35029,
  },
};

export const heightMetrics = {
  cm: {
    conversion_to_cm: 1,
  },
  ft: {
    conversion_to_cm: 30.48,
  },
  inch: {
    conversion_to_cm: 2.54,
  },
};

export const shoeSizeUnits = {
  EU: {
    from_cm: (cm_value) => (cm_value - 0.5) * 1.5, // Conversion factor from cm to EU size
    to_cm: (eu_value) => eu_value / 1.5 + 0.5, // Conversion factor from EU size to cm
  },
  UK: {
    from_cm: (cm_value) => (cm_value - 23.5) * 0.7, // Conversion factor from cm to UK size
    to_cm: (uk_value) => uk_value / 0.7 + 23.5, // Conversion factor from UK size to cm
  },
  US: {
    from_cm: (cm_value) => (cm_value - 21.5) * 0.7, // Conversion factor from cm to US size
    to_cm: (us_value) => us_value / 0.7 + 21.5, // Conversion factor from US size to cm
  },
} satisfies Record<string, { from_cm: (value: number) => number; to_cm: (value: number) => number }>;

// export const shoeSizeUnits = ["EU", "CN", "IN", "JP", "KR", "MX", "UK", "US"] as const;

interface Country {
  country_name: string;
  country_code: string;
  currency_code: string;
  telephone_code: string;
  height_unit: keyof typeof heightMetrics | null;
  weight_unit: keyof typeof weightMetrics | null;
  shoe_size_unit: keyof typeof shoeSizeUnits | null;
  vat_rate?: number;
  color?: string;
}

export const defaultCountryColor = "gray";
export const countries = [
  {
    country_code: "AF",
    country_name: "Afghanistan",
    currency_code: "AFN",
    telephone_code: "93",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "blueviolet",
  },
  {
    country_code: "AL",
    country_name: "Albania",
    currency_code: "ALL",
    telephone_code: "355",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "mediumaquamarine",
  },
  {
    country_code: "DZ",
    country_name: "Algeria",
    currency_code: "DZD",
    telephone_code: "213",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "violet",
  },
  {
    country_code: "AS",
    country_name: "American Samoa",
    currency_code: "USD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "lbs",
    shoe_size_unit: "US",
    color: "chocolate",
  },
  {
    country_code: "AD",
    country_name: "Andorra",
    currency_code: "EUR",
    telephone_code: "376",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "rebeccapurple",
  },
  {
    country_code: "AO",
    country_name: "Angola",
    currency_code: "AOA",
    telephone_code: "244",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "black",
  },
  {
    country_code: "AI",
    country_name: "Anguilla",
    currency_code: "XCD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
    color: "royalblue",
  },
  {
    country_code: "AQ",
    country_name: "Antarctica",
    currency_code: "N/A",
    telephone_code: "672",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "red",
  },
  {
    country_code: "AG",
    country_name: "Antigua and Barbuda",
    currency_code: "XCD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "AR",
    country_name: "Argentina",
    currency_code: "ARS",
    telephone_code: "54",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "midnightblue",
  },
  {
    country_code: "AM",
    country_name: "Armenia",
    currency_code: "AMD",
    telephone_code: "374",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "peru",
  },
  {
    country_code: "AW",
    country_name: "Aruba",
    currency_code: "AWG",
    telephone_code: "297",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "tomato",
  },
  {
    country_code: "AU",
    country_name: "Australia",
    currency_code: "AUD",
    telephone_code: "61",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
    color: "mediumpurple",
  },
  {
    country_code: "AT",
    country_name: "Austria",
    currency_code: "EUR",
    telephone_code: "43",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "springgreen",
  },
  {
    country_code: "AZ",
    country_name: "Azerbaijan",
    currency_code: "AZN",
    telephone_code: "994",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "mediumseagreen",
  },
  {
    country_code: "BS",
    country_name: "Bahamas",
    currency_code: "BSD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
    color: "lightseagreen",
  },
  {
    country_code: "BH",
    country_name: "Bahrain",
    currency_code: "BHD",
    telephone_code: "973",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "mediumvioletred",
  },
  {
    country_code: "BD",
    country_name: "Bangladesh",
    currency_code: "BDT",
    telephone_code: "880",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "yellowgreen",
  },
  {
    country_code: "BB",
    country_name: "Barbados",
    currency_code: "BBD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "BY",
    country_name: "Belarus",
    currency_code: "BYN",
    telephone_code: "375",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "lime",
  },
  {
    country_code: "BE",
    country_name: "Belgium",
    currency_code: "EUR",
    telephone_code: "32",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "dimgray",
  },
  {
    country_code: "BZ",
    country_name: "Belize",
    currency_code: "BZD",
    telephone_code: "501",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
    color: "darkgreen",
  },
  {
    country_code: "BJ",
    country_name: "Benin",
    currency_code: "XOF",
    telephone_code: "229",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "XC",
    country_name: "Anguilla",
    currency_code: "XCD",
    telephone_code: "1",
    height_unit: null,
    weight_unit: null,
    shoe_size_unit: null,
  },
  {
    country_code: "BM",
    country_name: "Bermuda",
    currency_code: "BMD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "BT",
    country_name: "Bhutan",
    currency_code: "BTN",
    telephone_code: "975",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "BO",
    country_name: "Bolivia",
    currency_code: "BOB",
    telephone_code: "591",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "dimgray",
  },
  {
    country_code: "BA",
    country_name: "Bosnia and Herzegovina",
    currency_code: "BAM",
    telephone_code: "387",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "BW",
    country_name: "Botswana",
    currency_code: "BWP",
    telephone_code: "267",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "BR",
    country_name: "Brazil",
    currency_code: "BRL",
    telephone_code: "55",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "indianred",
  },
  {
    country_code: "VG",
    country_name: "British Virgin Islands",
    currency_code: "USD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
    color: "darkgreen",
  },
  {
    country_code: "BN",
    country_name: "Brunei",
    currency_code: "BND",
    telephone_code: "673",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "BG",
    country_name: "Bulgaria",
    currency_code: "BGN",
    telephone_code: "359",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "darkslategray",
  },
  {
    country_code: "BF",
    country_name: "Burkina Faso",
    currency_code: "XOF",
    telephone_code: "226",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "MM",
    country_name: "Burma (Myanmar)",
    currency_code: "MMK",
    telephone_code: "95",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "BI",
    country_name: "Burundi",
    currency_code: "BIF",
    telephone_code: "257",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "KH",
    country_name: "Cambodia",
    currency_code: "KHR",
    telephone_code: "855",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "CM",
    country_name: "Cameroon",
    currency_code: "XAF",
    telephone_code: "237",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "slateblue",
  },
  {
    country_code: "CA",
    country_name: "Canada",
    currency_code: "CAD",
    telephone_code: "1",
    height_unit: "ft",
    weight_unit: "kg",
    shoe_size_unit: "US",
    color: "lightgreen",
  },
  {
    country_code: "CV",
    country_name: "Cape Verde",
    currency_code: "CVE",
    telephone_code: "238",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "KY",
    country_name: "Cayman Islands",
    currency_code: "KYD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "CF",
    country_name: "Central African Republic",
    currency_code: "XAF",
    telephone_code: "236",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "TD",
    country_name: "Chad",
    currency_code: "XAF",
    telephone_code: "235",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "CL",
    country_name: "Chile",
    currency_code: "CLP",
    telephone_code: "56",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "violet",
  },
  {
    country_code: "CN",
    country_name: "China",
    currency_code: "CNY",
    telephone_code: "86",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "darkgray",
  },
  {
    country_code: "CX",
    country_name: "Christmas Island",
    currency_code: "AUD",
    telephone_code: "61",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "CC",
    country_name: "Cocos (Keeling) Islands",
    currency_code: "AUD",
    telephone_code: "61",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "CO",
    country_name: "Colombia",
    currency_code: "COP",
    telephone_code: "57",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "coral",
  },
  {
    country_code: "KM",
    country_name: "Comoros",
    currency_code: "KMF",
    telephone_code: "269",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "CG",
    country_name: "Congo",
    currency_code: "XAF",
    telephone_code: "242",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "indianred",
  },
  {
    country_code: "CK",
    country_name: "Cook Islands",
    currency_code: "NZD",
    telephone_code: "682",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "CR",
    country_name: "Costa Rica",
    currency_code: "CRC",
    telephone_code: "506",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
    color: "darkgreen",
  },
  {
    country_code: "HR",
    country_name: "Croatia",
    currency_code: "HRK",
    telephone_code: "385",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "red",
  },
  {
    country_code: "CU",
    country_name: "Cuba",
    currency_code: "CUP",
    telephone_code: "53",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "darkorchid",
  },
  {
    country_code: "CY",
    country_name: "Cyprus",
    currency_code: "EUR",
    telephone_code: "357",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "saddlebrown",
  },
  {
    country_code: "CZ",
    country_name: "Czech Republic",
    currency_code: "CZK",
    telephone_code: "420",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "darkorchid",
  },
  {
    country_code: "CD",
    country_name: "Democratic Republic of the Congo",
    currency_code: "CDF",
    telephone_code: "243",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "DK",
    country_name: "Denmark",
    currency_code: "DKK",
    telephone_code: "45",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "gainsboro",
  },
  {
    country_code: "DG",
    country_name: "Diego Garcia",
    currency_code: "USD",
    telephone_code: "246",
    height_unit: null,
    weight_unit: null,
    shoe_size_unit: null,
  },
  {
    country_code: "DJ",
    country_name: "Djibouti",
    currency_code: "DJF",
    telephone_code: "253",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "DM",
    country_name: "Dominica",
    currency_code: "XCD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "DO",
    country_name: "Dominican Republic",
    currency_code: "DOP",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "EC",
    country_name: "Ecuador",
    currency_code: "USD",
    telephone_code: "593",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "midnightblue",
  },
  {
    country_code: "EG",
    country_name: "Egypt",
    currency_code: "EGP",
    telephone_code: "20",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "darkgoldenrod",
  },
  {
    country_code: "SV",
    country_name: "El Salvador",
    currency_code: "SVC",
    telephone_code: "503",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "GQ",
    country_name: "Equatorial Guinea",
    currency_code: "XAF",
    telephone_code: "240",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "ER",
    country_name: "Eritrea",
    currency_code: "ERN",
    telephone_code: "291",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "EE",
    country_name: "Estonia",
    currency_code: "EUR",
    telephone_code: "372",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "orangered",
  },
  {
    country_code: "ET",
    country_name: "Ethiopia",
    currency_code: "ETB",
    telephone_code: "251",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "FK",
    country_name: "Falkland Islands",
    currency_code: "FKP",
    telephone_code: "500",
    height_unit: "cm",
    weight_unit: "st",
    shoe_size_unit: "UK",
  },
  {
    country_code: "FO",
    country_name: "Faroe Islands",
    currency_code: "DKK",
    telephone_code: "298",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "FJ",
    country_name: "Fiji",
    currency_code: "FJD",
    telephone_code: "679",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "FI",
    country_name: "Finland",
    currency_code: "EUR",
    telephone_code: "358",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "lightgray",
  },
  {
    country_code: "FR",
    country_name: "France",
    currency_code: "EUR",
    telephone_code: "33",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "steelblue",
  },
  {
    country_code: "GF",
    country_name: "French Guiana",
    currency_code: "EUR",
    telephone_code: "594",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "PF",
    country_name: "French Polynesia",
    currency_code: "XPF",
    telephone_code: "689",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "GA",
    country_name: "Gabon",
    currency_code: "XAF",
    telephone_code: "241",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "GM",
    country_name: "Gambia",
    currency_code: "GMD",
    telephone_code: "220",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "GE",
    country_name: "Georgia",
    currency_code: "GEL",
    telephone_code: "995",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "DE",
    country_name: "Germany",
    currency_code: "EUR",
    telephone_code: "49",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "sienna",
  },
  {
    country_code: "GH",
    country_name: "Ghana",
    currency_code: "GHS",
    telephone_code: "233",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
    color: "crimson",
  },
  {
    country_code: "GI",
    country_name: "Gibraltar",
    currency_code: "GIP",
    telephone_code: "350",
    height_unit: "cm",
    weight_unit: "st",
    shoe_size_unit: "UK",
    color: "plum",
  },
  {
    country_code: "GR",
    country_name: "Greece",
    currency_code: "EUR",
    telephone_code: "30",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "orangered",
  },
  {
    country_code: "GL",
    country_name: "Greenland",
    currency_code: "DKK",
    telephone_code: "299",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "GD",
    country_name: "Grenada",
    currency_code: "XCD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "GP",
    country_name: "Guadeloupe",
    currency_code: "EUR",
    telephone_code: "590",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "springgreen",
  },
  {
    country_code: "GU",
    country_name: "Guam",
    currency_code: "USD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "lbs",
    shoe_size_unit: "US",
  },
  {
    country_code: "GT",
    country_name: "Guatemala",
    currency_code: "GTQ",
    telephone_code: "502",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
    color: "limegreen",
  },
  {
    country_code: "GN",
    country_name: "Guinea",
    currency_code: "GNF",
    telephone_code: "224",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "GW",
    country_name: "Guinea-Bissau",
    currency_code: "XOF",
    telephone_code: "245",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "red",
  },
  {
    country_code: "GY",
    country_name: "Guyana",
    currency_code: "GYD",
    telephone_code: "592",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "HT",
    country_name: "Haiti",
    currency_code: "HTG",
    telephone_code: "509",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "VA",
    country_name: "Holy See (Vatican City)",
    currency_code: "EUR",
    telephone_code: "39",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "HN",
    country_name: "Honduras",
    currency_code: "HNL",
    telephone_code: "504",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "HK",
    country_name: "Hong Kong",
    currency_code: "HKD",
    telephone_code: "852",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "darkslategray",
  },
  {
    country_code: "HU",
    country_name: "Hungary",
    currency_code: "HUF",
    telephone_code: "36",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "forestgreen",
  },
  {
    country_code: "IS",
    country_name: "Iceland",
    currency_code: "ISK",
    telephone_code: "354",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "firebrick",
  },
  {
    country_code: "IN",
    country_name: "India",
    currency_code: "INR",
    telephone_code: "91",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
    color: "lightsteelblue",
  },
  {
    country_code: "ID",
    country_name: "Indonesia",
    currency_code: "IDR",
    telephone_code: "62",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    vat_rate: 11,
    color: "slateblue",
  },
  {
    country_code: "IR",
    country_name: "Iran",
    currency_code: "IRR",
    telephone_code: "98",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "IQ",
    country_name: "Iraq",
    currency_code: "IQD",
    telephone_code: "964",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "royalblue",
  },
  {
    country_code: "IE",
    country_name: "Ireland",
    currency_code: "EUR",
    telephone_code: "353",
    height_unit: "cm",
    weight_unit: "st",
    shoe_size_unit: "UK",
    color: "midnightblue",
  },
  {
    country_code: "IM",
    country_name: "Isle of Man",
    currency_code: "GBP",
    telephone_code: "44",
    height_unit: "cm",
    weight_unit: "st",
    shoe_size_unit: "UK",
    color: "cornflowerblue",
  },
  {
    country_code: "IL",
    country_name: "Israel",
    currency_code: "ILS",
    telephone_code: "972",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "olivedrab",
  },
  {
    country_code: "IT",
    country_name: "Italy",
    currency_code: "EUR",
    telephone_code: "39",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "gainsboro",
  },
  {
    country_code: "CI",
    country_name: "Ivory Coast (Côte d'Ivoire)",
    currency_code: "XOF",
    telephone_code: "225",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "darkblue",
  },
  {
    country_code: "JM",
    country_name: "Jamaica",
    currency_code: "JMD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "JP",
    country_name: "Japan",
    currency_code: "JPY",
    telephone_code: "81",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "mediumpurple",
  },
  {
    country_code: "JE",
    country_name: "Jersey",
    currency_code: "GBP",
    telephone_code: "44",
    height_unit: "cm",
    weight_unit: "st",
    shoe_size_unit: "UK",
    color: "mediumseagreen",
  },
  {
    country_code: "JO",
    country_name: "Jordan",
    currency_code: "JOD",
    telephone_code: "962",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "saddlebrown",
  },
  {
    country_code: "KZ",
    country_name: "Kazakhstan",
    currency_code: "KZT",
    telephone_code: "7",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "KE",
    country_name: "Kenya",
    currency_code: "KES",
    telephone_code: "254",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
    color: "fuchsia",
  },
  {
    country_code: "KI",
    country_name: "Kiribati",
    currency_code: "AUD",
    telephone_code: "686",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "KW",
    country_name: "Kuwait",
    currency_code: "KWD",
    telephone_code: "965",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "khaki",
  },
  {
    country_code: "KG",
    country_name: "Kyrgyzstan",
    currency_code: "KGS",
    telephone_code: "996",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "LA",
    country_name: "Laos",
    currency_code: "LAK",
    telephone_code: "856",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "purple",
  },
  {
    country_code: "LV",
    country_name: "Latvia",
    currency_code: "EUR",
    telephone_code: "371",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "LB",
    country_name: "Lebanon",
    currency_code: "LBP",
    telephone_code: "961",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "LS",
    country_name: "Lesotho",
    currency_code: "LSL",
    telephone_code: "266",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "LR",
    country_name: "Liberia",
    currency_code: "LRD",
    telephone_code: "231",
    height_unit: "cm",
    weight_unit: "lbs",
    shoe_size_unit: "US",
  },
  {
    country_code: "LY",
    country_name: "Libya",
    currency_code: "LYD",
    telephone_code: "218",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "LI",
    country_name: "Liechtenstein",
    currency_code: "CHF",
    telephone_code: "423",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "mediumpurple",
  },
  {
    country_code: "LT",
    country_name: "Lithuania",
    currency_code: "EUR",
    telephone_code: "370",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "yellowgreen",
  },
  {
    country_code: "LU",
    country_name: "Luxembourg",
    currency_code: "EUR",
    telephone_code: "352",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "cornflowerblue",
  },
  {
    country_code: "MO",
    country_name: "Macau",
    currency_code: "MOP",
    telephone_code: "853",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "limegreen",
  },
  {
    country_code: "MK",
    country_name: "Macedonia",
    currency_code: "MKD",
    telephone_code: "389",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "MG",
    country_name: "Madagascar",
    currency_code: "MGA",
    telephone_code: "261",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "MW",
    country_name: "Malawi",
    currency_code: "MWK",
    telephone_code: "265",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "MY",
    country_name: "Malaysia",
    currency_code: "MYR",
    telephone_code: "60",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "orchid",
  },
  {
    country_code: "MV",
    country_name: "Maldives",
    currency_code: "MVR",
    telephone_code: "960",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "goldenrod",
  },
  {
    country_code: "ML",
    country_name: "Mali",
    currency_code: "XOF",
    telephone_code: "223",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "MT",
    country_name: "Malta",
    currency_code: "EUR",
    telephone_code: "356",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "limegreen",
  },
  {
    country_code: "MH",
    country_name: "Marshall Islands",
    currency_code: "USD",
    telephone_code: "692",
    height_unit: "cm",
    weight_unit: "lbs",
    shoe_size_unit: "US",
  },
  {
    country_code: "MQ",
    country_name: "Martinique",
    currency_code: "EUR",
    telephone_code: "596",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "MR",
    country_name: "Mauritania",
    currency_code: "MRU",
    telephone_code: "222",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "MU",
    country_name: "Mauritius",
    currency_code: "MUR",
    telephone_code: "230",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "olivedrab",
  },
  {
    country_code: "YT",
    country_name: "Mayotte",
    currency_code: "EUR",
    telephone_code: "262",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "MX",
    country_name: "Mexico",
    currency_code: "MXN",
    telephone_code: "52",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
    color: "rosybrown",
  },
  {
    country_code: "FM",
    country_name: "Micronesia",
    currency_code: "USD",
    telephone_code: "691",
    height_unit: "cm",
    weight_unit: "lbs",
    shoe_size_unit: "US",
  },
  {
    country_code: "MD",
    country_name: "Moldova",
    currency_code: "MDL",
    telephone_code: "373",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "MC",
    country_name: "Monaco",
    currency_code: "EUR",
    telephone_code: "377",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "royalblue",
  },
  {
    country_code: "MN",
    country_name: "Mongolia",
    currency_code: "MNT",
    telephone_code: "976",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "ME",
    country_name: "Montenegro",
    currency_code: "EUR",
    telephone_code: "382",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "MS",
    country_name: "Montserrat",
    currency_code: "XCD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "MA",
    country_name: "Morocco",
    currency_code: "MAD",
    telephone_code: "212",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "mediumseagreen",
  },
  {
    country_code: "MZ",
    country_name: "Mozambique",
    currency_code: "MZN",
    telephone_code: "258",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "NA",
    country_name: "Namibia",
    currency_code: "NAD",
    telephone_code: "264",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "NR",
    country_name: "Nauru",
    currency_code: "AUD",
    telephone_code: "674",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "NP",
    country_name: "Nepal",
    currency_code: "NPR",
    telephone_code: "977",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "sienna",
  },
  {
    country_code: "NL",
    country_name: "Netherlands",
    currency_code: "EUR",
    telephone_code: "31",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "#FF7557",
  },
  {
    country_code: "AN",
    country_name: "Netherlands Antilles",
    currency_code: "ANG",
    telephone_code: "599",
    height_unit: null,
    weight_unit: null,
    shoe_size_unit: null,
    color: "turquoise",
  },
  {
    country_code: "CW",
    country_name: "Curaçao",
    currency_code: "ANG",
    telephone_code: "599",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "turquoise",
  },
  {
    country_code: "BQ",
    country_name: "Bonaire, Sint Eustatius, and Saba",
    currency_code: "USD",
    telephone_code: "599",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "turquoise",
  },
  {
    country_code: "NC",
    country_name: "New Caledonia",
    currency_code: "XPF",
    telephone_code: "687",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "darkorchid",
  },
  {
    country_code: "NZ",
    country_name: "New Zealand",
    currency_code: "NZD",
    telephone_code: "64",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
    color: "palevioletred",
  },
  {
    country_code: "NI",
    country_name: "Nicaragua",
    currency_code: "NIO",
    telephone_code: "505",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "NE",
    country_name: "Niger",
    currency_code: "XOF",
    telephone_code: "227",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "NG",
    country_name: "Nigeria",
    currency_code: "NGN",
    telephone_code: "234",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "NU",
    country_name: "Niue",
    currency_code: "NZD",
    telephone_code: "683",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "NF",
    country_name: "Norfolk Island",
    currency_code: "AUD",
    telephone_code: "672",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "KP",
    country_name: "North Korea",
    currency_code: "KPW",
    telephone_code: "850",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "MP",
    country_name: "Northern Mariana Islands",
    currency_code: "USD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "lbs",
    shoe_size_unit: "US",
    color: "yellowgreen",
  },
  {
    country_code: "NO",
    country_name: "Norway",
    currency_code: "NOK",
    telephone_code: "47",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "yellowgreen",
  },
  {
    country_code: "OM",
    country_name: "Oman",
    currency_code: "OMR",
    telephone_code: "968",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "PK",
    country_name: "Pakistan",
    currency_code: "PKR",
    telephone_code: "92",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
    color: "lawngreen",
  },
  {
    country_code: "PW",
    country_name: "Palau",
    currency_code: "USD",
    telephone_code: "680",
    height_unit: "cm",
    weight_unit: "lbs",
    shoe_size_unit: "US",
  },
  {
    country_code: "PS",
    country_name: "Palestine",
    currency_code: "JOD",
    telephone_code: "970",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "PA",
    country_name: "Panama",
    currency_code: "PAB",
    telephone_code: "507",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "PG",
    country_name: "Papua New Guinea",
    currency_code: "PGK",
    telephone_code: "675",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "PY",
    country_name: "Paraguay",
    currency_code: "PYG",
    telephone_code: "595",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "PE",
    country_name: "Peru",
    currency_code: "PEN",
    telephone_code: "51",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "lightseagreen",
  },
  {
    country_code: "PH",
    country_name: "Philippines",
    currency_code: "PHP",
    telephone_code: "63",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
    color: "darkgray",
  },
  {
    country_code: "PN",
    country_name: "Pitcairn Islands",
    currency_code: "NZD",
    telephone_code: "870",
    height_unit: "cm",
    weight_unit: "st",
    shoe_size_unit: "UK",
  },
  {
    country_code: "PL",
    country_name: "Poland",
    currency_code: "PLN",
    telephone_code: "48",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "saddlebrown",
  },
  {
    country_code: "PT",
    country_name: "Portugal",
    currency_code: "EUR",
    telephone_code: "351",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "greenyellow",
  },
  {
    country_code: "PR",
    country_name: "Puerto Rico",
    currency_code: "USD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "lbs",
    shoe_size_unit: "US",
  },
  {
    country_code: "QA",
    country_name: "Qatar",
    currency_code: "QAR",
    telephone_code: "974",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "RE",
    country_name: "Reunion Island",
    currency_code: "EUR",
    telephone_code: "262",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "RO",
    country_name: "Romania",
    currency_code: "RON",
    telephone_code: "40",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "palegoldenrod",
  },
  {
    country_code: "RU",
    country_name: "Russia",
    currency_code: "RUB",
    telephone_code: "7",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "palegoldenrod",
  },
  {
    country_code: "RW",
    country_name: "Rwanda",
    currency_code: "RWF",
    telephone_code: "250",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "BL",
    country_name: "Saint Barthelemy",
    currency_code: "EUR",
    telephone_code: "590",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "SH",
    country_name: "Saint Helena",
    currency_code: "GBP",
    telephone_code: "290",
    height_unit: "cm",
    weight_unit: "st",
    shoe_size_unit: "UK",
  },
  {
    country_code: "KN",
    country_name: "Saint Kitts and Nevis",
    currency_code: "XCD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "LC",
    country_name: "Saint Lucia",
    currency_code: "XCD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "MF",
    country_name: "Saint Martin",
    currency_code: "ANG",
    telephone_code: "590",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "PM",
    country_name: "Saint Pierre and Miquelon",
    currency_code: "EUR",
    telephone_code: "508",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "VC",
    country_name: "Saint Vincent and the Grenadines",
    currency_code: "XCD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "WS",
    country_name: "Samoa",
    currency_code: "EUR",
    telephone_code: "685",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "SM",
    country_name: "San Marino",
    currency_code: "EUR",
    telephone_code: "378",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "ST",
    country_name: "Sao Tome and Principe",
    currency_code: "STD",
    telephone_code: "239",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "SA",
    country_name: "Saudi Arabia",
    currency_code: "SAR",
    telephone_code: "966",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "darkseagreen",
  },
  {
    country_code: "SN",
    country_name: "Senegal",
    currency_code: "XOF",
    telephone_code: "221",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "RS",
    country_name: "Serbia",
    currency_code: "RSD",
    telephone_code: "381",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "brown",
  },
  {
    country_code: "SC",
    country_name: "Seychelles",
    currency_code: "SCR",
    telephone_code: "248",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "SL",
    country_name: "Sierra Leone",
    currency_code: "SLL",
    telephone_code: "232",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "SG",
    country_name: "Singapore",
    currency_code: "SGD",
    telephone_code: "65",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "darkturquoise",
  },
  {
    country_code: "SX",
    country_name: "Sint Maarten",
    currency_code: "ANG",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "SK",
    country_name: "Slovakia",
    currency_code: "SKK",
    telephone_code: "421",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "gray",
  },
  {
    country_code: "SI",
    country_name: "Slovenia",
    currency_code: "EUR",
    telephone_code: "386",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "deepskyblue",
  },
  {
    country_code: "SB",
    country_name: "Solomon Islands",
    currency_code: "SBD",
    telephone_code: "677",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "SO",
    country_name: "Somalia",
    currency_code: "SOS",
    telephone_code: "252",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "ZA",
    country_name: "South Africa",
    currency_code: "ZAR",
    telephone_code: "27",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
    color: "mediumslateblue",
  },
  {
    country_code: "KR",
    country_name: "South Korea",
    currency_code: "KRW",
    telephone_code: "82",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "tan",
  },
  {
    country_code: "SS",
    country_name: "South Sudan",
    currency_code: "SSP",
    telephone_code: "211",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "ES",
    country_name: "Spain",
    currency_code: "EUR",
    telephone_code: "34",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "gray",
  },
  {
    country_code: "LK",
    country_name: "Sri Lanka",
    currency_code: "LKR",
    telephone_code: "94",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "SD",
    country_name: "Sudan",
    currency_code: "SDG",
    telephone_code: "249",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "SR",
    country_name: "Suriname",
    currency_code: "SRD",
    telephone_code: "597",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "SJ",
    country_name: "Svalbard",
    currency_code: "NOK",
    telephone_code: "47",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "SZ",
    country_name: "Swaziland",
    currency_code: "SZL",
    telephone_code: "268",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
    color: "steelblue",
  },
  {
    country_code: "SE",
    country_name: "Sweden",
    currency_code: "SEK",
    telephone_code: "46",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "silver",
  },
  {
    country_code: "CH",
    country_name: "Switzerland",
    currency_code: "CHF",
    telephone_code: "41",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "goldenrod",
  },
  {
    country_code: "SY",
    country_name: "Syria",
    currency_code: "SYP",
    telephone_code: "963",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "TW",
    country_name: "Taiwan",
    currency_code: "TWD",
    telephone_code: "886",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "turquoise",
  },
  {
    country_code: "TJ",
    country_name: "Tajikistan",
    currency_code: "TJS",
    telephone_code: "992",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "TZ",
    country_name: "Tanzania",
    currency_code: "TZS",
    telephone_code: "255",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "TH",
    country_name: "Thailand",
    currency_code: "THB",
    telephone_code: "66",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "mediumaquamarine",
  },
  {
    country_code: "TL",
    country_name: "Timor-Leste (East Timor)",
    currency_code: "IDR",
    telephone_code: "670",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "TG",
    country_name: "Togo",
    currency_code: "XOF",
    telephone_code: "228",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "TK",
    country_name: "Tokelau",
    currency_code: "NZD",
    telephone_code: "690",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "TO",
    country_name: "Tonga Islands",
    currency_code: "TOP",
    telephone_code: "676",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "TT",
    country_name: "Trinidad and Tobago",
    currency_code: "TTD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "TN",
    country_name: "Tunisia",
    currency_code: "TND",
    telephone_code: "216",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "thistle",
  },
  {
    country_code: "TR",
    country_name: "Turkey",
    currency_code: "TRY",
    telephone_code: "90",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "rebeccapurple",
  },
  {
    country_code: "TM",
    country_name: "Turkmenistan",
    currency_code: "TMT",
    telephone_code: "993",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "TC",
    country_name: "Turks and Caicos Islands",
    currency_code: "USD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "US",
  },
  {
    country_code: "TV",
    country_name: "Tuvalu",
    currency_code: "AUD",
    telephone_code: "688",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "UG",
    country_name: "Uganda",
    currency_code: "UGX",
    telephone_code: "256",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
    color: "lime",
  },
  {
    country_code: "UA",
    country_name: "Ukraine",
    currency_code: "UAH",
    telephone_code: "380",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "cornsilk",
  },
  {
    country_code: "AE",
    country_name: "United Arab Emirates",
    currency_code: "AED",
    telephone_code: "971",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "limegreen",
  },
  {
    country_code: "GB",
    country_name: "United Kingdom",
    currency_code: "GBP",
    telephone_code: "44",
    height_unit: "ft",
    weight_unit: "st",
    shoe_size_unit: "UK",
    color: "crimson",
  },
  {
    country_code: "US",
    country_name: "United States",
    currency_code: "USD",
    telephone_code: "1",
    height_unit: "ft",
    weight_unit: "lbs",
    shoe_size_unit: "US",
    color: "cadetblue",
  },
  {
    country_code: "UY",
    country_name: "Uruguay",
    currency_code: "UYU",
    telephone_code: "598",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "VI",
    country_name: "US Virgin Islands",
    currency_code: "USD",
    telephone_code: "1",
    height_unit: "cm",
    weight_unit: "lbs",
    shoe_size_unit: "US",
    color: "darkkhaki",
  },
  {
    country_code: "UZ",
    country_name: "Uzbekistan",
    currency_code: "UZS",
    telephone_code: "998",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "sienna",
  },
  {
    country_code: "VU",
    country_name: "Vanuatu",
    currency_code: "VUV",
    telephone_code: "678",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
  {
    country_code: "VE",
    country_name: "Venezuela",
    currency_code: "VEF",
    telephone_code: "58",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "darkkhaki",
  },
  {
    country_code: "VN",
    country_name: "Vietnam",
    currency_code: "VND",
    telephone_code: "84",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
    color: "darksalmon",
  },
  {
    country_code: "WF",
    country_name: "Wallis and Futuna",
    currency_code: "XPF",
    telephone_code: "681",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "EH",
    country_name: "Western Sahara",
    currency_code: "MAD",
    telephone_code: "212",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "YE",
    country_name: "Yemen",
    currency_code: "YER",
    telephone_code: "967",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "EU",
  },
  {
    country_code: "ZM",
    country_name: "Zambia",
    currency_code: "ZMW",
    telephone_code: "260",
    height_unit: "cm",
    weight_unit: "kg",
    shoe_size_unit: "UK",
  },
] satisfies Country[];

export function getFlagEmoji(countryCode: string): string {
  // Check if the input is a valid two-letter country code
  if (countryCode.length !== 2 || !/^[A-Z]{2}$/.test(countryCode)) {
    return "";
    throw new Error("Invalid country code. Please provide a valid two-letter country code.");
  }

  const OFFSET = 0x1f1e6 - "A".charCodeAt(0);
  const high = countryCode.charCodeAt(0) + OFFSET;
  const low = countryCode.charCodeAt(1) + OFFSET;

  // Construct the HTML entity for the emoji
  return `&#x${high.toString(16).toUpperCase()};&#x${low.toString(16).toUpperCase()};`;
}

export const defaultCountry = countries.find((country) => country.country_code === defaultCountryCode)!;

export type CountryCode = (typeof countries)[number]["country_code"];

export const outdatedCountryCode = ["AN"] satisfies CountryCode[];

export const getCountry = (countryCode?: CountryCode) => countries.find((coutnry) => coutnry.country_code === countryCode);
