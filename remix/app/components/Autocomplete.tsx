import { forwardRef, useRef, useState } from "react";
import {
  autoUpdate,
  flip,
  FloatingFocusManager,
  FloatingPortal,
  size,
  useClick,
  useDismiss,
  useFloating,
  useId,
  useInteractions,
  useListNavigation,
  useRole,
} from "@floating-ui/react";
import { countries } from "~/data/countries";
import { twMerge } from "tailwind-merge";

const data = countries;

interface ItemProps {
  children: React.ReactNode;
  active: boolean;
}

const Item = forwardRef<HTMLDivElement, ItemProps & React.HTMLProps<HTMLDivElement>>(({ children, active, ...rest }, ref) => {
  const id = useId();
  return (
    <div
      ref={ref}
      role="option"
      id={id}
      aria-selected={active}
      {...rest}
      className={twMerge("p-1 block cursor-default bg-slate-200 w-full text-left", active && "bg-slate-400")}
    >
      {children}
    </div>
  );
});

export function AutoComplete(props: { name: string; defaultValue?: string | null; required?: boolean }) {
  const [open, setOpen] = useState(false);
  const [inputvalue, setInputValue] = useState(props.defaultValue);
  const selectedCountry = countries.find((country) => country.country_code === inputvalue);
  const [searchValue, setSearchValue] = useState(selectedCountry?.country_name || "");
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const listRef = useRef<Array<HTMLElement | null>>([]);

  const { refs, floatingStyles, context } = useFloating<HTMLInputElement>({
    whileElementsMounted: autoUpdate,
    open,
    onOpenChange: setOpen,
    middleware: [
      flip({ padding: 10 }),
      size({
        apply({ rects, availableHeight, elements }) {
          Object.assign(elements.floating.style, {
            width: `${rects.reference.width}px`,
            maxHeight: `${availableHeight}px`,
          });
        },
        padding: 10,
      }),
    ],
  });

  const role = useRole(context, { role: "select" });
  const dismiss = useDismiss(context);
  const listNav = useListNavigation(context, {
    listRef,
    activeIndex,
    onNavigate: setActiveIndex,
    virtual: true,
    loop: true,
  });
  const click = useClick(context, { event: "click" });

  const { getReferenceProps, getFloatingProps, getItemProps } = useInteractions([role, dismiss, listNav]);

  function onChange(event: React.ChangeEvent<HTMLInputElement>) {
    const value = event.target.value;
    setSearchValue(value);

    if (value) {
      setOpen(true);
      setActiveIndex(0);
    }
  }

  const items = data.filter((item) => {
    const searchLower = searchValue.toLowerCase();
    if (!searchLower) return true;
    return item.country_name.toLowerCase().startsWith(searchLower) || item.country_code.toLowerCase().startsWith(searchLower);
  });

  return (
    <>
      <input type={"hidden"} name={props.name} value={inputvalue || ""} />
      <input
        name={props.name}
        className="input"
        {...getReferenceProps({
          ref: refs.setReference,
          required: props.required,
          onChange,
          value: searchValue,
          placeholder: "Select Country",
          "aria-autocomplete": "list",
          // onFocus: () => setOpen(true),
          onBlur: () => setOpen(false),
          onClick: () => {
            if (!searchValue) {
              setOpen(!open);
            }
          },
          onKeyDown(event) {
            if (event.key === "Escape") {
              setSearchValue(selectedCountry?.country_name || "");
            }
            if (event.key === "Enter" && activeIndex != null && items[activeIndex]) {
              event.preventDefault();
              setInputValue(items[activeIndex]?.country_code || "");
              setSearchValue(items[activeIndex]?.country_name || "");
              setActiveIndex(null);
              setOpen(false);
            }
          },
        })}
      />
      <FloatingPortal>
        {open && (
          <FloatingFocusManager context={context} initialFocus={-1} visuallyHiddenDismiss closeOnFocusOut={false}>
            <div
              className="overflow-y-auto bg-slate-200"
              {...getFloatingProps({
                ref: refs.setFloating,
                style: floatingStyles,
                onMouseEnter: (e) => {
                  console.log("mousenter");
                },
                onClickCapture: (e) => {
                  console.log("click capture");
                },
                onClick: (e) => {
                  console.log("wel werkende onclick");
                },
              })}
              onClick={() => {
                console.log("wel werkende onclick");
              }}
            >
              {items.map((item, index) => (
                <Item
                  {...getItemProps({
                    key: item.country_code,
                    ref(node) {
                      listRef.current[index] = node;
                    },
                    onClick: () => {
                      console.log("click", item.country_code);
                      setSearchValue(item.country_name);
                      setInputValue(item.country_code);
                      setOpen(false);
                      refs.domReference.current?.focus();
                    },
                  })}
                  active={activeIndex === index}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  {item.country_name}
                </Item>
              ))}
            </div>
          </FloatingFocusManager>
        )}
      </FloatingPortal>
    </>
  );
}
