import { ParamLink, ParamStateLinkProps } from "~/components/meta/CustomComponents";
import { HamburgerIcon } from "~/components/Icons";
import { setState, useSearchParams2 } from "~/hooks/use-search-params2";
import {
  FloatingFocusManager,
  FloatingOverlay,
  useDismiss,
  useFloating,
  useInteractions,
  useRole,
  useTransitionStatus,
} from "@floating-ui/react";
import { twMerge } from "tailwind-merge";
import { useLocation, useNavigation } from "@remix-run/react";
import { AnimatingDiv } from "~/components/base/base";
import React, { Fragment, ReactNode, useState } from "react";
import { usePageOverwrites } from "~/utils/remix";
import {
  _about,
  _addon,
  _boat,
  _booking,
  _contact,
  _customer,
  _diving_course,
  _diving_location,
  _establishment_paths,
  _event_overview,
  _feature,
  _form,
  _inventory,
  _member,
  _metrics,
  _notification,
  _operator,
  _payment,
  _sale,
  _operator_edit,
  _participant,
  _payment_method,
  _planning,
  _planning_u,
  _product,
  _retail,
  _spot,
  _tag,
  _user,
  _waiver,
  _workload,
  _rental,
  _order_value, _tank
} from "~/misc/paths";
import { useIsInterative } from "~/hooks/hooks";
import { ChevronUpIcon, XMarkIcon } from "@heroicons/react/20/solid";
import { IkImage } from "~/components/IkImage";
import { Trans } from "~/components/Trans";
import { uniqueBy } from "remeda";
import { useAppContext } from "~/hooks/use-app-context";
import { getTimezone, Timezone } from "~/data/timezones";
import { PinIcon, PinOffIcon } from "lucide-react";
import { ActionForm } from "~/components/form/BaseFrom";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { Button, SubmitButton } from "~/components/base/Button";
import { RInput } from "~/components/ResourceInputs";
import { useOptimisticMenu } from "~/hooks/use-optimistic-menu";
import { PermissionKey } from "~/domain/permission/permission";
import { StateInput } from "~/misc/parsers/global-state-parsers";

const defaultLinkClassName = `aria-busy:loading-dots block border-l-8 border-white p-3
    transition-colors aria-current:border-secondary-500 aria-current:bg-secondary-50`;

const DefaultLink = (props: ParamStateLinkProps) => {
  return <ParamLink className={defaultLinkClassName} {...props} paramState={{ persist_main_menu_toggled: false, ...props.paramState }} />;
};

interface MenuItem {
  path: string;
  paramState: Partial<StateInput>;
  paramsActive: boolean;
  show: boolean;
  end?: boolean;
  children?: ReactNode;
}

interface MenuSegment {
  id: string;
  title: string;
  items: MenuItem[];
}

const HamburgerMenuPanelContent = (props: { className?: string }) => {
  const search = useSearchParams2();
  const menu = useOptimisticMenu();
  const state = search.state;
  const navigation = useNavigation();
  const location = useLocation();
  const context = useAppContext();
  const defaultActive = context.members
    .map((member) => member.establishment_id)
    .find((establishmentId) => location.pathname.includes(establishmentId));
  const oneActive = context.members.length === 1 && context.members[0]?.establishment_id;
  const [activeMenuItemId, setActiveMenuItemId] = useState(
    oneActive ||
      defaultActive ||
      search.state.establishment_id ||
      search.state.persist_establishment_id ||
      search.state.persist_operator_id ||
      "",
  );

  const mergedData = usePageOverwrites();

  const myEstablishmentsWithEnterpriseOperator = context.members
    .filter((member) => member.owner || member.admin)
    .map((member) => member.establishment)
    .filter((establishment) => establishment.enterprise);

  const entrepriseOperators = uniqueBy(myEstablishmentsWithEnterpriseOperator, (establishment) => establishment.operator_id);

  return (
    <div className={twMerge("w-fit md:max-w-80 overflow-auto rounded-r-md bg-slate-100 h-svh md:min-w-80", props.className)}>
      {context.environment === "development" && false && (
        <div className="p-2">
          <p>Params:</p>
          <table>
            {Array.from(search.params.entries()).map(([key, value], index) => (
              <tr key={index}>
                <td className="p-1">{key}</td>
                <td className="p-1">{value}</td>
              </tr>
            ))}
          </table>
          <p>State:</p>
          <pre>{JSON.stringify(search.state, null, 2)}</pre>
        </div>
      )}
      <div className="p-3 flex flex-row gap-2 justify-end items-center">
        <ActionForm className="inline-block">
          <RedirectParamsInput path={"./"} paramState={{ persist_main_menu_toggled: !menu.toggled }} />
          <RInput table={"session"} field={"data.main_menu_pinned"} type={"hidden"} hiddenType={"__boolean__"} value={!menu.pinned + ""} />
          <Button
            type={"submit"}
            className="p-1.5 spinner-dark hidden md:inline-block font-bold text-secondary-500 transition-opacity hover:opacity-60"
          >
            {menu.pinned ? <PinOffIcon className="h-5 w-5" /> : <PinIcon className="h-5 w-5" />}
          </Button>
        </ActionForm>
        {/*<ParamLink*/}
        {/*  className="hidden md:inline-block font-bold text-secondary-500 transition-opacity hover:opacity-60"*/}
        {/*  paramState={{*/}
        {/*    persist_main_menu_pinned: !state.persist_main_menu_pinned,*/}
        {/*    persist_main_menu_toggled: !state.persist_main_menu_toggled,*/}
        {/*  }}*/}
        {/*>*/}
        {/*  {state.persist_main_menu_pinned ? <PinOffIcon className="h-5 w-5" /> : <PinIcon className="h-5 w-5" />}*/}
        {/*</ParamLink>*/}
        <ParamLink
          className="inline-block font-bold text-secondary-500 transition-opacity hover:opacity-60"
          paramState={{ persist_main_menu_toggled: !menu.toggled }}
        >
          <XMarkIcon className="h-7 w-7" />
        </ParamLink>
      </div>
      {context.members.filter((member) => member.captain || member.crew || member.diving_level).length > 0 && (
        <Fragment>
          <ParamLink
            // prefetch={"render"}
            path={_planning_u}
            paramState={{
              persist_operator_id: null,
              persist_establishment_id: null,
              persist_timezone: undefined,
              persist_main_menu_toggled: false,
            }}
            className={`w-fill block rounded-md bg-slate-100 p-2 text-slate-700 transition-colors
            hover:bg-slate-200 hover:opacity-60 data-loading:animate-pulse`}
          >
            <Trans>My schedule</Trans>
          </ParamLink>
        </Fragment>
      )}
      {entrepriseOperators.map((establishment) => {
        const isActive = activeMenuItemId === establishment.operator_id;
        const baseParamProps: ParamStateLinkProps = {
          paramsActive: state.persist_operator_id === establishment.operator_id && state.persist_establishment_id === null,
          paramState: {
            persist_operator_id: establishment.operator_id,
            persist_timezone: getTimezone(establishment.timezone),
            persist_establishment_id: null,
            persist_toggle_establishment_ids: undefined,
            persist_main_menu_toggled: false,
          },
        };
        const allowedForMoreThanOneEstablishment = myEstablishmentsWithEnterpriseOperator.filter(
          (item) => item.operator_id === establishment.operator_id,
        );

        if (allowedForMoreThanOneEstablishment.length < 2) return <Fragment />;

        return (
          <AnimatingDiv key={establishment.id}>
            <ParamLink
              path={_planning}
              paramState={{
                persist_operator_id: establishment.operator_id,
                persist_establishment_id: null,
                persist_previous_path: _planning,
              }}
              className="group block transition-colors hover:bg-slate-100 hover:opacity-80"
              aria-selected={isActive}
              onClick={(e) => {
                e.preventDefault();
                setActiveMenuItemId(activeMenuItemId === establishment.operator_id ? "" : establishment.operator_id || "");
              }}
            >
              <div className="flex flex-row items-center gap-3 p-3">
                <div className="h-10 w-10 overflow-hidden rounded-md bg-primary text-white flex items-center justify-center text-xl">E</div>
                <div>
                  <p className="font-bold">{establishment.operator_name}</p>
                  <p className="text-xs">Enterprise</p>
                </div>
                <div className="flex-1"></div>
                <ChevronUpIcon className="h-5 w-5 transition-transform group-hover:rotate-180 group-aria-selected:rotate-180 group-aria-selected:hover:rotate-0" />
              </div>
            </ParamLink>
            {isActive && (
              <div className="pb-12">
                <div className="">
                  <p className="p-3 text-xl font-bold text-slate-400">Workflow</p>
                  <div>
                    <ParamLink
                      className={defaultLinkClassName}
                      path={_planning}
                      paramsActive={baseParamProps.paramsActive}
                      paramState={{ persist_previous_path: _planning, ...baseParamProps.paramState }}
                    >
                      <Trans>Planning</Trans>
                    </ParamLink>
                    <ParamLink className={defaultLinkClassName} path={_participant} {...baseParamProps}>
                      <Trans>Detailed day view</Trans>
                    </ParamLink>
                    <ParamLink className={defaultLinkClassName} path={_customer} {...baseParamProps}>
                      <Trans>Customer base</Trans>
                    </ParamLink>
                    <ParamLink
                      className={defaultLinkClassName}
                      path={_booking}
                      paramsActive={baseParamProps.paramsActive}
                      paramState={{ persist_previous_path: _booking, ...baseParamProps.paramState }}
                    >
                      <Trans>Bookings</Trans>
                    </ParamLink>
                    <ParamLink className={defaultLinkClassName} path={_payment} {...baseParamProps}>
                      <Trans>Payments</Trans>
                    </ParamLink>
                  </div>
                </div>
              </div>
            )}
          </AnimatingDiv>
        );
      })}
      {context.members.length > 0 && (
        <div>
          {context.members.map((member) => {
            const isActive = activeMenuItemId === member.establishment_id;
            const isActiveOnPage = (search.state.establishment_id || search.state.persist_establishment_id) === member.establishment_id;
            const firstImage = member.establishment.files?.[0]?.filename;

            const locationName = member.establishment.location_name;
            const baseParamLinkProps = {
              className: defaultLinkClassName,
              paramState: {
                persist_operator_id: member.establishment.operator_id,
                persist_establishment_id: member.establishment_id,
                persist_timezone: member.establishment.timezone as Timezone,
                persist_toggle_establishment_ids: [],
                persist_main_menu_toggled: false,
              },
              paramsActive: state.persist_establishment_id === member.establishment_id,
            } satisfies ParamStateLinkProps;

            const filteredSegments = search.state.persist_opened_menu_segments.filter(
              (segment) => !segment.startsWith(member.establishment_id),
            );

            // Menu segments configuration
            const menuSegments: MenuSegment[] = [
              {
                id: "planning",
                title: "Planning",
                items: [
                  {
                    children: "Planning",
                    path: _planning,
                    paramState: { persist_previous_path: _planning, ...baseParamLinkProps.paramState },
                    show: member.establishment.workflow > 1 && !!member.admin,
                    paramsActive: baseParamLinkProps.paramsActive,
                  },
                ],
              },
              {
                id: "detailed_day",
                title: "Detailed Day",
                items: [
                  {
                    children: "Detailed Day",
                    path: _participant,
                    show: !!member.admin,
                    ...baseParamLinkProps,
                  },
                ],
              },
              {
                id: "activities_my_page",
                title: "Activities (My Page)",
                items: [
                  {
                    children: "Activities (My Page)",
                    path: _establishment_paths(member.establishment_id).index,
                    ...baseParamLinkProps,
                    paramState: {
                      customer_view: search.state.customer_view,
                      persist_main_menu_toggled: false,
                    },
                    show: true,
                    end: true,
                  },
                ],
              },
              {
                id: "bookings",
                title: "Bookings",
                items: [
                  {
                    children: "Bookings",
                    path: _booking,
                    paramState: {
                      persist_previous_path: _booking,
                      ...baseParamLinkProps.paramState,
                      toggle_invoice: undefined,
                    },
                    show: member.admin > 1,
                    paramsActive: baseParamLinkProps.paramsActive && !search.state.toggle_invoice,
                  },
                ],
              },
              {
                id: "customers",
                title: "Customers",
                items: [
                  {
                    children: "Customers",
                    path: _customer,
                    show: !!member.admin,
                    ...baseParamLinkProps,
                  },
                ],
              },
              // ---
              {
                id: "resources",
                title: "Resources",
                items: [
                  {
                    children: "Retail Items (Webshop)",
                    path: _retail,
                    show: true,
                    ...baseParamLinkProps,
                  },
                  {
                    children: "Retail Stock",
                    path: _inventory,
                    show: member.admin > 1,
                    ...baseParamLinkProps,
                  },
                  {
                    children: "Rental Items",
                    path: _rental,
                    show: member.admin > 1,
                    ...baseParamLinkProps,
                  },
                  {
                    children: "Staff Workload",
                    path: _workload,
                    show: member.admin > 1 && member.establishment.workflow > 1,
                    ...baseParamLinkProps,
                  },
                  {
                    children: "Add-ons",
                    path: _addon(member.establishment_id),
                    show: member.owner || member.admin > 1,
                    ...baseParamLinkProps,
                  },
                  {
                    children: "Boats",
                    path: _boat,
                    show: member.admin > 1 && member.establishment.workflow > 1,
                    ...baseParamLinkProps,
                  },
                ],
              },
              // ---
              {
                id: "reporting",
                title: "Reporting",
                items: [
                  {
                    children: "Metrics",
                    path: _metrics,
                    show: !!member.permissions?.includes("metrics" satisfies PermissionKey) && member.establishment.workflow > 1,
                    ...baseParamLinkProps,
                  },
                  {
                    children: "Sales",
                    path: _sale,
                    show: !!member.permissions?.includes("sales") && !!member.establishment.workflow,
                    ...baseParamLinkProps,
                  },
                  {
                    children: "Payments",
                    path: _payment,
                    show: !!member.permissions?.includes("payments") && !!member.establishment.workflow,
                    ...baseParamLinkProps,
                  },
                ],
              },
              // ---
              {
                id: "onboarding",
                title: "Onboarding",
                items: [
                  {
                    children: "Form Builder",
                    path: _form,
                    show: member.admin > 1,
                    ...baseParamLinkProps,
                  },
                  {
                    children: "Waiver Builder",
                    path: _waiver,
                    show: member.admin > 1,
                    ...baseParamLinkProps,
                  },
                  {
                    children: "Activity List (Forms)",
                    path: _product,
                    show: member.owner || member.admin > 1,
                    ...baseParamLinkProps,
                  },
                ],
              },
              // ---
              {
                id: "settings",
                title: "Settings",
                items: [
                  {
                    children: "General",
                    path: _establishment_paths(member.establishment_id).mutate,
                    ...baseParamLinkProps,
                    paramState: {
                      persist_main_menu_toggled: false,
                    },
                    show: member.admin > 1,
                  },
                  {
                    children: "Notifications",
                    path: _notification,
                    show: member.admin > 1,
                    ...baseParamLinkProps,
                  },
                  {
                    children: "Payment Methods",
                    path: _payment_method,
                    show: member.admin > 1,
                    ...baseParamLinkProps,
                  },
                  {
                    children: "Tanks & Blends",
                    path: _tank,
                    show: member.admin > 1,
                    ...baseParamLinkProps,
                  },
                  {
                    children: "Tags",
                    path: _tag,
                    show: member.admin > 1,
                    ...baseParamLinkProps,
                  },
                  {
                    children: "Users",
                    path: _member,
                    show: member.owner || member.admin > 1,
                    ...baseParamLinkProps,
                  },
                ],
              },
              {
                id: "admin",
                title: "Admin",
                items: [
                  {
                    children: "Operator",
                    path: _operator_edit(member.establishment.operator_id),
                    ...baseParamLinkProps,
                    show: context.editor,
                  },
                  {
                    children: "History",
                    path: _establishment_paths(member.establishment_id).history,
                    ...baseParamLinkProps,
                    show: context.editor,
                  },
                  {
                    children: "Insight",
                    path: _establishment_paths(member.establishment_id).insight,
                    ...baseParamLinkProps,
                    show: context.editor,
                  },
                  {
                    children: "Integrations",
                    path: _establishment_paths(member.establishment_id).integration,
                    ...baseParamLinkProps,
                    show: context.editor,
                  },
                ],
              },
              {
                id: "support",
                title: "Support",
                items: [
                  {
                    children: "Support",
                    path: `https://diversdesk.com/r/support/${member.establishment.operator_slug}-${member.establishment.operator_id}`,
                    ...baseParamLinkProps,
                    show: true,
                  },
                ],
              },
            ];

            return (
              <AnimatingDiv key={member.id}>
                <ParamLink
                  path={_establishment_paths(member.establishment_id).index}
                  className={twMerge("group block transition-colors hover:bg-slate-100 hover:opacity-80", isActiveOnPage && "bg-slate-200")}
                  aria-selected={isActive}
                  onClick={(e) => {
                    e.preventDefault();
                    setActiveMenuItemId(activeMenuItemId === member.establishment_id ? "" : member.establishment_id);
                  }}
                >
                  <div className="flex flex-row items-center gap-3 p-3">
                    <div className="h-10 w-10 overflow-hidden rounded-md bg-gray-200">
                      {firstImage && <IkImage w={40} h={30} className="h-full w-full object-cover" path={firstImage} />}
                    </div>
                    <div>
                      <p className="font-bold">
                        {member.establishment.operator_name} {!!locationName && " - " + locationName}
                      </p>
                      <p className="text-xs">{member.establishment.spot_name}</p>
                    </div>
                    <div className="flex-1"></div>
                    <ChevronUpIcon className="h-5 w-5 transition-transform group-hover:rotate-180 group-aria-selected:rotate-180 group-aria-selected:hover:rotate-0" />
                  </div>
                </ParamLink>
                {isActive && (
                  <div className="pb-12">
                    {menuSegments.map((segment) => {
                      const segmentId = member.establishment_id + "-" + segment.id;
                      const isExpanded = search.state.persist_opened_menu_segments.includes(segmentId);

                      const filteredItems = segment.items.filter((item) => item.show);

                      const firstItem = filteredItems[0];
                      if (!firstItem) return null;

                      if (filteredItems.length > 1) {
                        return (
                          <AnimatingDiv key={segment.id} className="group/segmentcontainer">
                            <ParamLink
                              aria-expanded={isExpanded}
                              paramState={{
                                persist_opened_menu_segments: isExpanded ? filteredSegments : [...filteredSegments, segmentId],
                              }}
                              className="group/segmentlink p-3 text-xl font-bold hover:text-slate-500 transition-colors text-slate-400 group-has-[div>[aria-current=true]]/segmentcontainer:bg-slate-200 flex items-center gap-2 justify-between"
                            >
                              {segment.title}
                              <ChevronUpIcon
                                className={twMerge("h-5 w-5 transition-transform", isExpanded ? "rotate-180 " : "rotate-0 ")}
                              />
                            </ParamLink>
                            <div className={twMerge("overflow-clip transition-all duration-300", isExpanded ? "h-auto" : "h-0")}>
                              {filteredItems.map((item, index) => {
                                return (
                                  <ParamLink
                                    key={index}
                                    path={item.path}
                                    className={twMerge(defaultLinkClassName, "pl-8")}
                                    paramState={item.paramState}
                                    paramsActive={item.paramsActive}
                                    end={item.end}
                                  >
                                    {item.children}
                                  </ParamLink>
                                );
                              })}
                            </div>
                          </AnimatingDiv>
                        );
                      }

                      if (firstItem.path.startsWith("https://")) {
                        return (
                          <a key={firstItem.path} href={firstItem.path} target="_blank" className={defaultLinkClassName}>
                            {firstItem.children}
                          </a>
                        );
                      }

                      return (
                        <ParamLink
                          key={firstItem.path}
                          path={firstItem.path}
                          className={defaultLinkClassName}
                          paramState={firstItem.paramState}
                          paramsActive={firstItem.paramsActive}
                          end={firstItem.end}
                        >
                          {firstItem.children}
                        </ParamLink>
                      );
                    })}
                  </div>
                )}
              </AnimatingDiv>
            );
          })}
        </div>
      )}
      {context.editor && (
        <div className="space-y-3 pt-6">
          <h5 className="px-3 font-bold text-secondary-500">Editor</h5>
          <ul className="space-y-2 border-t-2 border-secondary-stroke">
            <li>
              <DefaultLink path={_event_overview}>
                <Trans>Page events</Trans>
              </DefaultLink>
            </li>
            <li>
              <DefaultLink path={_spot}>
                <Trans>Countries/Regions/Spots</Trans>
              </DefaultLink>
            </li>
            <li>
              <DefaultLink path={_diving_course}>
                <Trans>Diving courses</Trans>
              </DefaultLink>
            </li>
            <li>
              <DefaultLink path={_diving_location}>
                <Trans>Diving locations/sites</Trans>
              </DefaultLink>
            </li>
            <li>
              <DefaultLink path={_operator}>
                <Trans>Operators</Trans>
              </DefaultLink>
            </li>
            <li>
              <DefaultLink path={_user}>
                <Trans>Users</Trans>
              </DefaultLink>
            </li>
            <li>
              <DefaultLink path={_notification} paramState={{ persist_operator_id: null, persist_establishment_id: null }}>
                <Trans>Notifications</Trans>
              </DefaultLink>
            </li>
            <li>
              <DefaultLink path={_waiver} paramState={{ persist_operator_id: null, persist_establishment_id: null }}>
                <Trans>Waivers</Trans>
              </DefaultLink>
            </li>
            <li>
              <DefaultLink path={_order_value}>
                <Trans>Defined order values</Trans>
              </DefaultLink>
            </li>
            {context.environment === "development" && (
              <Fragment>
                <li>
                  <DefaultLink path={_feature}>
                    <Trans>Features</Trans>
                  </DefaultLink>
                </li>
              </Fragment>
            )}
          </ul>
        </div>
      )}
      {!mergedData.simple_view && (
        <div className={twMerge("border-secondary-stroke", context.members.length > 0 && "border-t-2")}>
          <nav className="text-sm font-semibold text-slate-800">
            <DefaultLink
              path={"/../"}
              end
              aria-current={location.pathname.startsWith("/explore") || location.pathname === "/"}
              aria-busy={(navigation.location?.pathname.startsWith("/explore") || navigation.location?.pathname === "/") ?? false}
            >
              Explore
            </DefaultLink>
            {context.operator ? (
              <a href={"https://www.diversdesk.com/about"} className={defaultLinkClassName} target={"_blank"}>
                About us
              </a>
            ) : (
              <DefaultLink path={_about} end>
                About us
              </DefaultLink>
            )}
            {context.operator ? (
              <a href={"https://www.diversdesk.com/contact"} className={defaultLinkClassName} target={"_blank"}>
                Leave feedback / Contact
              </a>
            ) : (
              <DefaultLink path={_contact} end>
                Leave feedback / Contact
              </DefaultLink>
            )}
          </nav>
        </div>
      )}
    </div>
  );
};

export const HamburgerPanel = () => {
  const menu = useOptimisticMenu();
  const isInteractive = useIsInterative();
  const isMenuOpen = menu.toggled && !menu.pinned;

  const { refs, context } = useFloating({
    open: isMenuOpen,
    strategy: "fixed",
    onOpenChange: (open) => {
      setState({ persist_main_menu_toggled: open }, { replaceRoute: true });
    },
  });

  const { isMounted, status } = useTransitionStatus(context, { duration: 200 });

  const dismiss = useDismiss(context);
  const role = useRole(context, { role: "menu" });
  const { getReferenceProps, getFloatingProps, getItemProps } = useInteractions([dismiss, role]);

  if (menu.pinned) return menu.toggled ? <Fragment /> : <HamburgerMenuPanelContent className="sticky top-0 hidden md:block" />;

  // in the case of ssr the status is always unmounted, but could still be opened, thats why the check status === "unmounted" is there.
  // const isMountedAndOpen = (status === "unmounted" && isMenuOpen) || status === "open";
  const isMountedAndOpen = (!isInteractive && isMenuOpen) || status === "open";

  return (
    isMounted &&
    !menu.pinned && (
      <FloatingFocusManager context={context}>
        <Fragment>
          <FloatingOverlay
            lockScroll
            className={twMerge("fixed inset-0 z-20 bg-black/0 transition-colors", isMountedAndOpen && "bg-black/20")}
          />
          <div
            {...getFloatingProps}
            ref={refs.setFloating}
            // style={floatingStyles}
            className={twMerge(
              "fixed inset-y-0 left-0 z-30 h-full -translate-x-40 opacity-0 transition-all ease-in-out",
              isMountedAndOpen && "translate-x-0 opacity-100",
            )}
          >
            <div className="h-full ">
              <HamburgerMenuPanelContent />
            </div>
          </div>
        </Fragment>
      </FloatingFocusManager>
    )
  );
};

export const HamburgerMenuButton = () => {
  const menu = useOptimisticMenu();

  return (
    <div>
      {!menu.pinned || menu.toggled ? (
        <ParamLink paramState={{ persist_main_menu_toggled: !menu.toggled }} className="block p-3 transition-opacity hover:opacity-60">
          <HamburgerIcon className="h-5 w-5" />
        </ParamLink>
      ) : (
        <Fragment>
          <ActionForm className="block md:hidden">
            <RedirectParamsInput path={"./"} paramState={{ persist_main_menu_toggled: true }} />
            <RInput table={"session"} field={"data.main_menu_pinned"} hidden hiddenType={"__boolean__"} value={"false"} />
            <SubmitButton className="block p-3 transition-opacity hover:opacity-60">
              <HamburgerIcon className="h-5 w-5" />
            </SubmitButton>
          </ActionForm>
          <div className="w-3 hidden md:block"></div>
        </Fragment>
      )}
    </div>
  );
};
