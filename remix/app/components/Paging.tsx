import { ParamLink } from "~/components/meta/CustomComponents";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/20/solid";
import { Fragment } from "react";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { NavigatingSelect } from "~/components/NavigatingSelect";

export const pageLimits = [60, 200, 500] as const;

export const Paging = (props: { totalCount: number; pageLimit: number }) => {
  const search = useSearchParams2();
  const nrOfPages = Math.ceil(props.totalCount / props.pageLimit);
  const pageLimit = props.pageLimit;
  const nextPage = search.state.page_nr + 1;

  if (props.totalCount <= pageLimits[0] && !search.state.page_nr) return <Fragment />;

  return (
    <div className="flex flex-row items-center gap-3 justify-between py-3 sticky bottom-0 bg-white">
      <ParamLink
        className="link aria-disabled:text-slate-300 aria-disabled:cursor-default aria-disabled:no-underline flex gap-1 items-center"
        path="./"
        paramState={{ page_nr: search.state.page_nr - 1 }}
        preventScrollReset
        aria-disabled={!search.state.page_nr}
      >
        <ChevronLeftIcon className="w-5 h-5 inline-block" />
        <span className="max-sm:hidden">Previous</span>
      </ParamLink>
      <div className="flex flex-row items-center gap-3">
        <div className="flex flex-wrap gap-x-2 items-center">
          <span>Page </span>
          <div className={"flex flex-row items-center gap-x-2"}>
            <NavigatingSelect
              key={search.state.page_nr}
              className="select"
              defaultValue={search.state.page_nr}
              disabled={nrOfPages < 1}
              onChange={(value) => ({ page_nr: value as any })}
            >
              {Array.from({ length: nrOfPages }, (_, index) => (
                <option key={index} value={index}>
                  {index + 1}
                </option>
              ))}
            </NavigatingSelect>
            <span className="whitespace-nowrap">of {nrOfPages}</span>
          </div>
        </div>

        <div className="flex flex-wrap items-center gap-x-2">
          <span>Show</span>
          <NavigatingSelect
            className="select text-sm py-0.5"
            defaultValue={pageLimit}
            onChange={(e) => ({ page_limit: e as any, page_nr: 0 })}
          >
            {pageLimits.map((limit) => (
              <option key={limit} value={limit}>
                {limit}
              </option>
            ))}
          </NavigatingSelect>
        </div>
      </div>
      <ParamLink
        className="link aria-disabled:text-slate-300 aria-disabled:cursor-default aria-disabled:no-underline flex gap-1 items-center"
        path="./"
        preventScrollReset
        paramState={{ page_nr: search.state.page_nr < nrOfPages ? nextPage : nrOfPages - 1 }}
        aria-disabled={!(nextPage < nrOfPages)}
      >
        <span className="max-sm:hidden">Next</span>
        <ChevronRightIcon className="w-5 h-5 inline-block" />
      </ParamLink>
    </div>
  );
};
