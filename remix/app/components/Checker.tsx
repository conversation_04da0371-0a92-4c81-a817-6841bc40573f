import { CheckIcon } from "@heroicons/react/20/solid";
import React from "react";
import { twMerge } from "tailwind-merge";
import { CheckState } from "~/misc/parsers/global-state-parsers";

export const ControlledChecker = (props: { className?: string; checked: CheckState }) => {
  return (
    <span
      className={twMerge(
        "rounded-md w-5 h-5 flex items-center justify-center transition-colors text-white bg-transparent",
        props.checked === "true" && "bg-primary",
        props.checked === "mixed" && "bg-gray-500",
        props.className,
      )}
    >
      {props.checked === "mixed" && <div className={"font-bold"}>-</div>}
      {props.checked === "true" && <CheckIcon className={"w-4 h-4"} />}
    </span>
  );
};

export const Checker = (props: { className?: string }) => {
  return (
    <span
      className={twMerge(
        "rounded-md w-5 h-5 group-aria-selected:bg-primary bg-white flex items-center justify-center transition-colors text-white",
        props.className,
      )}
    >
      <div className="h-4 w-4 group-aria-selected:hidden"></div>
      <CheckIcon className="w-4 h-4 hidden group-aria-selected:block" />
    </span>
  );
};

export const Toggle = (props: { className?: string }) => {
  return (
    <span
      className={twMerge(
        "relative w-10 p-1 flex items-center rounded-full transition-colors",
        "group-aria-checked:bg-primary bg-gray-300",
        props.className,
      )}
    >
      <span className={"w-4 h-4 rounded-full bg-white transition-transform group-aria-checked:translate-x-4"} />
    </span>
  );
};
