import React, { useState } from "react";
import { format, addMonths, isWithinInterval, startOfDay, endOfDay, startOfMonth, endOfMonth, getYear, setYear, setMonth } from "date-fns";
import { twMerge } from "tailwind-merge";
import { ChevronLeftIcon } from "@heroicons/react/20/solid";
import { weekDays } from "~/misc/vars";
import { toUtc } from "~/misc/date-helpers";
import { getMonthObj } from "~/domain/planning/plannings-helpers";
import { useFormCtx } from "./form/BaseFrom";
import { refreshFormdata } from "./form/form-hooks";

interface DateRange {
  from: Date | null;
  to: Date | null;
}

interface DateRangePickerProps {
  defaultValue?: DateRange;
  className?: string;
  fromName?: string;
  toName?: string;
}

export const DateRangePicker: React.FC<DateRangePickerProps> = (props) => {
  const [selectedRange, setSelectedRange] = useState<DateRange>(props.defaultValue || { from: null, to: null });
  const [currentMonth, setCurrentMonth] = useState<string>(format(new Date(), "yyyy-MM"));

  const setDateRangeAndRefresh = (newRange: DateRange) => {
    setSelectedRange(newRange);
    refreshFormdata();
  };

  const handleDateClick = (date: Date) => {
    if (!selectedRange.from || (selectedRange.from && selectedRange.to)) {
      // Start new range
      setDateRangeAndRefresh({ from: date, to: null });
    } else {
      // Complete range
      const from = selectedRange.from;
      const to = date;
      const newRange = {
        from: from < to ? from : to,
        to: from < to ? to : from,
      };
      setDateRangeAndRefresh(newRange);
    }
  };

  const handleMonthChange = (add: number) => {
    const newMonth = addMonths(new Date(currentMonth + "-01"), add);
    setCurrentMonth(format(newMonth, "yyyy-MM"));
  };

  const handleMonthRangeSelect = (monthsAgo: number) => {
    const currentDate = new Date(currentMonth + "-01");
    if (monthsAgo === -1) {
      // This year option
      const startDate = startOfMonth(new Date(currentDate.getFullYear(), 0, 1)); // January 1st of current year
      const endDate = endOfMonth(currentDate);
      const newRange = { from: startDate, to: endDate };
      setDateRangeAndRefresh(newRange);
    } else {
      const startDate = startOfMonth(addMonths(currentDate, -monthsAgo));
      const endDate = endOfMonth(currentDate);
      const newRange = { from: startDate, to: endDate };
      setDateRangeAndRefresh(newRange);
    }
  };

  const handleYearChange = (year: number) => {
    const currentDate = new Date(currentMonth + "-01");
    const newDate = setYear(currentDate, year);
    setCurrentMonth(format(newDate, "yyyy-MM"));
  };

  const handleMonthSelect = (month: number) => {
    const currentDate = new Date(currentMonth + "-01");
    const newDate = setMonth(currentDate, month);
    setCurrentMonth(format(newDate, "yyyy-MM"));
  };

  const handleTodayClick = () => {
    const today = new Date();
    const currentMonthStr = format(today, "yyyy-MM");
    if (currentMonthStr !== currentMonth) {
      setCurrentMonth(currentMonthStr);
    }
  };

  const month = getMonthObj(currentMonth + "-01");
  const isCurrentMonth = format(new Date(), "yyyy-MM") === currentMonth;

  const isDateInRange = (date: Date) => {
    if (!selectedRange.from || !selectedRange.to) return false;
    return isWithinInterval(date, {
      start: startOfDay(selectedRange.from),
      end: endOfDay(selectedRange.to),
    });
  };

  const isDateSelected = (date: Date) => {
    if (!selectedRange.from) return false;
    if (!selectedRange.to) return format(date, "yyyy-MM-dd") === format(selectedRange.from, "yyyy-MM-dd");
    return (
      format(date, "yyyy-MM-dd") === format(selectedRange.from, "yyyy-MM-dd") ||
      format(date, "yyyy-MM-dd") === format(selectedRange.to, "yyyy-MM-dd")
    );
  };

  const currentYear = getYear(new Date(currentMonth + "-01"));
  const years = Array.from({ length: 10 }, (_, i) => currentYear - 5 + i);
  const months = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];

  return (
    <div className={twMerge("min-w-[310px] bg-white", props.className)}>
      <input type="hidden" name={props.fromName} value={selectedRange.from ? format(selectedRange.from, "yyyy-MM-dd") : ""} />
      <input type="hidden" name={props.toName} value={selectedRange.to ? format(selectedRange.to, "yyyy-MM-dd") : ""} />
      <div className="mb-4 text-sm text-slate-600">
        {selectedRange.from ? (
          <div className="flex items-center justify-between">
            <button
              type="button"
              disabled={format(new Date(currentMonth + "-01"), "yyyy-MM") === format(selectedRange.from, "yyyy-MM")}
              onClick={() => {
                if (selectedRange.from) {
                  setCurrentMonth(format(selectedRange.from, "yyyy-MM"));
                }
              }}
              className="hover:text-secondary-600 disabled:hover:text-slate-600"
            >
              {format(selectedRange.from, "MMM d, yyyy")}
              {selectedRange.to ? ` - ${format(selectedRange.to, "MMM d, yyyy")}` : ""}
            </button>
            {selectedRange.from && selectedRange.to && (
              <button
                type="button"
                onClick={() => {
                  setDateRangeAndRefresh({ from: null, to: null });
                }}
                className="text-slate-400 hover:text-slate-600"
              >
                Clear
              </button>
            )}
          </div>
        ) : (
          <span>Select a date range</span>
        )}
      </div>
      <div className="flex items-center justify-between mb-4">
        <button type="button" onClick={() => handleMonthChange(-1)} className="p-2 hover:bg-slate-100 rounded-full transition-colors">
          <ChevronLeftIcon className="h-5 w-5 text-secondary-500" />
        </button>
        <div className="flex items-center gap-2">
          <select
            className="text-sm border-none bg-transparent font-semibold focus:outline-none"
            value={format(new Date(currentMonth + "-01"), "M")}
            onChange={(e) => handleMonthSelect(Number(e.target.value) - 1)}
          >
            {months.map((month, index) => (
              <option key={month} value={index + 1}>
                {month}
              </option>
            ))}
          </select>
          <select
            className="text-sm border-none bg-transparent font-semibold focus:outline-none"
            value={currentYear}
            onChange={(e) => handleYearChange(Number(e.target.value))}
          >
            {years.map((year) => (
              <option key={year} value={year}>
                {year}
              </option>
            ))}
          </select>
          <button
            type="button"
            onClick={handleTodayClick}
            disabled={isCurrentMonth}
            className={twMerge(
              "px-2 py-1 text-sm rounded-md transition-colors",
              isCurrentMonth ? "text-slate-400 cursor-not-allowed" : "text-secondary hover:bg-secondary-50",
            )}
          >
            Today
          </button>
        </div>
        <button type="button" onClick={() => handleMonthChange(1)} className="p-2 hover:bg-slate-100 rounded-full transition-colors">
          <ChevronLeftIcon className="h-5 w-5 text-secondary-500 rotate-180" />
        </button>
      </div>

      <div className="mb-4">
        <select
          className="w-full p-2 border border-slate-200 rounded-md text-sm"
          onChange={(e) => handleMonthRangeSelect(Number(e.target.value))}
          value=""
        >
          <option value="" disabled>
            Select a range
          </option>
          <option value="0">This month</option>
          <option value="1">Last month</option>
          <option value="2">Last 2 months</option>
          <option value="3">Last 3 months</option>
          <option value="6">Last 6 months</option>
          <option value="12">Last 12 months</option>
          <option value="-1">This year</option>
        </select>
      </div>

      <div className="grid grid-cols-7 gap-1">
        {weekDays.map((day, index) => (
          <div key={index} className="text-left pl-3 pb-1 text-xs font-medium text-slate-500 uppercase">
            {day.key}
          </div>
        ))}
        {month.weeks.map((week, weekIndex) =>
          week.dates.map((dateValue, dayIndex) => {
            const date = toUtc(dateValue);
            const isInRange = isDateInRange(date);
            const isSelected = isDateSelected(date);
            const isToday = format(date, "yyyy-MM-dd") === format(new Date(), "yyyy-MM-dd");

            return (
              <button
                key={`${weekIndex}-${dayIndex}`}
                type="button"
                onClick={() => handleDateClick(date)}
                className={twMerge(
                  "relative h-10 w-10 flex items-center justify-center rounded-full text-sm transition-colors",
                  isInRange && "bg-primary/10",
                  isSelected && "bg-primary text-white font-semibold",
                  !isSelected && !isInRange && "hover:bg-slate-100",
                  isToday && !isSelected && "border-2 border-secondary",
                  dateValue.slice(0, 7) !== currentMonth && "opacity-50",
                )}
              >
                {format(date, "d")}
              </button>
            );
          }),
        )}
      </div>
    </div>
  );
};
