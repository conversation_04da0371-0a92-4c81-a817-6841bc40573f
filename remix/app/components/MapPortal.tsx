import type { ReactNode } from "react";
import React, { Fragment, useEffect, useRef } from "react";
import { useMapContext } from "~/hooks/use-map-context";
import * as portals from "react-reverse-portal";
import { Map } from "mapbox-gl";

import { useAppContext } from "~/hooks/use-app-context";

interface Props {
  onFit?: (map: Map) => void;
  children?: ReactNode;
}

export const MapElement = () => {
  const { env } = useAppContext();
  const mapElement = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (mapElement.current) {
      try {
        const map = new Map({
          container: mapElement.current,
          interactive: true,
          dragRotate: true,
          dragPan: true,
          scrollZoom: true,
          style: "mapbox://styles/mapbox/streets-v9",
          accessToken: env.mapBoxPublicApiKey,
        });
        map.on("load", (e) => {
          useMapContext.setState({
            map: e.target,
          });
        });
        return () => map.remove();
      } catch (e) {
        console.error("webgl not supported");
      }
    } else {
      useMapContext.setState({
        map: null,
      });
    }
  }, [env.mapBoxPublicApiKey]);

  return <div ref={mapElement} style={{ height: "100%", width: "100%" }} />;
};

export const MapInitPortal = () => {
  const { container } = useMapContext();

  useEffect(() => {
    useMapContext.setState({
      container: portals.createHtmlPortalNode({
        attributes: { style: "width: 100%; height: 100%" },
      }),
    });
  }, []);

  return (
    <div className="relative">
      {container && (
        <portals.InPortal node={container}>
          <div style={{ position: "relative", height: "100%", width: "100%" }}>
            <MapElement />
          </div>
        </portals.InPortal>
      )}
    </div>
  );
};

export const MapPortal = (props: Props) => {
  const { onFit } = props;
  const { map, container } = useMapContext();
  useEffect(() => {
    if (map) {
      map.resize();
    }
  }, [map]);

  return (
    <div className="relative h-full w-full">
      {onFit && (
        <div className="app-container relative">
          <div className="absolute left-0 top-0 z-10 p-2">
            <button
              className={"rounded bg-gray-50 p-1 text-sm font-bold hover:bg-gray-100 active:bg-gray-200"}
              onClick={() => {
                if (map) {
                  onFit(map);
                }
              }}
            >
              Fit
            </button>
          </div>
        </div>
      )}
      {container ? <portals.OutPortal node={container} /> : <Fragment />}
      {props.children}
    </div>
  );
};
