import type { ReactNode } from "react";
import React, { createContext, useContext, useEffect, useMemo } from "react";
import type { FirebaseApp } from "@firebase/app";
import { getApps, initializeApp } from "@firebase/app";
import { collection, getFirestore, limit, onSnapshot, orderBy, query, Timestamp, where } from "@firebase/firestore";
import { clientId } from "~/misc/vars";
import { useLocation, useRevalidator } from "@remix-run/react";
import { convertToFirestorePath } from "~/misc/paths";
import { useAppContext } from "~/hooks/use-app-context";
import { useRevalidatorWithPing } from "~/hooks/use-page-refresh";

const appName = "firebase_singapore";
const apps = getApps();
const foundApp = apps.find((app) => app.name === appName);

const FirebaseContext = createContext<{ firebase_app: FirebaseApp | undefined }>({ firebase_app: undefined });

export const FirebaseProvider = (props: { children: ReactNode }) => {
  const context = useAppContext();
  const location = useLocation();

  const { revalidate } = useRevalidatorWithPing();
  const fb = useMemo(() => {
    const firebaseConfig = context.env.firebase_singapore;
    const app = foundApp || initializeApp(firebaseConfig, appName);
    return { firebase_app: app };
  }, [context.env]);

  useEffect(() => {
    const fbApp = fb.firebase_app;
    try {
      const firestoreSingapore = getFirestore(fbApp);
      const path = convertToFirestorePath(location.pathname);
      // console.log("listen to path", path);
      const changesRef = collection(firestoreSingapore, path);
      const q = query(
        changesRef,
        where("timestamp", ">", Timestamp.now()),
        // where("client_id", "!=", clientId),
        limit(1),
        orderBy("timestamp", "desc"),
      );
      const unsub = onSnapshot(q, (collection) => {
        const firstDoc = collection.docs[0];
        console.log("listening", collection.docs);
        if (firstDoc && firstDoc.data()?.client_id !== clientId) {
          console.log("do refresh", collection.docs);
          revalidate();
        }
      });
      return () => unsub();
    } catch (e) {
      console.error("could not connect to firestore", e);
    }
  }, [fb.firebase_app, location.pathname, revalidate]);

  useEffect(() => {
    const sessionId = context.session_id;
    const fbApp = fb.firebase_app;
    if (!sessionId || !fbApp) return;
    try {
      const firestoreSingapore = getFirestore(fbApp);
      const changesRef = collection(firestoreSingapore, "session", sessionId, "changes");
      const q = query(
        changesRef,
        where("timestamp", ">", Timestamp.now()),
        // where("client_id", "!=", clientId),
        limit(1),
        orderBy("timestamp", "desc"),
      );
      const unsub = onSnapshot(q, (collection) => {
        const firstDoc = collection.docs[0];
        console.log("listening", collection.docs);
        if (firstDoc && firstDoc.data()?.client_id !== clientId) {
          console.log("do refresh", collection.docs);
          revalidate();
        }
      });
      return () => unsub();
    } catch (e) {
      console.error("could not connect to firestore", e);
    }
  }, [context.session_id]);

  return <FirebaseContext.Provider value={fb}>{props.children}</FirebaseContext.Provider>;
};

export const useFirebase = () => useContext(FirebaseContext);
