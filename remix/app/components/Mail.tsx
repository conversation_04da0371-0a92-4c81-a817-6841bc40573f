import type { BodyProps, LinkProps } from "@react-email/components";
import { Body, Button } from "@react-email/components";
import React from "react";
import { twMerge } from "tailwind-merge";
import { TailwindCustom } from "~/domain/email/TailwindCustom";

export const TailwindBody = (props: BodyProps) => (
  <TailwindCustom>
    <Body {...props} />
  </TailwindCustom>
);

export const StyledLink = (props: LinkProps) => (
  <Button
    {...props}
    className={twMerge(
      `flex items-center justify-center space-x-3 rounded bg-primary p-2 px-3 
            font-semibold text-white transition-colors hover:bg-primary-600 active:bg-primary-dark 
            disabled:opacity-60`,
      props.className,
    )}
  />
);
