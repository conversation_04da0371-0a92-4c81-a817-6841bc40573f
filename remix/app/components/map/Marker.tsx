import type { ReactNode } from "react";
import React, { Fragment, useEffect, useRef } from "react";
import { useMapContext } from "~/hooks/use-map-context";
import { Marker } from "mapbox-gl";
import type { LatLng } from "~/misc/types";

export const MapMarker = (props: { children?: ReactNode; className?: string; coordinates: LatLng }) => {
  const markerRef = useRef<HTMLDivElement | null>(null);
  const { map } = useMapContext();

  useEffect(() => {
    const coordinates = props.coordinates;
    if (map && markerRef.current && coordinates) {
      try {
        const marker = new Marker(markerRef.current).setLngLat(coordinates).addTo(map);
        return () => {
          marker.remove();
        };
      } catch (e) {
        console.error("could not add marker to map", coordinates, e);
      }
    }
  }, [map, props.coordinates]);

  if (!map) return <Fragment />;

  return (
    <div ref={markerRef} className={props.className}>
      {props.children}
    </div>
  );
};
