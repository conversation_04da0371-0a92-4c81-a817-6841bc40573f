import React, { Fragment, useEffect } from "react";
import { Alert } from "~/components/base/alert";
import { useFormCtx, useResponseId } from "~/components/form/BaseFrom";
import { ResponseIdentifierInput } from "~/components/form/DefaultInput";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { ParamLink } from "~/components/meta/CustomComponents";
import { resetResponseInput } from "~/misc/parsers/global-state-parsers";
import { XMarkIcon } from "@heroicons/react/20/solid";
import { getFinalMessage } from "~/misc/error-translations";
import { useLingui } from "@lingui/react";

export const ActionAlert = (props: { scrollTo?: boolean }) => {
  const { state } = useSearchParams2();
  const responseId = useResponseId();
  const lingui = useLingui();
  const form = useFormCtx();
  const finalIdentifier = form.id || responseId;
  const alertId = "alert-id-prefix" + finalIdentifier;
  const showError = state.response_identifier === finalIdentifier && state.response_error;

  useEffect(() => {
    if (showError && props.scrollTo) {
      const element = window.document.getElementById(alertId);
      element?.scrollIntoView({ behavior: "smooth" });
    }
  }, [alertId, showError, props.scrollTo, state.response_error]);

  return (
    <Fragment>
      {!!form.id && <ResponseIdentifierInput value={finalIdentifier} />}
      {showError && (
        <Alert status={"error"} id={alertId} className="flex flex-row justify-end gap-3 p-0 scroll-mt-16">
          <div className="flex-1 p-3">{getFinalMessage(showError, lingui.i18n)}</div>
          <ParamLink paramState={resetResponseInput} replace className="btn btn-text font-semibold">
            <XMarkIcon className="h-5 w-5" />
          </ParamLink>
        </Alert>
      )}
    </Fragment>
  );
};
