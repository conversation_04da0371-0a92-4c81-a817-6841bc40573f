import React from "react";

export const HamburgerIcon = (props: { className?: string }) => (
  <svg className={props.className} width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M0 1.01408C0 0.454021 0.454021 0 1.01408 0H17.2394C17.7995 0 18.2535 0.454021 18.2535 1.01408V1.26761C18.2535 1.82767 17.7995 2.28169 17.2394 2.28169H1.01408C0.454022 2.28169 0 1.82767 0 1.26761V1.01408Z"
      fill="currentColor"
    />
    <path
      d="M0 9.12676C0 8.5667 0.454021 8.11268 1.01408 8.11268H17.2394C17.7995 8.11268 18.2535 8.5667 18.2535 9.12676V9.38028C18.2535 9.94034 17.7995 10.3944 17.2394 10.3944H1.01408C0.454022 10.3944 0 9.94034 0 9.38028V9.12676Z"
      fill="currentColor"
    />
    <path
      d="M0 16.7324C0 16.1723 0.454021 15.7183 1.01408 15.7183H17.2394C17.7995 15.7183 18.2535 16.1723 18.2535 16.7324V16.9859C18.2535 17.546 17.7995 18 17.2394 18H1.01408C0.454022 18 0 17.546 0 16.9859V16.7324Z"
      fill="currentColor"
    />
  </svg>
);

export const ProfileIcon = (props: { className?: string }) => (
  <svg className={props.className} width="243" height="242" viewBox="0 0 243 242" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M129.857 0.688727C160.529 5.50673 181.485 34.2769 176.668 64.9496C171.85 95.6185 143.08 116.574 112.408 111.757C81.7372 106.937 60.7788 78.1692 65.5978 47.4965C70.4158 16.8277 99.1841 -4.12736 129.857 0.688727Z"
      fill="currentColor"
    />
    <path
      d="M0 242C0 180.698 49.693 131.005 110.995 131.005H131.176C192.478 131.005 242.171 180.698 242.171 242"
      fill="currentColor"
      stroke="orange"
      strokeWidth="2"
    />
  </svg>
);

export const ProfileLoggedInIcon = (props: { className?: string }) => (
  <svg width="41" height="39" viewBox="0 0 41 39" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <mask id="path-1-inside-1_5172_30976" fill="white">
      <path d="M21.9146 0.110993C26.8577 0.887449 30.235 5.52397 29.4587 10.4671C28.6822 15.4096 24.0457 18.7866 19.1026 18.0105C14.1598 17.2337 10.7822 12.5975 11.5589 7.6544C12.3353 2.7119 16.9715 -0.665153 21.9146 0.110993Z" />
      <path d="M0.987305 39C0.987305 29.1207 8.99569 21.1123 18.875 21.1123H22.1272C32.0065 21.1123 40.0149 29.1207 40.0149 39" />
    </mask>
    <path
      d="M21.9146 0.110993C26.8577 0.887449 30.235 5.52397 29.4587 10.4671C28.6822 15.4096 24.0457 18.7866 19.1026 18.0105C14.1598 17.2337 10.7822 12.5975 11.5589 7.6544C12.3353 2.7119 16.9715 -0.665153 21.9146 0.110993Z"
      fill="currentColor"
    />
    <path
      d="M0.987305 39C0.987305 29.1207 8.99569 21.1123 18.875 21.1123H22.1272C32.0065 21.1123 40.0149 29.1207 40.0149 39"
      fill="currentColor"
    />
    <path
      d="M21.9146 0.110993L22.4526 -3.31368L22.4524 -3.31371L21.9146 0.110993ZM29.4587 10.4671L32.8833 11.0051L32.8834 11.0049L29.4587 10.4671ZM19.1026 18.0105L18.5644 21.4351L18.5648 21.4352L19.1026 18.0105ZM11.5589 7.6544L14.9835 8.19245L14.9835 8.19241L11.5589 7.6544ZM21.3767 3.53567C24.4281 4.01498 26.5133 6.87721 26.034 9.92925L32.8834 11.0049C33.9566 4.17073 29.2873 -2.24009 22.4526 -3.31368L21.3767 3.53567ZM26.034 9.92908C25.5547 12.9797 22.6927 15.0651 19.6403 14.5858L18.5648 21.4352C25.3987 22.5082 31.8097 17.8394 32.8833 11.0051L26.034 9.92908ZM19.6408 14.5859C16.5891 14.1063 14.5041 11.244 14.9835 8.19245L8.13419 7.11635C7.0604 13.951 11.7305 20.3612 18.5644 21.4351L19.6408 14.5859ZM14.9835 8.19241C15.4628 5.14163 18.3246 3.05645 21.3769 3.5357L22.4524 -3.31371C15.6184 -4.38676 9.20783 0.282168 8.13419 7.11639L14.9835 8.19241ZM4.45397 39C4.45397 31.0353 10.9103 24.579 18.875 24.579V17.6457C7.0811 17.6457 -2.47936 27.2061 -2.47936 39H4.45397ZM18.875 24.579H22.1272V17.6457H18.875V24.579ZM22.1272 24.579C30.0919 24.579 36.5482 31.0353 36.5482 39H43.4816C43.4816 27.2061 33.9211 17.6457 22.1272 17.6457V24.579Z"
      fill="#FF7557"
      mask="url(#path-1-inside-1_5172_30976)"
    />
  </svg>
);

export const DiverIcon = (props: { className?: string }) => (
  <svg className={props.className} width="27" height="15" viewBox="0 0 27 15" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M20.559 0.176178C19.508 0.638374 19.0307 1.86473 19.4927 2.9156C19.9545 3.96661 21.1809 4.44397 22.2321 3.98192C22.3693 3.92175 22.4965 3.8481 22.6133 3.76375L23.7042 4.40866L24.6004 2.0949L23.4628 1.85826C23.4406 1.65097 23.3869 1.44369 23.2985 1.2425C22.8363 0.191867 21.6101 -0.285677 20.559 0.176178Z"
      fill="currentColor"
    />
    <path
      d="M18.2375 6.15805L18.2334 6.14642C18.157 5.91163 18.1764 5.66132 18.2882 5.44131C18.4001 5.2213 18.5911 5.05793 18.8257 4.98151C18.9188 4.95105 19.0153 4.93592 19.1124 4.93592C19.5129 4.93592 19.8655 5.19175 19.9904 5.57237C19.9952 5.58603 20.001 5.60191 20.0095 5.62424C20.0263 5.66762 20.0549 5.73924 20.0973 5.83282C20.164 5.9792 20.2725 6.19682 20.4266 6.44528C20.7131 6.05433 20.832 5.59415 20.6612 5.18881C20.3458 4.4396 19.3171 3.78117 17.9552 4.2203C16.0863 4.8346 15.0342 5.45404 13.5344 6.8759C12.9083 7.56717 12.8531 8.17664 13.0233 8.6996C12.7018 9.00471 12.2233 9.3894 11.5511 9.76426C10.5078 10.3446 8.99756 10.9065 6.85547 11.092L6.86507 11.1248C7.2933 12.5995 7.0406 13.2928 7.02972 13.3214L7.00221 13.3935L6.98579 13.4007C9.95335 13.1564 12.0386 12.2506 13.3905 11.3456C14.2719 10.7575 14.8413 10.1775 15.1666 9.79138C15.2172 9.7517 15.2692 9.70924 15.3239 9.66329C16.3092 8.69624 17.7463 8.00389 18.8845 7.46435C18.4971 6.84967 18.3025 6.34576 18.2383 6.16062L18.2375 6.15805Z"
      fill="currentColor"
    />
    <path
      d="M6.68625 11.1775C6.68625 11.1775 1.79646 10.1534 1.31117 10.2688C0.826066 10.3844 -0.0287017 12.5482 0.0174177 14.3963C0.0635635 16.2444 6.85546 13.2567 6.85546 13.2567C6.85546 13.2567 7.10193 12.6099 6.68625 11.1775ZM1.40709 11.1087L6.14284 11.5015L6.12013 11.7776L1.38439 11.3848L1.40709 11.1087Z"
      fill="currentColor"
    />
    <path
      d="M10.3586 4.06203L11.5053 6.3817C11.5957 6.56463 11.8174 6.63975 12.0003 6.5493L18.4618 3.35545C18.6447 3.265 18.7198 3.04313 18.6294 2.86037L17.4827 0.540889C17.3923 0.357966 17.1704 0.282842 16.9875 0.373285L10.5261 3.56715C10.3433 3.65741 10.2682 3.87909 10.3586 4.06203Z"
      fill="currentColor"
    />
    <path
      d="M24.6432 10.53C24.6487 10.5301 24.6541 10.5301 24.6593 10.5301C25.0604 10.5303 25.3895 10.2097 25.3981 9.80673C25.4072 9.39862 25.0833 9.06046 24.6751 9.05177C23.7101 9.03017 22.9452 8.8035 22.3187 8.48085C21.3816 7.99759 20.7488 7.27938 20.3511 6.67378C20.1528 6.3718 20.0142 6.10046 19.9274 5.9096C19.8842 5.81436 19.854 5.73923 19.8353 5.69087C19.8259 5.6665 19.8196 5.64897 19.816 5.63881C19.8147 5.63494 19.8137 5.63217 19.8132 5.63051C19.6862 5.24288 19.2692 5.03114 18.8814 5.15778C18.4932 5.28422 18.2813 5.70158 18.4076 6.08976C18.4286 6.14772 18.769 7.191 19.717 8.27783C20.1916 8.81997 20.8221 9.37185 21.6398 9.79439C22.4568 10.2175 23.4598 10.5052 24.6432 10.53Z"
      fill="currentColor"
    />
  </svg>
);

export const SnorkellerIcon = (props: { className?: string }) => (
  <svg className={props.className} width="32" height="17" viewBox="0 0 32 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M22.0349 9.76646L22.0335 9.75387C22.0107 9.50092 22.0875 9.25435 22.25 9.05935C22.4126 8.86435 22.6416 8.74429 22.8944 8.7214C22.9946 8.71217 23.0949 8.7191 23.1923 8.74134C23.5939 8.8331 23.8889 9.17041 23.927 9.58071C23.9287 9.59551 23.9308 9.61274 23.9342 9.63708C23.9411 9.68443 23.9533 9.7628 23.9745 9.86637C24.0078 10.0284 24.0668 10.2715 24.1644 10.556C24.5412 10.2295 24.7658 9.79531 24.6875 9.34973C24.5428 8.52618 23.6621 7.63028 22.1958 7.75866C20.1809 7.94655 18.984 8.32672 17.1544 9.40898C16.3682 9.95875 16.1732 10.5573 16.2241 11.1207C15.8317 11.353 15.2638 11.6291 14.5039 11.8511C13.3248 12.194 11.6816 12.4115 9.49102 12.1069L9.49312 12.142C9.58475 13.7188 9.17253 14.3562 9.15507 14.3824L9.11095 14.4484L9.09283 14.4519C12.1246 14.8866 14.4232 14.456 15.9862 13.8582C17.0047 13.4704 17.7086 13.0192 18.1232 12.7065C18.183 12.6783 18.2449 12.6476 18.3103 12.614C19.5199 11.87 21.1196 11.5049 22.3845 11.2246C22.1368 10.5195 22.0572 9.9696 22.0351 9.76923L22.0349 9.76646Z"
      fill="white"
    />
    <path
      d="M9.30463 12.1562C9.30463 12.1562 4.45345 9.90075 3.94038 9.9053C3.16297 10.0254 1.88099 12.7083 2.00889 13.8582C2.13679 15.008 8.99803 14.2799 8.99803 14.2799C8.99803 14.2799 9.39336 13.6877 9.30463 12.1562ZM4.02654 10.8778L8.68549 12.3566L8.59947 12.6283L3.94052 11.1496L4.02654 10.8778Z"
      fill="white"
    />
    <path
      d="M27.4596 15.6188C27.4651 15.6202 27.4704 15.6214 27.4756 15.6226C27.8778 15.7147 28.2813 15.4686 28.3823 15.0665C28.4849 14.6593 28.2375 14.246 27.8302 14.1438C26.8674 13.9011 26.1524 13.4985 25.598 13.0315C24.769 12.3322 24.299 11.4671 24.0388 10.7687C23.9092 10.4204 23.8323 10.1166 23.7891 9.9053C23.7676 9.7999 23.7544 9.71762 23.7468 9.66486C23.743 9.63827 23.7407 9.61925 23.7393 9.60822C23.7389 9.60404 23.7384 9.59926 23.7384 9.59926C23.6999 9.18146 23.3302 8.87362 22.9123 8.91177C22.4941 8.94965 22.186 9.31962 22.2237 9.73781C22.2315 9.80075 22.3339 10.9249 23.0356 12.2319C23.3873 12.8843 23.8932 13.5821 24.6163 14.1932C25.3386 14.8046 26.2786 15.3229 27.4596 15.6188Z"
      fill="white"
    />
    <path
      d="M25.4462 1.41269C25.4385 1.2611 25.3075 1.14555 25.1562 1.15676L25.0878 1.16182C24.977 1.17001 24.8865 1.24393 24.8522 1.34288C24.841 1.37503 24.8358 1.40982 24.8377 1.44586L25.0199 4.87667L25.0265 5.00071C25.1186 4.92803 25.2166 4.86296 25.3194 4.80622C25.4133 4.75441 25.5113 4.70956 25.6124 4.67221L25.4462 1.41269Z"
      fill="white"
    />
    <path
      d="M24.2464 6.30339C24.0388 7.46585 24.813 8.57628 25.9757 8.78383C26.1274 8.811 26.2782 8.82095 26.4264 8.81583L26.9888 9.99178L29.08 8.52861L28.2438 7.6747C28.3436 7.48492 28.4165 7.27707 28.4562 7.05454C28.6633 5.8922 27.8894 4.7817 26.7269 4.57407C26.339 4.50492 25.957 4.54497 25.6124 4.67221C25.5113 4.70956 25.4133 4.75441 25.3194 4.80622C25.2166 4.86296 25.1186 4.92803 25.0265 5.00071L25.1435 6.74391C25.1547 6.91029 25.1968 7.07365 25.2673 7.22479L25.4268 7.56672C25.5604 7.85294 25.7908 8.08373 26.0764 8.21839L26.3602 8.35192C26.3863 8.3642 26.4086 8.38314 26.4251 8.40684L26.4991 8.51387C26.543 8.57717 26.4977 8.66364 26.4207 8.66364C26.407 8.66364 26.3936 8.66072 26.3812 8.65508L25.8608 8.41853C25.5687 8.28578 25.3334 8.05354 25.1968 7.76329L24.98 7.30252C24.9057 7.14468 24.8627 6.97396 24.8532 6.79977L24.7689 5.24057C24.5066 5.52854 24.3199 5.8916 24.2464 6.30339Z"
      fill="white"
    />
  </svg>
);

export const DurationIcon = (props: { className?: string }) => (
  <svg className={props.className} width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M8.66864 9.24476C8.54261 9.38327 8.32508 9.38444 8.19757 9.24727L6.14224 7.03632C5.51676 6.36349 4.98082 5.61267 4.54772 4.80252L3.537 2.91185C3.42304 2.69868 3.5775 2.44098 3.81922 2.44098L13.0407 2.44098C13.2845 2.44098 13.4387 2.70272 13.3206 2.91601L12.2039 4.93283C11.8028 5.65705 11.3189 6.33211 10.7617 6.94441L8.66864 9.24476Z"
      fill="currentColor"
    />
    <path
      d="M8.16447 6.97039C8.28715 6.8551 8.47817 6.85457 8.60148 6.96918L10.1455 8.40417C11.1071 9.2979 11.8955 10.3615 12.471 11.5414L13.3611 13.3664C13.4648 13.579 13.31 13.8267 13.0735 13.8267L3.69536 13.8267C3.45786 13.8267 3.30312 13.5771 3.40873 13.3644L4.3477 11.4731C4.91145 10.3377 5.67309 9.31177 6.59688 8.44359L8.16447 6.97039Z"
      fill="currentColor"
    />
    <path
      d="M2 0.476016C2 0.299279 2.14327 0.156006 2.32001 0.156006H14.4804C14.6571 0.156006 14.8004 0.299279 14.8004 0.476015V1.29911C14.8004 1.47585 14.6571 1.61912 14.4804 1.61912H2.32001C2.14327 1.61912 2 1.47585 2 1.29911V0.476016Z"
      fill="currentColor"
    />
    <path
      d="M2 15.0129C2 14.8362 2.14327 14.6929 2.32001 14.6929H14.4804C14.6571 14.6929 14.8004 14.8362 14.8004 15.0129V15.836C14.8004 16.0127 14.6571 16.156 14.4804 16.156H2.32001C2.14327 16.156 2 16.0127 2 15.836V15.0129Z"
      fill="currentColor"
    />
  </svg>
);

export const AccomodationIcon = (props: { className?: string }) => (
  <svg className={props.className} width="32" height="17" viewBox="0 0 32 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M13.6335 5.68267C13.6335 7.14366 12.4491 8.32788 10.9883 8.32788C9.52731 8.32788 8.34309 7.14366 8.34309 5.68267C8.34309 4.22185 9.52731 3.03745 10.9883 3.03745C12.4491 3.03745 13.6335 4.22185 13.6335 5.68267Z"
      fill="currentColor"
    />
    <path
      d="M23.566 2.98797H16.0794C15.4306 2.98797 14.9065 3.51213 14.9065 4.16087V9.20184H7.07057V1.64039C7.07057 1.26613 6.77116 0.966553 6.39674 0.966553H5.29884C4.92458 0.966553 4.625 1.26596 4.625 1.64039V14.2927C4.625 14.667 4.92441 14.9666 5.29884 14.9666H6.39691C6.77117 14.9666 7.07075 14.6671 7.07075 14.2927L7.07058 11.6473H25.8869V14.2677C25.8869 14.6419 26.1863 14.9415 26.5607 14.9415H27.6588C28.033 14.9415 28.3326 14.6421 28.3326 14.2677V7.75434C28.3326 5.10913 26.1863 2.98797 23.566 2.98797Z"
      fill="currentColor"
    />
  </svg>
);

export const TransportIcon = (props: { className?: string }) => (
  <svg className={props.className} width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M14.9709 4.52564C14.8712 4.52564 14.7737 4.55332 14.6911 4.60522C14.6083 4.65712 14.5439 4.73078 14.5061 4.81683C14.3535 3.79282 14.2094 2.86002 14.182 2.63122C14.0843 1.87116 13.5955 1.41045 12.8871 1.10322C11.7808 0.624761 10.5957 0.429561 9.40025 0.288818C8.87375 0.226429 8.35058 0.199197 7.8121 0.156006H7.73662C6.75727 0.29518 5.77795 0.400761 4.80209 0.588035C4.1996 0.720624 3.61123 0.903779 3.04418 1.13524C2.2809 1.43601 1.87954 1.99764 1.77663 2.76235C1.70976 3.25833 1.61369 4.00076 1.51595 4.76716C1.46127 4.62854 1.33973 4.52196 1.18803 4.47976C1.03621 4.43746 0.871962 4.46447 0.744769 4.55253C0.617598 4.64058 0.542345 4.77942 0.541748 4.92718V6.51279C0.541748 6.63712 0.594748 6.75631 0.68902 6.84427C0.783172 6.93222 0.911063 6.98155 1.04435 6.98155C1.11541 6.98189 1.18564 6.96771 1.25012 6.94003C1.19354 7.42798 1.15585 7.77844 1.15585 7.86324V12.7001C1.10297 12.7704 1.07474 12.8541 1.07522 12.9401V13.6696C1.0757 13.7824 1.12391 13.8904 1.20933 13.9701C1.29475 14.0498 1.41056 14.0948 1.5314 14.0952H2.34778V15.3209C2.34826 15.5422 2.44278 15.7543 2.6105 15.9109C2.77835 16.0675 3.00577 16.1556 3.24313 16.156H4.66666C4.90377 16.1551 5.13059 16.0668 5.29809 15.9104C5.46546 15.7539 5.55973 15.5419 5.56021 15.3208V14.0952H10.455V15.3208C10.4554 15.5419 10.5497 15.7539 10.7171 15.9104C10.8846 16.0668 11.1115 16.1551 11.3486 16.156H12.7722C13.0095 16.1556 13.2368 16.0675 13.4047 15.9109C13.5724 15.7543 13.6669 15.5422 13.6674 15.3209V14.0952H14.4855C14.6063 14.0948 14.7221 14.0498 14.8075 13.9701C14.8931 13.8904 14.9413 13.7824 14.9416 13.6696V12.9401C14.9416 12.8498 14.911 12.7619 14.8542 12.6888C14.8645 11.2729 14.8731 8.89196 14.8233 7.5511C14.8233 7.40233 14.8044 7.21985 14.7856 7.01505C14.8452 7.03681 14.9086 7.04819 14.9725 7.04864C15.1059 7.04864 15.2337 6.99931 15.3279 6.91136C15.4221 6.82342 15.4751 6.70422 15.4751 6.57988V4.99427C15.4751 4.86972 15.4218 4.75019 15.3273 4.66224C15.2327 4.5743 15.1045 4.5252 14.9709 4.52564ZM5.60649 1.86324H10.4087C10.6276 1.86324 10.8296 1.97206 10.9391 2.14873C11.0484 2.32552 11.0484 2.54327 10.9391 2.71993C10.8296 2.89672 10.6275 3.00553 10.4087 3.00553H5.60649C5.38768 3.00553 5.18562 2.89672 5.07616 2.71993C4.96681 2.54326 4.96681 2.3255 5.07616 2.14873C5.18562 1.97206 5.38769 1.86324 5.60649 1.86324ZM3.26881 11.7624C3.01854 11.7575 2.78093 11.6584 2.61034 11.4874C2.43986 11.3165 2.35061 11.0885 2.36317 10.8553C2.37107 10.6289 2.47204 10.4141 2.64515 10.2554C2.81815 10.0966 3.05013 10.006 3.29276 10.0025C3.54327 10.0083 3.78147 10.1046 3.95708 10.2712C4.13282 10.4377 4.23224 10.6615 4.23439 10.8952C4.22805 11.1293 4.12313 11.3515 3.94249 11.5137C3.76172 11.6761 3.51968 11.7654 3.26881 11.7624ZM3.69244 8.95602C3.43247 8.95647 3.18315 8.86038 2.99939 8.68895C2.81563 8.51752 2.71263 8.2848 2.7131 8.0424V4.83754C2.7131 4.59524 2.81623 4.36288 2.99987 4.19157C3.18351 4.02025 3.43272 3.92393 3.69243 3.92393H12.3227C12.5825 3.92393 12.8316 4.02025 13.0153 4.19157C13.1989 4.36288 13.302 4.59525 13.302 4.83754V8.04869C13.3007 8.29009 13.1969 8.521 13.0134 8.69109C12.8297 8.86107 12.5815 8.95638 12.3227 8.95593L3.69244 8.95602ZM12.6624 11.756C12.4194 11.7461 12.1905 11.647 12.0254 11.4804C11.8603 11.3138 11.7724 11.0932 11.7808 10.8664C11.7799 10.6311 11.8814 10.4057 12.0619 10.2414C12.2425 10.0771 12.4866 9.98829 12.7387 9.99499C12.9908 10.0017 13.2291 10.1035 13.3993 10.2771C13.5695 10.4507 13.6569 10.6813 13.6417 10.9161C13.638 11.1508 13.5311 11.3738 13.3459 11.5326C13.1605 11.6915 12.9134 11.7723 12.6624 11.756Z"
      fill="currentColor"
    />
  </svg>
);

export const DivingGearIcon = (props: { className?: string }) => (
  <svg className={props.className} width="32" height="17" viewBox="0 0 32 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6.90527 1.84705C7.14892 1.84705 7.36197 1.87783 7.57513 1.9085V0.832399C7.57513 0.463468 7.27061 0.156006 6.90527 0.156006C6.53993 0.156006 6.23541 0.463496 6.23541 0.832399V1.9085C6.38761 1.87772 6.57041 1.84705 6.72259 1.84705H6.90527Z"
      fill="currentColor"
    />
    <path d="M3.59375 7.56382H10.2013V11.4379H3.59375V7.56382Z" fill="currentColor" />
    <path
      d="M6.73027 2.52345C4.96409 2.61569 3.59375 4.15307 3.59375 5.9364V6.8895H10.2013V5.84406C10.2014 3.96863 8.64866 2.43095 6.73027 2.52345Z"
      fill="currentColor"
    />
    <path
      d="M3.59375 15.1606C3.59375 15.714 4.02008 16.1445 4.56813 16.1445H9.22696C9.77501 16.1445 10.2013 15.714 10.2013 15.1606V12.086H3.59381L3.59375 15.1606Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.0187 2.74512C16.2242 2.40293 16.2767 2.33459 16.3946 2.28164C16.4645 2.2507 16.5782 2.25108 16.6767 2.28033C16.716 2.29356 16.8668 2.38176 17.0306 2.49213C17.3452 2.70175 17.566 2.81643 17.7802 2.88708C18.0905 2.98867 18.451 3.02838 18.6652 2.9842C18.8991 2.93556 19.0346 2.85399 19.2881 2.61125C19.4826 2.42592 19.546 2.38837 19.6596 2.38837C19.8541 2.38837 20.0071 2.56045 20.1076 2.89369C20.1469 3.0305 20.2868 3.63729 20.3917 4.12938C20.5754 4.99876 20.7217 5.88366 20.8048 6.64051C20.9729 8.14525 20.938 9.08745 20.6321 11.464C20.4573 12.8099 20.4355 13.163 20.4835 13.7986C20.5403 14.5665 20.5162 14.9967 20.3983 15.3432C20.2431 15.7977 19.8781 16.0736 19.3536 16.1376C18.8641 16.1972 18.4664 15.9809 18.1539 15.4888C17.9966 15.2416 17.8655 14.957 17.6535 14.4009C17.4306 13.8184 17.2864 13.5535 16.6111 12.4811C15.9031 11.3579 15.6079 10.8614 15.2474 10.1884C14.5744 8.93495 14.015 7.21794 13.5342 4.94958C13.486 4.71778 13.4359 4.47074 13.4249 4.40009C13.3725 4.04484 13.4338 3.83088 13.6173 3.74037C13.7025 3.69851 13.803 3.70728 14.0171 3.78009C14.3383 3.88599 14.4584 3.90584 14.6311 3.88383C14.9349 3.84411 15.2605 3.65434 15.5817 3.32771C15.7674 3.13793 15.846 3.03419 16.0187 2.74512ZM16.4923 3.61738L17.7167 8.85825C17.7385 8.95162 17.8112 9.02464 17.9044 9.04688C17.9819 9.06535 18.0636 9.04627 18.1249 8.99538C18.1986 8.93412 18.232 8.839 18.2102 8.74564L16.9849 3.501C16.9631 3.40765 16.8907 3.33584 16.7975 3.3136C16.72 3.29511 16.6383 3.31419 16.577 3.3651C16.5033 3.42636 16.4705 3.52402 16.4923 3.61738ZM18.2299 13.3875C18.3104 13.0548 18.5289 12.8342 18.8386 12.7674C18.9817 12.7385 19.2021 12.7801 19.3418 12.8632C19.5066 12.9626 19.6713 13.1651 19.7394 13.3532C19.7591 13.4056 19.8218 13.6642 19.8809 13.9281L19.9883 14.4072V14.5844C19.9883 14.7345 19.9847 14.7742 19.9632 14.8484C19.9076 15.0455 19.7913 15.2172 19.6498 15.3149C19.2613 15.5806 18.7527 15.4251 18.5164 14.9677C18.4466 14.834 18.4322 14.787 18.3141 14.2445C18.2103 13.7744 18.2067 13.7564 18.2067 13.6226C18.2067 13.5195 18.212 13.4597 18.2299 13.3875Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M23.662 2.49656C23.7284 2.36239 23.8437 2.24163 23.9256 2.2148C23.9521 2.20591 24.0009 2.19954 24.0366 2.19954C24.1673 2.19736 24.2359 2.2398 24.4242 2.44558C24.5925 2.62905 24.6989 2.71626 24.834 2.78335C25.0354 2.88164 25.2304 2.91081 25.5006 2.88616C25.9481 2.8459 26.2425 2.74073 26.741 2.44103C26.896 2.34695 27.02 2.28221 27.0798 2.26427C27.2393 2.21497 27.3677 2.24633 27.4718 2.35819C27.5426 2.43433 27.5692 2.47676 27.7176 2.75634C27.9281 3.15448 28.0387 3.31096 28.2736 3.53468C28.4949 3.74935 28.7409 3.90817 28.9469 3.97526C29.0266 4.00209 29.0731 4.00662 29.2326 4.00662C29.4164 4.00662 29.4319 4.00428 29.6335 3.94843C29.886 3.87682 29.9547 3.86792 30.0366 3.89023C30.1806 3.93049 30.2736 4.07137 30.2848 4.26826C30.2937 4.42039 30.2648 4.57467 30.1519 5.00418C29.5561 7.24971 28.8915 8.98973 28.1606 10.2199C27.7508 10.9089 27.3964 11.4388 26.4816 12.7272C25.8459 13.6196 25.6488 13.9438 25.4139 14.4806C24.9045 15.6436 24.5346 16.0664 23.9631 16.1469C23.6839 16.1871 23.2941 16.091 23.0416 15.921C22.8712 15.8046 22.7139 15.61 22.6363 15.4155C22.4814 15.0309 22.4726 14.5432 22.601 13.6151C22.6365 13.3602 22.6387 13.3199 22.6387 12.8099C22.6387 12.5057 22.6297 12.1768 22.6209 12.0471C22.4792 10.1751 22.4614 9.84187 22.4614 8.86898C22.4614 8.05924 22.4658 7.97657 22.5234 7.42412C22.643 6.28772 22.9287 4.93224 23.374 3.36665C23.5469 2.7582 23.5822 2.65086 23.662 2.49656ZM25.7734 8.77026L27.1814 3.57167C27.2064 3.47914 27.1767 3.38159 27.1052 3.31779C27.0457 3.26477 26.9648 3.24285 26.8867 3.25861C26.7927 3.27758 26.7175 3.34801 26.6924 3.44055L25.2855 8.6354C25.2605 8.72794 25.2898 8.8267 25.3614 8.8905C25.4209 8.9435 25.5018 8.96543 25.5799 8.94967C25.6739 8.93071 25.7484 8.8628 25.7734 8.77026ZM23.5431 13.1693C23.7782 12.9142 24.0803 12.8262 24.3876 12.9195C24.5287 12.9643 24.7034 13.1096 24.7863 13.252C24.8834 13.4212 24.9297 13.6817 24.8974 13.8818C24.8891 13.938 24.8174 14.198 24.7398 14.4609L24.5992 14.9383L24.512 15.0953C24.4381 15.2283 24.4153 15.2617 24.3598 15.3168C24.2136 15.4641 24.026 15.5591 23.8526 15.5759C23.3775 15.6201 23.0034 15.232 23.0192 14.7104C23.0231 14.5577 23.0335 14.5089 23.1958 13.9701C23.3353 13.5025 23.3409 13.4847 23.4068 13.3662C23.4575 13.2749 23.4917 13.2245 23.5431 13.1693Z"
      fill="currentColor"
    />
  </svg>
);

export const SnorkelGearIcon = (props: { className?: string }) => (
  <svg className={props.className} width="32" height="16" viewBox="0 0 32 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.02098 5.87187C9.46947 5.93115 10.9605 6.14536 12.0488 6.67386C12.0497 6.67386 12.052 6.67808 12.0529 6.6785C12.2187 6.76021 12.3106 6.91901 12.3621 7.08898H12.7827C12.9202 7.08898 13.0465 7.22893 13.0465 7.38614V8.77784C13.0465 8.93506 12.9202 9.07499 12.7827 9.07499H12.1766C12.1499 9.14502 12.1199 9.20978 12.0777 9.27318C12.0763 9.27549 12.075 9.27571 12.0735 9.27781C12.0729 9.27929 12.0701 9.28139 12.0694 9.28245C11.8441 9.63489 11.4794 9.91237 11.0759 10.1128C10.678 10.3103 10.2422 10.429 9.84325 10.3911C9.60426 10.3686 9.40228 10.271 9.21255 10.0184C9.07894 9.83277 8.99759 9.63481 8.90751 9.4618C8.81743 9.28878 8.72825 9.14262 8.60247 9.04195C8.38061 8.91612 8.20779 8.87884 8.01702 8.87684C7.81752 8.88653 7.5941 8.92475 7.44402 9.04195C7.31823 9.14263 7.22492 9.28879 7.13483 9.4618C7.04474 9.63481 6.9633 9.8329 6.82979 10.0184C6.6244 10.2738 6.43824 10.359 6.21141 10.3911C5.80799 10.4338 5.36556 10.313 4.96233 10.1128C4.55882 9.91238 4.19828 9.63489 3.97293 9.28245C3.97137 9.28002 3.97045 9.2755 3.96879 9.27307C3.92655 9.20968 3.89241 9.14492 3.86573 9.07489H3.25967C3.1221 9.07489 3 8.93494 3 8.77773V7.38604C3 7.22882 3.1222 7.08888 3.25967 7.08888H3.68021C3.73175 6.91892 3.82367 6.76013 3.9894 6.67839C3.99032 6.67839 3.99262 6.67418 3.99354 6.67376C5.27171 6.08268 6.6793 5.86651 8.00887 5.87176H8.0213L8.02098 5.87187ZM7.99623 6.47106C7.18166 6.46537 5.69129 6.47654 4.19927 7.22588C4.20111 7.2282 4.17204 7.26168 4.15391 7.38626C4.1332 7.52874 4.13366 7.74703 4.15805 7.96647C4.18234 8.18592 4.2266 8.41316 4.27344 8.59386C4.32037 8.77467 4.39371 8.92779 4.38479 8.9146C4.38617 8.91776 4.38764 8.92092 4.38893 8.92398C4.52337 9.13753 4.82832 9.39005 5.1722 9.56086C5.51609 9.73166 5.89953 9.82285 6.1699 9.792C6.2968 9.78284 6.35854 9.70533 6.4214 9.64099C6.49722 9.53558 6.58068 9.35583 6.68521 9.15513C6.78975 8.95443 6.92098 8.73011 7.13869 8.55594C7.44706 8.37313 7.74621 8.27415 8.02088 8.27288C8.32953 8.29278 8.67083 8.37829 8.89893 8.55594C9.11665 8.73 9.25616 8.9544 9.3607 9.15513C9.46525 9.35586 9.54456 9.53559 9.62037 9.64099C9.6962 9.7464 9.73466 9.78368 9.85539 9.792C9.85954 9.79189 9.86368 9.79189 9.86773 9.792C10.138 9.82285 10.5215 9.73155 10.8654 9.56086C11.2093 9.39005 11.5143 9.13754 11.6487 8.92398C11.7174 8.80351 11.7351 8.70284 11.7641 8.59374C11.811 8.41294 11.8552 8.18579 11.8795 7.96635C11.9038 7.74692 11.9084 7.5285 11.8878 7.38614C11.8671 7.24366 11.8299 7.21965 11.8424 7.22576H11.8383C10.6157 6.6566 9.25073 6.46714 7.99608 6.47094L7.99623 6.47106Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.72071 1.33133C4.72486 3.71372 4.7297 9.56926 4.73366 11.9516C4.73366 12.8355 5.35085 13.5145 6.06534 13.5145C6.74138 13.5145 7.31877 12.9188 7.38842 12.1152C7.38861 12.1139 7.38833 12.1123 7.38842 12.111C7.03992 11.8975 6.79384 11.5303 6.77645 11.1122C6.77606 10.8987 6.90563 10.7893 7.10833 10.7893H9.05634C9.30166 10.7791 9.3863 10.9032 9.37952 11.1294C9.35624 11.5441 9.11343 11.9138 8.75894 12.1239C8.6802 13.7042 7.5231 15 6.06539 15C4.06868 14.9861 3.33796 13.1785 3.36739 11.9518V1.34011C3.37551 1.08911 3.47338 1.00108 3.70353 1.00002H4.39309C4.61773 0.998279 4.71859 1.11924 4.72062 1.33151L4.72071 1.33133Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.7416 3.22036C15.9221 2.9198 15.9681 2.85977 16.0718 2.81326C16.1331 2.78609 16.233 2.78642 16.3195 2.81211C16.354 2.82373 16.4865 2.9012 16.6304 2.99814C16.9068 3.18227 17.1006 3.283 17.2888 3.34505C17.5614 3.43429 17.878 3.46917 18.0662 3.43037C18.2716 3.38764 18.3906 3.31599 18.6133 3.10277C18.7841 2.93999 18.8398 2.90701 18.9396 2.90701C19.1104 2.90701 19.2448 3.05815 19.3331 3.35086C19.3676 3.47103 19.4905 4.00402 19.5826 4.43625C19.744 5.19988 19.8725 5.97715 19.9455 6.64193C20.0932 7.96364 20.0625 8.79124 19.7938 10.8787C19.6402 12.0609 19.6211 12.3711 19.6633 12.9293C19.7132 13.6038 19.692 13.9817 19.5884 14.2861C19.4521 14.6853 19.1315 14.9276 18.6708 14.9838C18.2409 15.0362 17.8915 14.8462 17.6171 14.4139C17.4789 14.1968 17.3637 13.9468 17.1775 13.4584C16.9817 12.9468 16.8551 12.7141 16.2619 11.7721C15.64 10.7856 15.3808 10.3494 15.0641 9.75828C14.473 8.65729 13.9816 7.14913 13.5593 5.15668C13.517 4.95308 13.4729 4.73608 13.4633 4.67403C13.4172 4.36199 13.471 4.17406 13.6322 4.09456C13.7071 4.05779 13.7953 4.06549 13.9835 4.12944C14.2656 4.22246 14.3711 4.2399 14.5228 4.22057C14.7896 4.18568 15.0756 4.01899 15.3577 3.73209C15.5208 3.5654 15.5899 3.47427 15.7416 3.22036ZM16.1575 3.98652L17.233 8.58992C17.2522 8.67192 17.316 8.73606 17.3979 8.7556C17.466 8.77183 17.5377 8.75507 17.5915 8.71037C17.6563 8.65656 17.6857 8.57301 17.6665 8.49101L16.5903 3.8843C16.5711 3.8023 16.5075 3.73923 16.4256 3.71969C16.3575 3.70346 16.2858 3.72022 16.232 3.76493C16.1672 3.81874 16.1384 3.90452 16.1575 3.98652ZM17.6838 12.5682C17.7545 12.276 17.9464 12.0823 18.2185 12.0236C18.3442 11.9982 18.5377 12.0347 18.6605 12.1077C18.8052 12.195 18.9499 12.3729 19.0097 12.5381C19.027 12.5842 19.0821 12.8113 19.1339 13.0431L19.2283 13.4639V13.6196C19.2283 13.7514 19.2251 13.7863 19.2062 13.8514C19.1574 14.0246 19.0553 14.1754 18.931 14.2612C18.5897 14.4945 18.143 14.358 17.9355 13.9562C17.8741 13.8388 17.8615 13.7975 17.7577 13.321C17.6666 12.9081 17.6634 12.8922 17.6634 12.7747C17.6634 12.6842 17.6681 12.6317 17.6838 12.5682Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M22.1484 3.00204C22.2067 2.88419 22.308 2.77812 22.3799 2.75455C22.4033 2.74674 22.4461 2.74115 22.4774 2.74115C22.5922 2.73923 22.6525 2.77651 22.8179 2.95726C22.9657 3.11841 23.0592 3.19502 23.1778 3.25394C23.3548 3.34027 23.526 3.3659 23.7634 3.34425C24.1564 3.30889 24.4151 3.21651 24.8529 2.95326C24.989 2.87063 25.098 2.81376 25.1505 2.798C25.2906 2.7547 25.4033 2.78224 25.4948 2.8805C25.557 2.94737 25.5803 2.98465 25.7107 3.23022C25.8956 3.57993 25.9928 3.71738 26.199 3.91389C26.3935 4.10245 26.6096 4.24195 26.7905 4.30087C26.8605 4.32444 26.9013 4.32842 27.0414 4.32842C27.2029 4.32842 27.2165 4.32636 27.3936 4.27731C27.6153 4.21441 27.6757 4.20659 27.7476 4.22619C27.8741 4.26155 27.9558 4.38529 27.9656 4.55823C27.9735 4.69186 27.9481 4.82738 27.8489 5.20464C27.3256 7.17704 26.7418 8.7054 26.0998 9.78595C25.7399 10.3911 25.4286 10.8566 24.625 11.9883C24.0667 12.7721 23.8935 13.0569 23.6872 13.5284C23.2398 14.5499 22.9149 14.9213 22.4129 14.992C22.1676 15.0273 21.8252 14.9429 21.6035 14.7936C21.4538 14.6913 21.3156 14.5205 21.2475 14.3496C21.1114 14.0118 21.1037 13.5834 21.2164 12.7682C21.2477 12.5442 21.2496 12.5089 21.2496 12.0609C21.2496 11.7937 21.2417 11.5048 21.2339 11.3909C21.1095 9.74662 21.0939 9.45389 21.0939 8.59934C21.0939 7.8881 21.0977 7.81548 21.1483 7.33023C21.2534 6.33205 21.5043 5.14146 21.8954 3.76629C22.0473 3.23185 22.0784 3.13757 22.1484 3.00204ZM24.003 8.51263L25.2397 3.94638C25.2617 3.8651 25.2356 3.77941 25.1728 3.72338C25.1205 3.67681 25.0494 3.65755 24.9808 3.67139C24.8983 3.68806 24.8323 3.74992 24.8102 3.8312L23.5745 8.39417C23.5524 8.47546 23.5782 8.56221 23.6411 8.61824C23.6933 8.6648 23.7644 8.68406 23.833 8.67022C23.9156 8.65356 23.981 8.59391 24.003 8.51263ZM22.0439 12.3766C22.2505 12.1525 22.5158 12.0753 22.7858 12.1571C22.9097 12.1965 23.0631 12.3242 23.136 12.4493C23.2212 12.5978 23.2619 12.8267 23.2336 13.0024C23.2262 13.0518 23.1633 13.2801 23.0951 13.5111L22.9716 13.9304L22.895 14.0683C22.8301 14.1851 22.8101 14.2145 22.7613 14.2629C22.6329 14.3923 22.4681 14.4757 22.3158 14.4905C21.8985 14.5293 21.5699 14.1884 21.5838 13.7303C21.5872 13.5961 21.5964 13.5533 21.7389 13.08C21.8614 12.6692 21.8664 12.6536 21.9242 12.5495C21.9688 12.4693 21.9988 12.4251 22.0439 12.3766Z"
      fill="currentColor"
    />
  </svg>
);

export const NightDiveIcon = () => (
  <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6.95329 0.156053C5.23174 0.651712 3.71984 1.68274 2.64395 3.0947C1.56775 4.50679 0.985607 6.22404 0.984375 7.98887C0.984375 9.42254 1.3679 10.8309 2.09631 12.0725C2.8247 13.3141 3.87252 14.345 5.13411 15.0619C6.39571 15.7787 7.82704 16.156 9.28365 16.156C10.9418 16.1548 12.5617 15.6647 13.9344 14.749C15.3069 13.8333 16.3693 12.5341 16.9844 11.0188C16.2277 11.2383 15.443 11.3504 14.654 11.352C12.4528 11.352 10.3419 10.4914 8.78526 8.95991C7.22896 7.42825 6.35445 5.35094 6.35445 3.18485C6.35678 2.14699 6.55988 1.11902 6.95333 0.156006L6.95329 0.156053Z"
      fill="currentColor"
    />
  </svg>
);

export const FoodIcon = () => (
  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M2.86769 13.5118C2.20124 14.2192 2.34934 15.2971 3.18239 15.8529C3.49709 16.055 3.86732 16.156 4.21906 16.156C4.81153 16.156 5.38539 15.8865 5.71855 15.3812L7.94003 12.0801L6.36639 9.75575L2.86769 13.5118Z"
      fill="currentColor"
    />
    <path
      d="M12.4044 9.70509C12.9783 8.76194 13.9595 7.1114 14.6259 5.61235C15.4219 3.86079 15.6996 1.75541 15.1998 0.559587C15.0332 0.138523 14.4038 0.0712132 14.1076 0.441632L10.2014 5.34281C10.2385 5.91543 10.1459 6.50498 9.8867 7.04388L12.4044 9.70509Z"
      fill="currentColor"
    />
    <path
      d="M8.49717 7.17856C9.25622 6.18488 9.23761 4.80374 8.36752 3.81006L5.38712 0.357333C5.18348 0.138344 4.83171 0.0878254 4.57253 0.256296L4.51701 0.290014C4.25784 0.475288 4.20231 0.812103 4.40595 1.0479L7.25682 4.38272C7.46047 4.61851 7.40494 4.97227 7.14577 5.14061C6.8866 5.30908 6.51637 5.25856 6.33118 5.02265L3.51723 1.60362C3.33205 1.36783 2.96182 1.31731 2.70265 1.48566L2.62865 1.53618C2.36948 1.70465 2.31395 2.04149 2.499 2.27728L5.22035 5.76369C5.40553 5.99948 5.35 6.3363 5.0907 6.50479C4.81307 6.69006 4.44269 6.62274 4.25767 6.37003L1.64749 2.88363C1.46231 2.63104 1.09208 2.58052 0.814457 2.74887L0.758929 2.78259C0.499759 2.95106 0.444231 3.27096 0.610812 3.50676L3.29511 7.16159C4.07263 8.2227 5.51658 8.64377 6.77547 8.30682L11.5517 15.3807C12.0885 16.1723 13.2733 16.3913 14.1065 15.8355C14.9395 15.2797 15.0877 14.2017 14.4212 13.4944L8.49717 7.17856Z"
      fill="currentColor"
    />
  </svg>
);

//dive site icons
export const BoatEntryIcon = (props: { className?: string }) => (
  <svg width="32" height="19" viewBox="0 0 32 19" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M25.7029 10.1983L26.2771 8.54764H20.6798L19.2086 6.89701L19.5315 6H12.2478L11.8889 7.00465H12.8217L14.2928 8.6192L12.8578 10.1619H5.32287L5 10.9871L5.78937 13.7858H23.1914C24.4831 13.7858 25.5235 13.1401 25.9182 12.0276C26.1695 11.3458 26.6 10.1617 26.6 10.1617L25.7029 10.1983ZM25.0569 10.1983H24.2317L24.5907 9.19361H25.4158L25.0569 10.1983ZM14.1493 7.04098H17.9886L19.3521 8.54796H15.5126L14.1493 7.04098ZM21.2536 9.19368H22.4735L22.1146 10.1625L21.6841 9.6602L21.2536 9.19368ZM22.7606 10.1983L23.1195 9.19368H23.9089L23.55 10.1983H22.7606Z"
      fill="currentColor"
    />
  </svg>
);

export const CaveSiteOutlineIcon = (props: { className?: string }) => (
  <svg width="32" height="19" viewBox="0 0 32 19" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M8.109 6.93138C8.41134 6.00191 8.87334 5.14448 9.46262 4.39144C9.61448 4.19739 9.7748 4.01026 9.94301 3.83062L3 0H1.8C0.805887 0 0 0.805887 0 1.8V3L7.97219 7.39845C8.01333 7.24089 8.05898 7.08515 8.109 6.93138Z"
      fill="white"
    />
    <path
      d="M23.8052 12.3188C23.4938 13.1809 23.0436 13.9767 22.4816 14.6791C22.3509 14.8424 22.2142 15.0006 22.0718 15.1535L22.0672 15.1585L29 19H30.2C31.1941 19 32 18.1941 32 17.2V16L24.0278 11.6015C23.9642 11.8452 23.8898 12.0844 23.8052 12.3188Z"
      fill="white"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.7037 9.5C7.7037 14.0819 11.4181 17.7963 16 17.7963C18.3949 17.7963 20.5528 16.7815 22.0672 15.1585L22.0718 15.1535C22.2142 15.0006 22.3509 14.8424 22.4816 14.6791C23.0436 13.9767 23.4938 13.1809 23.8052 12.3188C23.8898 12.0844 23.9642 11.8452 24.0278 11.6015C24.203 10.9304 24.2963 10.2261 24.2963 9.5C24.2963 4.91808 20.5819 1.2037 16 1.2037C13.6104 1.2037 11.4568 2.21394 9.94301 3.83062C9.7748 4.01026 9.61448 4.19739 9.46262 4.39144C8.87334 5.14448 8.41134 6.00191 8.109 6.93138C8.05898 7.08515 8.01333 7.24089 7.97219 7.39845C7.79698 8.06963 7.7037 8.77392 7.7037 9.5ZM18.7924 14L21.7913 13.9998C21.853 13.9995 21.9112 13.9688 21.951 13.9156C21.9909 13.862 22.0077 13.7913 21.9968 13.7223C21.876 12.9414 20.2654 6.08965 19.1978 6.08965C19.0516 6.08965 18.7329 6.38571 18.1988 8.04362C17.4865 4.3329 17.1028 4.003 16.2714 4.00077C15.7284 3.98004 15.4933 4.3604 14.8745 6.04054C14.601 5.50302 14.3419 4.36041 14.0757 4.36041C12.9436 4.36041 11.1034 10.2417 10.504 13.7177C10.4919 13.7871 10.5077 13.8589 10.5472 13.9138C10.587 13.9684 10.6464 13.9999 10.7089 13.9998L18.7924 14Z"
      fill="white"
    />
    <path
      d="M16.2714 9.16348C15.1429 9.24209 14.2443 10.248 14 13.4062H16.2714H18.625C18.3367 10.101 17.3444 9.11746 16.2714 9.16348Z"
      fill="white"
    />
  </svg>
);

export const SiteCurrentIcon = (props: { className?: string }) => (
  <svg width="32" height="19" viewBox="0 0 32 19" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M25.0701 12.6519L23.1684 11.2732C23.0019 11.1543 22.788 11.1306 22.5979 11.2256C22.4077 11.3206 22.2888 11.5108 22.2888 11.7249V12.5569L17.7487 12.5567C17.4396 12.5567 17.202 12.7945 17.202 13.1034C17.202 13.4125 17.4397 13.6501 17.7487 13.6501H22.2888V14.4821C22.2888 14.696 22.4077 14.8862 22.5979 14.9814C22.6692 15.029 22.7643 15.0527 22.8356 15.0527C22.9545 15.0527 23.0734 15.029 23.1684 14.9577L25.0701 13.579C25.2127 13.484 25.3079 13.3175 25.3079 13.1273C25.3075 12.9371 25.2127 12.7706 25.0701 12.6519Z"
      fill="white"
    />
    <path
      d="M15.1594 12.6519L13.2577 11.2732C13.0913 11.1543 12.8774 11.1306 12.6872 11.2256C12.497 11.3206 12.3781 11.5108 12.3781 11.7249V12.5569L7.86177 12.5567C7.5527 12.5567 7.31509 12.7945 7.31509 13.1034C7.31509 13.4125 7.55286 13.6501 7.86177 13.6501H12.4019V14.4821C12.4019 14.696 12.5208 14.8862 12.711 14.9814C12.7823 15.029 12.8775 15.0527 12.9488 15.0527C13.0676 15.0527 13.1865 15.029 13.2815 14.9577L15.1832 13.579C15.3258 13.484 15.421 13.3175 15.421 13.1273C15.4208 12.9371 15.302 12.7706 15.1594 12.6519Z"
      fill="white"
    />
    <path
      d="M27.0912 8.42154C26.8773 8.37396 26.7108 8.25507 26.4969 8.13635C26.1166 7.89858 25.6412 7.6134 24.7618 7.6134C23.8823 7.6134 23.4068 7.89859 23.0266 8.13635C22.7175 8.32653 22.4799 8.46913 21.9569 8.46913C21.434 8.46913 21.1962 8.32653 20.8873 8.13635C20.5069 7.89858 20.0316 7.6134 19.1521 7.6134C18.2726 7.6134 17.7971 7.89859 17.4169 8.13635C17.0841 8.32653 16.8702 8.46913 16.3473 8.46913C15.8243 8.46913 15.5866 8.32653 15.2777 8.13635C14.8973 7.89858 14.4219 7.6134 13.5425 7.6134C12.663 7.6134 12.1875 7.89859 11.8073 8.13635C11.4982 8.32653 11.2606 8.46913 10.7376 8.46913C10.2147 8.46913 9.97691 8.32653 9.66802 8.13635C9.28766 7.89858 8.81229 7.6134 7.93282 7.6134C7.05336 7.6134 6.57783 7.89859 6.19763 8.13635C6.00744 8.25524 5.84114 8.35025 5.6271 8.39783C5.38932 8.46913 5.17542 8.65932 5.12783 8.8971C5.03282 9.37246 5.46061 9.75283 5.91226 9.63394C6.34004 9.53893 6.62541 9.34874 6.86299 9.20615C7.17205 9.01597 7.40966 8.87338 7.93261 8.87338C8.45556 8.87338 8.69335 9.01597 9.00224 9.20615C9.3826 9.44393 9.85797 9.7291 10.7374 9.7291C11.6169 9.7291 12.0924 9.44391 12.4726 9.20615C12.7817 9.01597 13.0193 8.87338 13.5423 8.87338C14.0652 8.87338 14.303 9.01597 14.6119 9.20615C14.9922 9.44393 15.4676 9.7291 16.3471 9.7291C17.2265 9.7291 17.7021 9.44391 18.0823 9.20615C18.3913 9.01597 18.6289 8.87338 19.1519 8.87338C19.6748 8.87338 19.9126 9.01597 20.2215 9.20615C20.6019 9.44393 21.0773 9.7291 21.9567 9.7291C22.8362 9.7291 23.3117 9.44391 23.6919 9.20615C24.001 9.01597 24.2386 8.87338 24.7615 8.87338C25.2845 8.87338 25.5223 9.01597 25.8312 9.20615C26.0689 9.34875 26.3541 9.51522 26.7582 9.63394C27.1623 9.72894 27.5664 9.44375 27.5664 9.01595C27.5904 8.7543 27.3765 8.49303 27.0912 8.42154Z"
      fill="white"
    />
    <path
      d="M5.79243 6.04412C6.22021 5.94911 6.50559 5.75892 6.74316 5.61634C7.05223 5.42615 7.28984 5.28356 7.81279 5.28356C8.33574 5.28356 8.57352 5.42616 8.88241 5.61634C9.26278 5.85411 9.73815 6.13928 10.6176 6.13928C11.4971 6.13928 11.9726 5.85409 12.3528 5.61634C12.6619 5.42615 12.8995 5.28356 13.4224 5.28356C13.9454 5.28356 14.1832 5.42616 14.4921 5.61634C14.8724 5.85411 15.3478 6.13928 16.2272 6.13928C17.1067 6.13928 17.5822 5.85409 17.9624 5.61634C18.2715 5.42615 18.5091 5.28356 19.0321 5.28356C19.555 5.28356 19.7928 5.42616 20.1017 5.61634C20.4821 5.85411 20.9574 6.13928 21.8369 6.13928C22.7164 6.13928 23.1919 5.85409 23.5721 5.61634C23.8812 5.42615 24.1188 5.28356 24.6417 5.28356C25.1647 5.28356 25.4024 5.42616 25.7113 5.61634C25.9491 5.75893 26.2343 5.9254 26.6384 6.04412C27.0425 6.13913 27.4465 5.85393 27.4465 5.42613C27.4465 5.14094 27.2326 4.87945 26.9473 4.80814C26.7334 4.76055 26.5669 4.64167 26.353 4.52295C25.9726 4.28518 25.4973 4 24.6178 4C23.7384 4 23.2628 4.28519 22.8826 4.52295C22.5736 4.71313 22.3359 4.85573 21.813 4.85573C21.29 4.85573 21.0523 4.71313 20.7434 4.52295C20.363 4.28518 19.8876 4 19.0082 4C18.1287 4 17.6532 4.28519 17.273 4.52295C16.9402 4.71313 16.7263 4.85573 16.2034 4.85573C15.6804 4.85573 15.4426 4.71313 15.1337 4.52295C14.7534 4.28518 14.278 4 13.3985 4C12.5191 4 12.0435 4.28519 11.6633 4.52295C11.3543 4.71313 11.1167 4.85573 10.5937 4.85573C10.0708 4.85573 9.83297 4.71313 9.52409 4.52295C9.14372 4.28518 8.66835 4 7.78889 4C6.95718 3.99983 6.45796 4.28519 6.07759 4.52278C5.88741 4.64166 5.7211 4.73667 5.50706 4.78426C5.26929 4.85556 5.05538 5.04574 5.0078 5.28352C4.93666 5.78262 5.36449 6.16301 5.79243 6.04412Z"
      fill="white"
    />
  </svg>
);

export const SiteDepthIcon = (props: { className?: string }) => (
  <svg width="32" height="19" viewBox="0 0 32 19" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M23.1943 14.7481L24.5127 13.0046C24.6264 12.852 24.649 12.6559 24.5582 12.4816C24.4673 12.3072 24.2855 12.1982 24.0808 12.1982H23.2851L23.2853 8.05762C23.2853 7.77427 23.0579 7.55643 22.7625 7.55643C22.467 7.55643 22.2398 7.77441 22.2398 8.05762V12.22H21.4441C21.2396 12.22 21.0577 12.329 20.9667 12.5033C20.9212 12.5687 20.8985 12.656 20.8985 12.7213C20.8985 12.8303 20.9212 12.9393 20.9894 13.0264L22.3078 14.7699C22.3986 14.9006 22.5578 14.9879 22.7397 14.9879C22.9215 14.9877 23.0807 14.8787 23.1942 14.748L23.1943 14.7481Z"
      fill="white"
    />
    <path
      d="M16.2285 14.7448L17.5468 13.0014C17.6605 12.8487 17.6832 12.6526 17.5923 12.4783C17.5015 12.3039 17.3196 12.1949 17.1149 12.1949H16.3193L16.3194 8.05435C16.3194 7.771 16.0921 7.55316 15.7967 7.55316C15.5011 7.55316 15.2739 7.77115 15.2739 8.05435V12.2167H14.4783C14.2737 12.2167 14.0919 12.3257 14.0009 12.5001C13.9554 12.5654 13.9327 12.6527 13.9327 12.7181C13.9327 12.8271 13.9554 12.9361 14.0235 13.0232L15.3419 14.7666C15.4327 14.8973 15.5919 14.9846 15.7738 14.9846C15.9557 14.9844 16.115 14.8755 16.2285 14.7448Z"
      fill="white"
    />
    <path
      d="M9.26252 14.7466L10.5809 13.0032C10.6946 12.8505 10.7172 12.6545 10.6264 12.4801C10.5355 12.3058 10.3537 12.1968 10.149 12.1968H9.35332L9.35348 8.05617C9.35348 7.77282 9.12612 7.55498 8.83073 7.55498C8.53519 7.55498 8.30797 7.77297 8.30797 8.05617L8.30797 12.2186H7.51233C7.30779 12.2186 7.12593 12.3275 7.03491 12.5019C6.98941 12.5673 6.96673 12.6545 6.96673 12.7199C6.96673 12.8289 6.98941 12.9379 7.05759 13.025L8.37594 14.7684C8.46679 14.8992 8.62598 14.9864 8.80786 14.9864C8.98972 14.9863 9.14891 14.8773 9.26242 14.7465L9.26252 14.7466Z"
      fill="white"
    />
    <path
      d="M5.54427 6.06058C5.97305 5.96535 6.25908 5.77473 6.49721 5.63181C6.80699 5.44118 7.04516 5.29826 7.56932 5.29826C8.09348 5.29826 8.33182 5.44119 8.64143 5.63181C9.02268 5.87013 9.49915 6.15597 10.3807 6.15597C11.2622 6.15597 11.7388 5.87011 12.1199 5.63181C12.4297 5.44118 12.6678 5.29826 13.192 5.29826C13.7161 5.29826 13.9545 5.44119 14.2641 5.63181C14.6453 5.87013 15.1218 6.15597 16.0033 6.15597C16.8848 6.15597 17.3615 5.87011 17.7425 5.63181C18.0523 5.44118 18.2905 5.29826 18.8147 5.29826C19.3388 5.29826 19.5772 5.44119 19.8868 5.63181C20.268 5.87013 20.7445 6.15597 21.626 6.15597C22.5075 6.15597 22.9841 5.87011 23.3652 5.63181C23.675 5.44118 23.9132 5.29826 24.4373 5.29826C24.9615 5.29826 25.1998 5.44119 25.5094 5.63181C25.7478 5.77474 26.0336 5.94159 26.4386 6.06058C26.8436 6.15581 27.2487 5.86996 27.2487 5.44116C27.2487 5.1553 27.0343 4.89321 26.7482 4.82174C26.5338 4.77404 26.367 4.65488 26.1526 4.53588C25.7713 4.29756 25.2949 4.01172 24.4134 4.01172C23.5319 4.01172 23.0552 4.29757 22.6741 4.53588C22.3644 4.72651 22.1262 4.86943 21.602 4.86943C21.0779 4.86943 20.8395 4.7265 20.5299 4.53588C20.1487 4.29756 19.6722 4.01172 18.7907 4.01172C17.9092 4.01172 17.4326 4.29757 17.0515 4.53588C16.7179 4.72651 16.5035 4.86943 15.9794 4.86943C15.4552 4.86943 15.2169 4.7265 14.9073 4.53588C14.526 4.29756 14.0495 4.01172 13.168 4.01172C12.2865 4.01172 11.8099 4.29757 11.4288 4.53588C11.119 4.72651 10.8809 4.86943 10.3567 4.86943C9.83254 4.86943 9.5942 4.7265 9.28459 4.53588C8.90334 4.29756 8.42687 4.01172 7.54537 4.01172C6.71173 4.01155 6.21135 4.29757 5.83009 4.53571C5.63947 4.65487 5.47278 4.7501 5.25824 4.7978C5.01991 4.86927 4.80551 5.05989 4.75781 5.29822C4.68652 5.79848 5.11534 6.17975 5.54427 6.06058Z"
      fill="white"
    />
  </svg>
);

export const SiteTemperatureIcon = (props: { className?: string }) => (
  <svg width="32" height="19" viewBox="0 0 32 19" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M26.0242 13.9453C25.8109 13.8978 25.645 13.7793 25.4317 13.661C25.0525 13.4239 24.5786 13.1396 23.7018 13.1396C22.8251 13.1396 22.351 13.4239 21.972 13.661C21.6638 13.8506 21.4269 13.9927 20.9056 13.9927C20.3842 13.9927 20.1472 13.8506 19.8392 13.661C19.46 13.4239 18.9861 13.1396 18.1094 13.1396C17.2326 13.1396 16.7585 13.4239 16.3795 13.661C16.0477 13.8506 15.8345 13.9927 15.3131 13.9927C14.7918 13.9927 14.5547 13.8506 14.2467 13.661C13.8675 13.4239 13.3936 13.1396 12.5169 13.1396C11.6401 13.1396 11.166 13.4239 10.787 13.661C10.4788 13.8506 10.242 13.9927 9.72061 13.9927C9.19926 13.9927 8.9622 13.8506 8.65426 13.661C8.27505 13.4239 7.80114 13.1396 6.92436 13.1396C6.04759 13.1396 5.57351 13.4239 5.19447 13.661C5.00487 13.7795 4.83907 13.8742 4.62568 13.9216C4.38864 13.9927 4.17539 14.1823 4.12795 14.4194C4.03323 14.8933 4.45971 15.2725 4.90997 15.154C5.33645 15.0592 5.62095 14.8696 5.8578 14.7275C6.16592 14.5379 6.4028 14.3957 6.92415 14.3957C7.4455 14.3957 7.68256 14.5379 7.99051 14.7275C8.36971 14.9645 8.84363 15.2488 9.7204 15.2488C10.5972 15.2488 11.0713 14.9645 11.4503 14.7275C11.7584 14.5379 11.9953 14.3957 12.5166 14.3957C13.038 14.3957 13.2751 14.5379 13.583 14.7275C13.9622 14.9645 14.4361 15.2488 15.3129 15.2488C16.1897 15.2488 16.6637 14.9645 17.0428 14.7275C17.3509 14.5379 17.5878 14.3957 18.1091 14.3957C18.6305 14.3957 18.8676 14.5379 19.1755 14.7275C19.5547 14.9645 20.0286 15.2488 20.9054 15.2488C21.7822 15.2488 22.2562 14.9645 22.6353 14.7275C22.9434 14.5379 23.1803 14.3957 23.7016 14.3957C24.223 14.3957 24.46 14.5379 24.768 14.7275C25.005 14.8697 25.2893 15.0356 25.6922 15.154C26.095 15.2487 26.4979 14.9644 26.4979 14.5379C26.5219 14.277 26.3087 14.0165 26.0242 13.9453Z"
      fill="white"
    />
    <path
      d="M18.5 10.8177C18.7187 10.8177 18.7476 10.9599 19.0555 11.1495C19.4347 11.3865 19.9086 11.6708 20.7854 11.6708C21.6622 11.6708 22.1363 11.3865 22.5153 11.1495C22.8234 10.9598 23.0603 10.8177 23.5817 10.8177C24.103 10.8177 24.3401 10.9599 24.648 11.1495C24.8851 11.2916 25.1694 11.4576 25.5722 11.5759C25.9751 11.6706 26.3779 11.3863 26.3779 10.9598C26.3779 10.6755 26.1647 10.4148 25.8802 10.3437C25.6669 10.2963 25.501 10.1778 25.2877 10.0594C24.9085 9.82236 24.4346 9.53806 23.5578 9.53806C22.6811 9.53806 22.207 9.82238 21.828 10.0594C21.5198 10.249 21.2829 10.3912 20.7616 10.3912C20.2402 10.3912 20.0032 10.249 19.6952 10.0594C19.316 9.82236 19 9.49997 18.5 9.49997V10.8177Z"
      fill="white"
    />
    <path
      d="M4.79001 11.5759C5.21648 11.4812 5.50098 11.2916 5.73783 11.1495C6.04595 10.9598 6.28284 10.8177 6.80419 10.8177C7.32554 10.8177 7.5626 10.9599 7.87054 11.1495C8.24975 11.3865 8.72366 11.6708 9.60044 11.6708C10.4772 11.6708 10.9513 11.3865 11.3303 11.1495C11.6384 10.9598 11.8753 10.8177 12.3967 10.8177C12.918 10.8177 13.6921 11.3104 14 11.5V9.99997C13.6208 9.76293 13.2496 9.53806 12.3729 9.53806C11.4961 9.53806 11.022 9.82238 10.643 10.0594C10.3348 10.249 10.098 10.3912 9.57661 10.3912C9.05526 10.3912 8.8182 10.249 8.51026 10.0594C8.13105 9.82236 7.65714 9.53806 6.78036 9.53806C5.9512 9.53789 5.4535 9.82238 5.0743 10.0592C4.88469 10.1778 4.7189 10.2725 4.50551 10.3199C4.26846 10.391 4.05521 10.5806 4.00777 10.8177C3.93686 11.3152 4.36338 11.6945 4.79001 11.5759Z"
      fill="white"
    />
    <path
      d="M14.4095 8.20028H13.8C13.6415 8.20028 13.5132 8.32873 13.5132 8.48719C13.5132 8.64555 13.6415 8.774 13.8 8.774H14.4095C14.5679 8.774 14.6963 8.64555 14.6963 8.48719C14.6963 8.32873 14.5679 8.20028 14.4095 8.20028ZM14.4095 6.76612H13.3697C13.2113 6.76612 13.0829 6.89457 13.0829 7.05303C13.0829 7.21139 13.2113 7.33984 13.3697 7.33984H14.4095C14.5679 7.33984 14.6963 7.21139 14.6963 7.05303C14.6963 6.89457 14.5679 6.76612 14.4095 6.76612ZM14.4095 5.33196H13.8C13.6415 5.33196 13.5132 5.46041 13.5132 5.61887C13.5132 5.77723 13.6415 5.90568 13.8 5.90568H14.4095C14.5679 5.90568 14.6963 5.77723 14.6963 5.61887C14.6963 5.46041 14.5679 5.33196 14.4095 5.33196ZM14.4095 3.8978H13.3697C13.2113 3.8978 13.0829 4.02625 13.0829 4.18471C13.0829 4.34307 13.2113 4.47152 13.3697 4.47152H14.4095C14.5679 4.47152 14.6963 4.34307 14.6963 4.18471C14.6963 4.02625 14.5679 3.8978 14.4095 3.8978ZM17.8515 10.6384C17.8515 11.0567 17.6853 11.4581 17.3894 11.7539C17.0936 12.0498 16.6923 12.216 16.2739 12.216C16.2149 12.2163 16.156 12.2129 16.0975 12.206C15.6788 12.1601 15.2959 11.9486 15.034 11.6186C14.7721 11.2887 14.653 10.8678 14.7033 10.4496C14.7536 10.0313 14.969 9.65058 15.3016 9.39218V4.01408C15.2914 3.74868 15.3897 3.49066 15.5738 3.29941C15.7579 3.10803 16.012 3 16.2775 3C16.543 3 16.7971 3.10804 16.9812 3.29941C17.1653 3.49068 17.2636 3.74868 17.2535 4.01408V9.39218C17.6355 9.69259 17.8577 10.1524 17.8558 10.6385L17.8515 10.6384Z"
      fill="white"
    />
  </svg>
);

export const SiteVisibilityIcon = (props: { className?: string }) => (
  <svg width="32" height="19" viewBox="0 0 32 19" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M19.8012 8.81504C19.8012 8.65994 19.9297 8.53421 20.0883 8.53421H26.2129C26.3715 8.53421 26.5 8.65994 26.5 8.81504V9.18948C26.5 9.34457 26.3715 9.47031 26.2129 9.47031H20.0883C19.9297 9.47031 19.8012 9.34457 19.8012 9.18948V8.81504Z"
      fill="white"
    />
    <path
      d="M19.868 6.75685C19.827 6.60704 19.9179 6.45305 20.071 6.4129L25.987 4.86232C26.1401 4.82218 26.2975 4.91108 26.3386 5.06089L26.4376 5.42257C26.4787 5.57239 26.3878 5.72637 26.2346 5.76652L20.3187 7.3171C20.1656 7.35724 20.0082 7.26834 19.9671 7.11853L19.868 6.75685Z"
      fill="white"
    />
    <path
      d="M19.9101 10.9099C19.9511 10.76 20.1085 10.6711 20.2617 10.7113L26.1776 12.2619C26.3308 12.302 26.4216 12.456 26.3806 12.6058L26.2815 12.9675C26.2405 13.1173 26.0831 13.2062 25.9299 13.1661L20.014 11.6155C19.8609 11.5753 19.77 11.4213 19.811 11.2715L19.9101 10.9099Z"
      fill="white"
    />
    <path
      d="M13.6046 8.82725C13.6046 10.0979 12.5516 11.128 11.2526 11.128C9.95354 11.128 8.90048 10.0979 8.90048 8.82725C8.90048 7.55657 9.95354 6.52648 11.2526 6.52648C12.5516 6.52648 13.6046 7.55657 13.6046 8.82725Z"
      fill="white"
    />
    <path
      d="M11.3022 4C8.01616 4 5.24118 6.07151 4 9C5.24143 11.9287 8.08939 14 11.3022 14C14.5153 14 17.3632 11.9285 18.6044 9C17.3629 6.07133 14.5882 4 11.3022 4ZM11.3022 12.5716C9.25759 12.5716 7.65094 11.0002 7.65094 9C7.65094 7 9.2574 5.4284 11.3022 5.4284C13.3468 5.4284 14.9534 6.99982 14.9534 9C14.9534 11 13.347 12.5716 11.3022 12.5716Z"
      fill="white"
    />
  </svg>
);

export const MuckSiteIcon = (props: { className?: string }) => (
  <svg width="32" height="19" viewBox="0 0 32 19" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M8.109 6.93138C8.41134 6.00191 8.87334 5.14448 9.46262 4.39144C9.61448 4.19739 9.7748 4.01026 9.94301 3.83062L3 0H1.8C0.805887 0 0 0.805887 0 1.8V3L7.97219 7.39845C8.01333 7.24089 8.05898 7.08515 8.109 6.93138Z"
      fill="white"
    />
    <path
      d="M23.8052 12.3188C23.4938 13.1809 23.0436 13.9767 22.4816 14.6791C22.3509 14.8424 22.2142 15.0006 22.0718 15.1535C22.0703 15.1552 22.0687 15.1568 22.0672 15.1585L29 19H30.2C31.1941 19 32 18.1941 32 17.2V16L24.0278 11.6015C23.9642 11.8452 23.8898 12.0844 23.8052 12.3188Z"
      fill="white"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16 17.7963C11.4181 17.7963 7.7037 14.0819 7.7037 9.5C7.7037 8.77392 7.79698 8.06963 7.97219 7.39845C8.01333 7.24089 8.05898 7.08515 8.109 6.93138C8.41134 6.00191 8.87334 5.14448 9.46262 4.39144C9.61448 4.19739 9.7748 4.01026 9.94301 3.83062C11.4568 2.21394 13.6104 1.2037 16 1.2037C20.5819 1.2037 24.2963 4.91808 24.2963 9.5C24.2963 10.2261 24.203 10.9304 24.0278 11.6015C23.9642 11.8452 23.8898 12.0844 23.8052 12.3188C23.4938 13.1809 23.0436 13.9767 22.4816 14.6791C22.3509 14.8424 22.2142 15.0006 22.0718 15.1535L22.0672 15.1585C20.5528 16.7815 18.3949 17.7963 16 17.7963ZM20.7074 5.48854C20.6725 5.54454 20.6375 5.60055 20.6036 5.65722C20.4755 5.85796 20.3595 6.06604 20.2563 6.28063C20.2498 6.28974 20.2476 6.30112 20.2501 6.31204C20.2526 6.32288 20.2596 6.33216 20.2693 6.33754C20.5626 6.57079 20.7591 6.90426 20.8211 7.27379C20.8848 7.67158 20.8419 8.07903 20.6968 8.45476C20.5931 8.72981 20.4697 8.99699 20.3277 9.25438C20.3079 9.29233 20.2881 9.33025 20.2684 9.36817C20.1202 9.65198 19.9722 9.93555 19.8434 10.2289C19.6874 10.5872 19.5664 10.9596 19.4822 11.3412C19.4339 11.5563 19.3936 11.7728 19.3533 11.9894C19.3373 12.0753 19.3213 12.1612 19.3048 12.247C19.2428 12.6107 19.1578 12.97 19.0502 13.3229C18.9542 13.6342 18.7945 13.9222 18.5812 14.1684C18.3715 14.3954 18.1125 14.5712 17.824 14.6822C17.5678 14.7845 17.3005 14.8569 17.0275 14.8979C16.546 14.9741 16.0587 15.0077 15.5713 14.9985C15.2432 14.9941 14.9146 14.9771 14.5883 14.951C14.254 14.9239 13.9201 14.8931 13.5864 14.8589C13.2276 14.8218 12.8686 14.7842 12.5116 14.732C12.263 14.702 12.0182 14.6469 11.7809 14.5675C11.5792 14.5014 11.3964 14.3878 11.2478 14.2363C11.0633 14.0461 10.9746 13.7828 11.0063 13.5198C11.0363 13.2574 11.1277 13.0058 11.2733 12.7856C11.5159 12.402 11.8169 12.059 12.1653 11.7682C12.1777 11.7579 12.19 11.7475 12.2024 11.7371C12.2583 11.6901 12.3143 11.6429 12.3737 11.599C12.389 11.588 12.4042 11.5769 12.4194 11.5658C12.4701 11.529 12.521 11.492 12.5726 11.4563C12.5911 11.4433 12.5911 11.435 12.5801 11.4167C13.2071 11.0236 13.8778 10.7637 14.4283 10.6568C14.6583 10.6279 14.9265 10.4947 15.1549 10.3812C15.2016 10.358 15.2466 10.3357 15.2893 10.3152C15.3845 10.2685 15.4751 10.213 15.5598 10.1494C15.5687 10.1425 15.5773 10.1347 15.5863 10.1276C15.7954 9.96151 15.9893 9.77726 16.1661 9.57697C16.2426 9.6457 17.6085 7.3558 17.7132 7.14144C17.8601 6.8969 18.0477 6.67914 18.2676 6.49742C18.3476 6.43123 18.4318 6.37042 18.5199 6.31534C18.5649 6.28721 18.6115 6.26154 18.659 6.23787C18.6806 6.22667 18.6828 6.21748 18.6726 6.19708C18.5119 5.86586 18.3614 5.52968 18.2187 5.19091C18.1842 5.11898 18.1592 5.04297 18.1444 4.96467C18.124 4.84276 18.1613 4.71823 18.2455 4.62755C18.3295 4.53696 18.4509 4.49044 18.574 4.50164C18.6972 4.51284 18.8082 4.58057 18.8745 4.68491C18.9137 4.74809 18.947 4.81482 18.974 4.88401C19.1073 5.19502 19.2438 5.50236 19.3894 5.8068C19.4195 5.86934 19.4514 5.93115 19.4889 5.99397C19.4922 5.9896 19.495 5.98505 19.4975 5.98032C19.6221 5.70928 19.7659 5.44755 19.928 5.19718C20.0038 5.073 20.0813 4.94965 20.1592 4.82729C20.2067 4.74599 20.2769 4.68026 20.3611 4.6382C20.4879 4.57875 20.6364 4.59003 20.7527 4.66797C20.869 4.74581 20.9361 4.87891 20.9293 5.01875C20.9255 5.08931 20.905 5.15786 20.8699 5.21913C20.8185 5.31065 20.763 5.39958 20.7074 5.48854Z"
      fill="white"
    />
  </svg>
);

export const ReefSiteIcon = (props: { className?: string }) => (
  <svg width="32" height="19" viewBox="0 0 32 19" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M8.109 6.93138C8.41134 6.00191 8.87334 5.14448 9.46262 4.39144C9.61448 4.19739 9.7748 4.01026 9.94301 3.83062L3 0H1.8C0.805887 0 0 0.805887 0 1.8V3L7.97219 7.39845C8.01333 7.24089 8.05898 7.08515 8.109 6.93138Z"
      fill="white"
    />
    <path
      d="M23.8052 12.3188C23.4938 13.1809 23.0436 13.9767 22.4816 14.6791C22.3509 14.8424 22.2142 15.0006 22.0718 15.1535C22.0703 15.1552 22.0687 15.1568 22.0672 15.1585L29 19H30.2C31.1941 19 32 18.1941 32 17.2V16L24.0278 11.6015C23.9642 11.8452 23.8898 12.0844 23.8052 12.3188Z"
      fill="white"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.7037 9.5C7.7037 14.0819 11.4181 17.7963 16 17.7963C18.3949 17.7963 20.5528 16.7815 22.0672 15.1585L22.0718 15.1535C22.2142 15.0006 22.3509 14.8424 22.4816 14.6791C23.0436 13.9767 23.4938 13.1809 23.8052 12.3188C23.8898 12.0844 23.9642 11.8452 24.0278 11.6015C24.203 10.9304 24.2963 10.2261 24.2963 9.5C24.2963 4.91808 20.5819 1.2037 16 1.2037C13.6104 1.2037 11.4568 2.21394 9.94301 3.83062C9.7748 4.01026 9.61448 4.19739 9.46262 4.39144C8.87334 5.14448 8.41134 6.00191 8.109 6.93138C8.05898 7.08515 8.01333 7.24089 7.97219 7.39845C7.79698 8.06963 7.7037 8.77392 7.7037 9.5ZM21.165 6.89166C21.3967 6.76426 21.6363 6.63243 21.8319 6.75296C21.9434 6.8216 22.0031 6.94711 21.9999 7.1069C21.9944 7.3755 21.8045 7.751 21.452 7.94887C21.2174 8.08079 20.9705 8.10356 20.7527 8.12365C20.5284 8.14432 20.3348 8.16223 20.1841 8.28361C20.0288 8.40866 19.8951 8.8209 19.7657 9.2195C19.6344 9.62396 19.4986 10.0422 19.3061 10.3239C19.0441 10.7072 18.5802 11.1081 18.2881 11.3414C18.6686 11.2926 19.3003 11.1629 19.6937 10.8239C19.9655 10.5898 20.1136 10.2205 20.2444 9.89475C20.4035 9.49807 20.5538 9.12351 20.9044 9.12351C21.0713 9.12351 21.2082 9.21366 21.2898 9.37739C21.4885 9.77582 21.351 10.6428 20.7773 11.4041C20.2685 12.0793 19.3212 12.4367 18.7449 12.6002C18.9048 12.6138 19.0851 12.6178 19.253 12.5963C19.406 12.5766 19.5606 12.5211 19.7102 12.4673C20.0042 12.3617 20.3082 12.2524 20.4893 12.5109C20.5487 12.5955 20.5629 12.6959 20.5306 12.8012C20.4343 13.1148 19.9313 13.4297 19.4756 13.6027C19.1257 13.7355 18.79 13.7018 18.5449 13.6771C18.3557 13.6581 18.2062 13.643 18.1615 13.7093C18.0967 13.8052 18.163 13.918 18.2801 14.0922C18.3411 14.183 18.4041 14.2768 18.4354 14.3746C18.519 14.6356 18.5066 14.9218 18.5061 14.9338C18.5036 14.9846 18.4618 15.0246 18.4109 15.0246H14.2202C14.1703 15.0246 14.1289 14.9861 14.1252 14.9365C14.1237 14.9173 14.0909 14.4631 14.1888 14.1498C14.2478 13.9605 14.3583 13.7602 14.4376 13.6296C13.9382 13.6094 13.4586 13.5547 13.0099 13.4667C12.8765 13.4405 12.7404 13.4674 12.6267 13.5427C12.0586 13.9186 11.0448 14.49 10.6371 14.0942C10.5086 13.9694 10.5007 13.8399 10.5167 13.7533C10.5777 13.4228 11.1011 13.1137 11.408 12.9586C11.123 12.8505 10.636 12.6406 10.3894 12.3879C10.1495 12.1421 9.93871 11.6214 10.0164 11.2088C10.0572 10.992 10.1736 10.8344 10.3528 10.7532C10.7188 10.5873 11.0427 10.9286 11.3856 11.2899C11.6058 11.5221 11.8336 11.7621 12.0916 11.9032C12.4093 12.0772 12.9029 12.2259 13.3299 12.3335C13.0654 12.1745 12.7911 11.9639 12.6022 11.7001C12.1386 11.0525 11.2471 7.93278 11.1909 7.54557C11.1391 7.18933 11.3253 6.93905 11.5328 6.84907C11.7462 6.75659 11.9766 6.82153 12.1341 7.01874C12.2795 7.20079 12.3377 7.5711 12.3993 7.96317C12.4667 8.39265 12.5508 8.9271 12.7508 8.9271C12.8359 8.9271 12.8499 8.74705 12.8606 8.49268C12.8722 8.21268 12.8854 7.89544 13.0746 7.72351C13.2634 7.55201 13.5109 7.49776 13.7207 7.58197C13.9236 7.66333 14.0553 7.86137 14.0729 8.11174C14.0899 8.35491 13.9813 8.64537 13.8554 8.9816C13.7151 9.35642 13.5561 9.78123 13.5766 10.1746C13.6004 10.6321 13.7907 11.0524 14.0731 11.2716C14.2549 11.4127 14.4597 11.4615 14.6819 11.4167C14.8093 11.391 14.9325 11.2612 15.038 11.0412C15.396 10.2953 15.4575 8.67617 14.9968 7.78413C14.7064 7.22182 14.2584 6.75709 13.8615 6.37236C13.8048 6.3174 13.7491 6.26401 13.6951 6.21227C13.3195 5.85242 13.0229 5.56815 12.9511 5.30064C12.8721 5.00635 12.9736 4.71572 13.2037 4.57744C13.43 4.44135 13.7101 4.48688 13.9346 4.6963C14.1037 4.85399 14.2243 5.10787 14.3519 5.37665C14.562 5.8191 14.7793 6.27663 15.2126 6.27663C15.5378 6.27663 15.6446 5.93948 15.7599 5.4632C15.8182 5.22239 15.8733 4.99488 15.9856 4.85719C16.124 4.68752 16.3606 4.62859 16.5612 4.71397C16.8014 4.81624 16.9203 5.08015 16.8789 5.41984C16.8306 5.81641 16.5692 6.13742 16.3591 6.39531C16.2048 6.58482 16.0589 6.76386 16.1186 6.84698C16.1739 6.92383 16.4027 6.80738 16.6046 6.70468C16.8993 6.55468 17.2334 6.38476 17.5009 6.50982C17.6387 6.57427 17.7239 6.69722 17.7407 6.8561C17.7675 7.10813 17.6193 7.39649 17.3631 7.59076C17.1583 7.74595 16.9259 7.77366 16.7208 7.7981C16.5042 7.82388 16.3683 7.84539 16.3037 7.95814C16.2349 8.07807 16.2883 8.21837 16.3885 8.45592C16.4712 8.65197 16.5741 8.89586 16.5937 9.19352C16.6179 9.55915 16.504 10.0091 16.4042 10.3223C16.3824 10.3906 16.3967 10.4643 16.4425 10.5196C16.4887 10.5753 16.556 10.6022 16.6275 10.5934C17.0122 10.5453 17.5667 10.3769 18.0225 9.84743C18.7747 8.9741 18.9386 7.62712 18.8561 7.13269C18.8258 6.95106 18.763 6.76365 18.6964 6.56527C18.6027 6.28587 18.5058 5.99701 18.5058 5.7192C18.5058 5.41192 18.6325 5.13418 18.8286 5.01164C18.9613 4.92869 19.1149 4.92216 19.2608 4.99339C19.5639 5.14088 19.5084 5.66043 19.4678 6.03969L19.4676 6.04141C19.4563 6.14759 19.4456 6.24761 19.4501 6.2919C19.4579 6.3684 19.5079 6.4398 19.5745 6.46969C19.6367 6.49748 19.7044 6.48693 19.7704 6.43913C19.8606 6.37393 19.9524 6.06161 20.0334 5.78615C20.1749 5.30502 20.3353 4.75969 20.6634 4.61857C20.8628 4.53286 21.0517 4.5547 21.1954 4.68001C21.3632 4.82641 21.4354 5.09158 21.3709 5.32496C21.3208 5.50698 21.1732 5.73232 21.0175 5.95724L20.9504 6.05369L20.949 6.05574C20.823 6.23637 20.7042 6.40682 20.6471 6.53349C20.5721 6.69998 20.5608 6.87383 20.6184 6.97654C20.6445 7.02291 20.6815 7.04852 20.7317 7.0548C20.8429 7.06875 21.0059 6.9791 21.164 6.89222L21.165 6.89166Z"
      fill="white"
    />
  </svg>
);

export const ShoreEntryIcon = (props: { className?: string }) => (
  <svg width="32" height="19" viewBox="0 0 32 19" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M14.4088 7.70796C14.7895 7.62341 15.0434 7.45417 15.2548 7.32729C15.5299 7.15804 15.7413 7.03115 16.2067 7.03115C16.672 7.03115 16.8836 7.15805 17.1585 7.32729C17.497 7.53887 17.92 7.79265 18.7026 7.79265C19.4852 7.79265 19.9084 7.53886 20.2467 7.32729C20.5218 7.15804 20.7332 7.03115 21.1986 7.03115C21.6639 7.03115 21.8755 7.15805 22.1504 7.32729C22.4889 7.53887 22.9119 7.79265 23.6945 7.79265C24.4771 7.79265 24.9003 7.53886 25.2386 7.32729C25.5137 7.15804 26.1692 7.02406 26.1692 7.02406C26.4194 6.90383 26.4194 6.54627 26.4194 6.45939C26.4194 6.31681 26.4194 6.1631 26.1692 5.99657C25.8268 5.86681 24.9634 6.14273 24.6251 6.3543C24.329 6.52354 24.1386 6.65043 23.6733 6.65043C23.2079 6.65043 22.9963 6.52354 22.7214 6.3543C22.3829 6.14271 21.9599 5.88894 21.1773 5.88894C20.3947 5.88894 19.9715 6.14273 19.6332 6.3543C19.3582 6.52354 19.1467 6.65043 18.6813 6.65043C18.216 6.65043 18.0044 6.52354 17.7295 6.3543C17.391 6.14271 16.968 5.88894 16.1854 5.88894C15.4453 5.88879 15.001 6.14273 14.6625 6.35415C14.4933 6.45995 14.3453 6.54449 14.1548 6.58684C13.9433 6.65029 13.7529 6.81953 13.7106 7.03112C13.6473 7.47526 14.028 7.81376 14.4088 7.70796Z"
      fill="currentColor"
    />
    <path
      d="M14.4088 10.9672C14.7895 10.8827 15.0434 10.7135 15.2548 10.5866C15.5299 10.4173 15.7413 10.2904 16.2067 10.2904C16.672 10.2904 16.8836 10.4173 17.1585 10.5866C17.497 10.7982 17.92 11.0519 18.7026 11.0519C19.4852 11.0519 19.9084 10.7981 20.2467 10.5866C20.5218 10.4173 20.7332 10.2904 21.1986 10.2904C21.6639 10.2904 21.8755 10.4173 22.1504 10.5866C22.4889 10.7982 22.9119 11.0519 23.6945 11.0519C24.4771 11.0519 24.9003 10.7981 25.2386 10.5866C25.5137 10.4173 26.1692 10.2833 26.1692 10.2833C26.4194 10.1631 26.4194 9.80555 26.4194 9.71867C26.4194 9.57609 26.4194 9.42238 26.1692 9.25586C25.8268 9.12609 24.9634 9.40201 24.6251 9.61358C24.329 9.78282 24.1386 9.90971 23.6733 9.90971C23.2079 9.90971 22.9963 9.78282 22.7214 9.61358C22.3829 9.402 21.9599 9.14822 21.1773 9.14822C20.3947 9.14822 19.9715 9.40201 19.6332 9.61358C19.3582 9.78282 19.1467 9.90971 18.6813 9.90971C18.216 9.90971 18.0044 9.78282 17.7295 9.61358C17.391 9.402 16.968 9.14822 16.1854 9.14822C15.4453 9.14807 15.001 9.40201 14.6625 9.61343C14.4933 9.71923 14.3453 9.80377 14.1548 9.84612C13.9433 9.90957 13.7529 10.0788 13.7106 10.2904C13.6473 10.7345 14.028 11.073 14.4088 10.9672Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.2874 14.3591C19.2874 14.586 19.4378 14.7701 19.6296 14.7701H26.1393C26.3284 14.7701 26.4815 14.5896 26.4815 14.3591V13.5526C26.4815 13.3256 26.3312 13.1418 26.1393 13.1418H19.6296C19.4407 13.1418 19.2874 13.3222 19.2874 13.5526V14.3591ZM7.48485 12.4107C7.75959 13.7178 8.90212 14.7772 10.0359 14.777C10.0359 14.777 16.508 14.7749 17.3804 14.7775C17.53 14.778 17.4763 14.7773 17.4763 14.7773C17.6647 14.7751 17.8145 14.5928 17.8108 14.3625L17.7976 13.5559C17.7939 13.3292 17.6404 13.1474 17.451 13.149C17.451 13.149 17.5247 13.1495 17.386 13.1492C16.5761 13.1464 15.6477 13.0814 14.7598 12.915C13.5312 12.6847 12.6389 12.2896 12.2627 11.7867C11.849 11.2336 11.8606 10.8996 12.2168 9.85153C12.3142 9.56456 12.3508 9.45352 12.3977 9.2931C12.6377 8.47347 12.6625 7.78895 12.3635 7.07841C11.8286 5.80733 10.7466 5.23581 9.2273 5.06394C8.75915 5.01101 8.38416 5 7.63967 5H6.26941C6.08021 5 5.9643 5.17804 6.00993 5.39542L7.48485 12.4107Z"
      fill="currentColor"
    />
  </svg>
);

export const WallSiteIcon = (props: { className?: string }) => (
  <svg width="32" height="19" viewBox="0 0 32 19" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M8.109 6.93138C8.41134 6.00191 8.87334 5.14448 9.46262 4.39144C9.61448 4.19739 9.7748 4.01026 9.94301 3.83062L3 0H1.8C0.805887 0 0 0.805887 0 1.8V3L7.97219 7.39845C8.01333 7.24089 8.05898 7.08515 8.109 6.93138Z"
      fill="white"
    />
    <path
      d="M23.8052 12.3188C23.4938 13.1809 23.0436 13.9767 22.4816 14.6791C22.3509 14.8424 22.2142 15.0006 22.0718 15.1535C22.0703 15.1552 22.0687 15.1568 22.0672 15.1585L29 19H30.2C31.1941 19 32 18.1941 32 17.2V16L24.0278 11.6015C23.9642 11.8452 23.8898 12.0844 23.8052 12.3188Z"
      fill="white"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.572 6.38541H19.5593C19.6183 6.34747 19.6436 6.29688 19.6436 6.24207C19.6436 6.18725 19.6057 6.14088 19.5509 6.10294C19.5558 6.10294 19.5579 6.10439 19.5596 6.10558L19.5635 6.10715L19.5764 6.11334C19.5904 6.11992 19.6056 6.12709 19.6183 6.13667C19.6436 6.15352 19.6647 6.17461 19.6816 6.19569C19.6984 6.22098 19.7027 6.24627 19.7027 6.27157V6.27577C19.6962 6.28224 19.6922 6.29117 19.6888 6.29879L19.6858 6.3053C19.6647 6.34747 19.6268 6.37276 19.572 6.38541ZM18.8257 5.87109C18.851 5.88794 18.8764 5.90903 18.8932 5.93432C18.9016 5.95117 18.9101 5.96805 18.9101 5.9891C18.9101 5.99332 18.909 5.99648 18.908 5.99965C18.9069 6.00281 18.9058 6.00597 18.9058 6.01019C18.9058 6.03548 18.889 6.06077 18.8679 6.08185C18.8468 6.09871 18.8173 6.11135 18.7836 6.11979L18.7728 6.11948L18.7752 6.11768C18.7752 6.11768 18.7773 6.11559 18.7794 6.11559C18.8173 6.0945 18.8426 6.06501 18.8511 6.03128C18.8519 6.02621 18.8529 6.02113 18.8539 6.01606C18.858 5.99585 18.862 5.97562 18.8553 5.9554C18.8504 5.94805 18.8469 5.93925 18.8432 5.92987C18.8405 5.92312 18.8377 5.91607 18.8342 5.90903C18.8215 5.88794 18.8047 5.87106 18.7836 5.85421C18.7794 5.85421 18.7752 5.84998 18.771 5.84577C18.771 5.84157 18.7752 5.84157 18.7752 5.84157L18.7855 5.84659C18.8 5.85349 18.8156 5.86094 18.8257 5.87109ZM19.3865 6.79857C19.3823 6.79857 19.377 6.79752 19.3717 6.79647C19.3665 6.79542 19.3612 6.79437 19.357 6.79437C19.357 6.79226 19.3591 6.79016 19.3591 6.79016C19.3591 6.79016 19.3612 6.78804 19.3612 6.78593C19.3865 6.78593 19.4076 6.77749 19.4287 6.76484C19.4708 6.73955 19.4834 6.70582 19.4792 6.66788C19.475 6.6468 19.4666 6.62571 19.4497 6.60886C19.4371 6.59622 19.4202 6.58357 19.4033 6.57092L19.3991 6.56672H19.4033C19.4371 6.58357 19.4666 6.60465 19.4919 6.62995C19.5172 6.65944 19.5341 6.68897 19.5214 6.7269C19.513 6.74796 19.5003 6.76484 19.4792 6.77749C19.454 6.79437 19.4202 6.79857 19.3865 6.79857ZM19.3485 6.15777C19.3443 6.17042 19.3401 6.18306 19.3274 6.1915C19.3064 6.20836 19.2811 6.221 19.2516 6.22521H19.1884C19.1863 6.22521 19.1842 6.22416 19.1821 6.2231C19.18 6.22205 19.1779 6.221 19.1758 6.221H19.1842C19.2095 6.21677 19.2305 6.21256 19.2516 6.19992C19.2643 6.19148 19.2811 6.17883 19.2896 6.16618C19.3022 6.14933 19.3107 6.12825 19.3107 6.10716C19.3064 6.08608 19.3022 6.06499 19.2896 6.04814C19.2832 6.04182 19.278 6.0355 19.2727 6.02917C19.2674 6.02285 19.2622 6.01653 19.2558 6.01021C19.2516 6.00599 19.2463 6.00282 19.2411 5.99966C19.2358 5.9965 19.2305 5.99334 19.2263 5.98912L19.2305 5.98491C19.2355 5.98825 19.2405 5.99092 19.2452 5.99346C19.2525 5.99734 19.2591 6.00089 19.2642 6.006L19.2804 6.01954C19.3041 6.03928 19.3264 6.05775 19.3401 6.08187L19.345 6.09109C19.3526 6.10501 19.3604 6.11943 19.357 6.13669C19.357 6.14092 19.3527 6.14933 19.3485 6.15777ZM19.1546 6.71429C19.1377 6.7185 19.1209 6.7185 19.104 6.7185C19.0914 6.7185 19.0787 6.71849 19.0702 6.71005V6.70584H19.0786C19.0829 6.70372 19.0882 6.70266 19.0935 6.7016C19.0987 6.70055 19.104 6.6995 19.1082 6.6974C19.1293 6.68896 19.1503 6.68052 19.163 6.66367C19.1883 6.63838 19.1925 6.60889 19.1883 6.57936C19.1798 6.54143 19.1546 6.5119 19.1208 6.48661C19.1208 6.48399 19.1192 6.48299 19.117 6.48162C19.117 6.48162 19.114 6.47977 19.1124 6.47817H19.1166C19.125 6.48658 19.1292 6.49082 19.1377 6.49502C19.163 6.50767 19.184 6.52875 19.2051 6.54984C19.2304 6.57513 19.2389 6.60042 19.2389 6.62995C19.2389 6.63415 19.2389 6.63839 19.2347 6.64259C19.232 6.65054 19.2277 6.65682 19.2238 6.66247C19.2215 6.66583 19.2194 6.66897 19.2178 6.67212C19.2052 6.6932 19.1841 6.71008 19.1546 6.71429ZM19.0408 6.36857C19.0281 6.36857 19.0155 6.36857 19.0028 6.37277C18.9942 6.37494 18.9856 6.37379 18.9775 6.37271C18.9698 6.37168 18.9626 6.37072 18.9564 6.37278V6.36857L18.9588 6.36815L18.9627 6.36647C18.9648 6.36542 18.9669 6.36437 18.969 6.36437C18.9732 6.36224 18.9785 6.36118 18.9838 6.36013C18.9891 6.35907 18.9943 6.35802 18.9985 6.35593C19.0154 6.35169 19.0281 6.34328 19.0407 6.33484C19.0534 6.32643 19.066 6.31375 19.0702 6.2969C19.0787 6.27161 19.0829 6.24208 19.066 6.21679C19.0492 6.1915 19.0323 6.16618 19.0028 6.14933L18.999 6.14901V6.14901L18.997 6.14769L18.9944 6.14513H19.0028C19.0323 6.16198 19.0618 6.17886 19.0829 6.20415C19.0914 6.21259 19.104 6.22523 19.1124 6.23788C19.1167 6.24632 19.1209 6.25473 19.1209 6.26317V6.28846C19.1209 6.29462 19.1186 6.29852 19.1158 6.30346L19.1124 6.30955C19.104 6.3306 19.0871 6.34748 19.0619 6.36013L19.0558 6.36343C19.0509 6.36631 19.047 6.36857 19.0408 6.36857ZM18.8215 6.66368C18.8103 6.66648 18.799 6.66555 18.7878 6.66462C18.7822 6.66415 18.7766 6.66368 18.7709 6.66368C18.7625 6.65948 18.7583 6.65948 18.7414 6.65951C18.7456 6.6553 18.7498 6.6553 18.7498 6.6553C18.7562 6.65317 18.7615 6.65212 18.7668 6.65106C18.772 6.65001 18.7773 6.64895 18.7836 6.64686C18.8047 6.63842 18.8257 6.62998 18.8384 6.61734C18.851 6.60469 18.8595 6.59204 18.8637 6.5794C18.8721 6.55411 18.8721 6.53302 18.8637 6.51194C18.8637 6.50191 18.8584 6.49453 18.852 6.48559L18.8468 6.47821C18.8384 6.46136 18.8215 6.44448 18.8004 6.43183L18.7962 6.42762C18.7983 6.42972 18.8015 6.43081 18.8047 6.43186C18.8078 6.43292 18.811 6.43397 18.8131 6.43606C18.8468 6.45712 18.8763 6.48241 18.8974 6.51194C18.91 6.52879 18.9185 6.54567 18.9185 6.56252C18.9227 6.59201 18.9101 6.6173 18.8806 6.63839C18.8637 6.65104 18.8426 6.65948 18.8215 6.66368ZM18.2945 6.62995L18.2608 6.66368C18.2582 6.66629 18.254 6.66891 18.2481 6.67254L18.2434 6.67544C18.241 6.67697 18.2383 6.67866 18.2355 6.68056C18.3159 6.50818 18.4103 6.34621 18.506 6.18206L18.5348 6.13249H18.5433L18.5408 6.14265C18.5373 6.15745 18.5332 6.17448 18.5264 6.19151C18.5159 6.22318 18.5043 6.25378 18.4926 6.28438C18.4811 6.31491 18.4695 6.34544 18.459 6.37702C18.4505 6.40231 18.4421 6.4276 18.4294 6.45289C18.4126 6.48238 18.3915 6.51191 18.3704 6.5372C18.3451 6.57093 18.3198 6.60042 18.2945 6.62995ZM17.7591 6.18727C17.7549 6.17463 17.7507 6.16198 17.7507 6.14934C17.7507 6.14555 17.7498 6.14177 17.7489 6.13762C17.7478 6.1325 17.7465 6.12682 17.7465 6.11984C17.7549 6.09876 17.7634 6.07767 17.7802 6.06082C17.7865 6.0566 17.7929 6.05133 17.7992 6.04606C17.8055 6.04079 17.8118 6.03551 17.8182 6.03129C17.835 6.02285 17.8561 6.01441 17.8772 6.01021C17.8862 6.01021 17.8952 6.009 17.9036 6.00788C17.9109 6.00691 17.9177 6.006 17.9236 6.006C17.9383 6.006 17.955 6.01012 17.971 6.01403C17.9779 6.01572 17.9847 6.01738 17.991 6.01865C18.0205 6.02706 18.0416 6.03974 18.0627 6.05659C18.0837 6.07344 18.1048 6.09452 18.1133 6.11981L18.1168 6.12946C18.121 6.14046 18.1259 6.15333 18.1259 6.16619C18.1301 6.18727 18.1259 6.20836 18.1133 6.22521C18.1006 6.2463 18.0795 6.26738 18.0542 6.28003C18.0248 6.29688 17.9952 6.30532 17.9573 6.30532C17.9151 6.30532 17.8814 6.29688 17.8477 6.28003C17.8182 6.26315 17.7971 6.2463 17.7802 6.22521C17.7718 6.21257 17.7633 6.19992 17.7591 6.18727ZM19.3317 6.49505C19.319 6.49505 19.3064 6.49505 19.2937 6.49926C19.2853 6.50136 19.2779 6.50031 19.2705 6.49926C19.2632 6.49821 19.2557 6.49716 19.2473 6.49926V6.49506C19.2494 6.49506 19.2515 6.49401 19.2536 6.49295C19.2557 6.4919 19.2578 6.49085 19.2599 6.49085C19.2641 6.48873 19.2694 6.48767 19.2747 6.48661C19.28 6.48556 19.2852 6.48451 19.2894 6.48241C19.3063 6.47818 19.319 6.46977 19.3316 6.46133C19.3443 6.45291 19.3569 6.44024 19.3611 6.42339C19.3696 6.39809 19.3738 6.36857 19.3569 6.34328C19.3401 6.31799 19.3232 6.29267 19.2937 6.27582C19.2895 6.27582 19.2895 6.27582 19.2853 6.27161H19.2937C19.3232 6.28846 19.3527 6.30534 19.3738 6.33063C19.3822 6.33907 19.3949 6.35172 19.4033 6.36436C19.4076 6.3728 19.4118 6.38122 19.4118 6.38966V6.41495C19.4118 6.42111 19.4095 6.42501 19.4067 6.42995L19.4033 6.43603C19.3949 6.45709 19.378 6.47397 19.3527 6.48661L19.3467 6.48992C19.3418 6.49279 19.3379 6.49505 19.3317 6.49505Z"
      fill="white"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.7037 9.5C7.7037 14.0819 11.4181 17.7963 16 17.7963C18.3949 17.7963 20.5528 16.7815 22.0672 15.1585L22.0718 15.1535C22.2142 15.0006 22.3509 14.8424 22.4816 14.6791C23.0436 13.9767 23.4938 13.1809 23.8052 12.3188C23.8898 12.0844 23.9642 11.8452 24.0278 11.6015C24.203 10.9304 24.2963 10.2261 24.2963 9.5C24.2963 4.91808 20.5819 1.2037 16 1.2037C13.6104 1.2037 11.4568 2.21394 9.94301 3.83062C9.7748 4.01026 9.61448 4.19739 9.46262 4.39144C8.87334 5.14448 8.41134 6.00191 8.109 6.93138C8.05898 7.08515 8.01333 7.24089 7.97219 7.39845C7.79698 8.06963 7.7037 8.77392 7.7037 9.5ZM18.1935 8.49145C18.2104 8.37287 18.1596 8.25417 18.0579 8.18643L16.4478 6.94907L15.5834 4.23728C15.5495 4.10168 15.4139 4 15.2614 4L11.7868 4C11.6004 4 11.4479 4.15251 11.4479 4.33895L11.4479 14.6609C11.4479 14.8474 11.6004 14.9999 11.7868 14.9999L20.0071 15C20.1257 15 20.2275 14.9492 20.2952 14.8475C20.363 14.7458 20.38 14.6271 20.3461 14.5254L19.5495 12.2372C19.5324 12.1864 19.4986 12.1356 19.4647 12.1016L18.0071 10.7457L18.1935 8.49145ZM17.6074 6.0903C17.5779 6.11559 17.5568 6.14088 17.5357 6.17041C17.5261 6.18316 17.519 6.1983 17.5124 6.21221L17.5062 6.22522C17.4977 6.23787 17.4935 6.25052 17.4976 6.25469C17.4993 6.26144 17.4997 6.2682 17.5 6.27495C17.5005 6.28506 17.501 6.29517 17.5061 6.30528C17.5103 6.32213 17.5145 6.33901 17.5272 6.35165C17.5328 6.36008 17.5422 6.36851 17.5515 6.37694C17.5562 6.38116 17.5609 6.38537 17.5651 6.38959C17.5778 6.40226 17.5904 6.41067 17.6073 6.41912C17.6157 6.42332 17.6284 6.42756 17.641 6.43176L17.6789 6.44441C17.6895 6.44651 17.699 6.44862 17.7085 6.45073C17.7179 6.45284 17.7274 6.45495 17.738 6.45705C17.7443 6.45705 17.7496 6.4581 17.7548 6.45915C17.7601 6.46021 17.7654 6.46126 17.7717 6.46126C17.8181 6.46546 17.8687 6.4697 17.9193 6.46126C17.9699 6.45705 18.0205 6.44017 18.0584 6.41488L18.0636 6.41063L18.0748 6.40164C18.0943 6.3861 18.1127 6.37146 18.1259 6.35165C18.1427 6.3306 18.1554 6.31372 18.1638 6.28843C18.1638 6.28422 18.168 6.27999 18.1722 6.27578C18.1807 6.26737 18.1891 6.26737 18.1975 6.27158C18.2018 6.27158 18.206 6.28002 18.206 6.28422C18.206 6.29266 18.2017 6.30107 18.1975 6.30951C18.1891 6.32636 18.1806 6.34324 18.168 6.36009C18.1554 6.37697 18.1427 6.38962 18.1301 6.40227L18.0795 6.4402L18.0542 6.45285C18.0205 6.46973 17.9867 6.47814 17.953 6.48658C17.9193 6.49502 17.8813 6.49502 17.8476 6.49502C17.8055 6.49502 17.7633 6.49081 17.7254 6.48237L17.6621 6.46973H17.6495C17.6453 6.48661 17.6495 6.49925 17.6621 6.5119C17.6706 6.51822 17.6779 6.52454 17.6853 6.53087C17.6927 6.53719 17.7001 6.54351 17.7085 6.54984C17.7254 6.56248 17.7422 6.57513 17.7633 6.58357C17.8013 6.60465 17.8392 6.62574 17.8772 6.64259C17.894 6.64891 17.9109 6.65629 17.9277 6.66367C17.9446 6.67105 17.9615 6.67844 17.9784 6.68476C17.9934 6.6898 18.007 6.69633 18.02 6.70258C18.0288 6.7068 18.0373 6.71088 18.0458 6.71429C18.0521 6.71639 18.0595 6.71955 18.0669 6.72271C18.0743 6.72587 18.0817 6.72903 18.088 6.73114C18.1048 6.73958 18.1217 6.74802 18.1386 6.76066C18.1343 6.76066 18.1512 6.78595 18.1639 6.7986C18.1849 6.81968 18.206 6.83656 18.2271 6.85342C18.2397 6.86183 18.2523 6.87128 18.2649 6.88074C18.2776 6.89026 18.2903 6.89978 18.303 6.90823C18.3156 6.91665 18.3272 6.92509 18.3388 6.93352C18.3504 6.94195 18.362 6.95039 18.3746 6.95881C18.3852 6.96514 18.3968 6.97252 18.4084 6.9799C18.4199 6.98728 18.4315 6.99466 18.4421 7.00098C18.4601 7.01388 18.4798 7.0252 18.5 7.03687C18.5129 7.04427 18.526 7.05182 18.539 7.06001C18.5517 7.06845 18.5644 7.07686 18.5812 7.0853C18.5918 7.08952 18.6034 7.09479 18.6149 7.10006C18.6265 7.10533 18.6381 7.1106 18.6487 7.11482C18.6613 7.12326 18.6782 7.13167 18.6951 7.14011L18.7028 7.14257C18.714 7.14609 18.727 7.15015 18.7372 7.15696L18.7625 7.16961C18.7688 7.17171 18.7752 7.17487 18.7815 7.17804C18.7878 7.1812 18.7941 7.18436 18.8005 7.18646C18.8131 7.1949 18.8637 7.21178 18.8763 7.21599C18.8784 7.21599 18.8816 7.21704 18.8848 7.21809C18.8879 7.21914 18.8911 7.22019 18.8932 7.22019C18.8985 7.22019 18.9372 7.23188 18.9693 7.2416C18.9883 7.24732 19.005 7.25236 19.0112 7.25392C19.0154 7.25813 19.0197 7.25813 19.0239 7.25813C19.0239 7.25813 19.0534 7.26657 19.066 7.27077C19.0703 7.2729 19.0756 7.27396 19.0808 7.27501C19.0861 7.27607 19.0914 7.27712 19.0956 7.27921C19.1061 7.28132 19.1156 7.28343 19.1251 7.28554C19.1346 7.28765 19.144 7.28976 19.1546 7.29186C19.1651 7.29396 19.1756 7.29712 19.1862 7.30027C19.1967 7.30344 19.2073 7.30661 19.2178 7.30871C19.2305 7.31081 19.242 7.31292 19.2536 7.31503C19.2652 7.31714 19.2768 7.31925 19.2895 7.32136C19.3 7.32346 19.3095 7.32557 19.319 7.32768C19.3285 7.32979 19.338 7.3319 19.3485 7.334C19.357 7.33613 19.3654 7.33718 19.3739 7.33824C19.3823 7.33929 19.3907 7.34035 19.3991 7.34244C19.4118 7.34457 19.4234 7.34563 19.4351 7.34668C19.4466 7.34774 19.4582 7.34879 19.4708 7.35088C19.4792 7.35301 19.4877 7.35407 19.4961 7.35512C19.5045 7.35618 19.5129 7.35723 19.5213 7.35932C19.5424 7.36356 19.5677 7.36776 19.593 7.36776C19.6225 7.36776 19.6562 7.36776 19.6857 7.37197C19.6921 7.37197 19.6973 7.37302 19.7026 7.37407C19.7079 7.37512 19.7132 7.37617 19.7195 7.37617C19.7448 7.38038 19.7701 7.38038 19.7954 7.38038H19.8628C19.8755 7.38038 19.8755 7.37617 19.8713 7.36773L19.8333 7.31715C19.8312 7.31293 19.8281 7.30872 19.8249 7.3045C19.8217 7.30029 19.8186 7.29608 19.8165 7.29186C19.8143 7.28975 19.8133 7.28763 19.8122 7.28552C19.8112 7.28341 19.8101 7.28131 19.808 7.27921C19.7912 7.25392 19.7743 7.22863 19.7617 7.1991C19.7532 7.18222 19.7448 7.16114 19.7406 7.14429C19.7385 7.13375 19.7353 7.12426 19.7321 7.11478C19.729 7.10529 19.7258 7.09581 19.7237 7.08527V7.06842C19.7237 7.0642 19.7227 7.05998 19.7216 7.05577C19.7206 7.05156 19.7195 7.04734 19.7195 7.04312C19.7195 7.0389 19.7185 7.03469 19.7174 7.03048C19.7164 7.02627 19.7153 7.02205 19.7153 7.01783V6.9799C19.7153 6.9799 19.7153 6.97566 19.7111 6.97146C19.7027 6.97778 19.6942 6.98305 19.6858 6.98832C19.6774 6.99359 19.6689 6.99887 19.6605 7.00519C19.6521 7.00941 19.6447 7.01362 19.6373 7.01783C19.6299 7.02205 19.6226 7.02626 19.6141 7.03048C19.6015 7.0347 19.5899 7.03891 19.5783 7.04312C19.5667 7.04734 19.5551 7.05155 19.5425 7.05577C19.5214 7.06418 19.4961 7.06842 19.4708 7.07262C19.463 7.07391 19.4548 7.07561 19.4465 7.07733C19.4278 7.08122 19.4082 7.08527 19.3907 7.08527C19.3527 7.08947 19.3148 7.08947 19.2769 7.08947C19.2473 7.08947 19.2178 7.08947 19.1754 7.07694L19.1227 7.07062L19.07 7.0643C19.0574 7.0622 19.0437 7.06009 19.03 7.05798C19.0163 7.05587 19.0026 7.05375 18.9899 7.05165C18.9773 7.04955 18.9636 7.04639 18.9499 7.04323C18.9362 7.04007 18.9225 7.0369 18.9098 7.0348C18.8719 7.02636 18.8381 7.01795 18.8044 7.00951C18.7791 7.00531 18.758 7.0011 18.7327 6.99266C18.7264 6.99056 18.7211 6.98845 18.7159 6.98634C18.7106 6.98423 18.7053 6.98212 18.699 6.98001C18.6927 6.97789 18.6863 6.97683 18.68 6.97577C18.6737 6.97472 18.6674 6.97367 18.6611 6.97157C18.6564 6.97001 18.6511 6.96785 18.6454 6.96554C18.6358 6.96166 18.6253 6.95736 18.6147 6.95472C18.5937 6.94841 18.5737 6.94103 18.5537 6.93366C18.5336 6.92627 18.5136 6.91888 18.4925 6.91255C18.4819 6.90833 18.4724 6.90412 18.4629 6.89991C18.4535 6.89569 18.444 6.89148 18.4334 6.88726C18.4208 6.88304 18.4081 6.87777 18.3955 6.8725C18.3829 6.86723 18.3702 6.86196 18.3576 6.85774C18.347 6.85352 18.3375 6.85035 18.328 6.84719C18.3186 6.84403 18.3091 6.84087 18.2985 6.83665C18.2859 6.83244 18.2775 6.82824 18.269 6.8198H18.2775C18.2859 6.82402 18.2943 6.82718 18.3027 6.83034C18.3112 6.8335 18.3196 6.83666 18.328 6.84088C18.3407 6.8451 18.3544 6.85038 18.3681 6.85565C18.3818 6.86092 18.3955 6.86619 18.4081 6.87041C18.4184 6.87722 18.4314 6.88129 18.4427 6.88482L18.4503 6.88726C18.4566 6.88936 18.464 6.89147 18.4714 6.89358C18.4788 6.89569 18.4862 6.8978 18.4925 6.89991C18.5051 6.90832 18.522 6.91255 18.5389 6.91676C18.5431 6.91888 18.5484 6.91994 18.5537 6.921C18.5589 6.92205 18.5642 6.9231 18.5684 6.9252C18.5933 6.9335 18.6222 6.94177 18.6512 6.95007L18.6527 6.95049C18.6594 6.95274 18.6674 6.9538 18.6759 6.95493C18.6833 6.95592 18.6912 6.95696 18.6991 6.95893C18.7075 6.96103 18.7149 6.96314 18.7223 6.96525C18.7296 6.96736 18.737 6.96947 18.7455 6.97157H18.7497C18.756 6.9737 18.7634 6.97476 18.7708 6.97581C18.7782 6.97687 18.7855 6.97792 18.7918 6.98001L18.812 6.98515C18.8376 6.99172 18.8611 6.99775 18.8846 7.0011C18.9268 7.00954 18.9689 7.01798 19.0111 7.02219L19.1123 7.03483C19.127 7.03483 19.1418 7.03588 19.1565 7.03693C19.1713 7.03799 19.186 7.03904 19.2008 7.03904C19.2124 7.03904 19.224 7.0393 19.2356 7.03957C19.2704 7.04036 19.3052 7.04116 19.3399 7.03483C19.3547 7.03063 19.3704 7.02853 19.3862 7.02643C19.4021 7.02432 19.4179 7.02221 19.4327 7.01798C19.4453 7.01798 19.4538 7.01798 19.4664 7.01377C19.4727 7.01167 19.4801 7.00956 19.4875 7.00745C19.4949 7.00534 19.5023 7.00323 19.5086 7.00113C19.5381 6.98848 19.5676 6.97584 19.5929 6.96319L19.6435 6.92526C19.6646 6.90841 19.6856 6.89152 19.7025 6.87467C19.7067 6.86835 19.7109 6.86308 19.7151 6.85781C19.7194 6.85254 19.7236 6.84727 19.7278 6.84094C19.7342 6.8324 19.7417 6.82279 19.7491 6.81319C19.7564 6.80381 19.7637 6.79445 19.77 6.78613C19.7784 6.77348 19.7868 6.76084 19.7952 6.7524C19.7995 6.74818 19.8037 6.7429 19.8079 6.73763C19.8121 6.73236 19.8163 6.72709 19.8205 6.72287C19.8344 6.71177 19.8465 6.69883 19.8591 6.68526C19.8657 6.67824 19.8724 6.67104 19.8796 6.66385C19.9006 6.64276 19.9259 6.62168 19.947 6.60483C19.9521 6.59972 19.9588 6.59616 19.966 6.59228C19.9708 6.58975 19.9758 6.58708 19.9808 6.58374C19.9839 6.58217 19.9871 6.58001 19.9904 6.57771C19.9961 6.57383 20.0024 6.56953 20.0103 6.56689C20.0356 6.55425 20.0609 6.5458 20.0862 6.5416C20.1199 6.53739 20.1536 6.54583 20.1789 6.56269C20.1831 6.56692 20.1873 6.56692 20.1915 6.57113C20.2084 6.58377 20.2169 6.59645 20.2211 6.6133C20.2295 6.64279 20.238 6.67232 20.2506 6.69761C20.2632 6.7271 20.2801 6.75663 20.297 6.78192L20.3602 6.85779C20.3771 6.87887 20.3981 6.89996 20.4192 6.91681C20.4403 6.9379 20.4656 6.95478 20.4909 6.97163C20.5078 6.98427 20.5246 6.99692 20.5457 7.00956L20.5963 7.03486C20.6028 7.0413 20.6117 7.04529 20.6193 7.0487L20.6258 7.05171C20.6343 7.06012 20.6469 7.06435 20.6595 7.06856C20.6638 7.07069 20.667 7.07174 20.6701 7.0728C20.6733 7.07385 20.6764 7.07491 20.6806 7.077H20.6975C20.7017 7.077 20.7017 7.07276 20.7017 7.06856L20.6764 6.98001C20.6741 6.97327 20.6731 6.96534 20.672 6.95684C20.671 6.94939 20.6699 6.94151 20.668 6.93363C20.6638 6.91255 20.6595 6.88725 20.6553 6.86196C20.6511 6.84932 20.6511 6.84088 20.6511 6.82823C20.6511 6.82191 20.6501 6.81664 20.649 6.81137C20.648 6.8061 20.6469 6.80082 20.6469 6.7945C20.6427 6.77342 20.6427 6.75657 20.6427 6.73548V6.58368C20.6469 6.57524 20.6469 6.56259 20.6469 6.54994L20.6487 6.54115C20.6523 6.52396 20.6553 6.509 20.6553 6.49092C20.6553 6.47635 20.6654 6.43356 20.6716 6.40715C20.6744 6.39535 20.6764 6.38553 20.6764 6.38553C20.6848 6.36447 20.6891 6.34338 20.6933 6.3223C20.6951 6.31512 20.6976 6.30796 20.7003 6.30046C20.7039 6.29033 20.7077 6.27961 20.7101 6.26748C20.7123 6.26325 20.7133 6.25796 20.7144 6.25268C20.7154 6.24742 20.7165 6.24216 20.7186 6.23796C20.7207 6.22743 20.7249 6.21796 20.7291 6.20849C20.7333 6.19899 20.7376 6.18949 20.7397 6.17894C20.7397 6.16911 20.744 6.16071 20.7484 6.15207C20.7515 6.14589 20.7548 6.13959 20.7565 6.13256C20.7607 6.11571 20.7692 6.09883 20.7776 6.08198L20.8029 6.03139C20.8092 6.02296 20.8145 6.01349 20.8197 6.00401C20.825 5.99451 20.8303 5.98501 20.8366 5.97658C20.8429 5.96815 20.8482 5.95972 20.8535 5.95129C20.8588 5.94285 20.864 5.93442 20.8703 5.926C20.8746 5.91967 20.8798 5.91335 20.8851 5.90703C20.8904 5.9007 20.8957 5.89438 20.8999 5.88806C20.9118 5.87611 20.9216 5.86205 20.9309 5.84888C20.9347 5.84345 20.9383 5.83817 20.942 5.83324C20.9484 5.82482 20.9557 5.81744 20.9631 5.81005C20.9705 5.80267 20.9779 5.79529 20.9842 5.78687C20.9863 5.78476 20.9884 5.7816 20.9905 5.77844C20.9926 5.77528 20.9948 5.77212 20.9969 5.77002C21.0011 5.76158 20.9969 5.75737 20.9842 5.75737C20.9631 5.75737 20.942 5.75737 20.9252 5.76158C20.883 5.76578 20.8451 5.76999 20.8071 5.77843C20.7692 5.78687 20.7355 5.79951 20.7017 5.81216L20.6895 5.81859C20.6781 5.82465 20.6679 5.83012 20.6554 5.83324C20.6356 5.83984 20.621 5.849 20.6055 5.85873L20.5921 5.86697L20.5747 5.8771C20.558 5.88663 20.5402 5.89676 20.5247 5.90915C20.5078 5.92179 20.491 5.93444 20.4783 5.94708C20.4573 5.96393 20.4362 5.98502 20.4151 6.0061C20.4109 6.01243 20.4056 6.01875 20.4003 6.02507C20.395 6.03139 20.3898 6.03772 20.3856 6.04404C20.3771 6.05248 20.3645 6.06513 20.356 6.07777L20.3307 6.11571C20.3286 6.11993 20.3255 6.1252 20.3223 6.13047C20.3192 6.13574 20.316 6.14101 20.3139 6.14523C20.3118 6.14734 20.3107 6.14946 20.3096 6.15158C20.3086 6.15368 20.3075 6.15578 20.3054 6.15788C20.3012 6.16629 20.297 6.17575 20.2928 6.1852C20.2886 6.19472 20.2844 6.20424 20.2802 6.2127L20.2675 6.23799V6.24219C20.2654 6.24851 20.2633 6.25379 20.2612 6.25906C20.2591 6.26433 20.257 6.2696 20.2549 6.27592C20.2506 6.28436 20.2422 6.28857 20.2338 6.28857L20.1706 6.30121C20.1453 6.30542 20.12 6.30542 20.0947 6.30542C20.0694 6.30542 20.0441 6.30121 20.023 6.29277C20.0019 6.28433 19.9808 6.27589 19.964 6.26325C19.9429 6.2506 19.9218 6.23372 19.905 6.21687C19.8839 6.20002 19.8712 6.17894 19.8586 6.15785C19.8474 6.1438 19.8399 6.12975 19.8324 6.11568C19.8286 6.10866 19.8249 6.10164 19.8207 6.09462C19.8194 6.09205 19.8177 6.08909 19.8159 6.08585C19.8117 6.07847 19.8067 6.06968 19.8038 6.06089C19.7911 6.04404 19.7827 6.02716 19.7743 6.01031C19.7701 6.00399 19.7669 5.99766 19.7637 5.99134C19.7606 5.98502 19.7574 5.9787 19.7532 5.97237C19.7511 5.96815 19.7479 5.96394 19.7448 5.95973C19.7416 5.95551 19.7384 5.9513 19.7363 5.94708C19.734 5.94359 19.7317 5.93978 19.7293 5.93583C19.7229 5.92545 19.716 5.91407 19.7068 5.90491C19.6942 5.88803 19.6815 5.87539 19.6689 5.86274C19.652 5.84165 19.6309 5.82057 19.6099 5.80372C19.5888 5.78263 19.5593 5.76575 19.5297 5.7489C19.5003 5.73202 19.475 5.71938 19.4454 5.70673C19.4286 5.69831 19.4118 5.69363 19.3949 5.68895C19.3865 5.68661 19.378 5.68426 19.3696 5.68144C19.3591 5.67934 19.3485 5.67618 19.338 5.67302C19.3274 5.66986 19.3169 5.66669 19.3063 5.66459C19.2979 5.66246 19.2894 5.66141 19.281 5.66035C19.2726 5.6593 19.2642 5.65824 19.2558 5.65615L19.2094 5.64982L19.163 5.6435C19.1494 5.6435 19.1364 5.64213 19.1236 5.64077C19.1047 5.63877 19.0861 5.63679 19.066 5.6393H18.9438C18.9311 5.6393 18.9195 5.64035 18.9079 5.6414C18.8963 5.64245 18.8848 5.6435 18.8721 5.6435C18.8616 5.6435 18.8511 5.64455 18.8405 5.6456C18.83 5.64666 18.8194 5.64771 18.8089 5.64771C18.7983 5.64771 18.7889 5.64876 18.7794 5.64981C18.7699 5.65086 18.7604 5.65191 18.7499 5.65191C18.7246 5.65191 18.7035 5.65191 18.6782 5.65612C18.6693 5.65835 18.6593 5.65941 18.6487 5.66052C18.6391 5.66152 18.6291 5.66257 18.6192 5.66456C18.5939 5.66456 18.5686 5.66879 18.5433 5.673C18.5391 5.673 18.5338 5.67405 18.5285 5.6751C18.5233 5.67615 18.518 5.6772 18.5138 5.6772V5.673C18.5277 5.673 18.5388 5.67011 18.5517 5.66673L18.5602 5.66456L18.6234 5.65191C18.636 5.64981 18.6476 5.6477 18.6592 5.64559C18.6708 5.64348 18.6824 5.64137 18.695 5.63927C18.7035 5.63714 18.712 5.63608 18.7204 5.63503C18.7288 5.63397 18.7372 5.63292 18.7456 5.63083L18.8215 5.61818C18.8347 5.61597 18.8479 5.61491 18.8618 5.61381C18.8744 5.6128 18.8875 5.61175 18.9016 5.60974C18.9122 5.60974 18.9216 5.60869 18.9311 5.60764C18.9406 5.60659 18.9501 5.60554 18.9606 5.60554C18.9738 5.60554 18.9859 5.60439 18.9974 5.60329C19.008 5.60229 19.018 5.60133 19.0281 5.60133H19.0913C19.1377 5.60133 19.1883 5.60133 19.2347 5.60554C19.2607 5.60554 19.2834 5.60884 19.3087 5.6125L19.319 5.61398C19.3485 5.61818 19.378 5.62239 19.4033 5.63083C19.4286 5.63506 19.4581 5.64347 19.4834 5.65191L19.5719 5.68985C19.5871 5.69592 19.6001 5.70417 19.614 5.71303C19.6194 5.71648 19.625 5.72003 19.631 5.72358C19.6352 5.72779 19.6394 5.73202 19.6478 5.73623C19.6562 5.74046 19.6604 5.74046 19.6604 5.73202C19.6604 5.7257 19.6594 5.72043 19.6583 5.71516C19.6573 5.70988 19.6562 5.70461 19.6562 5.69829V5.65191C19.6562 5.63927 19.6605 5.62239 19.6647 5.60974C19.6668 5.60132 19.6689 5.59394 19.671 5.58655C19.6731 5.57917 19.6752 5.57179 19.6773 5.56337C19.6816 5.54652 19.69 5.52963 19.6984 5.51278C19.7026 5.50436 19.7068 5.49698 19.7111 5.4896C19.7153 5.48221 19.7195 5.47483 19.7237 5.46641C19.73 5.45798 19.7353 5.44955 19.7406 5.44112C19.7458 5.43268 19.7511 5.42425 19.7574 5.41582C19.7617 5.4095 19.7669 5.40318 19.7722 5.39686C19.7775 5.39053 19.7827 5.38421 19.787 5.37789L19.8587 5.30619C19.8671 5.30196 19.8755 5.29355 19.8797 5.28511C19.8882 5.27667 19.884 5.27246 19.8713 5.27246C19.8608 5.27246 19.8492 5.27351 19.8376 5.27456C19.826 5.27561 19.8144 5.27667 19.8038 5.27667C19.7954 5.28087 19.7828 5.28087 19.7701 5.28087C19.7431 5.28312 19.7161 5.28657 19.6897 5.28994C19.6667 5.29288 19.6441 5.29576 19.6225 5.29772C19.6015 5.29772 19.5762 5.30196 19.5509 5.30616C19.5282 5.30842 19.5068 5.31191 19.4859 5.3153C19.4679 5.31822 19.4504 5.32107 19.4328 5.32301C19.4149 5.32301 19.4 5.32607 19.383 5.32957L19.3738 5.33145C19.3612 5.33356 19.3475 5.33672 19.3337 5.33988C19.32 5.34304 19.3063 5.3462 19.2937 5.3483C19.2873 5.35043 19.281 5.35149 19.2747 5.35255C19.2684 5.3536 19.2621 5.35465 19.2558 5.35674C19.222 5.36098 19.1883 5.36939 19.1546 5.37783L19.1451 5.37939C19.1221 5.38313 19.0966 5.38728 19.0745 5.39468C19.0576 5.3989 19.0418 5.40311 19.026 5.40733C19.0102 5.41154 18.9944 5.41575 18.9775 5.41997C18.9354 5.43262 18.8974 5.44526 18.8721 5.45785C18.8384 5.46629 18.8046 5.47893 18.7709 5.49158C18.7625 5.49368 18.7541 5.49684 18.7456 5.50001C18.7372 5.50317 18.7288 5.50633 18.7203 5.50843L18.6571 5.53372C18.6445 5.53794 18.6318 5.54321 18.6192 5.54848C18.6065 5.55376 18.5939 5.55903 18.5812 5.56325C18.5475 5.57589 18.5138 5.58857 18.48 5.60542C18.4548 5.61386 18.4295 5.62651 18.4042 5.63915C18.3936 5.64337 18.3841 5.64864 18.3747 5.65391C18.3652 5.65918 18.3557 5.66446 18.3451 5.66868L18.2946 5.69397L18.2842 5.69899C18.2697 5.7059 18.2541 5.71334 18.244 5.72349C18.2355 5.72913 18.2271 5.731 18.2187 5.73287C18.2145 5.73381 18.2103 5.73474 18.206 5.73614C18.2018 5.73824 18.1966 5.74035 18.1913 5.74246C18.186 5.74457 18.1807 5.74668 18.1765 5.74878C18.166 5.753 18.1555 5.75826 18.145 5.76352C18.1344 5.7688 18.1239 5.77408 18.1133 5.77831C18.1028 5.78463 18.0912 5.7899 18.0796 5.79518C18.068 5.80045 18.0564 5.80572 18.0458 5.81204C18.0332 5.81837 18.0195 5.82575 18.0058 5.83313C17.9921 5.84051 17.9784 5.84789 17.9657 5.85421C17.9438 5.86794 17.9218 5.87989 17.901 5.89121C17.8899 5.89727 17.8791 5.90315 17.8688 5.90903L17.7676 5.97226C17.7604 5.97702 17.753 5.98178 17.7453 5.98663C17.726 5.99898 17.7056 6.01194 17.6875 6.02707C17.6655 6.03959 17.6459 6.05677 17.6269 6.07342C17.6204 6.07918 17.6139 6.08488 17.6074 6.0903Z"
      fill="white"
    />
  </svg>
);

export const WreckSiteIcon = (props: { className?: string }) => (
  <svg width="32" height="19" viewBox="0 0 32 19" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M8.109 6.93138C8.41134 6.00191 8.87334 5.14448 9.46262 4.39144C9.61448 4.19739 9.7748 4.01026 9.94301 3.83062L3 0H1.8C0.805887 0 0 0.805887 0 1.8V3L7.97219 7.39845C8.01333 7.24089 8.05898 7.08515 8.109 6.93138Z"
      fill="white"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.7037 9.5C7.7037 14.0819 11.4181 17.7963 16 17.7963C18.3949 17.7963 20.5528 16.7815 22.0672 15.1585C22.0687 15.1568 22.0703 15.1552 22.0718 15.1535C22.2142 15.0006 22.3509 14.8424 22.4816 14.6791C23.0436 13.9767 23.4938 13.1809 23.8052 12.3188C23.8898 12.0844 23.9642 11.8452 24.0278 11.6015C24.203 10.9304 24.2963 10.2261 24.2963 9.5C24.2963 4.91808 20.5819 1.2037 16 1.2037C13.6104 1.2037 11.4568 2.21394 9.94301 3.83062C9.7748 4.01026 9.61448 4.19739 9.46262 4.39144C8.87334 5.14448 8.41134 6.00191 8.109 6.93138C8.05898 7.08515 8.01333 7.24089 7.97219 7.39845C7.79698 8.06963 7.7037 8.77392 7.7037 9.5ZM16.4354 9.57601L16.8807 11.9078L21.2362 11.1272L20.9732 13.9587L20.9191 14.5416L20.8918 14.835H19.8375H11.8015L11.0492 12.9534L15.9588 12.0733L15.9015 11.7732L15.5124 9.73599C15.1022 9.6761 14.7338 9.44928 14.4963 9.11328C14.362 9.67098 13.915 10.1284 13.3159 10.2434C13.2661 10.253 13.2163 10.2599 13.1668 10.2644C13.1214 10.2685 13.0762 10.2706 13.0313 10.2706C12.464 10.2706 11.9449 9.94812 11.6895 9.4494C11.6261 9.32547 11.5789 9.19067 11.5514 9.04723L15.2454 8.33814L15.1717 7.95263L11.0648 8.74095C11.0393 8.74582 11.0136 8.74825 10.9881 8.74825C10.9085 8.74825 10.8306 8.72458 10.7631 8.67882C10.6743 8.61848 10.6141 8.52715 10.5938 8.42161L10.5118 7.99465C10.47 7.77671 10.6133 7.56544 10.8312 7.52362L14.9393 6.7351L14.9077 6.56977L14.8624 6.33229L14.5767 6.38714L14.2279 6.45411L14.1085 6.12881L13.6222 4.80409L14.537 4.62846L14.4702 4.27935L15.3904 4.10363L15.4569 4.4518L16.3219 4.28581L16.3702 6.04279L15.7824 6.15563L15.8593 6.55844L20.0896 5.74597C20.1947 5.72567 20.3021 5.74766 20.3912 5.8081C20.4803 5.86845 20.5405 5.95977 20.5608 6.06532L20.6427 6.49228C20.6628 6.59792 20.6408 6.70497 20.5805 6.79395C20.5201 6.88294 20.4288 6.9431 20.3232 6.96332L16.8347 7.63294L16.0915 7.7756L16.1651 8.16111L17.3722 7.9295L19.9051 7.44352C20.0219 8.05273 19.7526 8.64813 19.2692 8.97613C19.1049 9.08763 18.9158 9.16823 18.7088 9.20797C18.613 9.22641 18.5169 9.23548 18.4215 9.23548C18.1229 9.23548 17.8314 9.1466 17.5789 8.97537C17.4254 8.87133 17.2961 8.74173 17.1924 8.59455C17.0924 9.01132 16.8176 9.37203 16.4354 9.57601Z"
      fill="white"
    />
    <path
      d="M23.8052 12.3188C23.4938 13.1809 23.0436 13.9767 22.4816 14.6791C22.3509 14.8424 22.2142 15.0006 22.0718 15.1535C22.0703 15.1552 22.0687 15.1568 22.0672 15.1585L29 19H30.2C31.1941 19 32 18.1941 32 17.2V16L24.0278 11.6015C23.9642 11.8452 23.8898 12.0844 23.8052 12.3188Z"
      fill="white"
    />
  </svg>
);

export const CaptainIcon = (props: { className?: string }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M15.1572 3.73842C15.1572 2.32851 12.359 1.16975 8.77797 1.03519C8.40609 0.99002 7.93978 0.988445 7.56208 1.02984C3.894 1.13118 1 2.30458 1 3.73839C1 4.42328 1.66177 5.04855 2.75127 5.52632V6.10498C2.74608 6.14936 2.76748 6.46411 2.76748 6.46411C3.0292 7.63056 5.30654 8.54191 8.07863 8.54191C10.8503 8.54191 13.1279 7.6304 13.3898 6.46411C13.3898 6.46411 13.4118 6.14999 13.4055 6.10577V5.52647C14.4953 5.04853 15.1572 4.42331 15.1572 3.73842ZM7.32922 3.25891C7.32922 3.71924 7.57992 4.03321 7.8078 4.22614V3.14953H7.491V2.60802H7.8078V2.60566C7.65294 2.51313 7.54829 2.34552 7.54829 2.15243C7.54829 1.86018 7.78608 1.62224 8.07848 1.62224C8.37087 1.62224 8.60867 1.86003 8.60867 2.15243C8.60867 2.34537 8.50401 2.51313 8.34947 2.60566V2.60771H8.66642V3.14922L8.34915 3.14906V4.22595C8.57703 4.03333 8.8279 3.71921 8.8279 3.25856H9.36941C9.36941 4.44327 8.25061 4.93917 8.20279 4.95964L8.15164 4.98167H8.00529L7.95414 4.95964C7.9063 4.93918 6.78753 4.44315 6.78753 3.25856L7.32922 3.25891ZM12.7592 6.26632H3.39753V5.46971H12.7592V6.26632Z"
      fill="currentColor"
    />
    <path
      d="M12.8639 10.1754V8.38254C12.8639 8.38254 10.9661 9.48992 8.03933 9.48992C5.06004 9.48992 3.21476 8.38254 3.21476 8.38254L3.21476 10.1754C3.21476 12.8398 5.37492 15 8.03933 15C10.7037 15 12.8639 12.84 12.8639 10.1754Z"
      fill="currentColor"
    />
  </svg>
);

export const CrewIcon = (props: { className?: string }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M1.86742 8.77779H11.2009V10.3334C11.2009 12.9107 9.11146 15 6.53432 15C3.95717 15 1.86776 12.9106 1.86776 10.3334L1.86742 8.77779ZM14.82 6.00972C15.146 6.11843 15.3222 6.47083 15.2135 6.79684C15.1048 7.12286 14.7524 7.29912 14.4264 7.19041L13.144 6.76295L13.9227 8.16454C14.0896 8.46499 13.9813 8.84375 13.681 9.0107C13.3806 9.17755 13.0017 9.06936 12.8348 8.76892L11.7699 6.85201L11.201 7.84456H1.86753L0.0792375 4.7245C-0.151245 4.30968 0.148664 3.80001 0.623083 3.80001H12.4455C12.9199 3.80001 13.2198 4.30968 12.9894 4.7245L12.6646 5.29122L14.82 6.00972ZM3.32477 2.86678L6.20802 1.09233C6.4081 0.969222 6.66025 0.969222 6.86033 1.09233L9.74359 2.86678H3.32477Z"
      fill="currentColor"
    />
  </svg>
);

export const PoolIcon = (props: { className?: string }) => (
  <svg width="32" height="16" viewBox="0 0 32 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M24.9019 3.5377V3.30241C24.9019 1.78257 24.2315 1.15864 22.5422 1C20.7407 1 19.2751 1.78261 19.2751 3.30241V3.5377H16.3712V3.30241C16.3712 1.78257 15.3593 1 13.5578 1C11.7563 1 10.2907 1.78261 10.2907 3.30241V3.5377L2.23389 3.53749V5.36952L10.2902 5.36973V11.588C10.2902 11.9685 10.6558 12.2771 11.1071 12.2771C11.5581 12.2771 11.9239 11.9687 11.9239 11.588V10.9839H19.2749V11.588C19.2749 11.9685 19.6405 12.2771 20.0918 12.2771C20.5428 12.2771 20.9086 11.9687 20.9086 11.588L20.9084 5.36973L29.1312 5.36952V3.53749L24.9019 3.5377ZM11.924 3.30241C11.9241 2.73019 12.4352 2.37801 13.5577 2.37801C14.25 2.37801 14.7376 2.74711 14.7376 3.30241V3.5377H11.9241L11.924 3.30241ZM11.924 9.6225V8.12595H19.275L19.2749 9.6225H11.924ZM19.275 6.74774H11.924V5.36952H19.275V6.74774ZM20.9087 3.30241C20.9087 2.83182 21.5688 2.37801 22.2494 2.37801C22.8704 2.37801 23.2686 2.81526 23.2686 3.30241V3.5377H20.9087L20.9087 3.30241Z"
      fill="#8BB7C8"
    />
    <path
      d="M2.95277 10.9768C3.46711 10.8804 3.81022 10.6875 4.09587 10.5429C4.46747 10.35 4.75316 10.2054 5.38192 10.2054C6.01068 10.2054 6.29659 10.35 6.66797 10.5429C7.1253 10.7841 7.69685 11.0733 8.75427 11.0733V9.79132C8.1277 9.79132 7.81307 9.62789 7.44338 9.43586L7.43948 9.43383C6.98215 9.19265 6.4106 8.90339 5.35319 8.90339C4.35319 8.90322 3.75296 9.19267 3.29563 9.43366C3.06696 9.55425 2.86701 9.65062 2.60966 9.69889C2.32378 9.77122 2.06659 9.96413 2.00937 10.2053C1.92385 10.7116 2.43824 11.0974 2.95277 10.9768Z"
      fill="#8BB7C8"
    />
    <path
      d="M22.2394 10.9781C22.2417 11.0311 22.2936 11.0735 22.3564 11.0722C23.3422 11.0525 23.8893 10.7753 24.3299 10.5429C24.7015 10.35 24.9872 10.2054 25.616 10.2054C26.2447 10.2054 26.5307 10.35 26.902 10.5429C27.1879 10.6875 27.5308 10.8564 28.0167 10.9768C28.5025 11.0732 28.9883 10.7839 28.9883 10.35C28.9883 10.0607 28.7312 9.79545 28.388 9.72312C28.1309 9.67485 27.9307 9.55426 27.6735 9.43383C27.2162 9.19265 26.6734 8.71296 25.616 8.71296C24.5586 8.71296 23.9581 9.19267 23.501 9.43383C23.1532 9.61436 22.8807 9.75261 22.3319 9.76962C22.2673 9.77162 22.2149 9.8157 22.2149 9.87019C22.2151 10.2449 22.2168 10.4558 22.2394 10.9781Z"
      fill="#8BB7C8"
    />
    <path
      d="M3.18666 14.9035C3.701 14.8071 4.04411 14.6142 4.32976 14.4696C4.70136 14.2766 4.98705 14.132 5.61581 14.132C6.24457 14.132 6.53048 14.2766 6.90186 14.4696C7.35919 14.7107 7.93075 15 8.98816 15C10.0456 15 10.6173 14.7107 11.0744 14.4696C11.4461 14.2766 11.7317 14.132 12.3605 14.132C12.9893 14.132 13.2752 14.2766 13.6466 14.4696C14.1039 14.7107 14.6754 15 15.7328 15C16.7903 15 17.362 14.7107 17.8191 14.4696C18.1907 14.2766 18.4764 14.132 19.1052 14.132C19.7339 14.132 20.0199 14.2766 20.3912 14.4696C20.8486 14.7107 21.4201 15 22.4775 15C23.5349 15 24.1067 14.7107 24.5638 14.4696C24.9354 14.2766 25.2211 14.132 25.8499 14.132C26.4786 14.132 26.7645 14.2766 27.1359 14.4696C27.4218 14.6142 27.7647 14.7831 28.2505 14.9035C28.7364 14.9998 29.2222 14.7106 29.2222 14.2766C29.2222 13.9873 28.965 13.7221 28.6219 13.6498C28.3648 13.6015 28.1646 13.4809 27.9074 13.3605C27.4501 13.1193 26.8786 12.83 25.8211 12.83C24.7637 12.83 24.192 13.1193 23.7348 13.3605C23.3632 13.5534 23.0776 13.698 22.4488 13.698C21.82 13.698 21.5341 13.5534 21.1627 13.3605C20.7054 13.1193 20.1339 12.83 19.0765 12.83C18.019 12.83 17.4473 13.1193 16.9902 13.3605C16.5901 13.5534 16.3329 13.698 15.7041 13.698C15.0753 13.698 14.7894 13.5534 14.4181 13.3605C13.9607 13.1193 13.3892 12.83 12.3318 12.83C11.2744 12.83 10.7026 13.1193 10.2455 13.3605C9.87387 13.5534 9.58818 13.698 8.95942 13.698C8.33066 13.698 8.04476 13.5534 7.67337 13.3605C7.21605 13.1193 6.64449 12.83 5.58708 12.83C4.58708 12.8299 3.98685 13.1193 3.52952 13.3603C3.30085 13.4809 3.1009 13.5773 2.84355 13.6256C2.55767 13.6979 2.30048 13.8908 2.24326 14.132C2.15774 14.6382 2.67213 15.0241 3.18666 14.9035Z"
      fill="#8BB7C8"
    />
  </svg>
);

export const TransportationIcon = (props: { className?: string }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M13.5675 6.00633H13.154L12.7109 2.3882C12.637 1.90086 12.2678 1.51692 11.7805 1.44306L10.2447 1.20675C9.34386 1.07386 8.44302 1 7.52741 1C6.6118 1 5.71098 1.07386 4.81012 1.20675L3.28902 1.44306C2.80168 1.51692 2.41774 1.91568 2.35862 2.40291L1.90084 6.00628H1.48734C1.22157 6.00628 1 6.22776 1 6.49362V7.9261C1 8.19187 1.22148 8.41345 1.48734 8.41345H1.66452V12.1645C1.66452 12.7404 2.09274 13.1982 2.65397 13.2573V14.5127C2.65397 14.7784 2.87545 15 3.14131 15H4.04215C4.30792 15 4.52949 14.7785 4.52949 14.5127V13.2722H10.5105V14.5127C10.5105 14.7784 10.732 15 10.9978 15H11.8987C12.1644 15 12.386 14.7785 12.386 14.5127V13.2573C12.9324 13.1983 13.3755 12.7257 13.3755 12.1645V8.41345H13.5526C13.8184 8.41345 14.04 8.19197 14.04 7.9261V6.49362C14.0694 6.22774 13.848 6.00628 13.5674 6.00628L13.5675 6.00633ZM4.07172 11.2785C3.61394 11.2785 3.24475 10.9093 3.24475 10.4515C3.24475 9.99371 3.61394 9.62452 4.07172 9.62452C4.5295 9.62452 4.8987 9.99371 4.8987 10.4515C4.8987 10.8944 4.5295 11.2785 4.07172 11.2785ZM10.9831 11.2785C10.5253 11.2785 10.1561 10.9093 10.1561 10.4515C10.1561 9.99371 10.5253 9.62452 10.9831 9.62452C11.4409 9.62452 11.8101 9.99371 11.8101 10.4515C11.8249 10.8944 11.4409 11.2785 10.9831 11.2785ZM2.90506 7.36498L3.46626 2.87554H11.6034L12.1646 7.36498H2.90506Z"
      fill="currentColor"
    />
  </svg>
);

export const InstructorsIcon = (props: { className?: string }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M3.39116 10.344C4.6599 10.344 5.78207 9.6365 6.36773 8.56304C6.95325 9.66093 8.07557 10.344 9.34431 10.344C11.1984 10.344 12.6868 8.83129 12.6868 6.97713C12.6868 5.12287 11.1741 3.61025 9.31991 3.61025L3.39128 3.61013C1.51272 3.61013 0 5.12284 0 6.97701C0 8.83114 1.51272 10.3439 3.39128 10.3439L3.39116 10.344Z"
      fill="currentColor"
    />
    <path
      d="M15.0432 10.5794V2.68356H15.1164C15.4579 2.68356 15.7507 2.39074 15.7507 2.04919V1.63437C15.7507 1.29283 15.4579 1 15.1164 1H13.3354C12.9939 1 12.701 1.29282 12.701 1.63437V2.04919C12.701 2.39073 12.9938 2.68356 13.3354 2.68356H13.5794V10.5794C13.5794 11.6529 12.701 12.5313 11.6276 12.5313H9.67574C8.6754 12.5313 7.94605 12.8894 7.82406 11.938H6.33579C6.45778 13.6947 7.87036 13.9951 9.65136 13.9951H11.6032C13.5306 13.9951 15.0432 12.458 15.0432 10.5794Z"
      fill="currentColor"
    />
    <path
      d="M8.03834 11.9242H4.62297C4.04127 11.9242 3.56808 12.3976 3.56808 12.9794V13.9449C3.56808 14.5268 4.04127 15 4.62297 15H8.03804C8.61998 15 9.0933 14.5268 9.0933 13.9449V12.9794C9.0933 12.3976 8.61998 11.9242 8.03828 11.9242H8.03834Z"
      fill="currentColor"
    />
  </svg>
);

export const ParticipantsIcon = (props: { className?: string }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.2499 7.63342C14.2499 8.82999 13.4105 9.80005 12.375 9.80005C11.3395 9.80005 10.5 8.82999 10.5 7.63342C10.5 6.43671 11.3395 5.46664 12.375 5.46664C13.4105 5.46664 14.2499 6.43671 14.2499 7.63342Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.24987 7.63342C5.24987 8.82999 4.41039 9.80005 3.3749 9.80005C2.33941 9.80005 1.49993 8.82999 1.49993 7.63342C1.49993 6.43671 2.33941 5.46664 3.3749 5.46664C4.41039 5.46664 5.24987 6.43671 5.24987 7.63342Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.5001 5.0334C10.5001 6.70863 9.32478 8.06665 7.87506 8.06665C6.42534 8.06665 5.25 6.70863 5.25 5.0334C5.25 3.35806 6.42534 2 7.87506 2C9.32478 2 10.5001 3.35803 10.5001 5.0334Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.7334 10.6665C13.2187 11.5418 13.5 12.5827 13.5 13.6999V14.5665C13.5 14.7187 13.4775 14.8643 13.4358 14.9999H15.375C15.4744 14.9999 15.5699 14.9544 15.6401 14.873C15.7106 14.7919 15.75 14.6815 15.75 14.5665V13.6999C15.75 12.0247 14.5748 10.6665 13.1249 10.6665H12.7334Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.37496 15C3.16792 15 2.99992 14.8059 2.99992 14.5666V13.7C2.99992 11.0675 4.84689 8.93342 7.12517 8.93342H8.62509C10.9032 8.93342 12.7503 11.0676 12.7503 13.7V14.5666C12.7503 14.8059 12.5823 15 12.3753 15H3.37496Z"
      fill="currentColor"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.01657 10.6665H2.62506C1.17525 10.6665 0 12.0247 0 13.6999V14.5665C0 14.6815 0.0394267 14.7919 0.109865 14.873C0.180053 14.9544 0.275602 14.9999 0.375046 14.9999H2.31418C2.27249 14.8643 2.25002 14.7187 2.25002 14.5665V13.6999C2.25002 12.5827 2.53127 11.5418 3.01657 10.6665Z"
      fill="currentColor"
    />
  </svg>
);

export const BoatEntry16x16Icon = (props: { className?: string }) => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M15.3355 8.77455L15.7608 7.29051H11.6146L10.5249 5.80648L10.7641 5H5.36871L5.10285 5.90326H5.79383L6.88358 7.35486L5.82056 8.74189H0.23916L0 9.48379L0.584717 12H13.4751C14.4319 12 15.2026 11.4194 15.495 10.4192C15.6811 9.80626 16 8.74171 16 8.74171L15.3355 8.77455ZM14.857 8.77455H14.2457L14.5116 7.87129H15.1228L14.857 8.77455ZM6.77725 5.93592H9.6212L10.6312 7.2908H7.78708L6.77725 5.93592ZM12.0397 7.87135H12.9433L12.6775 8.7424L12.3586 8.29079L12.0397 7.87135ZM13.156 8.77461L13.4218 7.87135H14.0066L13.7407 8.77461H13.156Z"
      fill="currentColor"
    />
  </svg>
);

export const DiveCenterIcon = (props: { className?: string }) => (
  <svg width="32" height="16" viewBox="0 0 32 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.0753 8.35274H4.52819C3.87272 8.35274 3.29005 7.93457 3.08016 7.3131C2.87085 6.6922 3.08062 6.00661 3.60227 5.60937L9.24275 1.31243C9.78986 0.895858 10.5478 0.895858 11.0949 1.31243L16.7353 5.60937C17.257 6.00661 17.4667 6.6922 17.2574 7.3131C17.0476 7.93457 16.4649 8.35274 15.8094 8.35274H15.2623V13.4719C15.2623 13.8774 15.1013 14.266 14.8146 14.5523C14.5283 14.839 14.1397 15 13.7342 15H6.60323C6.19778 15 5.80918 14.839 5.5229 14.5523C5.23615 14.266 5.07516 13.8773 5.07516 13.4719L5.0753 8.35274ZM8.13145 12.4532H12.2063C12.4875 12.4532 12.7157 12.225 12.7157 11.9439C12.7157 11.6627 12.4875 11.4345 12.2063 11.4345H8.13145C7.85028 11.4345 7.62209 11.6627 7.62209 11.9439C7.62209 12.225 7.85028 12.4532 8.13145 12.4532Z"
      fill="currentColor"
    />
    <path d="M18.5 15L20 14.9978V1H18.5V15Z" fill="currentColor" />
    <path d="M29.9859 8.08511V2.37004L22.1446 8.08511H29.9859Z" fill="currentColor" />
    <path d="M20.8184 1.00009V6.71517L28.6602 1.00009H20.8184Z" fill="currentColor" />
  </svg>
);

export const RestDayIcon = (props: { className?: string }) => {
  return (
    <svg width="63" height="70" viewBox="0 0 63 70" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
      <path
        d="M27.872 16.33V39.33C27.8625 39.6597 27.9199 39.9878 28.0408 40.2947C28.1617 40.6015 28.3435 40.8807 28.5753 41.1153C28.8071 41.3499 29.0841 41.5351 29.3895 41.6596C29.6948 41.7842 30.0223 41.8456 30.352 41.84H53.632C53.9087 41.8391 54.1825 41.8957 54.4363 42.006C54.69 42.1164 54.918 42.2782 55.106 42.4812C55.2939 42.6842 55.4378 42.924 55.5283 43.1854C55.6189 43.4469 55.6542 43.7242 55.632 44C54.232 62.12 35.532 75.38 16.032 67.28C11.0622 65.1769 6.91285 61.512 4.21204 56.84C-7.26796 37.12 6.35204 15.76 25.402 14.05C25.717 14.0237 26.0341 14.0631 26.333 14.1658C26.6319 14.2686 26.9063 14.4323 27.1385 14.6467C27.3708 14.8611 27.5559 15.1215 27.6822 15.4113C27.8085 15.701 27.8731 16.0139 27.872 16.33ZM62.712 3.48001V5.64001C62.71 6.49453 62.3973 7.31913 61.832 7.96002L45.352 26.48C45.2544 26.6039 45.1936 26.7528 45.1766 26.9096C45.1596 27.0664 45.1872 27.2248 45.256 27.3668C45.3249 27.5087 45.4323 27.6283 45.566 27.712C45.6997 27.7957 45.8543 27.8401 46.012 27.84H59.222C60.1476 27.84 61.0353 28.2077 61.6898 28.8622C62.3443 29.5167 62.712 30.4044 62.712 31.33C62.716 31.7904 62.6286 32.247 62.4549 32.6733C62.2811 33.0996 62.0245 33.4873 61.6999 33.8137C61.3753 34.1402 60.9892 34.399 60.5638 34.5752C60.1385 34.7513 59.6824 34.8414 59.222 34.84H38.352C37.8942 34.8413 37.4406 34.7523 37.0172 34.578C36.5939 34.4037 36.209 34.1476 35.8848 33.8243C35.5606 33.501 35.3034 33.1169 35.1279 32.6941C34.9524 32.2712 34.862 31.8179 34.862 31.36V29.2C34.846 28.3357 35.1485 27.4956 35.712 26.84L52.162 8.36001C52.2672 8.23862 52.3354 8.08963 52.3584 7.93069C52.3815 7.77174 52.3585 7.60952 52.2922 7.46323C52.2259 7.31695 52.1191 7.19274 51.9843 7.10532C51.8496 7.01791 51.6926 6.97095 51.532 6.97001H38.352C37.4264 6.97001 36.5387 6.60232 35.8842 5.94782C35.2297 5.29332 34.862 4.40562 34.862 3.48001C34.862 3.02217 34.9524 2.56883 35.1279 2.14596C35.3034 1.7231 35.5606 1.33902 35.8848 1.01574C36.209 0.692462 36.5939 0.436336 37.0172 0.262038C37.4406 0.087741 37.8942 -0.00130127 38.352 1.43691e-05H59.252C60.1715 0.00529884 61.0515 0.374272 61.6999 1.02633C62.3482 1.67838 62.7121 2.56052 62.712 3.48001Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const SignatureIcon = (props: { className?: string }) => {
  return (
    <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
      <path
        d="M5.87922 15.291C5.75298 15.1632 5.68219 14.9909 5.68219 14.8113C5.68219 14.6317 5.75298 14.4594 5.87922 14.3317C6.14782 14.0247 6.54655 13.5776 7.314 13.5776C8.08144 13.5776 8.49352 14.0397 8.76212 14.345C8.99569 14.6119 9.05242 14.652 9.19589 14.652C9.33937 14.652 9.3961 14.6119 9.62966 14.345C9.8966 14.0397 10.297 13.5776 11.0778 13.5776C11.8586 13.5776 12.259 14.0397 12.5259 14.345C12.7595 14.6119 12.8162 14.652 12.9597 14.652C13.1032 14.652 13.1599 14.6119 13.3935 14.345C13.4517 14.2784 13.5226 14.2239 13.6019 14.1847C13.6812 14.1455 13.7675 14.1222 13.8558 14.1163C13.9441 14.1105 14.0327 14.122 14.1166 14.1504C14.2004 14.1787 14.2778 14.2233 14.3444 14.2816C14.411 14.3399 14.4655 14.4107 14.5047 14.49C14.544 14.5694 14.5672 14.6556 14.5731 14.7439C14.579 14.8323 14.5674 14.9209 14.539 15.0047C14.5107 15.0885 14.4661 15.166 14.4078 15.2326C14.1409 15.5379 13.7405 16 12.9597 16C12.1789 16 11.7785 15.5379 11.5116 15.2326C11.278 14.9656 11.2213 14.9256 11.0778 14.9256C10.9343 14.9256 10.8776 14.9656 10.644 15.2326C10.3771 15.5379 9.97668 16 9.19756 16C8.41844 16 8.01303 15.5379 7.74109 15.2326C7.50752 14.9656 7.4508 14.9256 7.30732 14.9256C7.16384 14.9256 7.10712 14.9656 6.87355 15.2326L6.84519 15.2626L6.82517 15.2843C6.7636 15.347 6.69025 15.397 6.60932 15.4313C6.52839 15.4656 6.44148 15.4836 6.35358 15.4842C6.26568 15.4848 6.17853 15.468 6.09712 15.4349C6.01571 15.4017 5.94166 15.3528 5.87922 15.291ZM7.76111 11.6339L4.26926 13.4975C4.16124 13.5553 4.03983 13.5836 3.91736 13.5793C3.7949 13.575 3.67575 13.5384 3.57203 13.4731C3.46831 13.4079 3.38371 13.3163 3.32684 13.2078C3.26997 13.0992 3.24284 12.9776 3.24823 12.8552L3.41506 8.90118C3.42161 8.78266 3.45837 8.6678 3.52184 8.56751L8.72041 0.392596C8.76901 0.315442 8.83233 0.24862 8.90676 0.195947C8.98118 0.143275 9.06526 0.105785 9.15419 0.0856194C10.0517 -0.117355 10.993 0.0438803 11.7717 0.533965C12.5505 1.02405 13.1031 1.80298 13.3084 2.69992C13.3285 2.78888 13.3309 2.88093 13.3155 2.97082C13.3 3.06071 13.267 3.14667 13.2183 3.22378L8.01971 11.3987C7.95499 11.4979 7.86597 11.5789 7.76111 11.6339ZM6.93862 10.4944C6.77901 10.0658 6.48279 9.70156 6.09565 9.45794C5.70851 9.21432 5.25197 9.10485 4.79646 9.14642L4.73806 10.6913C5.07182 10.7608 5.36857 10.9501 5.57224 11.2235L6.93862 10.4944Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const RegistrationIcon = (props: { className?: string }) => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.8981 7.76785C12.1154 7.3915 12.5972 7.26238 12.9736 7.47968C13.3901 7.71982 13.9207 8.02688 14.3372 8.26702C14.7136 8.48432 14.8427 8.96617 14.6254 9.34252C13.8152 10.7463 11.9996 13.8902 11.569 14.6366C11.5083 14.7421 11.4233 14.8326 11.321 14.8995C11.0422 15.0838 10.4305 15.4877 9.85178 15.8695C9.60299 16.0341 9.28255 16.0435 9.0243 15.8947C8.76605 15.7451 8.61409 15.4633 8.63219 15.1656C8.67392 14.4728 8.71723 13.7421 8.7377 13.4083C8.74478 13.2863 8.78023 13.1674 8.84164 13.0619C9.27231 12.3155 11.0879 9.17167 11.8981 7.76785ZM7.11738 15.7467C7.0662 15.5286 7.04652 15.3011 7.0599 15.0712C7.10163 14.3791 7.14572 13.6477 7.16541 13.3138C7.18745 12.9477 7.29451 12.5918 7.47796 12.2746L10.5344 6.98051C11.1863 5.85069 12.6311 5.46411 13.7609 6.11602L13.8664 6.17665V2.36201C13.8664 1.05739 12.809 0 11.5044 0H3.63105C3.00433 0 2.40359 0.248796 1.96111 0.692066C1.51784 1.13455 1.26904 1.73529 1.26904 2.36201C1.26904 5.08855 1.26904 10.6582 1.26904 13.3847C1.26904 14.0114 1.51784 14.6122 1.96111 15.0546C2.40359 15.4979 3.00433 15.7467 3.63105 15.7467H7.11738ZM4.41838 11.0227H6.13083C6.56544 11.0227 6.91816 10.67 6.91816 10.2354C6.91816 9.80075 6.56544 9.44802 6.13083 9.44802H4.41838C3.98377 9.44802 3.63105 9.80075 3.63105 10.2354C3.63105 10.67 3.98377 11.0227 4.41838 11.0227ZM4.41838 7.87335H7.8433C8.27791 7.87335 8.63063 7.52063 8.63063 7.08602C8.63063 6.65141 8.27791 6.29868 7.8433 6.29868H4.41838C3.98377 6.29868 3.63105 6.65141 3.63105 7.08602C3.63105 7.52063 3.98377 7.87335 4.41838 7.87335ZM4.41838 4.72401H9.1424C9.577 4.72401 9.92973 4.37128 9.92973 3.93668C9.92973 3.50207 9.577 3.14934 9.1424 3.14934H4.41838C3.98377 3.14934 3.63105 3.50207 3.63105 3.93668C3.63105 4.37128 3.98377 4.72401 4.41838 4.72401Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const CheckDoneIcon = (props: { className?: string }) => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
      <path
        d="M0.523043 8.53731C0.233845 8.82878 0.234763 9.29922 0.525099 9.58956L5.06321 14.1277C5.35435 14.4188 5.82638 14.4188 6.11752 14.1277L15.4728 4.77233C15.764 4.48119 15.764 4.00916 15.4728 3.71803L14.059 2.30423C13.7682 2.01334 13.2966 2.01305 13.0054 2.30359L6.01038 9.28165C5.76768 9.52376 5.37473 9.52352 5.13232 9.28112L2.97745 7.12624C2.6855 6.8343 2.21188 6.83522 1.92108 7.1283L0.523043 8.53731Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const NotDoneIcon = (props: { className?: string }) => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
      <path
        d="M1.94244 3.99665C1.70759 3.76287 1.70673 3.38297 1.94051 3.14813L3.14074 1.94244C3.37452 1.70759 3.75442 1.70673 3.98927 1.94051L14.0119 11.9178C14.2468 12.1516 14.2476 12.5315 14.0138 12.7663L12.8136 13.972C12.5798 14.2068 12.1999 14.2077 11.9651 13.9739L1.94244 3.99665Z"
        fill="currentColor"
      />
      <path
        d="M11.9654 1.9405C12.2003 1.70672 12.5802 1.7076 12.814 1.94245L14.0141 3.14818C14.2479 3.38303 14.247 3.76293 14.0122 3.99671L3.98921 13.9736C3.75436 14.2074 3.37446 14.2065 3.14069 13.9717L1.9405 12.7659C1.70672 12.5311 1.7076 12.1512 1.94245 11.9174L11.9654 1.9405Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const PendingIcon = (props: { className?: string }) => {
  return (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
      <rect x="1.5" y="1.5" width="11" height="11" rx="5.5" stroke="white" />
      <mask id="path-2-outside-1_3913_126776" maskUnits="userSpaceOnUse" x="-0.00170898" y="0" width="15" height="14" fill="black">
        <rect fill="white" x="-0.00170898" width="15" height="14" />
        <path d="M11.7697 6.99821C11.7697 6.78429 11.757 6.57036 11.7316 6.35729C11.669 5.94415 11.5593 5.5392 11.4032 5.1515C11.3917 5.12292 11.38 5.09443 11.368 5.06605C11.3889 5.11534 11.4099 5.16462 11.4296 5.21268C11.4209 5.19223 11.4122 5.17183 11.4032 5.1515C11.2366 4.77103 11.0263 4.41181 10.7777 4.07925C10.5236 3.76065 10.2337 3.47192 9.9158 3.21805C9.58565 2.9716 9.22926 2.76286 8.85191 2.59706C8.46396 2.44121 8.05864 2.33008 7.6451 2.26697C7.43044 2.24165 7.21442 2.22903 6.99829 2.2291C6.78298 2.22917 6.56755 2.24184 6.35321 2.26711C5.94198 2.32983 5.5389 2.43927 5.15293 2.59464C5.12437 2.60614 5.09589 2.61789 5.06752 2.62989C5.11681 2.60895 5.1661 2.588 5.21416 2.56828C5.19369 2.57694 5.17328 2.58573 5.15293 2.59464C4.77246 2.7613 4.41325 2.9716 4.0807 3.22018C3.76209 3.47435 3.47335 3.76417 3.21949 4.08212C2.97305 4.41227 2.76431 4.76866 2.59851 5.146C2.44268 5.53394 2.33155 5.93925 2.26844 6.35278C2.24311 6.56757 2.23049 6.78373 2.23057 7C2.23066 7.21517 2.24333 7.43047 2.26859 7.64467C2.3313 8.05588 2.44074 8.45894 2.5961 8.8449C2.60761 8.87348 2.61936 8.90197 2.63137 8.93036C2.61042 8.88107 2.58947 8.83178 2.56976 8.78372C2.57841 8.80417 2.58719 8.82457 2.5961 8.8449C2.76275 9.22538 2.97306 9.5846 3.22164 9.91716C3.4758 10.2358 3.76561 10.5245 4.08355 10.7784C4.41371 11.0248 4.77011 11.2336 5.14746 11.3994C5.53541 11.5552 5.94072 11.6663 6.35425 11.7294C6.78276 11.78 7.21665 11.7799 7.64614 11.7293C8.05737 11.6666 8.46044 11.5571 8.84641 11.4018C8.87498 11.3903 8.90345 11.3785 8.93183 11.3665C8.88254 11.3875 8.83325 11.4084 8.78519 11.4281C8.80566 11.4195 8.82606 11.4107 8.84641 11.4018C9.22689 11.2351 9.58612 11.0248 9.91868 10.7762C10.2373 10.522 10.526 10.2322 10.7798 9.9143C11.0263 9.58414 11.235 9.22774 11.4008 8.85039C11.5573 8.4609 11.6687 8.05391 11.7317 7.63866C11.757 7.42577 11.7697 7.21201 11.7697 6.99821C11.7697 6.84049 11.8387 6.67411 11.9496 6.56199C12.0568 6.45478 12.2318 6.37468 12.3858 6.38208C12.5448 6.38947 12.7099 6.44123 12.822 6.56199C12.9329 6.68275 13.0032 6.83062 13.0019 6.99819C13.0007 7.61431 12.9083 8.24029 12.7173 8.82683C12.5324 9.39613 12.2675 9.9457 11.9188 10.4337C11.7339 10.6937 11.5331 10.9438 11.3113 11.173C11.0882 11.4034 10.8455 11.6067 10.5904 11.8014C10.1148 12.1662 9.58367 12.441 9.02302 12.648C8.44632 12.8599 7.82898 12.9708 7.21655 12.9955C6.59551 13.0201 5.95967 12.9437 5.36202 12.7737C4.78902 12.611 4.22959 12.3596 3.73176 12.0319C3.23886 11.7078 2.78416 11.3147 2.40834 10.86C2.20379 10.6111 2.01033 10.3511 1.84521 10.0751C1.67886 9.7978 1.54454 9.50576 1.42133 9.20632C1.18844 8.63826 1.06151 8.03324 1.01469 7.42203C0.966635 6.80221 1.02578 6.16637 1.17119 5.56259C1.31289 4.97974 1.54948 4.4092 1.86124 3.89659C2.16682 3.39385 2.55006 2.92558 2.98998 2.53496C3.4336 2.14188 3.93019 1.80301 4.47112 1.55903C4.76686 1.42472 5.07123 1.30642 5.3842 1.21893C5.70459 1.12898 6.03237 1.07476 6.3626 1.03656C6.98736 0.962624 7.62318 1.00452 8.23806 1.12898C8.82584 1.2485 9.4013 1.46908 9.925 1.76112C10.4388 2.04823 10.9182 2.4179 11.3248 2.84425C11.7352 3.27307 12.09 3.76596 12.3537 4.29707C12.6236 4.83925 12.822 5.41842 12.9132 6.01729C12.9625 6.34383 12.997 6.67037 12.997 7.00062C12.997 7.15835 12.928 7.32471 12.8171 7.43683C12.7099 7.54403 12.5349 7.62413 12.3809 7.61674C12.2219 7.60934 12.0568 7.55759 11.9446 7.43683C11.8399 7.3136 11.7697 7.16581 11.7697 6.99821Z" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.99829 1C3.68458 1 0.998291 3.68629 0.998291 7H2.23057C2.23049 6.78373 2.24311 6.56757 2.26844 6.35278C2.33155 5.93925 2.44268 5.53394 2.59851 5.146C2.76431 4.76866 2.97305 4.41227 3.21949 4.08212C3.47335 3.76417 3.76209 3.47435 4.0807 3.22018C4.41325 2.9716 4.77246 2.7613 5.15293 2.59464C5.5389 2.43927 5.94198 2.32983 6.35321 2.26711C6.56755 2.24184 6.78298 2.22917 6.99829 2.2291V1Z"
        />
        <path d="M11.7697 6.99821C11.7697 6.78429 11.757 6.57036 11.7316 6.35729C11.669 5.94415 11.5593 5.5392 11.4032 5.1515C11.2366 4.77103 11.0263 4.41181 10.7777 4.07925C10.5236 3.76065 10.2337 3.47192 9.9158 3.21805C9.58565 2.9716 9.22926 2.76286 8.85191 2.59706C8.46396 2.44121 8.05864 2.33008 7.6451 2.26697C7.43044 2.24165 7.21442 2.22903 6.99829 2.2291V7H2.23057C2.23066 7.21517 2.24333 7.43047 2.26859 7.64467C2.3313 8.05588 2.44074 8.45894 2.5961 8.8449C2.76275 9.22538 2.97306 9.5846 3.22164 9.91716C3.4758 10.2358 3.76561 10.5245 4.08355 10.7784C4.41371 11.0248 4.77011 11.2336 5.14746 11.3994C5.53541 11.5552 5.94072 11.6663 6.35425 11.7294C6.78276 11.78 7.21665 11.7799 7.64614 11.7293C8.05737 11.6666 8.46044 11.5571 8.84641 11.4018C9.22689 11.2351 9.58612 11.0248 9.91868 10.7762C10.2373 10.522 10.526 10.2322 10.7798 9.9143C11.0263 9.58414 11.235 9.22774 11.4008 8.85039C11.5573 8.4609 11.6687 8.05391 11.7317 7.63866C11.757 7.42577 11.7697 7.21201 11.7697 6.99821Z" />
      </mask>
      <path
        d="M11.7697 6.99821C11.7697 6.78429 11.757 6.57036 11.7316 6.35729C11.669 5.94415 11.5593 5.5392 11.4032 5.1515C11.3917 5.12292 11.38 5.09443 11.368 5.06605C11.3889 5.11534 11.4099 5.16462 11.4296 5.21268C11.4209 5.19223 11.4122 5.17183 11.4032 5.1515C11.2366 4.77103 11.0263 4.41181 10.7777 4.07925C10.5236 3.76065 10.2337 3.47192 9.9158 3.21805C9.58565 2.9716 9.22926 2.76286 8.85191 2.59706C8.46396 2.44121 8.05864 2.33008 7.6451 2.26697C7.43044 2.24165 7.21442 2.22903 6.99829 2.2291C6.78298 2.22917 6.56755 2.24184 6.35321 2.26711C5.94198 2.32983 5.5389 2.43927 5.15293 2.59464C5.12437 2.60614 5.09589 2.61789 5.06752 2.62989C5.11681 2.60895 5.1661 2.588 5.21416 2.56828C5.19369 2.57694 5.17328 2.58573 5.15293 2.59464C4.77246 2.7613 4.41325 2.9716 4.0807 3.22018C3.76209 3.47435 3.47335 3.76417 3.21949 4.08212C2.97305 4.41227 2.76431 4.76866 2.59851 5.146C2.44268 5.53394 2.33155 5.93925 2.26844 6.35278C2.24311 6.56757 2.23049 6.78373 2.23057 7C2.23066 7.21517 2.24333 7.43047 2.26859 7.64467C2.3313 8.05588 2.44074 8.45894 2.5961 8.8449C2.60761 8.87348 2.61936 8.90197 2.63137 8.93036C2.61042 8.88107 2.58947 8.83178 2.56976 8.78372C2.57841 8.80417 2.58719 8.82457 2.5961 8.8449C2.76275 9.22538 2.97306 9.5846 3.22164 9.91716C3.4758 10.2358 3.76561 10.5245 4.08355 10.7784C4.41371 11.0248 4.77011 11.2336 5.14746 11.3994C5.53541 11.5552 5.94072 11.6663 6.35425 11.7294C6.78276 11.78 7.21665 11.7799 7.64614 11.7293C8.05737 11.6666 8.46044 11.5571 8.84641 11.4018C8.87498 11.3903 8.90345 11.3785 8.93183 11.3665C8.88254 11.3875 8.83325 11.4084 8.78519 11.4281C8.80566 11.4195 8.82606 11.4107 8.84641 11.4018C9.22689 11.2351 9.58612 11.0248 9.91868 10.7762C10.2373 10.522 10.526 10.2322 10.7798 9.9143C11.0263 9.58414 11.235 9.22774 11.4008 8.85039C11.5573 8.4609 11.6687 8.05391 11.7317 7.63866C11.757 7.42577 11.7697 7.21201 11.7697 6.99821C11.7697 6.84049 11.8387 6.67411 11.9496 6.56199C12.0568 6.45478 12.2318 6.37468 12.3858 6.38208C12.5448 6.38947 12.7099 6.44123 12.822 6.56199C12.9329 6.68275 13.0032 6.83062 13.0019 6.99819C13.0007 7.61431 12.9083 8.24029 12.7173 8.82683C12.5324 9.39613 12.2675 9.9457 11.9188 10.4337C11.7339 10.6937 11.5331 10.9438 11.3113 11.173C11.0882 11.4034 10.8455 11.6067 10.5904 11.8014C10.1148 12.1662 9.58367 12.441 9.02302 12.648C8.44632 12.8599 7.82898 12.9708 7.21655 12.9955C6.59551 13.0201 5.95967 12.9437 5.36202 12.7737C4.78902 12.611 4.22959 12.3596 3.73176 12.0319C3.23886 11.7078 2.78416 11.3147 2.40834 10.86C2.20379 10.6111 2.01033 10.3511 1.84521 10.0751C1.67886 9.7978 1.54454 9.50576 1.42133 9.20632C1.18844 8.63826 1.06151 8.03324 1.01469 7.42203C0.966635 6.80221 1.02578 6.16637 1.17119 5.56259C1.31289 4.97974 1.54948 4.4092 1.86124 3.89659C2.16682 3.39385 2.55006 2.92558 2.98998 2.53496C3.4336 2.14188 3.93019 1.80301 4.47112 1.55903C4.76686 1.42472 5.07123 1.30642 5.3842 1.21893C5.70459 1.12898 6.03237 1.07476 6.3626 1.03656C6.98736 0.962624 7.62318 1.00452 8.23806 1.12898C8.82584 1.2485 9.4013 1.46908 9.925 1.76112C10.4388 2.04823 10.9182 2.4179 11.3248 2.84425C11.7352 3.27307 12.09 3.76596 12.3537 4.29707C12.6236 4.83925 12.822 5.41842 12.9132 6.01729C12.9625 6.34383 12.997 6.67037 12.997 7.00062C12.997 7.15835 12.928 7.32471 12.8171 7.43683C12.7099 7.54403 12.5349 7.62413 12.3809 7.61674C12.2219 7.60934 12.0568 7.55759 11.9446 7.43683C11.8399 7.3136 11.7697 7.16581 11.7697 6.99821Z"
        fill="#FF7557"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.99829 1C3.68458 1 0.998291 3.68629 0.998291 7H2.23057C2.23049 6.78373 2.24311 6.56757 2.26844 6.35278C2.33155 5.93925 2.44268 5.53394 2.59851 5.146C2.76431 4.76866 2.97305 4.41227 3.21949 4.08212C3.47335 3.76417 3.76209 3.47435 4.0807 3.22018C4.41325 2.9716 4.77246 2.7613 5.15293 2.59464C5.5389 2.43927 5.94198 2.32983 6.35321 2.26711C6.56755 2.24184 6.78298 2.22917 6.99829 2.2291V1Z"
        fill="#FF7557"
      />
      <path
        d="M11.7697 6.99821C11.7697 6.78429 11.757 6.57036 11.7316 6.35729C11.669 5.94415 11.5593 5.5392 11.4032 5.1515C11.2366 4.77103 11.0263 4.41181 10.7777 4.07925C10.5236 3.76065 10.2337 3.47192 9.9158 3.21805C9.58565 2.9716 9.22926 2.76286 8.85191 2.59706C8.46396 2.44121 8.05864 2.33008 7.6451 2.26697C7.43044 2.24165 7.21442 2.22903 6.99829 2.2291V7H2.23057C2.23066 7.21517 2.24333 7.43047 2.26859 7.64467C2.3313 8.05588 2.44074 8.45894 2.5961 8.8449C2.76275 9.22538 2.97306 9.5846 3.22164 9.91716C3.4758 10.2358 3.76561 10.5245 4.08355 10.7784C4.41371 11.0248 4.77011 11.2336 5.14746 11.3994C5.53541 11.5552 5.94072 11.6663 6.35425 11.7294C6.78276 11.78 7.21665 11.7799 7.64614 11.7293C8.05737 11.6666 8.46044 11.5571 8.84641 11.4018C9.22689 11.2351 9.58612 11.0248 9.91868 10.7762C10.2373 10.522 10.526 10.2322 10.7798 9.9143C11.0263 9.58414 11.235 9.22774 11.4008 8.85039C11.5573 8.4609 11.6687 8.05391 11.7317 7.63866C11.757 7.42577 11.7697 7.21201 11.7697 6.99821Z"
        fill="#FF7557"
      />
      <path
        d="M2.23057 7L1.73057 7.0002L1.73057 7.0002L2.23057 7ZM2.59851 5.146L2.13779 4.95156L2.13455 4.95963L2.59851 5.146ZM4.0807 3.22018L3.77416 2.82517L3.77416 2.82517L4.0807 3.22018ZM5.15293 2.59464L4.96622 2.13081L4.95923 2.13363L4.95232 2.13665L5.15293 2.59464ZM6.99829 2.2291L6.99846 2.7291L6.99846 2.7291L6.99829 2.2291ZM11.4032 5.1515L11.8671 4.96479L11.8643 4.9578L11.8612 4.9509L11.4032 5.1515ZM2.5961 8.8449L2.13227 9.03161L2.13508 9.03861L2.13811 9.04551L2.5961 8.8449ZM8.84641 11.4018L9.03312 11.8656L9.04011 11.8628L9.04701 11.8598L8.84641 11.4018ZM2.73057 6.99979C2.73049 6.80311 2.74197 6.60658 2.765 6.41134L1.77189 6.29421C1.74424 6.52857 1.73048 6.76434 1.73057 7.0002L2.73057 6.99979ZM2.76272 6.42821C2.81998 6.05299 2.92087 5.68489 3.06248 5.33237L2.13455 4.95963C1.96449 5.38299 1.84312 5.8255 1.77417 6.27734L2.76272 6.42821ZM3.05628 5.34713C3.20652 5.00518 3.39596 4.68158 3.62017 4.38121L2.81881 3.78303C2.55013 4.14296 2.3221 4.53214 2.14075 4.94487L3.05628 5.34713ZM3.61022 4.3941C3.84123 4.10477 4.10359 3.84152 4.39251 3.61104L3.76889 2.82931C3.42059 3.10717 3.10548 3.42356 2.82875 3.77015L3.61022 4.3941ZM4.38005 3.62066C4.68261 3.39451 5.00878 3.20365 5.35354 3.05263L4.95232 2.13665C4.53615 2.31894 4.14389 2.5487 3.78135 2.81969L4.38005 3.62066ZM5.33964 3.05847C5.68922 2.91775 6.0549 2.81839 6.42859 2.7614L6.27783 1.77283C5.82906 1.84126 5.38859 1.96079 4.96622 2.13081L5.33964 3.05847ZM6.41175 2.76367C6.6067 2.74069 6.80265 2.72917 6.99846 2.7291L6.99812 1.7291C6.76331 1.72918 6.52839 1.743 6.29466 1.77055L6.41175 2.76367ZM6.29466 1.77055C6.26806 1.77369 6.24146 1.77701 6.21489 1.7805L6.34521 2.77197C6.36737 2.76906 6.38956 2.76629 6.41175 2.76367L6.29466 1.77055ZM6.37684 1.75858C6.34379 1.76305 6.31079 1.7678 6.27783 1.77283L6.42859 2.7614C6.45603 2.75721 6.48351 2.75326 6.51103 2.74953L6.37684 1.75858ZM4.32754 3.66045C4.33009 3.6585 4.3721 3.62694 4.38724 3.61519L3.77416 2.82517C3.75185 2.84248 3.75555 2.83897 3.72009 2.86609L4.32754 3.66045ZM4.38724 3.61519C4.42929 3.58256 4.42373 3.58595 4.45199 3.56434L3.84455 2.76998C3.82703 2.78337 3.77723 2.82279 3.77416 2.82517L4.38724 3.61519ZM3.56581 4.45052C3.58847 4.4209 3.57956 4.43368 3.61449 4.38868L2.82449 3.77557C2.8183 3.78355 2.81199 3.79146 2.80257 3.80331C2.79397 3.81414 2.78291 3.82809 2.77145 3.84307L3.56581 4.45052ZM3.61449 4.38868C3.62652 4.37318 3.6601 4.32844 3.66192 4.32606L2.86756 3.71862C2.83884 3.75618 2.843 3.75171 2.82449 3.77557L3.61449 4.38868ZM3.05917 5.34041C3.07172 5.31068 3.08537 5.27674 3.09395 5.25582L2.16878 4.87627C2.15579 4.90794 2.14796 4.92766 2.13786 4.95159L3.05917 5.34041ZM2.75094 6.51002C2.7525 6.49859 2.75426 6.4867 2.75658 6.47105C2.75875 6.45643 2.76143 6.43833 2.76396 6.41959L1.77293 6.28596C1.7714 6.29725 1.76966 6.30914 1.76739 6.32444C1.76528 6.3387 1.7626 6.35663 1.76011 6.3749L2.75094 6.51002ZM2.76396 6.41959C2.76623 6.40278 2.76816 6.3865 2.76954 6.3749C2.77109 6.36198 2.7721 6.35365 2.77312 6.34613L1.78229 6.21102C1.77997 6.22802 1.77802 6.24439 1.77661 6.25624C1.77503 6.26941 1.774 6.27797 1.77293 6.28596L2.76396 6.41959ZM12.2697 6.99821C12.2697 6.76449 12.2558 6.5308 12.2281 6.29812L11.2351 6.41647C11.2582 6.60992 11.2697 6.80408 11.2697 6.99821H12.2697ZM12.226 6.28242C12.1577 5.83158 12.0379 5.38905 11.8671 4.96479L10.9394 5.33821C11.0808 5.68935 11.1804 6.05673 11.2372 6.43216L12.226 6.28242ZM11.8612 4.9509C11.679 4.53472 11.4492 4.14245 11.1782 3.7799L10.3772 4.3786C10.6034 4.68117 10.7942 5.00734 10.9453 5.35211L11.8612 4.9509ZM11.1686 3.76745C10.8907 3.41915 10.5744 3.10405 10.2278 2.82732L9.60382 3.60878C9.89314 3.83978 10.1564 4.10214 10.3869 4.39106L11.1686 3.76745ZM10.2149 2.81738C9.85496 2.54869 9.46579 2.32065 9.05305 2.1393L8.65078 3.05482C8.99274 3.20507 9.31634 3.39451 9.61671 3.61873L10.2149 2.81738ZM9.03829 2.13309C8.61492 1.96302 8.17239 1.84165 7.72053 1.77269L7.56967 2.76125C7.9449 2.81851 8.313 2.91941 8.66554 3.06102L9.03829 2.13309ZM7.70367 1.77041C7.46946 1.74279 7.23383 1.72902 6.99812 1.7291L6.99846 2.7291C7.19502 2.72903 7.39142 2.74052 7.58653 2.76353L7.70367 1.77041ZM1.73057 7.0002C1.73067 7.23487 1.74449 7.46964 1.77203 7.70321L2.76515 7.58613C2.74218 7.3913 2.73065 7.19548 2.73057 6.99979L1.73057 7.0002ZM1.7743 7.72005C1.84274 8.1688 1.96226 8.60926 2.13227 9.03161L3.05993 8.6582C2.91922 8.30863 2.81986 7.94297 2.76287 7.56929L1.7743 7.72005ZM2.13811 9.04551C2.3204 9.46169 2.55016 9.85396 2.82115 10.2165L3.62212 9.61781C3.39597 9.31524 3.20511 8.98907 3.05409 8.6443L2.13811 9.04551ZM2.83077 10.229C3.10862 10.5773 3.425 10.8924 3.77158 11.1691L4.39553 10.3876C4.10622 10.1566 3.84298 9.89426 3.61251 9.60535L2.83077 10.229ZM3.78446 11.179C4.1444 11.4477 4.53358 11.6758 4.94633 11.8571L5.34859 10.9416C5.00663 10.7913 4.68302 10.6019 4.38265 10.3777L3.78446 11.179ZM4.96109 11.8633C5.38445 12.0334 5.82698 12.1548 6.27882 12.2237L6.42969 11.2352C6.05446 11.1779 5.68636 11.077 5.33384 10.9354L4.96109 11.8633ZM6.29569 12.226C6.76319 12.2811 7.23638 12.2811 7.70468 12.2258L7.5876 11.2327C7.19692 11.2788 6.80233 11.2788 6.41282 11.2329L6.29569 12.226ZM7.72152 12.2236C8.17028 12.1551 8.61075 12.0356 9.03312 11.8656L8.6597 10.9379C8.31012 11.0787 7.94445 11.178 7.57076 11.235L7.72152 12.2236ZM9.04701 11.8598C9.4632 11.6775 9.85548 11.4477 10.218 11.1767L9.61932 10.3757C9.31675 10.6019 8.99058 10.7928 8.6458 10.9438L9.04701 11.8598ZM10.2305 11.1671C10.5788 10.8892 10.8939 10.5728 11.1706 10.2263L10.3891 9.60233C10.1581 9.89163 9.89577 10.1549 9.60687 10.3853L10.2305 11.1671ZM11.1805 10.2134C11.4492 9.85345 11.6773 9.46426 11.8586 9.05151L10.9431 8.64926C10.7928 8.99122 10.6034 9.31484 10.3792 9.61521L11.1805 10.2134ZM11.8648 9.03676C12.0355 8.61171 12.1572 8.16735 12.226 7.71363L11.2373 7.56369C11.1802 7.94046 11.079 8.31009 10.9369 8.66401L11.8648 9.03676ZM12.2282 7.6978C12.2558 7.46529 12.2697 7.23179 12.2697 6.99821H11.2697C11.2697 7.19222 11.2582 7.38624 11.2352 7.57953L12.2282 7.6978ZM12.2172 7.78425C12.2211 7.75545 12.2247 7.72664 12.2282 7.6978L11.2352 7.57953C11.2323 7.6035 11.2293 7.62746 11.2261 7.65141L12.2172 7.78425ZM11.2484 7.48638C11.2469 7.49719 11.2453 7.50843 11.2431 7.52322C11.2411 7.53704 11.2385 7.55415 11.2361 7.57185L12.2272 7.70547C12.2286 7.69481 12.2303 7.68358 12.2324 7.66914C12.2344 7.65569 12.2369 7.63875 12.2392 7.6215L11.2484 7.48638ZM11.8289 9.12404C11.8411 9.09504 11.8531 9.06594 11.8648 9.03676L10.9369 8.66401C10.9271 8.68831 10.9172 8.71253 10.907 8.73667L11.8289 9.12404ZM11.1312 10.2785C11.1478 10.2569 11.1642 10.2352 11.1805 10.2134L10.3792 9.61521C10.3656 9.63342 10.3518 9.65154 10.338 9.66959L11.1312 10.2785ZM11.8586 9.05151C11.8692 9.02729 11.8797 9.00299 11.8901 8.9786L10.9691 8.58884C10.9606 8.60904 10.9519 8.62918 10.9431 8.64926L11.8586 9.05151ZM10.4335 9.54588C10.4109 9.57548 10.4198 9.56277 10.3848 9.60775L11.1748 10.2209C11.181 10.2129 11.1873 10.205 11.1968 10.1931C11.2054 10.1823 11.2164 10.1683 11.2279 10.1533L10.4335 9.54588ZM10.1566 11.2251C10.1814 11.2059 10.206 11.1866 10.2305 11.1671L9.60687 10.3853C9.58657 10.4015 9.56614 10.4176 9.54559 10.4334L10.1566 11.2251ZM9.67181 10.336C9.66924 10.3379 9.62729 10.3694 9.61213 10.3812L10.2252 11.1712C10.2475 11.1539 10.2438 11.1574 10.2793 11.1303L9.67181 10.336ZM7.62251 12.2378C7.65556 12.2334 7.68856 12.2286 7.72152 12.2236L7.57076 11.235C7.54332 11.2392 7.51584 11.2431 7.48832 11.2469L7.62251 12.2378ZM7.65174 11.2248C7.64448 11.2257 7.6364 11.2267 7.62364 11.2282C7.61222 11.2296 7.59605 11.2315 7.57932 11.2338L7.71296 12.2248C7.72069 12.2238 7.729 12.2228 7.74201 12.2212C7.75367 12.2198 7.76994 12.2179 7.78686 12.2156L7.65174 11.2248ZM6.21472 12.2159C6.24169 12.2194 6.26868 12.2228 6.29569 12.226L6.41282 11.2329C6.39032 11.2302 6.36783 11.2274 6.34537 11.2245L6.21472 12.2159ZM4.87383 11.8275C4.90283 11.8397 4.93191 11.8516 4.96109 11.8633L5.33384 10.9354C5.30955 10.9256 5.28534 10.9157 5.26121 10.9055L4.87383 11.8275ZM6.27882 12.2237C6.31144 12.2287 6.34411 12.2334 6.37683 12.2378L6.51104 11.2469C6.48389 11.2432 6.45677 11.2393 6.42969 11.2352L6.27882 12.2237ZM5.40973 10.968C5.40095 10.9642 5.392 10.9603 5.38028 10.9552C5.36933 10.9505 5.35587 10.9446 5.34188 10.9387L4.95305 11.86C4.99253 11.8767 4.96693 11.8663 5.01859 11.8883L5.40973 10.968ZM3.71934 11.1297C3.74094 11.1463 3.76265 11.1627 3.78446 11.179L4.38265 10.3777C4.36444 10.3641 4.34632 10.3504 4.32829 10.3365L3.71934 11.1297ZM4.452 10.4321C4.4224 10.4094 4.43509 10.4183 4.39011 10.3834L3.77699 11.1733C3.78498 11.1795 3.7929 11.1859 3.80476 11.1953C3.8156 11.2039 3.82956 11.215 3.84454 11.2264L4.452 10.4321ZM2.77281 10.1551C2.79194 10.1799 2.81126 10.2045 2.83077 10.229L3.61251 9.60535C3.59632 9.58507 3.5803 9.56465 3.56445 9.54411L2.77281 10.1551ZM3.66192 9.67033C3.65998 9.66779 3.6284 9.62576 3.61665 9.61062L2.82663 10.2237C2.84396 10.246 2.84043 10.2423 2.86757 10.2778L3.66192 9.67033ZM1.76005 7.62104C1.76453 7.65409 1.76928 7.68709 1.7743 7.72005L2.76287 7.56929C2.75869 7.54185 2.75473 7.51437 2.75101 7.48685L1.76005 7.62104ZM2.77312 7.65027C2.77213 7.64301 2.77115 7.63493 2.76963 7.62216C2.76827 7.61075 2.76636 7.59457 2.7641 7.57785L1.77307 7.71149C1.77411 7.71922 1.77511 7.72753 1.77666 7.74054C1.77805 7.7522 1.77999 7.76847 1.78229 7.78539L2.77312 7.65027ZM9.12551 2.16893C9.09653 2.15675 9.06745 2.14481 9.03829 2.13309L8.66554 3.06102C8.68981 3.07077 8.71402 3.08072 8.73814 3.09086L9.12551 2.16893ZM7.72053 1.77269C7.68791 1.76771 7.65524 1.76301 7.62252 1.75858L7.48831 2.74953C7.51547 2.75321 7.54258 2.75711 7.56967 2.76125L7.72053 1.77269ZM8.58962 3.02845C8.5984 3.03218 8.60736 3.03608 8.61908 3.04118C8.63004 3.04595 8.6435 3.0518 8.65749 3.05771L9.04633 2.1364C9.00679 2.11971 9.03248 2.13009 8.98076 2.10812L8.58962 3.02845ZM10.28 2.86667C10.2584 2.85009 10.2367 2.83366 10.2149 2.81738L9.61671 3.61873C9.63491 3.63232 9.65303 3.64603 9.67106 3.65988L10.28 2.86667ZM9.54735 3.56433C9.57694 3.58696 9.56428 3.57815 9.60924 3.61305L10.2224 2.82306C10.2144 2.81686 10.2065 2.81054 10.1946 2.80112C10.1838 2.79251 10.1698 2.78144 10.1548 2.76998L9.54735 3.56433ZM11.2265 3.8413C11.2074 3.81652 11.1881 3.7919 11.1686 3.76745L10.3869 4.39106C10.403 4.41134 10.419 4.43175 10.4349 4.45229L11.2265 3.8413ZM11.1782 3.7799C11.1627 3.75913 11.147 3.73845 11.1312 3.71787L10.338 4.32682C10.3512 4.344 10.3643 4.36126 10.3772 4.3786L11.1782 3.7799ZM12.2281 6.29812C12.2247 6.26945 12.221 6.2408 12.2172 6.21216L11.2261 6.34498C11.2293 6.3688 11.2323 6.39263 11.2351 6.41647L12.2281 6.29812ZM12.2393 6.37536C12.2351 6.34434 12.2307 6.31336 12.226 6.28242L11.2372 6.43216C11.2411 6.45793 11.2448 6.48372 11.2483 6.50956L12.2393 6.37536ZM7.78463 1.78052C7.75766 1.77697 7.73067 1.7736 7.70367 1.77041L7.58653 2.76353C7.60903 2.76618 7.63151 2.76899 7.65397 2.77195L7.78463 1.78052ZM11.368 5.06605L12.2883 4.6749L10.447 5.4556L11.368 5.06605ZM11.4296 5.21268L10.5087 5.60241L12.3548 4.83312L11.4296 5.21268ZM5.06752 2.62989L4.67638 1.70956L5.45708 3.5509L5.06752 2.62989ZM5.21416 2.56828L5.60389 3.48921L4.8346 1.64311L5.21416 2.56828ZM2.63137 8.93036L1.71104 9.3215L3.55237 8.5408L2.63137 8.93036ZM2.56976 8.78372L3.49068 8.39399L1.64459 9.16328L2.56976 8.78372ZM8.93183 11.3665L9.32297 12.2868L8.54227 10.4455L8.93183 11.3665ZM8.78519 11.4281L8.39546 10.5072L9.16475 12.3533L8.78519 11.4281ZM11.9496 6.56199L11.2425 5.85487L11.2386 5.85875L11.9496 6.56199ZM12.3858 6.38208L12.3379 7.38093L12.3393 7.381L12.3858 6.38208ZM12.822 6.56199L13.5586 5.88557L13.5548 5.88158L12.822 6.56199ZM13.0019 6.99819L12.0019 6.99083L12.0019 6.99619L13.0019 6.99819ZM12.7173 8.82683L11.7664 8.51719L11.7661 8.51803L12.7173 8.82683ZM11.9188 10.4337L11.1052 9.85221L11.1037 9.85425L11.9188 10.4337ZM11.3113 11.173L12.0298 11.8685L12.0298 11.8685L11.3113 11.173ZM10.5904 11.8014L9.98365 11.0066L9.98188 11.0079L10.5904 11.8014ZM9.02302 12.648L9.36798 13.5866L9.36939 13.5861L9.02302 12.648ZM7.21655 12.9955L7.2562 13.9947L7.25677 13.9947L7.21655 12.9955ZM5.36202 12.7737L5.63569 11.8118L5.6351 11.8117L5.36202 12.7737ZM3.73176 12.0319L4.28168 11.1966L4.28115 11.1963L3.73176 12.0319ZM2.40834 10.86L1.63574 11.4949L1.63754 11.4971L2.40834 10.86ZM1.84521 10.0751L2.70339 9.5617L2.70271 9.56057L1.84521 10.0751ZM1.42133 9.20632L0.496075 9.58566L0.496559 9.58684L1.42133 9.20632ZM1.01469 7.42203L2.01177 7.34565L2.0117 7.34473L1.01469 7.42203ZM1.17119 5.56259L0.199492 5.32634L0.198981 5.32846L1.17119 5.56259ZM1.86124 3.89659L2.71564 4.41621L2.71577 4.416L1.86124 3.89659ZM2.98998 2.53496L2.32679 1.7865L2.32601 1.78719L2.98998 2.53496ZM4.47112 1.55903L4.88227 2.4706L4.88464 2.46953L4.47112 1.55903ZM5.3842 1.21893L5.65343 2.18201L5.65451 2.1817L5.3842 1.21893ZM6.3626 1.03656L6.47752 2.02994L6.48012 2.02963L6.3626 1.03656ZM8.23806 1.12898L8.43734 0.149032L8.43645 0.148852L8.23806 1.12898ZM9.925 1.76112L10.4128 0.888146L10.412 0.887743L9.925 1.76112ZM11.3248 2.84425L10.6012 3.53443L10.6023 3.53562L11.3248 2.84425ZM12.3537 4.29707L11.4581 4.74178L11.4585 4.74268L12.3537 4.29707ZM12.9132 6.01729L13.902 5.86803L13.9018 5.86675L12.9132 6.01729ZM12.8171 7.43683L13.5242 8.14395L13.528 8.14006L12.8171 7.43683ZM12.3809 7.61674L12.4288 6.61788L12.4273 6.61782L12.3809 7.61674ZM11.9446 7.43683L11.1827 8.08447L11.1969 8.10117L11.2118 8.11724L11.9446 7.43683ZM6.99829 1H7.99829V1.71337e-07H6.99829V1ZM6.99829 7V8H7.99829V7H6.99829ZM0.998291 7H-0.00170898V8H0.998291V7ZM5.15293 2.59464L5.52636 3.5223L5.54034 3.51667L5.55415 3.51063L5.15293 2.59464ZM11.4032 5.1515L10.4756 5.52492L10.4812 5.5389L10.4873 5.55271L11.4032 5.1515ZM2.5961 8.8449L3.52377 8.47149L3.51814 8.4575L3.51209 8.44369L2.5961 8.8449ZM8.84641 11.4018L8.47298 10.4741L8.459 10.4797L8.44519 10.4858L8.84641 11.4018ZM10.4477 5.45719C10.4691 5.50757 10.4875 5.551 10.5044 5.59224L12.3548 4.83312C12.3322 4.77825 12.3088 4.7231 12.2883 4.6749L10.4477 5.45719ZM5.45866 3.55022C5.50907 3.5288 5.55248 3.51036 5.59371 3.49345L4.8346 1.64311C4.77972 1.66563 4.72455 1.68909 4.67638 1.70956L5.45866 3.55022ZM3.5517 8.53921C3.53029 8.48883 3.51184 8.4454 3.49492 8.40416L1.64459 9.16328C1.6671 9.21815 1.69055 9.2733 1.71104 9.3215L3.5517 8.53921ZM8.54069 10.4462C8.49029 10.4676 8.44687 10.486 8.40563 10.503L9.16475 12.3533C9.21963 12.3308 9.27479 12.3073 9.32297 12.2868L8.54069 10.4462ZM12.7697 6.99821C12.7697 7.06194 12.7566 7.11023 12.7423 7.14477C12.728 7.17933 12.7038 7.22153 12.6606 7.26522L11.2386 5.85875C10.9443 6.15629 10.7697 6.57465 10.7697 6.99821H12.7697ZM12.6567 7.26909C12.6119 7.31388 12.5725 7.3361 12.5416 7.34925C12.5125 7.36162 12.4437 7.38601 12.3379 7.38093L12.4338 5.38323C11.9512 5.36007 11.5132 5.5842 11.2425 5.85488L12.6567 7.26909ZM12.3393 7.381C12.33 7.38056 12.3028 7.37838 12.262 7.36267C12.2193 7.34621 12.153 7.31114 12.0892 7.24239L13.5548 5.88158C13.2045 5.50419 12.7491 5.39789 12.4323 5.38316L12.3393 7.381ZM12.0855 7.23839C12.0751 7.22704 12.0538 7.20055 12.0348 7.15622C12.0148 7.10968 12.0015 7.05242 12.0019 6.99083L14.0019 7.00554C14.0054 6.53456 13.8032 6.15197 13.5585 5.88558L12.0855 7.23839ZM12.0019 6.99619C12.0009 7.51606 11.9226 8.03753 11.7664 8.51719L13.6681 9.13647C13.8939 8.44304 14.0005 7.71257 14.0019 7.00019L12.0019 6.99619ZM11.7661 8.51803C11.609 9.00191 11.3875 9.45721 11.1052 9.85221L12.7324 11.0151C13.1475 10.4342 13.4558 9.79035 13.6684 9.13563L11.7661 8.51803ZM11.1037 9.85425C10.9411 10.083 10.772 10.2923 10.5927 10.4775L12.0298 11.8685C12.2942 11.5953 12.5268 11.3043 12.7338 11.0131L11.1037 9.85425ZM10.5927 10.4775C10.4168 10.6593 10.2163 10.829 9.98365 11.0066L11.1972 12.5963C11.4747 12.3845 11.7597 12.1476 12.0298 11.8685L10.5927 10.4775ZM9.98188 11.0079C9.5994 11.3012 9.1606 11.5312 8.67665 11.7099L9.36939 13.5861C10.0067 13.3508 10.6302 13.0312 11.199 12.595L9.98188 11.0079ZM8.67806 11.7094C8.2106 11.8812 7.69756 11.9753 7.17633 11.9963L7.25677 13.9947C7.96041 13.9663 8.68205 13.8387 9.36798 13.5866L8.67806 11.7094ZM7.1769 11.9963C6.66217 12.0167 6.13206 11.9531 5.63569 11.8118L5.08835 13.7355C5.78728 13.9344 6.52886 14.0236 7.2562 13.9947L7.1769 11.9963ZM5.6351 11.8117C5.15558 11.6756 4.69022 11.4656 4.28168 11.1966L3.18184 12.8671C3.76896 13.2536 4.42247 13.5465 5.08894 13.7357L5.6351 11.8117ZM4.28115 11.1963C3.86092 10.92 3.48384 10.5916 3.17914 10.2229L1.63754 11.4971C2.08448 12.0378 2.61681 12.4956 3.18237 12.8674L4.28115 11.1963ZM3.18094 10.2251C2.99569 9.99968 2.83477 9.78134 2.70339 9.5617L0.987041 10.5884C1.18589 10.9208 1.41189 11.2225 1.63575 11.4949L3.18094 10.2251ZM2.70271 9.56057C2.57255 9.34364 2.45999 9.10258 2.34611 8.82581L0.496559 9.58684C0.629092 9.90893 0.785169 10.252 0.987714 10.5895L2.70271 9.56057ZM2.34659 8.82699C2.15972 8.37117 2.05202 7.87105 2.01177 7.34565L0.0176134 7.49841C0.0710095 8.19544 0.217161 8.90535 0.496076 9.58566L2.34659 8.82699ZM2.0117 7.34473C1.97243 6.83824 2.02035 6.30765 2.14339 5.79672L0.198981 5.32846C0.0312178 6.02508 -0.0391603 6.76617 0.0176843 7.49933L2.0117 7.34473ZM2.14288 5.79884C2.26019 5.31635 2.45708 4.84134 2.71564 4.41621L1.00684 3.37697C0.641883 3.97706 0.365604 4.64313 0.199494 5.32634L2.14288 5.79884ZM2.71577 4.416C2.97304 3.99274 3.2934 3.60285 3.65394 3.28272L2.32601 1.78719C1.80671 2.2483 1.36061 2.79495 1.00672 3.37718L2.71577 4.416ZM3.65316 3.28341C4.03439 2.94562 4.4476 2.66665 4.88227 2.4706L4.05997 0.647466C3.41277 0.939376 2.8328 1.33814 2.32679 1.7865L3.65316 3.28341ZM4.88464 2.46953C5.14757 2.35012 5.4026 2.25213 5.65343 2.18201L5.11497 0.255855C4.73985 0.360721 4.38615 0.499324 4.05761 0.648536L4.88464 2.46953ZM5.65451 2.1817C5.90624 2.11103 6.1776 2.06463 6.47752 2.02993L6.24769 0.0431818C5.88714 0.0848901 5.50294 0.146925 5.11389 0.256158L5.65451 2.1817ZM6.48012 2.02963C6.98348 1.97006 7.51159 2.00221 8.03968 2.1091L8.43645 0.148852C7.73476 0.00682563 6.99124 -0.0448125 6.24508 0.0434868L6.48012 2.02963ZM8.03879 2.10892C8.51823 2.20642 8.99718 2.3887 9.43795 2.6345L10.412 0.887743C9.80542 0.549459 9.13346 0.290592 8.43734 0.149032L8.03879 2.10892ZM9.43723 2.6341C9.86513 2.87318 10.2647 3.18159 10.6012 3.53443L12.0485 2.15408C11.5717 1.6542 11.0126 1.22328 10.4128 0.888146L9.43723 2.6341ZM10.6023 3.53562C10.9486 3.89749 11.2429 4.30838 11.4581 4.74178L13.2494 3.85237C12.9372 3.22355 12.5217 2.64864 12.0473 2.15288L10.6023 3.53562ZM11.4585 4.74268C11.6895 5.20684 11.8513 5.68681 11.9246 6.16782L13.9018 5.86675C13.7926 5.15003 13.5577 4.47166 13.249 3.85147L11.4585 4.74268ZM11.9244 6.16654C11.9695 6.46574 11.997 6.73855 11.997 7.00062H13.997C13.997 6.60218 13.9554 6.22191 13.902 5.86803L11.9244 6.16654ZM11.997 7.00062C11.997 6.93689 12.0101 6.88858 12.0244 6.85403C12.0386 6.81945 12.0629 6.77727 12.1061 6.7336L13.528 8.14006C13.8223 7.84254 13.997 7.42422 13.997 7.00062H11.997ZM12.11 6.72972C12.1547 6.68494 12.1941 6.66271 12.2251 6.64956C12.2541 6.6372 12.323 6.61281 12.4288 6.61788L12.3329 8.61559C12.8154 8.63874 13.2535 8.41461 13.5242 8.14393L12.11 6.72972ZM12.4273 6.61782C12.4367 6.61825 12.4639 6.62043 12.5047 6.63615C12.5474 6.6526 12.6137 6.68768 12.6775 6.75642L11.2118 8.11724C11.5622 8.49463 12.0176 8.60092 12.3344 8.61566L12.4273 6.61782ZM12.7066 6.78919C12.7129 6.79661 12.7697 6.87009 12.7697 6.99821H10.7697C10.7697 7.46152 10.9669 7.8306 11.1827 8.08447L12.7066 6.78919ZM6.99829 1.71337e-07C3.1323 1.71337e-07 -0.00170898 3.13401 -0.00170898 7H1.99829C1.99829 4.23858 4.23687 2 6.99829 2V1.71337e-07ZM0.998291 8H2.23057V6H0.998291V8ZM7.99829 2.2291V1H5.99829V2.2291H7.99829ZM5.99829 2.2291V7H7.99829V2.2291H5.99829ZM4.77951 1.66698C4.74555 1.68065 4.7117 1.69462 4.67796 1.70889L5.45708 3.5509C5.48009 3.54116 5.50318 3.53163 5.52636 3.5223L4.77951 1.66698ZM4.82442 1.64735C4.80012 1.65764 4.77588 1.66808 4.75171 1.67866L5.55415 3.51063C5.57068 3.50339 5.58726 3.49625 5.60389 3.48921L4.82442 1.64735ZM6.99829 6H2.23057V8H6.99829V6ZM9.17492 12.349C9.19924 12.3388 9.22347 12.3283 9.24762 12.3177L8.44519 10.4858C8.42866 10.493 8.41208 10.5002 8.39546 10.5072L9.17492 12.349ZM9.21983 12.3294C9.25378 12.3158 9.28764 12.3018 9.32139 12.2875L8.54227 10.4455C8.51926 10.4552 8.49617 10.4648 8.47298 10.4741L9.21983 12.3294ZM1.64883 9.17345C1.65911 9.19775 1.66954 9.22197 1.68012 9.24611L3.51209 8.44369C3.50485 8.42717 3.49771 8.4106 3.49068 8.39399L1.64883 9.17345ZM1.66844 9.21832C1.68211 9.25229 1.69609 9.28615 1.71037 9.31991L3.55237 8.5408C3.54263 8.51778 3.5331 8.49468 3.52377 8.47149L1.66844 9.21832ZM12.3505 4.82295C12.3402 4.79865 12.3298 4.77443 12.3192 4.75029L10.4873 5.55271C10.4945 5.56924 10.5016 5.58581 10.5087 5.60241L12.3505 4.82295ZM12.3309 4.77809C12.3172 4.74412 12.3033 4.71025 12.289 4.67649L10.447 5.4556C10.4567 5.47862 10.4662 5.50172 10.4756 5.52492L12.3309 4.77809Z"
        fill="white"
        mask="url(#path-2-outside-1_3913_126776)"
      />
    </svg>
  );
};

export const ExportIcon = (props: { className?: string }) => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M7.05692 23.098C6.77227 23.5458 6.05162 23.3539 6.10434 22.8259C7.25181 11.3346 17.4773 11.3903 17.4773 11.3903V9.18089C17.4773 8.32545 18.4816 7.86493 19.1298 8.42315L25.6174 14.0101C26.0843 14.4123 26.0799 15.137 25.6081 15.5335L19.1206 20.9848C18.47 21.5314 17.4773 21.0689 17.4773 20.2192V17.8568C17.4773 17.8568 11.4812 16.1382 7.05692 23.098Z"
      fill="currentColor"
    />
  </svg>
);

export const UploadIcon = (props: { className?: string }) => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" className={props.className}>
    <path
      d="M16.7461 18.8084H14.9477C14.5081 18.8064 14.0871 18.6305 13.7769 18.3188C13.4669 18.0072 13.2928 17.5855 13.2928 17.146V14.4481C13.2928 14.3688 13.2286 14.3046 13.1491 14.3046H12.3934C11.9685 14.3049 11.5598 14.1416 11.252 13.8488C10.944 13.556 10.7606 13.1559 10.7396 12.7315C10.7186 12.3071 10.8617 11.891 11.1392 11.5691L14.5851 7.54888C14.9084 7.19904 15.363 7 15.8393 7C16.3159 7 16.7704 7.19904 17.0938 7.54888L20.5397 11.5691C20.8172 11.891 20.9602 12.3071 20.9391 12.7315C20.9182 13.1559 20.7347 13.556 20.4269 13.8488C20.1189 14.1416 19.7102 14.3049 19.2853 14.3046H18.5296C18.4503 14.3046 18.386 14.3688 18.386 14.4481V17.146C18.386 17.583 18.214 18.0025 17.9072 18.3135C17.6002 18.6247 17.1832 18.8025 16.7461 18.8084Z"
      fill="currentColor"
    />
    <path
      d="M25.6625 20.1081C25.7183 19.7851 25.6284 19.4538 25.417 19.2031C25.2059 18.9525 24.8946 18.8081 24.5668 18.8084H22.2242C21.9911 18.8084 21.7639 18.8811 21.5741 19.0168C21.3845 19.1522 21.2418 19.3435 21.1662 19.5641L20.5617 21.3853C20.4871 21.6065 20.3448 21.7984 20.1548 21.934C19.9649 22.0697 19.7372 22.142 19.5038 22.1408H12.1889C11.9554 22.142 11.7277 22.0697 11.5378 21.934C11.3478 21.7984 11.2055 21.6063 11.1309 21.3852L10.549 19.5715C10.4734 19.3511 10.3307 19.1598 10.1411 19.0242C9.95134 18.8887 9.72413 18.8159 9.49101 18.8159H7.12584C6.79692 18.8115 6.48283 18.9532 6.26848 19.2025C6.05409 19.4519 5.96115 19.7836 6.01496 20.1081L6.52873 23.1686C6.63617 23.8187 6.97099 24.4094 7.47332 24.8356C7.97564 25.2618 8.61309 25.496 9.2719 25.4961H22.3904C23.0491 25.496 23.6865 25.2618 24.1889 24.8356C24.6913 24.4094 25.0259 23.8186 25.1334 23.1686L25.6625 20.1081Z"
      fill="currentColor"
    />
  </svg>
);
