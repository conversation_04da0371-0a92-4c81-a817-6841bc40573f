import { Trans } from "@lingui/react/macro";
import { countries, getFlagEmoji } from "~/data/countries";
import React, { Fragment, useState } from "react";
import { useIsInterative } from "~/hooks/hooks";
import { twMerge } from "tailwind-merge";
import { XMarkIcon } from "@heroicons/react/20/solid";
import { Dialog, Transition } from "@headlessui/react";
import { refreshFormdata } from "~/components/form/form-hooks";

export const BasicCountrySelect = (props: {
  name: string;
  id?: string;
  defaultValue?: string;
  disabled?: boolean;
  className?: string;
  required?: boolean;
}) => {
  return (
    <select
      id={props.id}
      className={props.className}
      defaultValue={props.defaultValue}
      required={props.required}
      name={props.name}
      disabled={props.disabled}
    >
      <option value="">
        <Trans>Select Country</Trans>
      </option>
      {countries.map((country) => (
        <option key={country.country_code} value={country.country_code}>
          {country.country_name}
        </option>
      ))}
    </select>
  );
};

export const CountrySelectHeadlessUi = (props: {
  name: string;
  className?: string;
  defaultValue?: string;
  disabled?: boolean;
  required?: boolean;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(props.defaultValue);
  const [searchValue, setSearchValue] = useState("");
  const isInteractive = useIsInterative();

  const selectedCountry = countries.find((country) => country.country_code === selectedValue);

  const filteredCountries = countries.filter(
    (country) =>
      !searchValue ||
      country.country_code.toLowerCase().startsWith(searchValue.toLowerCase()) ||
      country.country_name.toLowerCase().startsWith(searchValue.toLowerCase()),
  );

  const onChange = (value: string) => {
    setIsOpen(false);
    setSelectedValue(value);
    refreshFormdata();
  };

  if (!isInteractive) {
    return (
      <BasicCountrySelect
        name={props.name}
        disabled={props.disabled}
        required={props.required}
        defaultValue={selectedValue}
        className={twMerge("select", props.className)}
      />
    );
  }

  return (
    <Fragment>
      <div className="relative">
        <input
          required={props.required}
          disabled={props.disabled}
          className="opacity-0 inset-0 absolute"
          name={props.name}
          value={selectedCountry?.country_code}
        />
        <button
          type="button"
          onClick={() => setIsOpen(true)}
          disabled={props.disabled}
          className={twMerge("block relative w-full input hover:border-slate-500 bg-white py-3 text-left", props.className)}
        >
          {selectedCountry ? (
            <span className="pl-1">
              <span dangerouslySetInnerHTML={{ __html: getFlagEmoji(selectedCountry.country_code) }}></span>
              <span> &nbsp; {selectedCountry.country_name}</span>
            </span>
          ) : (
            <Trans>select country</Trans>
          )}
        </button>

        <Transition appear show={isOpen} as={Fragment}>
          <Dialog as="div" className="relative z-20" onClose={() => setIsOpen(false)}>
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-black/20" />
            </Transition.Child>

            <div className="fixed inset-0 overflow-y-auto">
              <div className="flex min-h-full items-center justify-center p-6">
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 scale-95"
                  enterTo="opacity-100 scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 scale-100"
                  leaveTo="opacity-0 scale-95"
                >
                  <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 shadow-xl transition-all">
                    <div className="space-y-3">
                      <div className="flex flex-row gap-3 justify-between items-center">
                        <Dialog.Title className="text-xl text-slate-600">
                          <Trans>Select country</Trans>
                        </Dialog.Title>
                        <button
                          type="button"
                          onClick={() => setIsOpen(false)}
                          className="inline-block rounded-xl p-2 text-right hover:bg-slate-100 active:bg-slate-100"
                        >
                          <XMarkIcon className="h-5 w-5" />
                        </button>
                      </div>

                      <div>
                        <input
                          className="input"
                          placeholder="search"
                          value={searchValue}
                          onChange={(e) => setSearchValue(e.target.value)}
                        />
                      </div>

                      <div className="max-h-[60vh] overflow-y-auto">
                        {filteredCountries.map((item) => (
                          <button
                            key={item.country_code}
                            type="button"
                            onClick={() => onChange(item.country_code)}
                            className={twMerge(
                              "block w-full text-left p-1 cursor-pointer hover:bg-slate-100",
                              item.country_code === selectedValue && "bg-slate-200",
                            )}
                          >
                            <span dangerouslySetInnerHTML={{ __html: getFlagEmoji(item.country_code) }}></span> &nbsp; {item.country_name}
                          </button>
                        ))}
                      </div>
                    </div>
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition>
      </div>
    </Fragment>
  );
};
