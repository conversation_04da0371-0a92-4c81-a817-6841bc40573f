import { Trans } from "@lingui/react/macro";
import { countries, getFlagEmoji } from "~/data/countries";
import React, { Fragment, Suspense, useEffect, useRef, useState } from "react";
import { useIsInterative } from "~/hooks/hooks";
import {
  FloatingFocusManager,
  FloatingOverlay,
  FloatingPortal,
  useDismiss,
  useFloating,
  useInteractions,
  useListNavigation,
  useRole,
  useTransitionStatus,
} from "@floating-ui/react";
import { twMerge } from "tailwind-merge";
import { XMarkIcon } from "@heroicons/react/20/solid";

import { refreshFormdata } from "~/components/form/form-hooks";
import { useDimensions } from "~/hooks/use-dimensions";
import { toInputId } from "~/components/ResourceInputs";

export const BasicCountrySelect = (props: {
  name: string;
  id?: string;
  defaultValue?: string;
  disabled?: boolean;
  className?: string;
  required?: boolean;
}) => {
  return (
    <select
      id={props.id}
      className={props.className}
      defaultValue={props.defaultValue}
      required={props.required}
      name={props.name}
      disabled={props.disabled}
    >
      <option value="">
        <Trans>Select Country</Trans>
      </option>
      {countries.map((country) => (
        <option key={country.country_code} value={country.country_code}>
          {country.country_name}
        </option>
      ))}
    </select>
  );
};

const FlagEmoji = (props: { country_code: string }) => {
  return (
    <Suspense fallback={<span>{props.country_code}</span>}>
      <span dangerouslySetInnerHTML={{ __html: getFlagEmoji(props.country_code) }}></span>
    </Suspense>
  );
};

export const CountrySelect = (props: {
  name: string;
  className?: string;
  defaultValue?: string;
  disabled?: boolean;
  required?: boolean;
}) => {
  const dimensions = useDimensions();
  const [selectedValue, setSelectedValue] = useState(props.defaultValue);
  const selectedCountry = countries.find((country) => country.country_code === selectedValue);
  const selectedCountryIndex = selectedCountry ? countries.findIndex((country) => selectedCountry === country) : 0;
  const [activeIndex, setActiveIndex] = useState<number | null>(selectedCountryIndex);
  const [searchValue, setSearchValue] = useState("");
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const selectedInputId = toInputId(props.name);
  const isInteractive = useIsInterative(() => {
    const element = document.getElementById(selectedInputId);
    if (element instanceof HTMLInputElement || element instanceof HTMLSelectElement) {
      // console.log('element', element.defaultValue, element.value);
      const currentValue = element.value || "";
      if ((props.defaultValue || "") !== currentValue) {
        setSelectedValue(currentValue);
      }
    }
  });

  const buttonRef = useRef<HTMLButtonElement>(null);
  const listRef = useRef<HTMLDivElement[]>([]);

  const onOpenChange = (open: boolean) => {
    setIsMenuOpen(open);
    setSearchValue("");
    setActiveIndex(selectedCountryIndex);
    if (!open) {
      setTimeout(() => {
        console.log("am i dong focus?");
        buttonRef.current?.focus();
      }, 10);
    }
  };

  const onChange = (value: string) => {
    onOpenChange(false);
    setSelectedValue(value);
    refreshFormdata();
  };

  const { refs, context, floatingStyles } = useFloating({
    open: isMenuOpen,
    // placement: 'center',
    strategy: "fixed",
    onOpenChange: onOpenChange,
  });

  const { isMounted, status } = useTransitionStatus(context, { duration: 200 });

  const listNavigation = useListNavigation(context, {
    listRef: listRef,
    activeIndex,
    // scrollItemIntoView: true,
    onNavigate: setActiveIndex,
    loop: true,
    virtual: true,
  });
  const dismiss = useDismiss(context, { outsidePressEvent: "click" });
  const role = useRole(context, { role: "dialog" });
  const { getReferenceProps, getFloatingProps, getItemProps } = useInteractions([dismiss, role, listNavigation]);

  const filteredCountries = countries.filter(
    (country) =>
      !searchValue ||
      country.country_code.toLowerCase().startsWith(searchValue.toLowerCase()) ||
      country.country_name.toLowerCase().startsWith(searchValue.toLowerCase()),
  );

  // in the case of ssr the status is always unmounted, but could still be opened, thats why the check status === "unmounted" is there.
  const isMountedAndOpen = isMenuOpen || status === "open";

  if (!isInteractive) {
    return (
      <BasicCountrySelect
        id={selectedInputId}
        name={props.name}
        disabled={props.disabled}
        required={props.required}
        defaultValue={props.defaultValue}
        className={twMerge("select", props.className)}
      />
    );
  }

  return (
    <div className="relative">
      <input type={"hidden"} value={selectedValue} name={props.name} required={props.required} />
      <Fragment>
        <div className="relative">
          <button
            ref={buttonRef}
            type={"button"}
            disabled={props.disabled}
            onClick={() => setIsMenuOpen(true)}
            className={twMerge("block relative w-full input hover:border-slate-500 bg-white py-3 text-left", props.className)}
          >
            {/* key is needed to force rerender when selectedCountry changes, because will crash otherwise when google translate is used */}
            <span key={selectedCountry?.country_code || ""}>
              {selectedCountry ? (
                <Fragment>
                  <FlagEmoji country_code={selectedCountry.country_code} />
                  &nbsp; {selectedCountry.country_name}
                </Fragment>
              ) : (
                <Trans>select country</Trans>
              )}
            </span>
          </button>
        </div>
        {isMountedAndOpen && (
          <FloatingFocusManager context={context} initialFocus={-1}>
            <FloatingPortal>
              <FloatingOverlay
                lockScroll
                style={dimensions ? { height: dimensions.height } : undefined}
                className={twMerge(
                  "fixed",
                  false && "fixed top-0 left-0 right-0 z-20 flex bg-black/0 transition-colors p-6",
                  isMountedAndOpen && "bg-black/20",
                )}
              >
                <div className="p-6">
                  <div
                    {...getFloatingProps}
                    ref={refs.setFloating}
                    // style={floatingStyles}
                    className={twMerge(
                      "z-30 m-auto max-w-md rounded-2xl bg-white p-6 align-middle opacity-0 shadow-2xl transition-all ease-in-out",
                      isMountedAndOpen && "opacity-100",
                      "md:h-96 w-full flex flex-col",
                    )}
                  >
                    <div className="space-y-3 h-full flex flex-col">
                      <div className="flex flex-row gap-3 justify-between items-center">
                        <p className="text-xl text-slate-600">
                          <Trans>Select country</Trans>
                        </p>
                        <button
                          type={"button"}
                          onClick={() => setIsMenuOpen(false)}
                          aria-label="close"
                          className={"inline-block rounded-xl p-2 text-right hover:bg-slate-100 active:bg-slate-100"}
                        >
                          <XMarkIcon className="h-5 w-5" />
                        </button>
                      </div>
                      <div className="">
                        <input
                          className="input "
                          ref={(e) => {
                            refs.setReference(e);
                            setTimeout(() => {
                              e?.focus();
                            }, 10);
                            // inputRef.current = e;
                            // e?.focus();
                          }}
                          {...getReferenceProps({
                            tabIndex: -1,
                            onKeyDown: (event) => {
                              const selectedCountry = filteredCountries[activeIndex || 0];
                              if (event.key === "Enter" && selectedCountry) {
                                onChange(selectedCountry.country_code);
                              }
                            },
                          })}
                          placeholder={"search"}
                          value={searchValue}
                          onChange={(e) => setSearchValue(e.target.value)}
                        />
                      </div>
                      <div className="flex-1 overflow-y-scroll relative">
                        {filteredCountries.map((item, index) => (
                          <div
                            key={item.country_code}
                            ref={(node) => {
                              listRef.current[index] = node!;
                            }}
                            {...getItemProps({
                              id: item.country_code,
                              key: item.country_code,
                              role: "option",
                              tabIndex: activeIndex === index ? 0 : -1,
                              onClick: () => onChange(item.country_code),
                            })}
                            className="block aria-selected:bg-slate-100 aria-checked:bg-slate-200 hover:bg-slate-100 p-1 cursor-pointer"
                            aria-checked={item.country_code === selectedValue}
                            aria-selected={activeIndex === index}
                          >
                            <FlagEmoji country_code={item.country_code} /> &nbsp; {item.country_name}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </FloatingOverlay>
            </FloatingPortal>
          </FloatingFocusManager>
        )}
      </Fragment>
    </div>
  );
};
