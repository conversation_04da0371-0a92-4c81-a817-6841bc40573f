import React, { ReactNode } from "react";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { InputSearchParamCopies } from "~/components/meta/input";
import { StateInputKey } from "~/misc/parsers/global-state-parsers";
import { MagnifyingGlassIcon } from "@heroicons/react/20/solid";
import { Form, useNavigation } from "@remix-run/react";
import { ParamLink } from "~/components/meta/CustomComponents";
import { twMerge } from "tailwind-merge";

const searchFormId = "form-search";
const searchInputId = "input-search";

export const SearchForm = (props: { children?: ReactNode }) => {
  return (
    <Form preventScrollReset method={"get"} id={searchFormId}>
      {props.children}
      <input type={"hidden"} value={""} name={"page_nr" satisfies StateInputKey} />
      <InputSearchParamCopies excludeKeys={["search", "error_message", "persist_date_from", "persist_date_to"]} />
    </Form>
  );
};

export const SearchField = (props: { className?: string }) => {
  const search = useSearchParams2();
  const navigation = useNavigation();
  const searchIsIdle = navigation.state === "idle";

  return (
    <fieldset
      className={twMerge(
        "rounded-md border border-slate-300 focus-within:ring-1 ring-slate-400 flex flex-row gap-2 items-center pr-0.5",
        props.className,
      )}
    >
      <input
        name={"search" satisfies StateInputKey}
        id={searchInputId}
        form={searchFormId}
        className="input-clean flex-1 text-sm"
        placeholder={"Search customer name"}
        defaultValue={search.state.search || ""}
      />
      {search.state.search && (
        <ParamLink
          path={"./"}
          paramState={{
            element_action: [searchInputId],
            element_clear: [searchInputId],
            rerender: search.state.rerender + 1,
            page_nr: undefined,
            search: undefined,
          }}
          className="text-slate-600 p-2 hover:text-black"
        >
          Clear
        </ParamLink>
      )}
      <div className="py-0.5 h-full">
        <button
          form={searchFormId}
          className=" btn h-full btn-primary disabled:spinner spinner-light group flex flex-row gap-2 items-center"
          disabled={!searchIsIdle}
        >
          <span className="max-md:hidden text-sm">Search</span>
          <MagnifyingGlassIcon className="w-5 h-5 group-disabled:opacity-0" />
        </button>
      </div>
    </fieldset>
  );
};
