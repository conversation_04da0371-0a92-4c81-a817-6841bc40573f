import { getFinalMessage } from "~/misc/error-translations";
import { isRouteErrorResponse, useRouteError } from "@remix-run/react";
import React from "react";
import { AccountContainer } from "~/components/account/AccountContainer";
import { Alert } from "./base/alert";
import { captureRemixErrorBoundaryError } from "@sentry/remix";
import { I18nProvider } from "@lingui/react";
import { setupI18n } from "@lingui/core";

const i18Instance = setupI18n({ locale: "EN" });

export function DefaultErrorBoundary() {
  const error = useRouteError();

  captureRemixErrorBoundaryError(error);

  if (isRouteErrorResponse(error)) {
    if (error.status === 400) return <Alert status={"error"}>{error.data.msg || error.statusText || "Unauthorized"}</Alert>;
    // if (error.status === 400) return <Alert status={"error"}>{error.statusText || "Unauthorized"}</Alert>;
    if (error.status === 401) {
      return (
        <I18nProvider i18n={i18Instance}>
          <AccountContainer />
        </I18nProvider>
      );
    }
    if (error.status == 404) return <p>{error.data.msg || "Oops! Looks like you tried to visit a page that does not exist."}</p>;

    const foundError = getFinalMessage(error.data.message);

    if (foundError) {
      return (
        <div className="app-container py-3 ">
          <Alert status={"error"}>
            <p>{foundError}</p>
          </Alert>
          <h1>Error</h1>
        </div>
      );
    }

    return <p>{error.statusText}</p>;
  }

  if (error instanceof Error) {
    const msg = getFinalMessage(error.message);

    if (msg)
      return (
        <Alert status={"error"}>
          <p>{msg}</p>
        </Alert>
      );

    return (
      <div>
        <h1>Error</h1>
        <p>{error.message}</p>
        <p>The stack trace is:</p>
        <pre>{error.stack}</pre>
      </div>
    );
  }

  const msg =
    typeof error === "object" && error && "message" in error && typeof error.message === "string"
      ? getFinalMessage(error.message)
      : "Unnkown error";

  return (
    <div>
      <Alert status={"error"}>
        <p>{msg}</p>
      </Alert>
    </div>
  );
}

export const ErrorBoundary = DefaultErrorBoundary;
