import React, { Fragment, ReactNode } from "react";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { useDimensions } from "~/hooks/use-dimensions";
import { twMerge } from "tailwind-merge";

export const IfDebug = (props: {children: ReactNode}) => {
  const search = useSearchParams2();
  if (search.state.persist_debug) return props.children;
  return null;
}

export const DebugContainer = (props: { children?: ReactNode; className?: string }) => {
  const search = useSearchParams2();
  return (
    search.state.persist_debug && <div className={twMerge("border border-red-500 p-3 rounded-md", props.className)}>{props.children}</div>
  );
};

export const ViewportDebug = () => {
  const dimensions = useDimensions();
  return <Fragment />;
  return (
    <div className="fixed top-0 left-0 bg-black/50 text-white p-2 z-50 text-sm">
      <div>Width: {dimensions?.width}px</div>
      <div>Height: {dimensions?.height}px</div>
    </div>
  );
};
