import { toInputId } from "~/components/ResourceInputs";
import { HiddenTypeInput } from "~/components/form/DefaultInput";
import { joinKeys } from "~/misc/helpers";
import React from "react";
import { twMerge } from "tailwind-merge";

export const unitOptions = [
  { value: "day", label: "Days" },
  { value: "mon", label: "Months" },
  { value: "year", label: "Years" },
];

const zeroDurationValue = "00:00:00";
const defaultZeroValue = "0 day";

export const formatValidityDuration = (duration?: string | null) => {
  if (!duration) return null;
  const values = duration.split(" ");
  const unitValue = values[1]?.toLowerCase()?.replace("s", "") || "day";
  return `${values[0]} ${unitOptions.find((option) => option.value === unitValue)?.label}`;
};

export const ValidityDurationSelect = (props: {
  name: string;
  label?: string;
  value?: string | null;
  min?: number;
  onChange?: (value: string) => void;
  disabled?: boolean;
  className?: string;
}) => {
  const defaultValue = props.value === zeroDurationValue ? defaultZeroValue : props.value;
  const values = defaultValue?.split(" ") || [];
  const inputId = toInputId(props.name);
  const unitValue = values[1]?.toLowerCase()?.replace("s", "") || "day";
  return (
    <div className="flex flex-row gap-3 items-center">
      <input
        type={"number"}
        min={props.min ?? 1}
        name={joinKeys(props.name, 0)}
        defaultValue={values[0]}
        className="input w-14"
        id={inputId}
        onChange={(e) => props.onChange?.(e.target.value + " " + unitValue)}
        disabled={props.disabled}
      />
      <select
        name={joinKeys(props.name, 2)}
        value={unitValue}
        className={twMerge("select w-full", props.className)}
        disabled={props.disabled}
        onChange={(e) => props.onChange?.(values[0] + " " + e.target.value)}
      >
        {unitOptions.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
};

export const ValidityDurationField = (props: {
  required?: boolean;
  min?: number;
  name: string;
  label?: string;
  defaultValue?: string | null;
  disabled?: boolean;
  defaultUnit?: string;
}) => {
  const defaultValue = props.defaultValue === zeroDurationValue ? defaultZeroValue : props.defaultValue;
  const values = defaultValue?.split(" ") || [];
  const inputId = toInputId(props.name);
  const unitValue = values[1]?.toLowerCase()?.replace("s", "") || "day";
  return (
    <div>
      {!!props.label && <label htmlFor={inputId}>{props.label || "Valid term"}</label>}
      <div className="flex flex-row gap-3">
        <HiddenTypeInput name={props.name} value={"__join__"} disabled={props.disabled} />
        <input
          type={"number"}
          min={props.min ?? 1}
          required={props.required}
          name={joinKeys(props.name, 0)}
          defaultValue={values[0]}
          className="input w-14"
          id={inputId}
          disabled={props.disabled}
        />
        <input type={"hidden"} name={joinKeys(props.name, 1)} value={" "} disabled={props.disabled} />
        <select name={joinKeys(props.name, 2)} defaultValue={unitValue} className="select w-full" disabled={props.disabled}>
          {unitOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};
