import React, { Fragment, ReactNode } from "react";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { ParamLink } from "~/components/meta/CustomComponents";
import { twMerge } from "tailwind-merge";
import { SortAscIcon, SortDescIcon } from "lucide-react";
import { XMarkIcon } from "@heroicons/react/20/solid";

export const SortButton = (props: { children: ReactNode; className?: string; column: string }) => {
  const search = useSearchParams2();
  const sorts = search.state.sorts;
  const foundSort = sorts.find((sort) => sort.key === props.column);
  const index = sorts.findIndex((sort) => sort === foundSort);
  const indexNr = index + 1;

  const getNewSorts = () => {
    if (!foundSort)
      return [
        ...sorts,
        {
          key: props.column,
          direction: "asc"
        }
      ];
    return sorts.map((sort) =>
      sort.key === props.column
        ? {
          ...sort,
          direction: sort.direction === "asc" ? "desc" : "asc"
        }
        : sort
    );
  };

  return (
    <Fragment>
      <ParamLink
        type={"button"}
        reload
        aria-busy={search.pendingState?.sorts.find((sort) => sort.key === props.column) ? true : undefined}
        className={twMerge("inline-flex items-center aria-busy:opacity-50 whitespace-nowrap", props.className)}
        paramState={{ sorts: getNewSorts() }}
      >
        <span className="underline">{props.children}</span>
        {foundSort && (
          <span className="pl-1">
            {foundSort.direction === "asc" ? <SortAscIcon className="w-4 h-4" /> : <SortDescIcon className="w-4 h-4" />}
          </span>
        )}
        {sorts.length > 1 && !!indexNr && <span className="text-xs">{indexNr}</span>}
      </ParamLink>
      {foundSort && (
        <ParamLink className="px-1 py-1" reload
                   paramState={{ sorts: sorts.filter((sort) => sort.key !== props.column) }}>
          <XMarkIcon className="w-3 h-3" />
        </ParamLink>
      )}
    </Fragment>
  );
};