import React, { Fragment, useId } from "react";
import { Trans } from "~/components/Trans";
import { HiUserCircle } from "react-icons/hi";
import { t } from "~/misc/trans";
import { IdNameSelect } from "~/components/base/IdNameSelect";
import { currencyOptions } from "~/data/currencies";
import { ActionAlert } from "~/components/ActionAlert";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { FormControl } from "../base/base";
import { Divider } from "../Divider";
import { MdEmail, MdLock } from "react-icons/md";
import { fName } from "~/misc/helpers";
import { DefaultSaveInner, SubmitButton, useIsLoading, useIsSuccess } from "~/components/base/Button";
import { RInput, toInputId } from "~/components/ResourceInputs";
import { ClientIdInput, RedirectParamsInput } from "~/components/form/DefaultInput";
import { ActionForm } from "~/components/form/BaseFrom";
import { ParamLink } from "~/components/meta/CustomComponents";
import { Alert } from "~/components/base/alert";
import { useAppContext } from "~/hooks/use-app-context";
import { resetResponseInput } from "~/misc/parsers/global-state-parsers";

const passwordResetFormId = "passwordreset";

const PasswordReset = () => {
  const session = useAppContext();
  const { state } = useSearchParams2();
  const isSuccess = useIsSuccess(passwordResetFormId);
  const [show, setShow] = React.useState(false);

  if (!state.toggle_password_reset)
    return (
      <div className="space-y-1">
        <p className="font-bold">Password</p>
        <p className={"text-xs"}>
          Do you want to (re)set your password?&nbsp;
          <ParamLink
            className="inline text-xs text-slate-500 hover:text-slate-800 hover:underline active:underline"
            paramState={{ toggle_password_reset: true, ...resetResponseInput }}
          >
            Set a new password
          </ParamLink>
        </p>
      </div>
    );

  if (isSuccess) {
    return (
      <div>
        <Alert status={"success"}>Password has been set!</Alert>
      </div>
    );
  }

  return (
    <Fragment>
      {/*<ActionForm identifier={passwordResetFormId} className="space-y-3">*/}
      {/*  <ActionAlert />*/}
      {/*  <div>*/}
      {/*    <input type={"hidden"} name={fName("user", "data.email")} value={session.email} />*/}
      {/*    <CallbackInput callbackName={"send_reset_email"} target_id={tableIdRef("user")} />*/}
      {/*    <SubmitButton className={"btn btn-basic w-full"}>Send reset email</SubmitButton>*/}
      {/*  </div>*/}
      {/*</ActionForm>*/}
      <ActionForm className="space-y-3" identifier={passwordResetFormId}>
        <label htmlFor={toInputId(fName("user", "data.password_hash"))}>Set Password</label>
        <RedirectParamsInput path={"./"} paramState={{ persist_authMethod: undefined }} />
        <RInput table={"user"} field={"id"} type={"hidden"} value={session.user_id} />
        <label className="flex w-full flex-row items-center rounded-md border border-slate-200 bg-white focus-within:ring-1">
          <div className="h-fit p-4 pr-0">
            <MdLock />
          </div>
          <div className="flex-1">
            <RInput
              autoFocus
              className={"w-full border-none py-1 focus:ring-0"}
              required
              min={8}
              minLength={8}
              table={"user"}
              field={"data.password_hash"}
              type={show ? "text" : "password"}
              placeholder="Enter your new Password"
            />
          </div>
          <div className={"pl-0 p-1"}>
            <button type="button" className={"btn btn-basic w-12 text-sm"} onClick={() => setShow(!show)}>
              {show ? "Hide" : "Show"}
            </button>
          </div>
        </label>
        {/*<RInput*/}
        {/*  autoFocus*/}
        {/*  table={"user"}*/}
        {/*  field={"data.password_hash"}*/}
        {/*  required*/}
        {/*  label={"Change Password"}*/}
        {/*  type={"password"}*/}
        {/*  className="input"*/}
        {/*  placeholder={"Enter your new Password"}*/}
        {/*/>*/}
        <div className="flex gap-3 items-center">
          <ParamLink className="btn btn-text flex-1 btn-basic" paramState={{ toggle_password_reset: false }}>
            Cancel
          </ParamLink>
          <SubmitButton className="btn btn-primary flex-1">Save</SubmitButton>
        </div>
      </ActionForm>
      {/*<OtpBlock participant={{ id: "" }} />*/}
    </Fragment>
  );
};

export const AccountManageBox = () => {
  const session = useAppContext();
  const formId = useId();
  const isLoading = useIsLoading(formId);

  if (!session.verified_at) return <Fragment />;

  return (
    <div className={"space-y-6"}>
      <h2 className="text-2xl font-bold text-slate-800">
        <Trans>My account</Trans>
      </h2>
      <ActionForm identifier={formId} className="space-y-6 group">
        <ActionAlert />
        <FormControl icon={<MdEmail />}>
          <input
            required
            disabled
            type={"email"}
            placeholder={`Email`}
            defaultValue={session.email}
            className="flex-1 border-none focus:border-none focus:ring-0 disabled:text-slate-400"
          />
        </FormControl>
        <div>
          {/*<RedirectParamsInput*/}
          {/*  path={"./"}*/}
          {/*  paramState={{*/}
          {/*    toggle_modal: undefined,*/}
          {/*    persist_flow: undefined,*/}
          {/*    persist_authMethod: undefined,*/}
          {/*  }}*/}
          {/*/>*/}
          <RInput table={"user"} field={"id"} value={session.user_id} />
          <ClientIdInput />
          <div className={"space-y-6"}>
            <div>
              <label>
                <Trans>Display name</Trans>
              </label>
              <FormControl icon={<HiUserCircle />}>
                <input
                  disabled={isLoading}
                  placeholder={t`Display name`}
                  className={"flex-1 border-none p-1 focus:border-none focus:ring-0"}
                  defaultValue={session.display_name || ""}
                  name={fName("user", "data.display_name")}
                />
              </FormControl>
            </div>
            <div className="flex flex-wrap gap-3 items-center">
              <label>
                <Trans>Your currency</Trans>
              </label>
              <IdNameSelect
                disabled={isLoading}
                items={currencyOptions}
                name={fName("user", "data.currency_id")}
                defaultValue={session.currency_id || ""}
              />
              <p className={"text-gray-600"}>
                <Trans>Default prices are shown as per operator. You can choose to display prices in this currency.</Trans>
              </p>
            </div>
            <SubmitButton className={"btn btn-primary group-data-success:bg-gray-400"}>
              <DefaultSaveInner />
            </SubmitButton>
          </div>
        </div>
      </ActionForm>
      <Divider />
      <PasswordReset />
    </div>
  );
};
