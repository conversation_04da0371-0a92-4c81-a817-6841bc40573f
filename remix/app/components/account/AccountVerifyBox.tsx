import { useEffect, useState } from "react";
import { Trans } from "~/components/Trans";

import { useAppContext } from "~/hooks/use-app-context";
import { AuthzMethod } from "~/domain/user_session/user_session";
import { OtpBlock, OtpGoBackBlock } from "~/domain/otp/otp-components";
import { toUtc } from "~/misc/date-helpers";
import { addMinutes, differenceInSeconds } from "date-fns";
import { RedirectParamsInput } from "~/components/form/DefaultInput";
import { useSearchParams2 } from "~/hooks/use-search-params2";

function formatSecondsToMinutes(seconds: number) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds < 10 ? "0" : ""}${remainingSeconds}`;
}

const CountDown = (props: { created_at: string; duration_in_minutes: number; onExpire: () => void }) => {
  const createdAt = toUtc(props.created_at);
  const [now, setNow] = useState(new Date());
  const seconds = differenceInSeconds(addMinutes(createdAt, props.duration_in_minutes), toUtc(now));

  useEffect(() => {
    const interval = setInterval(() => {
      setNow(new Date());
    }, 1000);
    return () => clearTimeout(interval);
  }, [setNow]);

  useEffect(() => {}, [props.duration_in_minutes]);
  return (
    <div>
      {formatSecondsToMinutes(seconds)} {seconds} {new Date(props.created_at).toString()}{" "}
      {new Date(props.created_at).getTime() - now.getTime()} {props.duration_in_minutes}
    </div>
  );
};

export const AccountVerifyBox = () => {
  const ctx = useAppContext();
  const params = useSearchParams2();

  return (
    <div className="flex flex-col gap-2 pt-2">
      <h3 className="text-xl font-bold text-gray-800">
        <Trans>Please Verify Your Email</Trans>
      </h3>
      {ctx.verification_method === ("otp" satisfies AuthzMethod) && (
        <div className="space-y-3">
          <OtpBlock
            user_id={ctx.user_id}
            verifyOTPComp={
              <RedirectParamsInput path={"./"} paramState={{ toggle_modal: params.state.toggle_password_reset ? "profile" : undefined }} />
            }
          />
          <OtpGoBackBlock />
        </div>
      )}
      {ctx.verification_method === ("email" satisfies AuthzMethod) && (
        <div className={"flex flex-row space-x-1"}>
          <p>A verification email has been sent to {ctx.email}.</p>
        </div>
      )}
    </div>
  );
};
