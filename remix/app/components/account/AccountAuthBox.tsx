import React, { Fragment, ReactNode, useEffect, useId, useRef, useState } from "react";
import { Divider, DividerWithText } from "~/components/Divider";
import { FaLock } from "react-icons/fa";
import { t } from "~/misc/trans";
import { ActionAlert } from "~/components/ActionAlert";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { PolicyLink, TermsLink } from "~/components/shared";
import { ParamLink } from "~/components/meta/CustomComponents";
import { getAuth, GoogleAuthProvider, signInWithPopup } from "firebase/auth";
import { FcGoogle } from "react-icons/fc";
import { MdEmail } from "react-icons/md";
import type { AuthFlow, AuthMethod } from "~/misc/parsers/global-state-parsers";
import { useFirebase } from "~/components/context/FirebaseProvider";
import { ActionForm } from "~/components/form/BaseFrom";
import { fName, tableIdRef } from "~/misc/helpers";
import { useNavigation, useSubmit } from "@remix-run/react";
import { fixedResponseIdentifierValue, identifierKey, responseIdentifierKey } from "~/misc/vars";
import { SubmitButton } from "~/components/base/Button";
import { Alert, AlertCloseButton } from "~/components/base/alert";
import { useIsInterative } from "~/hooks/hooks";
import { getFinalMessage } from "~/misc/error-translations";
import { AnimatingDiv } from "~/components/base/base";
import { AuthzMethod, defaultAltLabel, defaultAuthMethod } from "~/domain/user_session/user_session";
import { RInput } from "~/components/ResourceInputs";

import { useAppContext } from "~/hooks/use-app-context";
import { RedirectParamsInput } from "../form/DefaultInput";

type AuthMethodData = {
  label: string;
  altLabel: string;
  altMethod: AuthMethod;
  altSubmit?: boolean;
  comp: ReactNode;
};

const buttons: Record<AuthMethod, AuthMethodData> = {
  otp: {
    label: t`Send a One Time Password`,
    altLabel: t`Log in with password`,
    altMethod: "password",
    comp: (
      <Fragment>
        <RInput type="hidden" table="one_time_password" field="data.user_id" value={tableIdRef("user")} />
      </Fragment>
    ),
  },
  password: {
    label: t`Continue`,
    altLabel: defaultAltLabel,
    altMethod: defaultAuthMethod,
    altSubmit: true,
    comp: (
      <Fragment>
        <div className="flex flex-row items-center overflow-hidden rounded border border-slate-200 focus-within:ring-1">
          <div className="bg-slate-100 p-3">
            <FaLock />
          </div>
          <input
            className={"flex-1 border-none focus:border-none focus:ring-0"}
            name={fName("user_session", "data.verification_token")}
            type={"password"}
            placeholder={`Password`}
            required
          />
        </div>
      </Fragment>
    ),
  },
};

const googleProvider = new GoogleAuthProvider();

const GoogleLoginButton = () => {
  const context = useAppContext();
  const ref = useRef<HTMLButtonElement>(null);
  const [firebaseError, setFirebaseError] = useState<string>("");
  const [googleAuthLoading, setGoogleAuthLoading] = useState(false);
  const isInterative = useIsInterative();
  const search = useSearchParams2();
  const submit = useSubmit();
  const { firebase_app } = useFirebase();
  const navigation = useNavigation();
  const formId = useId();
  const isGoogleSubmission = navigation.formData?.get(identifierKey) === formId;
  const isGoogleLoading = googleAuthLoading || isGoogleSubmission;
  return (
    <AnimatingDiv className="w-full space-y-3">
      <button
        ref={ref}
        className="btn btn-basic w-full disabled:opacity-40"
        type={"button"}
        disabled={!isInterative || isGoogleLoading}
        onClick={async () => {
          setGoogleAuthLoading(true);
          setFirebaseError("");
          const auth = getAuth(firebase_app);
          try {
            const result = await signInWithPopup(auth, googleProvider);
            const token = await result.user.getIdToken();

            const formData = new FormData();
            formData.set(identifierKey, formId);
            formData.set(responseIdentifierKey, fixedResponseIdentifierValue);
            // formData.set(aTypeName(), "signin_with_google" satisfies keyof typeof actionHandlers);
            // formData.set(aFieldName("token"), token);

            formData.set(fName("session", "id"), context.session_id);
            formData.set(fName("session", "data.selected_user_id"), tableIdRef("user"));
            formData.set(fName("user", "data.email"), result.user.email || "");
            formData.set(fName("user_session", "data.verification_token"), token);
            formData.set(fName("user_session", "data.method"), "google" satisfies AuthzMethod);
            formData.set(fName("user_session", "data.user_id"), tableIdRef("user"));
            submit(formData, { method: "POST", relative: "path", action: "?" + search.params.toString() });
          } catch (e: any) {
            console.error(e);
            const msg = e?.message || "Could not signin with google";
            // dont show message if the popup was close
            if (msg !== "Firebase: Error (auth/popup-closed-by-user).") {
              setFirebaseError(msg);
            }
          } finally {
            setGoogleAuthLoading(false);
          }
        }}
      >
        <FcGoogle />
        <span>Continue using Google</span>
      </button>
      {!!firebaseError && (
        <Alert status={"warning"} className="flex flex-row gap-3">
          <div>{getFinalMessage(firebaseError)}</div>
          <AlertCloseButton onClose={() => setFirebaseError("")} />
        </Alert>
      )}
    </AnimatingDiv>
  );
};

const authFormId = "authform";

export const AccountAuthBox = (props: { defaultFlow: AuthFlow }) => {
  const emailInputRef = useRef<HTMLInputElement>(null);
  const params = useSearchParams2();
  const navigation = useNavigation();
  const isLoading = !!navigation.formData;

  const authMethod = params.state.persist_authMethod;
  const flow = params.state.persist_flow || props.defaultFlow;

  useEffect(() => {
    const emailInputEl = emailInputRef.current;
    if (emailInputEl) {
      emailInputEl.focus();
    }
  }, [flow, authMethod]);

  return (
    <div className="flex flex-col space-y-6">
      <h2 className={"text-2xl font-bold text-gray-800"}>{flow === "signup" ? t`Create an account` : t`Log in to your account`}</h2>
      <GoogleLoginButton />
      <DividerWithText className={"mt-6 mb-3"}>OR</DividerWithText>
      <ActionForm identifier={authFormId} className="space-y-3">
        <ActionAlert />
        <div className="flex flex-col space-y-3">
          <div className="flex flex-row items-center overflow-hidden rounded border border-slate-200 focus-within:ring-1">
            <div className="bg-slate-100 p-3">
              <MdEmail />
            </div>
            <input
              required
              disabled={isLoading}
              className={"flex-1 border-none focus:border-none focus:ring-0"}
              name={fName("user", "data.email")}
              type={"email"}
              placeholder={`Email`}
              ref={emailInputRef}
            />
          </div>
          {buttons[authMethod].comp}
          <RedirectParamsInput
            path={"./"}
            paramState={{
              persist_add_account: false,
            }}
          />
          <RInput table={"user_session"} field={"data.method"} type={"hidden"} value={authMethod} />
          <RInput table={"user_session"} field={"data.user_id"} type={"hidden"} value={tableIdRef("user")} />
          <RInput table={"session"} field={"data.selected_user_id"} type={"hidden"} value={tableIdRef("user")} />
          <SubmitButton className="btn btn-primary w-full">{buttons[authMethod].label}</SubmitButton>
          {flow === "signin" && (
            <ParamLink className={"btn btn-basic"} paramState={{ persist_authMethod: buttons[authMethod].altMethod }}>
              {buttons[authMethod].altLabel}
            </ParamLink>
          )}
        </div>
      </ActionForm>
      <div className={"flex flex-col space-y-3"}>
        <Divider />
        {flow === "signup" && (
          <Fragment>
            <p>
              By signing up, you agree with our <TermsLink /> and <PolicyLink />
            </p>
            <Divider />
            <p>
              Do you already have an account?{" "}
              <ParamLink className="link" paramState={{ persist_authMethod: defaultAuthMethod, persist_flow: "signin" }}>
                Log in
              </ParamLink>
            </p>
          </Fragment>
        )}
        {flow === "signin" && (
          <Fragment>
            <p>
              By logging in, you agree with our <TermsLink /> and <PolicyLink />
            </p>
            <Divider />
            <div className="space-y-1">
              <p>
                Are you new?{" "}
                <ParamLink className="link" paramState={{ persist_authMethod: defaultAuthMethod, persist_flow: "signup" }}>
                  Create an account
                </ParamLink>
              </p>
              {authMethod === "password" && (
                <p>
                  Forgot your password? Set it using{" "}
                  <ParamLink paramState={{ persist_authMethod: "otp", toggle_password_reset: true }} className="link">
                    OTP Login
                  </ParamLink>
                </p>
              )}
            </div>
          </Fragment>
        )}
      </div>
    </div>
  );
};
