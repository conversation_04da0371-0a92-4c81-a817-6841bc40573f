import React, { Fragment, ReactNode } from "react";
import { t } from "~/misc/trans";
import { Divider } from "../Divider";
import { UserIcon } from "@heroicons/react/20/solid";
import { ProfileIcon, ProfileLoggedInIcon } from "~/components/Icons";
import { CgUserAdd } from "react-icons/cg";
import { twMerge } from "tailwind-merge";
import { SubmitButton } from "~/components/base/Button";
import { RInput } from "~/components/ResourceInputs";
import { ClientIdInput, ResponseIdentifierInput } from "~/components/form/DefaultInput";
import { Trans } from "~/components/Trans";
import { ActionForm } from "~/components/form/BaseFrom";
import { ParamLink } from "~/components/meta/CustomComponents";
import { setState, useSearchParams2 } from "~/hooks/use-search-params2";
import { useIsInterative } from "~/hooks/hooks";
import {
  FloatingFocusManager,
  FloatingOverlay,
  useDismiss,
  useFloating,
  useInteractions,
  useRole,
  useTransitionStatus,
} from "@floating-ui/react";
import { useAppContext } from "~/hooks/use-app-context";
import { Toggle } from "~/components/Checker";
import { useShowCustomerToggle } from "~/hooks/use-show-customer-toggle";

const SwitchUserForm = (props: { user_id: string; children: ReactNode }) => (
  <ActionForm className="w-full">
    <ClientIdInput />
    <ResponseIdentifierInput />
    {/*<RInput table={"session"} field={"id"} value={props.session_id} />*/}
    <RInput table={"session"} field={"data.selected_user_id"} value={props.user_id} type={"hidden"} />
    <SubmitButton
      type={"submit"}
      className={
        "flex w-full flex-row items-center gap-2 p-2 px-3 text-slate-600 hover:bg-slate-100 active:bg-slate-200 disabled:animate-pulse disabled:cursor-progress"
      }
    >
      {props.children}
    </SubmitButton>
  </ActionForm>
);

const LogoutUserSession = () => {
  const session = useAppContext();
  const otherAccounts = session.user_sessions.filter((us) => us.user_id !== session.user_id);
  return (
    <ActionForm>
      <ClientIdInput />
      {/*<RInput table={"session"} field={"id"} value={session.session_id} />*/}
      <RInput table={"session"} field={"data.selected_user_id"} type={"hidden"} value={otherAccounts[0]?.user_id || ""} />
      <RInput table={"user_session"} field={"id"} value={session.user_session_id} />
      <RInput table={"user_session"} field={"data.destroyed_at"} value={"yes"} type={"hidden"} />
      <SubmitButton className={`w-full p-2 hover:bg-slate-100 active:bg-slate-200 aria-busy:animate-pulse aria-busy:cursor-progress`}>
        {otherAccounts.length > 0 ? `Logout on ${session.email || 'Anonymous'}` : t`Logout`}
      </SubmitButton>
    </ActionForm>
  );
};

export const AccountPopoverContent = () => {
  const session = useAppContext();
  const search = useSearchParams2();
  const showCustomerToggle = useShowCustomerToggle();

  // const anonymous = session.user_sessions.filter(us => !us.user_id);
  const allUserSessions = session.user_sessions.filter((us) => us.user_id);
  const otherUserSessions = allUserSessions.filter((us) => us.user_id !== session.user_id);

  return (
    <div className="relative overflow-hidden rounded-lg bg-slate-50 shadow-xl">
      <div className="space-y-3 p-6">
        {session.display_name && (
          <div className={"flex flex-row justify-center"}>
            <div className="rounded-full bg-primary p-3 text-gray-800">
              <UserIcon className={"h-10 w-10"} />
            </div>
          </div>
        )}
        <div>
          {session.display_name && <p className="text-center font-bold">{session.display_name}</p>}
          <p className="text-center">{session.email || <Trans>Anonymous</Trans>}</p>
          {session.editor && (
            <p className="text-center text-sm">
              Editor<span className="font-bold text-primary">!</span>
            </p>
          )}
        </div>
      </div>
      {showCustomerToggle && (
        <div className="md:hidden">
          <Divider />
          <ParamLink
            paramState={{ customer_view: !search.state.customer_view }}
            className="gap-1 flex flex-row items-center group aria-checked:text-primary p-3 justify-center"
            aria-checked={search.state.customer_view}
          >
            <span>Customer View</span>
            <Toggle />
          </ParamLink>
        </div>
      )}
      <Divider />
      <div className="flex flex-col text-center">
        {/*{(session.members.length > 0 || session.editor) && (*/}
        {/*  <BaseLink*/}
        {/*    className={`p-2 hover:bg-slate-100 active:bg-slate-200 */}
        {/*      aria-current:border-x-[2px] aria-current:border-primary aria-current:bg-gray-50`}*/}
        {/*    to="/u"*/}
        {/*  >{t`Manage`}</BaseLink>*/}
        {/*)}*/}
        <ParamLink
          paramState={{ toggle_modal: session.verified_at ? "profile" : "account" }}
          className={"p-2 hover:bg-slate-100 active:bg-slate-200"}
        >{t`Account`}</ParamLink>
        <Divider />
        {allUserSessions.length > 1 && (
          <Fragment>
            <div className="flex flex-col py-3">
              {otherUserSessions.map((us) => (
                <SwitchUserForm key={us.id} user_id={us.user_id || ""}>
                  <UserIcon className={"h-5 w-5 text-slate-600"} />
                  {us.email || "Anonymous"}
                </SwitchUserForm>
              ))}
              {!!session.user_session_id && (
                <SwitchUserForm user_id={""}>
                  <div className="flex h-5 w-5 items-center justify-center">
                    <div className={"h-3 w-3 rounded-full bg-slate-400"} />
                  </div>
                  Anonymous
                </SwitchUserForm>
              )}
            </div>
          </Fragment>
        )}
        {session.editor && (
          <Fragment>
            <Divider />
            <ParamLink
              paramState={{ toggle_modal: "account", persist_add_account: false }}
              className={"flex w-full flex-row items-center gap-2 p-2 px-3 text-slate-600 hover:bg-slate-100 active:bg-slate-200"}
            >
              <CgUserAdd className="h-5 w-5" /> {t`Add another account`}
            </ParamLink>
          </Fragment>
        )}
        {!!session.user_session_id && (!!session.user_id || otherUserSessions.length > 1) && (
          <Fragment>
            <Divider />
            <LogoutUserSession />
          </Fragment>
        )}
      </div>
    </div>
  );
};

export const AccountMenuPopover = () => {
  const search = useSearchParams2();
  const isInteractive = useIsInterative();
  const isMenuOpen = search.state.toggle_modal === "account_menu";

  const { refs, context, floatingStyles } = useFloating({
    open: isMenuOpen,
    strategy: "fixed",
    onOpenChange: (open) => {
      setState({ toggle_modal: open ? "account_menu" : undefined }, { replaceRoute: true });
    },
  });

  const { isMounted, status } = useTransitionStatus(context, { duration: 50 });

  const dismiss = useDismiss(context);
  const role = useRole(context, { role: "dialog" });
  const { getReferenceProps, getFloatingProps, getItemProps } = useInteractions([dismiss, role]);

  // in the case of ssr the status is always unmounted, but could still be opened, thats why the check status === "unmounted" is there.
  // const isMountedAndOpen = (status === "unmounted" && isMenuOpen) || status === "open";
  const isMountedAndOpen = (!isInteractive && isMenuOpen) || status === "open";

  if (!isMounted) return <Fragment />;

  return (
    <FloatingFocusManager context={context}>
      <Fragment>
        <FloatingOverlay
          lockScroll
          className={twMerge("fixed inset-0 z-20 bg-black/0 transition-colors", isMountedAndOpen && "bg-black/20")}
        />
        <div
          {...getFloatingProps}
          ref={refs.setFloating}
          className={twMerge("absolute right-0 top-0 z-20", isMountedAndOpen && "opacity-100")}
        >
          <AccountPopoverContent />
        </div>
      </Fragment>
    </FloatingFocusManager>
  );
};

export const AccountTopmenu = () => {
  const session = useAppContext();
  const userSessions = session.user_sessions.filter((userSession) => userSession.user_id);
  return (
    <div>
      {userSessions.length ? (
        <div className="relative">
          <ParamLink
            paramState={{ toggle_modal: "account_menu" }}
            className="block rounded-xl p-3 text-slate-800 hover:bg-slate-50 focus:bg-slate-100 focus:outline-none focus:ring-0 active:bg-slate-200"
          >
            <div className="relative">
              {session.editor && <div className="absolute top-[-7px] right-[-2px] font-bold text-primary">!</div>}
              <ProfileLoggedInIcon className={"h-5 w-5 text-slate-200"} />
            </div>
          </ParamLink>
          <AccountMenuPopover />
        </div>
      ) : (
        <Fragment>
          <ParamLink paramState={{ toggle_modal: "account" }} className="block p-3 text-slate-800 md:hidden">
            <ProfileIcon className={"h-5 w-5"} />
          </ParamLink>
          <div className={"hidden flex-row items-center space-x-3 md:flex"}>
            <ParamLink paramState={{ toggle_modal: "account", persist_flow: "signup" }} className="font-semibold text-slate-800">
              Sign up
            </ParamLink>
            <ParamLink
              paramState={{ toggle_modal: "account", persist_flow: "signin" }}
              className="btn btn-primary rounded-full text-white hover:no-underline active:no-underline"
            >
              Log in
            </ParamLink>
          </div>
        </Fragment>
      )}
    </div>
  );
};
