import type { ReactNode } from "react";
import React, { Fragment } from "react";
import { AccountAuthBox } from "~/components/account/AccountAuthBox";
import { AccountVerifyBox } from "~/components/account/AccountVerifyBox";
import type { AuthFlow } from "~/misc/parsers/global-state-parsers";
import { Divider } from "~/components/Divider";
import { RInput } from "~/components/ResourceInputs";
import { SubmitButton } from "~/components/base/Button";
import { ClientIdInput } from "~/components/form/DefaultInput";
import { ActionAlert } from "~/components/ActionAlert";
import { ActionForm, ResponseIdProvider } from "~/components/form/BaseFrom";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { ParamLink } from "~/components/meta/CustomComponents";
import { useAppContext } from "~/hooks/use-app-context";
import { defaultAuthMethod } from "~/domain/user_session/user_session";

const AccountPaths = (props: { defaultFlow: AuthFlow }) => {
  const context = useAppContext();
  const search = useSearchParams2();
  const userSessionsWithUser = context.user_sessions.filter(userSession => userSession.user_id);

  if (context.user_id && !context.verified_at) return <AccountVerifyBox />;
  if (userSessionsWithUser.length > 0) {
    return (
      <div className="space-y-2 pt-1">
        {/*<pre> {JSON.stringify(context.user_sessions, null, 2)}</pre>*/}
        <ResponseIdProvider>
          <ActionAlert />
          <h2 className="text-xl font-bold text-slate-800">Account</h2>
          {/*<p>Logged in as {context.user_sessions.map((userSession) => userSession.email).join(", ")}</p>*/}
          <p>
            {context.email ? (
              <span>
                Logged in as <strong>{context.email}</strong>
              </span>
            ) : (
              <strong>Anonymous</strong>
            )}
          </p>
          {context.user_sessions.length > 1 && (
            <div className="flex flex-wrap gap-3">
              <span>Switch to </span>
              <ul>
                {context.user_sessions.map((us) => (
                  <li key={us.id}>
                    <ActionForm>
                      <ClientIdInput />
                      {/*<RInput table={"session"} field={"id"} value={context.session_id} />*/}
                      <RInput table={"session"} field={"data.selected_user_id"} value={us.user_id || ''} type={"hidden"} />
                      <SubmitButton
                        className={`link isLoading && transition-colors disabled:text-slate-600 disabled:hover:no-underline 
                      aria-busy:animate-pulse aria-busy:cursor-progress aria-busy:hover:no-underline`}
                        disabled={us.user_id === context.user_id}
                      >
                        {us.email || 'Anonymous'}
                      </SubmitButton>
                    </ActionForm>
                  </li>
                ))}
                <li>
                  <ActionForm>
                    <ClientIdInput />
                    {/*<RInput table={"session"} field={"id"} value={context.session_id} />*/}
                    <RInput table={"session"} field={"data.selected_user_id"} value={""} type={"hidden"} />
                    <SubmitButton
                      className={`link isLoading && transition-colors disabled:text-slate-600 disabled:hover:no-underline 
                      aria-busy:animate-pulse aria-busy:cursor-progress aria-busy:hover:no-underline`}
                      disabled={!context.user_id}
                    >
                      Anonymous
                    </SubmitButton>
                  </ActionForm>
                </li>
              </ul>
            </div>
          )}

          {search.state.persist_add_account ? (
            <div className="space-y-3 py-6">
              <p className="font-bold">Add another account</p>
              <Divider />
              <AccountAuthBox defaultFlow={"signin"} />
            </div>
          ) : (
            <ParamLink
              paramState={{ persist_add_account: true, persist_flow: "signin", persist_authMethod: defaultAuthMethod }}
              className="btn btn-basic"
              type="button"
            >
              Add another account
            </ParamLink>
          )}
        </ResponseIdProvider>
      </div>
    );
  }
  return <AccountAuthBox defaultFlow={props.defaultFlow} />;
};

export const AccountContainer = (props: { children?: ReactNode }) => {
  return (
    <div className={"max-w-screen-sm"}>
      {props.children}
      <AccountPaths defaultFlow={"signup"} />
    </div>
  );
};

export const EditorRequired = () => {
  const app = useAppContext();
  if (!app.user_id || !app.verified_at)
    return (
      <div className="app-container py-6">
        <div className="max-w-screen-sm">
          <AccountPaths defaultFlow={"signin"} />
        </div>
      </div>
    );
  if (!app.editor)
    return (
      <div className="app-container py-6">
        <h1 className="text-xl font-bold">Unauthorized</h1>
        <p>You are not authorized for this page.</p>
        <p>
          Back to{" "}
          <ParamLink path={"/"} className="link">
            Home
          </ParamLink>
        </p>
      </div>
    );
  return <Fragment />;
};
