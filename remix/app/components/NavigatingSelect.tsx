import { ComponentProps } from "react";
import { StateInput } from "~/misc/parsers/global-state-parsers";
import { useIsInterative } from "~/hooks/hooks";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { useNavigation } from "@remix-run/react";
import { removeObjectKeys } from "~/misc/helpers";

export const NavigatingSelect = (
  props: Omit<ComponentProps<"select">, "onChange"> & {
    onChange: (value: string) => Partial<StateInput>;
  },
) => {
  const isInteractive = useIsInterative();
  const search = useSearchParams2();
  const navigation = useNavigation();
  const searchIsIdle = navigation.state === "idle";
  return (
    <select
      {...removeObjectKeys(props, "onChange")}
      disabled={!isInteractive || !searchIsIdle || props.disabled}
      onChange={(e) => {
        search.setState(props.onChange(e.target.value as any));
      }}
    >
      {props.children}
    </select>
  );
};
