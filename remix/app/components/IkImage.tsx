import React, { ComponentProps } from "react";
import { twMerge } from "tailwind-merge";
import { useAppContext } from "~/hooks/use-app-context";
import { removeObjectKeys } from "~/misc/helpers";

const imagekitBaseUrl = "https://ik.imagekit.io/scamgem";

export const ikUrl = (path: string, ikQuery?: string): string => {
  const imageKitUrl = imagekitBaseUrl + (ikQuery ? "/" + ikQuery : "");
  return `${imageKitUrl}/${path}?alt=media`;
};

export const createImageUrlForBucketPath = (bucket: string, path: string, ikQuery?: string): string => {
  const imageKitUrl = imagekitBaseUrl + (ikQuery ? "/" + ikQuery : "");
  const encededPath = encodeURIComponent(path);
  return `${imageKitUrl}/file/${encededPath}?alt=media`;
  return `${imageKitUrl}/v0/b/${bucket}/o/${encededPath}?alt=media`;
};

export const useIkUrl = () => {
  const { env } = useAppContext();
  const bucket = env.firebase_singapore.storageBucket;
  return { fromBucket: (path: string, ikQuery?: string) => createImageUrlForBucketPath(bucket, path, ikQuery) };
};

export const createImageUrl = (bucket: string, path: string, w: number = 800, h: number = 700) => {
  const ikQuery = `tr:w-${w},h-${h}`;
  return createImageUrlForBucketPath(bucket, path, ikQuery);
};

interface Props {
  path?: string;
  w: number;
  h: number;
  className?: string;
  alt?: string;
}

export const IkImageSimple = (props: ComponentProps<"img"> & { ik: { w: number; h: number; path: string } }) => {
  const { env } = useAppContext();
  const uri = createImageUrl(env.firebase_singapore.storageBucket, props.ik.path, props.ik.w, props.ik.h);

  return <img src={uri} {...removeObjectKeys(props, "ik", "src")} />;
};

export const IkImage = (props: Props) => {
  const { env } = useAppContext();
  const w = props.w || 800;
  const h = props.h || 700;
  if (props.path) {
    const uri = createImageUrl(env.firebase_singapore.storageBucket, props.path, w, h);
    return (
      <div className={twMerge("overflow-hidden bg-gray-100", props.className)}>
        <img alt={props.path} src={uri} {...props} />
      </div>
    );
  }
  return <div className={twMerge("bg-gray-50", props.className)} />;
};
