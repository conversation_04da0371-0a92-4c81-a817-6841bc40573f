import type { ComponentProps } from "react";
import React, { useEffect, useRef, useState } from "react";

export const ReadmoreBox = (props: ComponentProps<"div">) => {
  const [fullAbout, setFullAbout] = useState<null | boolean>(null);
  const aboutRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    const clientHeight = aboutRef.current?.clientHeight || 0;
    const scrollHeight = aboutRef.current?.scrollHeight || 0;
    if (clientHeight < scrollHeight || clientHeight + scrollHeight === 0) {
      setFullAbout(false);
    }
  }, []);

  return (
    <div>
      <div
        ref={aboutRef}
        className={`cursor-pointer whitespace-pre-wrap ${fullAbout ? "" : "line-clamp-[12]"}`}
        onClick={() => {
          if (fullAbout !== null) {
            setFullAbout(!fullAbout);
          }
        }}
      >
        {props.children}
      </div>
      <div>
        <button
          className={`text-xs text-gray-600 hover:underline active:underline ${fullAbout === null ? "hidden" : "visible"}`}
          onClick={() => {
            setFullAbout(!fullAbout);
          }}
        >
          {fullAbout ? "less" : "show more"}
        </button>
      </div>
    </div>
  );
};
