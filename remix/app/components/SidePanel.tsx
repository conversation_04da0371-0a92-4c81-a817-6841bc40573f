import { setState, useSearchParams2 } from "~/hooks/use-search-params2";
import { useIsInterative } from "~/hooks/hooks";
import {
  FloatingFocusManager,
  FloatingOverlay,
  useDismiss,
  useFloating,
  useInteractions,
  useRole,
  useTransitionStatus,
} from "@floating-ui/react";
import React, { Fragment, ReactNode, useId, useRef } from "react";
import { twMerge } from "tailwind-merge";
import type { modals } from "~/misc/parsers/global-state-parsers";
import { ParamLink } from "~/components/meta/CustomComponents";
import { ChevronLeft } from "lucide-react";
import { BackFallbackLink } from "~/components/base/BackFallbackLink";

export const SidePanelHeading = (props: { children: ReactNode; className?: string }) => {
  return (
    <div className={twMerge("flex flex-row items-center pl-2", props.className)}>
      <ParamLink paramState={{ toggle_modal: undefined }} className="p-2">
        <ChevronLeft className="w-5 h-5" />
      </ParamLink>
      {props.children}
    </div>
  );
};


export const SidePanel = (props: { children: ReactNode; className?: string; dialogname?: (typeof modals)[number] }) => {
  const search = useSearchParams2();
  const isInteractive = useIsInterative();
  const dialogName: (typeof modals)[number] = props.dialogname || "side";
  const isMenuOpen = search.state.toggle_modal === dialogName;
  const sidePanelBodyId = useId();

  const { refs, context } = useFloating({
    open: isMenuOpen,
    strategy: "fixed",
    onOpenChange: (open) => {
      if (
        open ||
        (() => {
          const sidePanelBody = document.getElementById(sidePanelBodyId)
          const forms = sidePanelBody?.querySelectorAll('form[data-equal="false"]');
          const hasUnsavedChanges = forms && forms.length > 0;
          return !hasUnsavedChanges || window.confirm("You will lose your changes, are you sure you want to close?");
        })()
      ) {
        setState({ toggle_modal: open ? dialogName : undefined }, { replaceRoute: true });
      }
    },
  });

  const { isMounted, status } = useTransitionStatus(context, { duration: 200 });

  const dismiss = useDismiss(context);
  const role = useRole(context, { role: "menu" });
  const { getReferenceProps, getFloatingProps, getItemProps } = useInteractions([dismiss, role]);

  // in the case of ssr the status is always unmounted, but could still be opened, thats why the check status === "unmounted" is there.
  // const isMountedAndOpen = (status === "unmounted" && isMenuOpen) || status === "open";
  const isMountedAndOpen = (!isInteractive && isMenuOpen) || status === "open";

  return (
    isMounted && (
      <FloatingFocusManager context={context}>
        <Fragment>
          <FloatingOverlay
            lockScroll
            className={twMerge("fixed inset-0 z-20 bg-black/0 transition-colors", isMountedAndOpen && "bg-black/20")}
          />
          <div
            {...getFloatingProps}
            ref={refs.setFloating}
            id={sidePanelBodyId}
            // style={floatingStyles}
            className={twMerge(
              "fixed inset-y-0 right-0 z-30 h-full translate-x-40 opacity-0 transition-all ease-in-out",
              isMountedAndOpen && "translate-x-0 opacity-100",
              props.className,
            )}
          >
            {props.children}
          </div>
        </Fragment>
      </FloatingFocusManager>
    )
  );
};
