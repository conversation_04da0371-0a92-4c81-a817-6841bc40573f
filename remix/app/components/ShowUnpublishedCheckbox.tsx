import { useSearchParams2 } from "~/hooks/use-search-params2";
import { ParamLink } from "~/components/meta/CustomComponents";
import React from "react";

export const ShowUnpublishedCheckbox = () => {
  const { state } = useSearchParams2();
  return (
    <ParamLink
      className="flex w-fit flex-row items-center gap-2 rounded-md border border-slate-300 p-3 shadow-sm"
      path="./"
      paramState={{ show_unpublished: !state.show_unpublished }}
    >
      <input type={"checkbox"} className="checkbox" checked={state.show_unpublished} />
      <span>Show unpublished</span>
    </ParamLink>
  );
};
