import React from "react";
import { SingleImageGallery } from "~/components/field/SingleImageGallery";

interface SingleImageGalleryExampleProps {
  fileNames?: string[] | null;
}

export const SingleImageGalleryExample = (props: SingleImageGalleryExampleProps) => {
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Single Image Gallery</h2>
      <p className="text-gray-600">
        This gallery shows one image at a time with navigation arrows. Click on the image to open it in a dialog.
      </p>
      <SingleImageGallery fileNames={props.fileNames} />
    </div>
  );
};
