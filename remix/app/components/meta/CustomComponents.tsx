import React, { forwardRef, SyntheticEvent, useMemo } from "react";
import type { LinkProps } from "~/components/base/base";
import { BaseLink } from "~/components/base/base";
import { toast } from "~/misc/toast";
import { twMerge } from "tailwind-merge";
import type { StateInput } from "~/misc/parsers/global-state-parsers";
import { dropTransientParams, mergeStateToParams } from "~/misc/parsers/global-state-parsers";
import { setParams, useSearchParams2 } from "~/hooks/use-search-params2";
import { removeObjectKeys } from "~/misc/helpers";
import { To } from "@remix-run/router";

export const handEvent = (e: SyntheticEvent<HTMLElement>) => {
  // console.log('disable');
  if (!window.navigator.onLine) {
    e.preventDefault();
    e.stopPropagation();
    toast(`Ooops... Connection failed, \nMake sure your device has an active internet connection`, null);
  }
};

export const CLink = (props: LinkProps) => {
  return <BaseLink onClick={handEvent} {...props} className={twMerge("text-primary hover:underline", props.className)} />;
};

interface ParamLinkProps extends Omit<LinkProps, "to"> {
  path?: string;
  hash?: string;
  params?: (params: URLSearchParams) => void;
  persistAllParams?: boolean;
  reload?: boolean;
}

const ParamLinkInternal = forwardRef<HTMLAnchorElement, ParamLinkProps>((props: ParamLinkProps, ref) => {
  const { params } = useSearchParams2();
  const newSearchParams = new URLSearchParams(params);
  if (props.path && props.path !== "./" && !props.persistAllParams) {
    dropTransientParams(newSearchParams);
  }
  if (props.params) {
    props.params(newSearchParams);
  }
  const newSearchParmsStr = newSearchParams.toString();
  const to: To = useMemo(() => {
    return { ...(props.path ? { pathname: props.path } : {}), search: newSearchParmsStr, hash: props.hash };
  }, [props.path, newSearchParmsStr]);
  if (props.log) {
    console.log("to", to);
  }

  return (
    <BaseLink
      {...removeObjectKeys(props, "params")}
      ref={ref}
      to={to}
      onClick={
        (props.onClick || props.params) &&
        ((e) => {
          if (!props.path && !props.prefetch && props.params && !props.reload) {
            e.preventDefault();
            setParams({
              params: newSearchParams,
              hash: props.hash,
              replace: props.replace === undefined ? true : props.replace,
            });
          }
          props.onClick?.(e);
        })
      }
    >
      {props.children}
    </BaseLink>
  );
});

export type ParamStateLinkProps = ParamLinkProps & {
  paramState?: Partial<StateInput>;
};

export const ParamLink = forwardRef<HTMLAnchorElement, ParamStateLinkProps>((props: ParamStateLinkProps, ref) => {
  if (props["aria-disabled"]) return <span {...removeObjectKeys(props, "paramState", "path", "hash")} />;
  return (
    <ParamLinkInternal
      {...removeObjectKeys(props, "paramState")}
      ref={ref}
      params={(e) => {
        if (props.paramState) {
          mergeStateToParams(e, props.paramState);
        }
        props.params?.(e);
      }}
    />
  );
});
