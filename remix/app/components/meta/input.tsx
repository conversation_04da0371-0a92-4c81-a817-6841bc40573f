import { StateInput } from "~/misc/parsers/global-state-parsers";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { Fragment } from "react";

export const InputSearchParamCopies = (props: { excludeKeys?: Array<keyof StateInput> }) => {
  const search = useSearchParams2();
  return (
    <Fragment>
      {Array.from(search.params.entries()).map(([key, value], index) => {
        if (!(key in search.state) || props.excludeKeys?.includes(key)) return <Fragment key={index} />;
        return <input key={index} name={key} value={value} type={"hidden"} />;
      })}
    </Fragment>
  );
};
