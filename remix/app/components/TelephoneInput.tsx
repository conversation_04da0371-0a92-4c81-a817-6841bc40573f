import { countries, getFlagEmoji } from "~/data/countries";
import { useIsInterative } from "~/hooks/hooks";
import { useFormCtx } from "~/components/form/BaseFrom";
import { fName, myGroupBy2 } from "~/misc/helpers";
import React, { Fragment, useRef, useState } from "react";
import {
  FloatingFocusManager,
  FloatingOverlay,
  FloatingPortal,
  useDismiss,
  useFloating,
  useInteractions,
  useListNavigation,
  useRole,
  useTransitionStatus,
} from "@floating-ui/react";
import { ChevronDownIcon, FlagIcon, XMarkIcon } from "@heroicons/react/20/solid";
import { twMerge } from "tailwind-merge";
import { RerenderOnErrorBoundary } from "~/components/ErrorBoundaryWithSuspense";

const fullCountries = countries.map((country) => ({ ...country, telephone_code_plus: "+" + country.telephone_code }));
const dialCodes = myGroupBy2(fullCountries, (country) => country.telephone_code).sort((a, b) => a.groupKey.localeCompare(b.groupKey));

const fromCountry = "clear";

export const DialCodeSelect = (props: {
  value?: string;
  disabled?: boolean;
  required?: boolean;
  showFromCountry: boolean;
  onChange?: (value: string) => void;
}) => {
  const formCtx = useFormCtx();
  const [activeIndex, setActiveIndex] = useState<number | null>(0);
  const [searchValue, setSearchValue] = useState("");
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const buttonRef = useRef<HTMLButtonElement>(null);
  const listRef = useRef<HTMLDivElement[]>([]);

  const dialCodesFiltered = dialCodes.filter((dialCode) => {
    if (!searchValue) return true;
    const lowerSearch = searchValue.toLowerCase();

    const countryCmpr = dialCode.items.find((country) => {
      const countryNameSearch = lowerSearch.length > 2 && country.country_name.toLowerCase().includes(lowerSearch);
      return country.country_code.toLowerCase().startsWith(lowerSearch) || countryNameSearch;
    });
    const dialCodePlus = "+" + dialCode.groupKey;
    return dialCode.groupKey.startsWith(lowerSearch) || dialCodePlus.startsWith(lowerSearch) || !!countryCmpr;
  });

  const telephoneCodes = dialCodesFiltered.map((dialCode) => "+" + dialCode.groupKey);

  const onOpenChange = (open: boolean) => {
    setIsMenuOpen(open);
    setSearchValue("");
    setActiveIndex(0);
    if (!open) {
      setTimeout(() => {
        buttonRef.current?.focus();
      }, 10);
    }
  };

  const onChange = (value: string) => {
    onOpenChange(false);
    props.onChange?.(value);
  };

  const { refs, context, floatingStyles } = useFloating({
    open: isMenuOpen,
    strategy: "fixed",
    onOpenChange: onOpenChange,
  });

  const { isMounted, status } = useTransitionStatus(context, { duration: 200 });

  const listNavigation = useListNavigation(context, {
    listRef: listRef,
    activeIndex,
    onNavigate: setActiveIndex,
    loop: true,
    virtual: true,
  });

  const dismiss = useDismiss(context, { outsidePressEvent: "click" });
  const role = useRole(context, { role: "dialog" });
  const { getReferenceProps, getFloatingProps, getItemProps } = useInteractions([dismiss, role, listNavigation]);

  const isMountedAndOpen = isMenuOpen || status === "open";

  const selectedDialCode = dialCodes.find((group) => group.groupKey.replace("+", "") === props.value?.replace("+", ""));

  return (
    <Fragment>
      <div className="relative w-28 max-w-28">
        <button
          ref={buttonRef}
          type="button"
          disabled={props.disabled}
          onClick={() => setIsMenuOpen(true)}
          className={twMerge("flex items-center gap-2 w-full input hover:border-slate-500 bg-white py-3 text-left")}
        >

                {/* key is needed to force rerender when selectedCountry changes, because will crash otherwise when google translate is used */}
            <span  key={selectedDialCode?.groupKey || ''} className="flex gap-3 flex-row items-center">
              {selectedDialCode ? (
                <Fragment>
                  {selectedDialCode.items.length > 1 ? (
                    <FlagIcon className="w-5 h-5 text-slate-300" />
                  ) : (
                    <span dangerouslySetInnerHTML={{ __html: getFlagEmoji(selectedDialCode.items[0].country_code) }}></span>
                  )}
                  <span className="whitespace-nowrap">+{selectedDialCode.groupKey}</span>
                </Fragment>
              ) : (
                "-"
              )}
            </span>
          <span className={"flex-1"}></span>
          <div className="w-4 h-4">
            <ChevronDownIcon className="w-4 h-4 text-slate-600" />
          </div>
        </button>
      </div>
      {isMountedAndOpen && (
        <FloatingFocusManager context={context} initialFocus={-1}>
          <FloatingPortal>
            <FloatingOverlay
              lockScroll
              className={twMerge("fixed inset-0 z-20 flex bg-black/0 transition-colors p-6", isMountedAndOpen && "bg-black/20")}
            >
              <div
                {...getFloatingProps}
                ref={refs.setFloating}
                className={twMerge(
                  "left-0 z-30 m-auto max-w-md rounded-2xl bg-white p-6 align-middle opacity-0 shadow-2xl transition-all ease-in-out",
                  isMountedAndOpen && "opacity-100",
                  "md:h-96 w-full min-h-dvh flex flex-col",
                )}
              >
                <div className="space-y-3 h-full flex flex-col">
                  <div className="flex flex-row gap-3 items-center">
                    <div className="flex flex-wrap gap-x-3 items-center">
                      <p className="text-xl text-slate-600 whitespace-nowrap">Select Dial Code</p>
                      {props.showFromCountry && false && (
                        <button
                          type={"button"}
                          className="link whitespace-nowrap"
                          onClick={() => {
                            onChange(fromCountry);
                          }}
                        >
                          From Country
                        </button>
                      )}
                    </div>
                    <span className="flex-1"></span>
                    <button
                      type="button"
                      onClick={() => setIsMenuOpen(false)}
                      aria-label="close"
                      className="inline-block rounded-xl p-2 text-right hover:bg-slate-100 active:bg-slate-100"
                    >
                      <XMarkIcon className="h-5 w-5" />
                    </button>
                  </div>
                  <div>
                    <input
                      className="input"
                      ref={refs.setReference}
                      {...getReferenceProps({
                        tabIndex: -1,
                        onKeyDown: (event) => {
                          const selectedCode = telephoneCodes[activeIndex || 0];
                          if (event.key === "Enter" && selectedCode) {
                            onChange(selectedCode);
                          }
                        },
                      })}
                      placeholder="search"
                      value={searchValue}
                      onChange={(e) => setSearchValue(e.target.value)}
                    />
                  </div>
                  <div className="flex-1 overflow-y-scroll relative">
                    {dialCodesFiltered.map((dialCode, index) => {
                      const countries = dialCode.items;
                      const code = dialCode.groupKey;
                      return (
                        <div
                          key={code}
                          ref={(node) => {
                            listRef.current[index] = node!;
                          }}
                          {...getItemProps({
                            id: code,
                            role: "option",
                            tabIndex: activeIndex === index ? 0 : -1,
                            onClick: () => onChange(code),
                          })}
                          className="flex gap-2 items-center aria-selected:bg-slate-100 aria-checked:bg-slate-200 hover:bg-slate-100 p-1 cursor-pointer "
                          aria-checked={code === props.value}
                          aria-selected={activeIndex === index}
                        >
                          {countries.length > 1 ? (
                            <span className="text-xs w-5 text-center rounded-sm text-slate-300 relative">
                              <FlagIcon className="w-5 h-5" />
                              {/*<span className="absolute top-0 left-1 text-white">{countries.length}</span>*/}
                            </span>
                          ) : (
                            <span dangerouslySetInnerHTML={{ __html: getFlagEmoji(countries[0].country_code) }}></span>
                          )}
                          +{code}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </FloatingOverlay>
          </FloatingPortal>
        </FloatingFocusManager>
      )}
    </Fragment>
  );
};

export const TelephoneInput = (props: { defaultValue?: string | null; required?: boolean; name: string; disabled?: boolean }) => {
  const isInteractive = useIsInterative();
  const form = useFormCtx();
  const countryCode = form.formdata?.get(fName("participant", "data.country_code"));
  const telephoneCodeForCountry = fullCountries.find((country) => country.country_code === countryCode)?.telephone_code_plus;
  const defaultValue = props.defaultValue;
  const defaultTelCodevalue =
    dialCodes.map((dialCode) => "+" + dialCode.groupKey).find((dialCode) => defaultValue?.startsWith(dialCode)) || "";
  const finalDefaultTelCodeValue = defaultValue && !defaultTelCodevalue ? null : defaultTelCodevalue;
  const defaultTelNumberValue = finalDefaultTelCodeValue ? defaultValue?.replace(finalDefaultTelCodeValue, "") : defaultValue;

  const [telCodeValue, setTelCodeValue] = useState<null | string | undefined>(finalDefaultTelCodeValue);
  const [phoneNumber, setPhoneNumber] = useState(defaultTelNumberValue);

  const finalTelCodeValue = (telCodeValue === "" ? telephoneCodeForCountry : telCodeValue) || "";
  const fullPhoneNumber = phoneNumber ? (finalTelCodeValue || "") + phoneNumber : "";

  const showCopyFromCountry = !!form.formdata?.has(fName("participant", "data.country_code")) && telCodeValue !== "";

  if (!isInteractive)
    return (
      <input
        name={props.name}
        className="input"
        disabled={props.disabled}
        required={props.required}
        type="tel"
        defaultValue={props.defaultValue || ""}
        minLength={6}
      />
    );

  return (
    <div className="flex flex-row gap-3">
      <DialCodeSelect
        disabled={props.disabled}
        showFromCountry={showCopyFromCountry}
        value={finalTelCodeValue}
        onChange={(value) => {
          setTelCodeValue(value === fromCountry ? "" : value || null);
        }}
      />
      <input
        type="tel"
        className="input"
        value={phoneNumber || ""}
        onChange={(e) => setPhoneNumber(e.target.value)}
        required={props.required}
        disabled={props.disabled}
        minLength={6}
      />
      {!props.disabled && <input type="hidden" value={fullPhoneNumber} name={props.name} />}
    </div>
  );
};
