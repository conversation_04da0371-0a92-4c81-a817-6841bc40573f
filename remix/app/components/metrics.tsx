import { <PERSON>, <PERSON>, Pie<PERSON><PERSON>, ResponsiveContainer } from "recharts";
import { ReactNode } from "react";
import { difference, sumBy } from "remeda";

export interface Total {
  label: string;
  count: number;
  color?: string;
}

export const PieBlock = (props: { totals: Total[]; children: ReactNode }) => {
  const totalCount = sumBy(props.totals, (total) => total.count);
  const top4 = [...props.totals].sort((a, b) => b.count - a.count).slice(0, 4);
  const other = difference(props.totals, top4);
  console.log("other", other);
  const totalRestItem: Total = {
    label: "Other",
    count: sumBy(other, (total) => total.count),
    color: "lightgrey",
  };

  const totalsToShow = totalRestItem.count ? [...top4, totalRestItem] : top4;

  return (
    <div className="p-3 bg-slate-100 rounded-md border border-slate-200">
      <p className="font-bold">{props.children}</p>
      <div className="flex flex-row">
        <div>
          <div className=" w-32 h-32 ">
            <ResponsiveContainer>
              <PieChart margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
                <Pie dataKey={"count"} data={totalsToShow} innerRadius={16} cornerRadius={4}>
                  {totalsToShow.map((total) => {
                    return <Cell key={total.label} fill={total.color || "grey"} />;
                  })}
                </Pie>
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
        <div className="p-3">
          {totalsToShow.map((total) => {
            const percentage = (total.count / totalCount) * 100;
            return (
              <p key={total.label} className="flex items-center flex-row gap-3 justify-between ">
                <div>
                  <span className="block w-3 h-3 rounded-full" style={{ background: total.color || "grey" }}></span>
                </div>
                <span>{total.label || "Unknown"}</span>
                <span className="flex-1"></span>
                <span>{percentage.toFixed(1)}%</span>
              </p>
            );
          })}
        </div>
      </div>
    </div>
  );
};
