import { ReactNode } from "react";
import { getEstablishmentName } from "~/domain/establishment/helpers";
import { useAppContext } from "~/hooks/use-app-context";
import { ParamLink } from "./meta/CustomComponents";
import { twMerge } from "tailwind-merge";

export const AllowedForEstablishment = (props: { children: ReactNode }) => {
  const { establishment, editor, members } = useAppContext();
  const allowed = establishment ? members.find((member) => member.establishment_id === establishment.id && member.admin) : editor;

  if (!allowed) {
    return <div>You are not allowed to access this page</div>;
  }

  return <>{props.children}</>;
};

export const EstablishmentLayout = (props: { children: ReactNode; title: ReactNode; className?: string }) => {
  const { establishment } = useAppContext();

  return (
    <div className={twMerge(props.className || "px-5")}>
      <p className="text-sm text-gray-500 mb-2">{establishment ? getEstablishmentName(establishment) : "Editor"}</p>
      {props.title}
      <AllowedForEstablishment>{props.children}</AllowedForEstablishment>
    </div>
  );
};
