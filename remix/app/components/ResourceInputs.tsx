import type { InputProps, LabelProps, SelectProps, TextAreaProps } from "~/types/input";
import type { DB } from "~/kysely/db";
import { FieldProps, fName, removeObjectKeys } from "~/misc/helpers";
import React, { ComponentProps, Fragment, ReactNode, Suspense, useId, useState } from "react";
import { HiddenTypeInput, OperationInput, RedirectInput } from "~/components/form/DefaultInput";
import { useNavigation } from "@remix-run/react";
import { identifierKey } from "~/misc/vars";
import { ActionForm, useFormCtx } from "~/components/form/BaseFrom";
import { DeleteButton, useIsLoading, useIsSuccess } from "~/components/base/Button";
import { FieldType } from "~/misc/formdata-to-nested-json";
import { twMerge } from "tailwind-merge";

export const toInputId = (str: string) => str.replace(/\.]\[/g, "-");
const toDataId = (str: string) => str.replace(/\.]\[/g, "-") + "-datalist";

export function RLabel<T extends keyof DB, F extends keyof DB[T] & string>(props: LabelProps & FieldProps<T, F>) {
  const inputId = toInputId(fName(props.table, props.field, props.index));
  return <label htmlFor={inputId} {...props} />;
}

export const FInput = (props: InputProps) => {
  const isLoading = useIsLoading();
  return <input disabled={isLoading} {...props} />;
};

export const LabelInput = (props: InputProps & { label: ReactNode }) => {
  const generatedId = useId();
  const finalId = props.id || generatedId;

  return (
    <Fragment>
      <label htmlFor={finalId} className={props.required ? "required" : ""}>
        {props.label}
      </label>
      <br />
      <input {...removeObjectKeys(props, "label")} />
    </Fragment>
  );
};

export const LabelTextarea = (props: TextAreaProps & { label: ReactNode }) => {
  const generatedId = useId();
  const finalId = props.id || generatedId;

  return (
    <Fragment>
      <label htmlFor={finalId} className={props.required ? "required" : ""}>
        {props.label}
      </label>
      <br />
      <textarea {...removeObjectKeys(props, "label")} />
    </Fragment>
  );
};

export const SimpleRInput = (
  props: InputProps & {
    formIdentifier?: string;
    hiddenType?: FieldType;
    label?: ReactNode;
    datalist?: ReactNode;
  },
) => {
  const [blurred, setBlurred] = useState(false);
  const [focused, setFocused] = useState(false);
  const navigation = useNavigation();
  const name = props.name || "";
  const id = toInputId(name);
  const dataListId = toDataId(name);
  const formId = useFormCtx().id;
  const finalFormId = props.formIdentifier || formId;
  const isSaving = !!finalFormId && navigation.formData?.get(identifierKey) === finalFormId;
  const finalId = props.id || id;

  return (
    <Fragment>
      {props.hiddenType === "__boolean__" && !props.disabled && <input type={"hidden"} name={name} value={"false"} />}
      {props.label && (
        <Fragment>
          <label className={props.required ? "required" : ""} htmlFor={id}>
            {props.label}
          </label>
          <br />
        </Fragment>
      )}
      <input
        onBlur={() => setBlurred(true)}
        onFocus={() => setFocused(true)}
        name={name}
        id={finalId}
        disabled={props.disabled || isSaving}
        list={props.list || props.datalist ? dataListId : undefined}
        ref={props.myref}
        {...(removeObjectKeys(props, "hiddenType", "formIdentifier", "name", "id") as InputProps)}
        className={twMerge(focused && blurred && "touched", props.className)}
      />
      {props.datalist && <datalist id={dataListId}>{props.datalist}</datalist>}
      {props.hiddenType && !props.disabled && <HiddenTypeInput name={name} value={props.hiddenType} />}
    </Fragment>
  );
};

const defaultNameKeys = [] as string[];

export function RInput<T extends keyof DB, F extends keyof DB[T] & string = keyof DB[T] & string>(
  props: InputProps &
    FieldProps<T, F> & {
      label?: ReactNode;
      datalist?: ReactNode;
    },
) {
  return (
    <SimpleRInput
      name={fName(props.table, props.field, props.index, ...(props.nameKeys || defaultNameKeys))}
      type={props.type || props.field === "id" ? "hidden" : "text"}
      {...props}
    />
  );
}

export function RSelect<T extends keyof DB, F extends keyof DB[T] & string>(props: SelectProps & FieldProps<T, F>) {
  const [blurred, setBlurred] = useState(false);
  const [focused, setFocused] = useState(false);
  const name = fName(props.table, props.field, props.index);
  const id = toInputId(name);
  const navigation = useNavigation();
  const formId = useFormCtx().id;
  const finalFormId = props.formIdentifier || formId;
  const isSaving = !!finalFormId && navigation.formData?.get(identifierKey) === finalFormId;
  return (
    <select
      name={name}
      onBlur={() => setBlurred(true)}
      onFocus={() => setFocused(true)}
      id={id}
      ref={props.myref}
      disabled={isSaving}
      {...props}
      className={twMerge(blurred && focused && "touched", props.className)}
    />
  );
}

export function RDatalist<T extends keyof DB, F extends keyof DB[T] & string>(props: ComponentProps<"datalist"> & FieldProps<T, F>) {
  const name = fName(props.table, props.field, props.index);
  const id = toDataId(name);
  return <datalist id={id} {...props} />;
}

export function RTextarea<T extends keyof DB, F extends keyof DB[T] & string>(props: TextAreaProps & FieldProps<T, F>) {
  const name = fName(props.table, props.field, props.index);
  const id = toInputId(name);
  return <textarea name={name} id={id} ref={props.myref} {...props} />;
}

export const DeleteInputs = (props: { table: keyof DB; ids: string[] }) =>
  props.ids.map((id) => (
    <Fragment key={id}>
      <RInput table={props.table} field={"id"} index={id} value={id} />
      <OperationInput table={props.table} value={"delete"} index={id} />
    </Fragment>
  ));

export function DeleteButtonForm<T extends keyof DB>(
  props: Omit<ComponentProps<"button">, "value"> & {
    table: T;
    values: string[];
    redirect?: string;
    confirmMessage?: string;
  },
) {
  return (
    <ActionForm replace confirmMessage={props.confirmMessage || "Are you sure?"}>
      {props.redirect && <RedirectInput value={props.redirect} />}
      <DeleteInputs table={props.table} ids={props.values} />
      <DeleteButton className={props.className}>{props.children}</DeleteButton>
    </ActionForm>
  );
}
