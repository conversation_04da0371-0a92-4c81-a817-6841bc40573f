import React from "react";
import { Trans } from "~/components/Trans";
import { CLink } from "~/components/meta/CustomComponents";
import type { RemixLinkProps } from "@remix-run/react/dist/components";
import { useAppContext } from "~/hooks/use-app-context";
import { isDiversdesk } from "~/utils/domain-helpers";

export const TermsLink = (props: Partial<RemixLinkProps>) => {
  const ctx = useAppContext();
  const diversDesk = isDiversdesk(ctx.host);
  return (
    <CLink to={diversDesk ? "https://www.diversdesk.com/terms-conditions" : "/terms-of-conditions.pdf"} target={"_blank"} {...props}>
      <Trans>Terms & conditions</Trans>
    </CLink>
  );
};

export const PolicyLink = (props: Partial<RemixLinkProps>) => {
  const ctx = useAppContext();
  const diversDesk = isDiversdesk(ctx.host);
  return (
    <CLink to={diversDesk ? "https://www.diversdesk.com/privacy-policy" : "/privacy-policy.pdf"} target={"_blank"} {...props}>
      <Trans>Privacy policy</Trans>
    </CLink>
  );
};

// export const StateIcon = (props: ComponentProps<"div"> & {severity: number}) => <div className={twMerge(props.severity === 0 ? 'bg-green-500' : props.severity === 1)}/>
export const defaultNotRegisteredValue = <span className="opacity-50">Not registered</span>;
