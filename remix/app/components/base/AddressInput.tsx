import { t } from "@lingui/core/macro";
import { ComponentProps, useEffect, useRef } from "react";
import { removeObjectKeys } from "~/misc/helpers";
import { Loader } from "@googlemaps/js-api-loader";
import { useAppContext } from "~/hooks/use-app-context";
import { create } from "zustand";
import { useLingui } from "@lingui/react";
import AutocompleteOptions = google.maps.places.AutocompleteOptions;
import PlacesLibrary = google.maps.PlacesLibrary;
import PlaceResult = google.maps.places.PlaceResult;

const useLib = create<{ places: PlacesLibrary | null }>(() => ({
  places: null,
}));

export const baseAutocompleteOptions: AutocompleteOptions = {
  types: ["geocode", "establishment"],
};

export const AddressInput = (
  props: ComponentProps<"input"> & {
    autocompleteOptions?: AutocompleteOptions;
    onPlaceChange?: (result: PlaceResult) => void;
  },
) => {
  const app = useAppContext();
  const lingui = useLingui();
  const { places } = useLib();
  const searchBoxRef = useRef<HTMLInputElement>(null);
  useEffect(() => {
    if (!places) {
      const loader = new Loader({
        apiKey: app.env.googleMapsPublicApiKey,
        libraries: ["places"],
      });
      loader.importLibrary("places").then((placesLib) => {
        useLib.setState({ places: placesLib });
      });
    }
  }, []);

  useEffect(() => {
    if (places && searchBoxRef.current) {
      const autocomplete = new places.Autocomplete(searchBoxRef.current, props.autocompleteOptions);
      const onPlaceChange = props.onPlaceChange;
      if (!onPlaceChange) return;
      autocomplete.addListener("place_changed", () => {
        const place = autocomplete.getPlace();

        if (place.place_id) {
          const service = new places.PlacesService(document.createElement("div")); // Dummy element for PlacesService
          service.getDetails({ placeId: place.place_id, fields: ["address_components"] }, (details, status) => {
            if (status === "OK" && details) {
              onPlaceChange(details);
              const zipCode = details?.address_components?.find((component) => component.types.includes("postal_code"))?.short_name;
              console.log("details", details);
              // Do something with the zipCode (e.g., update state, display it)
              console.log("ZIP code:", zipCode);
            } else {
              console.error("Error fetching place details:", status);
            }
          });
        }
      });
    }
  }, [places]);
  return (
    <input
      {...removeObjectKeys(props, "autocompleteOptions")}
      placeholder={props.placeholder || t(lingui.i18n)`Enter a location`}
      ref={searchBoxRef}
      onKeyDown={(e) => {
        if (e.key === "Enter") {
          // prevent form submit when selecting a suggested place by google maps by pressing enter
          e.preventDefault();
        }
      }}
    />
  );
};
