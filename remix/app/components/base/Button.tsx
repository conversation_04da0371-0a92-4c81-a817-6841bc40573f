import React, { ComponentProps, forwardRef, ReactNode, useEffect, useId, useRef, useState } from "react";
import { CgClose } from "react-icons/cg";
import { twMerge } from "tailwind-merge";
import { useNavigation } from "@remix-run/react";
import { identifierKey } from "~/misc/vars";
import { useFormCtx, useResponseId } from "~/components/form/BaseFrom";
import { removeObjectKeys } from "~/misc/helpers";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { useIsInterative } from "~/hooks/hooks";
import { StateInputKey } from "~/misc/parsers/global-state-parsers";

export const useIsLoading = (identifier?: string) => {
  const formId = useFormCtx().id;
  const navigation = useNavigation();
  return navigation.formData?.get(identifierKey) === (identifier || formId);
};

export const useIsSuccess = (identifier?: string) => {
  const search = useSearchParams2();
  const responseId = useResponseId();
  const formId = useFormCtx().id;
  const finalId = identifier || formId || responseId;
  return search.state.response_form_id === finalId && !search.state.response_error;
};

export const InteractiveButton = (props: ComponentProps<"button">) => {
  const isInteractive = useIsInterative();
  return (
    <button type={"button"} onClick={props.onClick} className={props.className} disabled={!isInteractive || props.disabled}>
      {props.children}
    </button>
  );
};

export const Button = (
  props: ComponentProps<"button"> & {
    loading?: boolean;
  },
) => {
  const btnProps = { ...props };
  delete btnProps.loading;
  return (
    <button
      aria-busy={props.loading}
      {...btnProps}
      disabled={props.disabled || props.loading}
      className={twMerge("relative", props.loading && "spinner", props.className)}
    >
      {props.children}
      {/*{!!props.loading && (*/}
      {/*  <div className="absolute inset-0 flex items-center justify-center">*/}
      {/*    <Spinner />*/}
      {/*  </div>*/}
      {/*)}*/}
    </button>
  );
};

export const DefaultSaveInner = (props: { children?: ReactNode; saved?: ReactNode }) => {
  const form = useFormCtx();
  const isSuccess = useIsSuccess();
  if (isSuccess && form.isEqual && !form.isTouched) {
    return props.saved || "Saved!";
  }
  return props.children || "Save";
};

export const useOnFormSuccess = (onSuccess?: () => void) => {
  const [ranSuccess, setRanSuccess] = useState<string[]>([]);
  const form = useFormCtx();
  const search = useSearchParams2();
  useEffect(() => {
    if (search.state.form_success_id === form.successId && !ranSuccess.find((id) => id === form.successId)) {
      onSuccess?.();
      setRanSuccess([...ranSuccess, form.successId]);
    }
  }, [onSuccess, search.state.form_success_id, form.successId, ranSuccess]);
};

export const OnFormSuccess = (props: { onSuccess?: () => void }) => {
  useOnFormSuccess(props.onSuccess);
  return null;
};

export const SubmitButton = (
  props: Omit<ComponentProps<"button">, "className"> & {
    formIdentifier?: string;
    className?: string;
  },
) => {
  const form = useFormCtx();
  const navigation = useNavigation();
  const finalFormId = props.formIdentifier || form.id;
  const isLoading = navigation.formData?.get(identifierKey) === finalFormId || form.isPingPending;
  return (
    <Button
      type={"submit"}
      loading={isLoading}
      aria-busy={isLoading}
      disabled={form.isEqual}
      // data-success={isSuccess}
      {...(removeObjectKeys(props, "formIdentifier") as any)}
      className={props.className}
    />
  );
};

export const DeleteButton = (props: { children?: ReactNode; className?: string; form?: string }) => {
  return (
    <SubmitButton
      form={props.form}
      className={twMerge(
        "aria-busy:loading-dots text-red-500 hover:underline disabled:opacity-60 disabled:hover:no-underline aria-busy:animate-pulse aria-busy:hover:no-underline",
        props.className,
      )}
    >
      {props.children || "delete"}
    </SubmitButton>
  );
};

export const CloseButton = forwardRef<HTMLButtonElement, ComponentProps<"button">>((props, ref) => {
  return (
    <button
      aria-label="close"
      {...props}
      ref={ref}
      className={twMerge("rounded-xl p-2 hover:bg-slate-100 active:bg-slate-100", props.className)}
    >
      <CgClose />
    </button>
  );
});
