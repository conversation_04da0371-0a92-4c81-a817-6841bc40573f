import React, { Fragment, ReactNode, useContext, useRef, useState } from "react";
import {
  ComputePositionConfig,
  flip,
  FloatingList,
  FloatingOverlay,
  offset,
  shift,
  useDismiss,
  useFloating,
  useInteractions,
  useListNavigation,
  useRole,
  useTransitionStatus,
} from "@floating-ui/react";
import { twMerge } from "tailwind-merge";
import { setState, useSearchParams2 } from "~/hooks/use-search-params2";

interface SelectContextValue {
  activeIndex: number | null;
  selectedIndex: number | null;
  // getItemProps: ReturnType<typeof useInteractions>["getItemProps"];
  // handleSelect: (index: number | null) => void;
}

const SelectContext = React.createContext<SelectContextValue>({} as SelectContextValue);

export const useSelectContext = () => useContext(SelectContext);

export const Popover = (props: {
  popoverId: string;
  children: ReactNode;
  content: ReactNode;
  className?: string;
  options?: Partial<ComputePositionConfig>;
}) => {
  const search = useSearchParams2();
  const [activeIndex, setActiveIndex] = useState<number | null>(0);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(0);

  const { x, y, refs, strategy, context } = useFloating({
    middleware: [offset(-5), flip(), shift() /*arrow({ element: arrowRef })*/],
    placement: "top",
    open: search.state.popover_id === props.popoverId,
    onOpenChange: (open) => {
      setState({ popover_id: open ? props.popoverId : null }, { replaceState: true, replaceRoute: true });
    },
    ...props.options,
  });
  const elementsRef = useRef<Array<HTMLElement | null>>([]);
  const listNav = useListNavigation(context, {
    listRef: elementsRef,
    activeIndex: activeIndex,
    selectedIndex: selectedIndex,
    onNavigate: setActiveIndex,
  });

  const { getReferenceProps, getFloatingProps } = useInteractions([listNav, useRole(context), useDismiss(context)]);

  const { isMounted, status } = useTransitionStatus(context, { duration: 200 });

  const selectContext = React.useMemo(
    () => ({
      activeIndex,
      selectedIndex,
    }),
    [activeIndex, selectedIndex],
  );

  if (!props.content) return <div className={props.className}>{props.children}</div>;

  console.log("status", status);

  return (
    <>
      <div className={props.className} ref={refs.setReference} {...getReferenceProps()}>
        {props.children}
      </div>
      {isMounted && (
        <Fragment>
          <FloatingOverlay
            lockScroll={true}
            className={twMerge("z-10 bg-black opacity-0 transition-opacity ", status === "open" && "opacity-5")}
          />
          <div
            ref={refs.setFloating}
            style={{
              position: strategy,
              top: y ?? 0,
              left: x ?? 0,
            }}
            {...getFloatingProps()}
            role="tooltip"
            className={twMerge("z-10 px-2 opacity-0 transition-opacity", status === "open" && "opacity-100")}
          >
            <div className="rounded-md bg-white shadow-xl border border-slate-200 ">
              <SelectContext.Provider value={selectContext}>
                <FloatingList elementsRef={elementsRef}>{props.content}</FloatingList>
              </SelectContext.Provider>
            </div>
          </div>
        </Fragment>
      )}
    </>
  );
};
