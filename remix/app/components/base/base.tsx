import React, { ComponentProps, DetailedHTMLProps, forwardRef, Fragment, HTMLAttributes, ReactNode, useEffect, useRef } from "react";
import { twMerge } from "tailwind-merge";
import { Transition } from "@headlessui/react";
import autoAnimate from "@formkit/auto-animate";
import type { LinkProps as RemixLinkProps } from "@remix-run/react";
import { Link as RemixLink, useLocation, useNavigate, useNavigation } from "@remix-run/react";
import { useIsInterative } from "~/hooks/hooks";
import { removeObjectKeys } from "~/misc/helpers";

export { Link as RemixLink } from "@remix-run/react";

export type DivProps = DetailedHTMLProps<HTMLAttributes<HTMLDivElement>, HTMLDivElement>;

export const FormControl = (
  props: DivProps & {
    icon: ReactNode;
  },
) => (
  <fieldset className={twMerge("flex flex-row items-center rounded border border-slate-200 focus-within:ring-1", props.className)}>
    <div className="h-fit bg-slate-100 p-1">{props.icon}</div>
    {props.children}
  </fieldset>
);

export const Spacer = (props: DivProps) => <div {...props} className={twMerge("flex-1", props.className)} />;

export const SectionHeading = (props: DivProps) => (
  <h3 {...props} className={twMerge("text-xl font-bold text-gray-800 first-letter:capitalize", props.className)} />
);

export const BlueBox = (props: DivProps) => <div {...props} className={twMerge("rounded border bg-secondary-50 p-3", props.className)} />;

export const OverlayTransitionChild = () => (
  <Transition.Child
    as={Fragment}
    enter="ease-out duration-300"
    enterFrom="opacity-0"
    enterTo="opacity-100"
    leave="ease-in duration-200"
    leaveFrom="opacity-100"
    leaveTo="opacity-0"
  >
    <div className="fixed inset-0 z-20 bg-black bg-opacity-20" />
  </Transition.Child>
);

export const Spinner = (props: { className?: string }) => (
  <svg
    aria-hidden="true"
    role="status"
    className={twMerge("inline h-4 w-4 animate-spin text-white", props.className)}
    viewBox="0 0 100 101"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
      fill="#E5E7EB"
    />
    <path
      d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
      fill="currentColor"
    />
  </svg>
);

export const AnimatingTableBody = (props: ComponentProps<"section">) => {
  const animatingParentRef = useRef<HTMLTableSectionElement>(null);
  useEffect(() => {
    animatingParentRef.current && autoAnimate(animatingParentRef.current);
  }, []);
  return <tbody {...props} ref={animatingParentRef} />;
};

export const AnimatingDiv = (props: ComponentProps<"div">) => {
  const animatingParentRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    animatingParentRef.current && autoAnimate(animatingParentRef.current);
  }, []);
  return <div {...props} ref={animatingParentRef} />;
};

export type LinkProps = RemixLinkProps & {
  exact?: boolean;
  paramsActive?: boolean;
  end?: boolean;
  log?: boolean;
};

export const Backbutton = (props: { children: ReactNode; className?: string }) => {
  const navigate = useNavigate();
  const isInteractive = useIsInterative();
  return (
    <button
      type={"button"}
      disabled={!isInteractive}
      onClick={() => navigate(-1)}
      className={twMerge("text-slate-800 disabled:opacity-50 disabled:hover:no-underline hover:underline", props.className)}
    >
      {props.children || "back"}
    </button>
  );
};

export const BaseLink = forwardRef<HTMLAnchorElement, LinkProps>((props: LinkProps, ref) => {
  const navigation = useNavigation();
  const location = useLocation();

  // console.log("equels", equals({ bla: "23", sd: "sdf" }, { bla: "23" }));

  const loadingPathname = (navigation.location?.pathname || "").replace(/\/+$/, "");
  const loadingTo = navigation.location && loadingPathname + navigation.location.search;

  const activePathname = location.pathname.replace(/\/+$/, "");
  const activeTo = activePathname + location.search;

  const toStr1 = typeof props.to === "string" ? props.to : (props.to.pathname || "") + "?" + (props.to.search || "") + (props.to.hash ? '#' + props.to.hash : '');
  const toQuery = (toStr1.split("?")[1] || "").replace(/\/+$/, "");
  const pathNameInput = (toStr1.split("?")[0] || "").replace(/\/+$/, "");
  const pathName = pathNameInput === "." ? activePathname : pathNameInput;
  const toStr = pathName + (toQuery ? "?" + toQuery : "");
  // const

  // if (pathName.includes("month")) {
  //   console.log("path", pathName);
  // }
  const isActiveParams = props.paramsActive !== undefined ? props.paramsActive : !props.exact || toQuery === location.search.slice(1);
  const isActive = props.exact
    ? toStr === activeTo
    : props.end
      ? pathName === activePathname && isActiveParams
      : activePathname.startsWith(pathName) && isActiveParams;
  const isPending =
    loadingTo && (props.exact ? toStr === loadingTo : props.end ? pathName === loadingPathname : loadingPathname.startsWith(pathName));

  if (props.log) {
    console.log("exact", props.exact);
    console.log("end", props.end);
    console.log("pathnaeinput", pathNameInput);
    console.log("activepathnae", activePathname);
    console.log("pathname", pathName);
    console.log("toStr", toStr);
    console.log("loadingTo", loadingTo);
    console.log("activeTo", activeTo);
  }
  // const isActive = pathName === activePathname;
  // const isPending = pathName === loadingPathname;

  return (
    <RemixLink
      aria-current={isActive}
      aria-busy={isPending}
      data-loading={isPending}
      {...(removeObjectKeys(props, "end", "log", "paramsActive", "exact") as any)}
      ref={ref}
      to={toStr}
    />
  );
});
