import type { ReactNode } from "react";
import React, { Fragment, useState } from "react";
import type { ComputePositionConfig } from "@floating-ui/react";
import {
  flip,
  offset,
  safePolygon,
  shift,
  useClick,
  useDismiss,
  useFloating,
  useHover,
  useInteractions,
  useRole,
  useTransitionStatus,
} from "@floating-ui/react";
import { twMerge } from "tailwind-merge";
import { IoInformationCircleOutline } from "react-icons/io5";

export const DefaultInfoIcon = (props: { className?: string }) => (
  <IoInformationCircleOutline className={twMerge("w-5 h-5 text-primary", props.className)} />
);

export const Tooltip = (props: {
  children: ReactNode;
  description: ReactNode;
  className?: string;
  options?: Partial<ComputePositionConfig>;
  lockScroll?: boolean;
}) => {
  const [open, setOpen] = useState(false);
  const { x, y, refs, strategy, context } = useFloating({
    middleware: [offset(10), flip(), shift() /*arrow({ element: arrowRef })*/],
    placement: "top",
    open: open,
    onOpenChange: setOpen,
    ...props.options,
  });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    useClick(context),
    useHover(context, { handleClose: safePolygon() }),
    useRole(context),
    useDismiss(context),
  ]);

  const { isMounted, status } = useTransitionStatus(context, { duration: 200 });

  if (!props.description) return <div className={props.className}>{props.children}</div>;

  return (
    <Fragment>
      <span className={props.className} ref={refs.setReference} {...getReferenceProps()}>
        {props.children}
      </span>
      {isMounted && (
        <Fragment>
          <div
            ref={refs.setFloating}
            style={{
              position: strategy,
              top: y ?? 0,
              left: x ?? 0,
            }}
            {...getFloatingProps()}
            role="tooltip"
            className={twMerge("z-10 px-2 opacity-0 transition-opacity", status === "open" && "opacity-100")}
          >
            <div className="whitespace-pre-wrap rounded-md bg-slate-700 p-2 text-sm text-slate-50 shadow-md">{props.description}</div>
          </div>
        </Fragment>
      )}
    </Fragment>
  );
};
