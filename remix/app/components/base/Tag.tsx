import React, { ReactNode } from "react";
import { CgClose } from "react-icons/cg";
import { twMerge } from "tailwind-merge";

export const BlueTag = (props: { children: ReactNode; className?: string }) => (
  <div
    className={twMerge(
      "flex flex-row items-center justify-center space-x-1 rounded bg-secondary-tag px-2 py-1 text-sm text-white first-letter:capitalize",
      props.className,
    )}
  >
    {props.children}
  </div>
);

export const TagCloseButton = (props: { onClick: () => void }) => {
  return (
    <button type={"button"} className={"text-sm"} onClick={props.onClick}>
      <CgClose />
    </button>
  );
};

export const GreyTag = (props: { children: ReactNode }) => (
  <div className={"flex flex-row items-center space-x-1 rounded bg-slate-100 p-1 px-2 text-sm"}>{props.children}</div>
);
