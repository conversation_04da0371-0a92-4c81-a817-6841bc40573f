import React, { ReactNode } from "react";
import {
  autoUpdate,
  flip,
  FloatingFocusManager,
  FloatingPortal,
  offset,
  size,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
  useListNavigation,
  useRole,
  useTypeahead,
} from "@floating-ui/react";
import { toInputId } from "~/components/ResourceInputs";

interface SelectOption {
  value: string | number | null;
  label: string;
}

export function FloatingSelect<T extends SelectOption>(props: {
  name?: string;
  options: T[];
  disabled?: boolean;
  defaultValue?: string | number | null;
  onChange?: (val: number) => void;
  className?: string;
  renderOption?: (args: { item: T; active: boolean; selected: boolean }) => ReactNode;
  renderValue?: (args: { item?: T; open: boolean }) => ReactNode;
}) {
  const [isOpen, setIsOpen] = React.useState(false);
  const defaultValueIndex = props.options.findIndex((item) => item.value === props.defaultValue);
  const [activeIndex, setActiveIndex] = React.useState<number | null>(null);
  const [selectedIndex, setSelectedIndex] = React.useState<number | null>(defaultValueIndex < 0 ? null : defaultValueIndex);

  const { refs, floatingStyles, context } = useFloating<HTMLElement>({
    placement: "bottom-start",
    open: isOpen,
    onOpenChange: setIsOpen,
    whileElementsMounted: autoUpdate,
    middleware: [
      offset(5),
      flip({ padding: 10 }),
      size({
        apply({ rects, elements, availableHeight }) {
          Object.assign(elements.floating.style, {
            maxHeight: `${availableHeight}px`,
            minWidth: `${rects.reference.width}px`,
          });
        },
        padding: 10,
      }),
    ],
  });

  const listRef = React.useRef<Array<HTMLElement | null>>([]);
  const listContentRef = React.useRef(props.options.map((opt) => opt.label));
  const isTypingRef = React.useRef(false);

  const click = useClick(context, { event: "mousedown" });
  const dismiss = useDismiss(context);
  const role = useRole(context, { role: "listbox" });
  const listNav = useListNavigation(context, {
    listRef,
    activeIndex,
    selectedIndex,
    onNavigate: setActiveIndex,
    // This is a large list, allow looping.
    loop: true,
  });
  const typeahead = useTypeahead(context, {
    listRef: listContentRef,
    activeIndex,
    selectedIndex,
    onMatch: isOpen ? setActiveIndex : setSelectedIndex,
    onTypingChange(isTyping) {
      isTypingRef.current = isTyping;
    },
  });

  const { getReferenceProps, getFloatingProps, getItemProps } = useInteractions([dismiss, role, listNav, typeahead, click]);

  const handleSelect = (index: number) => {
    setSelectedIndex(index);
    props?.onChange?.(index);
    setIsOpen(false);
  };

  const selectedOption = selectedIndex !== null ? props.options[selectedIndex] : undefined;

  return (
    <>
      {/*<label id="select-label" onClick={() => refs.domReference.current?.focus()}>*/}
      {/*  Select balloon color*/}
      {/*</label>*/}
      {props.name && <input disabled={props.disabled} name={props.name} type={"hidden"} value={selectedOption?.value ?? ""} />}
      <button
        type={"button"}
        tabIndex={0}
        id={props.name ? toInputId(props.name) : undefined}
        ref={refs.setReference}
        disabled={props.disabled}
        className={props.className}
        aria-labelledby="select-label"
        // aria-autocomplete="none"
        style={{ width: 150, lineHeight: 2, margin: "auto" }}
        {...getReferenceProps()}
      >
        {props.renderValue
          ? props.renderValue({ item: selectedOption, open: isOpen })
          : selectedOption
            ? selectedOption.label
            : "Select..."}
      </button>
      {isOpen && (
        <FloatingPortal>
          <FloatingFocusManager context={context} modal={false}>
            <div
              className="bg-slate-50 border border-slate-500 shadow-md"
              ref={refs.setFloating}
              style={{
                ...floatingStyles,
                overflowY: "auto",
                // background: "#eee",
                minWidth: 100,
                borderRadius: 8,
                outline: 0,
              }}
              {...getFloatingProps()}
            >
              {props.options.map((option, i) => (
                <div
                  key={option.value}
                  ref={(node) => {
                    listRef.current[i] = node;
                  }}
                  role="option"
                  tabIndex={i === activeIndex ? 0 : -1}
                  className="group"
                  // aria-checked={}
                  aria-selected={i === selectedIndex && i === activeIndex}
                  style={{
                    cursor: "default",
                    // background: i === activeIndex ? "cyan" : "",
                  }}
                  {...getItemProps({
                    // Handle pointer select.
                    onClick() {
                      handleSelect(i);
                    },
                    // Handle keyboard select.
                    onKeyDown(event) {
                      if (event.key === "Enter") {
                        event.preventDefault();
                        handleSelect(i);
                      }

                      if (event.key === " " && !isTypingRef.current) {
                        event.preventDefault();
                        handleSelect(i);
                      }
                    },
                  })}
                >
                  {props.renderOption ? (
                    props.renderOption({ item: option, selected: i === selectedIndex, active: i === activeIndex })
                  ) : (
                    <div className="flex flex-row justify-between">
                      <span>{option.label || option.value}</span>{" "}
                      <span
                        aria-hidden
                        style={{
                          position: "absolute",
                          right: 10,
                        }}
                      >
                        {i === selectedIndex ? " ✓" : ""}
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </FloatingFocusManager>
        </FloatingPortal>
      )}
    </>
  );
}
