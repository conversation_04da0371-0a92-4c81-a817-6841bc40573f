import React, { Fragment, ReactNode, useRef, useState } from "react";
import { setState, useSearchParams2 } from "~/hooks/use-search-params2";
import { useIsInterative } from "~/hooks/hooks";
import {
  FloatingFocusManager,
  FloatingOverlay,
  FloatingPortal,
  useDismiss,
  useFloating,
  useInteractions,
  useRole,
  useTransitionStatus,
} from "@floating-ui/react";
import { twMerge } from "tailwind-merge";
import type { modals } from "~/misc/parsers/global-state-parsers";
import { ParamLink } from "~/components/meta/CustomComponents";
import { CgClose } from "react-icons/cg";

type DialogProps = {
  className?: string;
  children: ReactNode;
  dialogname: (typeof modals)[number];
};

export const CDialog = (props: DialogProps) => {
  const dialogname = props.dialogname;
  const search = useSearchParams2();
  const isInteractive = useIsInterative();
  const isMenuOpen = search.state.toggle_modal === dialogname;

  const { refs, context, floatingStyles } = useFloating({
    open: isMenuOpen,
    strategy: "fixed",
    onOpenChange: (open) => {
      setState({ toggle_modal: open ? dialogname : undefined }, { replaceRoute: true });
    },
  });

  const { isMounted, status } = useTransitionStatus(context, { duration: 200 });

  const dismiss = useDismiss(context, { outsidePressEvent: "pointerdown" });
  const role = useRole(context, { role: "dialog" });
  const { getReferenceProps, getFloatingProps, getItemProps } = useInteractions([dismiss, role]);

  // in the case of ssr the status is always unmounted, but could still be opened, thats why the check status === "unmounted" is there.
  // const isMountedAndOpen = (status === "unmounted" && isMenuOpen) || status === "open";
  const isMountedAndOpen = (!isInteractive && isMenuOpen) || status === "open";

  if (!isMounted) return <Fragment />;

  return (
    <FloatingFocusManager context={context}>
      <FloatingPortal>
        <FloatingOverlay
          lockScroll
          className={twMerge("fixed inset-0 z-20 flex bg-black/0 transition-colors p-6", isMountedAndOpen && "bg-black/20")}
        >
          <div
            {...getFloatingProps}
            ref={refs.setFloating}
            className={twMerge(
              "z-30 m-auto max-w-md rounded-2xl bg-white p-6 align-middle opacity-0 shadow-2xl transition-all ease-in-out",
              isMountedAndOpen && "opacity-100",
              props.className,
            )}
          >
            {props.children}
          </div>
        </FloatingOverlay>
      </FloatingPortal>
    </FloatingFocusManager>
  );
};

export const DialogCloseButton = () => (
  <ParamLink paramState={{ toggle_modal: undefined }} className="p-2">
    <CgClose className="w-4 h-4" />
  </ParamLink>
);

export const SimpleDialog = (props: { className?: string; children: ReactNode; open: boolean; onOpenChange: (open: boolean) => void }) => {
  const isInteractive = useIsInterative();
  const isDraggingOutsideRef = useRef(false);
  const [isPointerDownInside, setIsPointerDownInside] = useState(false);
  const isMenuOpen = props.open;

  const { refs, context, floatingStyles } = useFloating({
    open: isMenuOpen,
    strategy: "fixed",
    onOpenChange: (open) => {
      props.onOpenChange(open);
    },
  });

  const { isMounted, status } = useTransitionStatus(context, { duration: 200 });

  const dismiss = useDismiss(context, { outsidePressEvent: "pointerdown" });
  const role = useRole(context, { role: "dialog" });
  const { getReferenceProps, getFloatingProps, getItemProps } = useInteractions([dismiss, role]);

  // in the case of ssr the status is always unmounted, but could still be opened, thats why the check status === "unmounted" is there.
  // const isMountedAndOpen = (status === "unmounted" && isMenuOpen) || status === "open";
  const isMountedAndOpen = (!isInteractive && isMenuOpen) || status === "open";

  if (!isMounted) return <Fragment />;

  return (
    <FloatingFocusManager context={context}>
      <FloatingPortal>
        <FloatingOverlay
          lockScroll
          className={twMerge("fixed inset-0 z-20 flex bg-black/0 transition-colors p-6", isMountedAndOpen && "bg-black/20")}
        >
          <div
            {...getFloatingProps}
            ref={refs.setFloating}
            className={twMerge(
              "z-30 m-auto max-w-md rounded-2xl bg-white p-6 align-middle opacity-0 shadow-2xl transition-all ease-in-out",
              isMountedAndOpen && "opacity-100",
              props.className,
            )}
          >
            {props.children}
          </div>
        </FloatingOverlay>
      </FloatingPortal>
    </FloatingFocusManager>
  );
};
