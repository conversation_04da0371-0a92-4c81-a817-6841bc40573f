import type { DetailedHTMLProps, SelectHTMLAttributes } from "react";
import React from "react";
import type { IdName } from "~/misc/models";
import { twMerge } from "tailwind-merge";

type SelectProps = DetailedHTMLProps<SelectHTMLAttributes<HTMLSelectElement>, HTMLSelectElement>;

interface Props extends SelectProps {
  items: IdName[];
}

export const IdNameSelect = (props: Props) => {
  const { items } = props;
  const defaultValue = props.defaultValue || undefined;

  const finalItems = [...items];
  if (
    typeof props.defaultValue === "string" &&
    props.defaultValue !== "" &&
    !finalItems.find((activity) => activity.id === props.defaultValue)
  ) {
    finalItems.push({
      id: props.defaultValue,
      name: props.defaultValue.substring(0, 5) + "...",
    });
  }

  const placeholder = props.placeholder && " --  " + props.placeholder + " -- ";

  return (
    <select
      className={twMerge("min-w-[150px] rounded border-slate-200 p-2", !defaultValue && "opacity-60", props.className)}
      {...props}
      placeholder={placeholder}
    >
      {!placeholder && <option value={""}> - </option>}
      {finalItems.map((item) => (
        <option key={item.id} value={item.id}>
          {item.name}
        </option>
      ))}
    </select>
  );
};

// export const SearchParamIdNameSelect = ()
