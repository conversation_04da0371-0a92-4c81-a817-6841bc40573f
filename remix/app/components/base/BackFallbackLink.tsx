import React from "react";
import { useNavigate } from "@remix-run/react";
import { ParamLink, ParamStateLinkProps } from "~/components/meta/CustomComponents";
import { twMerge } from "tailwind-merge";

/**
 * A link/button that tries to go back in browser history, otherwise falls back to a provided link.
 * Useful for side panels, modals, or cancel buttons.
 */
export const BackFallbackLink = (props: ParamStateLinkProps) => {
  const navigate = useNavigate();
  return (
    <ParamLink
      {...props}
      className={twMerge("cursor-pointer", props.className)}
      onClick={(e) => {
        if (window.history.length > 1) {
          e.preventDefault();
          navigate(-1);
        }
      }}
    >
      {props.children}
    </ParamLink>
  );
};
