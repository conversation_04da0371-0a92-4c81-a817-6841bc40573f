import type { Status } from "~/misc/parsers/meta-parser";
import { twMerge } from "tailwind-merge";
import React, { ComponentProps } from "react";
import { XMarkIcon } from "@heroicons/react/20/solid";

const statusColor: Record<Status, string> = {
  success: "text-green-800 bg-green-200",
  error: "text-red-900 bg-red-200",
  warning: "text-slate-800 bg-orange-200",
  info: "text-blue-800 bg-blue-200",
};

export const Alert = (props: ComponentProps<"div"> & { status: Status }) => {
  return <div {...props} className={twMerge("rounded p-3", statusColor[props.status], props.className)} />;
};

export const AlertCloseButton = (props: { onClose: () => void }) => (
  <button type={"button"} className="btn btn-text font-bold" onClick={props.onClose}>
    <XMarkIcon className="h-5 w-5" />
  </button>
);
