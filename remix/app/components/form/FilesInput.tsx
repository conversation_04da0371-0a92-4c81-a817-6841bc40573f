import React, { ComponentProps, Fragment, ReactNode, useEffect, useId, useRef, useState } from "react";
import { IkImage } from "~/components/IkImage";
import { MdChevronLeft, MdChevronRight } from "react-icons/md";
import autoAnimate from "@formkit/auto-animate";
import { RInput } from "~/components/ResourceInputs";
import { twMerge } from "tailwind-merge";
import { FileTargetValue } from "~/domain/file/file-resource";
import { useIsInterative } from "~/hooks/hooks";
import { ConfirmInput, OperationInput } from "~/components/form/DefaultInput";
import { _file } from "~/misc/paths";
import type { FilesAction } from "~/routes/file._index";
import { useBoolean } from "~/hooks/use-boolean";
import { Alert } from "~/components/base/alert";
import { removeObjectKeys } from "~/misc/helpers";
import { refreshFormdata } from "~/components/form/form-hooks";
import { PlusIcon } from "@heroicons/react/20/solid";

export interface FileTargetModel {
  id?: string;
  file_id: string;
  // sort_order: number;
  filename: string;
  deleted_at?: string | null;
}

export const UploadButton = (
  props: Omit<ComponentProps<"input">, "type"> & {
    public?: boolean;
    description?: string;
    onUploaded: (files: { id: string; filename: string }[]) => Promise<void> | void;
    children?: ReactNode;
  },
) => {
  const generatedId = useId();
  const id = props.id || generatedId;
  const isInteractive = useIsInterative();
  const key = useBoolean();
  const [error, setError] = useState<string>();
  const [state, setState] = useState<"ready" | "uploading">("ready");

  const isBusy = state === "uploading";
  const isDisabled = !isInteractive || state === "uploading" || !!props.disabled;
  // return <Fragment />;
  return (
    <div key={key.isOn + ""} className={"relative overflow-hidden h-full"}>
      {error && <Alert status={"error"}>{error}</Alert>}
      {state === "uploading" && <ConfirmInput message={'"There are still files uploading. Continue without files?"'} />}
      <input
        id={id}
        aria-busy={isBusy}
        disabled={isDisabled}
        readOnly={props.readOnly}
        multiple
        type={"file"}
        className="peer inset-0 opacity-0 absolute"
        onChange={async (e) => {
          e.preventDefault();
          setState("uploading");
          setError(undefined);
          const files = e.target.files;
          if (files && files.length > 0) {
            const filesArray = Array.from(files);
            const response = await fetch(_file, {
              body: JSON.stringify({
                files: filesArray.map((file) => ({ file: file.name, public: !!props.public, description: props.description })),
              }),
              method: "post",
              headers: {
                "Content-Type": "application/json",
                // 'Content-Type': 'application/x-www-form-urlencoded',
              },
            });

            const result: FilesAction | undefined = await response.json();

            const fileRecords = result?.files || [];
            try {
              await Promise.all(
                fileRecords.map(async (fileRecord) => {
                  if (!fileRecord) throw new Error("no filerecord");
                  const targetFile = filesArray.find((file) => fileRecord?.filename.includes(file.name));
                  if (!targetFile) throw new Error("no file found");
                  const writeUrl = fileRecord.writeurl;
                  await fetch(writeUrl, {
                    method: "PUT",
                    body: targetFile,
                  });
                }),
              );
              await props.onUploaded(fileRecords);
            } catch (e) {
              const errorMsg = e instanceof Error ? e.name + e.message : e + "";
              setError((e instanceof Error ? "yes errorobj" : "no errorobj") + errorMsg);
            }
            key.toggle();
          }
          setState("ready");
        }}
        required={isInteractive && props.required}
        {...(removeObjectKeys(props, "onUploaded", "required", "onChange", "public", "className", "children") as any)}
      />
      <label aria-busy={isBusy} aria-readonly={props.readOnly} aria-disabled={isDisabled} htmlFor={id} className={props.className}>
        {props.children || "Upload"}
      </label>
    </div>
  );
};

export const InputFilesDefault = (props: {
  disabled?: boolean;
  readonly?: boolean;
  target_id: string;
  small?: boolean;
  target: FileTargetValue;
  description?: string;
  existingFiles?: { id: string; filename: string }[];
  defaultValue?: FileTargetModel[] | null;
  multiple?: boolean;
  onInsert?: (e: React.MouseEvent, path: string) => void;
}) => {
  const uploadId = useId();
  const selectedFiles = useBoolean();
  const [fileTargets, setFileTargets] = useState(props.defaultValue || []);
  const otherFiles = props.existingFiles?.filter((file) => !fileTargets.find((item) => item.file_id === file.id)) || [];
  const animatingParentRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    animatingParentRef.current && autoAnimate(animatingParentRef.current);
  }, []);

  const onInsert = props.onInsert;

  return (
    <div>
      <div
        ref={animatingParentRef}
        className={twMerge("grid grid-cols-2 gap-3", !props.small && "sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5")}
      >
        {fileTargets.map((fileTarget, index) => {
          const fieldIndex = props.target + index;
          const move = (position: number) => {
            const newFiles = [...fileTargets];
            const otherFile = fileTargets[index + position];
            newFiles[index + position] = fileTarget;
            // @ts-ignore
            newFiles[index] = otherFile;
            setFileTargets(newFiles);
            refreshFormdata();
          };

          return (
            <div key={fileTarget.file_id + fileTarget.id} className="flex flex-col gap-3 h-full">
              <div className={twMerge(fileTarget.deleted_at && "opacity-50 ")}>
                {(fileTarget.id || !fileTarget.deleted_at) && (
                  <Fragment>
                    {!!fileTarget.id && <RInput table={"file_target"} field={"id"} index={fieldIndex} value={fileTarget.id} />}
                    <RInput table={"file_target"} field={"data.file_id"} index={fieldIndex} value={fileTarget.file_id} type={"hidden"} />
                    <RInput table={"file_target"} field={"data.target"} index={fieldIndex} value={props.target} type={"hidden"} />
                    <RInput table={"file_target"} field={"data.target_id"} index={fieldIndex} value={props.target_id} type={"hidden"} />
                    <RInput table={"file_target"} field={"data.sort_order"} index={fieldIndex} value={index} type={"hidden"} />
                    {!!fileTarget.deleted_at && <OperationInput table={"file_target"} index={fieldIndex} value={"delete"} />}
                  </Fragment>
                )}
                <IkImage w={200} h={200} path={fileTarget.filename} className="h-full" />
              </div>
              <div className="flex flex-row justify-between gap-1">
                <button
                  type="button"
                  className={twMerge("btn btn-basic", onInsert && "px-1")}
                  disabled={index === 0 || props.disabled || props.readonly}
                  onClick={() => move(-1)}
                  aria-label={"move left"}
                >
                  <MdChevronLeft />
                </button>
                <button
                  className={twMerge("btn btn-basic", onInsert && "text-xs px-2")}
                  type="button"
                  disabled={props.disabled || props.readonly}
                  onClick={() => {
                    const newFiles = [...fileTargets];
                    const newFile = newFiles[index];
                    if (newFile) {
                      newFile.deleted_at = fileTarget.deleted_at ? null : "delete";
                    }
                    setFileTargets(newFiles);
                    refreshFormdata();
                  }}
                >
                  {fileTarget.deleted_at ? "undo" : "delete"}
                </button>
                {onInsert && (
                  <button
                    type={"button"}
                    disabled={props.disabled || props.readonly}
                    className="btn btn-basic text-xs px-2"
                    onClick={(e) => onInsert(e, fileTarget.filename)}
                  >
                    insert
                  </button>
                )}
                <button
                  type="button"
                  className={twMerge("btn btn-basic", onInsert && "px-1")}
                  disabled={index === fileTargets.length - 1 || props.disabled || props.readonly}
                  onClick={() => move(1)}
                  aria-label={"move right"}
                >
                  <MdChevronRight />
                </button>
              </div>
            </div>
          );
        })}
        {!props.readonly && (
          <div className="flex flex-col gap-3">
            <div className="flex-1">
              <UploadButton
                disabled={props.disabled}
                id={uploadId}
                description={props.description}
                public
                accept="image/*"
                className="cursor-pointer rounded bg-slate-200 p-5 py-7 text-center align-middle relative aria-busy:spinner spinner-dark spinner-xl
                hover:bg-slate-300 active:bg-slate-300 aria-busy:animate-pulse disabled:animate-pulse text-xl flex items-center justify-center
                aria-busy:cursor-progress peer-disabled:opacity-80 disabled:hover:bg-slate-200 group w-full h-full flex-1"
                hidden
                onUploaded={(newFiles) => {
                  setFileTargets([
                    ...fileTargets,
                    ...newFiles.map((file) => ({
                      file_id: file.id,
                      filename: file.filename,
                    })),
                  ]);
                  refreshFormdata();
                }}
              >
                <span className="block pb-2 group-aria-busy:hidden">Upload</span>
              </UploadButton>
            </div>
            {!!otherFiles.length && (
              <div className="flex-1">
                <button
                  className="bg-slate-200 text-center flex-1 flex text-xl items-center justify-center w-full h-full py-5 px-3"
                  type={"button"}
                  onClick={selectedFiles.toggle}
                >
                  {selectedFiles.isOn ? (
                    "Close"
                  ) : (
                    <span>
                      Pick existing image
                      <br />({otherFiles.length})
                    </span>
                  )}
                </button>
              </div>
            )}
          </div>
        )}
        {selectedFiles.isOn &&
          otherFiles.map((file) => (
            <button
              type={"button"}
              className="opacity-60 hover:opacity-100 relative group transition-opacity"
              key={file.id}
              onClick={() => {
                setFileTargets([...fileTargets, { file_id: file.id, filename: file.filename }]);
              }}
            >
              <span className="absolute inset-0 text-center flex justify-center items-center text-xl font-bold">
                Add
                <PlusIcon className="w-10 h-10" />
              </span>
              <IkImage w={200} h={200} path={file.filename} className="h-full" />
            </button>
          ))}
      </div>
    </div>
  );
};
