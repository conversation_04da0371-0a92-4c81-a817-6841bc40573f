import React, { useEffect, useMemo, useRef } from "react";
import { useMapContext } from "~/hooks/use-map-context";
import MapboxDraw from "@mapbox/mapbox-gl-draw";
import { feature } from "@turf/turf";
import { HiLocationMarker } from "react-icons/hi";
import { MdDelete } from "react-icons/md";
import { ArrowsRightLeftIcon } from "@heroicons/react/20/solid";
import { useIsInterative } from "~/hooks/hooks";
import { HiddenTypeInput } from "~/components/form/DefaultInput";

const pointToText = (value?: any) => {
  const coordinates = value?.coordinates;
  return coordinates ? (coordinates.toString() as string) : "";
};

const textToPoint = (value?: string | null) => {
  if (!value) return null;
  const [coord1, coord2] = value.split(",");
  return {
    type: "Point" as const,
    coordinates: [coord1 || "", coord2 || ""],
  };
};

export const DefaultFieldInputGeom = (props: { name: string; defaultValue?: any; required?: boolean }) => {
  const isJsLoaded = useIsInterative();
  const inputRef = useRef<HTMLInputElement>(null);
  const defaultValueAsText = pointToText(props.defaultValue);

  const { map } = useMapContext();

  const drawControl = useMemo(() => {
    return new MapboxDraw({
      displayControlsDefault: false,
      controls: {},
    });
  }, []);

  const onUpdateInMap = (e: any) => {
    const firstFeature = e.features[0] || null;
    drawControl.deleteAll();
    drawControl.add(firstFeature);
    if (inputRef.current) {
      inputRef.current.value = firstFeature ? pointToText(firstFeature.geometry) : "";
    }
  };

  const onDelete = () => {
    if (inputRef.current) {
      inputRef.current.value = "";
    }
  };

  useEffect(() => {
    if (map) {
      map.on("draw.create", onUpdateInMap);
      map.on("draw.update", onUpdateInMap);
      map.on("draw.delete", onDelete);
      map.addControl(drawControl, "top-left");
      map.scrollZoom.enable();
      map.dragPan.enable();
      if (props.defaultValue) {
        drawControl.add(feature(props.defaultValue) as any);
      }
      return () => {
        map?.off("draw.create", onUpdateInMap);
        map?.off("draw.update", onUpdateInMap);
        map?.off("draw.delete", onDelete);
        map?.removeControl(drawControl);
      };
    }
  }, [drawControl, map, props.defaultValue]);

  return (
    <div className="formcontrol flex flex-wrap items-center bg-white">
      <span className="pl-2 font-semibold">Coordinates</span>
      <input
        name={props.name}
        ref={inputRef}
        className="formcontrol-input flex-1 p-2"
        required={props.required}
        onChange={(e) => {
          const value = e.target.value;
          drawControl.deleteAll();
          const geomValue = textToPoint(value);
          if (geomValue) {
            drawControl.add(feature(geomValue as any));
          }
        }}
        defaultValue={defaultValueAsText}
      />
      <button
        className="btn"
        type={"button"}
        disabled={!defaultValueAsText || !isJsLoaded}
        aria-label={"swap coordinates"}
        onClick={() => {
          if (!inputRef.current) return;
          const value = inputRef.current.value;
          if (!value) return;
          drawControl.deleteAll();
          const point = textToPoint(value);
          if (point) {
            point.coordinates = [point.coordinates[1] || "", point.coordinates[0] || ""];
            drawControl.add(feature(point as any));
          }
          inputRef.current.value = pointToText(point);
        }}
      >
        <ArrowsRightLeftIcon className="h-4 w-4" />
      </button>
      <button
        className="btn"
        type={"button"}
        disabled={!isJsLoaded}
        aria-label={"select-point"}
        onClick={() => {
          drawControl.changeMode("draw_point");
        }}
      >
        <HiLocationMarker />
      </button>
      <button
        className="btn btn-basic"
        type={"button"}
        disabled={!isJsLoaded}
        aria-label={"select-point"}
        onClick={(e) => {
          drawControl.deleteAll();
          if (inputRef.current) {
            inputRef.current.value = "";
          }
        }}
      >
        <MdDelete />
      </button>
      <HiddenTypeInput name={props.name} value={"__pg_coordinates__"} />
    </div>
  );
};
