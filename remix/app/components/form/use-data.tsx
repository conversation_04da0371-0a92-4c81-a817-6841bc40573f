import { useState } from "react";
import type { ExtendedFieldName } from "~/misc/types";

type Value = Partial<Record<ExtendedFieldName | string, any>>;

export function useData<T extends Value = Value>(defaultValue: T | null | undefined, initChanges?: T | false | null) {
  const [changes, setChanges] = useState(initChanges || {});

  const final = { ...defaultValue, ...changes };
  const isEqual = JSON.stringify(final) === JSON.stringify(defaultValue);

  return {
    changes: changes,
    final: final as T,
    isEqual: isEqual,
    merge: (value: Partial<Value>) => {
      const newChanges = { ...changes, ...value };
      setChanges(newChanges);
    },
    set: (value: Partial<Value>) => {
      setChanges(value);
    },
  };
}
