import type { DB, FieldName } from "~/kysely/db";
import {
  clientId,
  clientIdKey,
  confirmMessage<PERSON>ey,
  fixedResponseIdentifierValue,
  identifierKey,
  redirect<PERSON>ey,
  responseIdentifier<PERSON>ey,
} from "~/misc/vars";
import type { FieldType } from "~/misc/formdata-to-nested-json";
import { fName, removeObjectKeys } from "~/misc/helpers";
import type { StateInput } from "~/misc/parsers/global-state-parsers";
import { dropTransientParams, mergeStateToParams } from "~/misc/parsers/global-state-parsers";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import type { ComponentProps } from "react";

export const operations = ["insert", "update", "delete", "ignore"] as const;
export type Operation = (typeof operations)[number];

export const HiddenTypeInput = (props: { name: FieldName | string; value: FieldType; disabled?: boolean }) => (
  <input type="hidden" name={props.name} value={props.value} disabled={props.disabled} />
);

export const RedirectInput = (props: { value: string; disabled?: boolean }) => (
  <input type="hidden" disabled={props.disabled} name={redirectKey} value={props.value} />
);

export const RedirectParamsInput = (props: { path: string; paramState: Partial<StateInput>; disabled?: string }) => {
  const search = useSearchParams2();
  const redirectParams = new URLSearchParams(search.params);
  if (props.path && props.path !== "./") {
    dropTransientParams(redirectParams);
  }
  mergeStateToParams(redirectParams, props.paramState);
  return <RedirectInput value={props.path + "?" + redirectParams.toString()} />;
};

// export function ParamInput<Key extends keyof StateInput>(props: { name: Key; value: StateInput[Key] }) {
//   if (props.value instanceof Array) props.value.map((value) => <input key={props.name} name={props.name} value={value} />);
//   return <input name={props.name} value={props.value ? "" : props.value + ""} />;
// }

export const IdentifierInput = (props: { value: string; disabled?: boolean; form?: string }) => (
  <input type="hidden" disabled={props.disabled} name={identifierKey} form={props.form} value={props.value} />
);

export const ResponseIdentifierInput = (props: {
  value?: string | typeof fixedResponseIdentifierValue;
  disabled?: boolean;
  form?: string;
}) => (
  <input
    type="hidden"
    disabled={props.disabled}
    name={responseIdentifierKey}
    form={props.form}
    value={props.value || fixedResponseIdentifierValue}
  />
);

export const ConfirmInput = (props: { message: string }) => {
  return <input name={confirmMessageKey} value={props.message} type={"hidden"} />;
};

export const OperationInput = (
  props: {
    table: keyof DB;
    index?: number | string;
    defaultChecked?: boolean;
    value: Operation;
  } & Omit<ComponentProps<"input">, "value">,
) => (
  <input
    {...removeObjectKeys(props, "table", "index")}
    type={props.type || "hidden"}
    name={props.name || fName(props.table, "operation", props.index || 0)}
    value={props.value}
    defaultChecked={props.defaultChecked}
  />
);

export const ClientIdInput = (props: { disabled?: boolean }) => <input {...props} type={"hidden"} name={clientIdKey} value={clientId} />;

// export const ActionNameInput = (props: { action: keyof typeof actionHandlers; index?: string | number }) => (
//   <input type={"hidden"} name={aTypeName(props.index)} value={props.action} />
// );
