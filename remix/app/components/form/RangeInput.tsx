import React, { useState } from "react";
import { ranges, units } from "~/domain/diving-site/diving-site";
import { HiddenTypeInput } from "~/components/form/DefaultInput";
import { useIsInterative } from "~/hooks/hooks";
import { fName } from "~/misc/helpers";
import { toInputId } from "~/components/ResourceInputs";

export const mapValueToRange = (value?: string | null) => {
  const [lower, upper] = (value || "").replace(/[[\]() ]*/g, "").split(",");
  const upperFinalValue = upper && Number.parseInt(upper) - 1 + "";
  return [lower, upperFinalValue];
};

export const getDurationInDays = (durationInHours?: string | null) => {
  const duration = mapValueToRange(durationInHours);
  const finalDuration = duration[0] || duration[1];
  return finalDuration ? Math.ceil(Number(finalDuration) / 24) : 1;
};

export const rangeToText = (unit: string, value?: Array<string | undefined> | null) => {
  const [lower, upper] = value || [];

  if (!lower && !upper) return "";
  if (lower === upper) return `${lower}${unit}`;
  if (lower && upper) return `${lower}-${upper} ${unit}`;
  if (lower) return `${lower}${unit} or more`;
  if (upper) return `${upper}${unit} or less`;
  return "";
};

export const rangeValueToText = (unit: string, value?: string | null) => {
  const range = mapValueToRange(value);
  return rangeToText(unit, range);
};

interface Props {
  data: Record<keyof typeof ranges, string | undefined | null>;
  label?: string;
  name: keyof typeof ranges;
  min?: number;
  max?: number;
  required?: boolean;
  disabled?: boolean;
  className?: string;
}

export const RangeInput = (props: Props) => {
  const isJsLoaded = useIsInterative();
  const value = props.data[props.name] || "";

  const inputName = fName("diving_site", `data.${props.name}`);
  const inputId = toInputId(inputName);

  const [lowerBoundExtracted, upperBoundExtracted] = mapValueToRange(value);

  const [lower, setLower] = useState(lowerBoundExtracted || "");
  const [upper, setUpper] = useState(upperBoundExtracted || "");

  const required = props.required && !lower && !upper;

  const range = ranges[props.name];

  const min = isJsLoaded ? lower || props.min : props.min;
  const max = isJsLoaded ? upper || props.max : props.max;

  return (
    <div>
      <label htmlFor={inputId}>
        {props.label || props.name} ({lower || upper ? rangeToText(range.unit, [lower, upper]) : `in ${units[range.unit]}`})
      </label>
      <div className="flex flex-row items-center gap-3">
        <input
          name={inputName + ".from"}
          disabled={props.disabled}
          id={inputId}
          className={props.className || "formcontrol-input w-24"}
          required={required}
          placeholder="from"
          type="number"
          defaultValue={lower}
          min={props.min}
          max={max}
          onChange={(e) => {
            setLower(e.target.value || "");
          }}
        />
        <input
          name={inputName + ".to"}
          disabled={props.disabled}
          className={props.className || "formcontrol-input w-24"}
          placeholder="to"
          type="number"
          defaultValue={upper}
          min={min}
          max={props.max}
          onChange={(e) => {
            setUpper(e.target.value || "");
          }}
        />
        <HiddenTypeInput name={inputName} value={"__pg_int_range__"} />
      </div>
    </div>
  );
};
