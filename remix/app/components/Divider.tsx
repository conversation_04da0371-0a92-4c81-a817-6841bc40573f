import type { ReactNode } from "react";
import React from "react";
import { twMerge } from "tailwind-merge";

export const Divider = (props: { className?: string }) => <div className={twMerge("h-[1px] bg-slate-200", props.className)} />;

export const DividerWithText = (props: { className?: string; children: ReactNode; height?: number }) => {
  return (
    <div className={twMerge("flex flex-row items-center space-x-2", props.className)}>
      <div style={{ height: props.height || 1 }} className={"h-[1px] flex-1 bg-slate-200"} />
      <div className={"whitespace-nowrap text-xs"}>{props.children}</div>
      <div style={{ height: props.height || 1 }} className={"flex-1 bg-slate-200"} />
    </div>
  );
};
export const DividerBig = () => <div className="hidden h-3 bg-secondary-50 md:block" />;
