import type { FeatureKey } from "~/domain/feature/feature";
import { useAppContext } from "~/hooks/use-app-context";
import type { ReactNode } from "react";

export const useFeature = (feature: FeatureKey) => {
  const app = useAppContext();
  return app.features.find((item) => feature === item);
};

export const Feature = (props: { feature: FeatureKey; children: ReactNode }) => {
  const isEnabled = useFeature(props.feature);
  return isEnabled && props.children;
};
