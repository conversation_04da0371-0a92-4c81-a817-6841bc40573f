import { ParamLink, ParamStateLinkProps } from "~/components/meta/CustomComponents";
import { toast } from "~/misc/toast";
import React, { ReactNode } from "react";

export const CopyLink = (
  props: {
    children?: ReactNode;
    className?: string;
    share: { url: string; title: string; text: string };
  } & ParamStateLinkProps,
) => {
  return (
    <ParamLink
      path={props.path}
      className={props.className}
      onClick={(e) => {
        try {
          if (navigator.share) {
            e.preventDefault();
            navigator.share({ title: props.share.title, text: props.share.text, url: props.share.url });
          } else if (navigator.clipboard) {
            e.preventDefault();
            navigator.clipboard.writeText(props.share.url);
            toast("URL copied");
          } else {
            console.log("both share and clipboard apis are not supported");
          }
        } catch (e) {
          console.log("could not handle onclick copy", e);
        }
      }}
    >
      {props.children || "Copy/share"}
    </ParamLink>
  );
};
