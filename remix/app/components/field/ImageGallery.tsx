import React, { Fragment, useEffect, useRef, useState } from "react";
import { createImageUrlForBucketPath, IkImage } from "~/components/IkImage";
import { bigImageQuery } from "~/misc/vars";
import { Spinner } from "~/components/base/base";
import { Dialog, Transition } from "@headlessui/react";
import { CgClose } from "react-icons/cg";
import { useAppContext } from "~/hooks/use-app-context";

const ImageWrapper = (props: { filename: string; active: boolean }) => {
  const { env } = useAppContext();
  const ref = useRef<HTMLImageElement>(null);
  const path = createImageUrlForBucketPath(env.firebase_singapore.storageBucket, props.filename, bigImageQuery);

  useEffect(() => {
    console.log("active", props.active);
    if (props.active) {
      setTimeout(() => {
        ref.current?.scrollIntoView({
          behavior: "auto",
          block: "center",
          inline: "center",
        });
      }, 0);
    }
  }, [props.active]);

  const fallback = (
    <div className="flex h-[300px] w-screen items-center justify-center bg-gray-50 text-center md:w-[500px]">
      <Spinner />
    </div>
  );

  // const fallback = (
  //   <Skeleton height={"300px"} width={{ base: "100vw", md: "500px" }} colorScheme={'gray'} />
  // );

  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <img ref={ref} className="object-contain" src={path} alt="" />
    </div>
  );
};

export const ImageGallery = (props: { fileNames?: string[] | null }) => {
  const images = props.fileNames || [];
  const [activeImage, setActiveImage] = useState<number | null>(null);
  const dialogOpen = activeImage !== null;

  const showImages = images.slice(0, 4);

  const diff = images.length - showImages.length;

  const onClose = () => {
    setActiveImage(null);
    // history.back();
  };

  useEffect(() => {
    const handlePop = (e: PopStateEvent) => {
      setActiveImage(null);
    };
    window.addEventListener("popstate", handlePop);
    return () => {
      window.removeEventListener("popstate", handlePop);
    };
  }, []);

  return (
    <div className="flex flex-col space-y-1">
      <div className="grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4">
        {showImages.map((file: string, index: number) => {
          const lastImage = showImages.length - index === 1;
          return (
            <button
              className="relative h-[150px] w-auto overflow-hidden rounded hover:opacity-80 active:opacity-80"
              type="button"
              key={index}
              onClick={(e) => {
                e.preventDefault();
                setActiveImage(index);
              }}
            >
              {lastImage && !!diff && (
                <div className="absolute flex h-full w-full items-center justify-center bg-black/60 font-bold text-white">
                  <p>{diff} more...</p>
                </div>
              )}
              <IkImage w={300} h={150} className="object-cover w-full h-full" path={file} />
            </button>
          );
        })}
      </div>
      <Transition appear show={dialogOpen} as={Fragment}>
        <Dialog as="div" className={"relative z-10"} onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/90" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full w-full items-center justify-center p-4 text-center">
              <Transition.Child
                as="div"
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <div className="sticky top-0 z-10 w-full">
                  <div className="p-3 flex flex-row items-center justify-between ">
                    <Dialog.Title as="p" className="text-lg font-bold text-white drop-shadow-md">
                      Photos
                    </Dialog.Title>
                    <button
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        onClose();
                      }}
                      className="rounded-full bg-black/80 p-2 text-white hover:font-bold active:font-bold"
                    >
                      <CgClose />
                    </button>
                  </div>
                </div>
                <Dialog.Panel className="w-full max-w-xl transform overflow-hidden text-left align-middle transition-all">
                  <div className="flex flex-col items-center space-y-6 py-6">
                    {images.map((file: string, index: number) => (
                      <ImageWrapper key={index} filename={file} active={activeImage === index} />
                    ))}
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
};
