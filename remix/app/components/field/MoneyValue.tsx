import React from "react";
import type { ArgsWithFormat } from "~/utils/money";
import { formatMoney } from "~/utils/money";
import { useAppContext } from "~/hooks/use-app-context";
import { usePageOverwrites } from "~/utils/remix";

export const Empty = () => <>-</>;

export const useMoneyValue = (args?: ArgsWithFormat) => {
  const context = useAppContext();
  return formatMoney(context, args);
};

export const MoneyValue = (args: ArgsWithFormat) => {
  const moneyValue = useMoneyValue(args);
  return moneyValue ? <>{moneyValue}</> : <Empty />;
};

export const MoneyValueFull = (props: { amount: any; currency: any; locale: string | null }) => {
  const context = useAppContext();
  const selectedCurrency = props.currency;
  const selectedAmount = props.amount;
  const mergedData = usePageOverwrites();

  return (
    <div className="grid grid-cols-2 gap-y-1 gap-x-3 text-sm">
      <span className="text-slate-500">Selected currency</span>
      <span>
        <MoneyValue nativeAmount={selectedAmount} nativeCurrency={selectedCurrency} toCurrency={selectedCurrency} locale={props.locale} />
      </span>
      <span className="text-slate-500">Base currency operator</span>
      <span>
        <MoneyValue
          nativeAmount={selectedAmount}
          nativeCurrency={selectedCurrency}
          toCurrency={mergedData.base_currency}
          locale={props.locale}
        />
      </span>
      <span className="text-slate-500">Your currency</span>
      <span>
        <MoneyValue
          nativeAmount={selectedAmount}
          nativeCurrency={selectedCurrency}
          toCurrency={context.currency.user}
          locale={props.locale}
        />
      </span>
    </div>
  );
};
