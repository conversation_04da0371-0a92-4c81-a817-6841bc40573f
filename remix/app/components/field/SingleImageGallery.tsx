import React, { Fragment, useEffect, useState } from "react";
import { createImageUrlForBucketPath, IkImage } from "~/components/IkImage";
import { bigImageQuery } from "~/misc/vars";
import { useAppContext } from "~/hooks/use-app-context";
import { Dialog, Transition } from "@headlessui/react";
import { twMerge } from "tailwind-merge";

interface SingleImageGalleryProps {
  fileNames?: string[] | null;
}

export const SingleImageGallery = (props: SingleImageGalleryProps) => {
  const images = props.fileNames || [];
  const [activeIndex, setActiveIndex] = useState(0);
  const [dialogOpen, setDialogOpen] = useState(false);
  const { env } = useAppContext();

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowLeft") {
        navigatePrev();
      } else if (e.key === "ArrowRight") {
        navigateNext();
      } else if (e.key === "Escape" && dialogOpen) {
        setDialogOpen(false);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [activeIndex, dialogOpen, images.length]);

  // No images to display
  if (images.length === 0) {
    return null;
  }

  const navigateNext = () => {
    setActiveIndex((prev) => (prev + 1) % images.length);
  };

  const navigatePrev = () => {
    setActiveIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const openDialog = () => {
    setDialogOpen(true);
  };

  const closeDialog = () => {
    setDialogOpen(false);
  };

  // Create image URL for the active image
  const getImageUrl = (filename: string) => {
    return createImageUrlForBucketPath(env.firebase_singapore.storageBucket, filename, bigImageQuery);
  };

  return (
    <div className="flex flex-col gap-4 w-full">
      {/* Main gallery view */}
      <div className="relative w-full h-[250px] md:h-[350px] lg:h-[500px] rounded-lg overflow-hidden">
        {/* Left arrow navigation */}
        <button
          type="button"
          className={twMerge(
            "absolute left-3 md:left-4 top-1/2 -translate-y-1/2 w-8 h-8 md:w-10 md:h-10 flex items-center justify-center bg-black/50 text-white rounded-full z-10 transition-colors",
            "hover:bg-black/70 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
            images.length <= 1 &&
              "opacity-50 cursor-not-allowed relative before:content-[''] before:absolute before:top-0 before:left-1/2 before:w-0.5 before:h-full before:bg-white/70 before:rotate-45",
          )}
          onClick={navigatePrev}
          aria-label="Previous image"
          aria-disabled={images.length <= 1}
          disabled={images.length <= 1}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="w-5 h-5 md:w-6 md:h-6"
          >
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </button>
        <div className="w-full h-full flex items-center justify-center cursor-pointer" onClick={openDialog}>
          <img
            src={getImageUrl(images[activeIndex]!)}
            alt={`Product image ${activeIndex + 1} of ${images.length}`}
            className="max-w-full max-h-full object-contain transition-transform duration-300 hover:scale-[1.02]"
          />
        </div>
        <button
          type="button"
          className={twMerge(
            "absolute right-3 md:right-4 top-1/2 -translate-y-1/2 w-8 h-8 md:w-10 md:h-10 flex items-center justify-center bg-black/50 text-white rounded-full z-10 transition-colors",
            "hover:bg-black/70 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
            images.length <= 1 &&
              "opacity-50 cursor-not-allowed relative before:content-[''] before:absolute before:top-0 before:left-1/2 before:w-0.5 before:h-full before:bg-white/70 before:rotate-45",
          )}
          onClick={navigateNext}
          aria-label="Next image"
          aria-disabled={images.length <= 1}
          disabled={images.length <= 1}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="w-5 h-5 md:w-6 md:h-6"
          >
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </button>
        <div className="absolute bottom-4 right-4 px-2 py-1 bg-black/60 text-white text-sm rounded">
          {activeIndex + 1} / {images.length}
        </div>
      </div>
      {images.length > 1 && (
        <div className="flex gap-2 flex-wrap overflow-x-auto pb-1 scrollbar-thin">
          {images.map((filename, index) => (
            <button
              key={index}
              type="button"
              className={
                "w-16 h-16 md:w-20 md:h-20 flex-shrink-0 rounded overflow-hidden aria-current:border-secondary border-2 border-transparent cursor-pointer p-0 transition-colors"
              }
              onClick={() => setActiveIndex(index)}
              aria-label={`View image ${index + 1}`}
              aria-current={index === activeIndex}
            >
              <IkImage path={filename} w={100} h={100} className="w-full h-full object-cover" />
            </button>
          ))}
        </div>
      )}

      {/* Dialog/Modal view */}
      <Transition appear show={dialogOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeDialog}>
          {/* Backdrop */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/90" />
          </Transition.Child>

          {/* Dialog content */}
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="relative w-full max-w-5xl max-h-[90vh] overflow-hidden">
                  {/* Close button */}
                  <button
                    type="button"
                    className="absolute top-3 right-3 md:top-4 md:right-4 z-10 w-8 h-8 md:w-10 md:h-10 flex items-center justify-center bg-black/50 text-white rounded-full transition-colors hover:bg-black/70"
                    onClick={closeDialog}
                    aria-label="Close dialog"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="w-5 h-5 md:w-6 md:h-6"
                    >
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </button>

                  {/* Dialog image container */}
                  <div className="relative w-full h-full flex items-center justify-center">
                    {/* Left arrow navigation */}
                    <button
                      type="button"
                      className={twMerge(
                        "absolute left-3 md:left-4 top-1/2 -translate-y-1/2 w-10 h-10 md:w-[50px] md:h-[50px] flex items-center justify-center bg-black/50 text-white rounded-full z-10 transition-colors",
                        "hover:bg-black/70 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
                        images.length <= 1 &&
                          "opacity-50 cursor-not-allowed relative before:content-[''] before:absolute before:top-0 before:left-1/2 before:w-0.5 before:h-full before:bg-white/70 before:rotate-45",
                      )}
                      onClick={navigatePrev}
                      aria-label="Previous image"
                      aria-disabled={images.length <= 1}
                      disabled={images.length <= 1}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="w-5 h-5 md:w-7 md:h-7"
                      >
                        <polyline points="15 18 9 12 15 6"></polyline>
                      </svg>
                    </button>

                    {/* Dialog image */}
                    <img
                      src={getImageUrl(images[activeIndex]!)}
                      alt={`Product image ${activeIndex + 1} of ${images.length}`}
                      className="max-w-full max-h-[80vh] object-contain"
                    />

                    {/* Right arrow navigation */}
                    <button
                      type="button"
                      className={twMerge(
                        "absolute right-3 md:right-4 top-1/2 -translate-y-1/2 w-10 h-10 md:w-[50px] md:h-[50px] flex items-center justify-center bg-black/50 text-white rounded-full z-10 transition-colors",
                        "hover:bg-black/70 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
                        images.length <= 1 &&
                          "opacity-50 cursor-not-allowed relative before:content-[''] before:absolute before:top-0 before:left-1/2 before:w-0.5 before:h-full before:bg-white/70 before:rotate-45",
                      )}
                      onClick={navigateNext}
                      aria-label="Next image"
                      aria-disabled={images.length <= 1}
                      disabled={images.length <= 1}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="w-5 h-5 md:w-7 md:h-7"
                      >
                        <polyline points="9 18 15 12 9 6"></polyline>
                      </svg>
                    </button>

                    {/* Image counter */}
                    <div className="absolute bottom-4 right-4 px-2 py-1 bg-black/60 text-white text-sm rounded">
                      {activeIndex + 1} / {images.length}
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
};
