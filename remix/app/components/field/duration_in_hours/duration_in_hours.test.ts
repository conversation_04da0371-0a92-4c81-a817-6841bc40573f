import { expect, test } from "vitest";
import type { DurationObject } from "./duration_in_hours";
import { formatRange, getDurationGroup, getRangeObject } from "./duration_in_hours";

const oneMonth = 24 * 30;
const twoMonths = 2 * oneMonth;

const twoMonthsRange = `[${twoMonths},${twoMonths})`;

interface Expected {
  duration: DurationObject | null;
  format: string;
  group: {
    filter: string;
    product: string;
  };
}

const testCases: Record<string, Expected> = {
  empty: {
    duration: null,
    format: "-",
    group: { filter: "none", product: "none" },
  },
  "[1,4)": {
    duration: { lower: "1", upper: "3", range: true, unit: "hours" },
    format: "1-3 hrs.",
    group: { filter: "Short, 1-3 hrs.", product: "Short trip" },
  },
  "[1,7)": {
    duration: { lower: "1", upper: "6", range: true, unit: "hours" },
    format: "1-6 hrs.",
    group: { filter: "Short, 1-3 hrs.", product: "Short trip" },
  },
  "[4,7)": {
    duration: { lower: "4", upper: "6", range: true, unit: "hours" },
    format: "4-6 hrs.",
    group: { filter: "Half day, 4-6 hrs.", product: "Half day trip" },
  },
  "[24,25)": {
    duration: { lower: "1", upper: "1", range: false, unit: "days" },
    format: "1 day",
    group: { filter: "Full day, 7+ hrs.", product: "Day trip" },
  },
  "[,25)": {
    duration: { lower: "", upper: "1", range: true, unit: "days" },
    format: "1 days or less",
    group: { filter: "none", product: "none" },
  },
  "[14,28)": {
    duration: { lower: "14", upper: "27", range: true, unit: "hours" },
    format: "14-27 hrs.",
    group: { filter: "Full day, 7+ hrs.", product: "Day trip" },
  },
  "[48,49)": {
    duration: { lower: "2", upper: "2", range: false, unit: "days" },
    format: "2 days",
    group: { filter: "Multi day", product: "Multi-day" },
  },
  "[48,50)": {
    duration: { lower: "48", upper: "49", range: true, unit: "hours" },
    format: "48-49 hrs.",
    group: { filter: "Multi day", product: "Multi-day" },
  },
  "[5,8]": {
    duration: { lower: "5", upper: "8", range: true, unit: "hours" },
    format: "5-8 hrs.",
    group: { filter: "Half day, 4-6 hrs.", product: "Half day trip" },
  },
  // [twoMonthsRange]: {
  //   range: { lower: "2", upper: "2", unit: "months" },
  //   format: "2 months",
  // },
};

Object.entries(testCases).forEach(([input, expected]) => {
  test(`${input} should ${JSON.stringify(expected.duration, null, 2)}`, () => {
    expect(getRangeObject(input)).toEqual(expected.duration);
  });
  test(`${input} should format to ${expected.format}`, () => {
    expect(formatRange(getRangeObject(input))).toBe(expected.format);
  });
  test(`${input} should be in group ${expected.group}`, () => {
    expect(getDurationGroup(input)).toEqual(expected.group);
  });
});
