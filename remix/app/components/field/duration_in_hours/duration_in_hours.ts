type Unit = "hours" | "days" | "months";

export const customKey = "custom";
export const customRangeKey = "custom-range";

export interface DurationObject {
  lower: string;
  upper: string;
  range: boolean;
  unit: Unit;
}

export const durationPresets: Record<string, { duration: DurationObject | null; label: string }> = {
  "": {
    duration: null,
    label: "-",
  },
  short: {
    duration: { lower: "1", upper: "3", range: true, unit: "hours" },
    label: "Short, 1-3 hrs.",
  },
  halfday: {
    duration: { lower: "4", upper: "6", range: true, unit: "hours" },
    label: "Half day, 4-6 hrs.",
  },
  fullday: {
    duration: { lower: "7", upper: "24", range: true, unit: "hours" },
    label: "Full day, 7+ hrs.",
  },
  "2days": {
    duration: { lower: "2", upper: "2", range: false, unit: "days" },
    label: "2 days",
  },
  "3days": {
    duration: { lower: "3", upper: "3", range: false, unit: "days" },
    label: "3 days",
  },
  "4days": {
    duration: { lower: "4", upper: "4", range: false, unit: "days" },
    label: "4 days",
  },
  "5days": {
    duration: { lower: "5", upper: "5", range: false, unit: "days" },
    label: "5 days",
  },
  "6days": {
    duration: { lower: "6", upper: "6", range: false, unit: "days" },
    label: "6 days",
  },
  "7days": {
    duration: { lower: "7", upper: "7", range: false, unit: "days" },
    label: "7 days",
  },
  "8days": {
    duration: { lower: "8", upper: "8", range: false, unit: "days" },
    label: "8 days",
  },
  "9days": {
    duration: { lower: "9", upper: "9", range: false, unit: "days" },
    label: "9 days",
  },
  "10days": {
    duration: { lower: "10", upper: "10", range: false, unit: "days" },
    label: "10 days",
  },
};

const shortSingles: Record<Unit, string> = {
  hours: "hr.",
  days: "day",
  months: "month",
};

const shortPlurals: Record<Unit, string> = {
  hours: "hrs.",
  days: "days",
  months: "months",
};

export const getRangeObject = (value?: any): DurationObject | null => {
  if (value === "empty" || !value) return null;
  const [lowerBoundExtracted, upperBoundExtracted] = (value || "").replace(/[[\]() ]*/g, "").split(",");

  const lowerBoundAsNumber = parseFloat(lowerBoundExtracted);
  const upperBounderAsNumber = parseFloat(upperBoundExtracted);

  const inclusiveUpperBound = value.endsWith("]") ? 0 : 1;

  const unit =
    lowerBoundExtracted % 24 === 0 && (isFinite(upperBounderAsNumber) ? upperBoundExtracted % 24 === inclusiveUpperBound : true)
      ? "days"
      : "hours";

  const finalLowerBound = unit === "days" && isFinite(lowerBoundAsNumber) ? lowerBoundAsNumber / 24 : lowerBoundExtracted || "";

  const finalUpperBound = isFinite(upperBounderAsNumber)
    ? unit === "days"
      ? (upperBounderAsNumber - inclusiveUpperBound) / 24
      : upperBounderAsNumber - inclusiveUpperBound
    : upperBoundExtracted || "";

  const isRange = finalLowerBound !== finalUpperBound;

  return {
    lower: finalLowerBound + "",
    upper: finalUpperBound + "",
    range: isRange,
    unit: unit,
  };
};

export const getValueForRange = (range: DurationObject | null) => {
  if (!range) return "empty";
  const lowerBoundAsNumber = parseFloat(range.lower);
  const upperBounderAsNumber = parseFloat(range.upper);

  const finalLowerBound = range.unit === "days" && isFinite(lowerBoundAsNumber) ? lowerBoundAsNumber * 24 : range.lower;

  if (!range.range) return `[${finalLowerBound},${finalLowerBound}]`;

  const finalUpperBound = isFinite(upperBounderAsNumber)
    ? range.unit === "days"
      ? upperBounderAsNumber * 24 + 1
      : upperBounderAsNumber + 1
    : range.upper;

  return `[${finalLowerBound},${finalUpperBound})`;
};

export const formatRange = (range: DurationObject | null) => {
  if (!range) return "-";
  // if (range.lower === '0' && range.upper === '0') return '-';
  if (range.lower === range.upper) {
    if (range.lower === "1") {
      return range.lower + " " + shortSingles[range.unit];
    }
    if (range.lower === "") {
      return "unkown";
    }
    return `${range.lower} ${shortPlurals[range.unit]}`;
  }
  if (range.lower === "") return `${range.upper} ${shortPlurals[range.unit]} or less`;
  if (range.upper === "") return `${range.lower}+ ${shortPlurals[range.unit]}`;
  if (range.upper.startsWith("-")) return "-";

  // special exception for 7+ hours
  if (range.unit === "hours" && range.lower === "7" && range.upper === "24") return "7+ " + shortPlurals[range.unit];

  return `${range.lower}-${range.upper} ${shortPlurals[range.unit]}`;
};

// type FilterGroup = 'Short, 1-3 hrs.' | 'Half day, 4-6 hrs.' | 'Full day, 7+ hrs.' | 'Multi day'

export const getDurationGroup = (value?: string | null) => {
  const [lowerBoundExtracted] = (value || "").replace(/[[\]() ]*/g, "").split(",");

  const lowerBoundAsNumber = parseFloat(lowerBoundExtracted || "");
  if (lowerBoundAsNumber > 24) return { filter: "Multi day", product: "Multi-day" };
  if (lowerBoundAsNumber > 6) return { filter: "Full day, 7+ hrs.", product: "Day trip" };
  if (lowerBoundAsNumber > 3) return { filter: "Half day, 4-6 hrs.", product: "Half day trip" };
  if (lowerBoundAsNumber > 0) return { filter: "Short, 1-3 hrs.", product: "Short trip" };
  return { filter: "none", product: "none" };
};
