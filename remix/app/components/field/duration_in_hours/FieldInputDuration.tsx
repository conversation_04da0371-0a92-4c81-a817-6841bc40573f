import React, { useRef, useState } from "react";
import type { DurationObject } from "~/components/field/duration_in_hours/duration_in_hours";
import {
  customKey,
  customRangeKey,
  durationPresets,
  formatRange,
  getDurationGroup,
  getRangeObject,
  getValueForRange,
} from "~/components/field/duration_in_hours/duration_in_hours";
import { refreshFormdata } from "~/components/form/form-hooks";

export const FieldInputDuration = (props: { defaultValue?: string | null; name: string }) => {
  const { defaultValue } = props;

  const customContainer = useRef<HTMLDivElement | null>(null);
  const fromField = useRef<HTMLInputElement | null>(null);

  const [range, setRange] = useState<DurationObject | null>(getRangeObject(defaultValue));

  const finalValue = getValueForRange(range);
  const finalRange = getRangeObject(finalValue);

  const output = formatRange(finalRange);
  const presetValue = finalRange
    ? Object.entries(durationPresets).find(([key, value]) => {
        return JSON.stringify(finalRange) === JSON.stringify(value.duration);
      })?.[0] || (finalRange?.range ? customRangeKey : customKey)
    : "";

  const showCustom = useRef<boolean>(presetValue === customKey || presetValue === customRangeKey);

  const rangeObjForForm: DurationObject = range || {
    lower: "0",
    upper: "0",
    range: false,
    unit: "hours",
  };

  const setRangeAndOnChange = (value: DurationObject | null) => {
    setRange(value);
    refreshFormdata();
  };

  const durationGroup = getDurationGroup(finalValue);

  return (
    <label className="formcontrol flex w-fit flex-wrap items-center gap-2 pl-3 md:flex-row">
      <span className="font-semibold">Duration:</span>
      <select
        className="formcontrol-input"
        required
        value={presetValue}
        onChange={(e) => {
          const valueKey = e.target.value || "";
          const preset = durationPresets[valueKey];
          const value: DurationObject | null =
            valueKey === customKey || valueKey === customRangeKey
              ? { ...rangeObjForForm, range: valueKey === customRangeKey }
              : preset
                ? preset.duration
                : null;
          setRangeAndOnChange(value);

          if (!customContainer.current || !fromField.current) return;

          if (valueKey === customKey || valueKey === customRangeKey) {
            showCustom.current = true;
            customContainer.current.style.display = "inherit";
            fromField.current?.focus();
            fromField.current?.setSelectionRange(0, 3);
          }
          if (valueKey === "-") {
            showCustom.current = false;
            customContainer.current.style.display = "none";
          }
        }}
      >
        {Object.entries(durationPresets).map(([key, value]) => (
          <option value={key} key={key}>
            {value.label}
          </option>
        ))}
        <option value={customKey}>Custom</option>
        <option value={customRangeKey}>Custom range</option>
      </select>
      <div className={showCustom.current ? "" : "hidden"} ref={customContainer}>
        <div className="flex flex-row items-center">
          <input
            className="formcontrol-input w-20"
            type="number"
            required
            ref={fromField}
            step={1}
            placeholder={rangeObjForForm ? "from" : "duration"}
            value={rangeObjForForm.lower}
            onChange={(e) => setRangeAndOnChange({ ...rangeObjForForm, lower: e.target.value })}
          />
          {rangeObjForForm.range && (
            <input
              className="formcontrol-input w-20"
              min={rangeObjForForm.lower}
              type="number"
              step={1}
              placeholder="to"
              value={rangeObjForForm.upper}
              onChange={(e) => setRangeAndOnChange({ ...rangeObjForForm, upper: e.target.value })}
            />
          )}
          <select
            className="formcontrol-input"
            value={rangeObjForForm.unit}
            onChange={(e) => setRangeAndOnChange({ ...rangeObjForForm, unit: e.target.value as "days" | "hours" })}
          >
            <option value={"hours"}>Hours</option>
            <option value={"days"}>Days</option>
          </select>
        </div>
      </div>
      <input type={"hidden"} name={props.name} value={finalValue} />
    </label>
  );
};
