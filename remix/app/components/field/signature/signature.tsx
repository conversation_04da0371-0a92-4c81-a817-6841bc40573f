import { Fragment, ReactNode, useEffect, useRef, useState } from "react";
import type { PointGroup } from "signature_pad";
import SignaturePad from "signature_pad";
import type { InputProps } from "~/types/input";
import { useIsInterative } from "~/hooks/hooks";

const signaturePadEvents = ["beginStroke", "endStroke", "beforeUpdateStroke", "afterUpdateStroke"];

export const Signature = (
  props: InputProps & {
    defaultValue?: PointGroup[] | null;
    label?: ReactNode;
  },
) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const signaturePadRef = useRef<SignaturePad>(null);

  const [signature, setSignature] = useState<PointGroup[] | null | undefined>(null);
  const isJsLoaded = useIsInterative();

  useEffect(() => {
    if (canvasRef.current) {
      const signaturePad = new SignaturePad(canvasRef.current, { backgroundColor: "white", penColor: "black" });
      if (props.defaultValue) {
        signaturePad.fromData(props.defaultValue);
      }
      signaturePad.addEventListener("endStroke", () => {
        const data = signaturePadRef.current?.toData();
        setSignature(data ? [...data] : null);
      });

      // @ts-ignore
      signaturePadRef.current = signaturePad;
      if (props.readOnly || props.disabled) {
        signaturePad.off();
      }
      return () => signaturePad.off();
    }
  }, [props.defaultValue, props.disabled, props.readOnly]);

  const signData = signaturePadRef.current?.toData();
  // console.log("signdata", signData?.length, signData);

  const getDefaultValue = () => {
    const signaturePad = signaturePadRef.current;
    if (signature?.length && signaturePad)
      return JSON.stringify({
        source: signaturePad.toData(),
        base64: signaturePad.toDataURL(),
      });
    return "";
  };

  const inputProps = {
    ...props,
    defaultValue: getDefaultValue(),
  };

  const clear = () => {
    setSignature(null);
    signaturePadRef.current?.clear();
  };

  return (
    <div className="overflow-hidden">
      {!inputProps.readOnly && (
        <nav className="flex flex-wrap gap-3 py-1">
          <span>{props.label || "Signature by participant"}</span>
          {/*<button*/}
          {/*  type="button"*/}
          {/*  onClick={() => {*/}
          {/*    logSvg();*/}
          {/*  }}*/}
          {/*>*/}
          {/*  bsf*/}
          {/*</button>*/}
          <button type="button" className="link" onClick={clear} disabled={props.disabled} hidden={!inputProps.defaultValue}>
            clear
          </button>
          {/*<button type="button" className="link" onClick={dataUrl}>*/}
          {/*  svg*/}
          {/*</button>*/}
          {/*<button type="button" className="link" onClick={datPoints}>*/}
          {/*  datapoints*/}
          {/*</button>*/}
          {/*<button type="button" className="link" onClick={logSvg}>*/}
          {/*  log*/}
          {/*</button>*/}
        </nav>
      )}
      <div className="relative">
        {isJsLoaded && (
          <Fragment>
            {/*<input {...inputPropsBase64} type="text" className="absolute bottom-0 -z-10 opacity-0" />*/}
            <input required {...inputProps} type="text" className="absolute bottom-0 -z-10 opacity-0" />
          </Fragment>
        )}
      </div>
      <div className="relative border border-primary print:border-slate-300" style={{ width: 352, height: 202 }}>
        {!signature && (
          <button
            type="button"
            className="absolute inset-0 print:hidden text-center"
            onClick={() => {
              setSignature([]);
            }}
          >
            {isJsLoaded ? "Click to sign" : "Javascript is missing. You can continue and sign at a later moment."}
          </button>
        )}
        <canvas ref={canvasRef} width={350} height={200} className="print:hidden bg-white" />
      </div>
      {/*<div>{signature?.length || 0} draws</div>*/}
      {/*<div>{JSON.stringify(signature, undefined, 2)}</div>*/}
    </div>
  );
};
