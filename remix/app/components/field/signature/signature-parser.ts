import { z } from "zod";
// import type { PointGroup } from "signature_pad";

const signaturePadPointGroupParser = z.object({
  dotSize: z.number(),
  minWidth: z.number(),
  maxWidth: z.number(),
  penColor: z.string(),
  velocityFilterWeight: z.number(),
  points: z
    .object({
      x: z.number(),
      y: z.number(),
      pressure: z.number(),
      time: z.number(),
    })
    .array(),
});

export const signatureParser = z.object({
  base64: z.string(),
  source: signaturePadPointGroupParser.array(),
});
