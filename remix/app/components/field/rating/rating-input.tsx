import React, { Fragment } from "react";
import { StarIcon } from "@heroicons/react/20/solid";

export const stars = [1, 2, 3, 4, 5] as const;
export const starsBackwards = [...stars].reverse();
const starsArrayBackwards = starsBackwards.map((nr) => nr + "");

export const ratingsToAvg = (ratings: number[]) => {
  const totalStars = ratings.reduce((sum, rating) => rating + sum, 0);
  return totalStars / ratings.length;
};

export const StarsOrangeSmall = (props: { rating: number }) => {
  return (
    <div className="flex flex-row text-sm text-slate-500">
      {stars.map((nr) => (
        <div key={nr} className={`flex text-primary ${props.rating >= nr ? "" : "opacity-20"}`}>
          <StarIcon className="h-5 w-5" />
        </div>
      ))}
    </div>
  );
};

const Stars = (props: { stars: number }) => {
  return (
    <div className="flex flex-row gap-1">
      {stars.map((nr) => (
        <div
          key={nr}
          className={`flex rounded-full p-1 text-white
                ${props.stars >= nr ? "bg-green-700" : "bg-gray-200"}`}
        >
          <StarIcon className="h-5 w-5" />
        </div>
      ))}
    </div>
  );
};

export const StarsWithText = (props: { stars: number }) => {
  return (
    <div className="flex flex-row items-center gap-2">
      <StarsOrangeSmall rating={props.stars} />
      {/*<Stars stars={props.stars} />*/}
      {Math.round((props.stars + Number.EPSILON) * 10) / 10}/{stars.length}
    </div>
  );
};

export const RatingInput = (props: { name: string; required?: boolean; defaultValue?: string | number }) => {
  return (
    <div className="rating relative flex w-fit flex-row-reverse">
      {starsArrayBackwards.map((value) => (
        <Fragment key={value}>
          <input
            id={"rating" + value}
            type={"radio"}
            defaultChecked={props.defaultValue ? value + "" === props.defaultValue + "" : undefined}
            className="absolute left-1/2 top-1/2 -translate-x-1/2 opacity-0"
            {...props}
            value={value}
          />
          <label htmlFor={"rating" + value} className="cursor-point relative z-10 text-primary transition-colors">
            <StarIcon className={`h-10 w-10`} />
          </label>
        </Fragment>
      ))}
    </div>
  );
};
