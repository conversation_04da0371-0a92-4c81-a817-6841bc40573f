/* Stars rating */


/*.rating > input:checked ~ label,*/
.rating > label:hover, /* hover current star */
.rating > label:hover ~ label {
    opacity: .8;
}

/*.rating > input:checked ~*/


.rating > label {
    opacity: .3;
}

.rating > input:not(:checked) + label:hover {
    transform: scale(1.2);
    cursor: pointer;
}

/* hover previous stars in list */

.rating > input:checked + label:hover,
.rating > input:checked ~ label:hover,
.rating > input:checked + label,
.rating > input:checked ~ label {
    opacity: 1;
    /*color: theme('colors.primary.DEFAULT');*/
}
