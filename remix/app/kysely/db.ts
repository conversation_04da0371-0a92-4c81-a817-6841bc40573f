import type { ColumnType } from "kysely";
import type { IPostgresInterval } from "postgres-interval";
import type { ActivitySlug } from "~/domain/activity/activity";
import { CallbackName } from "~/domain/callback/callback";
import type { DivingCertificateLevelKey, DivingOrganizationKey } from "~/domain/diving-course/diving-courses.data";
import { FormName } from "~/components/form/form";

type DefaultValue<T> = T extends ColumnType<infer S, infer I, infer U> ? ColumnType<S, I | undefined, U> : ColumnType<T, T | undefined, T>;

type Int8 = ColumnType<string, string | number | bigint, string | number | bigint>;

type Integer = ColumnType<number, string | number, string | number>;

type Interval = ColumnType<IPostgresInterval, IPostgresInterval | number, IPostgresInterval | number>;

type Numeric = ColumnType<number, string | number, string | number>;

type Timestamp = ColumnType<string, Date | string, Date | string>;

type StringArray = ColumnType<string[] | null, string[] | string | null, string[] | string | null>;

export interface DivingLocation {
  id: DefaultValue<string>;
  name: string;
  description: string | null;
  // files: StringArray;
  highlights: string | null;
  region_id: string;
}

export interface DivingSite {
  id: DefaultValue<string>;
  name: string;
  slug: string;
  diving_location_id: string;
  description: string | null;
  // files: StringArray;
  geom: string | null;

  highlights: string | null;

  headline: string | null;
  summary: string | null;

  difficulty_level: number | null;
  difficulty_info: string | null;
  access_via_boat: boolean | null;
  access_via_shore: boolean | null;

  reef: boolean | null;
  wall: boolean | null;
  cave: boolean | null;
  wreck: boolean | null;
  muck: boolean | null;

  open_water: boolean | null;
  night: boolean | null;
  drift: boolean | null;
  macro: boolean | null;
  exploration: boolean | null;
  slope: boolean | null;
  snorkeling: boolean | null;

  ice: boolean | null;
  technical: boolean | null;
  altitude: boolean | null;

  depth_range_in_meters: string | null;
  current_range_in_level: string | null;
  visibility_range_in_meters: string | null;
  temperature_range_in_celsius: string | null;
  sources: string | null;
}

export interface ItemDivingCourse {
  id: DefaultValue<string>;
  created_at: Timestamp;
  item_id: string;
  // product_id: string;
  diving_course_id: string;
}

export interface ProductDivingLocation {
  id: DefaultValue<string>;
  created_at: Timestamp;
  diving_location_id: string;
  product_id: string;
}

export interface UserSession {
  user_id: string | null;
  id: DefaultValue<string>;
  // expires: Timestamp | null;
  // currency_switched: DefaultValue<boolean>;
  created_at: DefaultValue<Timestamp>;
  updated_at: Timestamp | null;
  destroyed_at: DefaultValue<Timestamp>;
  verification_token: string | null;
  verified_at: Timestamp | null;
  session_id: string;
  method: string | null;
  display_name: string | null;
}

export interface OneTimePassword {
  id: DefaultValue<string>;
  created_at: DefaultValue<Timestamp>;
  session_id: string;
  user_id: string;
  verified_at: string | null;
  password: string;
}

export interface XenditPlatform {
  id: string;
  name: string;
  currency_id: string;
  created_at: string;
}

export interface XenditEnvironment {
  id: string;
  xendit_platform_id: string;
  xendit_api_key: string;
  production: boolean;
  created_at: string;
}

export interface XenditSplitRule {
  id: string;
  xendit_environment_id: string;
  xendit_split_rule_id: string;
  xendit_response: any;
  active: boolean | null;
  created_at: string;
}

export interface XenditAccount {
  id: DefaultValue<string>;
  xendit_user_id: string;
  production: boolean;
  main: boolean | null;
  xendit_account_response: any;
  xendit_invoice_callback_token: string;
  xendit_invoice_callback_response: any;
  xendit_platform_id: string;
}

export interface XenditVirtualBankAccount {
  id: DefaultValue<string>;
  xendit_account_id: string;
  xendit_virtual_bank_account_id: string;
  account_number: string;
  obj: any;
  name: string;
  bank_code: string;
}

export interface XenditVirtualAccountPayment {
  id: DefaultValue<string>;
  xendit_virtual_bank_account_id: string;
  xendit_payment_id: string;
  obj: any;
}

export interface Establishment {
  operator_id: string;
  id: DefaultValue<string>;
  short: string | null;
  review_enabled: DefaultValue<boolean>;
  direct_booking_mode: number;
  direct_booking_form_root_id: string | null;
  workflow: number;
  vat_number: string | null;
  geom: string;
  address: string;
  internal_notes: string | null;
  telephone: string | null;
  email: string | null;
  notification_email: string | null;
  whatsapp: string | null;
  website: string | null;
  location_name: string | null;
  default_currency: string | null;
  spot_id: string | null;
  order: number | null;
  bio: string | null;
  published: DefaultValue<boolean>;
  about: string | null;
  xendit_account_id: string | null;
  require_email_verification_for_signing: boolean;
  default_weight_unit: string | null;
  default_shoe_size_unit: string | null;
  default_height_unit: string | null;
  booking_message_templates: string[] | null;
  whatsapp_message_template: string | null;
  direct_booking_window: string;
  default_trip_start_time: string | null;
  default_booking_meeting_time: string | null;
  default_booking_meeting_type: string | null;
  locale: string | null;
  activity_reminder_in_days_before_start: number | null;
  post_activity_email_days_after: number | null;
  tanks: string[] | null;
  blends: string[] | null;
}

export interface InvoiceCount {
  id: DefaultValue<string>;
  establishment_id: string;
  year: number;
  count: number;
}

export interface Inguiry {
  id: DefaultValue<string>;
  created_at: DefaultValue<Timestamp>;
  created_by_user_session_id: string;
  establishment_id: string;
  nr_of_participants?: number;
  product_id: string | null;
  date: string;
  flexible_date: boolean;
  question?: string;
  communication_method: string;
}

export interface Cache {
  key: string;
  updated_at: DefaultValue<Timestamp>;
  response: any;
}

export interface File {
  id: DefaultValue<string>;
  filename: string;
  description: string | null;
  public: boolean;
  created_at: DefaultValue<Timestamp> | null;
  created_by_session_id: string | null;
  created_by_user_session_id: string | null;
  deleted_at: DefaultValue<Timestamp | null>;
  deleted_by_user_session_id: string | null;
}

export interface FileTarget {
  id: DefaultValue<string>;
  file_id: string;
  target: string;
  target_id: string | null;
  sort_order: number;
  created_at: DefaultValue<Timestamp> | null;
  created_by_user_session_id: string | null;
}

export interface Session {
  id: DefaultValue<string>;
  features: string[] | null;
  currency_switched: ColumnType<boolean, boolean | undefined, boolean>;
  env_verified: ColumnType<boolean, boolean | undefined, boolean>;
  selected_user_id: string | null;
  created_at: ColumnType<Timestamp, never, never>;
  updated_at: Timestamp | null;
  expires: Timestamp | null;
  destroyed_at: DefaultValue<Timestamp>;
  flash_success_message: string | null;
  main_menu_pinned: DefaultValue<boolean>;
}

export interface Event {
  id: DefaultValue<string>;
  target_id: string | null;
  type: string;
  session_id: string;
  user_id: string | null;
  url: string;
  tag: string | null;
  created_at: DefaultValue<Timestamp>;
  request: any | null;
  role: string | null;
}

export interface UserEvent {
  id: DefaultValue<string>;
  session_id: string | null;
  user_session_id: string | null;
  ip_address: string | null;
  header_x_forwarded_for: string[] | null;
  connection_remote_address: string | null;
  created_at: DefaultValue<Timestamp>;
}

export interface EntityAction {
  id: DefaultValue<number>;
  action_name: "insert" | "update" | "edit" | "delete" | "order" | "soft_delete" | string;
  entity_name: string;
  entity_id: string;
  user_event_id: string;
  data: Record<string, any> | null;
}

export interface DivingCourse {
  id: DefaultValue<string>;
  name: string;
  description: string | null;
  tag: string | null;
  diving_certificate_organization_key: DivingOrganizationKey;
  diving_certificate_level_key: DivingCertificateLevelKey;
  sort_order: number | null;
}

export interface Region {
  id: DefaultValue<string>;
  name: string;
  // geom: string;
  country_code: string;
  timezone: string | null;
  // files: StringArray;
  published: DefaultValue<boolean>;
}

export interface ProductDivingSite {
  id: DefaultValue<string>;
  created_at: Timestamp;
  diving_site_id: string;
  product_id: string;
}

export interface SpatialRefSys {
  srid: number;
  auth_name: string | null;
  auth_srid: number | null;
  srtext: string | null;
  proj4text: string | null;
}

export interface Spot {
  id: DefaultValue<string>;
  geom: string | null;
  name: string;
  default_currency: string | null;
  // files: StringArray;

  region_id: string;
}

export interface Operator {
  id: DefaultValue<string>;
  name: string;
  enterprise: boolean;
  internal_notes: string | null;
  telephone: string | null;
  email: string | null;
  website: string | null;
  whatsapp: string | null;
  slug: string | null;
}

export interface User {
  id: DefaultValue<string>;
  created_at: DefaultValue<Timestamp> | null;
  created_by_user_session_id: string | null;
  editor: DefaultValue<boolean>;
  admin: DefaultValue<boolean>;
  // administrator: Generated<boolean>;
  features: string[] | null;
  email: string;
  display_name: string | null;
  photo_url: string | null;
  currency_id: string | null;
  deleted_at: DefaultValue<Timestamp | null>;
  password_hash: string | null;
}

export interface Schedule {
  id: DefaultValue<string>;
  range: string;
  available: boolean;
  note: string | null;
  target_id: string;
  days_of_week: number[];
  created_at: Timestamp | null;
  created_by_user_session_id: string | null;
}

export interface Currency {
  id: string;
  conversion_rate_usd: DefaultValue<Numeric>;
  decimals: number;
}

export interface View {
  id: string;
  // created_at: DefaultValue<Timestamp>;
  // created_by_user_session_id: string;
  sort_order: number;
  name: string;
  sorts: any;
  columns: string[];
  operator_id: string;
}

export interface Item {
  id: DefaultValue<string>;
  created_at: string;

  activity_slug: ActivitySlug;
  establishment_id: string;
  title: string | null;
  subtitle: string | null;
  description: string | null;
  tag_id: string | null;
  category_id: string;
  form_root_id: string | null;
  forms_old: FormName[];
  minimum_age: number | null;
  cancellation_policy_in_hours: number | null;
  diving_minimum_logged_dives: Numeric | null;
  info: string | null;

  addon_ids: string[];
  addons_description: string | null;

  // new
  brand: string | null;
}

export interface Product {
  id: DefaultValue<string>;
  root_id: string;
  created_at: Timestamp;
  deleted_at: Timestamp;
  deleted_by_user_session_id: string | null;

  external_identifier: string | null;
  sku: string | null;
  stock: number | null;
  cached_establishment_id: string;
  published: boolean;
  diving_course_level: string | null;
  diving_count: number | null;
  inclusions: string | null;
  exclusions: string | null;
  diving_type: string | null;
  guide_pax_max: number | null;
  itinerary: string | null;
  pickup: boolean;
  pickup_info: string | null;
  boat_dive: boolean;
  shore_dive: boolean;
  night_dive: boolean;
  gear_included: boolean;
  stay: boolean;
  stay_info: string | null;
  required_diving_certificate: string | null;
  guide_pax_min: number | null;
  elearning_included: boolean | null;
  required_experience: string | null;
  duration_in_hours: string | null;
  n_sessions: number | null;

  // new
  color: string | null;
  size: string | null;
  item_id: string;
}

export interface Price {
  id: DefaultValue<string>;
  created_at: string;
  created_by_user_session_id: string | null;
  amount: Numeric;
  currency_id: string;
  amount_usd: Numeric;
}

export interface ProductPrice {
  id: string;
  product_id: string;
  price_id: string;
  name: string | null;
}

export interface Waiver {
  id: DefaultValue<string>;
  establishment_id: string | null;
  description: string | null;
  slug: string;
  type: string;
  // medical: boolean;
  // signature_required: boolean;
  // upload_required: boolean;
  validity_duration: string | null;
  // name: string;
  // language_code: string;
  diving_certificate_organization_key: string | null;
  content_old: any | null;
  sort_order: number | null;
  // markdoc: string;
  // deleted_at: DefaultValue<Timestamp>;
  // deleted_by_user_session_id: string | null;
}

export interface WaiverEstablishment {
  id: string;
  waiver_id: string;
  establishment_id: string;
  validity_duration: string | null;
}

export interface WaiverTranslation {
  id: string;
  waiver_id: string;
  name: string;
  markdoc: string;
  language_code: string;
  questions: [string, string[]][] | null;
  // deleted_at: DefaultValue<Timestamp>;
  // deleted_by_user_session_id: string | null;
  sort_order: number | null;
}

export interface ParticipantWaiver {
  id: string;
  created_at: string;
  waiver_id: string;
  waiver_type: string;
  customer_id: string;
  participant_id: string;
  validity_duration: string | null;
  signature_required: boolean;
  // medical: boolean;
  signed_language_code: string | null;
  upload_required: boolean;
  upload_approved: boolean;
  // medical_approved: boolean;
  // medical_evaluation_required: boolean;
  medical_yes_answers: string[] | null;
  input: any;
}

export interface ParticipationWaiver {
  id: string;
  participant_waiver_id: string;
  sale_item_id: string;
  participant_id: string;
  manually_approved: boolean;
}

export interface FormWaiver {
  id: string;
  waiver_id: string;
  form_id: string;
}

export interface Comment {
  id: DefaultValue<string>;
  date: string;
  content: string;
  target: string;
  target_id: string;
  // participation_id: string | null;
  created_by_user_session_id: string;
  created_at: string;
}

export interface Review {
  id: DefaultValue<string>;
  establishment_id: string;
  activity_slug: ActivitySlug;
  positives: StringArray;
  negatives: StringArray;
  experience_rating: Integer;
  experience: string | null;
  // files: StringArray;
  created_by_user_session_id: string | null;
  created_at: string | null;
  published_at: string | null;
  published_by: string | null;
}

export interface AddonBase {
  name: string;
  allow_change: boolean;
  quantity: number;
  unit: string;
}

export interface Addon extends AddonBase {
  id: DefaultValue<string>;
  price_id: string;
  establishment_id: string;
}

export interface EstablishmentLanguage {
  id: DefaultValue<string>;
  establishment_id: string;
  language_code: string;
}

export interface Boat {
  id: string;
  name: string;
  capacity: number;
  calendar_display_boat_name: boolean;
  establishment_id: string;
  location: string | null;
}

export interface Member {
  id: DefaultValue<string>;
  user_id: string | null;
  establishment_id: string;
  owner: boolean;
  admin: number;
  permissions: string[] | null;
  crew: boolean;
  freelancer: boolean;
  diving_level: number;
  captain: boolean;
  name: string;
  deleted_at: string | null;
  deleted_by_user_session_id: string | null;
}

export interface Message {
  id: string;
  target_id: string;
  message: string;
}

export interface Booking {
  id: string;
  host: string | null;
  id_seq: number;
  direct_booking: boolean;
  vat_rate: number;
  meeting_type: string | null;
  meeting_address: string | null;
  establishment_id: string;
  booking_reference: string | null;
  meeting_time: string | null;
  created_at: Timestamp;
  cart_for_session_id: string | null;
  created_by_user_session_id: string | null;
  message: string | null;
  // discount: number;
  // exclude_discount_for_addons: boolean;
  currency_id: string;
  cancelled_at: Timestamp | null;
  cancelled_by_user_session_id: string | null;
  internal_note: string | null;
  cached_final_amount: number | null;
  cached_final_paid: number | null;
  cached_duration: string | null;
  accepted: boolean | null;
  reviewed_at: string | null;
  reviewed_by: string | null;
  booking_source: string | null;
  hide_price_for_customer: boolean;
}

export interface Invoice {
  id: string;
  invoice_nr: string;
  created_at: string;
  invoice_local_date: string;
  booking_id: string;
  customer_name: string;
  customer_address: string | null;
  customer_address2: string | null;
  customer_country_code: string | null;
  message: string | null;
  intuit_connection_id: string | null;
  intuit_invoice_id: string | null;
  intuit_invoice_response: any;
}

export interface IntuitInvoice {
  id: DefaultValue<string>;
  created_at: DefaultValue<string>;
  realm_id: string;
  intuit_invoice_id: string;
  invoice_id: string;
  response: any;
}

export interface PaymentMethod {
  id: string;
  created_at: Timestamp;
  created_by_user_session_id: string | null;
  deleted_at: string;
  deleted_by_user_session_id: string | null;
  establishment_id: string;
  intuit_connection_id: string | null;
  name: string;
  short: string;
  xendit: boolean;

  key: string;
  default_surcharge_percentage: number;
  fixed: boolean;
}

export interface Payment {
  id: string;
  refreshed_at: Timestamp | null;
  created_at: Timestamp;
  created_by_user_session_id: string;
  error: string | null;
  host: string | null;
  xendit_account_id: string | null;
  xendit_invoice_id: string | null;
  booking_id: string;
  amount: number;
  payment_amount: number | null;
  payment_currency: string | null;
  payment_method_id: string;
  // intuit_payment_id: string | null;
  surcharge_percentage: number;
  url: string | null;
  payed_at: Timestamp | null;
  deleted_at: Timestamp;
  deleted_by_user_session_id: string | null;
}

export interface DeviceToken {
  id: DefaultValue<string>;
  device_id: string;
  token: string;
  created_at: DefaultValue<Timestamp>;
}

export interface UserDeviceToken {
  id: DefaultValue<string>;
  user_id: string;
  device_token_id: string;
}

export interface SessionLink {
  id: DefaultValue<string>;
  session_id: string;
  created_at: DefaultValue<Timestamp>;
  participant_id: string;
}

export interface Address {
  id: DefaultValue<string>;
  place_id: string | null;
  place_details_response: any;
  formatted_address: string | null;
  street_number: string | null; // Nullable fields
  route: string | null;
  sublocality: string | null;
  locality: string | null;
  administrative_area_level_1: string | null;
  administrative_area_level_2: string | null;
  postal_code: string | null;
  country_code: string | null;
  latitude: number | null; // Consider using a more precise type if needed
  longitude: number | null; // Consider using a more precise type if needed
  types: string[] | null; // Use a string array for place types
}

export interface Person {
  id: DefaultValue<string>;
  user_id: string;
  first_name: string;
  last_name: string;
  full_name: ColumnType<string, never, never>;
  created_at: Timestamp;
}

export interface Customer {
  id: DefaultValue<string>;
  establishment_id: string;
  person_id: string;
  created_at: Timestamp;
}

export interface Participant {
  id: DefaultValue<string>;
  created_at: Timestamp;
  booking_id: string | null;
  customer_id: string;
  main_booker: boolean | null;

  cached_signature_waivers_valid: null | boolean;
  cached_read_waivers_valid: null | boolean;
  cached_incomplete_fields: string[] | null;

  import_row_ids: string[] | null;

  // created_by_session_id: string;
  created_by_user_session_id: string;
  phone: string | null;
  emergency_contact_name: string | null;
  emergency_contact_phone: string | null;
  emergency_contact_relationship: string | null;
  country_code: string | null;
  address: string | null;
  stay: string | null;
  room: string | null;
  passport_number: string | null;
  birth_date: string | null;
  gender: string | null;
  diet: string | null;
  food_allergies: string | null;
  insurance: string | null;

  height_value: number | null;
  height_unit: string | null;
  weight_value: number | null;
  weight_unit: string | null;
  shoe_size_value: number | null;
  shoe_size_unit: string | null;
  bcd_size: string | null;

  years_of_experience: string | null;
  diving_certificate_organization: string | null;
  diving_certificate_level: string | null;
  diving_certificate_number: string | null;
  number_of_dives: string | null;
  last_dive_within_months: number | null;
  last_dived_at_old: string | null;
  wetsuit_size: string | null;
  weightbelt_value: number | null;
  weightbelt_unit: string | null;

  my_gear: string[] | null;
  referral_source: string | null;
  allow_contact_for_experience: boolean | null;
  instagram: string | null;

  comment: string | null;

  form_id: string | null;
  verified_at: string | null;
  digital_signing_agreed_at: string | null;
}

export interface ParticipantDay {
  id: DefaultValue<string>;
  participant_id: string;
  date: string;
  rental_checked: boolean;
}

export interface ParticipantToken {
  id: DefaultValue<string>;
  created_at: DefaultValue<string>;
  created_by_user_session_id: string;
  token: DefaultValue<string>;
  participant_id: string;
}

export interface IntuitConnection {
  id: DefaultValue<string>;
  created_at: DefaultValue<string>;
  created_by_user_session_id: string;
  deleted_at: string;
  deleted_by_user_session_id: string | null;
  realm_id: string;
  sandbox: boolean;
  refresh_token: string;
  default_intuit_tax_code_id: string | null;
  intuit_company: CompanyInfo;
  intuit_user: UserInfoResponse;
}

export interface SaleItem {
  id: string;
  created_at: Timestamp;
  created_by_user_session_id: string;
  price_pp: Numeric;
  quantity: Integer;
  discount_percentage: number;
  exclude_discount_for_addons: boolean;
  cached_total_price_amount: number;
  cached_total_tax_amount: number;
  booking_id: string;
  product_id: string | null;
  form_id: string | null;
  duration: string | null;
  description: string | null;
}

export interface Participation {
  id: DefaultValue<string>;
  created_at: Timestamp;
  created_by_user_session_id: string;
  sale_item_id: string;
  participant_id: string | null;
}

export interface Mail {
  id: DefaultValue<string>;
  created_at: Timestamp;
  created_by_user_session_id: string | null;
  establishment_id: string;
  participant_id: string | null;
  sale_item_id: string | null;
  to_email: string;
  to_name: string;
  from_name: string;
  // template_name: string | null;
  success: boolean | null;
  msg: string | null;
}

export interface Signature {
  id: string;
  participant_waiver_id: string;
  signed_at: string;
  signed_by: string | null;
  signed_by_representative: boolean;
  signature: any;
}

export interface Tag {
  id: string;
  created_at: Timestamp;
  establishment_id: string;
  key: string;
  name: string;
}

export interface Category {
  id: string;
  created_at: Timestamp;
  establishment_id: string;
  key: string;
  name: string;
  parent_category_id: string | null;
}

export interface ProductTag {
  id: string;
  tag_id: string;
  product_id: string;
}

export interface Answer {
  id: string;
  // participant_waiver_id: string;
  target_id: string;
  form_name: FormName;
  question: string;
  answer: string;
}

export interface ActivityAddon extends AddonBase {
  id: string;
  sale_item_id: string;
  addon_id: string;
  // allow_change: boolean;
  price_amount: Numeric;
  // amount: number;
}

export interface ParticipationAddon {
  id: string;
  addon_id: string;
  participation_id: string;
  quantity: number;
}

export interface Trip {
  id: string;
  establishment_id: string;
  type: string;
  activity_location: string | null;
  sites: string[] | null;
  start_location: string | null;
  date: string;
  start_time: string | null;
  boat_id: string | null;
  capacity: number | null;
  created_at: string;
  created_by_user_session_id: string;
}

export interface TripAssignment {
  id: string;
  trip_id: string;
  member_id: string | null;
  role: string;
  participation_id: string | null;
  created_at: Timestamp | null;
}

export interface TankAssignment {
  id: string;
  trip_assignment_id: string;
  gas: string;
  quantity: number;
  liters: string;
  sort_order: number | null;
}

export interface Callback {
  id: DefaultValue<string>;
  created_at: DefaultValue<Timestamp>;
  created_by_user_session_id: string | null;
  name: ColumnType<CallbackName, CallbackName, Callback>;
  host: string;
  started_at: string | null;
  delay_in_seconds: number;
  target_id: string | null;
  client_id: string | null;
  handled_at: string | null;
  success: boolean | null;
  msg: string | null;
}

export interface Form {
  id: DefaultValue<string>;
  root_id: string;
  created_at: DefaultValue<string>;
  created_by_user_session_id: string | null;
  deleted_at: DefaultValue<string>;
  deleted_by_user_session_id: string | null;
  filter_activity_slug: string | null;
  filter_beginner_diver: boolean;
  // type: string;
  name: string;
  establishment_id: string | null;
  selectable: boolean;
  sort_order: number | null;
  upload_description: string | null;
  // forms: DefaultValue<FormName[]>;
}

export interface SignupSubmission {
  id: DefaultValue<string>;
  created_at: DefaultValue<string>;
  email_send_at: string | null;
  email: string;
  full_name: string;
  phone: string | null;
  job_title: string | null;
  vat_number: string | null;
  company: string | null;
  country: string | null;
  comment: string | null;
  website: string | null;
  type: string | null;
}

export interface Field {
  id: string;
  created_at: DefaultValue<string>;
  created_by_user_session_id: string | null;
  form_id: string;
  name: string;
  status: number;
}

export interface SortableValue {
  id: DefaultValue<string>;
  value: string;
  sort_order: number;
}

export interface Rentable {
  id: DefaultValue<string>;
  establishment_id: string;
  title: string | null;
  brand: string | null;
  type: string;
  size: string | null;
  color: string | null;
  reference_id: string;
  storage_location: string | null;
  deleted_at: Timestamp;
  deleted_by_user_session_id: string | null;
  first_use_date: string | null;
  service_interval: string | null;
  service_use_count: number | null;
}

export interface RentalAssignment {
  id: DefaultValue<string>;
  date: string;
  participant_id: string;
  rentable_id: string;
}

export interface RentableService {
  id: DefaultValue<string>;
  rentable_id: string;
  service_date: string | null;
  created_at: DefaultValue<Timestamp>;
  created_by_user_session_id: string;
  next_service_date: string | null;
  max_use_count: number | null;
  initial_use_count: number;
}

interface Import {
  id: DefaultValue<string>;
  name: string;
  created_at: Timestamp;
  created_by_user_session_id: string;

  // rows is only available on type level, so we can use that value in the importResource to insert rows in the import_row table.
  rows: any | null;
}

interface ImportRow {
  id: DefaultValue<string>;
  import_id: string;
  data: any;
}

export interface DB {
  diving_location: DivingLocation;
  diving_site: DivingSite;
  user_session: UserSession;
  one_time_password: OneTimePassword;
  intuit_connection: IntuitConnection;
  xendit_platform: XenditPlatform;
  xendit_environment: XenditEnvironment;
  xendit_split_rule: XenditSplitRule;
  xendit_account: XenditAccount;
  xendit_virtual_bank_account: XenditVirtualBankAccount;
  xendit_virtual_bank_account_payment: XenditVirtualAccountPayment;
  establishment: Establishment;
  establishment__language: EstablishmentLanguage;
  cache: Cache;
  session: Session;
  event: Event;
  user_event: UserEvent;
  entity_action: EntityAction;
  diving_course: DivingCourse;
  region: Region;
  tag: Tag;
  price: Price;
  category: Category;
  product__tag: ProductTag;
  item__diving_course: ItemDivingCourse;
  product__diving_site: ProductDivingSite;
  product__diving_location: ProductDivingLocation;
  spatial_ref_sys: SpatialRefSys;
  spot: Spot;
  operator: Operator;
  user: User;
  schedule: Schedule;
  currency: Currency;
  member: Member;
  item: Item;
  product: Product;
  product_price: ProductPrice;
  review: Review;
  addon: Addon;
  address: Address;
  boat: Boat;
  booking: Booking;
  invoice: Invoice;
  // intuit_invoice: IntuitInvoice;
  invoice_count: InvoiceCount;
  activity_addon: ActivityAddon;
  trip: Trip;
  trip_assignment: TripAssignment;
  tank_assignment: TankAssignment;
  person: Person;
  customer: Customer;
  participant: Participant;
  participant_day: ParticipantDay;
  participant_token: ParticipantToken;
  participation: Participation;
  sale_item: SaleItem;
  mail: Mail;
  signup_submission: SignupSubmission;
  answer_old: Answer;
  signature: Signature;
  participation_addon: ParticipationAddon;
  payment_method: PaymentMethod;
  payment: Payment;
  import: Import;
  import_row: ImportRow;
  // message: Message;
  inquiry: Inguiry;
  form: Form;
  field: Field;
  callback: Callback;
  file: File;
  file_target: FileTarget;
  device_token: DeviceToken;
  user__device_token: UserDeviceToken;
  session_link: SessionLink;
  waiver: Waiver;
  waiver_establishment: WaiverEstablishment;
  waiver_translation: WaiverTranslation;
  participant_waiver: ParticipantWaiver;
  participation_waiver: ParticipationWaiver;
  form_waiver: FormWaiver;
  comment: Comment;
  view: View;
  sortable_value: SortableValue;
  rentable: Rentable;
  rental_assignment: RentalAssignment;
  rentable_service: RentableService;
}

type KeysOfUnion<T> = T extends T ? keyof T : never;

export type FieldName = KeysOfUnion<DB[keyof DB]>;

export type TableFieldName = {
  [K in keyof DB]: `${K}.${keyof DB[K] & string}`;
}[keyof DB];
