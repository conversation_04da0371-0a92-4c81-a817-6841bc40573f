import { AliasedRawBuilder, ColumnType, Expression, ExpressionBuilder, ExpressionWrapper, SelectQueryBuilder, sql } from "kysely";
import type { Point } from "~/misc/types";
import { kysely } from "~/misc/database.server";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { defaultTimezone, Timezone } from "~/data/timezones";
import { shuffledAlphaNumbers } from "~/misc/vars";
import { RawBuilder } from "kysely/dist/cjs/raw-builder/raw-builder";
import { Simplify } from "kysely/dist/cjs/util/type-utils";
import type { DB } from "~/kysely/db";

export const notNull = <O>(expr: RawBuilder<Simplify<O> | null>) => expr as RawBuilder<Simplify<O>>;

const minimumSqidLength = 4;

export const nowValue = sql.raw<any>(`(now())`);
export const nowValueAdDate = sql.raw<any>(`(now()::date)`);

type RefWrap = Expression<string | null> | RawBuilder<any> | ExpressionWrapper<any, any, any>;

export const plus = <T>(number1: Expression<T>, number2: Expression<T>) => sql<T>`(${number1} + ${number2})`;
export const multiply = (number1: Expression<any>, number2: Expression<any>) => sql<number>`(${number1} * ${number2})`;
export const subtract = (number1: Expression<any>, number2: Expression<any>) => sql<number>`(${number1} - ${number2})`;
export const divide = (number1: Expression<any>, number2: Expression<any>) => sql<number>`(${number1} / ${number2})`;

export const pInterval = (unit: "days" | "hours" | "minutes" | "seconds" = "days") => sql<any>`(interval '1 ${sql.raw(unit)}')`;

export const addToTimestamp = (
  date: Expression<string | null>,
  quantity: Expression<number | null> | number,
  unit: "days" | "hours" | "minutes" | "seconds" = "days",
) => plus(date, multiply(quantity as any, pInterval(unit)) as any);

export const addToNow = (quantity: Expression<number | null> | number, unit: "days" | "hours" | "minutes" | "seconds" = "days") =>
  addToTimestamp(nowValue, quantity, unit);

export const tomorrowValueAdDate = sql.raw<any>(`(now()::date + 1)`);

export const ascNullsLast = sql`asc nulls last`;
export const ascNullsFirst = sql`asc nulls first`;
export const descNullsLast = sql`desc nulls last`;
export const descNullsFirst = sql`desc nulls first`;

export const emptyTextArray = sql.raw<string[]>("'{}'::text[]");
export const emptyUUIDArray = sql.raw<string[]>("'{}'::uuid[]");
export const emptyDateArray = sql.raw<string[]>("'{}'::date[]");

export const genRandomUuid = kysely.fn<string>("gen_random_uuid", []);

const dateFormats = [
  "DD Mon YYYY, HH24:MI",
  "DD Mon YYYY",
  "DD MM YYYY",
  "DD Mon ''YY",
  "YYYY-MM-DD",
  "YYYY-MM",
  "DD Mon",
  "DD",
  "DD Month YYYY",
  "YYYY.MM.DD",
  "YYYY.MM.DD HH24:MI",
  "YYYY.MM.DD HH24:MI:SS",
  "YYYY-MM-DD HH24:MI",
  "DD Mon ''YY HH24:MI",
] as const;

export const distinct = <T>(ref: Expression<T>) => sql<T>`distinct
${ref}`;

export const formatDate = (ref: RefWrap, format: (typeof dateFormats)[number]) =>
  kysely.fn<string | null>("to_char", [ref, sql.lit(format)]);

type TimeZoneRecord = { timezone: string | null };
export const formatDatetime = (
  ref: RefWrap,
  format: (typeof dateFormats)[number],
  timezone: Timezone | Expression<string | TimeZoneRecord | null>,
) =>
  formatDate(
    kysely.fn("timezone", [typeof timezone === "string" ? sql.lit(timezone) : kysely.fn.coalesce(timezone, sql.lit(defaultTimezone)), ref]),
    format,
  );

export const tstzToDate = (timestampTz: RefWrap, region: RefWrap) =>
  sql<string>`(${formatDatetime(timestampTz, "YYYY-MM-DD", region)}:: date)`;

export const upper = <T extends string | null>(ref: Expression<T>) => sql<T>`(upper (${ref}))`;

export const formatRange = (eb: ExpressionBuilder<any, any>, lowerQb: RefWrap, upperQb: RefWrap) => {
  const isSameMonth = eb(sql`(date_trunc('month', ${lowerQb}))`, "=", sql`(date_trunc('month', ${upperQb}))`);
  const isSameYear = eb(sql`(date_trunc('year', ${lowerQb}))`, "=", sql`(date_trunc('year', ${upperQb}))`);
  const lowerFull = formatDate(lowerQb, "DD Mon YYYY");
  const upperFull = formatDate(upperQb, "DD Mon YYYY");
  const lowerMonth = formatDate(lowerQb, "DD Mon");
  const lowerDays = formatDate(lowerQb, "DD");

  return eb
    .case()
    .when(lowerQb, "=", upperQb)
    .then(lowerFull)
    .when(isSameMonth)
    .then(sql<string>`(${lowerDays} || ' - ' || ${upperFull})`)
    .when(isSameYear)
    .then(sql<string>`(${lowerMonth} || ' - ' || ${upperFull})`)
    .else(sql<string>`(${lowerFull} || ' - ' || ${upperFull})`)
    .end();
};

export const getPayablePercentage = (discountRef: Expression<string | number>) => sql`(1 - (${discountRef}:: numeric / 100))`;

export const least = (...values: RefWrap[]) => kysely.fn<string | null>("least", values);
export const ceil = (value: Expression<number>) => kysely.fn<number>("ceil", [value]);
export const floor = (value: Expression<number>) => kysely.fn<number>("floor", [value]);
export const round = <T extends number | null>(
  value: Expression<T> | RawBuilder<T>,
  decimals: Expression<number> | SelectQueryBuilder<any, any, any> = sql.lit(0),
) => kysely.fn<T>("round", [value, decimals]);

export const toDate = <T extends string | null>(dateTime: Expression<T>) => sql<T extends null ? null : string>`(${dateTime}:: date)`;

export const subtractPercentage = (rawNumber: Expression<any>, discountRef: Expression<any>) =>
  multiply(rawNumber, getPayablePercentage(discountRef));

export const nowDateTimezone = (timezone: Timezone | Expression<string | null>) => formatDatetime(nowValue, "YYYY-MM-DD", timezone);

export const sqid = <T extends string | bigint | number | null>(ref: Expression<T>) =>
  kysely.fn<T extends null ? null : string>("sqids.encode", [
    sql`(array[${ref}])`,
    sql.val(shuffledAlphaNumbers),
    sql.val(minimumSqidLength),
  ]);

export const now = (addMinutes: number) => () => sql.raw<any>(`(now() + '${addMinutes} minutes')`);

export const nowDefaultTimezone = sql.raw(`(now() at time zone '${defaultTimezone}')`);

export const atInfinityLiteralFn = () => sql.lit(at_infinity_value);

export const coalesceIntArray = (column: ExpressionWrapper<unknown, never, unknown>) =>
  sql<number[]>`(coalesce (array_agg(${column}), '{}':: int []))`;

export const arrayAgg = (ref: Expression<string | null>, type: "uuid" | "text" | "date" = "text") =>
  kysely.fn.coalesce(
    kysely.fn<string[]>("array_agg", [ref]),
    type === "uuid" ? emptyUUIDArray : type === "date" ? emptyDateArray : emptyTextArray,
  );

export const coalesceDefaultTimezone = (ref: Expression<string | null>) =>
  kysely.fn.coalesce(ref, sql.raw<Timezone>(`'${defaultTimezone}'`));

export const arrayAggDisinct = <T extends string>(ref: Expression<T | null>) => sql<T[]>`(array_agg(${sql.raw("distinct ")}${ref}))`;

export const stAsGeoJsonPoint = (column: ExpressionWrapper<unknown, never, unknown>) => sql<Point | null>`(ST_asGeoJSON(${column})::jsonb)`;
export const isValidPoint = (column: ExpressionWrapper<unknown, never, unknown>) => sql<
  boolean | null
>`(st_xmin(${column}) >= -180 and st_xmax(${column}) <= 180
    and st_ymin(${column}) >= -90 and st_ymax(${column}) <= 90)`;

export const coalesceTextArray = (ref: Expression<string[] | null>, defaultValue: string[] = []) =>
  kysely.fn.coalesce(ref, sql.raw<string[]>(`'{${defaultValue.map((str) => `"${str}"`).join(",")}}'::text[]`));
// const dayNumberSelect = (date: string, durationRef: Expression<string>) => sql.raw<number>(`('${date}'::date) - (lower(${durationRef}))`);

export const lower = <T extends string | number | null>(exp: Expression<T> | RawBuilder<T>) => kysely.fn<T>("lower", [exp]);

export const dayNumberSelect = <T extends string | null>(date: string, durationRef: Expression<T>) =>
  sql<T extends null ? null : number>`((${sql.raw(`'${date}'::date`)}) - (${lower(durationRef)}))`;

export const jsonPopulateRecodSet = <
  T extends Record<string, any>,
  ArrayDef extends Partial<Record<keyof T, "text" | "bool" | "integer">>,
  A extends string = "arr",
>(
  arr: T[],
  columnDefs: ArrayDef,
  alias?: A,
) => {
  const trimmedArr = arr.map((item) => {
    const newItem = { ...item };
    Object.entries(item).forEach(([key]) => {
      const isIncludedInDef = Object.keys(columnDefs).includes(key);
      if (!isIncludedInDef) {
        delete newItem[key];
      }
    });
    return newItem;
  });
  const columnsDefsStr = Object.entries(columnDefs)
    .filter(([column]) => columnDefs)
    .map(([column, type]) => column + " " + type)
    .join(", ");

  return sql.raw(
    `json_populate_recordset(null::record, '${JSON.stringify(trimmedArr)}') as ${alias || "arr"}(${columnsDefsStr})`,
  ) as any as AliasedRawBuilder<Record<keyof ArrayDef, any>, A>;
};

export const unnestArray = <T extends string, A extends string = "arr">(array: ReadonlyArray<T>, alias?: A) => {
  return sql.raw(`unnest(ARRAY['${array.join("','")}']::text[]) with ORDINALITY ${alias || "arr"}(key, pos)`) as any as AliasedRawBuilder<
    { key: T; pos: number },
    A
  >;
};

export const generateDateSeries = (fromDate: string, toDate: string) =>
  sql.raw<{
    date: string;
  }>(`(select generate_series('${fromDate}'::date, '${toDate}'::date, interval '1 day')::date as date)`);

// export const min = (ref: Expression<string>) => kysely.fn("min", [ref]);

export const extractYear = (ref: Expression<string>) => sql<number>`(extract ( year from ${ref}))`;
export const extractMonth = (ref: Expression<string>) => sql<number>`(extract ( month from ${ref}))`;

export const dateRange = (
  startDate: RawBuilder<string | null> | Expression<string | null>,
  endDate: RawBuilder<string | null> | Expression<string | null>,
) => kysely.fn<string>("daterange", [startDate, endDate, sql.val("[]")]);

// export const isoDow = (date: Expression<string>) => sql.raw('')

// export const unnestArray2 = <T extends object>(array: ReadonlyArray<T>) => {
//   return sql.raw<T>(`unnest(jsonb_array_elements(['${array.join("','")}']::text[])`).as("key");
// };

type ExtractSelectType<T> = T extends ColumnType<infer S, any, any> ? S : T;
export const contextRef = <Table extends keyof DB, Column extends keyof DB[Table] & string>(table: Table, column: Column) => {
  return sql.ref<ExtractSelectType<DB[Table][Column]>>(`${table}.${column}`);
};

// export const localToUtc = (
//   datetime: Expression<string | null> | RawBuilder<string | null>,
//   timezone: Expression<string | null> | RawBuilder<string | null>,
// ) => sql<string | null>`(${datetime} at time zone ${timezone} at time zone 'UTC')`;

export const localToUtc = (
  datetime: Expression<string | null> | RawBuilder<string | null>,
  timezone: Expression<string | null> | RawBuilder<string | null>,
) => {
  return sql<string | null>`((${datetime} at time zone ${timezone})::timestamptz)`;
  // return sql<string | null>`(${datetime} at time zone ${timezone})`;
};
