import { t } from "~/misc/trans";
import { DB } from "~/kysely/db";

export const tableTranslations: Partial<Record<keyof DB, { single: string; plural: string }>> = {
  operator: {
    single: t`operator`,
    plural: t`operators`,
  },
  spot: {
    single: t`hotspot`,
    plural: t`hotspots`,
  },
  user: {
    single: t`user`,
    plural: t`users`,
  },
  establishment: {
    single: t`operator location`,
    plural: t`operator locations`,
  },
  region: {
    single: t`region`,
    plural: t`regions`,
  },
  diving_location: {
    single: t`dive/snorkel location`,
    plural: t`dive/snorkel locations`,
  },
  diving_site: {
    single: t`dive/snorkel site`,
    plural: t`dive/snorkel sites`,
  },
  diving_course: {
    single: t`diving course`,
    plural: t`diving courses`,
  },
  product: {
    single: "Product",
    plural: "Products",
  },
};
