import { <PERSON>, <PERSON><PERSON>, Container, Head, <PERSON><PERSON>, Html, Link, Preview, Section, Text } from "@react-email/components";
import * as React from "react";
import { kysely } from "~/misc/database.server";

import { StyledLink } from "~/components/Mail";
import { Tai<PERSON><PERSON>Custom } from "~/domain/email/TailwindCustom";

const baseUrl = "blala.nl";

export const CodepenChallengersEmail = () => (
  <Html>
    <Head />
    <Preview>#CodePenChallenge: Cubes</Preview>
    <TailwindCustom>
      <Body>
        <Section>
          <Text>blabla</Text>
        </Section>
        <Container>
          <Text>
            <Link>View this Challenge on CodePen</Link>
          </Text>
          <StyledLink>YES YES GOGO!</StyledLink>
          <Heading className="text-primary">
            <strong>This week:</strong> #CodePenChallenge: <Text className="text-xl">Cubes</Text>
          </Heading>
        </Container>
      </Body>
    </TailwindCustom>
  </Html>
);

export const createEmailCOmp = async (participanId: string) => {
  const signature = await kysely
    .selectFrom("participant")
    .innerJoin("participant_waiver", "participant_waiver.participant_id", "participant.id")
    .innerJoin("signature", "signature.participant_waiver_id", "participant_waiver.id")
    .where("participant.id", "=", participanId)
    .selectAll("signature")
    .executeTakeFirstOrThrow();

  return () => (
    <Html>
      <Head />
      <Preview>#CodePenChallenge: Cubes</Preview>
      <TailwindCustom>
        <Body>
          <Section>
            <Text>blabla</Text>
          </Section>
          <Container>
            <Text>
              <Link>View this Challenge on CodePen</Link>
            </Text>
            <Button
              className={`flex items-center justify-center space-x-3 rounded bg-primary p-2 px-3 
            font-semibold text-white transition-colors hover:bg-primary-600 active:bg-primary-dark 
            disabled:opacity-60 peer-checked:text-transparent`}
            >
              YES YES GOGO!
            </Button>
            <Heading className="text-primary">
              <strong>This week:</strong> #CodePenChallenge: <Text className="text-xl">Cubes</Text>
            </Heading>
          </Container>
        </Body>
      </TailwindCustom>
    </Html>
  );
};
