import type { LoaderFunctionArgs } from "@remix-run/router";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { getSessionSimple } from "~/utils/session.server";
import { getMonthObj } from "~/domain/planning/plannings-helpers";
import { memberIsAdminOrOwnerQb, memberQb, QbArgs } from "~/domain/member/member-queries.server";
import { kysely } from "~/misc/database.server";
import { arrayAgg, ascNullsFirst, dateRange, generateDateSeries } from "~/kysely/kysely-helpers";
import { tripCalendarQb } from "~/domain/trip/trip.server";
import { sql } from "kysely";
import { logTime, removeObjectKeys } from "~/misc/helpers";
import { getDbCachedValue } from "~/server/cache/cache.server";
import { getCacheKey } from "~/server/cache/cache.planning.server";
import { flat } from "remeda";
import { unauthorized } from "~/misc/responses";

export const monthLoader = async ({ request, params }: LoaderFunctionArgs, dateStr: string) => {
  const url = new URL(request.url);
  const state = paramsToRecord(url.searchParams);
  const month = getMonthObj(dateStr);

  const timer = logTime("monthloader", false);
  const { session_id } = await getSessionSimple(request);
  timer.timeLog("session");

  const qbArgs: QbArgs = { trx: kysely, ctx: { session_id: session_id } };

  const operatorId = state.persist_operator_id;
  const establishmentId = state.persist_establishment_id;

  const daysOfMonthQb = kysely
    .selectFrom(generateDateSeries(month.firstDateInCalender, month.lastDateInCalender).as("series"))
    .select((eb) => [arrayAgg(eb.ref("series.date"), "date").as("date")]);

  const daysOfMonth = await daysOfMonthQb.executeTakeFirstOrThrow();
  timer.timeLog("daysofmonth");

  if (!operatorId) {
    const myMemberIds = memberQb(qbArgs).select("_member.id");

    const trips = await tripCalendarQb
      .select("trip.date")
      .where("trip.date", ">=", month.firstDateInCalender)
      .where("trip.date", "<=", month.lastDateInCalender)
      .where((tcEb) => {
        const myAssignments = tcEb.selectFrom("trip_assignment").where("trip_assignment.member_id", "in", myMemberIds);
        return tcEb("trip.id", "in", myAssignments.select("trip_assignment.trip_id"));
      })
      .execute();
    timer.timeLog("trips");

    const schedules = await kysely
      .selectFrom("schedule")
      .selectAll("schedule")
      .select((eb) => [
        sql<string | null>`((lower (${eb.ref("schedule.range")})):: date)`.as("from"),
        sql<string | null>`((upper (${eb.ref("schedule.range")})):: date - 1)`.as("to"),
        daysOfMonthQb
          .where((seriesEb) =>
            seriesEb(eb.ref("schedule.range"), "&&", dateRange(seriesEb.ref("series.date"), seriesEb.ref("series.date"))),
          )
          .as("days"),
      ])
      .where((eb) => eb("schedule.range", "&&", dateRange(sql.lit(month.firstDateInCalender), sql.lit(month.lastDateInCalender))))
      .orderBy("schedule.created_at", ascNullsFirst)
      .orderBy("schedule.available", "desc")
      .orderBy("schedule.range", "asc")
      .whereRef("schedule.target_id", "in", myMemberIds)
      .execute();
    timer.timeLog("schedules");

    const newDays = daysOfMonth.date.map((day) => {
      return {
        day: day,
        trips: trips.filter((trip) => trip.date === day),
        unscheduled_participants: null,
        schedules: schedules,
      };
    });

    return {
      days: newDays || [],
      month: month,
    };
  }

  const establishments = await kysely
    .selectFrom("establishment")
    .where((eb) => {
      const operatorCmpr = eb("establishment.operator_id", "=", operatorId);
      if (!establishmentId) return operatorCmpr;
      const establishmentCmpr = eb("establishment.id", "=", establishmentId);
      return eb.and([operatorCmpr, establishmentCmpr]);
    })
    .where("establishment.id", "in", memberIsAdminOrOwnerQb(qbArgs, "read").select("_member.establishment_id"))
    .select(["establishment.id"])
    .execute();
  timer.timeLog("allowed establishments");

  const establishmentIds = establishments.map((establishment) => establishment.id);

  if (!establishments.length) throw unauthorized("Unauthorized for establishment(s)");

  const tripPromises = establishmentIds.map((establishmentId) =>
    getDbCachedValue({
      key: getCacheKey({ establishmentId, month: month.currentMonth, type: "trips-month" }),
      request: () =>
        tripCalendarQb
          .select("trip.date")
          .where("trip.date", ">=", month.firstDateInCalender)
          .where("trip.date", "<=", month.lastDateInCalender)
          .where("trip.establishment_id", "=", establishmentId)
          .execute(),
    }),
  );
  const trips = flat((await Promise.all(tripPromises)).map((item) => item.response));
  timer.timeLog("trips");

  const filteredEstablishmentIds = establishmentIds.filter(
    (establishmentId) => !state.persist_toggle_establishment_ids.includes(establishmentId),
  );

  const unscheduledParticipantsPromises = filteredEstablishmentIds.map((establishmentId) =>
    getDbCachedValue({
      key: getCacheKey({ establishmentId, month: month.currentMonth, type: "unscheduled-participants-month" }),
      request: (trx) =>
        trx
          .selectFrom("participation")
          .innerJoin("sale_item", "sale_item.id", "participation.sale_item_id")
          .innerJoin("booking", "booking.id", "sale_item.booking_id")
          .where("booking.establishment_id", "=", establishmentId)
          .where("booking.cancelled_at", "is", null)
          .where("sale_item.duration", "&&", dateRange(sql.lit(month.firstDateInCalender), sql.lit(month.lastDateInCalender)))
          .select((eb) => [
            "sale_item.booking_id",
            "booking.establishment_id",
            "sale_item.product_id",
            daysOfMonthQb
              .where((seriesEb) =>
                seriesEb(eb.ref("sale_item.duration"), "&&", dateRange(seriesEb.ref("series.date"), seriesEb.ref("series.date"))),
              )
              .where(
                "series.date",
                "not in",
                kysely
                  .selectFrom("trip_assignment")
                  .innerJoin("trip", "trip.id", "trip_assignment.trip_id")
                  .where("trip_assignment.participation_id", "=", eb.ref("participation.id"))
                  .select("trip.date"),
              )
              .as("days"),
          ])
          .execute(),
    }),
  );
  const unscheduledParticipants = flat((await Promise.all(unscheduledParticipantsPromises)).map((item) => item.response));
  timer.timeLog("unscheduled participants");

  const newDays = daysOfMonth?.date.map((day) => {
    return {
      day: day,
      trips: trips.filter((trip) => trip.date === day),
      unscheduled_participants:
        unscheduledParticipants
          .filter((participation) => participation.days?.includes(day))
          .map((participation) => removeObjectKeys(participation, "days")) || [],
      schedules: null,
    };
  });

  timer.end();

  return {
    days: newDays || [],
    month: month,
  };
};
