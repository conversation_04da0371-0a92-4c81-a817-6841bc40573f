import { CloudTasksClient } from "@google-cloud/tasks";
import type { InsertObject, Kysely } from "kysely";
import type { DB } from "~/kysely/db";
import { appConfig } from "~/config/config.server";
import { singleton } from "~/server/utils/singleton.server";
import { getFullUrl } from "~/misc/helpers";

interface Task {
  scheduleTime: { seconds: number };
  httpRequest: {
    httpMethod: "POST";
    url: string;
  };
}

if (!appConfig.MOCK_TASKS_ENABLED && !appConfig.GOOGLE_SERVICE_ACCOUNT) {
  throw new Error("GOOGLE_SERVICE_ACCOUNT is missing");
}

export const cloudTasksClient = new CloudTasksClient({ credentials: appConfig.GOOGLE_SERVICE_ACCOUNT });
export const cloudTasksQueue = cloudTasksClient.queuePath("scamgem", "asia-southeast1", "tt-queue-singapore");

const allTasks = singleton("tasks", () => ({
  scheduledTasks: [] as Task[],
  executingTasks: [] as Task[],
  doneTaks: [] as Task[],
}));

const mockEnabled = appConfig.MOCK_TASKS_ENABLED;

const runTasks = async () => {
  const { scheduledTasks, executingTasks, doneTaks } = allTasks;
  const nowInSeconds = Date.now() / 1000;
  const toBeExecutedTasks = scheduledTasks.filter(
    (scheduledTask) => !executingTasks.includes(scheduledTask) && !doneTaks.includes(scheduledTask),
  );
  // console.log("to be execute tastks", allTasks.scheduledTasks);
  // console.log("try to execute tasks", toBeExecutedTasks);
  for (let scheduledTask of toBeExecutedTasks) {
    const scheduleTimeInSeconds = scheduledTask.scheduleTime?.seconds;
    const shouldExecute = !scheduleTimeInSeconds || nowInSeconds > Number(scheduleTimeInSeconds) + 1;
    const url = scheduledTask.httpRequest?.url;
    if (!url) {
      console.log("no callback url found");
      return;
    }
    if (shouldExecute) {
      executingTasks.push(scheduledTask);
      await fetch(url, { method: "POST" });
      doneTaks.push(scheduledTask);
      console.log("task executed", url);
    }
  }
  setTimeout(async () => {
    await runTasks();
  }, 5000);
};

if (mockEnabled) {
  (() => {
    runTasks().then((resp) => {
      console.log(resp);
    });
    console.log("start task runner", allTasks.doneTaks.length);
  })();
}

export const sendCLoudTasks = async (...callbacks: { id: string; delay_in_seconds: number; host: string }[]) => {
  const tasks: Task[] = callbacks.map((callback) => ({
    scheduleTime: { seconds: Date.now() / 1000 + callback.delay_in_seconds },
    httpRequest: {
      httpMethod: "POST",
      url: `${getFullUrl(callback.host)}/callback/${callback.id}`,
    },
  }));
  if (mockEnabled) {
    tasks.forEach((task) => allTasks.scheduledTasks.push(task));
  } else {
    await Promise.all(tasks.map((task) => cloudTasksClient.createTask({ parent: cloudTasksQueue, task: task })));
  }
};

export const createCallbacksAndSendCloudTasks = async (db: Kysely<DB>, ...callback: Array<InsertObject<DB, "callback">>) => {
  if (callback.length === 0) return [];
  console.log("callbacks", callback);
  const insertedCallbacks = await db.insertInto("callback").values(callback).returningAll().execute();
  await sendCLoudTasks(...insertedCallbacks);
  return insertedCallbacks;
};

export const getCloudTasks = async () => {
  if (mockEnabled) return allTasks.scheduledTasks;
  const tasksResponse = await cloudTasksClient.listTasks({ parent: cloudTasksQueue });
  return tasksResponse[0];
};
