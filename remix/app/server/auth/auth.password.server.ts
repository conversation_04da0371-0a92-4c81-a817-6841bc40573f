import { compareSync, hashSync } from "bcrypt";
import type { Kysely } from "kysely";
import type { DB } from "~/kysely/db";
import { at_infinity_value } from "~/kysely/db-static-vars";

const saltRounds = 10;

export const createPasswordHash = (password: string) => hashSync(password, saltRounds);

export const validatePassword = async (trx: Kysely<DB>, args: { user_id: string; password: string }) => {
  const user = await trx
    .selectFrom("user")
    .select(["id", "email", "password_hash", "deleted_at"])
    .where("user.deleted_at", "=", at_infinity_value)
    .where("user.id", "=", args.user_id)
    .limit(1)
    .executeTakeFirst();

  if (!user?.password_hash) return false;

  const passwordHash = user.password_hash;

  return compareSync(args.password, passwordHash);
};
