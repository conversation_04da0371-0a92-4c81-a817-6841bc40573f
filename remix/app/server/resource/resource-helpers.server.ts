import type { DB } from "~/kysely/db";
import type { InsertObject, Kysely, Selectable, UpdateObject } from "kysely";
import type { Operation } from "~/components/form/DefaultInput";
import { AuditInsert } from "~/server/resource/resource.types";

export interface BaseArgs {
  ctx: { session_id: string };
  request: Request;
  formData: FormData;
}

export interface BaseTrxArgs extends BaseArgs {
  trx: Kysely<DB>;
  getExistingOrCreateAnonymousUserSession: () => Promise<{ id: string }>;
}

export interface BaseIdArgs extends BaseTrxArgs {
  id: string;
  captcha_score: number;
}

export interface AfterMutations {
  insideTransaction: Map<string, () => Promise<AuditInsert[] | void | null>>;
  outsideTransaction: Map<string, () => Promise<unknown>>;
}

export interface AuthArgs extends BaseIdArgs {
  action: Operation;
  trigger: "before" | "after";
  after_mutations: AfterMutations;
}

type AuthReturn = true | false | unknown;

export interface Args<T extends keyof DB> {
  // authorize runs before insert and update and after update and delete
  authorize: (args: AuthArgs) => Promise<AuthReturn> | AuthReturn;
  beforeMutate?: (args: BaseIdArgs & { data?: Partial<Selectable<DB[T]>>; operation: Operation }) => Promise<{
    id: string;
    operation: Operation;
    data?: Partial<Selectable<DB[T]>>;
  }>;
  disableAudit?: (args: BaseIdArgs & { action: Operation }) => Promise<boolean> | boolean;
  insert: (
    args: BaseIdArgs & {
      after_mutations: AfterMutations;
      data: Selectable<DB[T]>;
    },
  ) => Promise<InsertObject<DB, T> | Selectable<DB[T]> | false> | InsertObject<DB, T> | Selectable<DB[T]> | false;
  update: (
    args: BaseIdArgs & {
      after_mutations: AfterMutations;
      data: UpdateObject<DB, T>;
    },
  ) => Promise<UpdateObject<DB, T> | false> | UpdateObject<DB, T> | false;
  delete: (
    args: BaseIdArgs & {
      data?: UpdateObject<DB, T>;
      after_mutations: AfterMutations;
    },
  ) => Promise<AuthReturn> | AuthReturn;
  onChanged?: (
    args: BaseIdArgs & {
      action: Operation;
      after_mutations: AfterMutations;
      diff: {
        diff: Partial<Selectable<DB[T]>> | null;
        before: Selectable<DB[T]> | null;
        after: Selectable<DB[T]> | null;
      };
    },
  ) => Promise<boolean | string> | boolean | string;
}

export const bustCacheAfter = (args: { after_mutations: AfterMutations; trx: Kysely<DB> }, keyPrefix: string) => {
  args.after_mutations.insideTransaction.set(keyPrefix, async () => {
    await args.trx.deleteFrom("cache").where("cache.key", "ilike", `${keyPrefix}%`).execute();
    return []
  });
};
