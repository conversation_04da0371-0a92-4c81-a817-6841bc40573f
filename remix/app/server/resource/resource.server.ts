import type { DB } from "~/kysely/db";
import { boatResource, tripAssignmentResource, tripResource } from "~/domain/boat/boat-resource";
import {
  itemDivingCourseResource,
  itemResource,
  priceResource,
  productDivingLocationResource,
  productDivingSiteResource,
  productPriceResource,
  productResource,
} from "~/domain/product/product-resource";
import { scheduleResource } from "~/domain/schedule/schedule-resource";
import { addonResource } from "~/domain/addon/addon-resource";
import { memberResource } from "~/domain/member/member-resource";
import { activityAddonResource, bookingResource, invoiceResource, saleItemResource } from "~/domain/booking/booking-resource";
import {
  addressResource,
  commentResource,
  customerResource,
  participantDayResource,
  participantResource,
  participantTokenResource,
  participantWaiverResource,
  participationAddonResource,
  participationResource,
  participationWaiverResource,
  personResoruce,
} from "~/domain/participant/participant-resource";
import { establishmentLanguageResource, establishmentResource } from "~/domain/establishment/establishment-resource";
import { inquiryResource, reviewResource } from "~/domain/inquiry/inquiry-resource";
import { activeUserSessionQb, isEditorQb, memberIsAdminQb, userSessionId } from "~/domain/member/member-queries.server";
import { userResource } from "~/domain/user/user-resource";
import { sessionLinkResource, sessionResource } from "~/domain/session/session-resouce";
import { fieldResource, formResource, formWaiverResource } from "~/domain/form/form-resource";
import { fileResource, fileTargetResource } from "~/domain/file/file-resource";
import { slugify } from "~/misc/helpers";
import { paymentMethodResource, paymentResource } from "~/domain/payment/payment-resource.server";
import { signatureResource } from "~/domain/signature/signature-resource";
import { callbackResource } from "~/domain/callback/callback.server";
import { userSessionResource } from "~/domain/user_session/user_session.server";
import { waiverEstablishmentResource, waiverResource, waiverTranslationResource } from "~/domain/waiver/waiver-resource";
import {
  xenditAccountResource,
  xenditApiKeyResource,
  xenditEnvironmentResource,
  xenditSplitRuleResource,
  xenditVirtualBankAccountPaymentResource,
  xenditVirtualBankAccountResource,
} from "~/domain/xendit/xendit.resource.server";
import { viewResource } from "~/domain/view/view-resource";
import { operatorResource } from "~/domain/operator/operator-resource.server";
import { signupSubmissionResource } from "~/domain/signup_submission/signup-submission-resource.server";
import { intuitConnectionResource } from "~/domain/quickbooks/intuit-resource";
import { v4 } from "uuid";
import { oneTimePasswordResource } from "~/domain/user_session/otp.server";
import { Args } from "~/server/resource/resource-helpers.server";
import { categoryResource } from "~/domain/category/category-resource";
import { sendToParticipantReminder } from "~/domain/participant/participant-emails";
import { participantActivityReminderQuery } from "~/domain/email/participation-query.server";
import { nowValue } from "~/kysely/kysely-helpers";
import { ResourceError } from "~/utils/error";
import { emailCancelledMsg } from "~/domain/email/email";
import { at_infinity_value } from "~/kysely/db-static-vars";
import { getHost } from "~/misc/web-helpers";
import { importResource } from "~/domain/import/import-resource.server";

const notAllowedResource: Args<any> = {
  disableAudit: () => true,
  authorize: () => false,
  insert: () => false,
  update: () => false,
  delete: () => false,
};

const defaultEditorResource: Args<any> = {
  authorize: (args) => isEditorQb(args).executeTakeFirst(),
  insert: (args) => args.data,
  update: (args) => args.data,
  delete: () => true,
};

const divingSiteResource: Args<"diving_site"> = {
  authorize: (args) => activeUserSessionQb(args, true).where("_user.editor", "=", true).executeTakeFirst(),
  insert: (args) => ({ ...args.data, slug: slugify(args.data.name as string) }),
  update: (args) => {
    console.log("data", args.data);
    return { ...args.data, slug: typeof args.data.name === "string" ? slugify(args.data.name) : undefined };
  },
  delete: () => true,
};

const productTagResource: Args<"product__tag"> = {
  authorize: (args) =>
    args.trx
      .selectFrom("product__tag")
      .innerJoin("product", "product.id", "product__tag.product_id")
      .innerJoin("item", "item.id", "product.item_id")
      .innerJoin("tag", "tag.id", "product__tag.tag_id")
      .where("product__tag.id", "=", args.id)
      .where("tag.establishment_id", "=", (eb) => eb.ref("item.establishment_id"))
      .where("tag.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"))
      .where("item.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"))
      .executeTakeFirst(),
  insert: (args) => args.data,
  update: (args) => args.data,
  delete: () => true,
};

const tagResource: Args<"tag"> = {
  authorize: (args) =>
    args.trx
      .selectFrom("tag")
      .where("tag.id", "=", args.id)
      .where("tag.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"))
      .executeTakeFirst(),
  beforeMutate: async (args) => {
    const name = args.data?.name;
    const finalName = typeof name === "string" && name && name.trim();
    const finalKey = finalName && finalName.toLowerCase();

    return {
      operation: args.operation,
      id: args.id || v4(),
      data: args.data && {
        ...args.data,
        key: finalKey || undefined,
        name: finalName || undefined,
      },
    };
  },
  insert: (args) => args.data,
  update: (args) => args.data,
  delete: () => true,
};

const mailResource: Args<"mail"> = {
  authorize: (args) => {
    return args.trx
      .selectFrom("mail")
      .where("mail.id", "=", args.id)
      .where("mail.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"))
      .executeTakeFirst();
  },
  insert: async (args) => {
    const participation = await participantActivityReminderQuery
      .where("participant.id", "=", args.data.participant_id)
      .where("sale_item.id", "=", args.data.sale_item_id)
      .select((eb) => [
        "customer.establishment_id",
        eb("booking.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id")).as("is_allowed"),
      ])
      .executeTakeFirst();

    if (!participation) throw new ResourceError("Participation not found");
    if (!participation.is_allowed) return false;
    if (participation.participant_email !== args.data.to_email) throw new ResourceError("Email is different from participant email");

    const success = args.data.success;

    if (success) {
      args.after_mutations.insideTransaction.set(sendToParticipantReminder.name + args.id, async () => {
        await sendToParticipantReminder({
          ...participation,
          booking_host: participation.booking_host || getHost(args.request),
        });
      });
    }

    return {
      ...args.data,
      created_at: nowValue,
      to_email: participation.participant_email,
      to_name: participation.participant_full_name,
      from_name: participation.operator_name,
      establishment_id: participation.establishment_id,
      // template_name: "activity_reminder",
      success: success,
      msg: success ? null : emailCancelledMsg,
    };
  },
  update: (args) => false,
  delete: () => false,
};

const rentableResource: Args<"rentable"> = {
  authorize: (args) =>
    args.trx
      .selectFrom("rentable")
      .where("rentable.id", "=", args.id)
      .where("rentable.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"))
      .executeTakeFirst(),
  insert: (args) => ({ ...args.data, deleted_at: at_infinity_value, deleted_by_user_session_id: null }),
  update: (args) => args.data,
  delete: () => true,
};

const rentalAssignmentResource: Args<"rental_assignment"> = {
  authorize: (args) =>
    args.trx
      .selectFrom("rental_assignment")
      .innerJoin("rentable", "rentable.id", "rental_assignment.rentable_id")
      .where("rental_assignment.id", "=", args.id)
      .where("rentable.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"))
      .executeTakeFirst(),
  insert: (args) => args.data,
  update: (args) => args.data,
  delete: () => true,
};

const rentableServiceResource: Args<"rentable_service"> = {
  authorize: (args) =>
    args.trx
      .selectFrom("rentable_service")
      .innerJoin("rentable", "rentable.id", "rentable_service.rentable_id")
      .where("rentable_service.id", "=", args.id)
      .where("rentable.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"))
      .executeTakeFirst(),
  insert: (args) => ({ ...args.data, created_at: nowValue, created_by_user_session_id: userSessionId(args) }),
  update: (args) => args.data,
  delete: () => true,
};

const tankAssignmentResource: Args<"tank_assignment"> = {
  authorize: (args) =>
    args.trx
      .selectFrom("tank_assignment")
      .innerJoin("trip_assignment", "trip_assignment.id", "tank_assignment.trip_assignment_id")
      .innerJoin("trip", "trip.id", "trip_assignment.trip_id")
      .where("tank_assignment.id", "=", args.id)
      .where("trip.establishment_id", "in", memberIsAdminQb(args).select("_member.establishment_id"))
      .executeTakeFirst(),
  insert: (args) => args.data,
  update: (args) => args.data,
  delete: () => true,
};

const sortableValueResource: Args<"sortable_value"> = {
  disableAudit: () => true,
  authorize: (args) => isEditorQb(args).executeTakeFirst(),
  insert: (args) => args.data,
  update: (args) => args.data,
  delete: () => true,
};

export const resources: Record<keyof DB, Args<any>> = {
  callback: callbackResource,
  boat: boatResource,
  user: userResource,
  user_session: userSessionResource,
  one_time_password: oneTimePasswordResource,
  intuit_connection: intuitConnectionResource,
  form: formResource,
  tag: tagResource,
  price: priceResource,
  category: categoryResource,
  field: fieldResource,
  session: sessionResource,
  item: itemResource,
  product: productResource,
  product_price: productPriceResource,
  schedule: scheduleResource,
  trip: tripResource,
  trip_assignment: tripAssignmentResource,
  tank_assignment: tankAssignmentResource,
  addon: addonResource,
  member: memberResource,
  booking: bookingResource,
  invoice: invoiceResource,
  address: addressResource,
  sale_item: saleItemResource,
  activity_addon: activityAddonResource,
  signature: signatureResource,
  person: personResoruce,
  customer: customerResource,
  participant: participantResource,
  participant_day: participantDayResource,
  participant_token: participantTokenResource,
  participation: participationResource,
  mail: mailResource,
  participation_addon: participationAddonResource,
  xendit_platform: xenditEnvironmentResource,
  xendit_environment: xenditApiKeyResource,
  xendit_account: xenditAccountResource,
  xendit_split_rule: xenditSplitRuleResource,
  xendit_virtual_bank_account: xenditVirtualBankAccountResource,
  xendit_virtual_bank_account_payment: xenditVirtualBankAccountPaymentResource,
  signup_submission: signupSubmissionResource,
  region: defaultEditorResource,
  spot: defaultEditorResource,
  operator: operatorResource,
  establishment: establishmentResource,
  establishment__language: establishmentLanguageResource,
  inquiry: inquiryResource,
  review: reviewResource,
  diving_location: defaultEditorResource,
  diving_site: divingSiteResource,
  diving_course: defaultEditorResource,
  product__tag: productTagResource,
  product__diving_location: productDivingLocationResource,
  product__diving_site: productDivingSiteResource,
  item__diving_course: itemDivingCourseResource,
  comment: commentResource,
  file: fileResource,
  payment: paymentResource,
  payment_method: paymentMethodResource,
  file_target: fileTargetResource,
  waiver: waiverResource,
  waiver_establishment: waiverEstablishmentResource,
  waiver_translation: waiverTranslationResource,
  participant_waiver: participantWaiverResource,
  participation_waiver: participationWaiverResource,
  form_waiver: formWaiverResource,
  session_link: sessionLinkResource,
  view: viewResource,
  cache: notAllowedResource,
  event: notAllowedResource,
  device_token: notAllowedResource,
  user__device_token: notAllowedResource,
  answer_old: notAllowedResource,
  user_event: notAllowedResource,
  currency: notAllowedResource,
  spatial_ref_sys: notAllowedResource,
  entity_action: notAllowedResource,
  // message: notAllowedResource,
  invoice_count: notAllowedResource,
  rentable: rentableResource,
  sortable_value: sortableValueResource,
  rental_assignment: rentalAssignmentResource,
  rentable_service: rentableServiceResource,
  import: importResource,
  import_row: notAllowedResource,
};
