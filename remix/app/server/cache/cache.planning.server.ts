interface Args {
  establishmentId: string;
  date?: string | null;
  month?: string | null;
  type?: "planning-day" | "trips-month" | "unscheduled-participants-month";
  endStrict?: boolean;
}

const keyOrder = ["establishmentId", "month", "date", "type"] satisfies (keyof Args)[];

export const getCacheKey = (args: Args) => {
  const baseKey = ["establishment", args.establishmentId];
  keyOrder.forEach((key) => {
    const value = args[key];
    if (typeof value === "string") {
      baseKey.push(value);
    }
  });
  const endStrict = !args.type && args.endStrict !== false;
  return baseKey.join(".") + (endStrict ? "." : "");
};
