import { kysely } from "~/misc/database.server";
import { z } from "zod";
import { isAfter, subSeconds } from "date-fns";
import { Kysely } from "kysely";
import { DB } from "~/kysely/db";
import { addToNow, nowValue } from "~/kysely/kysely-helpers";

export interface Args<TResponse> {
  key: string;
  request: () => Promise<TResponse>;
  ttlInSeconds?: number;
}

const cacheParser = z.object({
  key: z.string(),
  updated_at: z.string(),
  response: z.string(),
});

export const getCachedValue = async <TResponse>(args: Args<TResponse>): Promise<TResponse> => {
  const key = args.key;
  const request = args.request;

  const ttlInSeconds = args.ttlInSeconds || 5 * 60;

  const result = await kysely.selectFrom("cache").selectAll().where("key", "=", key).executeTakeFirst();
  const parsed = cacheParser.optional().nullable().safeParse(result);

  if (parsed.success && parsed.data && isAfter(new Date(parsed.data.updated_at), subSeconds(new Date(), ttlInSeconds))) {
    console.log("cache hit", key);
    return result?.response;
  }
  try {
    console.log("updating cache key", key);
    const newResponse = await request();
    const cacheResponse = await kysely
      .insertInto("cache")
      .values({
        key: key,
        response: newResponse,
      })
      .onConflict((eb) => eb.column("key").doUpdateSet({ response: newResponse }))
      .executeTakeFirstOrThrow();
    return newResponse;
  } catch (e: any) {
    console.error(e);
    if (result) {
      console.log("returning cached response because api gave error");
      return result.response;
    }
    throw new Error(e);
  }
};

export interface Args2<TResponse> {
  key: string;
  request: (trx: Kysely<DB>) => Promise<TResponse>;
  ttlInSeconds?: number | null;
}

export interface Result<TResponse> {
  updated_at: string;
  response: TResponse;
}

export const getDbCachedValue = async <TResponse>(args: Args2<TResponse>): Promise<Result<TResponse>> => {
  const enabled = true;
  const ttlInSeconds = args.ttlInSeconds || 5 * 60;
  const result = await kysely.transaction().execute(async (trx) => {
    if (!enabled) {
      const result = await args.request(trx);
      return {
        // key: "",
        updated_at: "",
        response: result as any,
      } as any;
    }
    const existingCache = await trx
      .selectFrom("cache")
      .select(["cache.response", "cache.updated_at"])
      .where((eb) => {
        const keyCmpr = eb("cache.key", "=", args.key);
        if (args.ttlInSeconds === null) return keyCmpr;
        return eb.and([keyCmpr, eb("cache.updated_at", ">", addToNow(-ttlInSeconds, "seconds"))]);
      })
      .executeTakeFirst();

    if (existingCache) return existingCache satisfies Result<TResponse>;

    const result = await args.request(trx);
    // console.log("result", result);
    const cacheEntry = await trx
      .insertInto("cache")
      .values({ key: args.key, response: JSON.stringify(result), updated_at: nowValue })
      .onConflict((oc) =>
        oc.column("key").doUpdateSet((eb) => ({
          response: eb.ref("excluded.response"),
          updated_at: eb.ref("excluded.updated_at"),
        })),
      )
      .returningAll()
      .executeTakeFirstOrThrow();

    return cacheEntry satisfies Result<TResponse>;
  });
  return result as Promise<Result<TResponse>>;
};
