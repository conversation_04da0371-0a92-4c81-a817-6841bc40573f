import { SESv2 } from "@aws-sdk/client-sesv2";
import { appConfig } from "~/config/config.server";
import { singleton } from "~/server/utils/singleton.server";
import { diversdeskNoReplyEmail, traveltrusterName, traveltrusterNoReplyEmail } from "~/misc/consts";
import { Kysely } from "kysely";
import { DB } from "~/kysely/db";
import { AsyncReturnType } from "type-fest";
import { ReactElement } from "react";
import { render } from "@react-email/render";
import { createMimeMessage } from "mimetext";
import { ResourceError } from "~/utils/error";

export const mockedEmails = singleton("emails", () => [] as string[]);

const sesClient = new SESv2({
  region: "ap-southeast-1",
  credentials: {
    accessKeyId: appConfig.AWS_ACCESS_KEY_ID,
    secretAccessKey: appConfig.AWS_SECRET_ACCESS_KEY || "",
  },
});

export const getMailContext = (operator: { email: string | null; name: string } | null) => {
  return {
    contact: operator ? { addr: operator.email, name: operator.name } : null,
    sender: {
      addr: operator ? diversdeskNoReplyEmail : traveltrusterNoReplyEmail,
      name: operator ? operator.name : traveltrusterName,
    },
  };
};

export const getMailContextForPrefix = async (db: Kysely<DB>, prefix: string | null) => {
  const operator = await db
    .selectFrom("operator")
    .select((eb) => [
      "operator.name",
      eb
        .selectFrom("establishment")
        .select("establishment.email")
        .where("establishment.operator_id", "=", eb.ref("operator.id"))
        .limit(1)
        .as("email"),
    ])
    .where("operator.slug", "=", prefix)
    .executeTakeFirst();

  return getMailContext(operator || null);
};

export type MailCtx = AsyncReturnType<typeof getMailContextForPrefix>;

export const createMim = async (comp: ReactElement) => {
  const msg = createMimeMessage();
  msg.addMessage({
    contentType: "text/html",
    data: await render(comp),
  });
  msg.addMessage({
    contentType: "text/plain",
    data: await render(comp, { plainText: true }),
  });

  // mimeMsg.addMessage("text/html", await render(comp));
  // mimeMsg.addMessage("text/plain", await render(comp, { plainText: true }));

  const send = (replyAddresses?: string[]) => {
    const recipientsRaw = msg.getRecipients();
    const recipiants = recipientsRaw ? (recipientsRaw instanceof Array ? recipientsRaw : [recipientsRaw]) : [];
    if (appConfig.ALLOWED_EMAIL_SUFFIXES.length) {
      for (const recipient of recipiants) {
        if (!appConfig.ALLOWED_EMAIL_SUFFIXES.some((suffix) => recipient.addr.endsWith(suffix))) {
          throw new ResourceError(
            `Cannot send to ${recipient.addr}, only email addresses with the following suffixes are allowed in this environment: ${appConfig.ALLOWED_EMAIL_SUFFIXES.join(", ")}`,
          );
        }
      }
    }
    const sender = msg.getSender();
    const emailRaw = msg.asRaw();
    if (appConfig.MOCK_MAIL_ENABLED) {
      mockedEmails.push(emailRaw);
      console.log("email", emailRaw);
      return;
    }
    return sesClient.sendEmail({
      Destination: {
        ToAddresses: recipiants.filter((recipient) => recipient.type === "To").map((recipient) => recipient.addr),
        CcAddresses: recipiants.filter((recipient) => recipient.type === "Cc").map((recipient) => recipient.addr),
        BccAddresses: recipiants.filter((recipient) => recipient.type === "Bcc").map((recipient) => recipient.addr),
      },
      ReplyToAddresses: replyAddresses,
      FromEmailAddress: sender && `${sender.name}<${sender.addr}>`,
      Content: {
        Raw: {
          Data: Buffer.from(emailRaw, "utf8"),
        },
      },
    });
  };

  return {
    msg: msg,
    send: send,
  };
};
