import React, { Fragment } from "react";
import { Head, Hr, Html, Link, Section, Text } from "@react-email/components";
import { TailwindBody } from "~/components/Mail";
import type { MailCtx } from "~/server/mail/email.client.server";

export const VerificationEmail = (props: { url: string; ctx: MailCtx }) => (
  <Html>
    <Head />
    <TailwindBody>
      <Section>
        <Text>Hi there!</Text>
        <Text>Please follow the link below to verify your email address.</Text>
        <Text>
          <Link className="text-primary" href={props.url}>
            Verify and Sign in
          </Link>
        </Text>
        <Text>If this verification request wasn't made by you, feel free to disregard this email.</Text>
        <Text>Kind regards,</Text>
        <Text>Team {props.ctx.sender.name}</Text>
      </Section>
      {props.ctx.contact && (
        <Fragment>
          <Hr />
          <Section className="text-xs text-slate-600">
            <Text className="text-xs">
              This non-reply email was automatically generated by &copy;Diversdesk on behalf of {props.ctx.contact.name}.<br />
              {props.ctx.contact.addr && (
                <Fragment>
                  For questions, please feel free to reach out to{" "}
                  <Link href={`mailto:${props.ctx.contact.addr}`}>{props.ctx.contact.addr}</Link>
                </Fragment>
              )}
            </Text>
          </Section>
        </Fragment>
      )}
    </TailwindBody>
  </Html>
);

export const PasswordResetEmail = (props: { url: string; senderName: string }) => (
  <Html>
    <Head />
    <TailwindBody>
      <Section>
        <Text>Hi there!</Text>
        <Text>Please follow the link below to set your new password.</Text>
        <Text>
          <Link href={props.url.toString()}>Reset your password</Link>
        </Text>
        <Text>If you didn’t ask to reset a password for this address, you can ignore this email.</Text>
        <Text>Kind regards,</Text>
        <Text>Team {props.senderName}</Text>
      </Section>
    </TailwindBody>
  </Html>
);

export const OTPMail = (props: { url: string; ctx: MailCtx }) => (
  <Html>
    <Head />
    <TailwindBody>
      <Section>
        <Text>Hi there!</Text>
        <Text>code</Text>
        <Text>
          <Link className="text-primary" href={props.url}>
            Verify and Sign in
          </Link>
        </Text>
        <Text>If this verification request wasn't made by you, feel free to disregard this email.</Text>
        <Text>Kind regards,</Text>
        <Text>Team {props.ctx.sender.name}</Text>
      </Section>
      {props.ctx.contact && (
        <Fragment>
          <Hr />
          <Section className="text-xs text-slate-600">
            <Text className="text-xs">
              This non-reply email was automatically generated by &copy;Diversdesk on behalf of {props.ctx.contact.name}.<br />
              {props.ctx.contact.addr && (
                <Fragment>
                  For questions, please feel free to reach out to{" "}
                  <Link href={`mailto:${props.ctx.contact.addr}`}>{props.ctx.contact.addr}</Link>
                </Fragment>
              )}
            </Text>
          </Section>
        </Fragment>
      )}
    </TailwindBody>
  </Html>
);

// export const addPlainAndHtml = async (
//   mimeMsg: {
//     setMessage(format: TextFormat, message: string, moreHeaders?: MessageHeaders): unknown;
//   },
//   comp: ReactElement,
// ) => {
//   mimeMsg.setMessage("text/html", await render(comp));
//   mimeMsg.setMessage("text/plain", await render(comp, { plainText: true }));
// };
