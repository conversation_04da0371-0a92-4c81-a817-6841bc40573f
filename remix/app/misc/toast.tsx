import { create } from "zustand/index";
import { useEffect } from "react";
import { CheckDoneIcon } from "~/components/Icons";

type ToastType = "success" | "error";

interface Toast {
  id: string;
  msg: string;
  type: ToastType | null;
}

const useToasts = create<{ toasts: Toast[] }>(() => ({ toasts: [] }));

export const toast = (msg: string, type: ToastType | null = "success") => {
  useToasts.setState((prev) => ({
    toasts: [
      ...prev.toasts.slice(-2),
      {
        msg: msg,
        type: type,
        id: Math.random().toString(36).substring(2, 9),
      },
    ],
  }));
};

export const Toast = (props: { toast: Toast }) => {
  useEffect(() => {
    console.log("remove on timeout", props.toast.id);
    const timeout = setTimeout(() => {
      useToasts.setState((prev) => ({ toasts: prev.toasts.filter((toast) => toast.id !== props.toast.id) }));
    }, 5_000);
    return () => clearTimeout(timeout);
  }, []);

  return (
    <div className="rounded-md bg-white shadow-2xl shadow-black p-3 items-center flex flex-row whitespace-nowrap gap-2 text-sm text-slate-700 px-4 py-2 w-fit mb-1">
      {props.toast.type === "success" && <CheckDoneIcon className="text-green-500" />}
      <p className="whitespace-nowrap">{props.toast.msg}</p>
    </div>
  );
};

export const Toaster = () => {
  const toasts = useToasts();

  return (
    <div className="fixed left-1/2 w-0 bottom-0">
      {toasts.toasts.map((toast, index) => {
        const startY = -(5 * (toasts.toasts.length - 1));
        const translateY = startY + 3 * index;
        return (
          <div key={toast.id} className="w-fit fade-in fade-out" style={{ transform: `translate(-50%, ${translateY}px)` }}>
            <Toast key={toast.id} toast={toast} />
          </div>
        );
      })}
    </div>
  );
};
