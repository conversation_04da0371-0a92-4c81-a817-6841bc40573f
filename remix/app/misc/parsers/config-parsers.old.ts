import { z } from "zod";

const firebaseConfigParser = z.object({
  apiKey: z.string(),
  authDomain: z.string(),
  projectId: z.string(),
  storageBucket: z.string(),
  messagingSenderId: z.string(),
  appId: z.string(),
});

export const publicConfigParser = z.object({
  openreplayProjectKey: z.string().default("Wzl6jJfE4yKbJj0JF9WV"),
  googleMapsPublicApiKey: z.string().default("AIzaSyDjUqZNWwUVJN1yoauMF167lM9kDWYgyH8"),
  mapBoxPublicApiKey: z.string().default("pk.eyJ1Ijoic2NhbWdlbSIsImEiOiJja2ZncGpkMWowODAzMndwOTFlcDR4dmc1In0.-E8JDQK_6u2-YfybYbxQzw"),
  GOOGLE_OAUTH_CLIENT_ID: z.string().default("635500722361-067v890seuedrmei3bd7tajq3c9oq0ev.apps.googleusercontent.com"),
  GOOGLE_ANALYTICS_MEASUREMENT_ID: z.string().nullish(),
  firebase_singapore: firebaseConfigParser,
});
