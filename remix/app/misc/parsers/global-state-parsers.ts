import { entries, keys, safeParseJson } from "~/misc/helpers";
import { getActivitySlug } from "~/domain/activity/activity";
import { defaultTimezone, timezones } from "~/data/timezones";
import { paymentsStates } from "~/domain/booking/booking-payment";
import { defaultAuthMethod } from "~/domain/user_session/user_session";
import { DB, TableFieldName } from "~/kysely/db";
import type { ActivitySlug } from "~/domain/activity/activity";

export const authMethods = ["otp", "password"] as const;
export const authFlows = ["signup", "signin"] as const;

export const tooltips = ["register"] as const;
export const modals = [
  "main",
  "delete",
  "tag",
  "payment",
  "boat",
  "message",
  "account",
  "account_menu",
  "profile",
  "inquiry",
  "finish",
  "content",
  "pre_navigate",
  "language",
  "page_language",
  "country",
  "dive_location",
  "detail",
  "sort",
  "filter",
  "side",
  "native",
  "contact",
  "cart",
] as const;
export const medicalFormSteps = ["questionaire", "signature"] as const;

export const participantSelectModes = ["normal", "pending"] as const;

type MedicalStep = (typeof medicalFormSteps)[number];

const actionTypes = ["copy", "view"] as const;

export const checkStates = ["true", "false", "mixed"] as const;
export type CheckState = (typeof checkStates)[number];

export const toggleCheckState = (currentState: CheckState): CheckState =>
  currentState === "mixed" ? "true" : currentState === "false" ? "mixed" : "false";

export type AuthMethod = (typeof authMethods)[number];

export type AuthFlow = (typeof authFlows)[number];

export const removeFromArray = <T extends string>(arr: T[], id?: T | null) => arr.filter((itemId) => itemId !== id);

export const toggleArray = <T extends string>(arr: T[], id?: T | null) => {
  if (!id) return [] as T[];
  return arr.includes(id) ? removeFromArray(arr, id) : [...arr, id];
};

export interface SortObj {
  key: string;
  direction: string;
}

export const paramsToRecord = (params: URLSearchParams) => {
  const coersePositiveNumber = (key: string) => {
    const number = Number(params.get(key));
    return Number.isSafeInteger(number) && number >= 0 ? number : 0;
  };
  return {
    id: params.get("id"),
    ids: params.getAll("ids"),
    previous_path: params.get("previous_path"),
    // exact: params.has("exact"),
    // toggle_questions: params.getAll("toggle_questions"),
    // dirty_inputs: params.getAll("dirty_inputs"),
    brand: params.get("brand"),
    page_nr: coersePositiveNumber("page_nr"),
    page_limit: coersePositiveNumber("page_limit"),
    product_id: params.get("product_id"),
    booking_id: params.get("booking_id"),
    member_id: params.get("member_id"),
    category_id: params.get("category_id"),
    spot_id: params.get("spot_id"),
    waiver_id: params.get("waiver_id"),
    view_id: params.get("view_id"),
    print_token: params.get("print_token"),
    persist_token: params.get("persist_token"),
    main_tags: params.getAll("main_tags"),
    tags: params.getAll("tags"),
    categories: params.getAll("categories"),
    toggle_edit: params.has("toggle_edit"),
    delete_and_create: params.has("delete_and_create"),
    retail: params.has("retail"),

    page_type: params.get("page_type"),
    boat_id: params.get("boat_id"),
    toggle_direction: params.has("toggle_direction"),
    action_type: actionTypes.find((actionType) => actionType === params.get("action_type")),
    participant_select: participantSelectModes.find((mode) => mode === params.get("participant_select")),
    toggle_participant_select: params.has("toggle_participant_select"),
    toggle_participation_ids: params.getAll("toggle_participation_ids"),
    participation_id: params.get("participation_id"),
    toggled_tabs: params.getAll("toggled_tabs"),
    toggle_trip_sites: params.getAll("toggle_trip_sites"),
    toggle_qr: params.has("toggle_qr"),
    medical_step: medicalFormSteps.find((step) => step === params.get("medical_step")) || ("questionaire" satisfies MedicalStep),
    search: params.get("search"),
    error_message: params.get("error_message"),
    element_action: params.getAll("element_action"),
    element_clear: params.getAll("element_clear"),
    sale_item_id: params.get("sale_item_id"),
    country_code: params.get("country_code"),
    region_ids: params.getAll("region_ids"),
    currency_id: params.get("currency_id"),
    persist_page: coersePositiveNumber("persist_page"),
    toggle_payment_form: params.has("toggle_payment_form"),
    create: params.has("create"),
    edit: params.getAll("edit"),
    remove: params.getAll("remove"),
    show_unpublished: params.has("show_unpublished"),
    tab: params.get("tab"),
    form_id: params.get("form_id"),
    diving_course_id: params.get("diving_course_id"),
    diving_courses: params.getAll("diving_courses"),
    diving_course_tag: params.get("diving_course_tag"),
    diving_locations: params.getAll("diving_locations"),
    diving_sites: params.getAll("diving_sites"),
    rerender: coersePositiveNumber("rerender"),
    refresh_formdata: params.has("refresh_formdata"),
    sorts: params.getAll("sorts").map((sort) => {
      const parsedJson = safeParseJson(sort);
      const column = parsedJson?.key;
      const direction = parsedJson?.direction;
      return {
        key: typeof column === "string" ? column : "",
        direction: typeof direction === "string" ? direction : "",
      } satisfies SortObj;
    }),
    modal_detail_name: params.get("modal_detail_name"),
    modal_tab: params.get("modal_tab"),
    modal_domain: params.get("modal_domain") as keyof DB | null,
    modal_detail_id: params.get("modal_detail_id"),
    booking_payment_states: keys(paymentsStates).filter((state) =>
      params.getAll("booking_payment_states").find((paramState) => paramState === state),
    ),
    booking_payment_status: keys(paymentsStates).find((state) => state === params.get("booking_payment_status")) || null,

    toggle_trip_edit: params.getAll("toggle_trip_edit"),
    toggle_member_panel: params.get("toggle_member_panel"),
    toggle_sale_item_ids: params.getAll("toggle_sale_item_ids"),
    toggle_tooltip: tooltips.find((tooltip) => tooltip === params.get("toggle_tooltip")),
    toggle_trip_add_panel: params.get("toggle_trip_add_panel"),
    toggle_trip_templates_panel: params.has("toggle_trip_templates_panel"),
    toggle_panels: params.getAll("toggle_panels"),
    toggle_pending_participants: params.has("toggle_pending_participants"),
    // toggle_select_pickup: params.has("toggle_select_pickup"),
    toggle_delete_ids: params.getAll("toggle_delete_ids"),
    toggle_modal: modals.find((modal) => modal === params.get("toggle_modal")),
    toggle_columns: params.getAll("toggle_columns"),
    toggle_cancelled: params.has("toggle_cancelled"),
    toggle_invoice: params.has("toggle_invoice"),
    toggle_password_reset: params.has("toggle_password_reset"),
    direct_booking: checkStates.find((str) => str === params.get("direct_booking")) || "mixed",
    establishment_id: params.get("establishment_id"),
    participant_ids: params.getAll("participant_ids"),
    participant_id: params.get("participant_id"),
    participant_waiver_id: params.get("participant_waiver_id"),
    activity_slug: getActivitySlug(params.get("activity_slug")) || null,
    activity_slugs: params.getAll("activity_slugs") as ActivitySlug[],
    preview: params.has("preview"),
    language_codes: params.getAll("language_codes"),
    language_code: params.get("language_code"),
    popover_id: params.get("popover_id"),
    filter_names: params.getAll("filter_names"),
    page_lang: params.get("page_lang"),
    print_friendly: params.has("print_friendly"),
    date_field: params.get("date_field") as TableFieldName | null,
    date_from: params.get("date_from"),
    date_to: params.get("date_to"),

    response_identifier: params.get("response_identifier"),
    response_form_id: params.get("response_form_id"),
    response_error: params.get("response_error"),
    form_success_id: params.get("form_success_id"),

    lang: params.get("lang"),
    trip_id: params.get("trip_id"),
    filter_id: params.get("filter_id"),
    // filter_activity_title: params.get("filter_activity_title"),

    persist_debug: params.has("persist_debug"),
    persist_timezone: timezones.find((timezone) => timezone === params.get("persist_timezone")) || defaultTimezone,
    persist_page_lang: params.get("persist_page_lang"),
    persist_toggle_activity_panel_id: params.getAll("persist_toggle_activity_panel_id"),
    persist_toggle_trip_panel_id: params.getAll("persist_toggle_trip_panel_id"),
    persist_toggle_activity_slug: params.getAll("persist_toggle_activity_slug"),
    persist_flow: authFlows.find((item) => item === params.get("persist_flow")) || ("signup" satisfies AuthFlow),
    persist_authMethod: authMethods.find((item) => item === params.get("persist_authMethod")) || defaultAuthMethod,
    persist_create: params.has("persist_create"),
    persist_month: params.get("persist_month"),
    persist_date: params.get("persist_date"),
    persist_date_from: params.get("persist_date_from"),
    persist_date_to: params.get("persist_date_to"),
    persist_participant_id: params.get("persist_participant_id"),
    persist_action_type: actionTypes.find((actionType) => actionType === params.get("persist_action_type")),
    persist_selected_participant_id: params.get("persist_selected_participant_id"),
    persist_toggle_establishment_ids: params.getAll("persist_toggle_establishment_ids"),
    persist_toggled_tabs: params.getAll("persist_toggled_tabs"),
    persist_operator_id: params.get("persist_operator_id"),
    persist_previous_path: params.get("persist_previous_path"),
    persist_establishment_id: params.get("persist_establishment_id"),
    persist_opened_menu_segments: params.getAll("persist_opened_menu_segments"),
    persist_main_menu_toggled: params.has("persist_main_menu_toggled"),
    persist_add_account: params.has("persist_add_account"),
    customer_view: params.has("customer_view"),
    type: params.get("type"),
    size: params.get("size"),
    color: params.get("color"),
    created_by: params.get('created_by'),
  };
};

export type StateInput = ReturnType<typeof paramsToRecord>;
export type StateInputKey = keyof StateInput;

export const resetResponseInput = {
  response_identifier: null,
  response_form_id: null,
  response_error: null,
} satisfies Partial<StateInput>;

// export const sName = (str: keyof StateInput) => str;

export const dropTransientParams = (search: URLSearchParams) => {
  Array.from(search.keys()).forEach((key) => {
    if (!key.startsWith("persist_")) {
      search.delete(key);
    }
  });
};

export const mergeStateToParams = (search: URLSearchParams, newState: Partial<StateInput>) => {
  entries(newState).forEach(([key, value]) => {
    search.delete(key);
    if (value instanceof Array) {
      value.forEach((value) => {
        const finalVal = value instanceof Object ? JSON.stringify(value) : value;
        search.append(key, finalVal);
      });
    } else if (value || value === "") {
      search.set(key, value + "");
    }
  });
};
