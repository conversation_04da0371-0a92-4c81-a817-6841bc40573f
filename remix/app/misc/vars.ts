import { v4 } from "uuid";
import type { NavigateOptions } from "react-router";

export const shuffledAlphaNumbers = "z5mubqely24o30xa1tgkfiwjns8rd9vhc6p7";

export const defaultCurrency = "IDR";
export const defaultCountryCode = "ID";
export const defaultLangauge = "en";
export const defaultLocale = "nl-NL";

export const mapSourceId = "points-source";

export namespace layerIds {
  export const points = "points";
  export const pointNames = "point-names";
  export const clusters = "clusters";
  export const clusterCount = "cluster-names";
}

export const bigImageWidth = 1200;
export const bigImageQuery = `tr:w-${bigImageWidth},c-at_max`;

export const primaryOrange = "#FF7557";

export const limit = 50;

export const clientIdKey = "client_id";
export const redirectKey = "redirect";
export const redirectErrorKey = "redirect_error";
export const identifierKey = "identifier";
export const responseIdentifierKey = "response_identifier";
export const confirmMessageKey = "confirm";

export const fixedResponseIdentifierValue = "fixed";

export const fileUploadFormId = "file-upload-form";

export const clientId = v4();

export const defaultDebounce = 500;

export const defaultNavOptions: NavigateOptions = {
  replace: true,
  preventScrollReset: true,
};

export const weekDays = [
  {
    name: "monday",
    key: "mo",
  },
  {
    name: "tuesday",
    key: "tu",
  },
  {
    name: "wednesday",
    key: "we",
  },
  {
    name: "thursday",
    key: "th",
  },
  {
    name: "friday",
    key: "fr",
  },
  {
    name: "saterday",
    key: "sa",
  },
  {
    name: "sunday",
    key: "su",
  },
] satisfies { key: string; name: string }[];
export const nrOfDaysInWeek = 7;
export const dateFormat = "yyyy-MM-dd"
export const localHost = "localhost:3000";

export const actionValues = {
  payedAtXendit: "xendit",
};

export const defaultUnkownValue = "";
export const defaultNotFilledValue = "-";
