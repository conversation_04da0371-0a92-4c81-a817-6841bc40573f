import { localHost } from "~/misc/vars";

const allowedHosts = ["diversdesk.com", "traveltruster.com", "localhost:3000", "dinkel.works"];
export const getHost = (request: Request) => {
  const host = new URL(request.url).host;
  const allowedHost = allowedHosts.find((apaxDomain) => host?.endsWith(apaxDomain));
  return allowedHost ? host : localHost;
};

export const robotsMeta = (content: "index" | "noindex" = "noindex") => ({ name: "robots", content: content });
