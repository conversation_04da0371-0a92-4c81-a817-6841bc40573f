import { Kysely, PostgresDialect } from "kysely";
import type { DB } from "~/kysely/db";
import type { PoolConfig } from "pg";
import { Pool, types } from "pg";
import { appConfig } from "~/config/config.server";

types.setTypeParser(types.builtins.DATE, (val) => val);
types.setTypeParser(types.builtins.TIMESTAMPTZ, (val) => val);
types.setTypeParser(1182, (val) => {
  if (!val) {
    return val;
  }
  return val
    .replace(/[{}]/g, "")
    .split(",")
    .map((date) => date);
});
types.setTypeParser(types.builtins.NUMERIC, (val) => {
  return val === null ? null : parseFloat(val);
});
types.setTypeParser(types.builtins.INT8, (val) => {
  return val === null ? null : parseInt(val);
});
types.setTypeParser(types.builtins.INTERVAL, (val) => val);

const doConnectionConfig: PoolConfig = {
  ...appConfig.DB,
  // max: 1,
  // ...appConfig.DB,
  // ssl: environment === "development" ? undefined : { rejectUnauthorized: false },
  // max: 10,
  // connectionTimeoutMillis: 4000,
  // query_timeout: 3000,
  // idleTimeoutMillis: 5000,
  // connectionString: "postgresql://doadmin:<EMAIL>:25060/master",
  // user: "doadmin",
  // password: "AVNS_xlkes0PO74VODftm9BO",
  // host: "db-scamgem-dev-do-user-4626682-0.c.db.ondigitalocean.com",
  // port: 25060,
  // database: "master",

  // port: 25061,
  // database: "master-pool",
  // ssl: { rejectUnauthorized: false },
};

const createKyselyInstance = () => {
  if (global.__kysely) return __kysely;
  console.log("init kysely");
  global.__kysely = new Kysely<DB>({
    dialect: new PostgresDialect({
      pool: new Pool(doConnectionConfig),
    }),
    // plugins: [kPLugiin],
  });
  return global.__kysely;
};

export const kysely = createKyselyInstance();

// export const kysely = singleton("kysely", () => {
//   console.log("init kysely");
//   return new Kysely<DB>({
//     dialect: new PostgresDialect({
//       pool: new Pool(doConnectionConfig),
//     }),
//     // plugins: [kPLugiin],
//   });
// });
