import { msg } from "@lingui/core/macro";
import { I18n } from "@lingui/core";

export const getErrorTranslations = (i18n: I18n) => {
  return {
    no_changes: i18n._(msg`No changes were made`),
    "auth/expired-action-code": i18n._(msg`Authentication code was expired`),
    "auth/invalid-email": i18n._(msg`Invalid email address`),
    "auth/wrong-password": i18n._(msg`Invalid credentials`),
    "no result": i18n._(msg`Page was not found or you are unauthorized`),
    "jwt expired": "Token has been expired or already used",
    'null value in column "created_by_user_session_id" of relation "activity" violates not-null constraint':
      'null value in column "created_by_user_session_id" of relation "activity" violates not-null constraint',
    "The email address is already in use by another account.": i18n._(msg`Het email adres is al in gebruik door een ander account.`),
    "Password should be at least 6 characters": i18n._(msg`Wachtwoord moet tenminste 6 tekens lang zijn.`),
    'update or delete on table "invoice" violates foreign key constraint "intuit_invoice_invoice_id_fkey" on table "intuit_invoice"':
      "Still processing quickbooks invoice, could you try again",
    'update or delete on table "waiver" violates foreign key constraint "form_waiver_waiver_id_fkey" on table "form_waiver"':
      "Oops... It looks like this waiver is linked to an existing onboarding journey",
    'Not-NULL violation. null value in column "user_id" of relation "node_user" violates not-null constraint': "No user selected",
    'value for domain phone violates check constraint "phone_check"': "Incorrect phonenumber",
    'duplicate key value violates unique constraint "product_deleted_at_establishment_id_sku_key"':
      "A product with the same SKU already exists",
    // location_user_schedule_location_schedule_id_fkey: "Trip is required",
    'null value in column "trip_id" of relation "trip_assignment" violates not-null constraint': "Trip is required",
    'null value in column "product_id" of relation "booking" violates not-null constraint': "Product is required",
    'duplicate key value violates unique constraint "intuit_connection_realm_id_key"':
      "Quickbooks organisation is already connected by you or someone else",
    'duplicate key value violates unique constraint "tag_establishment_id_key_key"': "Tag name already exists",
    'null value in column "key" of relation "tag" violates not-null constraint': "Tag name required",
    // no_registrations_left_in_booking: "Sorry, all available registration slots have been filled.",
    fk_trip_assignment_member: "One of the users is connected to a trip",
    'duplicate key value violates unique constraint "active_user_session"': "You are already logged in with this account",
    'duplicate key value violates unique constraint "form_target_id_type_name_key"': "Form name is already taken",
    'update or delete on table "product" violates foreign key constraint "inquiry_product_id_fkey" on table "inquiry':
      "There are inquiries connected to this product.",
    'update or delete on table "product" violates foreign key constraint "booking_product_id_fkey" on table "booking':
      "There are inquiries connected to this product.",
    'update or delete on table "establishment" violates foreign key constraint "addon_operator_location_id_fkey" on table "addon"':
      "Could not delete Operator location because there are still add-ons connected to it.",
    "Firebase: Error (auth/popup-blocked).":
      "Oops! It looks like pop-ups are blocked in your browser. Please try again, and if the issue persists, try allowing pop-ups.",
    "Firebase: Error (auth/popup-closed-by-user).": "Could not login with google because the popup was close.",
    'null value in column "name" of relation "addon" violates not-null constraint': "Name is required for add-on",
    'duplicate key value violates unique constraint "diving_site_diving_location_id_slug_key"':
      "There already is diving site with a similar or exactly the same name",
    'duplicate key value violates unique constraint "diving_site_name_diving_location_id_key"': "Diving site name already exists",
    'duplicate key value violates unique constraint "member_establishment_id_user_id_key"': "Member already added",
    "range lower bound must be less than or equal to range upper bound": "Start date should be less than end date",
    'update or delete on table "participation" violates foreign key constraint "trip_assignment_participation_id_fkey" on table "trip_assignment"':
      "Activity has assignments connected to it",
    'null value in column "product_id" of relation "activity" violates not-null constraint': "A product is required to create an activity",
    'null value in column "region_id" of relation "diving_location" violates not-null constraint': "A diving location requires a region",
    'duplicate key value violates unique constraint "tracks_spot_name_gin_idx"': "Spot name already exists",
    'duplicate key value violates unique constraint "form_deleted_at_target_id_name_key"': "Form name already exists",
    'duplicate key value violates unique constraint "region_name_key"': "Region already exists within country",
    'duplicate key value violates unique constraint "waiver_root_id_language_code_key"': "There already is a waiver for this language",
    'duplicate key value violates unique constraint "waiver_slug_language_code_establishment_id_diving_certifica_key"':
      "This waiver combination already exists",
    'duplicate key value violates unique constraint "waiver_establishment_id_slug_key"': "This identifier already exists",
    'update or delete on table "participant_waiver" violates foreign key constraint "signature_participant_waiver_id_fkey" on table "signature"':
      "There are still signatures connected to this waiver",
    'duplicate key value violates unique constraint "location_name_key"': "Diving Location name already exists for this region",
    'duplicate key value violates unique constraint "diving_location_region_id_name_generated_key"':
      "Diving Location name already exists for this region",
    'duplicate key value violates unique constraint "member_establishment_id_name_idx"': i18n._(
      msg`Name already exists under this establishment`,
    ),
    'duplicate key value violates unique constraint "waiver_translation_waiver_id_language_code_key"': "Waiver translation already exists",
    'duplicate key value violates unique constraint "operator_location_operator_id_address_key"':
      "Establishment with the same address already exists for this operator",
    'update or delete on table "xendit_account" violates foreign key constraint "establishment_xendit_account_id_fkey" on table "establishment"':
      "Xendit account is connected to establishment",
    'duplicate key value violates unique constraint "invoice_booking_id_key"': "Invoice already exists for this booking",
    'update or delete on table "participation" violates foreign key constraint "comment_participation_id_fkey" on table "comment"':
      "Could delete participation because there are comments connected to it",
    'duplicate key value violates unique constraint "view_operator_id_name_key"': "View name already exists",
    'update or delete on table "diving_site" violates foreign key constraint "product_site_diving_site_id_fkey" on table "product__diving_site"':
      "Dive site still has active products connected to it.",
    'value for domain website violates check constraint "website_check"': "Invalid website format",
    'duplicate key value violates unique constraint "payment_method_deleted_at_establishment_id_key_key"':
      "Payment method name already exists",
    'duplicate key value violates unique constraint "payment_method_deleted_at_establishment_id_name_key"':
      "Payment method name already exists",
    'duplicate key value violates unique constraint "intuit_connection_deleted_at_realm_id_key"':
      "Quickbooks organization is already connected",
    'null value in column "quantity" of relation "addon" violates not-null constraint': "Quantity is required for addon",
    'update or delete on table "activity" violates foreign key constraint "participation_waiver_activity_id_fkey" on table "participation_waiver"':
      "activity has manually manually approved waivers",
    'duplicate key value violates unique constraint "member_deleted_at_establishment_id_name_key"': "Name of member is already in use",
    'duplicate key value violates unique constraint "xendit_split_rule_xendit_platform_id_active_key"':
      "Another split rule is already active, deactivate that one first",
    'null value in column "name" of relation "view" violates not-null constraint': "Name is required for View",
    'duplicate key value violates unique constraint "product_deleted_at_item_id_color_size_key"':
      "There already is a variant with this color/size combination",
    'duplicate key value violates unique constraint "member_deleted_at_establishment_id_user_id_key"': "Member already exists",
    'update or delete on table "category" violates foreign key constraint "category_parent_category_id_fkey" on table "category"':
      "Cannot delete category because it has sub-categories",
    'duplicate key value violates unique constraint "product_deleted_at_cached_establishment_id_sku_key"': "SKU Already exists",
    'duplicate key value violates unique constraint "category_establishment_id_key_key"': "Category name already exists",
    'duplicate key value violates unique constraint "rentable_establishment_id_reference_id_key"': "Reference ID already exists",
    'duplicate key value violates unique constraint "rentable_service_rentable_id_service_date_key"': "Service date already exists",
    'new row for relation "rentable_service" violates check constraint "rentable_service_date_check"':
      "Service date should be before next service date",
  } satisfies Record<string, string>;
};

export type ErrorKey = keyof ReturnType<typeof getErrorTranslations>;

export const findErrorMessage = (str: string, i18n?: I18n) => {
  const finalI18n = i18n || new I18n({ locale: "EN" });
  const errorTranslations = getErrorTranslations(finalI18n);
  const translatedMessage = errorTranslations[str as ErrorKey];
  if (translatedMessage) return translatedMessage;
  for (let [key, value] of Object.entries(errorTranslations)) {
    if (key?.includes(str) || str?.includes(key)) return value;
  }
  return null;
};

export const getFinalMessage = (str: string, i18n?: I18n) => findErrorMessage(str, i18n) || str;

export const getErrorMsgFromCatch = (error: unknown) => {
  return typeof error === "string"
    ? error
    : typeof error === "object" && error !== null
      ? "message" in error
        ? error.message
        : "routine" in error
          ? error.routine === "aclcheck_error"
            ? `Permission denied`
            : "detail" in error
              ? error.detail
              : error
          : error
      : error;
};
