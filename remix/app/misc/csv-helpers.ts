export function escapeCSV(text: string): string {
  // return text.replace(/\n/g, "\\n").replace(/\r/g, "\\r").replace(/"/g, '\\"').replace(/,/g, "\\,");
  if (text.includes('"') || text.includes(",") || text.includes("\n") || text.includes("\r")) {
    // Escape double quotes by doubling them
    text = text.replace(/"/g, '""');
    // Wrap the field in double quotes
    text = `"${text}"`;
  }
  return text;
}
