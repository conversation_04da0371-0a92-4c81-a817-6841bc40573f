import { json } from "@remix-run/server-runtime";

export const badRequest = (obj: Record<string, string | boolean | number> | { msg: string }) =>
  json(obj, {
    status: 400,
    statusText: "Bad request",
  });

export const notFound = (statusText?: string) =>
  json(
    { msg: statusText || "Oops! Looks like you tried to visit a page that does not exist." },
    {
      status: 404,
      statusText: "Not found",
    },
  );

export const notFoundOrUnauthorzied = (text?: string) =>
  json(
    { msg: "Oops! Looks like you tried to visit a page that does not exist or you are unauthorized for." },
    { status: 404, statusText: text || "Not found or unauthorized" },
  );

// export const NotImplemented = new Response("Not implemented", { status: 500 });

export const unauthorized = (text?: string) =>
  json(
    { msg: text || "Unauthorized" },
    {
      status: 401,
      statusText: "Unauthorized",
    },
  );

// export const OperationNotSupported = new Response("Operation not supported", { status: 400 });

export const unauthenticated = () =>
  json(
    { msg: "Not authenticated" },
    {
      status: 400,
      statusText: "Not authenticated",
    },
  );
