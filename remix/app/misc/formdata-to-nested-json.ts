import { dateRegex, keys, referencePrefixValue } from "~/misc/helpers";
import { sql } from "kysely";
import { z } from "zod";
import { sum } from "remeda";

const falseValues = ["no", "false"];

export const fieldTypes = {
  __trim__: (value) => (typeof value === "string" ? value.trim() || null : value),
  __boolean__: (value) => (value === null ? null : falseValues.includes(value) ? false : !!value),
  __pg_int_range__: (rawValue) => {
    const value = z
      .object({
        from: z.coerce
          .number()
          .nullish()
          .transform((item) => item || ""),
        to: z.coerce
          .number()
          .transform((nr) => nr + 1)
          .nullish()
          .transform((item) => item || ""),
      })
      .parse(rawValue);

    if (!value.from && !value.to) return null;
    return `[${value.from},${value.to})`;
  },
  __pg_coordinates__: (value) => {
    if (!value || typeof value !== "string") return null;
    return {
      type: "Point" as const,
      coordinates: value.replace(" ", "").split(","),
    };
  },
  __empty_array__: (value) => {
    if (value) return value;
    return [];
  },
  __sum__: (value) => {
    console.log("values", value);
    const numbers = z.coerce.number().array().parse(value);
    return sum(numbers);
  },
  __join__: (value) => {
    const strings = z.string().nullable().array().parse(value);
    if (strings.includes(null)) return null;
    return strings.join("");
  },
  __to_string__: (value) => (typeof value === "object" ? JSON.stringify(value) : value),
  // __pg_tstzrange__: (rawValue) => {
  //   if (typeof rawValue === "string") return `(tstzrange(${rawValue}, ${rawValue},'[]'))`;
  //   const value = z
  //     .object({
  //       from: z.object({
  //         date: z.string().regex(dateRegex),
  //         time: z.string().regex(timeRegex).nullish(),
  //       }),
  //       to: z
  //         .object({
  //           date: z.string().regex(dateRegex).nullish(),
  //           time: z.string().regex(timeRegex).nullish(),
  //         })
  //         .nullish(),
  //     })
  //     .parse(rawValue);
  //   const zone = defaultTimezone;
  //
  //   const startTime = `('${value.from.date} ${value.from.time || "00:00"} ${zone}'::timestamptz)`;
  //   const endTime = value.to && value.to.date ? `('${value.to.date} ${value.to.time || "00:00"} ${zone}'::timestamptz)` : `null`;
  //   const rangeValue = `(tstzrange(${startTime}, ${endTime},'[)'))`;
  //
  //   console.log("rangevalue", rangeValue);
  //
  //   return sql.raw(rangeValue);
  // },
  __pg_daterange__: (rawValue) => {
    const value = z
      .object({
        from: z.string().regex(dateRegex).nullish(),
        to: z.string().regex(dateRegex).nullish(),
      })
      .parse(rawValue);

    const startDate = value.from ? `'${value.from}'::date` : "null";
    const endDate = value.to ? `'${value.to}'::date` : "null";
    return sql.raw(`(daterange(${startDate}, ${endDate}, '[]'))`);
  },
  __pg_daterange: (rawValue) => {
    const value = z
      .object({
        date: z.string().regex(dateRegex),
        duration: z.coerce.number().nullish(),
      })
      .parse(rawValue);

    return sql.raw(`(daterange('${value.date}', '${value.date}'::date + ${value.duration || "null"},'[)'))`);
  },
  // __pg_timestamptz__: (rawValue) => {
  //   const value = z
  //     .object({
  //       date: z.string().regex(dateRegex),
  //       time: z.string().regex(timeRegex),
  //       duration: z.coerce.number().nullish(),
  //       duration_unit: z.literal("hour").default("hour"),
  //     })
  //     .parse(rawValue);
  //   const zone = defaultTimezone;
  //
  //   const durationInHours = value.duration;
  //   const startTime = `('${value.date} ${value.time} ${zone}'::timestamptz)`;
  //   if (durationInHours === undefined) {
  //     return sql.raw(startTime);
  //   }
  //
  //   return sql.raw(`(tstzrange(${startTime}, ${startTime} +
  //                     ${durationInHours || "null"} *
  //                     interval '1 ${value.duration_unit}',
  //                     '[)'))`);
  // },
} satisfies Record<string, (values: any) => unknown>;

export type FieldType = keyof typeof fieldTypes;

type ReadonlyFormdata = Omit<FormData, "append" | "delete" | "set">;

export const copyFormdata = (formData: ReadonlyFormdata) => {
  const copiedFormData = new FormData();
  formData.forEach((value, key) => {
    copiedFormData.append(key, value);
  });
  return copiedFormData;
};

export function formdataToNestedJson(initialFormdata: ReadonlyFormdata): Record<string, unknown> {
  const result: Record<string, unknown> = {};

  const finalFormData = copyFormdata(initialFormdata);

  // Update reference values on finalFormData
  Array.from(finalFormData.keys()).forEach((fieldKey) => {
    initialFormdata.forEach((referenceValue, referenceKey) => {
      const allFieldValues = finalFormData.getAll(fieldKey);
      const fieldValue = allFieldValues[0];
      if (
        typeof referenceValue === "string" &&
        typeof fieldValue === "string" &&
        fieldValue.includes(referencePrefixValue) &&
        referenceKey.includes(referencePrefixValue)
      ) {
        if (allFieldValues.length > 1) {
          console.error("reference replacement not supported for multiple values with the same key, because it will override");
        } else {
          finalFormData.set(fieldKey, fieldValue.replace(referenceKey, referenceValue));
        }
      }
    });
  });

  // Process entries and sort them
  const sortedEntries = Array.from(finalFormData.entries())
    .map(([key, value]) => {
      const type = keys(fieldTypes).find((fieldType) => fieldType === value);
      return {
        key: key,
        value: value,
        type: type,
      };
    })
    .sort((a, b) => {
      const longestKeyFirst = b.key.length - a.key.length;
      // Values need to be mutated first
      if (!!a.type === !!b.type) return longestKeyFirst;
      // Type keys last because they need to mutate/map data formed by the previous form entries
      if (a.type) return 1;
      return -1;
    });

  // Build the nested result object
  sortedEntries.forEach((item) => {
    const keyParts = item.key.split(".");
    let currentObj: any = result;

    keyParts.forEach((currentKeyPartRaw, index) => {
      const nextKeyPartRaw = keyParts[index + 1];
      const currentKeyPart = /^\d+$/.test(currentKeyPartRaw) ? Number(currentKeyPartRaw) : currentKeyPartRaw;

      const currentValue = currentObj[currentKeyPart];
      if (nextKeyPartRaw) {
        const newOrExistingValue = currentValue || (/^\d+$/.test(nextKeyPartRaw) ? [] : {});
        if (!currentValue) {
          currentObj[currentKeyPart] = newOrExistingValue;
        }
        currentObj = newOrExistingValue;
      } else {
        try {
          currentObj[currentKeyPart] = item.type ? fieldTypes[item.type](currentValue) : item.value || null;
        } catch (e) {
          console.error("could not parse", item.key);
          throw e;
        }
      }
    });
  });
  console.log("result", result);
  return result;
}
