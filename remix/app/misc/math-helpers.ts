const initTotals = {
  raw: 0,
  discount: 0,
  final: 0,
};

export type Totals = typeof initTotals;

export const sumTotals = (...totalsArr: Totals[]): Totals => {
  const init = { ...initTotals };
  return totalsArr.reduce(
    (acc, totals) => ({
      discount: acc.discount + totals.discount,
      final: acc.final + totals.final,
      raw: acc.raw + totals.raw,
    }),
    init
  );
};

export const calcTotals = (args: { basePrice: number | string; amount: number; discount: number }): Totals => {
  const payingPercentage = 1 - args.discount / 100;
  const rawPrice = Number(args.basePrice) * args.amount;
  const finalPriceForActivity = rawPrice * payingPercentage;
  return {
    raw: rawPrice,
    discount: rawPrice - finalPriceForActivity,
    final: finalPriceForActivity,
  };
};
