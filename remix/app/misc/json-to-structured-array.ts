import { entries, tableOrder } from "~/misc/helpers";
import { z } from "zod";
import { Operation, operations } from "~/components/form/DefaultInput";
import { Selectable } from "kysely";
import { DB } from "~/kysely/db";

// Define the return type for a single entry in the structured array
type StructuredArrayEntry<T extends keyof DB> = {
  data: Selectable<DB[T]>;
  id: string;
  refId: string;
  operation: Operation;
  tableName: T;
};

// Define the return type for the entire function
type StructuredArray = Array<StructuredArrayEntry<keyof DB>>;

export const jsonToStructuredArray = (value: Record<string, unknown>): StructuredArray => {
  return entries(value)
    .map(([ref, refObj]) => {
      if (!refObj || typeof refObj !== "object") return null;
      return tableOrder
        .map((tableName) => {
          const parsedInput = z
            .object({
              id: z.string().nullish(),
              operation: z.string().nullish(),
              data: z.any(),
            })
            .safeParse((refObj as any)[tableName]);

          if (!parsedInput.success) return null;
          const input = parsedInput.data;

          const definedOperation = operations.find((operation) => input.operation === operation);
          const operation = definedOperation || (input.id && input.data ? "update" : input.data ? "insert" : "ignore");

          return {
            data: input.data as Selectable<DB[typeof tableName]>,
            id: input.id || "",
            refId: ref,
            operation: operation,
            tableName: tableName,
          } as const;
        })
        .filter((entry) => !!entry)[0];
    })
    .filter((entry): entry is NonNullable<typeof entry> => !!entry)
    .sort((a, b) => a.refId.localeCompare(b.refId));
};

export const getDataForTable = <T extends keyof DB>(input: StructuredArray, tableName: T): Array<StructuredArrayEntry<T>> => {
  return input.filter((mutation): mutation is StructuredArrayEntry<T> => mutation.tableName === tableName);
};
