import { unique } from "remeda";
import { gasOptions } from "~/domain/tank/tank-vars";

export function parsePostgresRange<T>(rangeString: string): [T, T] | [T] | null {
  const rangeRegex = /^([[(])([^,]+),([^,]+)([\])])$/;
  const match = rangeString.match(rangeRegex);
  if (!match) {
    return null;
  }
  const lowerInclusive = match[1] === "[" ? true : false;
  const upperInclusive = match[4] === "]" ? true : false;
  const lowerBound = parseRangeBound<T>(match[2]!, lowerInclusive);
  const upperBound = parseRangeBound<T>(match[3]!, upperInclusive);
  if (!lowerBound || !upperBound) {
    return null;
  }
  return [lowerBound, upperBound];
}

function parseRangeBound<T>(boundString: string, inclusive: boolean): T | null {
  if (boundString === "empty") {
    return null;
  }
  const numericRegex = /^(-?\d+(\.\d+)?)$/;
  const dateRegex = /^(\d{4}-\d{2}-\d{2})$/;
  const timestampRegex = /^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}(?:\.\d+)?)(?: ([+-]\d{2})(\d{2}))?$/;
  let value: any;
  if (typeof value === "number") {
    const match = boundString.match(numericRegex);
    if (!match) {
      return null;
    }
    value = match[1] as unknown as T;
  } else if (typeof value === "string") {
    const dateMatch = boundString.match(dateRegex);
    if (dateMatch) {
      value = new Date(dateMatch[1] || "") as unknown as T;
    } else {
      const timestampMatch = boundString.match(timestampRegex);
      if (timestampMatch) {
        const timezoneOffset = timestampMatch[2] ? parseInt(timestampMatch[2], 10) * 60 + parseInt(timestampMatch[3] || "", 10) : undefined;
        value = new Date(timestampMatch[1] + (timezoneOffset !== undefined ? ` GMT${timezoneOffset}` : "")) as unknown as T;
      } else {
        value = boundString as unknown as T;
      }
    }
  } else {
    throw new Error(`Unsupported data type ${typeof value}`);
  }
  return value;
}

export const uniqueTextArray = (array: any[] | null | undefined, defaultList: readonly string[]) => {
  const uniqueArray = unique((array || []).filter((item) => typeof item === "string").filter(Boolean));
  return uniqueArray.length ? uniqueArray : (defaultList as string[]);
};

export const selectListWithFallback = (options: string[], selectedOption?: string | null) => {
  return options.includes(selectedOption || "") ? options : ([...options, selectedOption] as string[]);
};
