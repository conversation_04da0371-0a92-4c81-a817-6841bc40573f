import { parseAcceptLanguage } from "~/misc/parse-accept-language";

export function getClientLocales(request: Request): string[] | undefined {
  const headers = request.headers;

  let acceptLanguage = headers.get("Accept-Language");

  // if the header is not defined, return undefined
  if (!acceptLanguage) return undefined;

  let locales = parseAcceptLanguage(acceptLanguage, {
    validate: Intl.DateTimeFormat.supportedLocalesOf,
    ignoreWildcard: true,
  });

  // if there are no locales found, return undefined
  if (locales.length === 0) return undefined;
  // if there are multiple locales, return the array
  return locales;
}
