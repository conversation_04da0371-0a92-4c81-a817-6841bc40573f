import { expect, test } from "vitest";
import { formdataToNestedJson } from "./formdata-to-nested-json";

test(`formdataToNestedJson`, () => {
  const formdata = new FormData();
  formdata.set("booking.0.data.date.be", "bla");
  formdata.set("booking.0.data.date.be2", "");

  formdata.append("booking.1.data.duration.date", "2020-10-12");
  formdata.append("booking.1.data.duration.time", "10:00");

  formdata.append("medical.0.data.uuidarr.0", "yes");
  formdata.append("medical.0.data.uuidarr.1", "dds");
  formdata.append("medical.0.data.uuidarr.2", "yes");

  formdata.append("medical.0.data.answer.0.0", "yes");
  formdata.append("medical.0.data.answer.0.0", "__boolean__");
  formdata.append("medical.0.data.answer.0.1", "");
  formdata.append("medical.0.data.answer.0.1", "__boolean__");
  formdata.append("medical.0.data.answer.0.2", "yes");
  formdata.append("medical.0.data.answer.1.0", "yes");
  formdata.append("medical.0.data.answer.1.1", "");
  formdata.append("medical.0.data.answer.2.0", "yes");

  formdata.append("medical.0.data.threedim.0.0.0", "bla");
  formdata.append("medical.0.data.threedim.0.0.1", "ble");

  const expected = {
    medical: [
      {
        data: {
          threedim: [[["bla", "ble"]]],
          answer: [[true, null, "yes"], ["yes", null], ["yes"]],
          uuidarr: ["yes", "dds", "yes"],
        },
      },
    ],
    booking: [
      { data: { date: { be2: null, be: "bla" } } },
      {
        data: {
          duration: {
            date: "2020-10-12",
            time: "10:00",
          },
        },
      },
    ],
  };

  const result = formdataToNestedJson(formdata);
  console.log(JSON.stringify(result));
  expect(result).toEqual(expected);
});
