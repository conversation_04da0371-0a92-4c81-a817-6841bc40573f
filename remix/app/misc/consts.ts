import { defaultCurrency } from "~/misc/vars";

export const traveltrusterName = "traveltruster";
export const traveltrusterDescription = `Explore Bali’s underwater world with traveltruster. Trusted information from partner operators — inquire for snorkelling, fun diving and diving courses easily online.`;
export const traveltrusterNoReplyEmail = "<EMAIL>";
export const traveltruserWhatsappNr = "+6281320948424";

export const diversdeskName = "diversdesk";
export const diversdeskNoReplyEmail = "<EMAIL>";

// old number
// export const whatsappNr = '+6281337316926'
// export const whatsappNr = '+62 813 3731 6926'
// export const whatsappNr = '+62 813-3731-6926'
// export const whatsappNr = '006281337316926'

interface PageOverwrites {
  show_currency_swap: boolean;
  simple_view: boolean;
  whatsapp_fixed: boolean;
  show_whatsapp: boolean;
  base_currency: string;
  customer_toggle: boolean;
  fixed_width: boolean;
  establishment_id: string | null;
}

export const defaultPageOverwriteVars: PageOverwrites = {
  show_currency_swap: false,
  simple_view: false,
  whatsapp_fixed: false,
  show_whatsapp: false,
  base_currency: defaultCurrency,
  establishment_id: null,
  fixed_width: false,
  customer_toggle: false,
};

export const createPageOverwrites = (overwrites: Partial<PageOverwrites>) => overwrites;
