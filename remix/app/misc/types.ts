import type { FieldName } from "~/kysely/db";

export type AsyncReturnType<T extends (...args: any) => Promise<any>> = T extends (...args: any) => Promise<infer R> ? R : any;

export type ExtendedFieldName = FieldName | "language_codes";

export type LatLng = [number, number];

export interface Point {
  type: "Point";
  coordinates: LatLng;
}

export type FunctionResponse = [success: boolean, message: string | null];