import { MetaArgs, MetaDescriptor, MetaFunction } from "@remix-run/react";
import type { AppContext } from "~/hooks/use-app-context";
import { traveltrusterDescription, traveltrusterName } from "~/misc/consts";
import { robotsMeta } from "~/misc/web-helpers";

export const getAppContextInMeta = (args: MetaArgs) => {
  return args.matches.find((match) => match.id === "root")?.data as AppContext;
};

export const createMeta =
  (args: { title: string; description?: string; robots?: "noindex" | "index" }): MetaFunction =>
  (metaArgs) => {
    const ctx = getAppContextInMeta(metaArgs);
    const robots = args.robots || "noindex";
    const operator = ctx?.operator;
    const orgName = operator ? operator.name : traveltrusterName;
    const title = [args.title, orgName].filter((str) => !!str).join(" | ");
    const description = args.description || (operator && traveltrusterDescription);
    const metas: MetaDescriptor[] = [{ title: title }];

    if (robots === "noindex" || operator) {
      metas.push(robotsMeta("noindex"));
    }
    if (description) {
      metas.push({ description: traveltrusterDescription }, { "og:description": traveltrusterDescription });
    }
    return metas;
  };

export const getIpAddress = (request: Request): string | null => {
  const forwardedFor = request.headers.get("x-forwarded-for");
  if (forwardedFor) {
    // x-forwarded-for might return multiple comma-separated values, we take the first
    return forwardedFor.split(",")[0]?.trim() || null;
  }
  // Fallback to remoteAddress if x-forwarded-for is not provided
  return (request as any).connection?.remoteAddress || null;
};
