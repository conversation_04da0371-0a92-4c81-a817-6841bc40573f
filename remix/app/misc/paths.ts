import { ActivitySlug } from "~/domain/activity/activity";
import type { FileTargetValue } from "~/domain/file/file-resource";
import { IntuitEnv } from "~/domain/quickbooks/quickbooks-client.server";

export const _ping = "/ping";
export const _file = "/file";
export const _order_value = "/sortable_value";
export const _file_download = (filename: string) => `/file/${encodeURIComponent(filename)}`;
export const _event = "/event";
export const _event_overview = "/event/overview";
export const _event_year = (year: number) => _event_overview + "/" + year;
export const _event_year_month = (year: number, month: string) => _event_year(year) + "/" + month;
export const _tank = '/tank';

export const _fileTarget = (args: { target_id: string; target: FileTargetValue }) =>
  ["/file_target", args.target, args.target_id].join("/");

export const _payment_success = (paymentId: string) => "/payment/success/" + paymentId;

export const _explore_base = "/explore";

export const _explore = (
  filters: {
    diving_courses?: string[];
    activity?: ActivitySlug;
    spot_id?: string;
    diving_course_tag?: string;
    diving_locations?: string[];
    diving_sites?: string[];
  },
  params?: URLSearchParams,
) => {
  const searchParams = new URLSearchParams(params);

  Object.entries(filters).forEach(([key, value]) => {
    if (!value) return;
    searchParams.delete(key);
    const values = typeof value === "string" ? [value] : value;
    values.forEach((value) => {
      searchParams.append(key, value);
    });
  });

  return {
    pathname: _explore_base,
    search: searchParams.toString(),
  };
};

export const _operator_edit = (idOrSlug: string) => "/operator/" + idOrSlug + "/mutate";
export const _operator_detail = (idOrSlug: string) => "/operator/" + idOrSlug;
export const _operator = "/operator";
export const _establishment_path = "/operator/location";
export const _establishment_detail = (id: string) => "/operator/location/" + id;
export const _establishment_create = _establishment_path + "/create";

export const _review_create_relative = "review/create";
export const _review_detail = ({ establishment_id, review_id }: { establishment_id: string; review_id: string }) =>
  _establishment_detail(establishment_id) + "/review/" + review_id;

export const _addon = (establishment_id: string) => _establishment_detail(establishment_id) + "/addon";

export const _item = "/item";
export const _item_detail = (id: string) => "/item/" + id;
export const _item_mutate = "/item/mutate";

export const _product = "/product";
export const _product_detail = (id: string) => "/product/" + id;
export const _product_history = (id: string) => _product_detail(id) + "/history";
export const __product = "routes/_all._catch.product.$id";
export const _product_mutate = "/product/mutate";

export const _payment = "/payment";

export const _payment_method = "/payment-method";
export const _payment_method_mutate = "/payment-method/mutate";

export const _diving_course = "/diving-course";

export const _user = "/user";

export const _userDetail = (id: string) => _user + "/" + id;
export const _spot = "/spot";
export const _spot_detail = (id: string) => _spot + "/" + id;
export const _spot_mutate = (id: string) => _spot_detail(id) + "/edit";
export const _diving_location = "/diving-location";
export const _diving_location_detail = (id: string) => _diving_location + "/" + id;
export const _diving_site = (site: { id: string; diving_location_id: string }) =>
  _diving_location_detail(site.diving_location_id) + "/site/" + site.id;

export const _diving_site_grade = "/diving-site/grade";

export const _blog = "/blog";

export const _contact = "/contact";
export const _about = "/about";

export const _feature = "/feature";

export const _metrics = "/metrics";

export const _notification = "/notification";
export const _notification_sent = "/notification/send";
export const _notification_settings = "/notification/settings";

export const _sale = "/sale";

export const _booking = "/booking";
export const _booking_mutate = "/booking/mutate";
export const _booking_detail = (bookingId: string) => "/booking/" + bookingId;
export const _booking_lock = (bookingId: string) => _booking_detail(bookingId) + "/lock";
export const _booking_quickbooks = (bookingId: string) => _booking_detail(bookingId) + "/quickbooks";

export const _activity_mutate = "/activity/mutate";

export const _boat = "/boat";
export const _boat_mutate = "/boat/mutate";

export const _tag = "/tag";

export const _retail = "/retail";
export const _rental = "/rental";
export const _inventory = "/inventory";
export const _category_mutate = "/category/mutate";

export const _waiver = "/waiver";
export const _waiver_pdf = (id: string) => `/waiver/${id}/pdf`;
export const _waiver_mutate = "/waiver/mutate";
export const _waiver_detail = (waiverId: string) => `/waiver/` + waiverId;

export const _planning = "/planning";
export const _planning_month = "/planning/month";
export const _planning_u = "/planning/u";
export const _participant = "/participant";
export const _participant_mutate = "/participant/mutate";
export const _participant_detail = (id: string) => _participant + "/" + id;

export const _xendit = "/xendit";
export const _xendit_env_bank = (envId: string) => _xendit + "/environment/" + envId + "/bank";
export const _xendit_account = "/xendit/account";
export const _xendit_account_detail = (id: string) => _xendit_account + "/" + id;

export const _customer = "/customer";
export const _customerDetail = (customerId: string) => `/customer/${customerId}`;
export const _member = "/member";
export const _member_detail = (id: string) => _member + "/" + id;
export const _workload = "/workload";
export const _workload_detail = (memberId: string) => _workload + "/" + memberId;

export const _register = (establishmentId: string) => `/establishment/${establishmentId}/register`;

export const _connect = (env: Lowercase<IntuitEnv> | IntuitEnv) => `/connect/${env.toLowerCase()}`;
export const _intuit_connection = (id: string) => "/intuit_connection/" + id;

export const _form = "/form";
export const _form_mutate = "/form/mutate";

export const _establishment_paths = (operatorLocationId: string) => {
  const prefix = _establishment_detail(operatorLocationId);
  return {
    index: prefix,
    insight: prefix + "/insight",
    mutate: prefix + "/mutate",
    // product: prefix + "/product",
    history: prefix + "/history",
    integration: prefix + "/integration",
  };
};

export const _verify_participant = (token: string) => `/verify/${token}/participant`;

export const convertToFirestorePath = (path: string) => {
  const segments = path.split("/").filter((segment) => !!segment);
  const isOdd = !!(segments.length & 1);
  // segments.length % 2 !== 0;
  // const lastItem = segments[segments.length - 1];
  // console.log("doitgether?", path);
  if (isOdd) return segments.join("/");
  return [...segments, "odd_segment_end"].join("/");
  // console.log("do i get here?", path);
  // return path + (lastItem ? "" : "/") + "odd_segment_end";
};
