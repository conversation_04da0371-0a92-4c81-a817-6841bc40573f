import { useCallback, useRef, useState } from "react";

interface Dimensions {
  width: number | null;
  height: number | null;
}

type RefCallback = (node: Element | null) => void;

export function useMeasure(): [RefCallback, Dimensions] {
  const [dimensions, setDimensions] = useState<Dimensions>({
    width: null,
    height: null,
  });

  const previousObserver = useRef<ResizeObserver | null>(null);

  const customRef = useCallback<RefCallback>((node) => {
    if (previousObserver.current) {
      previousObserver.current.disconnect();
      previousObserver.current = null;
    }

    if (node && node.nodeType === Node.ELEMENT_NODE) {
      const observer = new ResizeObserver(([entry]) => {
        const firstBorderBoxSize = entry?.borderBoxSize?.[0];
        if (firstBorderBoxSize) {
          setDimensions({ width: firstBorderBoxSize.inlineSize, height: firstBorderBoxSize.blockSize });
        }
      });

      observer.observe(node);
      previousObserver.current = observer;
    }
  }, []);

  return [customRef, dimensions];
}
