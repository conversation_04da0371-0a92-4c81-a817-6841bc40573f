import { getDataForTable, jsonToStructuredArray } from "~/misc/json-to-structured-array";
import { formdataToNestedJson } from "~/misc/formdata-to-nested-json";
import { useSearchParams2 } from "~/hooks/use-search-params2";
import { useNavigation } from "@remix-run/react";
import { redirectKey } from "~/misc/vars";
import { paramsToRecord } from "~/misc/parsers/global-state-parsers";
import { useAppContext } from "~/hooks/use-app-context";

const getMainMenuPinned = (formData: FormData) => {
  const structuredArray = jsonToStructuredArray(formdataToNestedJson(formData));
  return getDataForTable(structuredArray, "session")[0]?.data?.main_menu_pinned;
};
export const useOptimisticMenu = () => {
  const appCtx = useAppContext();
  const search = useSearchParams2();
  const navigation = useNavigation();
  const formData = navigation.formData;

  const newUrl = formData?.get(redirectKey);
  const newParamStr = typeof newUrl === "string" && newUrl.split("?")[1];
  const toggled = newParamStr
    ? paramsToRecord(new URLSearchParams(newParamStr)).persist_main_menu_toggled
    : search.state.persist_main_menu_toggled;

  const optimisticPinned = formData && getMainMenuPinned(formData);
  const pinned = typeof optimisticPinned === "boolean" ? optimisticPinned : appCtx.main_menu_pinned;

  return {
    toggled: toggled,
    pinned: pinned,
  };
};
