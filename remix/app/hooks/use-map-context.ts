import { create } from "zustand";
import type { Map } from "mapbox-gl";
import type { HtmlPortalNode } from "react-reverse-portal";
import { useEffect } from "react";

interface MapContext {
  map: Map | null;
  container?: HtmlPortalNode;
  fullscreen: boolean;
  rerender: number;
}

export const useMapContext = create<MapContext>(() => ({
  fullscreen: false,
  map: null,
  rerender: 0
}));

interface Args {
  static?: boolean;
  callback?: (map: Map) => void;
}

export const useMap = (args?: Args) => {
  const { map } = useMapContext();
  useEffect(() => {
    if (!map) return;

    if (args?.static) {
      map.dragPan.disable();
      map.scrollZoom.disable();
    } else {
      map.dragPan.enable();
      map.scrollZoom.enable();
    }

    args?.callback?.(map);
  }, [map, args?.callback]);
};
