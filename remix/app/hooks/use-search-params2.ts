import { create } from "zustand";
import { useNavigation, useSearchParams } from "@remix-run/react";
import { useMemo } from "react";
import { entries } from "~/misc/helpers";
import { mergeStateToParams, paramsToRecord, StateInput } from "~/misc/parsers/global-state-parsers";

interface NavOptions {
  replace?: boolean | undefined;
  state?: any;
}

const useRerender = create<{ toggle: boolean }>(() => ({
  toggle: false,
}));

export const getUrl = () => {
  return new URL(window.location.href);
};

export const rerender = () => {
  useRerender.setState((state) => ({ toggle: !state.toggle }));
};

if (typeof window !== "undefined") {
  window.addEventListener("popstate", rerender);
  // window.addEventListener("pushstate", rerender)
}

const navigateTo = (url: URL, replace?: boolean) => {
  if (replace) {
    window.history.replaceState({ clientOnly: true }, "traveltruster", url);
  } else {
    window.history.pushState({ clientOnly: true }, "traveltruster", url);
  }
  if (url.hash) {
    window.location.hash = url.hash;
  }
  rerender();
};

// https://local-tt.dinkel.works/planning?persist_month=2025-07&persist_previous_path=%2Fplanning&persist_toggle_activity_panel_id=1.Scheduled+activitiesd65b0644-07f2-4287-8caf-95be0300f29c18361820-304c-4034-95e3-a42829c5f0bc-3-1&persist_toggle_activity_panel_id=1.Scheduled+activitiesf768ac88-fb1b-4046-bb0e-2d2c195c1b0a18361820-304c-4034-95e3-a42829c5f0bc-3-1&persist_toggle_trip_panel_id=d65b0644-07f2-4287-8caf-95be0300f29c&persist_toggle_trip_panel_id=ea1893a8-2f30-405d-8665-3def6f1a744f&persist_toggle_trip_panel_id=99c2956a-d4b7-4a4c-8965-fa1fc301434d&persist_debug=true&persist_date=2025-06-19&persist_opened_menu_segments=3745cc14-25f7-4066-8926-ff67c0768e6d-operations&persist_operator_id=c4a9a9df-1c64-4845-8df9-8dcb16baab40&persist_establishment_id=3745cc14-25f7-4066-8926-ff67c0768e6d&persist_timezone=Asia%2FMakassar&toggle_trip_add_panel=3745cc14-25f7-4066-8926-ff67c0768e6d
// https://local-tt.dinkel.works/planning?persist_month=2025-07&persist_previous_path=%2Fplanning&persist_toggle_activity_panel_id=1.Scheduled+activitiesd65b0644-07f2-4287-8caf-95be0300f29c18361820-304c-4034-95e3-a42829c5f0bc-3-1&persist_toggle_activity_panel_id=1.Scheduled+activitiesf768ac88-fb1b-4046-bb0e-2d2c195c1b0a18361820-304c-4034-95e3-a42829c5f0bc-3-1&persist_toggle_trip_panel_id=d65b0644-07f2-4287-8caf-95be0300f29c&persist_toggle_trip_panel_id=ea1893a8-2f30-405d-8665-3def6f1a744f&persist_toggle_trip_panel_id=99c2956a-d4b7-4a4c-8965-fa1fc301434d&persist_debug=true&persist_date=2025-06-19&persist_opened_menu_segments=3745cc14-25f7-4066-8926-ff67c0768e6d-operations&persist_operator_id=c4a9a9df-1c64-4845-8df9-8dcb16baab40&persist_establishment_id=3745cc14-25f7-4066-8926-ff67c0768e6d&persist_timezone=Asia%2FMakassar&toggle_trip_add_panel=3745cc14-25f7-4066-8926-ff67c0768e6d

export const setParams = (args: { params?: URLSearchParams | null; replace?: boolean; hash?: string }) => {
  const url = getUrl();
  const newUrl = new URL(url);
  if (args.hash) {
    newUrl.hash = args.hash;
  }
  newUrl.search = args.params?.toString() || "";
  navigateTo(newUrl, args?.replace);
};

export const setState = (
  input?: Partial<StateInput> | ((prev: StateInput) => Partial<StateInput>) | null,
  options?: {
    replaceState?: boolean;
    replaceRoute?: boolean;
  },
) => {
  const url = getUrl();
  if (options?.replaceState || !input) {
    Array.from(url.searchParams.keys()).forEach((key) => {
      url.searchParams.delete(key);
    });
  }
  if (input) {
    const stateInput = typeof input === "function" ? input(paramsToRecord(getUrl().searchParams)) : input;
    mergeStateToParams(url.searchParams, stateInput);
  }
  navigateTo(url, options?.replaceRoute);
};

let debouncedInit: ReturnType<typeof setTimeout> | undefined | null;

export const useSearchParams2 = () => {
  const [searchParams, init] = useSearchParams();
  const navigation = useNavigation();

  const { toggle } = useRerender();

  const finalParams = useMemo(() => {
    if (typeof window !== "undefined") {
      const url = new URL(window.location.href);
      const isClientOnly = window.history.state?.clientOnly;
      const params = url.searchParams;
      const state = paramsToRecord(url.searchParams);
      const isPending = !isClientOnly && searchParams.toString() !== params.toString();
      return {
        isPending: isPending,
        params: params,
        state: state,
      };
    }
    return {
      isPending: false,
      params: searchParams,
      state: paramsToRecord(searchParams),
    };
  }, [searchParams, toggle, navigation.state]);

  const debounce = (url: URL, debounce: number, navigateOptions?: NavOptions) => {
    window.history.replaceState(null, "traveltruster", url);
    if (debouncedInit) {
      clearTimeout(debouncedInit);
    }
    debouncedInit = setTimeout(() => {
      const url = new URL(window.location.href);
      console.log("init searchpara", url.searchParams);
      init(url.searchParams, navigateOptions);
    }, debounce);
    rerender();
  };

  const pendingState = useMemo(
    () => navigation.location && paramsToRecord(new URLSearchParams(navigation.location.search)),
    [navigation.location],
  );

  const setState = (state: Partial<StateInput>, options?: { replace?: boolean }) => {
    const newUrlSearchParams = new URLSearchParams(searchParams);
    mergeStateToParams(newUrlSearchParams, state);
    init(newUrlSearchParams, options);
  };

  return {
    ...finalParams,
    pendingState: pendingState,
    setState: setState,
    debounce: debounce,
  };
};
