import { useRevalidator } from "@remix-run/react";
import { useEffect, useMemo } from "react";
import { ping } from "~/domain/event/event-fetcher";
import { toast } from "~/misc/toast";

export const useRevalidatorWithPing = () => {
  const { revalidate, state } = useRevalidator();

  return useMemo(() => {
    return {
      revalidate: async () => {
        const pingSucces = await ping();
        if (pingSucces) {
          revalidate();
        } else {
          toast("Connection Lost! Displaying Last Available Data.", "error");
        }
      },
      state,
    };
  }, [revalidate, state]);
};

export const usePageRefresh = (timeoutInSeconds: number = 20, identifier: any) => {
  const { revalidate } = useRevalidator();

  useEffect(() => {
    const timeout = setInterval(async () => {
      const pingSucces = await ping();
      if (pingSucces) {
        revalidate();
      }
    }, timeoutInSeconds * 1000);

    return () => clearInterval(timeout);
  }, [revalidate, identifier, timeoutInSeconds]);
};
