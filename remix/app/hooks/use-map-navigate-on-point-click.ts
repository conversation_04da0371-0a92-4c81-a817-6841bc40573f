import { useEffect } from "react";
import { EventData, MapLayerEventType } from "mapbox-gl";
import { useMapContext } from "~/hooks/use-map-context";
import { useNavigate } from "@remix-run/react";
import { layerIds, mapSourceId } from "~/misc/vars";

export const useMapNavigateOnPointClick = () => {
  const navigate = useNavigate();
  const { map } = useMapContext();

  useEffect(() => {
    if (map) {
      const clickHandler = (e: MapLayerEventType["click"] & EventData) => {
        const firstFeature = e.features?.[0];
        const path = firstFeature?.properties?.path;
        if (typeof path === "string") {
          navigate(path);
        }
      };
      const handleClusteClick = (e: MapLayerEventType["click"]) => {
        console.log("click point", e);

        const features = map.queryRenderedFeatures(e.point, {
          layers: [layerIds.clusters],
        });
        const clusterId = features[0]?.properties?.cluster_id;
        const source: any = map.getSource(mapSourceId);
        source?.getClusterExpansionZoom(clusterId, (err: any, zoom: number) => {
          if (err) return;

          const geom: any = features[0]?.geometry;
          map.easeTo({
            center: geom.coordinates,
            zoom: zoom,
          });
        });
      };
      map.on("click", layerIds.points, clickHandler);
      map.on("click", layerIds.clusters, handleClusteClick);

      return () => {
        map.off("click", layerIds.points, clickHandler);
        map.off("click", layerIds.clusters, handleClusteClick);
      };
    }
  }, [map]);
};
