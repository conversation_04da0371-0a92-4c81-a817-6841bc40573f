import { useEffect } from "react";
import { bbox, FeatureCollection, Point } from "@turf/turf";
import { useMapContext } from "~/hooks/use-map-context";
import buffer from "@turf/buffer";
import { layerIds, mapSourceId, primaryOrange } from "~/misc/vars";
import { Marker } from "mapbox-gl";

export const useDivingSitesLayer = (collection: FeatureCollection<Point>) => {
  const { map, fullscreen } = useMapContext();

  useEffect(() => {
    if (map && collection.features.length > 0) {
      collection.features.forEach((feature) => {
        const el = document.createElement("div");
        const lineEl = document.createElement("div");
        el.appendChild(lineEl);
        el.classList.add("dive-site-marker");
        new Marker(el).setLngLat(feature.geometry.coordinates as any).addTo(map);
      });

      map.addSource(mapSourceId, {
        type: "geojson",
        cluster: true,
        clusterRadius: 60,
        // clusterMaxZoom: 14,
        data: collection,
      });
      map.addLayer({
        type: "circle",
        source: mapSourceId,
        id: layerIds.clusters,
        filter: ["has", "point_count"],
        paint: {
          "circle-radius": 10,
          "circle-color": "red",
        },
      });
      map.addLayer({
        id: layerIds.clusterCount,
        type: "symbol",
        source: mapSourceId,
        filter: ["has", "point_count"],
        layout: {
          "text-field": "{point_count_abbreviated}",
          "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
          "text-size": 12,
        },
        paint: {
          "text-color": "#FFFFFF",
        },
      });
      map.addLayer({
        type: "circle",
        source: mapSourceId,
        id: layerIds.points,
        filter: ["!", ["has", "point_count"]],
        paint: {
          "circle-radius": 10,
          "circle-color": "blue",
        },
      });
      // map.addLayer({
      //   type: "line",
      //   source: mapSourceId,
      //   id: "divesiteline",
      //   filter: ["!", ["has", "point_count"]],
      //   paint: {
      //     "line-color": "white",
      //     "line-width": 2,
      //   },
      // });
      // map.addLayer({
      //   type: "symbol",
      //   source: "sdfsf",
      //   id: layerIds.pointNames,
      //   filter: ["!", ["has", "point_count"]],
      //   paint: {
      //     "text-color": primaryOrange,
      //   },
      // });
      map.addLayer({
        type: "symbol",
        source: mapSourceId,
        id: layerIds.pointNames,
        filter: ["!", ["has", "point_count"]],
        layout: {
          "text-offset": [0, 1],
          "text-field": "{name}",
          "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
        },
        paint: {
          "text-color": primaryOrange,
        },
      });

      return () => {
        try {
          map.removeLayer(layerIds.points);
          map.removeLayer(layerIds.pointNames);
          map.removeLayer(layerIds.clusters);
          map.removeLayer(layerIds.clusterCount);
          map.removeSource(mapSourceId);
        } catch (e) {
          console.error("could not cleanup map", e);
        }
      };
    }
  }, [map, collection, fullscreen]);

  useEffect(() => {
    if (map && collection.features.length > 0) {
      const bufferedCollection =
        collection.features.length > 1
          ? collection
          : buffer(collection, 1, {
              units: "kilometers",
            });
      const [minLng, minLat, maxLng, maxLat] = bbox(bufferedCollection);
      map.fitBounds(
        [
          [minLng, minLat],
          [maxLng, maxLat],
        ],
        { padding: 40, duration: 0 },
      );
    }
  }, [map, collection, fullscreen]);
};
