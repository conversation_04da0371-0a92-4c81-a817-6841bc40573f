import { useAppContext } from "~/hooks/use-app-context";
import { usePageOverwrites } from "~/utils/remix";

export const useShowCustomerToggle = () => {
  const app = useAppContext();
  const overwrites = usePageOverwrites();
  const adminMember = app.members.find((member) => !!member.admin && member.establishment_id === overwrites.establishment_id);

  return overwrites.customer_toggle && adminMember;
};
