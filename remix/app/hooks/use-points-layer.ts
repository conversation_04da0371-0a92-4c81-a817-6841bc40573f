import { useEffect } from "react";
import type { FeatureCollection, Point } from "@turf/turf";
import { bbox } from "@turf/turf";
import { useMapContext } from "~/hooks/use-map-context";
import buffer from "@turf/buffer";
import { layerIds, mapSourceId, primaryOrange } from "~/misc/vars";

export const usePointsLayer = (collection: FeatureCollection<Point>) => {
  const { map, fullscreen } = useMapContext();

  useEffect(() => {
    if (map && collection.features.length > 0) {
      try {
        map.addSource(mapSourceId, {
          type: "geojson",
          cluster: true,
          clusterRadius: 60,
          data: collection,
        });
        map.addLayer({
          type: "circle",
          source: mapSourceId,
          id: layerIds.clusters,
          filter: ["has", "point_count"],
          paint: {
            "circle-radius": 10,
            "circle-color": primaryOrange,
          },
        });
        map.addLayer({
          id: layerIds.clusterCount,
          type: "symbol",
          source: mapSourceId,
          filter: ["has", "point_count"],
          layout: {
            "text-field": "{point_count_abbreviated}",
            "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
            "text-size": 12,
          },
          paint: {
            "text-color": "#FFFFFF",
          },
        });
        map.addLayer({
          type: "circle",
          source: mapSourceId,
          id: layerIds.points,
          filter: ["!", ["has", "point_count"]],
          paint: {
            "circle-radius": 10,
            "circle-color": primaryOrange,
          },
        });
        map.addLayer({
          type: "symbol",
          source: mapSourceId,
          id: layerIds.pointNames,
          filter: ["!", ["has", "point_count"]],
          layout: {
            "text-offset": [0, 1],
            "text-field": "{name}",
            "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
          },
          paint: {
            "text-color": primaryOrange,
          },
        });
      } catch (e) {
        console.error("could not create points on map", e);
      }

      return () => {
        try {
          map.removeLayer(layerIds.points);
          map.removeLayer(layerIds.pointNames);
          map.removeLayer(layerIds.clusters);
          map.removeLayer(layerIds.clusterCount);
          map.removeSource(mapSourceId);
        } catch (e) {
          console.error("could not cleanup map", e);
        }
      };
    }
  }, [map, collection, fullscreen]);

  useEffect(() => {
    if (map && collection.features.length > 0) {
      try {
        const bufferedCollection =
          collection.features.length > 1
            ? collection
            : buffer(collection, 1, {
                units: "kilometers",
              });
        const [minLng, minLat, maxLng, maxLat] = bbox(bufferedCollection);
        map.fitBounds(
          [
            [minLng, minLat],
            [maxLng, maxLat],
          ],
          { padding: 40, duration: 0 },
        );
      } catch (e) {
        console.error("could not fit bound", e);
      }
    }
  }, [map, collection, fullscreen]);
};
