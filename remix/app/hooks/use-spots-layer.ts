import { useEffect } from "react";
import { bbox, FeatureCollection, Point } from "@turf/turf";
import { useMapContext } from "~/hooks/use-map-context";
import { layerIds, mapSourceId, primaryOrange } from "~/misc/vars";

export const useSpotsLayer = (collection: FeatureCollection<Point>) => {
  const { map } = useMapContext();

  useEffect(() => {
    if (map && collection.features.length > 0) {
      const layedIdOlCount = "spot-counts";
      map.addSource(mapSourceId, {
        type: "geojson",
        data: collection,
      });
      map.addLayer({
        type: "circle",
        source: mapSourceId,
        id: layerIds.points,
        interactive: true,
        paint: {
          "circle-radius": 10,
          "circle-color": primaryOrange,
        },
      });
      map.addLayer({
        type: "symbol",
        source: mapSourceId,
        id: layedIdOlCount,
        layout: {
          "text-field": "{count}",
          "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
        },
        paint: {
          "text-color": "white",
        },
      });
      map.addLayer({
        type: "symbol",
        minzoom: 10,
        source: mapSourceId,
        id: layerIds.pointNames,
        layout: {
          "text-offset": [0, 1],
          "text-field": "{name}",
          "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
        },
        paint: {
          "text-color": primaryOrange,
        },
      });

      return () => {
        map.removeLayer(layerIds.points);
        map.removeLayer(layedIdOlCount);
        map.removeLayer(layerIds.pointNames);
        map.removeSource(mapSourceId);
      };
    }
  }, [map, collection]);

  useEffect(() => {
    if (map && collection.features.length > 0) {
      const [minLng, minLat, maxLng, maxLat] = bbox(collection);
      map.fitBounds(
        [
          [minLng, minLat],
          [maxLng, maxLat],
        ],
        { padding: 40, duration: 0 }
      );
    }
  }, [map, collection]);
};
