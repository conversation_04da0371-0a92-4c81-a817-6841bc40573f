import { useEffect, useState } from "react";

interface Dimensions {
  width: number;
  height: number;
}

export function useDimensions() {
  const [dimensions, setDimensions] = useState<Dimensions | null>(null);

  useEffect(() => {
    const updateDimensions = () => {
      setDimensions({
        width: window.visualViewport?.width ?? window.innerWidth,
        height: window.visualViewport?.height ?? window.innerHeight,
      });
    };

    window.addEventListener("resize", updateDimensions);
    window.visualViewport?.addEventListener("resize", updateDimensions);

    return () => {
      window.removeEventListener("resize", updateDimensions);
      window.visualViewport?.removeEventListener("resize", updateDimensions);
    };
  }, [setDimensions]);

  return dimensions;
}
