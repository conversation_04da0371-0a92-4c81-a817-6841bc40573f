msgid ""
msgstr ""
"POT-Creation-Date: 2024-03-30 07:31+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: fr\n"
"Project-Id-Version: traveltruster\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-28 13:35\n"
"Last-Translator: \n"
"Language-Team: French\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Crowdin-Project: traveltruster\n"
"X-Crowdin-Project-ID: 668342\n"
"X-Crowdin-Language: fr\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 2\n"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:603
#~ msgid "{0, plural, one {upload} other {uploads}}"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
#~ msgid "&sl;1y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:8
#~ msgid "&sl;3mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:9
#~ msgid "&sl;6mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
msgid "<1y"
msgstr "<1an"

#: app/domain/participant/participant-helpers.tsx:12
#~ msgid "<2y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
msgid "<3mo"
msgstr "<3m."

#: app/domain/participant/participant-helpers.tsx:11
msgid "<6mo"
msgstr "<6m."

#: app/domain/participant/participant-helpers.tsx:13
msgid ">1y"
msgstr ">1an"

#: app/domain/participant/participant-helpers.tsx:14
msgid ">2y"
msgstr ">2an"

#: app/routes/_all._catch.waiver.$id.tsx:381
msgid "Address"
msgstr "Adresse"

#: app/domain/participant/participant-data.tsx:52
msgid "Agency"
msgstr "Agence"

#: app/routes/_all._catch.waiver.$id.tsx:349
msgid "Approved – I find no conditions that I consider incompatible with recreational scuba diving or freediving."
msgstr "Approuvé - Je ne trouve aucune condition que je considère incompatible avec la plongée loisir ou l’apnée."

#: app/misc/error-translations.ts:7
msgid "Authentication code was expired"
msgstr "Le code d'authentification a expiré"

#: app/domain/participant/participant-data.tsx:34
msgid "BCD"
msgstr "Gilet de stabilisation (BCD)"

#: app/domain/participant/ParticipantFields.tsx:1221
msgid "BCD Size"
msgstr "Taille du gilet de stabilisation"

#: app/routes/_all._catch.waiver.$id.tsx:324
msgid "Birthdate"
msgstr "Date de naissance"

#: app/routes/_all._catch.participant_.mutate.tsx:843
msgid "Booking"
msgstr "Réservation"

#: app/domain/participant/participant-fields.tsx:11
msgid "Bring my own"
msgstr "Apportez-moi le mien"

#: app/domain/participant/ParticipantFields.tsx:694
#~ msgid "Certificate"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:690
msgid "Certificate details"
msgstr "Détails du brevet"

#: app/domain/participant/ParticipantFields.tsx:667
msgid "Certificate level"
msgstr "Niveau de certificat"

#: app/domain/participant/ParticipantFields.tsx:686
#~ msgid "Certificate number"
#~ msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:996
msgid "Certification"
msgstr "Certificat"

#: app/domain/participant/ParticipantFields.tsx:644
msgid "Certification organisation"
msgstr "Organisme de certification"

#: app/routes/_all._catch.participant_.mutate.tsx:1416
msgid "Click to read"
msgstr "Cliquez pour lire"

#: app/routes/_all._catch.waiver.$id.tsx:378
msgid "Clinic/Hospital"
msgstr "Clinique/Hôpital"

#: app/routes/_all._catch.waiver.$id.tsx:375
msgid "Clinical Degrees/Credentials"
msgstr "Spécialité"

#: app/domain/participant/ParticipantFields.tsx:607
msgid "Company and policy number"
msgstr "Entreprise et numéro de police"

#: app/routes/_all._catch.participant_.mutate.tsx:1217
#~ msgid "Continue"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Copy"
msgstr "Copie"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1008
#: app/domain/participant/ParticipantFields.tsx:443
msgid "Country"
msgstr "Pays"

#: app/routes/_all._catch.waiver.$id.tsx:403
msgid "Created by the <0>Diver Medical Screen Committee</0> in association with the following bodies:"
msgstr "Créé par la <0>Diver Medical Screen Committee</0> en association avec les organismes suivants:"

#: app/routes/_all._catch.waiver.$id.tsx:369
msgid "Date (dd/mm/yyyy)"
msgstr "Créé par le Comité de dépistage médical des plongeurs en association avec les organismes suivants:"

#: app/domain/participant/ParticipantFields.tsx:516
msgid "Date of birth"
msgstr "Date de naissance"

#: app/domain/participant/ParticipantFields.tsx:126
msgid "Dive specific details"
msgstr "Détails spécifiques à la plongée"

#: app/routes/_all._catch.waiver.$id.tsx:308
msgid "Diver Medical"
msgstr "Plongeur Médical"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1000
msgid "Dives"
msgstr "Nombre de plongées"

#: app/domain/participant/ParticipantFields.tsx:606
msgid "Do you have an insurance that covers underwater activities? please fill your details below"
msgstr "Avez-vous une assurance qui couvre les activités sous-marines? Veuillez remplir vos coordonnées ci-dessous"

#: app/domain/participant/ParticipantFields.tsx:641
msgid "e.g. friend, father or mother"
msgstr "Par exemple ami, père, mère"

#: app/domain/participant/ParticipantFields.tsx:497
msgid "e.g. Hotel name"
msgstr "Par exemple, nom de l'hôtel"

#: app/domain/participant/ParticipantFields.tsx:601
msgid "e.g. nut allergy"
msgstr "par exemple allergie aux noix"

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Edit"
msgstr "Éditer"

#: app/routes/_all._catch.waiver.$id.tsx:387
msgid "Email"
msgstr "E-mail"

#: app/routes/_all._catch.participant_.mutate.tsx:1201
msgid "Email OTP"
msgstr "OTP par e-mail"

#: app/domain/participant/ParticipantFields.tsx:118
msgid "Emergency contact details"
msgstr "Coordonnées de contact d'urgence"

#: app/domain/participant/ParticipantFields.tsx:611
msgid "Emergency contact name"
msgstr "Nom du contact d'urgence"

#: app/domain/participant/ParticipantFields.tsx:615
msgid "Emergency contact phone number"
msgstr "Numéro de téléphone du contact d'urgence"

#: app/components/base/AddressInput.tsx:70
msgid "Enter a location"
msgstr "Renseigner l’adresse"

#: app/domain/participant/ParticipantFields.tsx:396
#~ msgid "Enter here"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:390
#~ msgid "Enter here or upload below"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:347
msgid "Enter number"
msgstr "Entrez le numéro"

#: app/routes/_all._catch._index.tsx:154
msgid "Explore Bali underwater"
msgstr "Explorez Bali sous l'eau"

#: app/domain/participant/participant-data.tsx:46
msgid "Facebook"
msgstr "Facebook"

#: app/domain/participant/ParticipantFields.tsx:130
msgid "Feedback"
msgstr "Commentaires"

#: app/domain/participant/GenderSelect.tsx:9
msgid "Female"
msgstr "Femme"

#: app/domain/participant/participant-data.tsx:33
msgid "Fins"
msgstr "Palmes"

#: app/domain/participant/ParticipantFields.tsx:407
msgid "First name"
msgstr "Prénom"

#: app/domain/participant/ParticipantFields.tsx:600
msgid "Food allergies"
msgstr "Allergies alimentaires"

#: app/domain/participant/participant-data.tsx:44
msgid "Friend"
msgstr "Ami"

#: app/domain/participant/ParticipantFields.tsx:543
msgid "Gender"
msgstr "Sexe"

#: app/domain/participant/participant-fields.tsx:9
msgid "Gluten free"
msgstr "Sans gluten"

#: app/domain/participant/participant-data.tsx:48
msgid "Google"
msgstr "Google"

#: app/domain/participant/participant-data.tsx:49
msgid "Google Maps"
msgstr "Google Maps"

#: app/domain/participant/ParticipantFields.tsx:808
msgid "Height"
msgstr "Taille"

#: app/misc/error-translations.ts:14
msgid "Het email adres is al in gebruik door een ander account."
msgstr "L'adresse e-mail est déjà utilisée par un autre compte"

#: app/domain/participant/ParticipantFields.tsx:463
msgid "Home/residential address"
msgstr "Adresse domicile/résidentielle"

#: app/domain/participant/participant-data.tsx:51
msgid "Hotel"
msgstr "Hôtel"

#: app/domain/participant/ParticipantFields.tsx:385
#~ msgid "Hotel/hostel/homestay"
#~ msgstr ""

#. placeholder {0}: waiver.translation?.name
#: app/routes/_all._catch.participant_.mutate.tsx:1458
msgid "I have read and agree with the above <0>{0}</0>"
msgstr "J'ai lu et j'accepte ce qui précède <0>{0}</0>"

#: app/routes/_all._catch.participant_.mutate.tsx:800
#~ msgid "I have read and agree with the above <0>{0}</0> and agree"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1290
msgid "If you require any additional addons, select the amount below"
msgstr "Si vous avez besoin d'extensions supplémentaires, veuillez sélectionner la quantité ci-dessous"

#: app/domain/participant/ParticipantFields.tsx:121
msgid "Important! Ensure the emergency contact is not someone joining the activity."
msgstr "Important ! Assurez-vous que le contact d'urgence n'est pas une personne participant à l'activité."

#: app/domain/participant/participant-data.tsx:45
msgid "Instagram"
msgstr "Instagram"

#: app/domain/participant/ParticipantFields.tsx:605
msgid "Insurance"
msgstr "Assurances"

#: app/misc/error-translations.ts:9
msgid "Invalid credentials"
msgstr "Identifiants invalides"

#: app/misc/error-translations.ts:8
msgid "Invalid email address"
msgstr "Adresse e-mail erronée"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1004
#: app/domain/participant/ParticipantFields.tsx:712
msgid "Last dive"
msgstr "Dernière plongée"

#: app/domain/participant/ParticipantFields.tsx:421
msgid "Last name"
msgstr "Nom de famille"

#: app/domain/participant/GenderSelect.tsx:8
msgid "Male"
msgstr "Homme"

#: app/domain/participant/participant-data.tsx:32
msgid "Mask"
msgstr "Masque"

#: app/domain/participant/ParticipantFields.tsx:1226
msgid "May we ask how you heard about us?"
msgstr "Pouvons-nous vous demander comment vous avez entendu parler de nous ?"

#: app/domain/participant/ParticipantFields.tsx:1230
msgid "May we contact you to ask about your experience?"
msgstr "Pouvons-nous vous contacter pour vous renseigner sur votre expérience?"

#: app/domain/participant/ParticipantFields.tsx:595
msgid "Meal preferences"
msgstr "Préférences alimentaires"

#: app/routes/_all._catch.waiver.$id.tsx:312
msgid "Medical Examiner's Evaluation Form"
msgstr "Formulaire d’évaluation du médecin"

#: app/routes/_all._catch.waiver.$id.tsx:372
msgid "Medical Examiner’s Name"
msgstr "Nom de l'examinateur"

#: app/domain/participant/participant-helpers.tsx:13
msgid "More than 1 year ago"
msgstr "Il y a plus d'un an"

#: app/domain/participant/participant-helpers.tsx:14
msgid "more than 2 years ago"
msgstr "Il y a plus de 2 ans"

#: app/misc/error-translations.ts:67
msgid "Name already exists under this establishment"
msgstr "Ce nom existe déjà sous cet établissement"

#: app/domain/participant/participant.signature.component.tsx:97
msgid "Name of parent or guardian"
msgstr "Nom du parent ou du tuteur"

#: app/domain/participant/participant.signature.component.tsx:57
msgid "Name participant"
msgstr "Nom du participant"

#: app/routes/_all._catch.waiver.$id.tsx:231
#: app/routes/_all._catch.waiver.$id.tsx:282
#: app/domain/participant/ParticipantFields.tsx:1267
msgid "No"
msgstr "Non"

#: app/misc/error-translations.ts:6
msgid "No changes were made"
msgstr "Aucun changement n'a été apporté"

#: app/domain/participant/participant-fields.tsx:10
msgid "No preference"
msgstr "Aucune préférence"

#: app/domain/participant/GenderSelect.tsx:10
msgid "Non-binary"
msgstr "Non-binaire"

#: app/routes/_all._catch.waiver.$id.tsx:355
msgid "Not approved – I find conditions that I consider incompatible with recreational scuba diving or freediving"
msgstr "Non approuvé – Je trouve des conditions que je considère incompatibles avec la plongée sous-marine récréative ou l'apnée."

#: app/domain/participant/ParticipantFields.tsx:704
msgid "Number of dives"
msgstr "Nombre de plongées"

#: app/domain/participant/ParticipantFields.tsx:436
msgid "Or a valid local ID card"
msgstr "Ou une carte d'identité locale valide"

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "or an ID that's valid in the operator country"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:804
msgid "Or t-shirt size if you don't know your wetsuit size."
msgstr "Ou taille de t-shirt si vous ne connaissez pas votre taille de combinaison de plongée."

#: app/domain/participant/ParticipantFields.tsx:134
msgid "Other info (optional)"
msgstr "Autres informations (facultatif)"

#: app/domain/participant/ParticipantFields.tsx:749
msgid "Own gear"
msgstr "Equipement personnel"

#: app/misc/error-translations.ts:10
msgid "Page was not found or you are unauthorized"
msgstr "La page n'a pas été trouvée ou vous n'êtes pas autorisé"

#: app/domain/participant/ParticipantFields.tsx:114
msgid "Participant details"
msgstr "Détails des participants"

#: app/routes/_all._catch.waiver.$id.tsx:318
msgid "Participant name"
msgstr "Nom du participant"

#: app/domain/participant/ParticipantFields.tsx:435
msgid "Passport"
msgstr "Passeport"

#: app/domain/participant/ParticipantFields.tsx:303
#~ msgid "Passport No."
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "Passport number"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:384
#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1012
msgid "Phone"
msgstr "Téléphone"

#: app/domain/participant/ParticipantFields.tsx:569
msgid "Phone number"
msgstr "Numéro de téléphone"

#: app/routes/_all._catch.waiver.$id.tsx:396
msgid "Physician/Clinic Stamp (optional)"
msgstr "Cachet du médecin/Clinique (optionnel)"

#: app/routes/_all._catch._w.planning.u.tsx:107
msgid "Planning"
msgstr "Planification"

#: app/routes/_all._catch.participant_.mutate.tsx:1406
msgid "Please check this box to continue."
msgstr "Veuillez cocher cette case pour continuer."

#: app/domain/participant/ParticipantFields.tsx:1289
msgid "Please fill any additional important info or questions below"
msgstr "Veuillez remplir toutes les informations supplémentaires importantes ou poser des questions ci-dessous"

#: app/domain/participant/ParticipantFields.tsx:750
msgid "Please select in case you bring your own gear."
msgstr "Veuillez sélectionner si vous apportez votre propre équipement."

#: app/routes/_all._catch.participant_.mutate.tsx:883
msgid "Please select the activity (or activities) you're attending."
msgstr "Veuillez sélectionner l'activité (ou les activités) auxquelles vous participez."

#: app/domain/participant/GenderSelect.tsx:11
msgid "Prefer not to say"
msgstr "Préfère ne pas dire"

#: app/routes/_all._catch.participant_.mutate.tsx:830
msgid "Preferred language"
msgstr "Langue préférée"

#: app/routes/_all._catch.participant_.mutate.tsx:1250
msgid "Recipient email"
msgstr "E-mail du destinataire"

#: app/routes/_all._catch.participant_.mutate.tsx:1149
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Register"
msgstr "S'enregistrer"

#: app/domain/participant/participant-data.tsx:35
msgid "Regulator"
msgstr "Détendeur"

#: app/domain/participant/ParticipantFields.tsx:640
msgid "Relationship"
msgstr "Relation"

#: app/routes/_all._catch._index.tsx:156
msgid "Research & booking made easier"
msgstr "La recherche et la réservation sont facilitées"

#: app/routes/_all._catch.participant_.mutate.tsx:1191
msgid "Returning participant? Retrieve your registration via OTP email verification."
msgstr "Déjà participant ? Récupérez votre inscription via la vérification par e-mail OTP."

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:992
#: app/domain/participant/ParticipantFields.tsx:510
msgid "Room number"
msgstr "Numéro de chambre"

#: app/domain/participant/ParticipantFields.tsx:511
msgid "Room number of your stay"
msgstr "Numéro de chambre de votre séjour"

#: app/domain/participant/ParticipantFields.tsx:425
#~ msgid "Room number of your stay at our resort"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:562
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Save"
msgstr "Sauvegarder"

#: app/domain/participant/ParticipantFields.tsx:737
msgid "select"
msgstr "sélectionner"

#: app/domain/participant/ParticipantFields.tsx:1338
msgid "Select"
msgstr "Sélectionner"

#: app/routes/_all._catch.participant_.mutate.tsx:1281
msgid "Select add-ons"
msgstr "Sélectionner les extensions"

#: app/components/CountrySelectHeadlessUi.tsx:100
#: app/components/CountrySelect.tsx:171
msgid "select country"
msgstr "sélectionner le pays"

#: app/components/CountrySelectHeadlessUi.tsx:133
#: app/components/CountrySelect.tsx:202
msgid "Select country"
msgstr "Sélectionner le pays"

#: app/components/CountrySelectHeadlessUi.tsx:28
#: app/components/CountrySelect.tsx:41
msgid "Select Country"
msgstr "Sélectionner le pays"

#: app/domain/participant/GenderSelect.tsx:67
msgid "Select gender"
msgstr "Sélectionner le sexe"

#: app/domain/participant/ParticipantFields.tsx:197
msgid "Select level"
msgstr "Sélectionnez le niveau"

#: app/routes/_all._catch.participant_.mutate.tsx:646
#~ msgid "Select one or more activities"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:222
msgid "Select organisation"
msgstr "Sélectionnez l'organisation"

#: app/domain/participant/ParticipantFields.tsx:803
msgid "Select size"
msgstr "Sélectionnez la taille"

#: app/domain/participant/ParticipantFields.tsx:1012
msgid "Shoe size"
msgstr "Pointure"

#: app/routes/_all._catch.waiver.$id.tsx:364
msgid "Signature of certified medical doctor or other legally certified medical provider"
msgstr "Signature d'un médecin certifié ou d'un prestateur de soins de santé légalement certifié"

#: app/domain/participant/participant.signature.component.tsx:131
#: app/domain/participant/participant.signature.component.tsx:137
msgid "Signature of parent or guardian"
msgstr "Nom du parent ou du tuteur"

#: app/domain/participant/participant.signature.component.tsx:78
#: app/domain/participant/participant.signature.component.tsx:84
msgid "Signature participant"
msgstr "Participant à la signature"

#: app/domain/participant/ParticipantFields.tsx:488
msgid "Stay"
msgstr "Séjour"

#: app/domain/participant/ParticipantFields.tsx:386
#~ msgid "Staying with us? Please fill your room number."
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:330
msgid "The above-named person requests your opinion of his/her medical suitability to participate in recreational scuba diving or freediving training or activity. Please visit <0>uhms.org</0> for medical guidance on medical conditions as they relate to diving. Review the areas relevant to your patient as part of your evaluation."
msgstr "La personne susmentionnée demande votre avis sur son aptitude médicale à participer à une formation ou à une activité de plongée\n"
"sous-marine ou de plongée en apnée. Si nécessaire, vous pouvez consulter <0>uhms.org</0> pour obtenir des conseils médicaux sur les\n"
"conditions médicales liées à la plongée. Passez en revue les domaines pertinents pour votre patient dans le cadre de votre évaluation."

#: app/domain/participant/participant.signature.component.tsx:23
msgid "Today's date"
msgstr "Date d'aujourd'hui"

#: app/domain/participant/participant-fields.tsx:8
msgid "Vegan"
msgstr "Vegan"

#: app/domain/participant/participant-fields.tsx:7
msgid "Vegetarian"
msgstr "Végétarien"

#: app/routes/_all._catch._w.waiver_.mutate.tsx:173
#~ msgid "View"
#~ msgstr "View"

#: app/misc/error-translations.ts:15
msgid "Wachtwoord moet tenminste 6 tekens lang zijn."
msgstr "Le mot de passe doit comporter au moins 6 caractères."

#: app/domain/participant/participant-data.tsx:47
msgid "Walk-in"
msgstr "Visite spontanée"

#: app/domain/participant/ParticipantFields.tsx:910
msgid "Weight"
msgstr "Poids"

#: app/domain/participant/ParticipantFields.tsx:1115
msgid "Weightbelt"
msgstr "Ceinture de plombs "

#: app/domain/participant/participant-data.tsx:36
msgid "Wetsuit"
msgstr "Combinaison néoprène"

#: app/domain/participant/ParticipantFields.tsx:802
msgid "Wetsuit size"
msgstr "Taille de la combinaison néoprène"

#: app/domain/participant/ParticipantFields.tsx:570
msgid "WhatsApp preferred"
msgstr "WhatsApp préféré"

#: app/domain/participant/participant-helpers.tsx:10
msgid "Within the last 3 months"
msgstr "Au cours des 3 derniers mois"

#: app/domain/participant/participant-helpers.tsx:11
msgid "Within the last 6 months"
msgstr "Au cours des 6 derniers mois"

#: app/domain/participant/participant-helpers.tsx:12
msgid "Within the last year"
msgstr "Au cours de la dernière année"

#: app/domain/participant/ParticipantFields.tsx:709
msgid "Years of experience"
msgstr "Années d'expérience"

#: app/routes/_all._catch.waiver.$id.tsx:214
#: app/routes/_all._catch.waiver.$id.tsx:265
#: app/domain/participant/ParticipantFields.tsx:1253
msgid "Yes"
msgstr "Oui"

#: app/domain/participant/ParticipantFields.tsx:1285
msgid "Your Instagram Handle"
msgstr "Votre nom d'utilisateur Instagram"

#: app/domain/participant/participant-data.tsx:50
msgid "YouTube"
msgstr "YouTube"

