msgid ""
msgstr ""
"POT-Creation-Date: 2024-03-30 07:31+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: el\n"
"Project-Id-Version: traveltruster\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-28 13:35\n"
"Last-Translator: \n"
"Language-Team: Greek\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: traveltruster\n"
"X-Crowdin-Project-ID: 668342\n"
"X-Crowdin-Language: el\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 2\n"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:603
#~ msgid "{0, plural, one {upload} other {uploads}}"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
#~ msgid "&sl;1y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:8
#~ msgid "&sl;3mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:9
#~ msgid "&sl;6mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
msgid "<1y"
msgstr "<1χρ."

#: app/domain/participant/participant-helpers.tsx:12
#~ msgid "<2y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
msgid "<3mo"
msgstr "<3μ."

#: app/domain/participant/participant-helpers.tsx:11
msgid "<6mo"
msgstr "<6μ."

#: app/domain/participant/participant-helpers.tsx:13
msgid ">1y"
msgstr ">1χρ."

#: app/domain/participant/participant-helpers.tsx:14
msgid ">2y"
msgstr ">2χρ."

#: app/routes/_all._catch.waiver.$id.tsx:381
msgid "Address"
msgstr "Διεύθυνση"

#: app/domain/participant/participant-data.tsx:52
msgid "Agency"
msgstr "Πρακτορείο"

#: app/routes/_all._catch.waiver.$id.tsx:349
msgid "Approved – I find no conditions that I consider incompatible with recreational scuba diving or freediving."
msgstr "Εγκρίθηκε - Δεν βρίσκω παθολογικές καταστάσεις που θεωρώ ασυμβίβαστες με δραστηριότητες αναψυχής που σχετίζονται με την\n"
"αυτόνομη ή ελεύθερη κατάδυση."

#: app/misc/error-translations.ts:7
msgid "Authentication code was expired"
msgstr "Ο κωδικός ελέγχου ταυτότητας έληξε"

#: app/domain/participant/participant-data.tsx:34
msgid "BCD"
msgstr "BCD"

#: app/domain/participant/ParticipantFields.tsx:1221
msgid "BCD Size"
msgstr "Μέγεθος BCD"

#: app/routes/_all._catch.waiver.$id.tsx:324
msgid "Birthdate"
msgstr "Ημερομηνία γέννησης"

#: app/routes/_all._catch.participant_.mutate.tsx:843
msgid "Booking"
msgstr "Κράτηση"

#: app/domain/participant/participant-fields.tsx:11
msgid "Bring my own"
msgstr "Να φέρω το δικό μου"

#: app/domain/participant/ParticipantFields.tsx:694
#~ msgid "Certificate"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:690
msgid "Certificate details"
msgstr "Λεπτομέρειες πιστοποιητικού"

#: app/domain/participant/ParticipantFields.tsx:667
msgid "Certificate level"
msgstr "Επίπεδο πιστοποιητικού"

#: app/domain/participant/ParticipantFields.tsx:686
#~ msgid "Certificate number"
#~ msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:996
msgid "Certification"
msgstr "Πιστοποίηση"

#: app/domain/participant/ParticipantFields.tsx:644
msgid "Certification organisation"
msgstr "Οργανισμός πιστοποίησης"

#: app/routes/_all._catch.participant_.mutate.tsx:1416
msgid "Click to read"
msgstr "Κάντε κλικ για να διαβάσετε"

#: app/routes/_all._catch.waiver.$id.tsx:378
msgid "Clinic/Hospital"
msgstr "Κλινική/Νοσοκομείο"

#: app/routes/_all._catch.waiver.$id.tsx:375
msgid "Clinical Degrees/Credentials"
msgstr "Κλινικοί Βαθμοί/Προσόντα"

#: app/domain/participant/ParticipantFields.tsx:607
msgid "Company and policy number"
msgstr "Εταιρεία και αριθμός ασφαλιστηρίου"

#: app/routes/_all._catch.participant_.mutate.tsx:1217
#~ msgid "Continue"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Copy"
msgstr "Αντιγραφή"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1008
#: app/domain/participant/ParticipantFields.tsx:443
msgid "Country"
msgstr "Χώρα"

#: app/routes/_all._catch.waiver.$id.tsx:403
msgid "Created by the <0>Diver Medical Screen Committee</0> in association with the following bodies:"
msgstr "Δημιουργήθηκε από την Επιτροπή Ιατρικής Αξιολόγηση Δυτών <0>(Diver Medical Screen Committee)</0> σε συνεργασία με τους ακόλουθους φορείς: "

#: app/routes/_all._catch.waiver.$id.tsx:369
msgid "Date (dd/mm/yyyy)"
msgstr "Ημερομηνία (ηη/μμ/εεεε)"

#: app/domain/participant/ParticipantFields.tsx:516
msgid "Date of birth"
msgstr "Ημερομηνία γέννησης"

#: app/domain/participant/ParticipantFields.tsx:126
msgid "Dive specific details"
msgstr "Ειδικές λεπτομέρειες κατάδυσης"

#: app/routes/_all._catch.waiver.$id.tsx:308
msgid "Diver Medical"
msgstr "Ιατρική κατάδυσης"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1000
msgid "Dives"
msgstr "Καταδύσεις"

#: app/domain/participant/ParticipantFields.tsx:606
msgid "Do you have an insurance that covers underwater activities? please fill your details below"
msgstr "Έχετε ασφάλεια που καλύπτει υποβρύχιες δραστηριότητες; Παρακαλώ συμπληρώστε τα στοιχεία σας παρακάτω"

#: app/domain/participant/ParticipantFields.tsx:641
msgid "e.g. friend, father or mother"
msgstr "π.χ. φίλος, πατέρας ή μητέρα"

#: app/domain/participant/ParticipantFields.tsx:497
msgid "e.g. Hotel name"
msgstr "π.χ. Όνομα ξενοδοχείου"

#: app/domain/participant/ParticipantFields.tsx:601
msgid "e.g. nut allergy"
msgstr "π.χ. αλλεργία στους ξηρούς καρπούς"

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Edit"
msgstr "Επεξεργασία"

#: app/routes/_all._catch.waiver.$id.tsx:387
msgid "Email"
msgstr "Ηλεκτρονικό ταχυδρομείο"

#: app/routes/_all._catch.participant_.mutate.tsx:1201
msgid "Email OTP"
msgstr "Κωδικός OTP Ηλεκτρονικού Ταχυδρομείου"

#: app/domain/participant/ParticipantFields.tsx:118
msgid "Emergency contact details"
msgstr "Στοιχεία επικοινωνίας έκτακτης ανάγκης"

#: app/domain/participant/ParticipantFields.tsx:611
msgid "Emergency contact name"
msgstr "Όνομα επικοινωνίας έκτακτης ανάγκης"

#: app/domain/participant/ParticipantFields.tsx:615
msgid "Emergency contact phone number"
msgstr "Αριθμός τηλεφώνου επικοινωνίας έκτακτης ανάγκης"

#: app/components/base/AddressInput.tsx:70
msgid "Enter a location"
msgstr "Εισαγάγετε μια τοποθεσία"

#: app/domain/participant/ParticipantFields.tsx:396
#~ msgid "Enter here"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:390
#~ msgid "Enter here or upload below"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:347
msgid "Enter number"
msgstr "Εισαγάγετε αριθμό"

#: app/routes/_all._catch._index.tsx:154
msgid "Explore Bali underwater"
msgstr "Εξερευνήστε το Μπαλί υποβρυχίως"

#: app/domain/participant/participant-data.tsx:46
msgid "Facebook"
msgstr "Facebook"

#: app/domain/participant/ParticipantFields.tsx:130
msgid "Feedback"
msgstr "Σχόλια"

#: app/domain/participant/GenderSelect.tsx:9
msgid "Female"
msgstr "Γυναίκα"

#: app/domain/participant/participant-data.tsx:33
msgid "Fins"
msgstr "Πτερύγια"

#: app/domain/participant/ParticipantFields.tsx:407
msgid "First name"
msgstr "Όνομα"

#: app/domain/participant/ParticipantFields.tsx:600
msgid "Food allergies"
msgstr "Αλλεργίες σε τρόφιμα"

#: app/domain/participant/participant-data.tsx:44
msgid "Friend"
msgstr "Φίλος"

#: app/domain/participant/ParticipantFields.tsx:543
msgid "Gender"
msgstr "Φύλο"

#: app/domain/participant/participant-fields.tsx:9
msgid "Gluten free"
msgstr "Χωρίς γλουτένη"

#: app/domain/participant/participant-data.tsx:48
msgid "Google"
msgstr "Google"

#: app/domain/participant/participant-data.tsx:49
msgid "Google Maps"
msgstr "Google Maps"

#: app/domain/participant/ParticipantFields.tsx:808
msgid "Height"
msgstr "Ύψος"

#: app/misc/error-translations.ts:14
msgid "Het email adres is al in gebruik door een ander account."
msgstr "Η διεύθυνση ηλεκτρονικού ταχυδρομείου χρησιμοποιείται ήδη από άλλον λογαριασμό."

#: app/domain/participant/ParticipantFields.tsx:463
msgid "Home/residential address"
msgstr "Διεύθυνση κατοικίας"

#: app/domain/participant/participant-data.tsx:51
msgid "Hotel"
msgstr "Ξενοδοχείο"

#: app/domain/participant/ParticipantFields.tsx:385
#~ msgid "Hotel/hostel/homestay"
#~ msgstr ""

#. placeholder {0}: waiver.translation?.name
#: app/routes/_all._catch.participant_.mutate.tsx:1458
msgid "I have read and agree with the above <0>{0}</0>"
msgstr "Έχω διαβάσει και συμφωνώ με τα παραπάνω <0>{0}</0>"

#: app/routes/_all._catch.participant_.mutate.tsx:800
#~ msgid "I have read and agree with the above <0>{0}</0> and agree"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1290
msgid "If you require any additional addons, select the amount below"
msgstr "Αν χρειάζεστε πρόσθετα πρόσθετα, επιλέξτε την ποσότητα παρακάτω"

#: app/domain/participant/ParticipantFields.tsx:121
msgid "Important! Ensure the emergency contact is not someone joining the activity."
msgstr "Σημαντικό! Βεβαιωθείτε ότι ο υπεύθυνος επικοινωνίας έκτακτης ανάγκης δεν είναι κάποιος που συμμετέχει στην δραστηριότητα."

#: app/domain/participant/participant-data.tsx:45
msgid "Instagram"
msgstr "Instagram"

#: app/domain/participant/ParticipantFields.tsx:605
msgid "Insurance"
msgstr "Ασφάλιση"

#: app/misc/error-translations.ts:9
msgid "Invalid credentials"
msgstr "Μη έγκυρα διαπιστευτήρια"

#: app/misc/error-translations.ts:8
msgid "Invalid email address"
msgstr "Μη έγκυρη διεύθυνση email"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1004
#: app/domain/participant/ParticipantFields.tsx:712
msgid "Last dive"
msgstr "Τελευταία κατάδυση"

#: app/domain/participant/ParticipantFields.tsx:421
msgid "Last name"
msgstr "Επώνυμο"

#: app/domain/participant/GenderSelect.tsx:8
msgid "Male"
msgstr "Άνδρας"

#: app/domain/participant/participant-data.tsx:32
msgid "Mask"
msgstr "Μάσκα"

#: app/domain/participant/ParticipantFields.tsx:1226
msgid "May we ask how you heard about us?"
msgstr "Μπορούμε να ρωτήσουμε πώς μας βρήκατε?"

#: app/domain/participant/ParticipantFields.tsx:1230
msgid "May we contact you to ask about your experience?"
msgstr "Μπορούμε να επικοινωνήσουμε μαζί σας για να ρωτήσουμε σχετικά με την εμπειρία σας?"

#: app/domain/participant/ParticipantFields.tsx:595
msgid "Meal preferences"
msgstr "Προτιμήσεις γευμάτων"

#: app/routes/_all._catch.waiver.$id.tsx:312
msgid "Medical Examiner's Evaluation Form"
msgstr "Έντυπο Αξιολόγησης Ιατρού"

#: app/routes/_all._catch.waiver.$id.tsx:372
msgid "Medical Examiner’s Name"
msgstr "Ονοματεπώνυμο Εξεταστή / Ιατρού"

#: app/domain/participant/participant-helpers.tsx:13
msgid "More than 1 year ago"
msgstr "Περισσότερο από 1 χρόνο πριν"

#: app/domain/participant/participant-helpers.tsx:14
msgid "more than 2 years ago"
msgstr "περισσότερο από 2 χρόνια πριν"

#: app/misc/error-translations.ts:67
msgid "Name already exists under this establishment"
msgstr "Το όνομα υπάρχει ήδη σε αυτή την εγκατάσταση"

#: app/domain/participant/participant.signature.component.tsx:97
msgid "Name of parent or guardian"
msgstr "Όνομα γονέα ή κηδεμόνα"

#: app/domain/participant/participant.signature.component.tsx:57
msgid "Name participant"
msgstr "Όνομα συμμετέχοντα"

#: app/routes/_all._catch.waiver.$id.tsx:231
#: app/routes/_all._catch.waiver.$id.tsx:282
#: app/domain/participant/ParticipantFields.tsx:1267
msgid "No"
msgstr "Όχι"

#: app/misc/error-translations.ts:6
msgid "No changes were made"
msgstr "Δεν έγιναν αλλαγές"

#: app/domain/participant/participant-fields.tsx:10
msgid "No preference"
msgstr "Καμία προτίμηση"

#: app/domain/participant/GenderSelect.tsx:10
msgid "Non-binary"
msgstr "Μη δυαδικό"

#: app/routes/_all._catch.waiver.$id.tsx:355
msgid "Not approved – I find conditions that I consider incompatible with recreational scuba diving or freediving"
msgstr "Δεν Εγκρίθηκε - Βρίσκω παθολογικές καταστάσεις που θεωρώ ασυμβίβαστες με δραστηριότητες αναψυχής που σχετίζονται με την αυτόνομη ή ελεύθερη κατάδυση."

#: app/domain/participant/ParticipantFields.tsx:704
msgid "Number of dives"
msgstr "Αριθμός καταδύσεων"

#: app/domain/participant/ParticipantFields.tsx:436
msgid "Or a valid local ID card"
msgstr "Ή μια έγκυρη τοπική ταυτότητα"

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "or an ID that's valid in the operator country"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:804
msgid "Or t-shirt size if you don't know your wetsuit size."
msgstr "Ή μέγεθος μπλουζας αν δεν γνωρίζετε το μέγεθος της στολής κατάδυσής σας."

#: app/domain/participant/ParticipantFields.tsx:134
msgid "Other info (optional)"
msgstr "Άλλες πληροφορίες (προαιρετικό)"

#: app/domain/participant/ParticipantFields.tsx:749
msgid "Own gear"
msgstr "Δικός μου εξοπλισμός"

#: app/misc/error-translations.ts:10
msgid "Page was not found or you are unauthorized"
msgstr "Η σελίδα δεν βρέθηκε ή δεν έχετε εξουσιοδότηση"

#: app/domain/participant/ParticipantFields.tsx:114
msgid "Participant details"
msgstr "Στοιχεία συμμετέχοντα"

#: app/routes/_all._catch.waiver.$id.tsx:318
msgid "Participant name"
msgstr "Όνομα συμμετέχοντα"

#: app/domain/participant/ParticipantFields.tsx:435
msgid "Passport"
msgstr "Διαβατήριο"

#: app/domain/participant/ParticipantFields.tsx:303
#~ msgid "Passport No."
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "Passport number"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:384
#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1012
msgid "Phone"
msgstr "Τηλέφωνο"

#: app/domain/participant/ParticipantFields.tsx:569
msgid "Phone number"
msgstr "Αριθμός τηλεφώνου"

#: app/routes/_all._catch.waiver.$id.tsx:396
msgid "Physician/Clinic Stamp (optional)"
msgstr "Υπογραφή Ιατρού/Κλινικής (Σφραγίδα προαιρετική)"

#: app/routes/_all._catch._w.planning.u.tsx:107
msgid "Planning"
msgstr "Σχεδιασμός"

#: app/routes/_all._catch.participant_.mutate.tsx:1406
msgid "Please check this box to continue."
msgstr "Παρακαλώ τσεκάρετε αυτό το κουτάκι για να συνεχίσετε."

#: app/domain/participant/ParticipantFields.tsx:1289
msgid "Please fill any additional important info or questions below"
msgstr "Παρακαλώ συμπληρώστε οποιεσδήποτε πρόσθετες σημαντικές πληροφορίες ή ερωτήσεις παρακάτω"

#: app/domain/participant/ParticipantFields.tsx:750
msgid "Please select in case you bring your own gear."
msgstr "Παρακαλώ επιλέξτε αν φέρνετε τον δικό σας εξοπλισμό."

#: app/routes/_all._catch.participant_.mutate.tsx:883
msgid "Please select the activity (or activities) you're attending."
msgstr "Παρακαλώ επιλέξτε τη δραστηριότητα (ή δραστηριότητες) που θα παρακολουθήσετε."

#: app/domain/participant/GenderSelect.tsx:11
msgid "Prefer not to say"
msgstr "Προτιμώ να μην απαντήσω"

#: app/routes/_all._catch.participant_.mutate.tsx:830
msgid "Preferred language"
msgstr "Προτιμώμενη γλώσσα"

#: app/routes/_all._catch.participant_.mutate.tsx:1250
msgid "Recipient email"
msgstr "Email παραλήπτη"

#: app/routes/_all._catch.participant_.mutate.tsx:1149
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Register"
msgstr "Εγγραφή"

#: app/domain/participant/participant-data.tsx:35
msgid "Regulator"
msgstr "Ρυθμιστής"

#: app/domain/participant/ParticipantFields.tsx:640
msgid "Relationship"
msgstr "Σχέση"

#: app/routes/_all._catch._index.tsx:156
msgid "Research & booking made easier"
msgstr "Έρευνα & κράτηση πιο εύκολη"

#: app/routes/_all._catch.participant_.mutate.tsx:1191
msgid "Returning participant? Retrieve your registration via OTP email verification."
msgstr "Επιστρέφων συμμετέχων; Ανάκτηση της εγγραφής σας μέσω επαλήθευσης email OTP."

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:992
#: app/domain/participant/ParticipantFields.tsx:510
msgid "Room number"
msgstr "Αριθμός δωματίου"

#: app/domain/participant/ParticipantFields.tsx:511
msgid "Room number of your stay"
msgstr "Αριθμός δωματίου της διαμονής σας"

#: app/domain/participant/ParticipantFields.tsx:425
#~ msgid "Room number of your stay at our resort"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:562
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Save"
msgstr "Αποθήκευση"

#: app/domain/participant/ParticipantFields.tsx:737
msgid "select"
msgstr "Επιλέξτε"

#: app/domain/participant/ParticipantFields.tsx:1338
msgid "Select"
msgstr "Επιλέξτε"

#: app/routes/_all._catch.participant_.mutate.tsx:1281
msgid "Select add-ons"
msgstr "Επιλέξτε πρόσθετα"

#: app/components/CountrySelectHeadlessUi.tsx:100
#: app/components/CountrySelect.tsx:171
msgid "select country"
msgstr "Επιλέξτε χώρα"

#: app/components/CountrySelectHeadlessUi.tsx:133
#: app/components/CountrySelect.tsx:202
msgid "Select country"
msgstr "Επιλέξτε χώρα"

#: app/components/CountrySelectHeadlessUi.tsx:28
#: app/components/CountrySelect.tsx:41
msgid "Select Country"
msgstr "Επιλέξτε Χώρα"

#: app/domain/participant/GenderSelect.tsx:67
msgid "Select gender"
msgstr "Επιλέξτε φύλο"

#: app/domain/participant/ParticipantFields.tsx:197
msgid "Select level"
msgstr "Επιλέξτε επίπεδο"

#: app/routes/_all._catch.participant_.mutate.tsx:646
#~ msgid "Select one or more activities"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:222
msgid "Select organisation"
msgstr "Επιλέξτε οργανισμό"

#: app/domain/participant/ParticipantFields.tsx:803
msgid "Select size"
msgstr "Επιλέξτε μέγεθος"

#: app/domain/participant/ParticipantFields.tsx:1012
msgid "Shoe size"
msgstr "Μέγεθος παπουτσιού"

#: app/routes/_all._catch.waiver.$id.tsx:364
msgid "Signature of certified medical doctor or other legally certified medical provider"
msgstr "Υπογραφή Πιστοποιημένου Ιατρού ή νομικά κατοχυρωμένου πάροχο υγείας"

#: app/domain/participant/participant.signature.component.tsx:131
#: app/domain/participant/participant.signature.component.tsx:137
msgid "Signature of parent or guardian"
msgstr "Υπογραφή γονέα ή κηδεμόνα"

#: app/domain/participant/participant.signature.component.tsx:78
#: app/domain/participant/participant.signature.component.tsx:84
msgid "Signature participant"
msgstr "Υπογραφή συμμετέχοντα"

#: app/domain/participant/ParticipantFields.tsx:488
msgid "Stay"
msgstr "Διαμονή"

#: app/domain/participant/ParticipantFields.tsx:386
#~ msgid "Staying with us? Please fill your room number."
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:330
msgid "The above-named person requests your opinion of his/her medical suitability to participate in recreational scuba diving or freediving training or activity. Please visit <0>uhms.org</0> for medical guidance on medical conditions as they relate to diving. Review the areas relevant to your patient as part of your evaluation."
msgstr "Το παραπάνω πρόσωπο ζητά τη γνώμη σας για την ιατρική του καταλληλόλητα να συμμετάσχει σε ψυχαγωγικές ή αθλητικές δραστηριότητες αυτόνομης ή ελεύθερης κατάδυσης. Παρακαλούμε επισκεφθείτε το <0>www.uhms.org</0> για ιατρική καθοδήγηση σχετικά με τις ιατρικές καταστάσεις που σχετίζονται με τις καταδύσεις. Εξετάστε τους τομείς που σχετίζονται με τον ασθενή σας στο πλαίσιο της αξιολόγησής σας."

#: app/domain/participant/participant.signature.component.tsx:23
msgid "Today's date"
msgstr "Σημερινή ημερομηνία"

#: app/domain/participant/participant-fields.tsx:8
msgid "Vegan"
msgstr "Vegan"

#: app/domain/participant/participant-fields.tsx:7
msgid "Vegetarian"
msgstr "Χορτοφάγος"

#: app/routes/_all._catch._w.waiver_.mutate.tsx:173
#~ msgid "View"
#~ msgstr "View"

#: app/misc/error-translations.ts:15
msgid "Wachtwoord moet tenminste 6 tekens lang zijn."
msgstr "Ο κωδικός πρόσβασης πρέπει να έχει τουλάχιστον 6 χαρακτήρες."

#: app/domain/participant/participant-data.tsx:47
msgid "Walk-in"
msgstr "Walk-in"

#: app/domain/participant/ParticipantFields.tsx:910
msgid "Weight"
msgstr "Βάρος"

#: app/domain/participant/ParticipantFields.tsx:1115
msgid "Weightbelt"
msgstr "Ζώνη βάρους"

#: app/domain/participant/participant-data.tsx:36
msgid "Wetsuit"
msgstr "Στολή κατάδυσης"

#: app/domain/participant/ParticipantFields.tsx:802
msgid "Wetsuit size"
msgstr "Μέγεθος στολής κατάδυσης"

#: app/domain/participant/ParticipantFields.tsx:570
msgid "WhatsApp preferred"
msgstr "Προτιμότερο WhatsApp"

#: app/domain/participant/participant-helpers.tsx:10
msgid "Within the last 3 months"
msgstr "Εντός των τελευταίων 3 μηνών"

#: app/domain/participant/participant-helpers.tsx:11
msgid "Within the last 6 months"
msgstr "Εντός των τελευταίων 6 μηνών"

#: app/domain/participant/participant-helpers.tsx:12
msgid "Within the last year"
msgstr "Εντός του τελευταίου έτους"

#: app/domain/participant/ParticipantFields.tsx:709
msgid "Years of experience"
msgstr "Χρόνια εμπειρίας"

#: app/routes/_all._catch.waiver.$id.tsx:214
#: app/routes/_all._catch.waiver.$id.tsx:265
#: app/domain/participant/ParticipantFields.tsx:1253
msgid "Yes"
msgstr "Ναι"

#: app/domain/participant/ParticipantFields.tsx:1285
msgid "Your Instagram Handle"
msgstr "Το όνομα χρήστη σας στο Instagram"

#: app/domain/participant/participant-data.tsx:50
msgid "YouTube"
msgstr "YouTube"

