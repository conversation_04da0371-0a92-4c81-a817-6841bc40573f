msgid ""
msgstr ""
"POT-Creation-Date: 2024-03-30 07:31+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: it\n"
"Project-Id-Version: traveltruster\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-28 13:35\n"
"Last-Translator: \n"
"Language-Team: Italian\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: traveltruster\n"
"X-Crowdin-Project-ID: 668342\n"
"X-Crowdin-Language: it\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 2\n"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:603
#~ msgid "{0, plural, one {upload} other {uploads}}"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
#~ msgid "&sl;1y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:8
#~ msgid "&sl;3mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:9
#~ msgid "&sl;6mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
msgid "<1y"
msgstr "<1a"

#: app/domain/participant/participant-helpers.tsx:12
#~ msgid "<2y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
msgid "<3mo"
msgstr "<3m"

#: app/domain/participant/participant-helpers.tsx:11
msgid "<6mo"
msgstr "<6m"

#: app/domain/participant/participant-helpers.tsx:13
msgid ">1y"
msgstr ">1a"

#: app/domain/participant/participant-helpers.tsx:14
msgid ">2y"
msgstr ">2a"

#: app/routes/_all._catch.waiver.$id.tsx:381
msgid "Address"
msgstr "Indirizzo"

#: app/domain/participant/participant-data.tsx:52
msgid "Agency"
msgstr "Agenzia"

#: app/routes/_all._catch.waiver.$id.tsx:349
msgid "Approved – I find no conditions that I consider incompatible with recreational scuba diving or freediving."
msgstr "Approvato - Non ravviso condizioni che reputo incompatibili con la subacquea ricreativa con autorespiratore o in apnea."

#: app/misc/error-translations.ts:7
msgid "Authentication code was expired"
msgstr "Il codice di autenticazione è scaduto"

#: app/domain/participant/participant-data.tsx:34
msgid "BCD"
msgstr "Gav"

#: app/domain/participant/ParticipantFields.tsx:1221
msgid "BCD Size"
msgstr "Taglia Gav"

#: app/routes/_all._catch.waiver.$id.tsx:324
msgid "Birthdate"
msgstr "Data di nascita"

#: app/routes/_all._catch.participant_.mutate.tsx:843
msgid "Booking"
msgstr "Prenotazione"

#: app/domain/participant/participant-fields.tsx:11
msgid "Bring my own"
msgstr "Porto la mia"

#: app/domain/participant/ParticipantFields.tsx:694
#~ msgid "Certificate"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:690
msgid "Certificate details"
msgstr "Dettagli certificato"

#: app/domain/participant/ParticipantFields.tsx:667
msgid "Certificate level"
msgstr "Livello di certificazione"

#: app/domain/participant/ParticipantFields.tsx:686
#~ msgid "Certificate number"
#~ msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:996
msgid "Certification"
msgstr "Certificazione"

#: app/domain/participant/ParticipantFields.tsx:644
msgid "Certification organisation"
msgstr "Organizzazione di certificazione"

#: app/routes/_all._catch.participant_.mutate.tsx:1416
msgid "Click to read"
msgstr "Clicca per leggere"

#: app/routes/_all._catch.waiver.$id.tsx:378
msgid "Clinic/Hospital"
msgstr "Clinica/Ospedale"

#: app/routes/_all._catch.waiver.$id.tsx:375
msgid "Clinical Degrees/Credentials"
msgstr "Attestati clinici/credenziali"

#: app/domain/participant/ParticipantFields.tsx:607
msgid "Company and policy number"
msgstr "Compagnia e numero di polizza"

#: app/routes/_all._catch.participant_.mutate.tsx:1217
#~ msgid "Continue"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Copy"
msgstr "Copia"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1008
#: app/domain/participant/ParticipantFields.tsx:443
msgid "Country"
msgstr "Paese"

#: app/routes/_all._catch.waiver.$id.tsx:403
msgid "Created by the <0>Diver Medical Screen Committee</0> in association with the following bodies:"
msgstr "Creato dal <0>Diver Medical Screen Committee</0> in associazione con i seguenti enti:"

#: app/routes/_all._catch.waiver.$id.tsx:369
msgid "Date (dd/mm/yyyy)"
msgstr "Data (gg/mm/aaaa)"

#: app/domain/participant/ParticipantFields.tsx:516
msgid "Date of birth"
msgstr "Data di nascita"

#: app/domain/participant/ParticipantFields.tsx:126
msgid "Dive specific details"
msgstr "Dettagli specifici dell'immersione"

#: app/routes/_all._catch.waiver.$id.tsx:308
msgid "Diver Medical"
msgstr "Certificato medico subacqueo"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1000
msgid "Dives"
msgstr "Immersioni"

#: app/domain/participant/ParticipantFields.tsx:606
msgid "Do you have an insurance that covers underwater activities? please fill your details below"
msgstr "Hai un'assicurazione che copre le attività subacquee? Inserisci i tuoi dati qui sotto"

#: app/domain/participant/ParticipantFields.tsx:641
msgid "e.g. friend, father or mother"
msgstr "es. amico, padre o madre"

#: app/domain/participant/ParticipantFields.tsx:497
msgid "e.g. Hotel name"
msgstr "es. Nome dell'hotel"

#: app/domain/participant/ParticipantFields.tsx:601
msgid "e.g. nut allergy"
msgstr "es. allergia alle noci"

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Edit"
msgstr "Modifica"

#: app/routes/_all._catch.waiver.$id.tsx:387
msgid "Email"
msgstr "Email"

#: app/routes/_all._catch.participant_.mutate.tsx:1201
msgid "Email OTP"
msgstr "OTP Email"

#: app/domain/participant/ParticipantFields.tsx:118
msgid "Emergency contact details"
msgstr "Dettagli contatto di emergenza"

#: app/domain/participant/ParticipantFields.tsx:611
msgid "Emergency contact name"
msgstr "Nome contatto di emergenza"

#: app/domain/participant/ParticipantFields.tsx:615
msgid "Emergency contact phone number"
msgstr "Numero di telefono contatto di emergenza"

#: app/components/base/AddressInput.tsx:70
msgid "Enter a location"
msgstr "Inserisci una località"

#: app/domain/participant/ParticipantFields.tsx:396
#~ msgid "Enter here"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:390
#~ msgid "Enter here or upload below"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:347
msgid "Enter number"
msgstr "Inserisci numero"

#: app/routes/_all._catch._index.tsx:154
msgid "Explore Bali underwater"
msgstr "Esplora Bali sott'acqua"

#: app/domain/participant/participant-data.tsx:46
msgid "Facebook"
msgstr "Facebook"

#: app/domain/participant/ParticipantFields.tsx:130
msgid "Feedback"
msgstr "Feedback"

#: app/domain/participant/GenderSelect.tsx:9
msgid "Female"
msgstr "Femmina"

#: app/domain/participant/participant-data.tsx:33
msgid "Fins"
msgstr "Pinne"

#: app/domain/participant/ParticipantFields.tsx:407
msgid "First name"
msgstr "Nome"

#: app/domain/participant/ParticipantFields.tsx:600
msgid "Food allergies"
msgstr "Allergie alimentari"

#: app/domain/participant/participant-data.tsx:44
msgid "Friend"
msgstr "Amico"

#: app/domain/participant/ParticipantFields.tsx:543
msgid "Gender"
msgstr "Sesso"

#: app/domain/participant/participant-fields.tsx:9
msgid "Gluten free"
msgstr "Senza glutine"

#: app/domain/participant/participant-data.tsx:48
msgid "Google"
msgstr "Google"

#: app/domain/participant/participant-data.tsx:49
msgid "Google Maps"
msgstr "Google Map"

#: app/domain/participant/ParticipantFields.tsx:808
msgid "Height"
msgstr "Altezza"

#: app/misc/error-translations.ts:14
msgid "Het email adres is al in gebruik door een ander account."
msgstr "L'indirizzo email è già in uso da un altro account."

#: app/domain/participant/ParticipantFields.tsx:463
msgid "Home/residential address"
msgstr "Indirizzo di casa/residenza"

#: app/domain/participant/participant-data.tsx:51
msgid "Hotel"
msgstr "Hotel"

#: app/domain/participant/ParticipantFields.tsx:385
#~ msgid "Hotel/hostel/homestay"
#~ msgstr ""

#. placeholder {0}: waiver.translation?.name
#: app/routes/_all._catch.participant_.mutate.tsx:1458
msgid "I have read and agree with the above <0>{0}</0>"
msgstr "Ho letto e accetto quanto sopra <0>{0}</0>"

#: app/routes/_all._catch.participant_.mutate.tsx:800
#~ msgid "I have read and agree with the above <0>{0}</0> and agree"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1290
msgid "If you require any additional addons, select the amount below"
msgstr "Se desideri componenti aggiuntivi, seleziona la quantità di seguito"

#: app/domain/participant/ParticipantFields.tsx:121
msgid "Important! Ensure the emergency contact is not someone joining the activity."
msgstr "Importante! Assicurati che il contatto di emergenza non sia qualcuno che partecipa all'attività."

#: app/domain/participant/participant-data.tsx:45
msgid "Instagram"
msgstr "Instagram"

#: app/domain/participant/ParticipantFields.tsx:605
msgid "Insurance"
msgstr "Assicurazione"

#: app/misc/error-translations.ts:9
msgid "Invalid credentials"
msgstr "Credenziali non valide"

#: app/misc/error-translations.ts:8
msgid "Invalid email address"
msgstr "Indirizzo email non valido"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1004
#: app/domain/participant/ParticipantFields.tsx:712
msgid "Last dive"
msgstr "Ultima immersione"

#: app/domain/participant/ParticipantFields.tsx:421
msgid "Last name"
msgstr "Cognome"

#: app/domain/participant/GenderSelect.tsx:8
msgid "Male"
msgstr "Maschio"

#: app/domain/participant/participant-data.tsx:32
msgid "Mask"
msgstr "Maschera"

#: app/domain/participant/ParticipantFields.tsx:1226
msgid "May we ask how you heard about us?"
msgstr "Possiamo chiedere come hai saputo di noi?"

#: app/domain/participant/ParticipantFields.tsx:1230
msgid "May we contact you to ask about your experience?"
msgstr "Possiamo contattarti per chiederti della tua esperienza?"

#: app/domain/participant/ParticipantFields.tsx:595
msgid "Meal preferences"
msgstr "Preferenze pasto"

#: app/routes/_all._catch.waiver.$id.tsx:312
msgid "Medical Examiner's Evaluation Form"
msgstr "Modulo di Valutazione del Medico Esaminatore"

#: app/routes/_all._catch.waiver.$id.tsx:372
msgid "Medical Examiner’s Name"
msgstr "Nome del Medico Esaminatore"

#: app/domain/participant/participant-helpers.tsx:13
msgid "More than 1 year ago"
msgstr "Più di 1 anno fa"

#: app/domain/participant/participant-helpers.tsx:14
msgid "more than 2 years ago"
msgstr "più di 2 anni fa"

#: app/misc/error-translations.ts:67
msgid "Name already exists under this establishment"
msgstr "Nome già esistente in questa struttura"

#: app/domain/participant/participant.signature.component.tsx:97
msgid "Name of parent or guardian"
msgstr "Nome del genitore o tutore"

#: app/domain/participant/participant.signature.component.tsx:57
msgid "Name participant"
msgstr "Nome partecipante"

#: app/routes/_all._catch.waiver.$id.tsx:231
#: app/routes/_all._catch.waiver.$id.tsx:282
#: app/domain/participant/ParticipantFields.tsx:1267
msgid "No"
msgstr "No"

#: app/misc/error-translations.ts:6
msgid "No changes were made"
msgstr "Nessuna modifica è stata apportata"

#: app/domain/participant/participant-fields.tsx:10
msgid "No preference"
msgstr "Nessuna preferenza"

#: app/domain/participant/GenderSelect.tsx:10
msgid "Non-binary"
msgstr "Non binario"

#: app/routes/_all._catch.waiver.$id.tsx:355
msgid "Not approved – I find conditions that I consider incompatible with recreational scuba diving or freediving"
msgstr "Non approvato - Ravviso condizioni che reputo incompatibili con la subacquea ricreativa con autorespiratore o in apnea."

#: app/domain/participant/ParticipantFields.tsx:704
msgid "Number of dives"
msgstr "Numero di immersioni"

#: app/domain/participant/ParticipantFields.tsx:436
msgid "Or a valid local ID card"
msgstr "O una carta d'identità locale valida"

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "or an ID that's valid in the operator country"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:804
msgid "Or t-shirt size if you don't know your wetsuit size."
msgstr "O la taglia della maglietta se non conosci la taglia della muta."

#: app/domain/participant/ParticipantFields.tsx:134
msgid "Other info (optional)"
msgstr "Altre informazioni (opzionale)"

#: app/domain/participant/ParticipantFields.tsx:749
msgid "Own gear"
msgstr "Attrezzatura propria"

#: app/misc/error-translations.ts:10
msgid "Page was not found or you are unauthorized"
msgstr "Pagina non trovata o non autorizzato"

#: app/domain/participant/ParticipantFields.tsx:114
msgid "Participant details"
msgstr "Dettagli partecipante"

#: app/routes/_all._catch.waiver.$id.tsx:318
msgid "Participant name"
msgstr "Nome partecipante"

#: app/domain/participant/ParticipantFields.tsx:435
msgid "Passport"
msgstr "Passaporto"

#: app/domain/participant/ParticipantFields.tsx:303
#~ msgid "Passport No."
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "Passport number"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:384
#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1012
msgid "Phone"
msgstr "Telefono"

#: app/domain/participant/ParticipantFields.tsx:569
msgid "Phone number"
msgstr "Numero di telefono"

#: app/routes/_all._catch.waiver.$id.tsx:396
msgid "Physician/Clinic Stamp (optional)"
msgstr "Timbro del medico / della clinica (facoltativo)"

#: app/routes/_all._catch._w.planning.u.tsx:107
msgid "Planning"
msgstr "Pianificazione"

#: app/routes/_all._catch.participant_.mutate.tsx:1406
msgid "Please check this box to continue."
msgstr "Si prega di spuntare questa casella per continuare."

#: app/domain/participant/ParticipantFields.tsx:1289
msgid "Please fill any additional important info or questions below"
msgstr "Si prega di compilare qualsiasi informazione aggiuntiva importante o domande qui sotto"

#: app/domain/participant/ParticipantFields.tsx:750
msgid "Please select in case you bring your own gear."
msgstr "Si prega di selezionare nel caso in cui porti la tua attrezzatura."

#: app/routes/_all._catch.participant_.mutate.tsx:883
msgid "Please select the activity (or activities) you're attending."
msgstr "Si prega di selezionare l'attività (o le attività) a cui si sta partecipando."

#: app/domain/participant/GenderSelect.tsx:11
msgid "Prefer not to say"
msgstr "Preferisco non dire"

#: app/routes/_all._catch.participant_.mutate.tsx:830
msgid "Preferred language"
msgstr "Lingua preferita"

#: app/routes/_all._catch.participant_.mutate.tsx:1250
msgid "Recipient email"
msgstr "Email del destinatario"

#: app/routes/_all._catch.participant_.mutate.tsx:1149
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Register"
msgstr "Registrati"

#: app/domain/participant/participant-data.tsx:35
msgid "Regulator"
msgstr "Erogatore"

#: app/domain/participant/ParticipantFields.tsx:640
msgid "Relationship"
msgstr "Relazione"

#: app/routes/_all._catch._index.tsx:156
msgid "Research & booking made easier"
msgstr "Ricerca e prenotazione semplificate"

#: app/routes/_all._catch.participant_.mutate.tsx:1191
msgid "Returning participant? Retrieve your registration via OTP email verification."
msgstr "Partecipante di ritorno? Recupera la tua registrazione tramite verifica OTP via email."

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:992
#: app/domain/participant/ParticipantFields.tsx:510
msgid "Room number"
msgstr "Numero di stanza"

#: app/domain/participant/ParticipantFields.tsx:511
msgid "Room number of your stay"
msgstr "Numero di stanza del tuo soggiorno"

#: app/domain/participant/ParticipantFields.tsx:425
#~ msgid "Room number of your stay at our resort"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:562
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Save"
msgstr "Salva"

#: app/domain/participant/ParticipantFields.tsx:737
msgid "select"
msgstr "seleziona"

#: app/domain/participant/ParticipantFields.tsx:1338
msgid "Select"
msgstr "Seleziona"

#: app/routes/_all._catch.participant_.mutate.tsx:1281
msgid "Select add-ons"
msgstr "Seleziona extra"

#: app/components/CountrySelectHeadlessUi.tsx:100
#: app/components/CountrySelect.tsx:171
msgid "select country"
msgstr "seleziona paese"

#: app/components/CountrySelectHeadlessUi.tsx:133
#: app/components/CountrySelect.tsx:202
msgid "Select country"
msgstr "Seleziona paese"

#: app/components/CountrySelectHeadlessUi.tsx:28
#: app/components/CountrySelect.tsx:41
msgid "Select Country"
msgstr "Seleziona Paese"

#: app/domain/participant/GenderSelect.tsx:67
msgid "Select gender"
msgstr "Seleziona genere"

#: app/domain/participant/ParticipantFields.tsx:197
msgid "Select level"
msgstr "Seleziona livello"

#: app/routes/_all._catch.participant_.mutate.tsx:646
#~ msgid "Select one or more activities"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:222
msgid "Select organisation"
msgstr "Seleziona organizzazione"

#: app/domain/participant/ParticipantFields.tsx:803
msgid "Select size"
msgstr "Seleziona taglia"

#: app/domain/participant/ParticipantFields.tsx:1012
msgid "Shoe size"
msgstr "Numero di scarpe"

#: app/routes/_all._catch.waiver.$id.tsx:364
msgid "Signature of certified medical doctor or other legally certified medical provider"
msgstr "Firma di medico certificato o altro fornitore medico legalmente certificato"

#: app/domain/participant/participant.signature.component.tsx:131
#: app/domain/participant/participant.signature.component.tsx:137
msgid "Signature of parent or guardian"
msgstr "Firma del genitore o tutore"

#: app/domain/participant/participant.signature.component.tsx:78
#: app/domain/participant/participant.signature.component.tsx:84
msgid "Signature participant"
msgstr "Firma partecipante"

#: app/domain/participant/ParticipantFields.tsx:488
msgid "Stay"
msgstr "Soggiorno"

#: app/domain/participant/ParticipantFields.tsx:386
#~ msgid "Staying with us? Please fill your room number."
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:330
msgid "The above-named person requests your opinion of his/her medical suitability to participate in recreational scuba diving or freediving training or activity. Please visit <0>uhms.org</0> for medical guidance on medical conditions as they relate to diving. Review the areas relevant to your patient as part of your evaluation."
msgstr "La persona sopramenzionata richiede la Sua opinione circa la propria idoneità a partecipare a un addestramento o attività di immersione ricreativa con autorespiratore o in apnea. Per favore, visiti il sito <0>uhms.org</0> per un orientamento medico sulla relazione tra condizioni mediche e subacquea. Controlli le aree rilevanti per il suo paziente come parte della Sua valutazione."

#: app/domain/participant/participant.signature.component.tsx:23
msgid "Today's date"
msgstr "Data odierna"

#: app/domain/participant/participant-fields.tsx:8
msgid "Vegan"
msgstr "Vegano"

#: app/domain/participant/participant-fields.tsx:7
msgid "Vegetarian"
msgstr "Vegetariano"

#: app/routes/_all._catch._w.waiver_.mutate.tsx:173
#~ msgid "View"
#~ msgstr "View"

#: app/misc/error-translations.ts:15
msgid "Wachtwoord moet tenminste 6 tekens lang zijn."
msgstr "La password deve essere lunga almeno 6 caratteri."

#: app/domain/participant/participant-data.tsx:47
msgid "Walk-in"
msgstr "Accesso diretto"

#: app/domain/participant/ParticipantFields.tsx:910
msgid "Weight"
msgstr "Peso"

#: app/domain/participant/ParticipantFields.tsx:1115
msgid "Weightbelt"
msgstr "Cintura dei pesi"

#: app/domain/participant/participant-data.tsx:36
msgid "Wetsuit"
msgstr "Muta"

#: app/domain/participant/ParticipantFields.tsx:802
msgid "Wetsuit size"
msgstr "Taglia muta"

#: app/domain/participant/ParticipantFields.tsx:570
msgid "WhatsApp preferred"
msgstr "WhatsApp preferito"

#: app/domain/participant/participant-helpers.tsx:10
msgid "Within the last 3 months"
msgstr "Negli ultimi 3 mesi"

#: app/domain/participant/participant-helpers.tsx:11
msgid "Within the last 6 months"
msgstr "Negli ultimi 6 mesi"

#: app/domain/participant/participant-helpers.tsx:12
msgid "Within the last year"
msgstr "Nell'ultimo anno"

#: app/domain/participant/ParticipantFields.tsx:709
msgid "Years of experience"
msgstr "Anni di esperienza"

#: app/routes/_all._catch.waiver.$id.tsx:214
#: app/routes/_all._catch.waiver.$id.tsx:265
#: app/domain/participant/ParticipantFields.tsx:1253
msgid "Yes"
msgstr "Sì"

#: app/domain/participant/ParticipantFields.tsx:1285
msgid "Your Instagram Handle"
msgstr "Il tuo nome utente Instagram"

#: app/domain/participant/participant-data.tsx:50
msgid "YouTube"
msgstr "YouTube"

