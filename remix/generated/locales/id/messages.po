msgid ""
msgstr ""
"POT-Creation-Date: 2024-03-30 07:31+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: id\n"
"Project-Id-Version: traveltruster\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-28 13:35\n"
"Last-Translator: \n"
"Language-Team: Indonesian\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: traveltruster\n"
"X-Crowdin-Project-ID: 668342\n"
"X-Crowdin-Language: id\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 2\n"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:603
#~ msgid "{0, plural, one {upload} other {uploads}}"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
#~ msgid "&sl;1y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:8
#~ msgid "&sl;3mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:9
#~ msgid "&sl;6mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
msgid "<1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
#~ msgid "<2y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
msgid "<3mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:11
msgid "<6mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:13
msgid ">1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:14
msgid ">2y"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:381
msgid "Address"
msgstr "Alamat"

#: app/domain/participant/participant-data.tsx:52
msgid "Agency"
msgstr "Agensi"

#: app/routes/_all._catch.waiver.$id.tsx:349
msgid "Approved – I find no conditions that I consider incompatible with recreational scuba diving or freediving."
msgstr "Disetujui – Saya tidak menemukan kondisi yang saya anggap tidak sesuai dengan kegiatan rekreasi scuba diving atau freediving."

#: app/misc/error-translations.ts:7
msgid "Authentication code was expired"
msgstr "Kode otentikasi sudah kedaluwarsa"

#: app/domain/participant/participant-data.tsx:34
msgid "BCD"
msgstr "Alat Pengendali Ketimbulan (BCD)"

#: app/domain/participant/ParticipantFields.tsx:1221
msgid "BCD Size"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:324
msgid "Birthdate"
msgstr "Tanggal Lahir"

#: app/routes/_all._catch.participant_.mutate.tsx:843
msgid "Booking"
msgstr "Pemesanan"

#: app/domain/participant/participant-fields.tsx:11
msgid "Bring my own"
msgstr "Bawa sendiri"

#: app/domain/participant/ParticipantFields.tsx:694
#~ msgid "Certificate"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:690
msgid "Certificate details"
msgstr "Detail Sertifikat"

#: app/domain/participant/ParticipantFields.tsx:667
msgid "Certificate level"
msgstr "Tingkat sertifikasi"

#: app/domain/participant/ParticipantFields.tsx:686
#~ msgid "Certificate number"
#~ msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:996
msgid "Certification"
msgstr "Sertifikat"

#: app/domain/participant/ParticipantFields.tsx:644
msgid "Certification organisation"
msgstr "Organisasi sertifikasi"

#: app/routes/_all._catch.participant_.mutate.tsx:1416
msgid "Click to read"
msgstr "Klik untuk membaca"

#: app/routes/_all._catch.waiver.$id.tsx:378
msgid "Clinic/Hospital"
msgstr "Klinik/Rumah Sakit"

#: app/routes/_all._catch.waiver.$id.tsx:375
msgid "Clinical Degrees/Credentials"
msgstr "Spesialis"

#: app/domain/participant/ParticipantFields.tsx:607
msgid "Company and policy number"
msgstr "Perusahaan dan nomor polis"

#: app/routes/_all._catch.participant_.mutate.tsx:1217
#~ msgid "Continue"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Copy"
msgstr "Salin"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1008
#: app/domain/participant/ParticipantFields.tsx:443
msgid "Country"
msgstr "Negara"

#: app/routes/_all._catch.waiver.$id.tsx:403
msgid "Created by the <0>Diver Medical Screen Committee</0> in association with the following bodies:"
msgstr "Dibuat oleh <0>Komite Skrining Medis Penyelam</0> bekerja sama dengan badan-badan berikut ini:"

#: app/routes/_all._catch.waiver.$id.tsx:369
msgid "Date (dd/mm/yyyy)"
msgstr "Tanggal (Tanggal/Bulan/Tahun)"

#: app/domain/participant/ParticipantFields.tsx:516
msgid "Date of birth"
msgstr "Tanggal lahir"

#: app/domain/participant/ParticipantFields.tsx:126
msgid "Dive specific details"
msgstr "Detail spesifikasi penyelaman"

#: app/routes/_all._catch.waiver.$id.tsx:308
msgid "Diver Medical"
msgstr "Kondisi Medis Penyelam"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1000
msgid "Dives"
msgstr "Penyelaman"

#: app/domain/participant/ParticipantFields.tsx:606
msgid "Do you have an insurance that covers underwater activities? please fill your details below"
msgstr "Apakah Anda memiliki asuransi yang mencakup aktivitas di bawah air? Silakan isi detail Anda di bawah ini"

#: app/domain/participant/ParticipantFields.tsx:641
msgid "e.g. friend, father or mother"
msgstr "Misalnya teman, ayah, ibu"

#: app/domain/participant/ParticipantFields.tsx:497
msgid "e.g. Hotel name"
msgstr "mis. Nama hotel"

#: app/domain/participant/ParticipantFields.tsx:601
msgid "e.g. nut allergy"
msgstr "Contoh: Alergi kacang"

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Edit"
msgstr "Sunting"

#: app/routes/_all._catch.waiver.$id.tsx:387
msgid "Email"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1201
msgid "Email OTP"
msgstr "Email OTP"

#: app/domain/participant/ParticipantFields.tsx:118
msgid "Emergency contact details"
msgstr "Detail kontak darurat"

#: app/domain/participant/ParticipantFields.tsx:611
msgid "Emergency contact name"
msgstr "Nama kontak darurat"

#: app/domain/participant/ParticipantFields.tsx:615
msgid "Emergency contact phone number"
msgstr "Nomor telepon kontak darurat"

#: app/components/base/AddressInput.tsx:70
msgid "Enter a location"
msgstr "Masukkan lokasi"

#: app/domain/participant/ParticipantFields.tsx:396
#~ msgid "Enter here"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:390
#~ msgid "Enter here or upload below"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:347
msgid "Enter number"
msgstr "Masukkan nomor"

#: app/routes/_all._catch._index.tsx:154
msgid "Explore Bali underwater"
msgstr "Jelajahi Bali di bawah air"

#: app/domain/participant/participant-data.tsx:46
msgid "Facebook"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:130
msgid "Feedback"
msgstr "Umpan balik"

#: app/domain/participant/GenderSelect.tsx:9
msgid "Female"
msgstr ""

#: app/domain/participant/participant-data.tsx:33
msgid "Fins"
msgstr "Kaki katak"

#: app/domain/participant/ParticipantFields.tsx:407
msgid "First name"
msgstr "Nama depan"

#: app/domain/participant/ParticipantFields.tsx:600
msgid "Food allergies"
msgstr "Alergi makanan"

#: app/domain/participant/participant-data.tsx:44
msgid "Friend"
msgstr "Teman"

#: app/domain/participant/ParticipantFields.tsx:543
msgid "Gender"
msgstr ""

#: app/domain/participant/participant-fields.tsx:9
msgid "Gluten free"
msgstr "Bebas gluten"

#: app/domain/participant/participant-data.tsx:48
msgid "Google"
msgstr ""

#: app/domain/participant/participant-data.tsx:49
msgid "Google Maps"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:808
msgid "Height"
msgstr "Tinggi"

#: app/misc/error-translations.ts:14
msgid "Het email adres is al in gebruik door een ander account."
msgstr "Alamat email sudah digunakan oleh akun lain."

#: app/domain/participant/ParticipantFields.tsx:463
msgid "Home/residential address"
msgstr "Alamat rumah/tempat tinggal"

#: app/domain/participant/participant-data.tsx:51
msgid "Hotel"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:385
#~ msgid "Hotel/hostel/homestay"
#~ msgstr ""

#. placeholder {0}: waiver.translation?.name
#: app/routes/_all._catch.participant_.mutate.tsx:1458
msgid "I have read and agree with the above <0>{0}</0>"
msgstr "Saya telah membaca dan menyetujui yang tersebut di atas <0>{0}</0>"

#: app/routes/_all._catch.participant_.mutate.tsx:800
#~ msgid "I have read and agree with the above <0>{0}</0> and agree"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1290
msgid "If you require any additional addons, select the amount below"
msgstr "Jika Anda memerlukan addon tambahan, pilih jumlah di bawah"

#: app/domain/participant/ParticipantFields.tsx:121
msgid "Important! Ensure the emergency contact is not someone joining the activity."
msgstr "Penting! Pastikan kontak darurat bukan seseorang yang ikut dalam aktivitas."

#: app/domain/participant/participant-data.tsx:45
msgid "Instagram"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:605
msgid "Insurance"
msgstr "Asuransi"

#: app/misc/error-translations.ts:9
msgid "Invalid credentials"
msgstr "Kredensial tidak valid"

#: app/misc/error-translations.ts:8
msgid "Invalid email address"
msgstr "Alamat email tidak valid"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1004
#: app/domain/participant/ParticipantFields.tsx:712
msgid "Last dive"
msgstr "Penyelaman terakhir"

#: app/domain/participant/ParticipantFields.tsx:421
msgid "Last name"
msgstr "Nama belakang"

#: app/domain/participant/GenderSelect.tsx:8
msgid "Male"
msgstr ""

#: app/domain/participant/participant-data.tsx:32
msgid "Mask"
msgstr "Topeng selam"

#: app/domain/participant/ParticipantFields.tsx:1226
msgid "May we ask how you heard about us?"
msgstr "Dapatkah Anda memberitahu kami, dari mana Anda mendengar tentang kami?"

#: app/domain/participant/ParticipantFields.tsx:1230
msgid "May we contact you to ask about your experience?"
msgstr "Dapatkah kami menghubungi Anda untuk mendiskusikan pengalaman Anda?"

#: app/domain/participant/ParticipantFields.tsx:595
msgid "Meal preferences"
msgstr "Preferensi makanan"

#: app/routes/_all._catch.waiver.$id.tsx:312
msgid "Medical Examiner's Evaluation Form"
msgstr "Formulir Pemeriksaan Dokter"

#: app/routes/_all._catch.waiver.$id.tsx:372
msgid "Medical Examiner’s Name"
msgstr "Nama Dokter"

#: app/domain/participant/participant-helpers.tsx:13
msgid "More than 1 year ago"
msgstr "Lebih dari 1 tahun yang lalu"

#: app/domain/participant/participant-helpers.tsx:14
msgid "more than 2 years ago"
msgstr "lebih dari 2 tahun yang lalu"

#: app/misc/error-translations.ts:67
msgid "Name already exists under this establishment"
msgstr "Nama sudah ada di bawah lembaga ini"

#: app/domain/participant/participant.signature.component.tsx:97
msgid "Name of parent or guardian"
msgstr "Nama orang tua atau wali"

#: app/domain/participant/participant.signature.component.tsx:57
msgid "Name participant"
msgstr "Nama peserta"

#: app/routes/_all._catch.waiver.$id.tsx:231
#: app/routes/_all._catch.waiver.$id.tsx:282
#: app/domain/participant/ParticipantFields.tsx:1267
msgid "No"
msgstr "Tidak"

#: app/misc/error-translations.ts:6
msgid "No changes were made"
msgstr "Tidak ada perubahan yang dilakukan"

#: app/domain/participant/participant-fields.tsx:10
msgid "No preference"
msgstr "Tidak ada preferensi"

#: app/domain/participant/GenderSelect.tsx:10
msgid "Non-binary"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:355
msgid "Not approved – I find conditions that I consider incompatible with recreational scuba diving or freediving"
msgstr "Tidak Disetujui – Saya menemukan kondisi yang saya anggap tidak sesuai dengan kegiatan rekreasi scuba diving atau freediving."

#: app/domain/participant/ParticipantFields.tsx:704
msgid "Number of dives"
msgstr "Jumlah penyelaman"

#: app/domain/participant/ParticipantFields.tsx:436
msgid "Or a valid local ID card"
msgstr "Atau kartu identitas lokal yang sah"

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "or an ID that's valid in the operator country"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:804
msgid "Or t-shirt size if you don't know your wetsuit size."
msgstr "Atau ukuran kaos jika Anda tidak tahu ukuran pakaian selam basah Anda."

#: app/domain/participant/ParticipantFields.tsx:134
msgid "Other info (optional)"
msgstr "Info lainnya (opsional)"

#: app/domain/participant/ParticipantFields.tsx:749
msgid "Own gear"
msgstr "Perlengkapan pribadi"

#: app/misc/error-translations.ts:10
msgid "Page was not found or you are unauthorized"
msgstr "Halaman tidak ditemukan atau Anda tidak memiliki otorisasi"

#: app/domain/participant/ParticipantFields.tsx:114
msgid "Participant details"
msgstr "Detail peserta"

#: app/routes/_all._catch.waiver.$id.tsx:318
msgid "Participant name"
msgstr "Nama Peserta"

#: app/domain/participant/ParticipantFields.tsx:435
msgid "Passport"
msgstr "Paspor"

#: app/domain/participant/ParticipantFields.tsx:303
#~ msgid "Passport No."
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "Passport number"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:384
#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1012
msgid "Phone"
msgstr "Telepon"

#: app/domain/participant/ParticipantFields.tsx:569
msgid "Phone number"
msgstr "Nomor telepon"

#: app/routes/_all._catch.waiver.$id.tsx:396
msgid "Physician/Clinic Stamp (optional)"
msgstr "Stempel Dokter/Klinik (jika ada)"

#: app/routes/_all._catch._w.planning.u.tsx:107
msgid "Planning"
msgstr "Perencanaan"

#: app/routes/_all._catch.participant_.mutate.tsx:1406
msgid "Please check this box to continue."
msgstr "Silakan centang kotak ini untuk melanjutkan."

#: app/domain/participant/ParticipantFields.tsx:1289
msgid "Please fill any additional important info or questions below"
msgstr "Silakan isi informasi penting tambahan atau pertanyaan di bawah ini"

#: app/domain/participant/ParticipantFields.tsx:750
msgid "Please select in case you bring your own gear."
msgstr "Silakan pilih jika Anda membawa peralatan sendiri"

#: app/routes/_all._catch.participant_.mutate.tsx:883
msgid "Please select the activity (or activities) you're attending."
msgstr "Silakan pilih aktivitas (atau beberapa aktivitas) yang Anda ikuti."

#: app/domain/participant/GenderSelect.tsx:11
msgid "Prefer not to say"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:830
msgid "Preferred language"
msgstr "Bahasa yang disukai"

#: app/routes/_all._catch.participant_.mutate.tsx:1250
msgid "Recipient email"
msgstr "E-mail penerima"

#: app/routes/_all._catch.participant_.mutate.tsx:1149
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Register"
msgstr "Mendaftar"

#: app/domain/participant/participant-data.tsx:35
msgid "Regulator"
msgstr "Regulator"

#: app/domain/participant/ParticipantFields.tsx:640
msgid "Relationship"
msgstr "Hubungan"

#: app/routes/_all._catch._index.tsx:156
msgid "Research & booking made easier"
msgstr "Penelitian & pemesanan menjadi lebih mudah"

#: app/routes/_all._catch.participant_.mutate.tsx:1191
msgid "Returning participant? Retrieve your registration via OTP email verification."
msgstr "Peserta yang kembali? Ambil pendaftaran Anda melalui verifikasi email OTP."

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:992
#: app/domain/participant/ParticipantFields.tsx:510
msgid "Room number"
msgstr "Nomor Kamar"

#: app/domain/participant/ParticipantFields.tsx:511
msgid "Room number of your stay"
msgstr "Nomor kamar tempat Anda menginap"

#: app/domain/participant/ParticipantFields.tsx:425
#~ msgid "Room number of your stay at our resort"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:562
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Save"
msgstr "Simpan"

#: app/domain/participant/ParticipantFields.tsx:737
msgid "select"
msgstr "Pilih"

#: app/domain/participant/ParticipantFields.tsx:1338
msgid "Select"
msgstr "Pilih"

#: app/routes/_all._catch.participant_.mutate.tsx:1281
msgid "Select add-ons"
msgstr "Pilih add-on"

#: app/components/CountrySelectHeadlessUi.tsx:100
#: app/components/CountrySelect.tsx:171
msgid "select country"
msgstr "Pilih Negara"

#: app/components/CountrySelectHeadlessUi.tsx:133
#: app/components/CountrySelect.tsx:202
msgid "Select country"
msgstr "Pilih Negara"

#: app/components/CountrySelectHeadlessUi.tsx:28
#: app/components/CountrySelect.tsx:41
msgid "Select Country"
msgstr "Pilih Negara"

#: app/domain/participant/GenderSelect.tsx:67
msgid "Select gender"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:197
msgid "Select level"
msgstr "Pilih tingkat"

#: app/routes/_all._catch.participant_.mutate.tsx:646
#~ msgid "Select one or more activities"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:222
msgid "Select organisation"
msgstr "Pilih organisasi"

#: app/domain/participant/ParticipantFields.tsx:803
msgid "Select size"
msgstr "Pilih ukuran"

#: app/domain/participant/ParticipantFields.tsx:1012
msgid "Shoe size"
msgstr "Ukuran sepatu"

#: app/routes/_all._catch.waiver.$id.tsx:364
msgid "Signature of certified medical doctor or other legally certified medical provider"
msgstr "Tanda Tangan Dokter"

#: app/domain/participant/participant.signature.component.tsx:131
#: app/domain/participant/participant.signature.component.tsx:137
msgid "Signature of parent or guardian"
msgstr "Tanda tangan orang tua atau wali"

#: app/domain/participant/participant.signature.component.tsx:78
#: app/domain/participant/participant.signature.component.tsx:84
msgid "Signature participant"
msgstr "Tanda tangan peserta"

#: app/domain/participant/ParticipantFields.tsx:488
msgid "Stay"
msgstr "Penginapan"

#: app/domain/participant/ParticipantFields.tsx:386
#~ msgid "Staying with us? Please fill your room number."
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:330
msgid "The above-named person requests your opinion of his/her medical suitability to participate in recreational scuba diving or freediving training or activity. Please visit <0>uhms.org</0> for medical guidance on medical conditions as they relate to diving. Review the areas relevant to your patient as part of your evaluation."
msgstr "Yang bersangkutan yang disebutkan di atas meminta pendapat Anda tentang kesesuaian kondisi medisnya untuk berpartisipasi\n"
"dalam kegiatan rekreasi atau pelatihan scuba diving atau freediving. Silakan kunjungi <0>uhms.org</0> untuk panduan medis tentang\n"
"kondisi medis yang berkaitan dengan menyelam. Tinjau area yang relevan dengan pasien Anda sebagai bagian dari pemeriksaan\n"
"anda."

#: app/domain/participant/participant.signature.component.tsx:23
msgid "Today's date"
msgstr "Tanggal hari ini"

#: app/domain/participant/participant-fields.tsx:8
msgid "Vegan"
msgstr "Vegan"

#: app/domain/participant/participant-fields.tsx:7
msgid "Vegetarian"
msgstr "Vegetarian"

#: app/routes/_all._catch._w.waiver_.mutate.tsx:173
#~ msgid "View"
#~ msgstr "View"

#: app/misc/error-translations.ts:15
msgid "Wachtwoord moet tenminste 6 tekens lang zijn."
msgstr "Kata sandi harus setidaknya 6 karakter."

#: app/domain/participant/participant-data.tsx:47
msgid "Walk-in"
msgstr "Datang langsung"

#: app/domain/participant/ParticipantFields.tsx:910
msgid "Weight"
msgstr "Berat"

#: app/domain/participant/ParticipantFields.tsx:1115
msgid "Weightbelt"
msgstr "Sabuk pemberat"

#: app/domain/participant/participant-data.tsx:36
msgid "Wetsuit"
msgstr "Pakaian selam basah"

#: app/domain/participant/ParticipantFields.tsx:802
msgid "Wetsuit size"
msgstr "Ukuran pakaian selam basah"

#: app/domain/participant/ParticipantFields.tsx:570
msgid "WhatsApp preferred"
msgstr "Whatsapp pilihan"

#: app/domain/participant/participant-helpers.tsx:10
msgid "Within the last 3 months"
msgstr "Dalam 3 bulan terakhir"

#: app/domain/participant/participant-helpers.tsx:11
msgid "Within the last 6 months"
msgstr "Dalam 6 bulan terakhir"

#: app/domain/participant/participant-helpers.tsx:12
msgid "Within the last year"
msgstr "Dalam setahun terakhir"

#: app/domain/participant/ParticipantFields.tsx:709
msgid "Years of experience"
msgstr "Tahun pengalaman"

#: app/routes/_all._catch.waiver.$id.tsx:214
#: app/routes/_all._catch.waiver.$id.tsx:265
#: app/domain/participant/ParticipantFields.tsx:1253
msgid "Yes"
msgstr "Ya"

#: app/domain/participant/ParticipantFields.tsx:1285
msgid "Your Instagram Handle"
msgstr "Handle Instagram Anda"

#: app/domain/participant/participant-data.tsx:50
msgid "YouTube"
msgstr ""

