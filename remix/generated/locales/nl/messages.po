msgid ""
msgstr ""
"POT-Creation-Date: 2024-03-30 07:31+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: nl\n"
"Project-Id-Version: traveltruster\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-28 13:35\n"
"Last-Translator: \n"
"Language-Team: Dutch\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: traveltruster\n"
"X-Crowdin-Project-ID: 668342\n"
"X-Crowdin-Language: nl\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 2\n"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:603
#~ msgid "{0, plural, one {upload} other {uploads}}"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
#~ msgid "&sl;1y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:8
#~ msgid "&sl;3mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:9
#~ msgid "&sl;6mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
msgid "<1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
#~ msgid "<2y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
msgid "<3mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:11
msgid "<6mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:13
msgid ">1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:14
msgid ">2y"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:381
msgid "Address"
msgstr "Adres"

#: app/domain/participant/participant-data.tsx:52
msgid "Agency"
msgstr "Agentschap"

#: app/routes/_all._catch.waiver.$id.tsx:349
msgid "Approved – I find no conditions that I consider incompatible with recreational scuba diving or freediving."
msgstr "Goedgekeurd – Ik vind geen aandoeningen die ik onverenigbaar acht met recreatief duiken of freediven."

#: app/misc/error-translations.ts:7
msgid "Authentication code was expired"
msgstr "Authenticatie code is verlopen"

#: app/domain/participant/participant-data.tsx:34
msgid "BCD"
msgstr "BCD"

#: app/domain/participant/ParticipantFields.tsx:1221
msgid "BCD Size"
msgstr "BCD Maat"

#: app/routes/_all._catch.waiver.$id.tsx:324
msgid "Birthdate"
msgstr "Geboortedatum"

#: app/routes/_all._catch.participant_.mutate.tsx:843
msgid "Booking"
msgstr "Boeking"

#: app/domain/participant/participant-fields.tsx:11
msgid "Bring my own"
msgstr "Neem mijn eigen mee"

#: app/domain/participant/ParticipantFields.tsx:694
#~ msgid "Certificate"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:690
msgid "Certificate details"
msgstr "Certificaatgegevens"

#: app/domain/participant/ParticipantFields.tsx:667
msgid "Certificate level"
msgstr "Certificeringsniveau"

#: app/domain/participant/ParticipantFields.tsx:686
#~ msgid "Certificate number"
#~ msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:996
msgid "Certification"
msgstr "Certificering"

#: app/domain/participant/ParticipantFields.tsx:644
msgid "Certification organisation"
msgstr "Certificeringsinstantie"

#: app/routes/_all._catch.participant_.mutate.tsx:1416
msgid "Click to read"
msgstr "Klik om te lezen"

#: app/routes/_all._catch.waiver.$id.tsx:378
msgid "Clinic/Hospital"
msgstr "Clinic/ziekenhuis"

#: app/routes/_all._catch.waiver.$id.tsx:375
msgid "Clinical Degrees/Credentials"
msgstr "Klinische Kwalificaties/Titels"

#: app/domain/participant/ParticipantFields.tsx:607
msgid "Company and policy number"
msgstr "Verzekeraar en polisnummer"

#: app/routes/_all._catch.participant_.mutate.tsx:1217
#~ msgid "Continue"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Copy"
msgstr "Kopieer"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1008
#: app/domain/participant/ParticipantFields.tsx:443
msgid "Country"
msgstr "Land"

#: app/routes/_all._catch.waiver.$id.tsx:403
msgid "Created by the <0>Diver Medical Screen Committee</0> in association with the following bodies:"
msgstr "Gemaakt door de <0>Diver Medical Screen commissie</0> in samenwerking met de volgende organen:"

#: app/routes/_all._catch.waiver.$id.tsx:369
msgid "Date (dd/mm/yyyy)"
msgstr "Datum (dd/mm/jjjj)"

#: app/domain/participant/ParticipantFields.tsx:516
msgid "Date of birth"
msgstr "Geboortedatum"

#: app/domain/participant/ParticipantFields.tsx:126
msgid "Dive specific details"
msgstr "Duikspecifieke gegevens"

#: app/routes/_all._catch.waiver.$id.tsx:308
msgid "Diver Medical"
msgstr "Duikmedisch onderzoek"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1000
msgid "Dives"
msgstr "Duiken"

#: app/domain/participant/ParticipantFields.tsx:606
msgid "Do you have an insurance that covers underwater activities? please fill your details below"
msgstr "Heb je een verzekering die onderwateractiviteiten dekt? Vul hieronder je gegevens in"

#: app/domain/participant/ParticipantFields.tsx:641
msgid "e.g. friend, father or mother"
msgstr "bijv. vriend, vader of moeder"

#: app/domain/participant/ParticipantFields.tsx:497
msgid "e.g. Hotel name"
msgstr "bijv. Hotelnaam"

#: app/domain/participant/ParticipantFields.tsx:601
msgid "e.g. nut allergy"
msgstr "bijv. notenallergie"

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Edit"
msgstr "Bewerken"

#: app/routes/_all._catch.waiver.$id.tsx:387
msgid "Email"
msgstr "Email"

#: app/routes/_all._catch.participant_.mutate.tsx:1201
msgid "Email OTP"
msgstr "E-mail OTP"

#: app/domain/participant/ParticipantFields.tsx:118
msgid "Emergency contact details"
msgstr "Contactgegevens voor noodgevallen"

#: app/domain/participant/ParticipantFields.tsx:611
msgid "Emergency contact name"
msgstr "Naam noodcontact"

#: app/domain/participant/ParticipantFields.tsx:615
msgid "Emergency contact phone number"
msgstr "Telefoonnummer noodcontact"

#: app/components/base/AddressInput.tsx:70
msgid "Enter a location"
msgstr "Voer een locatie in"

#: app/domain/participant/ParticipantFields.tsx:396
#~ msgid "Enter here"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:390
#~ msgid "Enter here or upload below"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:347
msgid "Enter number"
msgstr "Voer een nummer in"

#: app/routes/_all._catch._index.tsx:154
msgid "Explore Bali underwater"
msgstr "Verken Bali onderwater"

#: app/domain/participant/participant-data.tsx:46
msgid "Facebook"
msgstr "Facebook"

#: app/domain/participant/ParticipantFields.tsx:130
msgid "Feedback"
msgstr "Feedback"

#: app/domain/participant/GenderSelect.tsx:9
msgid "Female"
msgstr "Vrouw"

#: app/domain/participant/participant-data.tsx:33
msgid "Fins"
msgstr "Vinnen"

#: app/domain/participant/ParticipantFields.tsx:407
msgid "First name"
msgstr "Voornaam"

#: app/domain/participant/ParticipantFields.tsx:600
msgid "Food allergies"
msgstr "Voedingsallergieën"

#: app/domain/participant/participant-data.tsx:44
msgid "Friend"
msgstr "Vriend"

#: app/domain/participant/ParticipantFields.tsx:543
msgid "Gender"
msgstr "Geslacht"

#: app/domain/participant/participant-fields.tsx:9
msgid "Gluten free"
msgstr "Glutenvrij"

#: app/domain/participant/participant-data.tsx:48
msgid "Google"
msgstr "Google"

#: app/domain/participant/participant-data.tsx:49
msgid "Google Maps"
msgstr "Google Maps"

#: app/domain/participant/ParticipantFields.tsx:808
msgid "Height"
msgstr "Lengte"

#: app/misc/error-translations.ts:14
msgid "Het email adres is al in gebruik door een ander account."
msgstr "Het email adres is al in gebruik door een ander account."

#: app/domain/participant/ParticipantFields.tsx:463
msgid "Home/residential address"
msgstr "Thuis/woonadres"

#: app/domain/participant/participant-data.tsx:51
msgid "Hotel"
msgstr "Hotel"

#: app/domain/participant/ParticipantFields.tsx:385
#~ msgid "Hotel/hostel/homestay"
#~ msgstr ""

#. placeholder {0}: waiver.translation?.name
#: app/routes/_all._catch.participant_.mutate.tsx:1458
msgid "I have read and agree with the above <0>{0}</0>"
msgstr "Ik heb de <0>{0}</0> gelezen en ga hiermee akkoord"

#: app/routes/_all._catch.participant_.mutate.tsx:800
#~ msgid "I have read and agree with the above <0>{0}</0> and agree"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1290
msgid "If you require any additional addons, select the amount below"
msgstr "Als je extra opties wilt toevoegen, kies dan hieronder het gewenste aantal."

#: app/domain/participant/ParticipantFields.tsx:121
msgid "Important! Ensure the emergency contact is not someone joining the activity."
msgstr "Belangrijk! Zorg ervoor dat de contactpersoon bij noodgevallen niet iemand is die deelneemt aan de activiteit."

#: app/domain/participant/participant-data.tsx:45
msgid "Instagram"
msgstr "Instagram"

#: app/domain/participant/ParticipantFields.tsx:605
msgid "Insurance"
msgstr "Verzekering"

#: app/misc/error-translations.ts:9
msgid "Invalid credentials"
msgstr "Ongeldige inloggegevens"

#: app/misc/error-translations.ts:8
msgid "Invalid email address"
msgstr "Ongeldig E-mailadres"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1004
#: app/domain/participant/ParticipantFields.tsx:712
msgid "Last dive"
msgstr "Laatste duik"

#: app/domain/participant/ParticipantFields.tsx:421
msgid "Last name"
msgstr "Achternaam"

#: app/domain/participant/GenderSelect.tsx:8
msgid "Male"
msgstr "Man"

#: app/domain/participant/participant-data.tsx:32
msgid "Mask"
msgstr "Duikbril"

#: app/domain/participant/ParticipantFields.tsx:1226
msgid "May we ask how you heard about us?"
msgstr "Hoe heeft u ons hebt gevonden?"

#: app/domain/participant/ParticipantFields.tsx:1230
msgid "May we contact you to ask about your experience?"
msgstr "Mogen we contact met u opnemen om te vragen naar uw ervaring?"

#: app/domain/participant/ParticipantFields.tsx:595
msgid "Meal preferences"
msgstr "Maaltijd voorkeuren"

#: app/routes/_all._catch.waiver.$id.tsx:312
msgid "Medical Examiner's Evaluation Form"
msgstr "Evaluatieformulier in te vullen door de arts"

#: app/routes/_all._catch.waiver.$id.tsx:372
msgid "Medical Examiner’s Name"
msgstr "Naam onderzoekende Arts"

#: app/domain/participant/participant-helpers.tsx:13
msgid "More than 1 year ago"
msgstr "Meer dan 1 jaar geleden"

#: app/domain/participant/participant-helpers.tsx:14
msgid "more than 2 years ago"
msgstr "Meer dan 2 jaar geleden"

#: app/misc/error-translations.ts:67
msgid "Name already exists under this establishment"
msgstr "Naam bestaat al onder deze instelling"

#: app/domain/participant/participant.signature.component.tsx:97
msgid "Name of parent or guardian"
msgstr "Naam van de ouder of voogd"

#: app/domain/participant/participant.signature.component.tsx:57
msgid "Name participant"
msgstr "Naam deelnemer"

#: app/routes/_all._catch.waiver.$id.tsx:231
#: app/routes/_all._catch.waiver.$id.tsx:282
#: app/domain/participant/ParticipantFields.tsx:1267
msgid "No"
msgstr "Nee"

#: app/misc/error-translations.ts:6
msgid "No changes were made"
msgstr "Er zijn geen wijzigingen gemaakt"

#: app/domain/participant/participant-fields.tsx:10
msgid "No preference"
msgstr "Geen voorkeur"

#: app/domain/participant/GenderSelect.tsx:10
msgid "Non-binary"
msgstr "Niet-binair"

#: app/routes/_all._catch.waiver.$id.tsx:355
msgid "Not approved – I find conditions that I consider incompatible with recreational scuba diving or freediving"
msgstr "Afgekeurd – Ik vind aandoeningen die ik onverenigbaar acht met recreatief duiken of freediving."

#: app/domain/participant/ParticipantFields.tsx:704
msgid "Number of dives"
msgstr "Aantal duiken"

#: app/domain/participant/ParticipantFields.tsx:436
msgid "Or a valid local ID card"
msgstr "Of een geldige lokale identiteitskaart"

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "or an ID that's valid in the operator country"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:804
msgid "Or t-shirt size if you don't know your wetsuit size."
msgstr "Of T-shirtmaat als je je wetsuitmaat niet weet."

#: app/domain/participant/ParticipantFields.tsx:134
msgid "Other info (optional)"
msgstr "Overige info (optioneel)"

#: app/domain/participant/ParticipantFields.tsx:749
msgid "Own gear"
msgstr "Eigen uitrusting"

#: app/misc/error-translations.ts:10
msgid "Page was not found or you are unauthorized"
msgstr "Pagina is niet gevonden of u bent niet gemachtigd"

#: app/domain/participant/ParticipantFields.tsx:114
msgid "Participant details"
msgstr "Deelnemergegevens"

#: app/routes/_all._catch.waiver.$id.tsx:318
msgid "Participant name"
msgstr "Naam deelnemer"

#: app/domain/participant/ParticipantFields.tsx:435
msgid "Passport"
msgstr "Paspoort"

#: app/domain/participant/ParticipantFields.tsx:303
#~ msgid "Passport No."
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "Passport number"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:384
#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1012
msgid "Phone"
msgstr "Telefoon"

#: app/domain/participant/ParticipantFields.tsx:569
msgid "Phone number"
msgstr "Telefoonnummer"

#: app/routes/_all._catch.waiver.$id.tsx:396
msgid "Physician/Clinic Stamp (optional)"
msgstr "Stempel Arts / Kliniek (optioneel)"

#: app/routes/_all._catch._w.planning.u.tsx:107
msgid "Planning"
msgstr "Planning"

#: app/routes/_all._catch.participant_.mutate.tsx:1406
msgid "Please check this box to continue."
msgstr "Vink dit selectievakje aan om door te gaan."

#: app/domain/participant/ParticipantFields.tsx:1289
msgid "Please fill any additional important info or questions below"
msgstr "Gelieve eventuele aanvullende belangrijke informatie of vragen hieronder in te vullen."

#: app/domain/participant/ParticipantFields.tsx:750
msgid "Please select in case you bring your own gear."
msgstr "Selecteer in het geval u uw eigen uitrusting meeneemt."

#: app/routes/_all._catch.participant_.mutate.tsx:883
msgid "Please select the activity (or activities) you're attending."
msgstr "Selecteer de activiteit(en) waaraan je deelneemt."

#: app/domain/participant/GenderSelect.tsx:11
msgid "Prefer not to say"
msgstr "Zeg ik liever niet"

#: app/routes/_all._catch.participant_.mutate.tsx:830
msgid "Preferred language"
msgstr "Voorkeurstaal"

#: app/routes/_all._catch.participant_.mutate.tsx:1250
msgid "Recipient email"
msgstr "E-mailadres ontvanger"

#: app/routes/_all._catch.participant_.mutate.tsx:1149
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Register"
msgstr "Registreer"

#: app/domain/participant/participant-data.tsx:35
msgid "Regulator"
msgstr "Ademautomaat"

#: app/domain/participant/ParticipantFields.tsx:640
msgid "Relationship"
msgstr "Relatie"

#: app/routes/_all._catch._index.tsx:156
msgid "Research & booking made easier"
msgstr "Makkelijker zoeken, sneller boeken"

#: app/routes/_all._catch.participant_.mutate.tsx:1191
msgid "Returning participant? Retrieve your registration via OTP email verification."
msgstr "Terugkerende deelnemer? Haal je registratie op via OTP-e-mailverificatie."

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:992
#: app/domain/participant/ParticipantFields.tsx:510
msgid "Room number"
msgstr "Kamernummer"

#: app/domain/participant/ParticipantFields.tsx:511
msgid "Room number of your stay"
msgstr "Kamernummer van je verblijf"

#: app/domain/participant/ParticipantFields.tsx:425
#~ msgid "Room number of your stay at our resort"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:562
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Save"
msgstr "Opslaan"

#: app/domain/participant/ParticipantFields.tsx:737
msgid "select"
msgstr "selecteer"

#: app/domain/participant/ParticipantFields.tsx:1338
msgid "Select"
msgstr "Selecteer"

#: app/routes/_all._catch.participant_.mutate.tsx:1281
msgid "Select add-ons"
msgstr "Selecteer extra opties"

#: app/components/CountrySelectHeadlessUi.tsx:100
#: app/components/CountrySelect.tsx:171
msgid "select country"
msgstr "Selecteer land"

#: app/components/CountrySelectHeadlessUi.tsx:133
#: app/components/CountrySelect.tsx:202
msgid "Select country"
msgstr "Selecteer land"

#: app/components/CountrySelectHeadlessUi.tsx:28
#: app/components/CountrySelect.tsx:41
msgid "Select Country"
msgstr "Kies land"

#: app/domain/participant/GenderSelect.tsx:67
msgid "Select gender"
msgstr "Selecteer geslacht"

#: app/domain/participant/ParticipantFields.tsx:197
msgid "Select level"
msgstr "Selecteer niveau"

#: app/routes/_all._catch.participant_.mutate.tsx:646
#~ msgid "Select one or more activities"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:222
msgid "Select organisation"
msgstr "Selecteer organisatie"

#: app/domain/participant/ParticipantFields.tsx:803
msgid "Select size"
msgstr "Selecteer maat"

#: app/domain/participant/ParticipantFields.tsx:1012
msgid "Shoe size"
msgstr "Schoenmaat"

#: app/routes/_all._catch.waiver.$id.tsx:364
msgid "Signature of certified medical doctor or other legally certified medical provider"
msgstr "Handtekening van gecertificeerde arts of andere wettelijk gecertificeerde medische zorgverlener"

#: app/domain/participant/participant.signature.component.tsx:131
#: app/domain/participant/participant.signature.component.tsx:137
msgid "Signature of parent or guardian"
msgstr "Handtekening van ouder of voogd"

#: app/domain/participant/participant.signature.component.tsx:78
#: app/domain/participant/participant.signature.component.tsx:84
msgid "Signature participant"
msgstr "Handtekening deelnemer"

#: app/domain/participant/ParticipantFields.tsx:488
msgid "Stay"
msgstr "Verblijf"

#: app/domain/participant/ParticipantFields.tsx:386
#~ msgid "Staying with us? Please fill your room number."
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:330
msgid "The above-named person requests your opinion of his/her medical suitability to participate in recreational scuba diving or freediving training or activity. Please visit <0>uhms.org</0> for medical guidance on medical conditions as they relate to diving. Review the areas relevant to your patient as part of your evaluation."
msgstr "De bovengenoemde persoon vraagt uw opinie over zijn/haar medische geschiktheid om deel te nemen aan recreatieve duik- of freediving training of -activiteiten. Indien nodig, kan U op de website <0>uhms.org</0> informatie vinden over medische aandoeningen die betrekking hebben op het duiken. Bekijk de onderdelen die relevant zijn voor uw patiënt als onderdeel van uw evaluatie."

#: app/domain/participant/participant.signature.component.tsx:23
msgid "Today's date"
msgstr "Datum van vandaag"

#: app/domain/participant/participant-fields.tsx:8
msgid "Vegan"
msgstr "Veganistisch"

#: app/domain/participant/participant-fields.tsx:7
msgid "Vegetarian"
msgstr "Vegetarisch"

#: app/routes/_all._catch._w.waiver_.mutate.tsx:173
#~ msgid "View"
#~ msgstr "View"

#: app/misc/error-translations.ts:15
msgid "Wachtwoord moet tenminste 6 tekens lang zijn."
msgstr "Wachtwoord moet tenminste 6 tekens lang zijn."

#: app/domain/participant/participant-data.tsx:47
msgid "Walk-in"
msgstr "Inloop"

#: app/domain/participant/ParticipantFields.tsx:910
msgid "Weight"
msgstr "Gewicht"

#: app/domain/participant/ParticipantFields.tsx:1115
msgid "Weightbelt"
msgstr "Loodgordel"

#: app/domain/participant/participant-data.tsx:36
msgid "Wetsuit"
msgstr "Wetsuit"

#: app/domain/participant/ParticipantFields.tsx:802
msgid "Wetsuit size"
msgstr "Wetsuitmaat\n"

#: app/domain/participant/ParticipantFields.tsx:570
msgid "WhatsApp preferred"
msgstr "Bij voorkeur WhatsApp"

#: app/domain/participant/participant-helpers.tsx:10
msgid "Within the last 3 months"
msgstr "In de afgelopen 3 maanden"

#: app/domain/participant/participant-helpers.tsx:11
msgid "Within the last 6 months"
msgstr "In de afgelopen 6 maanden"

#: app/domain/participant/participant-helpers.tsx:12
msgid "Within the last year"
msgstr "In het afgelopen jaar"

#: app/domain/participant/ParticipantFields.tsx:709
msgid "Years of experience"
msgstr "Aantal jaren ervaring"

#: app/routes/_all._catch.waiver.$id.tsx:214
#: app/routes/_all._catch.waiver.$id.tsx:265
#: app/domain/participant/ParticipantFields.tsx:1253
msgid "Yes"
msgstr "Ja"

#: app/domain/participant/ParticipantFields.tsx:1285
msgid "Your Instagram Handle"
msgstr "Je Instagram naam"

#: app/domain/participant/participant-data.tsx:50
msgid "YouTube"
msgstr "YouTube"

