msgid ""
msgstr ""
"POT-Creation-Date: 2024-03-30 07:31+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: ru\n"
"Project-Id-Version: traveltruster\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-28 13:35\n"
"Last-Translator: \n"
"Language-Team: Russian\n"
"Plural-Forms: nplurals=4; plural=((n%10==1 && n%100!=11) ? 0 : ((n%10 >= 2 && n%10 <=4 && (n%100 < 12 || n%100 > 14)) ? 1 : ((n%10 == 0 || (n%10 >= 5 && n%10 <=9)) || (n%100 >= 11 && n%100 <= 14)) ? 2 : 3));\n"
"X-Crowdin-Project: traveltruster\n"
"X-Crowdin-Project-ID: 668342\n"
"X-Crowdin-Language: ru\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 2\n"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:603
#~ msgid "{0, plural, one {upload} other {uploads}}"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
#~ msgid "&sl;1y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:8
#~ msgid "&sl;3mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:9
#~ msgid "&sl;6mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
msgid "<1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
#~ msgid "<2y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
msgid "<3mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:11
msgid "<6mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:13
msgid ">1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:14
msgid ">2y"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:381
msgid "Address"
msgstr "Адрес"

#: app/domain/participant/participant-data.tsx:52
msgid "Agency"
msgstr "Агентство"

#: app/routes/_all._catch.waiver.$id.tsx:349
msgid "Approved – I find no conditions that I consider incompatible with recreational scuba diving or freediving."
msgstr "Одобрено – Я не нахожу условий, которые считаю несовместимыми с рекреационным дайвингом или фридайвингом."

#: app/misc/error-translations.ts:7
msgid "Authentication code was expired"
msgstr "Срок действия кода аутентификации истек"

#: app/domain/participant/participant-data.tsx:34
msgid "BCD"
msgstr "BCD"

#: app/domain/participant/ParticipantFields.tsx:1221
msgid "BCD Size"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:324
msgid "Birthdate"
msgstr "Дата рождения"

#: app/routes/_all._catch.participant_.mutate.tsx:843
msgid "Booking"
msgstr "Бронирование"

#: app/domain/participant/participant-fields.tsx:11
msgid "Bring my own"
msgstr "Принести своё"

#: app/domain/participant/ParticipantFields.tsx:694
#~ msgid "Certificate"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:690
msgid "Certificate details"
msgstr "Детали сертификата"

#: app/domain/participant/ParticipantFields.tsx:667
msgid "Certificate level"
msgstr "Уровень сертификата"

#: app/domain/participant/ParticipantFields.tsx:686
#~ msgid "Certificate number"
#~ msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:996
msgid "Certification"
msgstr "Сертификация"

#: app/domain/participant/ParticipantFields.tsx:644
msgid "Certification organisation"
msgstr "Сертифицирующая организация"

#: app/routes/_all._catch.participant_.mutate.tsx:1416
msgid "Click to read"
msgstr "Нажмите, чтобы прочитать"

#: app/routes/_all._catch.waiver.$id.tsx:378
msgid "Clinic/Hospital"
msgstr "Клиника/Больница"

#: app/routes/_all._catch.waiver.$id.tsx:375
msgid "Clinical Degrees/Credentials"
msgstr "Клинические степени/Квалификации"

#: app/domain/participant/ParticipantFields.tsx:607
msgid "Company and policy number"
msgstr "Компания и номер полиса"

#: app/routes/_all._catch.participant_.mutate.tsx:1217
#~ msgid "Continue"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Copy"
msgstr "Копия"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1008
#: app/domain/participant/ParticipantFields.tsx:443
msgid "Country"
msgstr "Страна"

#: app/routes/_all._catch.waiver.$id.tsx:403
msgid "Created by the <0>Diver Medical Screen Committee</0> in association with the following bodies:"
msgstr "Создано <0>Комитетом по медосмотру дайверов</0> в сотрудничестве с следующими органами:"

#: app/routes/_all._catch.waiver.$id.tsx:369
msgid "Date (dd/mm/yyyy)"
msgstr "Дата (дд/мм/гггг)"

#: app/domain/participant/ParticipantFields.tsx:516
msgid "Date of birth"
msgstr "Дата рождения"

#: app/domain/participant/ParticipantFields.tsx:126
msgid "Dive specific details"
msgstr "Подробности о дайвинге"

#: app/routes/_all._catch.waiver.$id.tsx:308
msgid "Diver Medical"
msgstr "Медицинское обследование дайвера"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1000
msgid "Dives"
msgstr "Дайвы"

#: app/domain/participant/ParticipantFields.tsx:606
msgid "Do you have an insurance that covers underwater activities? please fill your details below"
msgstr "У вас есть страховка, покрывающая подводные активности? Пожалуйста, заполните свои данные ниже"

#: app/domain/participant/ParticipantFields.tsx:641
msgid "e.g. friend, father or mother"
msgstr "например, друг, отец или мать"

#: app/domain/participant/ParticipantFields.tsx:497
msgid "e.g. Hotel name"
msgstr "например, название отеля"

#: app/domain/participant/ParticipantFields.tsx:601
msgid "e.g. nut allergy"
msgstr "например, аллергия на орехи"

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Edit"
msgstr "Редактировать"

#: app/routes/_all._catch.waiver.$id.tsx:387
msgid "Email"
msgstr "Email"

#: app/routes/_all._catch.participant_.mutate.tsx:1201
msgid "Email OTP"
msgstr "OTP по электронной почте"

#: app/domain/participant/ParticipantFields.tsx:118
msgid "Emergency contact details"
msgstr "Контактные данные на случай чрезвычайной ситуации"

#: app/domain/participant/ParticipantFields.tsx:611
msgid "Emergency contact name"
msgstr "Имя контакта на случай чрезвычайной ситуации"

#: app/domain/participant/ParticipantFields.tsx:615
msgid "Emergency contact phone number"
msgstr "Номер телефона контакта на случай чрезвычайной ситуации"

#: app/components/base/AddressInput.tsx:70
msgid "Enter a location"
msgstr "Введите местоположение"

#: app/domain/participant/ParticipantFields.tsx:396
#~ msgid "Enter here"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:390
#~ msgid "Enter here or upload below"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:347
msgid "Enter number"
msgstr "Введите номер"

#: app/routes/_all._catch._index.tsx:154
msgid "Explore Bali underwater"
msgstr "Исследуйте подводный мир Бали"

#: app/domain/participant/participant-data.tsx:46
msgid "Facebook"
msgstr "Facebook"

#: app/domain/participant/ParticipantFields.tsx:130
msgid "Feedback"
msgstr "Отзыв"

#: app/domain/participant/GenderSelect.tsx:9
msgid "Female"
msgstr ""

#: app/domain/participant/participant-data.tsx:33
msgid "Fins"
msgstr "Ласты"

#: app/domain/participant/ParticipantFields.tsx:407
msgid "First name"
msgstr "Имя"

#: app/domain/participant/ParticipantFields.tsx:600
msgid "Food allergies"
msgstr "Пищевые аллергии"

#: app/domain/participant/participant-data.tsx:44
msgid "Friend"
msgstr "Друг"

#: app/domain/participant/ParticipantFields.tsx:543
msgid "Gender"
msgstr ""

#: app/domain/participant/participant-fields.tsx:9
msgid "Gluten free"
msgstr "Без глютена"

#: app/domain/participant/participant-data.tsx:48
msgid "Google"
msgstr "Google"

#: app/domain/participant/participant-data.tsx:49
msgid "Google Maps"
msgstr "Google Maps"

#: app/domain/participant/ParticipantFields.tsx:808
msgid "Height"
msgstr "Рост"

#: app/misc/error-translations.ts:14
msgid "Het email adres is al in gebruik door een ander account."
msgstr "Этот адрес электронной почты уже используется другим аккаунтом."

#: app/domain/participant/ParticipantFields.tsx:463
msgid "Home/residential address"
msgstr "Домашний/проживающий адрес"

#: app/domain/participant/participant-data.tsx:51
msgid "Hotel"
msgstr "Отель"

#: app/domain/participant/ParticipantFields.tsx:385
#~ msgid "Hotel/hostel/homestay"
#~ msgstr ""

#. placeholder {0}: waiver.translation?.name
#: app/routes/_all._catch.participant_.mutate.tsx:1458
msgid "I have read and agree with the above <0>{0}</0>"
msgstr "Я прочитал и согласен с вышеуказанным <0>{0}</0>"

#: app/routes/_all._catch.participant_.mutate.tsx:800
#~ msgid "I have read and agree with the above <0>{0}</0> and agree"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1290
msgid "If you require any additional addons, select the amount below"
msgstr "Если вам требуются дополнительные дополнения, выберите количество ниже"

#: app/domain/participant/ParticipantFields.tsx:121
msgid "Important! Ensure the emergency contact is not someone joining the activity."
msgstr "Важно! Убедитесь, что контактное лицо в экстренной ситуации не является участником мероприятия."

#: app/domain/participant/participant-data.tsx:45
msgid "Instagram"
msgstr "Instagram"

#: app/domain/participant/ParticipantFields.tsx:605
msgid "Insurance"
msgstr "Страховка"

#: app/misc/error-translations.ts:9
msgid "Invalid credentials"
msgstr "Недействительные учетные данные"

#: app/misc/error-translations.ts:8
msgid "Invalid email address"
msgstr "Недействительный адрес электронной почты"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1004
#: app/domain/participant/ParticipantFields.tsx:712
msgid "Last dive"
msgstr "Последний дайв"

#: app/domain/participant/ParticipantFields.tsx:421
msgid "Last name"
msgstr "Фамилия"

#: app/domain/participant/GenderSelect.tsx:8
msgid "Male"
msgstr ""

#: app/domain/participant/participant-data.tsx:32
msgid "Mask"
msgstr "Маска"

#: app/domain/participant/ParticipantFields.tsx:1226
msgid "May we ask how you heard about us?"
msgstr "Можем ли мы спросить, как вы о нас узнали?"

#: app/domain/participant/ParticipantFields.tsx:1230
msgid "May we contact you to ask about your experience?"
msgstr "Можем ли мы связаться с вами, чтобы узнать о вашем опыте?"

#: app/domain/participant/ParticipantFields.tsx:595
msgid "Meal preferences"
msgstr "Предпочтения в еде"

#: app/routes/_all._catch.waiver.$id.tsx:312
msgid "Medical Examiner's Evaluation Form"
msgstr "Форма оценки врача"

#: app/routes/_all._catch.waiver.$id.tsx:372
msgid "Medical Examiner’s Name"
msgstr "Имя врача"

#: app/domain/participant/participant-helpers.tsx:13
msgid "More than 1 year ago"
msgstr "Более года назад"

#: app/domain/participant/participant-helpers.tsx:14
msgid "more than 2 years ago"
msgstr "Более двух лет назад"

#: app/misc/error-translations.ts:67
msgid "Name already exists under this establishment"
msgstr "Имя уже существует в этом учреждении"

#: app/domain/participant/participant.signature.component.tsx:97
msgid "Name of parent or guardian"
msgstr "Имя родителя или опекуна"

#: app/domain/participant/participant.signature.component.tsx:57
msgid "Name participant"
msgstr "Имя участника"

#: app/routes/_all._catch.waiver.$id.tsx:231
#: app/routes/_all._catch.waiver.$id.tsx:282
#: app/domain/participant/ParticipantFields.tsx:1267
msgid "No"
msgstr "Нет"

#: app/misc/error-translations.ts:6
msgid "No changes were made"
msgstr "Изменений не было"

#: app/domain/participant/participant-fields.tsx:10
msgid "No preference"
msgstr "Без предпочтений"

#: app/domain/participant/GenderSelect.tsx:10
msgid "Non-binary"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:355
msgid "Not approved – I find conditions that I consider incompatible with recreational scuba diving or freediving"
msgstr "Не одобрено – Я нахожу условия, которые считаю несовместимыми с рекреационным дайвингом или фридайвингом"

#: app/domain/participant/ParticipantFields.tsx:704
msgid "Number of dives"
msgstr "Количество погружений"

#: app/domain/participant/ParticipantFields.tsx:436
msgid "Or a valid local ID card"
msgstr "Или действительное местное удостоверение личности"

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "or an ID that's valid in the operator country"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:804
msgid "Or t-shirt size if you don't know your wetsuit size."
msgstr "Или размер футболки, если вы не знаете размер гидрокостюма."

#: app/domain/participant/ParticipantFields.tsx:134
msgid "Other info (optional)"
msgstr "Дополнительная информация (необязательно)"

#: app/domain/participant/ParticipantFields.tsx:749
msgid "Own gear"
msgstr "Свое снаряжение"

#: app/misc/error-translations.ts:10
msgid "Page was not found or you are unauthorized"
msgstr "Страница не найдена или у вас нет доступа"

#: app/domain/participant/ParticipantFields.tsx:114
msgid "Participant details"
msgstr "Данные участника"

#: app/routes/_all._catch.waiver.$id.tsx:318
msgid "Participant name"
msgstr "Имя участника"

#: app/domain/participant/ParticipantFields.tsx:435
msgid "Passport"
msgstr "Паспорт (Passport)"

#: app/domain/participant/ParticipantFields.tsx:303
#~ msgid "Passport No."
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "Passport number"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:384
#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1012
msgid "Phone"
msgstr "Телефон"

#: app/domain/participant/ParticipantFields.tsx:569
msgid "Phone number"
msgstr "Номер телефона"

#: app/routes/_all._catch.waiver.$id.tsx:396
msgid "Physician/Clinic Stamp (optional)"
msgstr "Штамп врача/клиники (необязательно)"

#: app/routes/_all._catch._w.planning.u.tsx:107
msgid "Planning"
msgstr "Планирование"

#: app/routes/_all._catch.participant_.mutate.tsx:1406
msgid "Please check this box to continue."
msgstr "Пожалуйста, поставьте галочку для продолжения."

#: app/domain/participant/ParticipantFields.tsx:1289
msgid "Please fill any additional important info or questions below"
msgstr "Пожалуйста, заполните любую дополнительную важную информацию или вопросы ниже"

#: app/domain/participant/ParticipantFields.tsx:750
msgid "Please select in case you bring your own gear."
msgstr "Пожалуйста, выберите, если вы приносите свое снаряжение."

#: app/routes/_all._catch.participant_.mutate.tsx:883
msgid "Please select the activity (or activities) you're attending."
msgstr "Пожалуйста, выберите мероприятие (или мероприятия), в которых вы участвуете."

#: app/domain/participant/GenderSelect.tsx:11
msgid "Prefer not to say"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:830
msgid "Preferred language"
msgstr "Предпочитаемый язык"

#: app/routes/_all._catch.participant_.mutate.tsx:1250
msgid "Recipient email"
msgstr "Электронная почта получателя"

#: app/routes/_all._catch.participant_.mutate.tsx:1149
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Register"
msgstr "Регистрация"

#: app/domain/participant/participant-data.tsx:35
msgid "Regulator"
msgstr "Регулятор"

#: app/domain/participant/ParticipantFields.tsx:640
msgid "Relationship"
msgstr "Отношения"

#: app/routes/_all._catch._index.tsx:156
msgid "Research & booking made easier"
msgstr "Исследования и бронирования проще"

#: app/routes/_all._catch.participant_.mutate.tsx:1191
msgid "Returning participant? Retrieve your registration via OTP email verification."
msgstr "Вы являетесь зарегистрированным участником? Восстановите свою регистрацию через OTP-верификацию по электронной почте."

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:992
#: app/domain/participant/ParticipantFields.tsx:510
msgid "Room number"
msgstr "Номер комнаты"

#: app/domain/participant/ParticipantFields.tsx:511
msgid "Room number of your stay"
msgstr "Номер вашей комнаты для проживания"

#: app/domain/participant/ParticipantFields.tsx:425
#~ msgid "Room number of your stay at our resort"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:562
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Save"
msgstr "Сохранить"

#: app/domain/participant/ParticipantFields.tsx:737
msgid "select"
msgstr "Выбрать"

#: app/domain/participant/ParticipantFields.tsx:1338
msgid "Select"
msgstr "Выбрать"

#: app/routes/_all._catch.participant_.mutate.tsx:1281
msgid "Select add-ons"
msgstr "Выберите дополнения"

#: app/components/CountrySelectHeadlessUi.tsx:100
#: app/components/CountrySelect.tsx:171
msgid "select country"
msgstr "Выберите страну"

#: app/components/CountrySelectHeadlessUi.tsx:133
#: app/components/CountrySelect.tsx:202
msgid "Select country"
msgstr "Выберите страну"

#: app/components/CountrySelectHeadlessUi.tsx:28
#: app/components/CountrySelect.tsx:41
msgid "Select Country"
msgstr "Выберите страну"

#: app/domain/participant/GenderSelect.tsx:67
msgid "Select gender"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:197
msgid "Select level"
msgstr "Выбрать уровень"

#: app/routes/_all._catch.participant_.mutate.tsx:646
#~ msgid "Select one or more activities"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:222
msgid "Select organisation"
msgstr "Выбрать организацию"

#: app/domain/participant/ParticipantFields.tsx:803
msgid "Select size"
msgstr "Выбрать размер"

#: app/domain/participant/ParticipantFields.tsx:1012
msgid "Shoe size"
msgstr "Размер обуви"

#: app/routes/_all._catch.waiver.$id.tsx:364
msgid "Signature of certified medical doctor or other legally certified medical provider"
msgstr "Подпись сертифицированного врача или другого законно сертифицированного медицинского работника"

#: app/domain/participant/participant.signature.component.tsx:131
#: app/domain/participant/participant.signature.component.tsx:137
msgid "Signature of parent or guardian"
msgstr "Подпись родителя или опекуна"

#: app/domain/participant/participant.signature.component.tsx:78
#: app/domain/participant/participant.signature.component.tsx:84
msgid "Signature participant"
msgstr "Подпись участника"

#: app/domain/participant/ParticipantFields.tsx:488
msgid "Stay"
msgstr "Пребывание"

#: app/domain/participant/ParticipantFields.tsx:386
#~ msgid "Staying with us? Please fill your room number."
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:330
msgid "The above-named person requests your opinion of his/her medical suitability to participate in recreational scuba diving or freediving training or activity. Please visit <0>uhms.org</0> for medical guidance on medical conditions as they relate to diving. Review the areas relevant to your patient as part of your evaluation."
msgstr "Вышеупомянутый человек просит ваше мнение о его/ее медицинской пригодности для участия в рекреационном дайвинге или тренировках по фридайвингу. Пожалуйста, посетите <0>uhms.org</0> для получения медицинских рекомендаций по состояниям, связанным с дайвингом. Просмотрите области, относящиеся к вашему пациенту, в рамках вашей оценки."

#: app/domain/participant/participant.signature.component.tsx:23
msgid "Today's date"
msgstr "Сегодняшняя дата"

#: app/domain/participant/participant-fields.tsx:8
msgid "Vegan"
msgstr "Веган"

#: app/domain/participant/participant-fields.tsx:7
msgid "Vegetarian"
msgstr "Вегетарианец"

#: app/routes/_all._catch._w.waiver_.mutate.tsx:173
#~ msgid "View"
#~ msgstr "View"

#: app/misc/error-translations.ts:15
msgid "Wachtwoord moet tenminste 6 tekens lang zijn."
msgstr "Пароль должен быть не менее 6 символов."

#: app/domain/participant/participant-data.tsx:47
msgid "Walk-in"
msgstr "Без записи"

#: app/domain/participant/ParticipantFields.tsx:910
msgid "Weight"
msgstr "Вес"

#: app/domain/participant/ParticipantFields.tsx:1115
msgid "Weightbelt"
msgstr "Грузовой пояс"

#: app/domain/participant/participant-data.tsx:36
msgid "Wetsuit"
msgstr "Гидрокостюм"

#: app/domain/participant/ParticipantFields.tsx:802
msgid "Wetsuit size"
msgstr "Размер гидрокостюма"

#: app/domain/participant/ParticipantFields.tsx:570
msgid "WhatsApp preferred"
msgstr "Предпочтительно WhatsApp"

#: app/domain/participant/participant-helpers.tsx:10
msgid "Within the last 3 months"
msgstr "В течение последних 3 месяцев"

#: app/domain/participant/participant-helpers.tsx:11
msgid "Within the last 6 months"
msgstr "В течение последних 6 месяцев"

#: app/domain/participant/participant-helpers.tsx:12
msgid "Within the last year"
msgstr "В течение последнего года"

#: app/domain/participant/ParticipantFields.tsx:709
msgid "Years of experience"
msgstr "Годы опыта"

#: app/routes/_all._catch.waiver.$id.tsx:214
#: app/routes/_all._catch.waiver.$id.tsx:265
#: app/domain/participant/ParticipantFields.tsx:1253
msgid "Yes"
msgstr "Да"

#: app/domain/participant/ParticipantFields.tsx:1285
msgid "Your Instagram Handle"
msgstr "Ваш никнейм в Instagram"

#: app/domain/participant/participant-data.tsx:50
msgid "YouTube"
msgstr ""

