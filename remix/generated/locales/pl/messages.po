msgid ""
msgstr ""
"POT-Creation-Date: 2024-03-30 07:31+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: pl\n"
"Project-Id-Version: traveltruster\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-28 13:35\n"
"Last-Translator: \n"
"Language-Team: Polish\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"
"X-Crowdin-Project: traveltruster\n"
"X-Crowdin-Project-ID: 668342\n"
"X-Crowdin-Language: pl\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 2\n"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:603
#~ msgid "{0, plural, one {upload} other {uploads}}"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
#~ msgid "&sl;1y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:8
#~ msgid "&sl;3mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:9
#~ msgid "&sl;6mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
msgid "<1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
#~ msgid "<2y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
msgid "<3mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:11
msgid "<6mo"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:13
msgid ">1y"
msgstr ""

#: app/domain/participant/participant-helpers.tsx:14
msgid ">2y"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:381
msgid "Address"
msgstr "Adres"

#: app/domain/participant/participant-data.tsx:52
msgid "Agency"
msgstr "Agencja"

#: app/routes/_all._catch.waiver.$id.tsx:349
msgid "Approved – I find no conditions that I consider incompatible with recreational scuba diving or freediving."
msgstr "Zatwierdzone – Nie znajduję żadnych warunków, które uważam za niezgodne z rekreacyjnym nurkowaniem lub freedivingiem."

#: app/misc/error-translations.ts:7
msgid "Authentication code was expired"
msgstr "Kod uwierzytelniający wygasł"

#: app/domain/participant/participant-data.tsx:34
msgid "BCD"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:1221
msgid "BCD Size"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:324
msgid "Birthdate"
msgstr "Data urodzenia"

#: app/routes/_all._catch.participant_.mutate.tsx:843
msgid "Booking"
msgstr "Rezerwacja"

#: app/domain/participant/participant-fields.tsx:11
msgid "Bring my own"
msgstr "Przynieś swoje własne"

#: app/domain/participant/ParticipantFields.tsx:694
#~ msgid "Certificate"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:690
msgid "Certificate details"
msgstr "Szczegóły certyfikatu"

#: app/domain/participant/ParticipantFields.tsx:667
msgid "Certificate level"
msgstr "Poziom certyfikatu"

#: app/domain/participant/ParticipantFields.tsx:686
#~ msgid "Certificate number"
#~ msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:996
msgid "Certification"
msgstr "Certyfikacja"

#: app/domain/participant/ParticipantFields.tsx:644
msgid "Certification organisation"
msgstr "Organizacja certyfikująca"

#: app/routes/_all._catch.participant_.mutate.tsx:1416
msgid "Click to read"
msgstr "Kliknij, aby przeczytać"

#: app/routes/_all._catch.waiver.$id.tsx:378
msgid "Clinic/Hospital"
msgstr "Klinika/Szpital"

#: app/routes/_all._catch.waiver.$id.tsx:375
msgid "Clinical Degrees/Credentials"
msgstr "Stopnie/Kwalifikacje kliniczne"

#: app/domain/participant/ParticipantFields.tsx:607
msgid "Company and policy number"
msgstr "Firma i numer polisy"

#: app/routes/_all._catch.participant_.mutate.tsx:1217
#~ msgid "Continue"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Copy"
msgstr "Kopia"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1008
#: app/domain/participant/ParticipantFields.tsx:443
msgid "Country"
msgstr "Kraj"

#: app/routes/_all._catch.waiver.$id.tsx:403
msgid "Created by the <0>Diver Medical Screen Committee</0> in association with the following bodies:"
msgstr "Utworzone przez <0>Komitet ds. Przesiewowych Badań Medycznych Nurków</0> we współpracy z następującymi organami:"

#: app/routes/_all._catch.waiver.$id.tsx:369
msgid "Date (dd/mm/yyyy)"
msgstr "Data (dd/mm/yyyy)"

#: app/domain/participant/ParticipantFields.tsx:516
msgid "Date of birth"
msgstr "Data urodzenia"

#: app/domain/participant/ParticipantFields.tsx:126
msgid "Dive specific details"
msgstr "Szczegóły dotyczące nurkowania"

#: app/routes/_all._catch.waiver.$id.tsx:308
msgid "Diver Medical"
msgstr "Medyczne badanie nurka"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1000
msgid "Dives"
msgstr "Nurkowania"

#: app/domain/participant/ParticipantFields.tsx:606
msgid "Do you have an insurance that covers underwater activities? please fill your details below"
msgstr "Czy masz ubezpieczenie obejmujące aktywności podwodne? Proszę wypełnij swoje dane poniżej"

#: app/domain/participant/ParticipantFields.tsx:641
msgid "e.g. friend, father or mother"
msgstr "np. przyjaciel, ojciec lub matka"

#: app/domain/participant/ParticipantFields.tsx:497
msgid "e.g. Hotel name"
msgstr "np. nazwa hotelu"

#: app/domain/participant/ParticipantFields.tsx:601
msgid "e.g. nut allergy"
msgstr "np. alergia na orzechy"

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Edit"
msgstr "Edytuj"

#: app/routes/_all._catch.waiver.$id.tsx:387
msgid "Email"
msgstr "Email"

#: app/routes/_all._catch.participant_.mutate.tsx:1201
msgid "Email OTP"
msgstr "OTP e-mail"

#: app/domain/participant/ParticipantFields.tsx:118
msgid "Emergency contact details"
msgstr "Dane kontaktowe w nagłych wypadkach"

#: app/domain/participant/ParticipantFields.tsx:611
msgid "Emergency contact name"
msgstr "Imię osoby do kontaktu w nagłych wypadkach"

#: app/domain/participant/ParticipantFields.tsx:615
msgid "Emergency contact phone number"
msgstr "Numer telefonu do kontaktu w nagłych wypadkach"

#: app/components/base/AddressInput.tsx:70
msgid "Enter a location"
msgstr "Wprowadź lokalizację"

#: app/domain/participant/ParticipantFields.tsx:396
#~ msgid "Enter here"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:390
#~ msgid "Enter here or upload below"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:347
msgid "Enter number"
msgstr "Wprowadź numer"

#: app/routes/_all._catch._index.tsx:154
msgid "Explore Bali underwater"
msgstr "Odkrywaj podwodne Bali"

#: app/domain/participant/participant-data.tsx:46
msgid "Facebook"
msgstr "Facebook"

#: app/domain/participant/ParticipantFields.tsx:130
msgid "Feedback"
msgstr "Opinia"

#: app/domain/participant/GenderSelect.tsx:9
msgid "Female"
msgstr ""

#: app/domain/participant/participant-data.tsx:33
msgid "Fins"
msgstr "Płetwy"

#: app/domain/participant/ParticipantFields.tsx:407
msgid "First name"
msgstr "Imię"

#: app/domain/participant/ParticipantFields.tsx:600
msgid "Food allergies"
msgstr "Alergie pokarmowe"

#: app/domain/participant/participant-data.tsx:44
msgid "Friend"
msgstr "Przyjaciel"

#: app/domain/participant/ParticipantFields.tsx:543
msgid "Gender"
msgstr ""

#: app/domain/participant/participant-fields.tsx:9
msgid "Gluten free"
msgstr "Bez glutenu"

#: app/domain/participant/participant-data.tsx:48
msgid "Google"
msgstr "Google"

#: app/domain/participant/participant-data.tsx:49
msgid "Google Maps"
msgstr "Google Mapy"

#: app/domain/participant/ParticipantFields.tsx:808
msgid "Height"
msgstr "Wzrost"

#: app/misc/error-translations.ts:14
msgid "Het email adres is al in gebruik door een ander account."
msgstr "Adres e-mail jest już używany przez inne konto."

#: app/domain/participant/ParticipantFields.tsx:463
msgid "Home/residential address"
msgstr "Adres domowy/zamieszkania"

#: app/domain/participant/participant-data.tsx:51
msgid "Hotel"
msgstr "Hotel"

#: app/domain/participant/ParticipantFields.tsx:385
#~ msgid "Hotel/hostel/homestay"
#~ msgstr ""

#. placeholder {0}: waiver.translation?.name
#: app/routes/_all._catch.participant_.mutate.tsx:1458
msgid "I have read and agree with the above <0>{0}</0>"
msgstr "Przeczytałem i zgadzam się z powyższym <0>{0}</0>"

#: app/routes/_all._catch.participant_.mutate.tsx:800
#~ msgid "I have read and agree with the above <0>{0}</0> and agree"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1290
msgid "If you require any additional addons, select the amount below"
msgstr "Jeśli potrzebujesz dodatkowych dodatków, wybierz kwotę poniżej"

#: app/domain/participant/ParticipantFields.tsx:121
msgid "Important! Ensure the emergency contact is not someone joining the activity."
msgstr "Ważne! Upewnij się, że osoba kontaktowa w nagłych wypadkach nie bierze udziału w aktywności."

#: app/domain/participant/participant-data.tsx:45
msgid "Instagram"
msgstr "Instagram"

#: app/domain/participant/ParticipantFields.tsx:605
msgid "Insurance"
msgstr "Ubezpieczenie"

#: app/misc/error-translations.ts:9
msgid "Invalid credentials"
msgstr "Nieprawidłowe dane logowania"

#: app/misc/error-translations.ts:8
msgid "Invalid email address"
msgstr "Nieprawidłowy adres e-mail"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1004
#: app/domain/participant/ParticipantFields.tsx:712
msgid "Last dive"
msgstr "Ostatnie nurkowanie"

#: app/domain/participant/ParticipantFields.tsx:421
msgid "Last name"
msgstr "Nazwisko"

#: app/domain/participant/GenderSelect.tsx:8
msgid "Male"
msgstr ""

#: app/domain/participant/participant-data.tsx:32
msgid "Mask"
msgstr "Maska"

#: app/domain/participant/ParticipantFields.tsx:1226
msgid "May we ask how you heard about us?"
msgstr "Czy możemy zapytać, skąd się o nas dowiedziałeś/aś?"

#: app/domain/participant/ParticipantFields.tsx:1230
msgid "May we contact you to ask about your experience?"
msgstr "Czy możemy skontaktować się z Tobą, aby zapytać o Twoje doświadczenia?"

#: app/domain/participant/ParticipantFields.tsx:595
msgid "Meal preferences"
msgstr "Preferencje dotyczące posiłków"

#: app/routes/_all._catch.waiver.$id.tsx:312
msgid "Medical Examiner's Evaluation Form"
msgstr "Formularz badania lekarskiego"

#: app/routes/_all._catch.waiver.$id.tsx:372
msgid "Medical Examiner’s Name"
msgstr "Imię i nazwisko lekarza"

#: app/domain/participant/participant-helpers.tsx:13
msgid "More than 1 year ago"
msgstr "Ponad 1 rok temu"

#: app/domain/participant/participant-helpers.tsx:14
msgid "more than 2 years ago"
msgstr "Ponad 2 lata temu"

#: app/misc/error-translations.ts:67
msgid "Name already exists under this establishment"
msgstr "Nazwa już istnieje w tym miejscu"

#: app/domain/participant/participant.signature.component.tsx:97
msgid "Name of parent or guardian"
msgstr "Imię rodzica lub opiekuna"

#: app/domain/participant/participant.signature.component.tsx:57
msgid "Name participant"
msgstr "Imię uczestnika"

#: app/routes/_all._catch.waiver.$id.tsx:231
#: app/routes/_all._catch.waiver.$id.tsx:282
#: app/domain/participant/ParticipantFields.tsx:1267
msgid "No"
msgstr "Nie"

#: app/misc/error-translations.ts:6
msgid "No changes were made"
msgstr "Nie wprowadzono żadnych zmian"

#: app/domain/participant/participant-fields.tsx:10
msgid "No preference"
msgstr "Brak preferencji"

#: app/domain/participant/GenderSelect.tsx:10
msgid "Non-binary"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:355
msgid "Not approved – I find conditions that I consider incompatible with recreational scuba diving or freediving"
msgstr "Niezatwierdzone – znajduję warunki, które uważam za niezgodne z rekreacyjnym nurkowaniem lub freedivingiem"

#: app/domain/participant/ParticipantFields.tsx:704
msgid "Number of dives"
msgstr "Liczba nurkowań"

#: app/domain/participant/ParticipantFields.tsx:436
msgid "Or a valid local ID card"
msgstr "Lub ważny lokalny dowód tożsamości"

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "or an ID that's valid in the operator country"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:804
msgid "Or t-shirt size if you don't know your wetsuit size."
msgstr "Lub rozmiar koszulki, jeśli nie znasz rozmiaru pianki."

#: app/domain/participant/ParticipantFields.tsx:134
msgid "Other info (optional)"
msgstr "Inne informacje (opcjonalnie)"

#: app/domain/participant/ParticipantFields.tsx:749
msgid "Own gear"
msgstr "Własny sprzęt"

#: app/misc/error-translations.ts:10
msgid "Page was not found or you are unauthorized"
msgstr "Strona nie została znaleziona lub nie masz uprawnień"

#: app/domain/participant/ParticipantFields.tsx:114
msgid "Participant details"
msgstr "Dane uczestnika"

#: app/routes/_all._catch.waiver.$id.tsx:318
msgid "Participant name"
msgstr "Imię i nazwisko uczestnika"

#: app/domain/participant/ParticipantFields.tsx:435
msgid "Passport"
msgstr "Paszport"

#: app/domain/participant/ParticipantFields.tsx:303
#~ msgid "Passport No."
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "Passport number"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:384
#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1012
msgid "Phone"
msgstr "Telefon"

#: app/domain/participant/ParticipantFields.tsx:569
msgid "Phone number"
msgstr "Numer telefonu"

#: app/routes/_all._catch.waiver.$id.tsx:396
msgid "Physician/Clinic Stamp (optional)"
msgstr "Pieczątka lekarza/kliniki (opcjonalnie)"

#: app/routes/_all._catch._w.planning.u.tsx:107
msgid "Planning"
msgstr "Planowanie"

#: app/routes/_all._catch.participant_.mutate.tsx:1406
msgid "Please check this box to continue."
msgstr "Proszę zaznaczyć to pole, aby kontynuować."

#: app/domain/participant/ParticipantFields.tsx:1289
msgid "Please fill any additional important info or questions below"
msgstr "Proszę wypełnić wszelkie dodatkowe ważne informacje lub pytania poniżej"

#: app/domain/participant/ParticipantFields.tsx:750
msgid "Please select in case you bring your own gear."
msgstr "Proszę zaznaczyć, jeśli przynosisz własny sprzęt."

#: app/routes/_all._catch.participant_.mutate.tsx:883
msgid "Please select the activity (or activities) you're attending."
msgstr "Proszę wybrać aktywność (lub aktywności), w których bierzesz udział."

#: app/domain/participant/GenderSelect.tsx:11
msgid "Prefer not to say"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:830
msgid "Preferred language"
msgstr "Preferowany język"

#: app/routes/_all._catch.participant_.mutate.tsx:1250
msgid "Recipient email"
msgstr "Adres email odbiorcy"

#: app/routes/_all._catch.participant_.mutate.tsx:1149
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Register"
msgstr "Zarejestruj"

#: app/domain/participant/participant-data.tsx:35
msgid "Regulator"
msgstr "Regulator"

#: app/domain/participant/ParticipantFields.tsx:640
msgid "Relationship"
msgstr "Relacja"

#: app/routes/_all._catch._index.tsx:156
msgid "Research & booking made easier"
msgstr "Badania i rezerwacje łatwiejsze"

#: app/routes/_all._catch.participant_.mutate.tsx:1191
msgid "Returning participant? Retrieve your registration via OTP email verification."
msgstr "Czy jesteś powracającym uczestnikiem? Odzyskaj swoją rejestrację za pomocą weryfikacji OTP przez e-mail."

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:992
#: app/domain/participant/ParticipantFields.tsx:510
msgid "Room number"
msgstr "Numer pokoju"

#: app/domain/participant/ParticipantFields.tsx:511
msgid "Room number of your stay"
msgstr "Numer pokoju, w którym przebywasz"

#: app/domain/participant/ParticipantFields.tsx:425
#~ msgid "Room number of your stay at our resort"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:562
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Save"
msgstr "Zapisz"

#: app/domain/participant/ParticipantFields.tsx:737
msgid "select"
msgstr "Wybierz"

#: app/domain/participant/ParticipantFields.tsx:1338
msgid "Select"
msgstr "Wybierz"

#: app/routes/_all._catch.participant_.mutate.tsx:1281
msgid "Select add-ons"
msgstr "Wybierz dodatki"

#: app/components/CountrySelectHeadlessUi.tsx:100
#: app/components/CountrySelect.tsx:171
msgid "select country"
msgstr "Wybierz kraj"

#: app/components/CountrySelectHeadlessUi.tsx:133
#: app/components/CountrySelect.tsx:202
msgid "Select country"
msgstr "Wybierz kraj"

#: app/components/CountrySelectHeadlessUi.tsx:28
#: app/components/CountrySelect.tsx:41
msgid "Select Country"
msgstr "Wybierz kraj"

#: app/domain/participant/GenderSelect.tsx:67
msgid "Select gender"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:197
msgid "Select level"
msgstr "Wybierz poziom"

#: app/routes/_all._catch.participant_.mutate.tsx:646
#~ msgid "Select one or more activities"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:222
msgid "Select organisation"
msgstr "Wybierz organizację"

#: app/domain/participant/ParticipantFields.tsx:803
msgid "Select size"
msgstr "Wybierz rozmiar"

#: app/domain/participant/ParticipantFields.tsx:1012
msgid "Shoe size"
msgstr "Rozmiar buta"

#: app/routes/_all._catch.waiver.$id.tsx:364
msgid "Signature of certified medical doctor or other legally certified medical provider"
msgstr "Podpis lekarza lub innego prawnie certyfikowanego pracownika medycznego"

#: app/domain/participant/participant.signature.component.tsx:131
#: app/domain/participant/participant.signature.component.tsx:137
msgid "Signature of parent or guardian"
msgstr "Podpis rodzica lub opiekuna"

#: app/domain/participant/participant.signature.component.tsx:78
#: app/domain/participant/participant.signature.component.tsx:84
msgid "Signature participant"
msgstr "Podpis uczestnika"

#: app/domain/participant/ParticipantFields.tsx:488
msgid "Stay"
msgstr "Pobyt"

#: app/domain/participant/ParticipantFields.tsx:386
#~ msgid "Staying with us? Please fill your room number."
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:330
msgid "The above-named person requests your opinion of his/her medical suitability to participate in recreational scuba diving or freediving training or activity. Please visit <0>uhms.org</0> for medical guidance on medical conditions as they relate to diving. Review the areas relevant to your patient as part of your evaluation."
msgstr "Powyżej wymieniona osoba prosi o Twoją opinię na temat jej medycznej zdolności do udziału w rekreacyjnym nurkowaniu lub szkoleniu freedivingowym. Proszę odwiedzić <0>uhms.org</0> po medyczne wskazówki dotyczące dolegliwości medycznych związanych z nurkowaniem. Przejrzyj obszary istotne dla Twojego pacjenta w ramach swojej oceny."

#: app/domain/participant/participant.signature.component.tsx:23
msgid "Today's date"
msgstr "Dzisiejsza data"

#: app/domain/participant/participant-fields.tsx:8
msgid "Vegan"
msgstr "Weganin"

#: app/domain/participant/participant-fields.tsx:7
msgid "Vegetarian"
msgstr "Wegetarianin"

#: app/routes/_all._catch._w.waiver_.mutate.tsx:173
#~ msgid "View"
#~ msgstr "View"

#: app/misc/error-translations.ts:15
msgid "Wachtwoord moet tenminste 6 tekens lang zijn."
msgstr "Hasło musi mieć co najmniej 6 znaków."

#: app/domain/participant/participant-data.tsx:47
msgid "Walk-in"
msgstr "Bez zapowiedzi"

#: app/domain/participant/ParticipantFields.tsx:910
msgid "Weight"
msgstr "Waga"

#: app/domain/participant/ParticipantFields.tsx:1115
msgid "Weightbelt"
msgstr "Pas obciążeniowy"

#: app/domain/participant/participant-data.tsx:36
msgid "Wetsuit"
msgstr "Pianka"

#: app/domain/participant/ParticipantFields.tsx:802
msgid "Wetsuit size"
msgstr "Rozmiar pianki"

#: app/domain/participant/ParticipantFields.tsx:570
msgid "WhatsApp preferred"
msgstr "Preferowane WhatsApp"

#: app/domain/participant/participant-helpers.tsx:10
msgid "Within the last 3 months"
msgstr "W ciągu ostatnich 3 miesięcy"

#: app/domain/participant/participant-helpers.tsx:11
msgid "Within the last 6 months"
msgstr "W ciągu ostatnich 6 miesięcy"

#: app/domain/participant/participant-helpers.tsx:12
msgid "Within the last year"
msgstr "W ciągu ostatniego roku"

#: app/domain/participant/ParticipantFields.tsx:709
msgid "Years of experience"
msgstr "Lata doświadczenia"

#: app/routes/_all._catch.waiver.$id.tsx:214
#: app/routes/_all._catch.waiver.$id.tsx:265
#: app/domain/participant/ParticipantFields.tsx:1253
msgid "Yes"
msgstr "Tak"

#: app/domain/participant/ParticipantFields.tsx:1285
msgid "Your Instagram Handle"
msgstr "Twój handle na Instagramie"

#: app/domain/participant/participant-data.tsx:50
msgid "YouTube"
msgstr ""

