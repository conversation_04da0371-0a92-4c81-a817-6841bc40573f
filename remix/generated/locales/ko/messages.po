msgid ""
msgstr ""
"POT-Creation-Date: 2024-03-30 07:31+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: ko\n"
"Project-Id-Version: traveltruster\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-28 13:35\n"
"Last-Translator: \n"
"Language-Team: Korean\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: traveltruster\n"
"X-Crowdin-Project-ID: 668342\n"
"X-Crowdin-Language: ko\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 2\n"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:603
#~ msgid "{0, plural, one {upload} other {uploads}}"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
#~ msgid "&sl;1y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:8
#~ msgid "&sl;3mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:9
#~ msgid "&sl;6mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
msgid "<1y"
msgstr "<1년"

#: app/domain/participant/participant-helpers.tsx:12
#~ msgid "<2y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
msgid "<3mo"
msgstr "<3개월"

#: app/domain/participant/participant-helpers.tsx:11
msgid "<6mo"
msgstr "<6개월"

#: app/domain/participant/participant-helpers.tsx:13
msgid ">1y"
msgstr ">1년"

#: app/domain/participant/participant-helpers.tsx:14
msgid ">2y"
msgstr ">2년"

#: app/routes/_all._catch.waiver.$id.tsx:381
msgid "Address"
msgstr "주소"

#: app/domain/participant/participant-data.tsx:52
msgid "Agency"
msgstr "에이전시"

#: app/routes/_all._catch.waiver.$id.tsx:349
msgid "Approved – I find no conditions that I consider incompatible with recreational scuba diving or freediving."
msgstr "승인됨 – 레크레이셔널 스쿠버 다이빙 또는 프리다이빙에 부적합하다고 판단되는 소견이 없습니다."

#: app/misc/error-translations.ts:7
msgid "Authentication code was expired"
msgstr "인증 코드가 만료 되었습니다"

#: app/domain/participant/participant-data.tsx:34
msgid "BCD"
msgstr "비씨디 (BCD)"

#: app/domain/participant/ParticipantFields.tsx:1221
msgid "BCD Size"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:324
msgid "Birthdate"
msgstr "생년월일"

#: app/routes/_all._catch.participant_.mutate.tsx:843
msgid "Booking"
msgstr "예약"

#: app/domain/participant/participant-fields.tsx:11
msgid "Bring my own"
msgstr "개인장비"

#: app/domain/participant/ParticipantFields.tsx:694
#~ msgid "Certificate"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:690
msgid "Certificate details"
msgstr "자격증 세부사항"

#: app/domain/participant/ParticipantFields.tsx:667
msgid "Certificate level"
msgstr "자격증 레벨"

#: app/domain/participant/ParticipantFields.tsx:686
#~ msgid "Certificate number"
#~ msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:996
msgid "Certification"
msgstr "자격증"

#: app/domain/participant/ParticipantFields.tsx:644
msgid "Certification organisation"
msgstr "인증 기관"

#: app/routes/_all._catch.participant_.mutate.tsx:1416
msgid "Click to read"
msgstr "더 자세히 읽기"

#: app/routes/_all._catch.waiver.$id.tsx:378
msgid "Clinic/Hospital"
msgstr "클리닉/병원"

#: app/routes/_all._catch.waiver.$id.tsx:375
msgid "Clinical Degrees/Credentials"
msgstr "임상 학위/등급"

#: app/domain/participant/ParticipantFields.tsx:607
msgid "Company and policy number"
msgstr "회사 및 보험 번호"

#: app/routes/_all._catch.participant_.mutate.tsx:1217
#~ msgid "Continue"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Copy"
msgstr "복사"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1008
#: app/domain/participant/ParticipantFields.tsx:443
msgid "Country"
msgstr "국가"

#: app/routes/_all._catch.waiver.$id.tsx:403
msgid "Created by the <0>Diver Medical Screen Committee</0> in association with the following bodies:"
msgstr "제작: <0>다이버 메디컬 스크린 위원회</0>\n"
"제작 협력:"

#: app/routes/_all._catch.waiver.$id.tsx:369
msgid "Date (dd/mm/yyyy)"
msgstr "날짜 (일/월/년)"

#: app/domain/participant/ParticipantFields.tsx:516
msgid "Date of birth"
msgstr "생년월일"

#: app/domain/participant/ParticipantFields.tsx:126
msgid "Dive specific details"
msgstr "다이버 세부 사항"

#: app/routes/_all._catch.waiver.$id.tsx:308
msgid "Diver Medical"
msgstr "다이버 메디컬 (Diver Medical)"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1000
msgid "Dives"
msgstr "다이빙"

#: app/domain/participant/ParticipantFields.tsx:606
msgid "Do you have an insurance that covers underwater activities? please fill your details below"
msgstr "수중 활동을 보장하는 보험에 가입하셨나요? 아래 세부 정보를 입력해주세요."

#: app/domain/participant/ParticipantFields.tsx:641
msgid "e.g. friend, father or mother"
msgstr "예: 친구, 아버지, 어머니"

#: app/domain/participant/ParticipantFields.tsx:497
msgid "e.g. Hotel name"
msgstr "예시: 호텔 이름"

#: app/domain/participant/ParticipantFields.tsx:601
msgid "e.g. nut allergy"
msgstr "예시: 견과류 알레르기"

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Edit"
msgstr "편집"

#: app/routes/_all._catch.waiver.$id.tsx:387
msgid "Email"
msgstr "이메일"

#: app/routes/_all._catch.participant_.mutate.tsx:1201
msgid "Email OTP"
msgstr "이메일 OTP"

#: app/domain/participant/ParticipantFields.tsx:118
msgid "Emergency contact details"
msgstr "긴급 연락처 정보 "

#: app/domain/participant/ParticipantFields.tsx:611
msgid "Emergency contact name"
msgstr "긴급 연락처 이름"

#: app/domain/participant/ParticipantFields.tsx:615
msgid "Emergency contact phone number"
msgstr "긴급 연락처 전화번호"

#: app/components/base/AddressInput.tsx:70
msgid "Enter a location"
msgstr "주소 입력"

#: app/domain/participant/ParticipantFields.tsx:396
#~ msgid "Enter here"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:390
#~ msgid "Enter here or upload below"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:347
msgid "Enter number"
msgstr "자격증 번호"

#: app/routes/_all._catch._index.tsx:154
msgid "Explore Bali underwater"
msgstr "발리 수중 탐험"

#: app/domain/participant/participant-data.tsx:46
msgid "Facebook"
msgstr "페이스북"

#: app/domain/participant/ParticipantFields.tsx:130
msgid "Feedback"
msgstr "피드백"

#: app/domain/participant/GenderSelect.tsx:9
msgid "Female"
msgstr ""

#: app/domain/participant/participant-data.tsx:33
msgid "Fins"
msgstr "핀"

#: app/domain/participant/ParticipantFields.tsx:407
msgid "First name"
msgstr "이름"

#: app/domain/participant/ParticipantFields.tsx:600
msgid "Food allergies"
msgstr "음식 알레르기"

#: app/domain/participant/participant-data.tsx:44
msgid "Friend"
msgstr "친구"

#: app/domain/participant/ParticipantFields.tsx:543
msgid "Gender"
msgstr ""

#: app/domain/participant/participant-fields.tsx:9
msgid "Gluten free"
msgstr "글루텐 프리"

#: app/domain/participant/participant-data.tsx:48
msgid "Google"
msgstr "구글"

#: app/domain/participant/participant-data.tsx:49
msgid "Google Maps"
msgstr "구글 지도"

#: app/domain/participant/ParticipantFields.tsx:808
msgid "Height"
msgstr "키"

#: app/misc/error-translations.ts:14
msgid "Het email adres is al in gebruik door een ander account."
msgstr "해당 이메일 주소는 다른 계정에서 이미 사용 중입니다"

#: app/domain/participant/ParticipantFields.tsx:463
msgid "Home/residential address"
msgstr "집 주소/거주지 주소"

#: app/domain/participant/participant-data.tsx:51
msgid "Hotel"
msgstr "호텔"

#: app/domain/participant/ParticipantFields.tsx:385
#~ msgid "Hotel/hostel/homestay"
#~ msgstr ""

#. placeholder {0}: waiver.translation?.name
#: app/routes/_all._catch.participant_.mutate.tsx:1458
msgid "I have read and agree with the above <0>{0}</0>"
msgstr "위 내용을 읽고 동의합니다 <0>{0}</0>"

#: app/routes/_all._catch.participant_.mutate.tsx:800
#~ msgid "I have read and agree with the above <0>{0}</0> and agree"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1290
msgid "If you require any additional addons, select the amount below"
msgstr "추가 기능이 필요한 경우 아래 수량을 선택하세요"

#: app/domain/participant/ParticipantFields.tsx:121
msgid "Important! Ensure the emergency contact is not someone joining the activity."
msgstr "중요! 비상 연락처는 이 활동에 참여하는 사람이 아닌지 확인하세요."

#: app/domain/participant/participant-data.tsx:45
msgid "Instagram"
msgstr "인스타그램"

#: app/domain/participant/ParticipantFields.tsx:605
msgid "Insurance"
msgstr "보험"

#: app/misc/error-translations.ts:9
msgid "Invalid credentials"
msgstr "로그인 정보가 틀립니다"

#: app/misc/error-translations.ts:8
msgid "Invalid email address"
msgstr "이메일 주소가 틀립니다"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1004
#: app/domain/participant/ParticipantFields.tsx:712
msgid "Last dive"
msgstr "마지막 다이빙"

#: app/domain/participant/ParticipantFields.tsx:421
msgid "Last name"
msgstr "성"

#: app/domain/participant/GenderSelect.tsx:8
msgid "Male"
msgstr ""

#: app/domain/participant/participant-data.tsx:32
msgid "Mask"
msgstr "마스크"

#: app/domain/participant/ParticipantFields.tsx:1226
msgid "May we ask how you heard about us?"
msgstr "저희를 어떻게 알게 되었는지 여쭈어도 될까요?"

#: app/domain/participant/ParticipantFields.tsx:1230
msgid "May we contact you to ask about your experience?"
msgstr "귀하의 경험에 대해 문의드려도 될까요?"

#: app/domain/participant/ParticipantFields.tsx:595
msgid "Meal preferences"
msgstr "식사 선호도"

#: app/routes/_all._catch.waiver.$id.tsx:312
msgid "Medical Examiner's Evaluation Form"
msgstr "의사 평가 양식 (Medical Examiner‘s Evaluation Form)"

#: app/routes/_all._catch.waiver.$id.tsx:372
msgid "Medical Examiner’s Name"
msgstr "검진한 의사의 이름"

#: app/domain/participant/participant-helpers.tsx:13
msgid "More than 1 year ago"
msgstr "1년 이상 경과"

#: app/domain/participant/participant-helpers.tsx:14
msgid "more than 2 years ago"
msgstr "2년 이상 경과"

#: app/misc/error-translations.ts:67
msgid "Name already exists under this establishment"
msgstr "해당 업체에 이미 동일한 이름이 있습니다"

#: app/domain/participant/participant.signature.component.tsx:97
msgid "Name of parent or guardian"
msgstr "부모님 또는 보호자의 이름"

#: app/domain/participant/participant.signature.component.tsx:57
msgid "Name participant"
msgstr "참가자 이름"

#: app/routes/_all._catch.waiver.$id.tsx:231
#: app/routes/_all._catch.waiver.$id.tsx:282
#: app/domain/participant/ParticipantFields.tsx:1267
msgid "No"
msgstr "아니오"

#: app/misc/error-translations.ts:6
msgid "No changes were made"
msgstr "변경사항이 없습니다"

#: app/domain/participant/participant-fields.tsx:10
msgid "No preference"
msgstr "없음"

#: app/domain/participant/GenderSelect.tsx:10
msgid "Non-binary"
msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:355
msgid "Not approved – I find conditions that I consider incompatible with recreational scuba diving or freediving"
msgstr "승인되지 않음 – 레크레이셔널 스쿠버 다이빙 또는 프리다이빙에 부적합하다고 판단되는 소견이 있습니다."

#: app/domain/participant/ParticipantFields.tsx:704
msgid "Number of dives"
msgstr "다이빙 횟수"

#: app/domain/participant/ParticipantFields.tsx:436
msgid "Or a valid local ID card"
msgstr "또는 유효한 신분증"

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "or an ID that's valid in the operator country"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:804
msgid "Or t-shirt size if you don't know your wetsuit size."
msgstr "또는 티셔츠 사이즈, 웻슈트 사이즈를 모르는 경우"

#: app/domain/participant/ParticipantFields.tsx:134
msgid "Other info (optional)"
msgstr "기타 정보 (선택 사항)"

#: app/domain/participant/ParticipantFields.tsx:749
msgid "Own gear"
msgstr "개인 장비"

#: app/misc/error-translations.ts:10
msgid "Page was not found or you are unauthorized"
msgstr "페이지를 찾을 수 없거나 권한이 없습니다"

#: app/domain/participant/ParticipantFields.tsx:114
msgid "Participant details"
msgstr "참가자 세부 사항"

#: app/routes/_all._catch.waiver.$id.tsx:318
msgid "Participant name"
msgstr "참가자 이름"

#: app/domain/participant/ParticipantFields.tsx:435
msgid "Passport"
msgstr "여권"

#: app/domain/participant/ParticipantFields.tsx:303
#~ msgid "Passport No."
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "Passport number"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:384
#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1012
msgid "Phone"
msgstr "전화"

#: app/domain/participant/ParticipantFields.tsx:569
msgid "Phone number"
msgstr "전화번호"

#: app/routes/_all._catch.waiver.$id.tsx:396
msgid "Physician/Clinic Stamp (optional)"
msgstr "의사/클리닉 스탬프 (선택 사항)"

#: app/routes/_all._catch._w.planning.u.tsx:107
msgid "Planning"
msgstr "계획"

#: app/routes/_all._catch.participant_.mutate.tsx:1406
msgid "Please check this box to continue."
msgstr "계속하려면 이 확인란을 선택하세요"

#: app/domain/participant/ParticipantFields.tsx:1289
msgid "Please fill any additional important info or questions below"
msgstr "추가로 중요한 정보나 질문이 있으면 아래에 기입해 주세요"

#: app/domain/participant/ParticipantFields.tsx:750
msgid "Please select in case you bring your own gear."
msgstr "장비를 가져올 경우 선택해주세요"

#: app/routes/_all._catch.participant_.mutate.tsx:883
msgid "Please select the activity (or activities) you're attending."
msgstr "참여할 활동(또는 활동들)을 선택해 주세요."

#: app/domain/participant/GenderSelect.tsx:11
msgid "Prefer not to say"
msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:830
msgid "Preferred language"
msgstr "선호하는 언어"

#: app/routes/_all._catch.participant_.mutate.tsx:1250
msgid "Recipient email"
msgstr "수신인 이메일"

#: app/routes/_all._catch.participant_.mutate.tsx:1149
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Register"
msgstr "등록하기"

#: app/domain/participant/participant-data.tsx:35
msgid "Regulator"
msgstr "레귤레이터"

#: app/domain/participant/ParticipantFields.tsx:640
msgid "Relationship"
msgstr "관계"

#: app/routes/_all._catch._index.tsx:156
msgid "Research & booking made easier"
msgstr "검색 및 예약이 쉬웠습니다"

#: app/routes/_all._catch.participant_.mutate.tsx:1191
msgid "Returning participant? Retrieve your registration via OTP email verification."
msgstr "이미 등록하셨나요? OTP 이메일 인증을 통해 등록 정보를 검색하세요."

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:992
#: app/domain/participant/ParticipantFields.tsx:510
msgid "Room number"
msgstr "방 번호"

#: app/domain/participant/ParticipantFields.tsx:511
msgid "Room number of your stay"
msgstr "여행 중 머무는 숙소의 방 번호."

#: app/domain/participant/ParticipantFields.tsx:425
#~ msgid "Room number of your stay at our resort"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:562
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Save"
msgstr "저장"

#: app/domain/participant/ParticipantFields.tsx:737
msgid "select"
msgstr "선택하기"

#: app/domain/participant/ParticipantFields.tsx:1338
msgid "Select"
msgstr "선택하기"

#: app/routes/_all._catch.participant_.mutate.tsx:1281
msgid "Select add-ons"
msgstr "추가 아이템 선택"

#: app/components/CountrySelectHeadlessUi.tsx:100
#: app/components/CountrySelect.tsx:171
msgid "select country"
msgstr "국가 선택"

#: app/components/CountrySelectHeadlessUi.tsx:133
#: app/components/CountrySelect.tsx:202
msgid "Select country"
msgstr "국가 선택"

#: app/components/CountrySelectHeadlessUi.tsx:28
#: app/components/CountrySelect.tsx:41
msgid "Select Country"
msgstr "국가 선택"

#: app/domain/participant/GenderSelect.tsx:67
msgid "Select gender"
msgstr ""

#: app/domain/participant/ParticipantFields.tsx:197
msgid "Select level"
msgstr "레벨 선택"

#: app/routes/_all._catch.participant_.mutate.tsx:646
#~ msgid "Select one or more activities"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:222
msgid "Select organisation"
msgstr "기관 선택"

#: app/domain/participant/ParticipantFields.tsx:803
msgid "Select size"
msgstr "사이즈 선택"

#: app/domain/participant/ParticipantFields.tsx:1012
msgid "Shoe size"
msgstr "신발 사이즈"

#: app/routes/_all._catch.waiver.$id.tsx:364
msgid "Signature of certified medical doctor or other legally certified medical provider"
msgstr "인증된 의사 또는 기타 법적으로 인증된 의료 제공자의 서명"

#: app/domain/participant/participant.signature.component.tsx:131
#: app/domain/participant/participant.signature.component.tsx:137
msgid "Signature of parent or guardian"
msgstr "부모 또는 보호자의 서명"

#: app/domain/participant/participant.signature.component.tsx:78
#: app/domain/participant/participant.signature.component.tsx:84
msgid "Signature participant"
msgstr "참가자 서명"

#: app/domain/participant/ParticipantFields.tsx:488
msgid "Stay"
msgstr "숙박"

#: app/domain/participant/ParticipantFields.tsx:386
#~ msgid "Staying with us? Please fill your room number."
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:330
msgid "The above-named person requests your opinion of his/her medical suitability to participate in recreational scuba diving or freediving training or activity. Please visit <0>uhms.org</0> for medical guidance on medical conditions as they relate to diving. Review the areas relevant to your patient as part of your evaluation."
msgstr "위의 사람이 레크레이셔널 스쿠버 다이빙 또는 프리다이빙 트레이닝 또는 활동에 참가하기 위해, 의학적 적합성에 대해 의사인 귀하\n"
"의견을 요청 드립니다. 다이빙과 관련된 의학적 상태에 대한 가이드, <0>uhms.org</0> 를 방문하시길 부탁 드립니다. 평가의 일부로써 환자\n"
"와관련된 영역을 참고하시기 바랍니다."

#: app/domain/participant/participant.signature.component.tsx:23
msgid "Today's date"
msgstr "금일 날짜"

#: app/domain/participant/participant-fields.tsx:8
msgid "Vegan"
msgstr "비건"

#: app/domain/participant/participant-fields.tsx:7
msgid "Vegetarian"
msgstr "채식주의"

#: app/routes/_all._catch._w.waiver_.mutate.tsx:173
#~ msgid "View"
#~ msgstr "View"

#: app/misc/error-translations.ts:15
msgid "Wachtwoord moet tenminste 6 tekens lang zijn."
msgstr "비밀번호는 최소 6자 이상이어야 합니다"

#: app/domain/participant/participant-data.tsx:47
msgid "Walk-in"
msgstr "방문"

#: app/domain/participant/ParticipantFields.tsx:910
msgid "Weight"
msgstr "몸무게"

#: app/domain/participant/ParticipantFields.tsx:1115
msgid "Weightbelt"
msgstr "웨이트 벨트"

#: app/domain/participant/participant-data.tsx:36
msgid "Wetsuit"
msgstr "웻 슈트"

#: app/domain/participant/ParticipantFields.tsx:802
msgid "Wetsuit size"
msgstr "웻 슈트 사이즈"

#: app/domain/participant/ParticipantFields.tsx:570
msgid "WhatsApp preferred"
msgstr "왓츠앱 선호"

#: app/domain/participant/participant-helpers.tsx:10
msgid "Within the last 3 months"
msgstr "최근 3개월 이내"

#: app/domain/participant/participant-helpers.tsx:11
msgid "Within the last 6 months"
msgstr "최근 6개월 이내"

#: app/domain/participant/participant-helpers.tsx:12
msgid "Within the last year"
msgstr "작년 이내에"

#: app/domain/participant/ParticipantFields.tsx:709
msgid "Years of experience"
msgstr "경력 년수"

#: app/routes/_all._catch.waiver.$id.tsx:214
#: app/routes/_all._catch.waiver.$id.tsx:265
#: app/domain/participant/ParticipantFields.tsx:1253
msgid "Yes"
msgstr "예"

#: app/domain/participant/ParticipantFields.tsx:1285
msgid "Your Instagram Handle"
msgstr "인스타그램 아이디"

#: app/domain/participant/participant-data.tsx:50
msgid "YouTube"
msgstr "유튜브"

