msgid ""
msgstr ""
"POT-Creation-Date: 2024-03-30 07:31+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: de\n"
"Project-Id-Version: traveltruster\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-28 13:35\n"
"Last-Translator: \n"
"Language-Team: German\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: traveltruster\n"
"X-Crowdin-Project-ID: 668342\n"
"X-Crowdin-Language: de\n"
"X-Crowdin-File: messages.po\n"
"X-Crowdin-File-ID: 2\n"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:603
#~ msgid "{0, plural, one {upload} other {uploads}}"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
#~ msgid "&sl;1y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:8
#~ msgid "&sl;3mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:9
#~ msgid "&sl;6mo"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:12
msgid "<1y"
msgstr "<1J"

#: app/domain/participant/participant-helpers.tsx:12
#~ msgid "<2y"
#~ msgstr ""

#: app/domain/participant/participant-helpers.tsx:10
msgid "<3mo"
msgstr "<3M"

#: app/domain/participant/participant-helpers.tsx:11
msgid "<6mo"
msgstr "<6M"

#: app/domain/participant/participant-helpers.tsx:13
msgid ">1y"
msgstr ">1J"

#: app/domain/participant/participant-helpers.tsx:14
msgid ">2y"
msgstr ">2J"

#: app/routes/_all._catch.waiver.$id.tsx:381
msgid "Address"
msgstr "Adresse"

#: app/domain/participant/participant-data.tsx:52
msgid "Agency"
msgstr "Agentur"

#: app/routes/_all._catch.waiver.$id.tsx:349
msgid "Approved – I find no conditions that I consider incompatible with recreational scuba diving or freediving."
msgstr "Zugelassen – Ich erkenne keine Bedingungen, die ich mit dem Sporttauchen oder dem Freitauchen für unvereinbar halte."

#: app/misc/error-translations.ts:7
msgid "Authentication code was expired"
msgstr "Der Authentifizierungscode ist abgelaufen"

#: app/domain/participant/participant-data.tsx:34
msgid "BCD"
msgstr "Tarierjacket"

#: app/domain/participant/ParticipantFields.tsx:1221
msgid "BCD Size"
msgstr "Größe Tarierjacket"

#: app/routes/_all._catch.waiver.$id.tsx:324
msgid "Birthdate"
msgstr "Geburtsdatum"

#: app/routes/_all._catch.participant_.mutate.tsx:843
msgid "Booking"
msgstr "Buchung"

#: app/domain/participant/participant-fields.tsx:11
msgid "Bring my own"
msgstr "Eigenes mitbringen"

#: app/domain/participant/ParticipantFields.tsx:694
#~ msgid "Certificate"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:690
msgid "Certificate details"
msgstr "Zertifikatsdetails"

#: app/domain/participant/ParticipantFields.tsx:667
msgid "Certificate level"
msgstr "Zertifizierungsstufe"

#: app/domain/participant/ParticipantFields.tsx:686
#~ msgid "Certificate number"
#~ msgstr ""

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:996
msgid "Certification"
msgstr "Zertifizierung"

#: app/domain/participant/ParticipantFields.tsx:644
msgid "Certification organisation"
msgstr "Zertifizierungsorganisation"

#: app/routes/_all._catch.participant_.mutate.tsx:1416
msgid "Click to read"
msgstr "Zum Lesen klicken"

#: app/routes/_all._catch.waiver.$id.tsx:378
msgid "Clinic/Hospital"
msgstr "Klinik/Krankenhaus"

#: app/routes/_all._catch.waiver.$id.tsx:375
msgid "Clinical Degrees/Credentials"
msgstr "Akademischer Grad/medizinische Zulassung"

#: app/domain/participant/ParticipantFields.tsx:607
msgid "Company and policy number"
msgstr "Firma und Policennummer"

#: app/routes/_all._catch.participant_.mutate.tsx:1217
#~ msgid "Continue"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Copy"
msgstr "Kopieren"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1008
#: app/domain/participant/ParticipantFields.tsx:443
msgid "Country"
msgstr "Land"

#: app/routes/_all._catch.waiver.$id.tsx:403
msgid "Created by the <0>Diver Medical Screen Committee</0> in association with the following bodies:"
msgstr "Erstellt vom <0>Diver Medical Screen Committee</0> in Zusammenarbeit mit den folgenden Organisationen:"

#: app/routes/_all._catch.waiver.$id.tsx:369
msgid "Date (dd/mm/yyyy)"
msgstr "Datum (TT/MM/JJJJ)"

#: app/domain/participant/ParticipantFields.tsx:516
msgid "Date of birth"
msgstr "Geburtsdatum"

#: app/domain/participant/ParticipantFields.tsx:126
msgid "Dive specific details"
msgstr "Tauchspezifische Details"

#: app/routes/_all._catch.waiver.$id.tsx:308
msgid "Diver Medical"
msgstr "Taucherärztliche Untersuchung"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1000
msgid "Dives"
msgstr "Tauchgänge"

#: app/domain/participant/ParticipantFields.tsx:606
msgid "Do you have an insurance that covers underwater activities? please fill your details below"
msgstr "Haben Sie eine Versicherung, die Unterwasseraktivitäten abdeckt? Bitte füllen Sie Ihre Daten unten aus"

#: app/domain/participant/ParticipantFields.tsx:641
msgid "e.g. friend, father or mother"
msgstr "z.B. Freund, Vater oder Mutter"

#: app/domain/participant/ParticipantFields.tsx:497
msgid "e.g. Hotel name"
msgstr "z.B. Hotelname"

#: app/domain/participant/ParticipantFields.tsx:601
msgid "e.g. nut allergy"
msgstr "z.B. Nussallergie"

#: app/routes/_all._catch.participant_.mutate.tsx:1142
msgid "Edit"
msgstr "Bearbeiten"

#: app/routes/_all._catch.waiver.$id.tsx:387
msgid "Email"
msgstr "E-Mail"

#: app/routes/_all._catch.participant_.mutate.tsx:1201
msgid "Email OTP"
msgstr "E-Mail-OTP"

#: app/domain/participant/ParticipantFields.tsx:118
msgid "Emergency contact details"
msgstr "Notfallkontaktdaten"

#: app/domain/participant/ParticipantFields.tsx:611
msgid "Emergency contact name"
msgstr "Notfallkontakt Name"

#: app/domain/participant/ParticipantFields.tsx:615
msgid "Emergency contact phone number"
msgstr "Notfallkontakt Telefonnummer"

#: app/components/base/AddressInput.tsx:70
msgid "Enter a location"
msgstr "Ort eingeben"

#: app/domain/participant/ParticipantFields.tsx:396
#~ msgid "Enter here"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:390
#~ msgid "Enter here or upload below"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:347
msgid "Enter number"
msgstr "Nummer eingeben"

#: app/routes/_all._catch._index.tsx:154
msgid "Explore Bali underwater"
msgstr "Entdecke Bali unter Wasser"

#: app/domain/participant/participant-data.tsx:46
msgid "Facebook"
msgstr "Facebook"

#: app/domain/participant/ParticipantFields.tsx:130
msgid "Feedback"
msgstr "Feedback"

#: app/domain/participant/GenderSelect.tsx:9
msgid "Female"
msgstr "Weiblich"

#: app/domain/participant/participant-data.tsx:33
msgid "Fins"
msgstr "Flossen"

#: app/domain/participant/ParticipantFields.tsx:407
msgid "First name"
msgstr "Vorname"

#: app/domain/participant/ParticipantFields.tsx:600
msgid "Food allergies"
msgstr "Nahrungsmittelallergien"

#: app/domain/participant/participant-data.tsx:44
msgid "Friend"
msgstr "Freund"

#: app/domain/participant/ParticipantFields.tsx:543
msgid "Gender"
msgstr "Geschlecht"

#: app/domain/participant/participant-fields.tsx:9
msgid "Gluten free"
msgstr "Glutenfrei"

#: app/domain/participant/participant-data.tsx:48
msgid "Google"
msgstr "Google"

#: app/domain/participant/participant-data.tsx:49
msgid "Google Maps"
msgstr "Google Maps\n"

#: app/domain/participant/ParticipantFields.tsx:808
msgid "Height"
msgstr "Größe"

#: app/misc/error-translations.ts:14
msgid "Het email adres is al in gebruik door een ander account."
msgstr "Die E-Mail-Adresse wird bereits von einem anderen Konto verwendet."

#: app/domain/participant/ParticipantFields.tsx:463
msgid "Home/residential address"
msgstr "Wohnadresse"

#: app/domain/participant/participant-data.tsx:51
msgid "Hotel"
msgstr "Hotel"

#: app/domain/participant/ParticipantFields.tsx:385
#~ msgid "Hotel/hostel/homestay"
#~ msgstr ""

#. placeholder {0}: waiver.translation?.name
#: app/routes/_all._catch.participant_.mutate.tsx:1458
msgid "I have read and agree with the above <0>{0}</0>"
msgstr "Ich habe die obigen <0>{0}</0> gelesen und stimme ihnen zu"

#: app/routes/_all._catch.participant_.mutate.tsx:800
#~ msgid "I have read and agree with the above <0>{0}</0> and agree"
#~ msgstr ""

#: app/routes/_all._catch.participant_.mutate.tsx:1290
msgid "If you require any additional addons, select the amount below"
msgstr "Wenn Sie zusätzliche Add-Ons benötigen, wählen Sie den Betrag unten aus"

#: app/domain/participant/ParticipantFields.tsx:121
msgid "Important! Ensure the emergency contact is not someone joining the activity."
msgstr "Wichtig! Stellen Sie sicher, dass der Notfallkontakt nicht jemand ist, der an der Aktivität teilnimmt."

#: app/domain/participant/participant-data.tsx:45
msgid "Instagram"
msgstr "Instagram"

#: app/domain/participant/ParticipantFields.tsx:605
msgid "Insurance"
msgstr "Versicherung"

#: app/misc/error-translations.ts:9
msgid "Invalid credentials"
msgstr "Ungültige Anmeldedaten"

#: app/misc/error-translations.ts:8
msgid "Invalid email address"
msgstr "Ungültige E-Mail-Adresse"

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1004
#: app/domain/participant/ParticipantFields.tsx:712
msgid "Last dive"
msgstr "Letzter Tauchgang"

#: app/domain/participant/ParticipantFields.tsx:421
msgid "Last name"
msgstr "Nachname"

#: app/domain/participant/GenderSelect.tsx:8
msgid "Male"
msgstr "Männlich"

#: app/domain/participant/participant-data.tsx:32
msgid "Mask"
msgstr "Maske"

#: app/domain/participant/ParticipantFields.tsx:1226
msgid "May we ask how you heard about us?"
msgstr "Dürfen wir fragen, wie Sie von uns gehört haben?"

#: app/domain/participant/ParticipantFields.tsx:1230
msgid "May we contact you to ask about your experience?"
msgstr "Dürfen wir Sie kontaktieren, um nach Ihren Erfahrungen zu fragen?"

#: app/domain/participant/ParticipantFields.tsx:595
msgid "Meal preferences"
msgstr "Essenspräferenzen"

#: app/routes/_all._catch.waiver.$id.tsx:312
msgid "Medical Examiner's Evaluation Form"
msgstr "Formular ärztliche Beurteilung"

#: app/routes/_all._catch.waiver.$id.tsx:372
msgid "Medical Examiner’s Name"
msgstr "Name des medizinischen Untersuchers"

#: app/domain/participant/participant-helpers.tsx:13
msgid "More than 1 year ago"
msgstr "Vor mehr als 1 Jahr"

#: app/domain/participant/participant-helpers.tsx:14
msgid "more than 2 years ago"
msgstr "vor mehr als 2 Jahren"

#: app/misc/error-translations.ts:67
msgid "Name already exists under this establishment"
msgstr "Name existiert bereits unter dieser Einrichtung"

#: app/domain/participant/participant.signature.component.tsx:97
msgid "Name of parent or guardian"
msgstr "Name des Elternteils oder Erziehungsberechtigten"

#: app/domain/participant/participant.signature.component.tsx:57
msgid "Name participant"
msgstr "Name des Teilnehmers"

#: app/routes/_all._catch.waiver.$id.tsx:231
#: app/routes/_all._catch.waiver.$id.tsx:282
#: app/domain/participant/ParticipantFields.tsx:1267
msgid "No"
msgstr "Nein"

#: app/misc/error-translations.ts:6
msgid "No changes were made"
msgstr "Es wurden keine Änderungen vorgenommen"

#: app/domain/participant/participant-fields.tsx:10
msgid "No preference"
msgstr "Keine Präferenz"

#: app/domain/participant/GenderSelect.tsx:10
msgid "Non-binary"
msgstr "Nicht-binär"

#: app/routes/_all._catch.waiver.$id.tsx:355
msgid "Not approved – I find conditions that I consider incompatible with recreational scuba diving or freediving"
msgstr "Nicht genehmigt – Ich finde Bedingungen, die ich für unvereinbar mit Freizeit-Sporttauchen oder Freitauchen halte"

#: app/domain/participant/ParticipantFields.tsx:704
msgid "Number of dives"
msgstr "Anzahl der Tauchgänge"

#: app/domain/participant/ParticipantFields.tsx:436
msgid "Or a valid local ID card"
msgstr "Oder ein gültiger lokaler Ausweis"

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "or an ID that's valid in the operator country"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:804
msgid "Or t-shirt size if you don't know your wetsuit size."
msgstr "Oder T-Shirt-Größe, wenn Sie Ihre Neoprenanzuggröße nicht kennen."

#: app/domain/participant/ParticipantFields.tsx:134
msgid "Other info (optional)"
msgstr "Weitere Informationen (optional)"

#: app/domain/participant/ParticipantFields.tsx:749
msgid "Own gear"
msgstr "Eigene Ausrüstung"

#: app/misc/error-translations.ts:10
msgid "Page was not found or you are unauthorized"
msgstr "Seite nicht gefunden oder Sie sind nicht autorisiert"

#: app/domain/participant/ParticipantFields.tsx:114
msgid "Participant details"
msgstr "Teilnehmerdetails"

#: app/routes/_all._catch.waiver.$id.tsx:318
msgid "Participant name"
msgstr "Name des Teilnehmers"

#: app/domain/participant/ParticipantFields.tsx:435
msgid "Passport"
msgstr "Reisepass"

#: app/domain/participant/ParticipantFields.tsx:303
#~ msgid "Passport No."
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:356
#~ msgid "Passport number"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:384
#: app/routes/_all._catch.participant_.$participant_id._index.tsx:1012
msgid "Phone"
msgstr "Telefon"

#: app/domain/participant/ParticipantFields.tsx:569
msgid "Phone number"
msgstr "Telefonnummer"

#: app/routes/_all._catch.waiver.$id.tsx:396
msgid "Physician/Clinic Stamp (optional)"
msgstr "Arzt-/Klinikstempel (optional)"

#: app/routes/_all._catch._w.planning.u.tsx:107
msgid "Planning"
msgstr "Planung"

#: app/routes/_all._catch.participant_.mutate.tsx:1406
msgid "Please check this box to continue."
msgstr "Bitte dieses Kästchen ankreuzen, um fortzufahren."

#: app/domain/participant/ParticipantFields.tsx:1289
msgid "Please fill any additional important info or questions below"
msgstr "Bitte tragen Sie alle zusätzlichen wichtigen Informationen oder Fragen unten ein"

#: app/domain/participant/ParticipantFields.tsx:750
msgid "Please select in case you bring your own gear."
msgstr "Bitte wählen Sie, falls Sie Ihre eigene Ausrüstung mitbringen."

#: app/routes/_all._catch.participant_.mutate.tsx:883
msgid "Please select the activity (or activities) you're attending."
msgstr "Bitte wählen Sie die Aktivität (oder Aktivitäten) aus, an der/denen Sie teilnehmen."

#: app/domain/participant/GenderSelect.tsx:11
msgid "Prefer not to say"
msgstr "Möchte ich nicht sagen"

#: app/routes/_all._catch.participant_.mutate.tsx:830
msgid "Preferred language"
msgstr "Bevorzugte Sprache"

#: app/routes/_all._catch.participant_.mutate.tsx:1250
msgid "Recipient email"
msgstr "Empfänger-E-Mail"

#: app/routes/_all._catch.participant_.mutate.tsx:1149
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Register"
msgstr "Registrieren"

#: app/domain/participant/participant-data.tsx:35
msgid "Regulator"
msgstr "Atemregler"

#: app/domain/participant/ParticipantFields.tsx:640
msgid "Relationship"
msgstr "Beziehung"

#: app/routes/_all._catch._index.tsx:156
msgid "Research & booking made easier"
msgstr "Recherche & Buchung einfacher gemacht"

#: app/routes/_all._catch.participant_.mutate.tsx:1191
msgid "Returning participant? Retrieve your registration via OTP email verification."
msgstr "Wiederkehrender Teilnehmer? Rufen Sie Ihre Registrierung per OTP-E-Mail-Verifizierung ab."

#: app/routes/_all._catch.participant_.$participant_id._index.tsx:992
#: app/domain/participant/ParticipantFields.tsx:510
msgid "Room number"
msgstr "Zimmernummer"

#: app/domain/participant/ParticipantFields.tsx:511
msgid "Room number of your stay"
msgstr "Zimmernummer Ihres Aufenthalts"

#: app/domain/participant/ParticipantFields.tsx:425
#~ msgid "Room number of your stay at our resort"
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:562
#: app/routes/_all._catch.participant_.mutate.tsx:1506
msgid "Save"
msgstr "Speichern"

#: app/domain/participant/ParticipantFields.tsx:737
msgid "select"
msgstr "Auswählen"

#: app/domain/participant/ParticipantFields.tsx:1338
msgid "Select"
msgstr "Auswählen"

#: app/routes/_all._catch.participant_.mutate.tsx:1281
msgid "Select add-ons"
msgstr "Add-Ons auswählen"

#: app/components/CountrySelectHeadlessUi.tsx:100
#: app/components/CountrySelect.tsx:171
msgid "select country"
msgstr "Land auswählen"

#: app/components/CountrySelectHeadlessUi.tsx:133
#: app/components/CountrySelect.tsx:202
msgid "Select country"
msgstr "Land auswählen"

#: app/components/CountrySelectHeadlessUi.tsx:28
#: app/components/CountrySelect.tsx:41
msgid "Select Country"
msgstr "Land auswählen"

#: app/domain/participant/GenderSelect.tsx:67
msgid "Select gender"
msgstr "Geschlecht auswählen"

#: app/domain/participant/ParticipantFields.tsx:197
msgid "Select level"
msgstr "Level auswählen"

#: app/routes/_all._catch.participant_.mutate.tsx:646
#~ msgid "Select one or more activities"
#~ msgstr ""

#: app/domain/participant/ParticipantFields.tsx:222
msgid "Select organisation"
msgstr "Organisation auswählen"

#: app/domain/participant/ParticipantFields.tsx:803
msgid "Select size"
msgstr "Größe auswählen"

#: app/domain/participant/ParticipantFields.tsx:1012
msgid "Shoe size"
msgstr "Schuhgröße"

#: app/routes/_all._catch.waiver.$id.tsx:364
msgid "Signature of certified medical doctor or other legally certified medical provider"
msgstr "Unterschrift approbierter Arzt oder anderweitig gesetzlich bestallter medizinischer Dienstleister"

#: app/domain/participant/participant.signature.component.tsx:131
#: app/domain/participant/participant.signature.component.tsx:137
msgid "Signature of parent or guardian"
msgstr "Unterschrift des Elternteils oder Erziehungsberechtigten"

#: app/domain/participant/participant.signature.component.tsx:78
#: app/domain/participant/participant.signature.component.tsx:84
msgid "Signature participant"
msgstr "Unterschrift des Teilnehmers"

#: app/domain/participant/ParticipantFields.tsx:488
msgid "Stay"
msgstr "Aufenthalt"

#: app/domain/participant/ParticipantFields.tsx:386
#~ msgid "Staying with us? Please fill your room number."
#~ msgstr ""

#: app/routes/_all._catch.waiver.$id.tsx:330
msgid "The above-named person requests your opinion of his/her medical suitability to participate in recreational scuba diving or freediving training or activity. Please visit <0>uhms.org</0> for medical guidance on medical conditions as they relate to diving. Review the areas relevant to your patient as part of your evaluation."
msgstr "Die oben genannte Person bittet Sie um Ihre Meinung über ihre medizinische Eignung zur Teilnahme an einer Ausbildung oder Aktivität im Freizeit-Gerätetauchen oder Freitauchen. Bitte besuchen Sie <0>uhms.org</0> für ärztliche Beratung über die medizinischen Bedingungen im Zusammenhang mit dem Tauchen. Überprüfen Sie die für Ihren Patienten relevanten Bereiche als Teil Ihrer Beurteilung."

#: app/domain/participant/participant.signature.component.tsx:23
msgid "Today's date"
msgstr "Heutiges Datum"

#: app/domain/participant/participant-fields.tsx:8
msgid "Vegan"
msgstr "Vegan"

#: app/domain/participant/participant-fields.tsx:7
msgid "Vegetarian"
msgstr "Vegetarisch"

#: app/routes/_all._catch._w.waiver_.mutate.tsx:173
#~ msgid "View"
#~ msgstr "View"

#: app/misc/error-translations.ts:15
msgid "Wachtwoord moet tenminste 6 tekens lang zijn."
msgstr "Das Passwort muss mindestens 6 Zeichen lang sein."

#: app/domain/participant/participant-data.tsx:47
msgid "Walk-in"
msgstr "Walk-in (ohne Termin)"

#: app/domain/participant/ParticipantFields.tsx:910
msgid "Weight"
msgstr "Neoprenanzug"

#: app/domain/participant/ParticipantFields.tsx:1115
msgid "Weightbelt"
msgstr "Bleigurt"

#: app/domain/participant/participant-data.tsx:36
msgid "Wetsuit"
msgstr "Neoprenanzug"

#: app/domain/participant/ParticipantFields.tsx:802
msgid "Wetsuit size"
msgstr "Neoprenanzug-Größe"

#: app/domain/participant/ParticipantFields.tsx:570
msgid "WhatsApp preferred"
msgstr "WhatsApp bevorzugt"

#: app/domain/participant/participant-helpers.tsx:10
msgid "Within the last 3 months"
msgstr "Innerhalb der letzten 3 Monate"

#: app/domain/participant/participant-helpers.tsx:11
msgid "Within the last 6 months"
msgstr "Innerhalb der letzten 6 Monate"

#: app/domain/participant/participant-helpers.tsx:12
msgid "Within the last year"
msgstr "Innerhalb des letzten Jahres"

#: app/domain/participant/ParticipantFields.tsx:709
msgid "Years of experience"
msgstr "Jahre Erfahrung"

#: app/routes/_all._catch.waiver.$id.tsx:214
#: app/routes/_all._catch.waiver.$id.tsx:265
#: app/domain/participant/ParticipantFields.tsx:1253
msgid "Yes"
msgstr "Ja"

#: app/domain/participant/ParticipantFields.tsx:1285
msgid "Your Instagram Handle"
msgstr "Ihr Instagram-Benutzername"

#: app/domain/participant/participant-data.tsx:50
msgid "YouTube"
msgstr "YouTube"

