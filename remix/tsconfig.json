{"include": ["env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["./app/libs/tiptap/**/*"], "compilerOptions": {"module": "esnext", "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "lib": ["DOM", "DOM.Iterable", "ES2019"], "isolatedModules": true, "esModuleInterop": true, "jsx": "react-jsx", "moduleResolution": "node", "resolveJsonModule": true, "target": "ES2019", "strict": true, "noUncheckedIndexedAccess": true, "baseUrl": ".", "paths": {"~/*": ["./app/*", "./generated/*"]}, "typeRoots": ["node_modules/@types", "@types"], "noEmit": true, "forceConsistentCasingInFileNames": true, "allowJs": true}}