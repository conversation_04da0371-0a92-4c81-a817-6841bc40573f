import type { <PERSON> } from "~/kysely/db";
import type { PoolConfig } from "pg";
import pkg from "pg";
import { FileMigrationProv<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PostgresDialect } from "kysely";
import path from "path";
import { fileURLToPath } from "url";

import { promises as fs } from "fs";
import { appConfig } from "~/config/config.server";

const { Pool } = pkg;

const __filename = fileURLToPath(import.meta.url);

const __dirname = path.dirname(__filename);

const kyseleMigrationFileProvider = new FileMigrationProvider({
  fs,
  path,
  migrationFolder: `${__dirname}/migrations`,
});

(async () => {
  const doConfig: PoolConfig = {
    ...appConfig.DB,
    max: 1,
    // ssl: environment === "development" ? undefined : { rejectUnauthorized: false },
    // maxUses: 1,
    // allowExitOnIdle: true,
    // maxUses: 1,
    // allowExitOnIdle: true,
    // user: "doadmin",
    // password: "AVNS_xlkes0PO74VODftm9BO",
    // host: "db-scamgem-dev-do-user-4626682-0.c.db.ondigitalocean.com",
    // port: 25061,
    // database: "master-pool",
    // ssl: { rejectUnauthorized: false },˙˙
  };

  const kyselyClient = new Kysely<DB>({
    dialect: new PostgresDialect({
      pool: new Pool(doConfig),
    }),
  });

  const migrator = new Migrator({
    db: kyselyClient,
    provider: kyseleMigrationFileProvider,
  });

  await kyselyClient.destroy();

  const { results, error } = await migrator.migrateToLatest();
  console.log("migrations executed", results?.length);
  results?.forEach((it) => {
    if (it.status === "Success") {
      console.log(`migration "${it.migrationName}" was executed successfully`);
    } else if (it.status === "Error") {
      console.error(`failed to execute migration "${it.migrationName}"`);
    }
  });

  if (error) {
    console.error("failed to migrate");
    console.error(error);
    throw error;
  }
})();
