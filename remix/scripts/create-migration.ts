import { fileURLToPath } from "url";
import path from "path";
import fs from "fs";
import { toUtc } from "~/misc/date-helpers";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function getTimestamp(): string {
  const now = toUtc();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");

  return `${year}${month}${day}${hours}${minutes}${seconds}`;
}

const timestamp = getTimestamp();
const migrationsDir = path.join(__dirname, "../migrations");

// Get migration name from command line args, default to 'new-migration'
const migrationName = process.argv[2] || "migration";

// Create the filename with timestamp
const filename = `${timestamp}_${migrationName}.ts`;

// Migration template
const template = `import { Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      \`
      -- Your SQL statements here
      \`
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      \`
      -- Reversion SQL statements here
      \`
    )
    .execute(db);
}
`;

// Ensure migrations directory exists
if (!fs.existsSync(migrationsDir)) {
  fs.mkdirSync(migrationsDir, { recursive: true });
}

// Write the file
const fullPath = path.join(migrationsDir, filename);
fs.writeFileSync(fullPath, template);

console.log(`Created migration: file://${fullPath}`);
