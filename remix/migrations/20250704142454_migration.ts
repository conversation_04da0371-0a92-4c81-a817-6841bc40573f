import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
        -- Your SQL statements here
        alter table comment
          alter column participation_id drop not null;
        alter table comment
          rename column participation_id to participation_id_old;
alter table comment alter column date drop not null ;

        alter table comment
          add target_id uuid;

        alter table comment
          add target varchar(255);

        update comment
        set target_id = participation_id_old,
            target    = 'participation';

        alter table comment
          alter column target_id set not null,
          alter column target set not null;

      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `
    )
    .execute(db);
}
