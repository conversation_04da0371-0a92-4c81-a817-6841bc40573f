import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table booking_registration add column signature jsonb[];
alter table booking_registration drop column signature;
alter table booking_registration add column signature json;
`
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
