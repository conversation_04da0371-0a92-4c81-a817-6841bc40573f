import { Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
        -- Your SQL statements here

        alter table user_session
          alter column user_id drop not null;

        alter table user_session
          drop constraint active_user_session;
--         drop index active_user_session;


        create unique index active_user_session
          on user_session (session_id, user_id, destroyed_at)
          nulls not distinct;

--         select count(*)
--         from user_event
--         where user_event.user_session_id is null
--           and user_event.session_id is null;


        insert into user_session (session_id, created_at, currency_switched)
        select distinct on (user_event.session_id) user_event.session_id,
                                                   user_event.created_at,
                                                   session.currency_switched
        from user_event
               inner join session on user_event.session_id = session.id
        where user_event.user_session_id is null
        order by user_event.session_id, user_event.created_at asc
        on conflict do nothing;

        alter table user_event
          drop constraint user_event_check;

        update user_event
        set user_session_id = (select user_session.id
                               from user_session
                               where user_session.session_id = user_event.session_id
                                 and user_session.user_id is null)
        where user_event.user_session_id is null;

        delete
        from entity_action
        where entity_action.user_event_id in (select user_event.id
                                              from user_event
                                              where user_event.session_id is null
                                                and user_event.user_session_id is null);

        delete
        from user_event
        where user_event.session_id is null
          and user_event.user_session_id is null;

        alter table user_event
          alter column user_session_id set not null;


        insert into user_session (session_id, created_at, currency_switched)
        select distinct on (participant.created_by_session_id) participant.created_by_session_id,
                                                               participant.created_at,
                                                               session.currency_switched
        from participant
               inner join session on participant.created_by_session_id = session.id
        where participant.created_by_user_session_id is null
        order by participant.created_by_session_id, participant.created_at asc
        on conflict do nothing;


        update participant
        set created_by_user_session_id = (select user_session.id
                                          from user_session
                                          where user_session.user_id is null
                                            and user_session.session_id = participant.created_by_session_id)
        where participant.created_by_user_session_id is null;


--         select min(participant.created_at), count(distinct created_by_session_id)
--         from participant
--         where created_by_user_session_id is null;
-- select count(*) from user_session where user_session.user_id is not null;

        alter table participant
          rename created_by_session_id to created_by_session_id_old;
        alter table participant
          alter column created_by_session_id_old drop not null;
        alter table participant
          alter column created_by_user_session_id set not null;
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
