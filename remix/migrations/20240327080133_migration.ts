import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table waiver add column root_id uuid, add column language_code char(2) not null default 'en';
update waiver set root_id = id where waiver.root_id is null;
alter table waiver alter column root_id set not null ;
`,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
