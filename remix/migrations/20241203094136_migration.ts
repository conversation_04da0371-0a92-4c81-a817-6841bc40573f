import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          --           select operator.name,
--                  member.name,
--                  count(member.id),
--                  jsonb_agg(
--                          json_build_object('email', "user".email, 'admin', member.admin, 'captain', member.captain,
--                                            'instructor',
--                                            member.instructor, 'crew', member.crew, 'owner', member.owner)
--                          order by "user".email desc)
--           from member
--                    inner join establishment on member.establishment_id = establishment.id
--                    inner join operator on establishment.operator_id = operator.id
--                    left join "user" on member.user_id = "user".id
--           where "user".deleted_at = 'infinity'
--              or "user".deleted_at is null
--           group by operator.name, member.establishment_id, member.name
--           having count(member.id) > 1
--           order by operator.name, member.establishment_id;


          update member
          set name = updatingmembers.name_with_index
          from (select member.id,
                       trim(member.name),
                       member.user_id,
                       member.establishment_id,
                       row_number()
                       over (partition by member.establishment_id, trim(member.name) order by member.user_id, member.id),
                       CASE
                           WHEN ROW_NUMBER()
                                OVER (PARTITION BY member.establishment_id, trim(member.name) ORDER BY member.user_id, member.id) >
                                1
                               THEN trim(member.name) || ' ' ||
                                    ROW_NUMBER()
                                    OVER (PARTITION BY member.establishment_id, trim(member.name) ORDER BY member.user_id, member.id)
                           ELSE trim(member.name)
                           END AS name_with_index
                from member
                where (select count(*)
                       from member as m2
                       where m2.establishment_id = member.establishment_id
                         and trim(m2.name) = trim(member.name)) > 1) as updatingmembers
          where updatingmembers.id = member.id;

-- select member.establishment_id, trim(member.name), count(member.id) from member group by member.establishment_id, trim(member.name) having count(member.id) > 1;

          update member
          set name = trim(name)
          where name != trim(name);

          ALTER TABLE member
              ADD CONSTRAINT name_no_whitespace
                  CHECK (name = TRIM(name));

          update member
          set deleted_at = 'infinity'
          where deleted_at is null;

          alter table member
              alter column deleted_at set not null;
          alter table member
              alter column deleted_at set default 'infinity';

          alter table member
              drop constraint member_establishment_id_user_id_key;

          alter table member
              add unique (deleted_at, establishment_id, user_id);

          alter table member
              add unique (deleted_at, establishment_id, name);


          CREATE OR REPLACE FUNCTION trim_name_before_insert_or_update()
              RETURNS TRIGGER AS
          $$
          BEGIN
              -- Trim leading and trailing whitespace from the 'name' column
              NEW.name := TRIM(NEW.name);
              RETURN NEW;
          END;
          $$ LANGUAGE plpgsql;

          CREATE TRIGGER trim_name_trigger
              BEFORE INSERT OR UPDATE
              ON member
              FOR EACH ROW
          EXECUTE FUNCTION trim_name_before_insert_or_update();
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
