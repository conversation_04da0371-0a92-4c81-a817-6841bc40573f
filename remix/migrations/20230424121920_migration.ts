import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          create table user_schedule
          (
              id                   uuid primary key   default gen_random_uuid(),
              user_id              uuid references "user" (id),
              range                tstzrange not null,
              available            bool      not null default true,
              note                 text,
              operator_location_id uuid references operator_location (id),
              days_of_week         int[]
          );

-- select extract (dow from '2023-04-24' at time zone 'Asia/Makassar');
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
