import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          
alter table currency alter column id type char(3);
    alter table xendit_environment add column currency_id char(3) references currency;

update xendit_environment set currency_id = 'IDR' where xendit_environment.name = 'indonesia';
update xendit_environment set currency_id = 'THB' where xendit_environment.name = 'thailand';


alter table xendit_environment alter column currency_id set not null ;

-- select distinct currency.id from currency
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
