import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table product
              add column liability_form_enabled    bool default false,
              add column medical_form_enabled      bool default false,
              add column safety_diving_for_enabled bool default false;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
