import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
      alter table user_session
          drop constraint active_user_session;

      alter table user_session
          add constraint active_user_session
              unique (session_id, user_id, destroyed_at);

    alter table session 
            add column selected_user_id uuid;
  `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
