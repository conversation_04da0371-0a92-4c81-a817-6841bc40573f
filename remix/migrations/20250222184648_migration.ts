import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          -- diving_certificate_number
          -- previous 0-hidden, 1-show, 2-require-or, 3-require-both 
-- next 0-hidden, 1-show, 2-show-number, 3-show-upload, 4-require-or, 5-require-number, 6-require-upload

          update field
          set status = 4
          where (field.name = 'diving_certificate_number' or field.name = 'passport_number')
            and field.status >= 2;

          -- passport
          -- previous 0-hidden, 1-show, 2-require-or, 3-require-both 
-- next 0-hidden, 1-show, 2-show-number, 3-show-upload, 4-require-or, 5-require-number, 6-require-upload

      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
