import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table callback add column success bool; 
-- select distinct status from callback;
update callback set success = true where callback.status = 'SUCCESS';
alter table callback drop column status;
-- alter table participant add column 
`
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
