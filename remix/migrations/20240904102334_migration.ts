import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

          alter table participant_waiver
              add column created_at timestamptz default now();

          update participant_waiver
          set created_at = (select user_event.created_at
                            from user_event
                                     inner join entity_action on user_event.id = entity_action.user_event_id
                            where entity_action.entity_id = participant_waiver.id
                              and entity_action.action_name = 'insert')
          where participant_waiver.created_at is null;

          alter table participant_waiver
              alter column created_at set not null;

          alter table activity
              alter column created_by_user_session_id drop not null;

          alter table participation
              alter column created_by_user_session_id drop not null;


          alter table participant
              add column main_booker bool;

          alter table participant
              add unique (booking_id, main_booker);

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
