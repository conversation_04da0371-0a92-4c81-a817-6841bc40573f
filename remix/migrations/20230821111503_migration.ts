import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table diving_site
              add column slug text,
              add unique (diving_location_id, slug);

          update diving_site
          set slug = replace(lower(name), ' ', '-')
          where slug is null;

          alter table diving_site
              alter column slug set not null;

      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
