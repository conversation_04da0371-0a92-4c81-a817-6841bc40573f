import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table participant
              alter column first_name set not null,
              alter column last_name set not null;

          update activity
          set form_id = (select form.id
                         from form
                         where form.target_id is null
                           and form.deleted_at = 'infinity'
                           and exists(select product.id
                                      from product
                                      where product.id = activity.product_id
                                        and (product.activity_slug = form.filter_activity_slug or
                                             (product.activity_slug = 'other' and form.filter_activity_slug is null))
                                        and (
                                          form.filter_beginner_diver = false or
                                          product.id in (select product__diving_course.product_id
                                                         from diving_course
                                                                  inner join product__diving_course
                                                                             on product__diving_course.diving_course_id = diving_course.id
                                                         where (diving_course.tag =
                                                                'first_time_diver' or diving_course.tag =
                                                                                      'ready_to_start'))))
                         order by form.filter_activity_slug asc nulls last, form.target_id asc nulls last
                         limit 1)
          where form_id is null;
          alter table activity
              alter column form_id set not null;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
