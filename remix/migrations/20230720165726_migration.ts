import type { <PERSON>ys<PERSON> } from "kysely";
import { sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table participant
              drop column years_of_experience;
          alter table participant
              add column years_of_experience int4range;
          alter table participant
              drop column experience_in_years;

          create table file
          (
              id                         uuid primary key,
              sort_order                 int  not null default 0,
              filename                   text not null unique,
              target                     text not null,
              target_id                  uuid not null,
              created_at                 timestamptz   default now(),
              created_by_user_session_id uuid references user_session,
              deleted_at                 timestamptz,
              deleted_by_user_session_id uuid references user_session
          );

          insert into file (id, filename, target, target_id, sort_order, created_at)
          select gen_random_uuid(), file.name, 'spot', spot.id, file.nr, null
          from spot
                   inner join lateral unnest(spot.files) with ordinality as file(name, nr) on true
          where files is not null;

          insert into file (id, filename, target, target_id, sort_order, created_at)
          select gen_random_uuid(), file.name, 'region', region.id, file.nr, null
          from region
                   inner join lateral unnest(region.files) with ordinality as file(name, nr) on true
          where files is not null;


          insert into file (id, filename, target, target_id, sort_order, created_at)
          select gen_random_uuid(), file.name, 'diving_location', diving_location.id, file.nr, null
          from diving_location
                   inner join lateral unnest(diving_location.files) with ordinality as file(name, nr) on true
          where files is not null;


          insert into file (id, filename, target, target_id, sort_order, created_at)
          select gen_random_uuid(), file.name, 'diving_site', diving_site.id, file.nr, null
          from diving_site
                   inner join lateral unnest(diving_site.files) with ordinality as file(name, nr) on true
          where files is not null;

          insert into file (id, filename, target, target_id, sort_order, created_at)
          select gen_random_uuid(), file.name, 'establishment', establishment.id, file.nr, null
          from establishment
                   inner join lateral unnest(establishment.files) with ordinality as file(name, nr) on true
          where files is not null;

          insert into file (id, filename, target, target_id, sort_order, created_at, created_by_user_session_id)
          select gen_random_uuid(), file.name, 'review', review.id, file.nr, review.created_at, review.created_by_user_session_id
          from review
                   inner join lateral unnest(review.files) with ordinality as file(name, nr) on true
          where files is not null;


-- select count(*) from file;


--           insert into file (id, filename, target, target_id, sort_order, created_at)
--           select gen_random_uuid(),
--                  filez.filename,
--                  'diving_location',
--                  filez.id                                        as target_id,
--                  (row_number() over (partition by filez.id) - 1) as index,
--                  null
--           from (select unnest(diving_location.files) as filename,
--                        diving_location.id
--                 from diving_location
--                 where files is not null) as filez;
-- 
--           insert into file (id, filename, target, target_id, sort_order, created_at)
--           select gen_random_uuid(),
--                  filez.filename,
--                  'diving_site',
--                  filez.id                                        as target_id,
--                  (row_number() over (partition by filez.id) - 1) as index,
--                  null
--           from (select unnest(diving_site.files) as filename,
--                        diving_site.id
--                 from diving_site
--                 where files is not null) as filez;
-- 
-- 
--           insert into file (id, filename, target, target_id, sort_order, created_at)
--           select gen_random_uuid(),
--                  filez.filename,
--                  'diving_site',
--                  filez.id                                        as target_id,
--                  (row_number() over (partition by filez.id) - 1) as index,
--                  null
--           from (select unnest(diving_site.files) as filename,
--                        diving_site.id
--                 from diving_site
--                 where files is not null) as filez;
-- 
-- 
-- 
--           insert into file (id, filename, target, target_id, sort_order, created_at)
--           select gen_random_uuid(),
--                  filez.filename,
--                  'establishment',
--                  filez.id                                        as target_id,
--                  (row_number() over (partition by filez.id) - 1) as index,
--                  null
--           from (select unnest(establishment.files) as filename,
--                        establishment.id
--                 from establishment
--                 where files is not null) as filez;
-- 
-- 
-- 
--           insert into file (id, filename, target, target_id, sort_order, created_at, created_by_user_session_id)
--           select gen_random_uuid(),
--                  filez.filename,
--                  'review',
--                  filez.id                                        as target_id,
--                  (row_number() over (partition by filez.id) - 1) as index,
--                  filez.created_at,
--                  filez.created_by_user_session_id
--           from (select unnest(review.files) as filename,
--                        review.id,
--                        review.created_at,
--                        review.created_by_user_session_id
--                 from review
--                 where files is not null) as filez;
-- 
-- 
-- 
--           insert into file (id, filename, target, target_id, sort_order, created_at)
--           select gen_random_uuid(),
--                  filez.filename,
--                  'region',
--                  filez.id                                        as target_id,
--                  (row_number() over (partition by filez.id) - 1) as index,
--                  null
--           from (select unnest(region.files) as filename,
--                        region.id
--                 from region
--                 where files is not null) as filez;
-- 
-- 
--           insert into file (id, filename, target, target_id, sort_order, created_at)
--           select gen_random_uuid(),
--                  filez.filename,
--                  'spot',
--                  filez.id                                        as target_id,
--                  (row_number() over (partition by filez.id) - 1) as index,
--                  null
--           from (select unnest(spot.files) as filename,
--                        spot.id
--                 from spot
--                 where files is not null) as filez;
-- 
-- 
--           select *
--           from file
--           where target = 'region';
          --           select *
--           from spot
--           where files is not null;
-- 
-- 
--           select *
--           from file;
--           delete
--           from file;
-- select * from review whe; 
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
