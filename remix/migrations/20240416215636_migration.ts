import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table waiver_translation drop column if exists questions;
      alter table waiver_translation
          add column questions jsonb;

update waiver_translation
set markdoc = '# **Diver medical** | Participant Questionnaire

Recreational scuba diving and freediving requires good physical and mental health. There are a few medical conditions which can be hazardous while diving, listed below. Those who have, or are predisposed to, any of these conditions, should be evaluated by a physician. This Diver Medical Participant Questionnaire provides a basis to determine if you should seek out that evaluation. If you have any concerns about your diving fitness not represented on this form, consult with your physician before diving. If you are feeling ill, avoid diving. If you think you may have a contagious disease, protect yourself and others by not participating in dive training and/ or dive activities. References to “diving” on this form encompass both recreational scuba diving and freediving. This form is principally designed as an initial medical screen for new divers, but is also appropriate for divers taking continuing education. For your safety, and that of others who may dive with you, answer all questions honestly.

**Directions** {% br /%}
Complete this questionnaire as a prerequisite to a recreational scuba diving or freediving course.{% br /%}
**Note to women**: If you are pregnant, or attempting to become pregnant, do not dive.'
where waiver_id = (select waiver.id from waiver where waiver.medical = true) and language_code = 'en';

alter table signature
    drop constraint signature_target_id_form_name_signed_by_representative_key;

alter table signature
    add constraint signature_pk
        unique (participant_waiver_id, signed_by_representative);

  `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
