import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
        -- Your SQL statements here
        alter table addon
          add column price_id uuid references price;

        insert into price (amount, currency_id, amount_usd)
        select addon.price,
               addon.price_currency,
               (coalesce(
                 nullif(addon.price, 0) /
                 (select currency.conversion_rate_usd
                  from currency
                  where currency.id = addon.price_currency
                  limit 1),
                 0
                ))
        from addon
        on conflict do nothing;

        update addon
        set price_id = (select price.id
                        from price
                        where price.amount = addon.price
                          and price.currency_id = addon.price_currency);

        alter table addon
          alter column price_id set not null,
          alter column price drop not null ,
          alter column price_currency drop not null;
        alter table addon
          rename column price to price_old;
        alter table addon
          rename column price_currency to price_currency_old;

        alter table activity_addon
          rename price to price_amount;



      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
