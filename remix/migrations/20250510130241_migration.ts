import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
        -- Your SQL statements here
        alter table booking
          alter column meeting_type drop not null;

        alter table booking
          add column cart_for_session_id uuid references session,
          add unique (establishment_id, cart_for_session_id);

        alter table booking
          alter column created_by_user_session_id drop not null;

-- alter table booking add column 
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
