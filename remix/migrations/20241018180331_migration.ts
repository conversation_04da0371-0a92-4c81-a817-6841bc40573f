import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      alter table participant
          rename column cache_read_waivers_valid to cached_read_waivers_valid;
      alter table participant
          rename column cache_signature_waivers_valid to cached_signature_waivers_valid;
      alter table participant
          drop column cache_stale;
  `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
