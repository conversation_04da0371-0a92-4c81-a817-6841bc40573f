import { <PERSON>ys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table form
              add column forms text[] not null default '{}'::text[];

          update form
          set forms = coalesce((select array_agg(form_name)
                                from (select distinct unnest(forms) as form_name
                                      from product
                                      where product.form_id = form.id) as formNames), '{}'::text[])
          where form.forms is not null;
          select *
          from form;

          alter table product
              rename column forms to forms_old;


          alter table activity
              add column form_id uuid references form;

          update activity
          set form_id = (select product.form_id from product where product.id = activity.product_id)
          where activity.form_id is null;

alter table product rename column form_id to form_root_id;

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
