import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table one_time_password
              add column session_id  uuid references session,
              add column email       email,
              add column verified_at timestamptz;

          update one_time_password
          set session_id = (select session_id
                            from user_session
                            where user_session.id = one_time_password.user_session_id),
              email      = (select "user".email
                            from user_session
                                     inner join "user" on user_session.user_id = "user".id
                            where user_session.id = one_time_password.user_session_id);

          alter table one_time_password
              alter column user_session_id drop not null;
          alter table one_time_password
              rename column user_session_id to user_session_id_old;


          alter table participant
              add column verified_at timestamptz;

          delete
          from session_link
          where session_link.target_id not in (select participant.id from participant);

          alter table session_link
              rename column target_id to participant_id;

          --           alter table session_link
--               drop constraint session_link_participant_id_fk;
          alter table session_link
              add constraint session_link_participant_id_fk foreign key (participant_id) references participant (id) on delete cascade;

          alter table participant
              add column digital_signing_agreed_at timestamptz;

alter table one_time_password alter column email type text;

      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
