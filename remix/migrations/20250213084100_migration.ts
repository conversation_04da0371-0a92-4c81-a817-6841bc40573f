import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          --           select max(participant_waiver.created_at), participant_waiver.approved
--           from participant_waiver
--           group by participant_waiver.approved;
          alter table participant_waiver
              drop column approved;

          alter table participant_waiver
              rename column medical_approved to upload_approved;
          alter table participant_waiver
              rename column medical_evaluation_required to upload_required;

          alter table participant_waiver
              add column medical bool;

          update participant_waiver
          set medical = (select waiver.medical from waiver where waiver.id = participant_waiver.waiver_id);

          alter table participant_waiver
              alter column medical set not null;

          alter table waiver
              add column upload_required bool not null default false;


          alter table waiver
              add column type text;

          update waiver
          set type = 'read';
          update waiver
          set type = 'signature'
          where waiver.signature_required = true;
          update waiver
          set type = 'medical'
          where waiver.medical = true;

          alter table waiver
              alter column type set not null;

          alter table waiver
              alter column signature_required drop default,
              alter column signature_required drop not null,
              alter column medical drop default,
              alter column medical drop not null,
              alter column upload_required drop default,
              alter column upload_required drop not null;

          alter table waiver
              rename column signature_required to signature_required_old;
          alter table waiver
              rename column medical to medical_old;
          alter table waiver
              rename column upload_required to upload_required_old;

          alter table participant_waiver
              add column waiver_type text;

          update participant_waiver
          set waiver_type = 'read';
          update participant_waiver
          set waiver_type = 'signature'
          where participant_waiver.signature_required = true;
          update participant_waiver
          set waiver_type = 'medical'
          where participant_waiver.medical = true;

          alter table participant_waiver
              alter column waiver_type set not null;

          alter table participant_waiver
              alter column medical drop default,
              alter column medical drop not null;
          alter table participant_waiver
              rename column medical to medical_old;

      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
