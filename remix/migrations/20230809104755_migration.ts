import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table file
              add column created_by_session_id uuid references session;

          update file
          set created_by_session_id = (select user_session.session_id
                                       from user_session
                                       where user_session.id = file.created_by_user_session_id)
          where file.created_by_user_session_id is not null;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
