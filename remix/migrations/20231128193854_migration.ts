import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table schedule rename column range to range_old;
alter table schedule add column range daterange;
update schedule set range = daterange(lower(range_old)::date, upper(range_old)::date, '[]') where range is null;
alter table schedule alter column range set not null ;
alter table schedule alter column range_old drop not null ;
alter table schedule add column sort_order int;
alter table schedule add column created_at timestamptz;
alter table schedule add column created_by_user_session_id uuid references user_session;
alter table schedule alter column created_at set default now();

-- alter table schedule drop column created_at;
-- select * from schedule inner join ;
`,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
