import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
          --       select count(ta.user_event_id), array_agg(ta.table_name), array_agg(ta.target_id)
--       from user_event ue
--                inner join table_action ta on ue.id = ta.user_event_id
--       where payload is not null
--       group by ta.user_event_id
--       having count(ta.user_event_id) = 0;
-- 
--       select *
--       from user_event
--       where user_event.id not in (select user_event_id from table_action);
-- 
--       select count(*)
--       from user_event
--       where payload is not null;
-- 
-- select * from table_action limit 100;
-- 
--       select id, table_name, name, table_action.data -> 'diving_location_ids', table_action.data
--       select count(*)
--       from table_action
--       where (table_action.data ->> 'diving_location_ids') is not null
--       limit 20;
-- 
--       select *
--       from user_event;

          insert into table_action (target_id, name, table_name, user_event_id, data)
          select ta.target_id,
                 'set_diving_location_ids',
                 ta.table_name,
                 ta.user_event_id,
                 ta.data -> 'diving_location_ids'
          from table_action ta
          where (ta.data ->> 'diving_location_ids') is not null;

          select count(*)
          from table_action
          where name = 'set_diving_location_ids';

          update table_action
          set data = data - 'diving_location_ids';


          insert into table_action (target_id, name, table_name, user_event_id, data)
          select ta.target_id,
                 'set_diving_site_ids',
                 ta.table_name,
                 ta.user_event_id,
                 ta.data -> 'diving_site_ids'
          from table_action ta
          where (ta.data ->> 'diving_site_ids') is not null;

          select count(*)
          from table_action
          where name = 'set_diving_site_ids';

          update table_action
          set data = data - 'diving_site_ids';



          insert into table_action (target_id, name, table_name, user_event_id, data)
          select ta.target_id,
                 'set_diving_course_ids',
                 ta.table_name,
                 ta.user_event_id,
                 ta.data -> 'diving_course_ids'
          from table_action ta
          where (ta.data ->> 'diving_course_ids') is not null;

          select count(*)
          from table_action
          where name = 'set_diving_course_ids';

          update table_action
          set data = data - 'diving_course_ids';

          update table_action
          set data = data - 'id';


          alter table table_action
              rename column table_name to entity_name;
          alter table table_action
              rename column target_id to entity_id;
          alter table table_action
              rename column name to action_name;
          alter table table_action
              rename to entity_action;

          alter table user_event
              drop column target_id,
              drop column payload,
              drop column table_name,
              drop column "name";


          insert into entity_action (action_name, entity_name, entity_id, data, user_event_id)
          select 'add_language_codes',
                 'operator_location',
                 (t.data ->> 'operator_location_id')::uuid,
                 json_build_array(t.data ->> 'language_code'),
                 t.user_event_id
          from entity_action as t
          where t.entity_name = 'operator_location__language'
            and t.action_name = 'create';

          delete
          from entity_action
          where entity_name = 'operator_location__language'
            and action_name = 'create';


          insert into entity_action (action_name, entity_name, entity_id, data, user_event_id)
          select 'remove_language_codes',
                 'operator_location',
                 (t.data ->> 'operator_location_id')::uuid,
                 json_build_array(t.data ->> 'language_code'),
                 t.user_event_id
          from entity_action as t
          where t.entity_name = 'operator_location__language'
            and t.action_name = 'delete';

          delete
          from entity_action
          where entity_name = 'operator_location__language'
            and action_name = 'delete';


          alter table product__diving_course
              drop column id;
          alter table product__diving_location
              drop column id;
          alter table product__diving_site
              drop column id;
          alter table operator_location__language
              drop column id;
          -- select * from entity_action where entity_action.entity_name = 'product' and entity_action.action_name != 'create' limit 100;
-- select * from product where id not in (select entity_action.entity_id from entity_action where entity_name = 'product' and entity_id = product.id and action_name = 'create')

      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
