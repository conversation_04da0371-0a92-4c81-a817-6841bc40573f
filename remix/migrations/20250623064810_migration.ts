import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Your SQL statements here
      create table tank_assignment (  
        id                         uuid primary key default gen_random_uuid() not null,
        quantity int2 not null,
        liters int2 not null,
        gas text not null,
        trip_assignment_id uuid not null references trip_assignment(id) on delete cascade
        );

        alter table rentable add column brand text;
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
