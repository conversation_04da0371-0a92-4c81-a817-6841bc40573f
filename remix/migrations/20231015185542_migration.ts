import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
--           alter table member column instructor;
alter table member add column diving_level int2 default 0 not null;
update member
set diving_level = 2
where instructor = true;

          alter table member
              rename column admin to admin_old;
          alter table member
              add column admin int2 not null default 0;
          update member
          set admin = 2
          where member.admin_old = true;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
