import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table participant add created_by_session_id uuid references session (id);
alter table participant alter column created_by_user_session_id drop not null ;
update participant set created_by_session_id = (select session_id from user_session where user_session.id = participant.created_by_user_session_id)
where participant.created_by_session_id is null;
alter table participant alter column created_by_session_id set not null ;
`,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
