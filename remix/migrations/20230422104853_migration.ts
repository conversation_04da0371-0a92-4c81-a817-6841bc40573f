import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table booking__boat_departure
              drop constraint booking__boat_departure_boat_departure_id_fkey;
   
          alter table booking__boat_departure
              add foreign key (boat_departure_id) references boat_departure
                  on delete cascade;


--           select '2020-12-01 10:00 Asia/Makassar'::timestamptz;
-- select ((now()) at time zone 'Asia/Makassar')::date::text + ' 10:00'::text;
-- select (((now() + '3 hour') at time zone 'Asia/Makassar')::date::text || ' 10:00 ' || 'Asia/Makassar')::timestamptz;
-- select 'Asia/Makassar'::timestamptz;
-- select boat_departure.departure_time, * from boat_departure;
`
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
