import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Your SQL statements here
   alter table participant_day
    drop constraint participant_day_participant_id_fkey;

alter table participant_day
    add foreign key (participant_id) references participant
        on delete cascade;   
      `
    )
    .execute(db);
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `
    )
    .execute(db);
}
