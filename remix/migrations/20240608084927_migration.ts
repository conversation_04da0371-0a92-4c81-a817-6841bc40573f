import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
--       alter table comment
--           add column participant_id uuid references participant;
--       alter table comment
--           add column activity_id uuid references activity;
--       alter table comment
--           rename column participation_id to participation_id_old;
--       alter table comment
--           alter column participation_id_old drop not null;
-- 
--       update comment
--       set participant_id = (select participant_id
--                             from participation
--                             where participation.id = comment.participation_id_old),
--           activity_id    = (select activity_id
--                             from participation
--                             where participation.id = comment.participation_id_old);
  `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
