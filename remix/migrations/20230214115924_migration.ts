import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
      alter table diving_site
          rename column short to headline;
      alter table diving_site
          add column summary text;
  `
    )
    .execute(db);
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
}
