import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table product
              rename column title to subtitle;
          alter table product
              add column title text;
          alter table product
              alter column forms set default '{}'::text[];
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
