import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
        -- Your SQL statements here
--       alter table view ;

        update view
        set columns = regexp_split_to_array(
          replace(
            array_to_string(columns, ','),
            'remarks',
            'participant_comment,booking_internal_note'
          ),
          ','
                      );
-- 
--         select *
--         from view
--         where 'participant_comment' = ANY (columns)
--           and columns[array_length(columns, 1)] != 'participant_comment';;
-- 
--         select *
--         from view
--         order by view.id;
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
