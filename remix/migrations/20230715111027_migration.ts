import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      alter table "user"
          add column created_at                 timestamptz,
          add column created_by_user_session_id uuid references "user_session";

update entity_action set action_name = 'insert' where action_name = 'create';

-- select "user".id, count("user".id) from "user" inner join entity_action on entity_action.entity_id = "user".id and entity_action.entity_name = 'user' and entity_action.action_name = 'insert' group by "user".id

-- select distinct action_name, count(action_name) from entity_action group by action_name;
-- 
-- 
--       select (select ue.created_at
--                                         from entity_action
--                                                  inner join user_event ue on ue.id = entity_action.user_event_id
--                                         where entity_action.entity_id = "user".id
--                                           and entity_action.entity_name = 'user'
--                                           and entity_action.action_name = 'insert' limit(1)) as created_at,
--            (select ue.user_session_id
--                                         from entity_action
--                                                  inner join user_event ue on ue.id = entity_action.user_event_id
--                                         where entity_action.entity_id = "user".id
--                                           and entity_action.entity_name = 'user'
--                                           and entity_action.action_name = 'insert' limit(1)) as created_by from "user";


      update "user"
      set created_at                 = (select ue.created_at
                                        from entity_action
                                                 inner join user_event ue on ue.id = entity_action.user_event_id
                                        where entity_action.entity_id = "user".id
                                          and entity_action.entity_name = 'user'
                                          and entity_action.action_name = 'insert' limit(1)),
          created_by_user_session_id = (select ue.user_session_id
                                        from entity_action
                                                 inner join user_event ue on ue.id = entity_action.user_event_id
                                        where entity_action.entity_id = "user".id
                                          and entity_action.entity_name = 'user'
                                          and entity_action.action_name = 'insert' limit(1));

alter table "user" alter created_at set default now();






      alter table "inquiry"
          add column created_at                 timestamptz,
          add column created_by_user_session_id uuid references "user_session";

      -- select "user".id, count("user".id) from "user" inner join entity_action on entity_action.entity_id = "user".id and entity_action.entity_name = 'user' and entity_action.action_name = 'insert' group by "user".id

-- select distinct action_name, count(action_name) from entity_action group by action_name;
-- 
--
--       select * from inquiry;
--       select (select ue.created_at
--                                         from entity_action
--                                                  inner join user_event ue on ue.id = entity_action.user_event_id
--                                         where entity_action.entity_id = "inquiry".id
--                                           and entity_action.entity_name = 'inquiry'
--                                           and entity_action.action_name = 'insert' limit(1)) as created_at,
--            (select ue.user_session_id
--                                         from entity_action
--                                                  inner join user_event ue on ue.id = entity_action.user_event_id
--                                         where entity_action.entity_id = "inquiry".id
--                                           and entity_action.entity_name = 'inquiry'
--                                           and entity_action.action_name = 'insert' limit(1)) as created_by from "inquiry";


      update "inquiry"
      set created_at                 = (select ue.created_at
                                        from entity_action
                                                 inner join user_event ue on ue.id = entity_action.user_event_id
                                        where entity_action.entity_id = "inquiry".id
                                          and entity_action.entity_name = 'inquiry'
                                          and entity_action.action_name = 'insert' limit(1)),
          created_by_user_session_id = (select ue.user_session_id
                                        from entity_action
                                                 inner join user_event ue on ue.id = entity_action.user_event_id
                                        where entity_action.entity_id = "inquiry".id
                                          and entity_action.entity_name = 'inquiry'
                                          and entity_action.action_name = 'insert' limit(1));

      alter table "inquiry" alter created_at set default now();

alter table inquiry alter created_at set not null, alter created_by_user_session_id set not null ;

  `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
