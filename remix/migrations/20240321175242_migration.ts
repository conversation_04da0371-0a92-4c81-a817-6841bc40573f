import { Kysely, sql } from "kysely";
import type { <PERSON> } from "~/kysely/db";

export async function up(db: Kysely<DB>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

          alter table form
              drop constraint if exists form_deleted_at_target_id_name_key ;

          alter table form
              add unique nulls not distinct (deleted_at, target_id, name);

          alter table form
              alter column target_id drop not null;


          alter table form
              add column filter_activity_slug text;

          alter table form
              add column filter_beginner_diver bool not null default false;

          alter table form
              alter column created_by_user_session_id drop not null;
          alter table field
              alter column created_by_user_session_id drop not null;

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
