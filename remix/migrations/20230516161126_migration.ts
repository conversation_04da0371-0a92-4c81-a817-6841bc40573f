import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table operator_location__user
              add column crew       bool default false,
              add column freelancer bool default false,
              add column admin      bool default false;

          update operator_location__user
          set admin = true
          where owner = true;

          --           alter table booking
--               add column start_datetime    timestamptz,
--               add column duration_in_hours int8;
--           update booking
--           set duration_in_hours = (extract(epoch from upper(duration) - lower(duration)) / 3600),
--               start_datetime    = lower(duration);
-- 
-- 
--           alter table booking
--               add column duration_new tstzrange generated always as (duration) stored not null;
--               
--           alter table booking
--               add column duration_new tstzrange generated always as (tstzrange(start_datetime, start_datetime +
--                                                                                            duration_in_hours *
--                                                                                            interval '1 hour',
--                                                                            '[)')) stored not null;
-- 
-- 
--           alter table booking
--               rename column duration_old to duration;
-- 
--           select *
--           from booking;
-- 
--           alter table booking
--               add column duration tstzrange generated always as (tstzrange(start_datetime, start_datetime +
--                                                                                            duration_in_hours *
--                                                                                            interval '1 hour',
--                                                                            '[)')) stored not null;
-- 
-- alter table booking drop column start_datetime, drop column duration_in_hours;
-- 
--           select duration,
--                  extract(epoch from upper(duration) - lower(duration)) / 3600 as hours,
--                  lower(duration)                                              as start_datetime
--           from booking;
--           select departure_time,
--                  tstzrange(departure_time, departure_time + null * INTERVAL '1 hour', '[)'),
--                  tstzrange(departure_time, null, '[)')
--           from boat_departure;


          delete
          from boat_departure
          where captain_user_id is not null;
          alter table boat_departure
              drop constraint boat_departure_captain_user_id_fkey;
          alter table boat_departure
              rename column captain_user_id to captain__operator_location__user_id;

          alter table operator_location__user
              alter column user_id drop not null;
          alter table operator_location__user
              alter column first_name set not null,
              alter column last_name set not null;

          alter table booking_schedule
              drop constraint booking_dayschedule_boat_departure_id_fkey;

          alter table user_schedule
              add column operator_location__user_id uuid references operator_location__user;
          update user_schedule
          set operator_location__user_id = (select operator_location__user.id
                                            from operator_location__user
                                            where operator_location__user.user_id = user_schedule.user_id
                                              and operator_location__user.operator_location_id =
                                                  user_schedule.operator_location_id);
delete from user_schedule where operator_location__user_id is null; 
alter table user_schedule alter column operator_location__user_id set not null, drop column user_id, drop column operator_location_id;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
