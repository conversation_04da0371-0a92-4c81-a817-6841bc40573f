import type { Kysely } from "kysely";
import { sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
--           select verified_at, old_user_session_id
--           from participant
--           where verified_at is not null;

          --           alter table participant
--               add column verified_at timestamptz;

          create table token
          (
              id                         uuid primary key     default gen_random_uuid(),
              target_id                  uuid        not null,
              created_at                 timestamptz not null default now(),
              created_by_user_session_id uuid        not null references user_session
          );

          create table session_link
          (
              id         uuid primary key     default gen_random_uuid(),
              session_id uuid        not null references session,
              target_id  uuid        not null,
              created_at timestamptz not null default now(),
              unique (session_id, target_id)
          );

          alter table token
              alter column created_by_user_session_id drop not null;

          alter table participant
              rename column user_session_id to old_user_session_id;
          alter table participant
              rename column user_session_created_at to old_user_session_created_at;

          insert into session_link (session_id, target_id, created_at)
          select user_session.session_id,
                 participant.id,
                 coalesce(participant.old_user_session_created_at, participant.created_at)
          from participant
                   inner join user_session on participant.old_user_session_id = user_session.id;

--           select *
--           from participant
--           where user_id is null
--             and user_session_id is not null;
--           select *
--           from participant
--           where user_session_id is not null
--             and user_session_created_at is not null;
          -- insert into session_link 


-- alter table participant rename column 
--           select *
--           from participant
--           where participant;

          --           alter table participant
--               add initiated_by_session_id uuid references session,
--               add token                   uuid default gen_random_uuid() not null;
-- 
--           update participant
--           set initiated_by_session_id = (select session_id
--                                          from user_session
--                                          where user_session.id = participant.user_session_id)
--           where participant.initiated_by_session_id is null;


          -- create table participation (
--     id uuid primary key,
--     created_at timestamptz default now() not null,
--     created_by_user_session_id uuid references user_session not null ,
-- )

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
