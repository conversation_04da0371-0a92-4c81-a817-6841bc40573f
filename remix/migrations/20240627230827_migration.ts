import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table xendit_account
              add column main boolean unique nulls distinct;
          alter table xendit_account
              alter column xendit_invoice_callback_token drop not null;

          create table xendit_virtual_bank_account
          (
              id                             uuid primary key default gen_random_uuid(),
              xendit_virtual_bank_account_id uuid                           not null unique,
              xendit_account_id              uuid references xendit_account not null,
              name                           text                           not null,
              account_number                 text                           not null unique,
              bank_code                      text                           not null,
              obj                            jsonb                          not null
--               created_obj                    jsonb                          not null,
--               latest_obj                     jsonb                          not null
--               merchant_code     text                           not null,
--               name              text                           not null,
--               is_closed         bool                           not null,
--               expiration_date   timestamptz                    not null,
--               is_single_use     bool                           not null,
--               status            text                           not null,
--               currency          text                           not null,
--               country           text                           not null

          );

          create table xendit_virtual_bank_account_payment
          (
              id                             uuid primary key default gen_random_uuid(),
              xendit_virtual_bank_account_id uuid references xendit_virtual_bank_account not null,
              xendit_payment_id              text                                        not null unique,
              obj                            jsonb                                       not null
          );

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
