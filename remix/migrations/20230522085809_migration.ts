import { Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          drop table boat_schedule;
          alter table location_schedule
              add column boat_id uuid;

          alter table booking_schedule
              rename column boat_departure_id__location_schedule_id to location_schedule_id;

          alter table booking_schedule
              drop column instructor_id;

          alter table booking_schedule
              rename to booking_location_schedule;

          delete
          from location_schedule;
          alter table location_schedule
              add column type               text not null,
              add column departure_location text;

          create table location_user_schedule
          (
              id                         uuid primary key,
              location_schedule_id       uuid references location_schedule on delete cascade not null,
              operator_location__user_id uuid                                                not null,
              crew                       boolean                                             not null default false,
              captain                    boolean                                             not null default false,
              instructor                 boolean                                             not null default false
          );

          alter table booking_registration
              drop constraint booking_registration_booking_id_fkey;

          alter table booking_location_schedule
              drop constraint booking_dayschedule_booking_id_fkey;

alter table location_schedule drop column shore;

-- alter table location_user_schedule add column booking_id
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
