import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `alter table booking
          add column cached_duration datemultirange;

      create index on booking (establishment_id, cached_duration);

      -- 
-- select range_agg(activity.duration)  from activity where lower(activity.duration) = '2024-08-04' or lower(activity.duration) = '2024-08-01' limit 30;
-- 
--       select (select range_agg(activity.duration)
--                       from activity
--                       where activity.booking_id = booking.id) from booking limit 10;
      update booking
      set cached_duration = (select range_agg(activity.duration)
                             from activity
                             where activity.booking_id = booking.id)`,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
