import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
          alter table diving_course
              add column tag text;
          drop trigger set_node on diving_course;
          create table mutation_event
          (
              id         uuid primary key default gen_random_uuid() not null,
              created_at timestamptz      default now()             not null,
              user_id    text references "user" (id),
              target_id  uuid,
              table_name text,
              name       text,
              payload    jsonb
          );
          drop trigger set_node on diving_certificate_level;
          drop trigger set_node on diving_certificate_organization;
      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
