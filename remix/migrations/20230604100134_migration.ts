import type { Kysely } from "kysely";
import { sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table booking_registration
              rename to participant;
          alter table booking_registration_addon
              rename to participant_addon;
          alter table participant_addon
              rename column booking_registration_id to participant_id;
          update entity_action
          set entity_name = 'participant'
          where entity_name = 'booking_registration';
          update entity_action
          set entity_name = 'participant_addon'
          where entity_name = 'booking_registration_addon';
          alter table participant
              add column created_by_user_session_id uuid references user_session;
          update participant
          set created_by_user_session_id = (select user_session.id
                                            from user_session
                                                     inner join user_event on user_session.id = user_event.user_session_id
                                                     inner join entity_action on entity_action.user_event_id = user_event.id
                                            where entity_action.entity_id = participant.id
                                              and entity_action.action_name = 'create')
          where created_by_user_session_id is null;
          delete
          from participant
          where created_by_user_session_id is null;
          alter table participant
              alter created_by_user_session_id set not null;

          alter table operator_location
              rename to establishment;
          alter table operator_location__language
              rename to establishment__language;
          alter table establishment__language
              rename column operator_location_id to establishment_id;
          alter table inquiry
              rename column operator_location_id to establishment_id;
          alter table product
              rename column operator_location_id to establishment_id;
          alter table review
              rename column operator_location_id to establishment_id;
          alter table addon
              rename column operator_location_id to establishment_id;
          alter table boat
              rename column operator_location_id to establishment_id;
          alter table member
              rename column operator_location_id to establishment_id;
          alter table booking
              rename column operator_location_id to establishment_id;
          alter table trip
              rename column operator_location_id to establishment_id;

          alter table product__diving_course
              add column id uuid default gen_random_uuid() unique not null;
          alter table product__diving_location
              add column id uuid default gen_random_uuid() unique not null;
          alter table product__diving_site
              add column id uuid default gen_random_uuid() unique not null;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
