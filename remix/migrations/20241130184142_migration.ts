import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table member
              add column deleted_at                 timestamptz,
              add column deleted_by_user_session_id uuid references user_session,
              drop column old_first_name,
              drop column old_last_name;

delete from addon where establishment_id is null;
alter table addon alter column establishment_id set not  null ;
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
