import type { Kysely } from "kysely";
import { sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          create table inquiry
          (
              id                   uuid not null primary key,
              operator_location_id uuid not null references operator_location,
              product_id           uuid not null references product,
              question             text,
              communication_method text not null,
              nr_of_participants   int  not null,
              date                 date,
              flexible_date        bool
          );
          alter table inquiry
              alter column product_id drop not null;
          alter table inquiry
              alter column nr_of_participants drop not null;

      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
