import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
      --       select id, duration, duration_in_hours, upper(duration_in_hours) from product;
      
      alter table product drop column duration;
      
      update product
      set duration_in_hours = '[7,25)'::int4range
      where lower(duration_in_hours) = 7
        and upper(duration_in_hours) is null;
-- update 
  `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
