import { <PERSON>ys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table booking
              add column pickup_time      time,
              add column duration_in_days daterange;

          update booking
          set duration_in_days = daterange((lower(booking.duration) at time zone 'Asia/Makassar')::date,
                                           (upper(booking.duration) at time zone 'Asia/Makassar')::date, '[)'),
              pickup_time      = (lower(booking.duration) at time zone 'Asia/Makassar')::time
          where booking.duration_in_days is null;
-- select pickup_time, duration_in_days from booking;

          update booking
          set duration_in_days = daterange((lower(booking.duration) at time zone 'Asia/Makassar')::date,
                                           (upper(booking.duration) at time zone 'Asia/Makassar')::date, '[]')
          where (lower(booking.duration) at time zone 'Asia/Makassar')::date =
                (upper(booking.duration) at time zone 'Asia/Makassar')::date;

          alter table booking
              rename column duration to duration_old;
          alter table booking
              rename column duration_in_days to duration;

          alter table trip
              add column date date;
          alter table trip
              rename column start_time to start_time_old;
          alter table trip
              add column start_time time;


          select trip.id,
                 trip.start_time_old,
                 (trip.start_time_old at time zone 'Asia/Makassar')::date,
                 (trip.start_time_old at time zone 'Asia/Makassar')::time
          from trip;

          update trip
          set date       = (trip.start_time_old at time zone 'Asia/Makassar')::date,
              start_time = (trip.start_time_old at time zone 'Asia/Makassar')::time
          where date is null;

-- select * from tri

          alter table trip
              alter column start_time set not null;
          alter table trip
              alter column date set not null;

          select trip.date - trip.date,
                 trip.date - lower(booking.duration),
                 '2023-09-30'::date - lower (booking.duration),
                 trip.date,
                 trip.date + 1,
                 trip.*
          from trip
                   inner join trip_assignment on trip.id = trip_assignment.trip_id
                   inner join public.participant p on p.id = trip_assignment.participant_id
                   inner join booking on booking.id = p.booking_id;

          alter table booking
              alter column duration_old drop not null;

          alter table trip
              alter column start_time_old drop not null;
          select *
          from trip;
          -- 
-- select * from booking;
-- 
--           select booking.id,
--                  lower(booking.duration)::time,
--                  (lower(booking.duration) at time zone 'Asia/Makassar')::time,
--                  (lower(booking.duration) at time zone 'Asia/Makassar')::date,
--                  (upper(booking.duration) at time zone 'Asia/Makassar')::date,
--                  daterange((lower(booking.duration) at time zone 'Asia/Makassar')::date,
--                            (upper(booking.duration) at time zone 'Asia/Makassar')::date, '[)'),
--                  booking.duration
--           from booking;
--           select booking.id,
--                  daterange((lower(booking.duration) at time zone 'Asia/Makassar')::date,
--                            (upper(booking.duration) at time zone 'Asia/Makassar')::date, '[]'),
--                  booking.duration
--           from booking
--           where lower(booking.duration)::date = upper(booking.duration)::date
--             and booking_reference = 'blabla';
--           select '[2023-01-01,2023-01-05)'::daterange;
--           select upper('[2023-01-01,2023-01-05)'::daterange) - lower('[2023-01-01,2023-01-05)'::daterange)
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
