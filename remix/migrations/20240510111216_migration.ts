import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          create table comment
          (
              id                         uuid primary key,
              date                       date                               not null,
              created_at                 timestamptz                        not null default now(),
              created_by_user_session_id uuid references user_session (id) not null ,
              content                    text                               not null,
              participation_id           uuid references participation (id) not null
          );

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
