import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
        -- Your SQL statements here
        create table import
        (
          id                         uuid primary key                      default gen_random_uuid(),
          created_at                 timestamptz                  not null default now(),
          created_by_user_session_id uuid references user_session not null,
          name                       text                         not null unique,
          rows                       jsonb
        );

        create table import_row
        (
          id             uuid primary key default gen_random_uuid(),
          import_id      uuid references import not null,
          participant_id uuid,
          data           jsonb                  not null
        );

        alter table participant
          add column import_row_ids uuid[];
      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `
    )
    .execute(db);
}
