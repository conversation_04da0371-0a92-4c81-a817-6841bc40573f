import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `

          drop table booking;
          create table booking
          (
              id                         uuid primary key                                default gen_random_uuid() not null,
              product_id                 uuid references product (id),
              operator_location_id       uuid references operator_location (id) not null,
              created_at                 timestamptz                            not null default now(),
              created_by_user_session_id uuid references user_session (id)
          );

          create table booking_event
          (
              booking_id                 uuid references booking (id) not null,
              created_at                 timestamptz                  not null default now(),
              created_by_user_session_id uuid references user_session (id),
              as_operator                bool                         not null,
              data                       jsonb,
              PRIMARY KEY (booking_id, created_at)
          );


-- select st_x(geom) || ',' || st_y(geom) as coordinates, st_astext(geom) from diving_site where geom is not null;

--           alter table diving_site
--               add column snorkel_image text,
--               add column diving_image  text
--               add column high;

      `
    )
    .execute(db);
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
}
