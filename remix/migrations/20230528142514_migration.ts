import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table boat add column location text;
alter table trip add column boat_location text;
alter table booking drop column meeting_location;
`
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
