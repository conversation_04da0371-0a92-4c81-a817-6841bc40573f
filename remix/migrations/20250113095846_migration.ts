import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON>ely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      alter table establishment add column default_booking_meeting_type text;
      `,
    )
    .execute(db);
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
