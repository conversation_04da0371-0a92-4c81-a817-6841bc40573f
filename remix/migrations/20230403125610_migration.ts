import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

          -- 668db541-2c54-490e-8b90-59cf030d66c6,Philippines,PH
-- 603417ba-8343-429a-b829-75d835e54a36,Thailand,TH
-- 3ceefbe2-bdfc-43f1-bc5e-a015d7d5650a,Indonesia,ID

          alter table region
              add column country_code char(2);

          update region
          set country_code = 'PH'
          where region.country_id = '668db541-2c54-490e-8b90-59cf030d66c6';
          update region
          set country_code = 'TH'
          where region.country_id = '603417ba-8343-429a-b829-75d835e54a36';
          update region
          set country_code = 'ID'
          where region.country_id = '3ceefbe2-bdfc-43f1-bc5e-a015d7d5650a';

          alter table region
              alter column country_code set not null;

          alter table region
              drop column country_id;


          alter table booking_registration
              add column country_code char(2);

          update booking_registration
          set country_code = 'PH'
          where booking_registration.country_id = '668db541-2c54-490e-8b90-59cf030d66c6';
          update booking_registration
          set country_code = 'TH'
          where booking_registration.country_id = '603417ba-8343-429a-b829-75d835e54a36';
          update booking_registration
          set country_code = 'ID'
          where booking_registration.country_id = '3ceefbe2-bdfc-43f1-bc5e-a015d7d5650a';

          alter table booking_registration
              drop column country_id;

          alter table booking_registration
              alter column country_code set not null;

          drop table country;


      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
