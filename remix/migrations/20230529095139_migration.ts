import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table trip rename column max_no_participants to capacity;
alter table trip rename column diving_location to activity_location;
alter table trip rename column departure_location to start_location;
alter table trip drop column boat_location;
alter table trip rename column departure_time to start_time;
update trip set type = 'dry' where type = 'divecenter';
alter table user_schedule
    drop constraint user_schedule_operator_location__user_id_fkey;

alter table user_schedule
    add foreign key (member_id) references member
        on delete cascade;

alter table booking add column pickup_location text;
`
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
