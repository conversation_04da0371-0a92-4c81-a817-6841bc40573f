import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table xendit_account
              drop constraint xendit_account_main_key;

          alter table xendit_account
              add unique (xendit_environment_id, main);


          create table xendit_api_key
          (
              id                    uuid primary key                            default gen_random_uuid(),
              xendit_environment_id uuid references xendit_environment not null,
              xendit_api_key        text                               not null,
              production            bool                               not null,
              created_at            timestamptz                        not null default now()
          );

          insert into xendit_api_key (xendit_environment_id, xendit_api_key, production)
          select xendit_environment.id, xendit_environment.xendit_api_key, xendit_environment.production
          from xendit_environment;

          alter table xendit_account
              add column production bool;

          update xendit_account
          set production = (select xendit_environment.production
                            from xendit_environment
                            where xendit_environment.id = xendit_account.xendit_environment_id)
          where xendit_account.production is null;

          alter table xendit_account
              alter column production set not null;

          alter table xendit_environment
              alter column production drop not null;
          alter table xendit_environment
              alter column xendit_api_key drop not null;

          alter table xendit_environment
              rename column production to production_old;
          alter table xendit_environment
              rename column xendit_api_key to xendit_api_key_old;


          --           create table category
--           (
--               id                    uuid primary key,
--               establishment_id      uuid references establishment,
--               name                  text not null,
--               slug                  text not null,
--               default_activity_slug text,
--           );
-- 
--           insert into category (establishment_id, name, slug, default_activity_slug)
--           select bla.establishment_id, bla.activity_slug, bla.activity_slug, bla.activity_slug
--           from (select distinct product.establishment_id, product.activity_slug from product) as bla;

          -- Your SQL statements here
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
