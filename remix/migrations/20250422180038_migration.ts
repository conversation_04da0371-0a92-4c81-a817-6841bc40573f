import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
        -- Create item table with fields moved from product
        CREATE TABLE IF NOT EXISTS item
        (
          id                           UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          created_at                   timestamptz      DEFAULT now(),
          activity_slug                TEXT                          NOT NULL,
          establishment_id             UUID references establishment NOT NULL,
          title                        TEXT,
          subtitle                     TEXT,
          description                  TEXT,
          tag_id                       UUID references tag,
          category_id                  UUID references category,
          form_root_id                 UUID references form,
          minimum_age                  INTEGER,
          cancellation_policy_in_hours INTEGER,
          diving_minimum_logged_dives  NUMERIC,
          info                         TEXT,
          addon_ids                    UUID[],
          addons_description           TEXT,
          brand                        TEXT
        );

        -- Add new columns to product table
        ALTER TABLE product
          ADD COLUMN color   TEXT,
          ADD COLUMN size    TEXT,
          add column item_id uuid not null default gen_random_uuid();

        alter table product
          alter column item_id drop default;

        -- Migrate data from product to item
        INSERT INTO item (id,
                          created_at,
                          activity_slug,
                          establishment_id,
                          title,
                          subtitle,
                          description,
                          tag_id,
                          category_id,
                          form_root_id,
                          minimum_age,
                          cancellation_policy_in_hours,
                          diving_minimum_logged_dives,
                          info,
                          addon_ids,
                          addons_description)
        SELECT product.item_id,
               product.created_at,
               product.activity_slug,
               product.establishment_id,
               product.title,
               product.subtitle,
               product.description,
               product.tag_id,
               product.category_id,
               product.form_root_id,
               product.minimum_age,
               product.cancellation_policy_in_hours,
               product.diving_minimum_logged_dives,
               product.info,
               product.addon_ids,
               product.addons_description
        FROM product;

        -- Add foreign key constraint for item_id
        ALTER TABLE product
          ADD CONSTRAINT fk_product_item
            FOREIGN KEY (item_id)
              REFERENCES item (id);



        alter table product__diving_course
          rename to item__diving_course;
        alter table item__diving_course
          add column item_id uuid references item;
        update item__diving_course
        set item_id = (select product.item_id from product where product.id = item__diving_course.product_id);

        alter table item__diving_course
          alter column item_id set not null;

        alter table item__diving_course
          drop constraint diving_course_product__diving_course_pkey;

        alter table item__diving_course
          drop constraint product__diving_course_id_key;

--         drop index diving_course_product__diving_course_pkey;
--         drop index product__diving_course_id_key;

        alter table item__diving_course
          add unique (item_id, diving_course_id);
        alter table item__diving_course
          add primary key (id);

        alter table item__diving_course
          alter column product_id drop not null;
        alter table item__diving_course
          rename column product_id to product_id_old;


        -- rename columns from product that have been moved to item
        alter table product
          rename COLUMN activity_slug to activity_slug_old;
        alter table product
          rename COLUMN establishment_id to establishment_id_old;
        alter table product
          rename COLUMN title to title_old;
        alter table product
          rename COLUMN subtitle to subtitle_old;
        alter table product
          rename COLUMN description to description_old;
        alter table product
          rename COLUMN tag_id to tag_id_old;
        alter table product
          rename COLUMN category_id to category_id_old;
        alter table product
          rename COLUMN form_root_id to form_root_id_old;
        alter table product
          rename COLUMN minimum_age to minimum_age_old;
        alter table product
          rename COLUMN cancellation_policy_in_hours to cancellation_policy_in_hours_old;
        alter table product
          rename COLUMN diving_minimum_logged_dives to diving_minimum_logged_dives_old;
        alter table product
          rename COLUMN info to info_old;
        alter table product
          rename COLUMN addon_ids to addon_ids_old;
        alter table product
          rename COLUMN addons_description to addons_description_old;

        alter table product
          alter COLUMN establishment_id_old drop not null, alter column addon_ids_old drop not null, alter column activity_slug_old drop not null ;
          
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      `,
    )
    .execute(db);
}
