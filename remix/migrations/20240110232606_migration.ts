import type { Kysely } from "kysely";
import { sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table product
              add column forms text[];
          alter table product
              rename column liability_form_enabled to liability_form_enabled_old;
          alter table product
              rename column medical_form_enabled to medical_form_enabled_old;
          alter table product
              rename column safety_diving_form_enabled to safety_diving_form_enabled_old;

          update product
          set forms = array_append(forms, 'liability')
          where product.liability_form_enabled_old = true;

          update product
          set forms = array_append(forms, 'medical')
          where product.medical_form_enabled_old = true;

          update product
          set forms = array_append(forms, 'safety_diving')
          where product.safety_diving_form_enabled_old = true;


          insert into signature (target_id, form_name, signature, signed_at, signed_by)
          select participant.id,
                 form.name,
                 participant.signature,
                 form.created_at,
                 participant.first_name || ' ' || participant.last_name
          from participant
                   inner join form on form.target_id = participant.id
          where form.name = 'medical'
            and participant.signature is not null;

          alter table participant
              rename column signature to signature_old;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
