import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table booking
              alter column vat_rate drop not null;
          update booking
          set vat_rate = null;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
