import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table participant_waiver add column approved boolean not null default false;


update participant_waiver set approved = true where medical_evaluation_required = false;
update participant_waiver set approved = false where medical_evaluation_required = true;

alter table participant_waiver alter column approved drop default ;

-- select * from participant_waiver where participant_waiver.medical_evaluation_required = false and participant_waiver.id in (select signature.participant_waiver_id from signature);
`,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
