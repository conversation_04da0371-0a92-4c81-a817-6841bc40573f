import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          update participant_waiver
          set signature_required = (select waiver.signature_required
                                    from waiver
                                    where waiver.id = participant_waiver.waiver_id)
          where signature_required is null;

          alter table participant_waiver
              alter column signature_required set not null;
`,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
