import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          create table waiver_translation
          (
              id            uuid                   not null primary key default gen_random_uuid(),
              waiver_id     uuid references waiver not null,
              name          text                   not null,
              markdoc       text                   not null,
              language_code text                   not null,
              unique (waiver_id, language_code)
          );

          insert into waiver_translation (id, waiver_id, name, markdoc, language_code)
          select gen_random_uuid(), waiver.id, waiver.name, waiver.markdoc, waiver.language_code
          from waiver;

          alter table waiver
              drop constraint waiver_slug_language_code_establishment_id_diving_certifica_key;
          alter table waiver
              add unique nulls not distinct (establishment_id, slug);

          alter table waiver
              rename column markdoc to markdoc_old;
          alter table waiver
              rename column content to content_old;
          alter table waiver
              rename column name to name_old;
          alter table waiver
              rename column language_code to language_code_old;

          alter table waiver
              alter column markdoc_old drop not null ;
          alter table waiver
              alter column content_old drop not null ;
          alter table waiver
              alter column name_old drop not null ;
          alter table waiver
              alter column language_code_old drop not null ;


      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
