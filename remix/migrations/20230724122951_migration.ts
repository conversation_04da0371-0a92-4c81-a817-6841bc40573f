import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          -- drop table if exists trip_site;
--           create table trip_site
--           (
--               id                         uuid primary key,
--               trip_id                    uuid references trip on delete cascade on update restrict not null,
--               name                       text,
--               created_at                 timestamptz                                               not null default now(),
--               created_by_user_session_id uuid references user_session                              not null
--           );
-- 
--           drop table trip_site;
          alter table trip
              add column sites text[];
--           select *
--           from trip_site;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
