import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table participant_waiver
              add column customer_id uuid references customer;

          update participant_waiver
          set customer_id = (select participant.customer_id
                             from participant
                             where participant.id = participant_waiver.participant_id);

          alter table participant_waiver
              alter column customer_id set not null;

          create table participation_waiver
          (
              id                    uuid primary key                   not null,
              participant_waiver_id uuid references participant_waiver not null,
              participant_id        uuid references participant        not null,
              activity_id           uuid references activity,
              manually_approved     bool                               not null default false,
              unique nulls not distinct (participant_id, participant_waiver_id, activity_id) 
          );

          alter table participant_waiver
              alter column medical_evaluation_required set not null;

          alter table participant_waiver
              alter column approved drop default;
          alter table participant_waiver
              add column medical_approved bool;
          update participant_waiver
          set medical_approved = approved;
          alter table participant_waiver
              alter column medical_approved set not null;
          alter table participant_waiver
              alter column medical_approved set default false;


          -- 
--           insert into participation_waiver
--           select *
--           from participant_waiver
--           where participant_waiver.approved = true
--              or participant_waiver.approved = false;

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
