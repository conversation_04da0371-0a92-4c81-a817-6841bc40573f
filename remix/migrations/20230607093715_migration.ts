import type { Kys<PERSON> } from "kysely";
import { sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `


          create table answer
          (
              id        uuid primary key,
              target_id uuid not null,
              form_name text not null,
              question  text not null,
              answer    text not null,
              unique (target_id, form_name, question)
          );

          create table signature
          (
              id        uuid primary key,
              target_id uuid        not null,
              form_name text        not null,
              signature json        not null,
              signed_at timestamptz not null,
              unique (target_id, form_name)
          );

          drop table medical;

-- select to_jsonb(answer.*) as json, * from answer;
--           select *
--           from answer;
-- select *, to_jsonb("participant".*)::jsonb from participant limit 10;
-- update answer set answer = 'yes'
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
