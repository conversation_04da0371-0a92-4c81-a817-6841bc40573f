import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table "participant"
              add column user_id uuid references "user";
          update participant
          set user_id = (select user_id from user_session where user_session.id = participant.user_session_id);

          alter table participant
              add column establishment_id uuid references establishment;
          update participant
          set establishment_id = (select booking.establishment_id
                                  from booking
                                  where participant.booking_id = booking.id)
          where participant.establishment_id is null;
          -- select * from participant where establishment_id is null;
--           alter table participant
--               alter column establishment_id set not null;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
