import { Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          --           select count(*)
--           from product
--           where deleted_at is not null;

          alter table product
              add column root_id uuid;
          update product
          set root_id = product.id
          where product.root_id is null;

          alter table product
              alter column root_id set not null;

          update product
          set deleted_at = 'infinity'
          where deleted_at is null;

          alter table product
              alter column deleted_at set not null;

          alter table product
              add unique (root_id, deleted_at);

          alter table product
              add column created_at timestamptz not null default '-infinity';
          alter table product
              alter column created_at drop default;

          alter table product__diving_course
              add column created_at timestamptz not null default '-infinity';
          alter table product__diving_course
              alter column created_at drop default;

          alter table product__diving_location
              add column created_at timestamptz not null default '-infinity';
          alter table product__diving_location
              alter column created_at drop default;

          alter table product__diving_site
              add column created_at timestamptz not null default '-infinity';
          alter table product__diving_site
              alter column created_at drop default;

          alter table product
              alter column created_at set default now();

          alter table product__diving_course
              alter column created_at set default now();

          alter table product__diving_location
              alter column created_at set default now();

          alter table product__diving_site
              alter column created_at set default now();


          update product
          set addons = '{}'::uuid[]
          where addons is null;
          alter table product
              alter column addons set not null;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
