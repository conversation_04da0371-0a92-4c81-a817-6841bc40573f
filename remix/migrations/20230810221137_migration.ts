import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table payment
              add column deleted_at                 timestamptz,
              add column deleted_by_user_session_id uuid references user_session;

          alter table payment
              drop column deposit;

          alter table payment
              add column url text;

          insert into payment (id, booking_id, method, amount, currency, payed_at, created_at,
                               created_by_user_session_id)
          select gen_random_uuid(),
                 booking.id,
                 'other',
                 booking.paid_amount,
                 booking.currency_id,
                 null,
                 booking.created_at,
                 booking.created_by_user_session_id
          from booking
          where booking.paid_amount is not null;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
