import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table review
              add column published_at timestamptz,
              add column published_by uuid references user_session;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
