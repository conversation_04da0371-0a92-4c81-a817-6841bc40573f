import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table waiver
              add column validity_duration interval;

          create table person
          (
              id         uuid primary key default gen_random_uuid(),
              first_name text                   not null,
              last_name  text                   not null,
              user_id    uuid references "user" not null,
              unique (first_name, last_name, user_id)
          );

          create table customer
          (
              id               uuid primary key default gen_random_uuid(),
              establishment_id uuid not null references establishment,
              person_id        uuid not null references person,
              unique (establishment_id, person_id)
          );

          insert into person (first_name, last_name, user_id)
          SELECT participant.first_name, participant.last_name, participant.user_id
          from participant
          on conflict do nothing;

          insert into customer (establishment_id, person_id)
          select participant.establishment_id,
                 (select person.id
                  from person
                  where person.user_id = participant.user_id
                    and person.first_name = participant.first_name
                    and person.last_name = participant.last_name) as person_id
          from participant
          on conflict do nothing;

          alter table participant
              add column customer_id uuid references customer;

          update participant
          set customer_id = (select customer.id
                             from customer
                             where customer.establishment_id = participant.establishment_id
                               and customer.person_id = (select person.id
                                                         from person
                                                         where person.user_id = participant.user_id
                                                           and person.first_name = participant.first_name
                                                           and person.last_name = participant.last_name))
          where participant.customer_id is null;

          alter table participant
              alter column customer_id set not null,
              alter column first_name drop not null,
              alter column last_name drop not null,
              alter column user_id drop not null,
              alter column establishment_id drop not null;

          alter table participant
              rename column first_name to first_name_old;
          alter table participant
              rename column last_name to last_name_old;
          alter table participant
              rename column user_id to user_id_old;
          alter table participant
              rename column establishment_id to establishment_id_old;

          select count(*)
          from participant;

          select count(*)
          from participant
                   inner join customer on customer.id = participant.customer_id
                   inner join person on person.id = customer.person_id
          where participant.first_name_old = person.first_name
            and participant.last_name_old = person.last_name
            and participant.user_id_old = person.user_id
            and participant.establishment_id_old = customer.establishment_id;

select count(person.id) from person inner join customer c on person.id = c.person_id;
-- select * from participant as particpant;
          -- select * from waiver where validity_duration is not null;
-- select '2 mons'::interval;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
