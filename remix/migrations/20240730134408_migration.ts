import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table participant_waiver
              drop constraint if exists participant_waiver_participant_id_waiver_id_key;

          alter table participant_waiver
              add column validity_duration interval;


`,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
