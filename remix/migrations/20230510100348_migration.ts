import type { Kysely } from "kysely";
import { sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
        create table booking_registration_addon
        (
            id                      uuid                  not null primary key,
            addon_id                uuid                  not null,
            booking_registration_id uuid                  not null
                references booking_registration
                    on delete cascade,
            amount                  integer default 0     not null
        );
alter table booking_registration drop column addons;
    `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
