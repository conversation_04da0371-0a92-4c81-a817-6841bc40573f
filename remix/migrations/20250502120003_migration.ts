import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

        -- Your SQL statements here
        alter table product
          add unique nulls not distinct (deleted_at, item_id, color, size);

        alter table product
          rename column sku to external_identifier;

        alter table product
          add column cached_establishment_id uuid references establishment;

        update product
        set cached_establishment_id = (select item.establishment_id from item where item.id = product.item_id);

        alter table product
          alter column cached_establishment_id set not null;

        alter table product
          add column sku text,
          add unique (deleted_at, cached_establishment_id, sku);

        alter table payment
          add column xendit_account_id uuid references xendit_account,
          add column host              text;



        update payment
        set xendit_account_id = (select establishment.xendit_account_id
                                 from payment_method
                                        inner join establishment on payment_method.establishment_id = establishment.id
                                 where payment_method.xendit = true
                                   and payment.payment_method_id = payment_method.id
                                   and payment.xendit_invoice_id is not null),
            host              = (select callback.host from callback where callback.target_id = payment.id limit 1);

        select count(*)
        from payment
        where payment.host is not null;

        -- select count(*) from payment where xendit_invoice_id is not null;
-- select count(*) from payment inner join payment_method on payment.payment_method_id = payment_method.id where payment_method.xendit = true;
-- select count(*) from payment where xendit_account_id is not null;

        -- select count(*) from payment where payment.id in (select callback.target_id from callback)


        --         select *
--         from payment
--                inner join payment_method on payment.payment_method_id = payment_method.id
--                inner join establishment on payment_method.establishment_id = establishment.id
--                inner join operator on establishment.operator_id = operator.id
--         where payment.xendit_invoice_id is null and payment_method.xendit = true;
--           and establishment.xendit_account_id is null;

        --         update

--           alter table 

--         create table partner
--         (
--           id        uuid primary key,
--           name      text not null,
--           website   website,
--           telephone text
--         );
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
