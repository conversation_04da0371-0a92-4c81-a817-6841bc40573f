import type { Kys<PERSON> } from "kysely";
import { sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      alter table trip_assignment
          add column role text;
      update trip_assignment
      set role = 'instructor'
      where instructor = true;
      update trip_assignment
      set role = 'crew'
      where crew = true;
      update trip_assignment
      set role = 'captain'
      where captain = true;
      delete
      from trip_assignment
      where role is null;
      alter table trip_assignment
          alter column role set not null,
          drop column instructor,
          drop column crew,
          drop column captain;

  `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
