import type { Kysely } from "kysely";
import { sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          create table "boat"
          (
              id                   uuid primary key default gen_random_uuid() not null,
              name                 varchar(255)                               not null,
              capacity             int                                        not null,
              operator_location_id uuid references operator_location (id)     not null
          );

          create table "operator_location__user"
          (
              id                   uuid primary key                       not null default gen_random_uuid(),
              user_id              uuid references "user" (id)            not null,
              operator_location_id uuid references operator_location (id) not null,
              owner                boolean                                not null default false,
              instructor           boolean                                not null default false,
              captain              boolean                                not null default false,
              first_name           text,
              last_name            text
          );

          insert into operator_location__user (id, user_id, operator_location_id, first_name, last_name, owner)
          select id, user_id, operator_location_id, first_name, last_name, true
          from owner;

          drop function if exists get_owner();
          drop function if exists get_owner_ids();
          drop table owner;

          create table booking_addon
          (
              id                uuid                         not null default gen_random_uuid() primary key,
              addon_id          uuid                         not null,
              booking_id        uuid references booking (id) not null,
              name              text                         not null,
              price             numeric                      not null,
              price_currency    text references currency (id),
              duration_in_hours numeric,
              unit              text                         not null,
              amount            int                          not null default 0
          );

          alter table booking
              add column duration tstzrange;


          alter table booking
              add column "from" date,
              add "to"          date;

          alter table booking_addon
              add column show bool not null default false;

          alter table booking
              add column activity_slug text;
-- alter table booking add column created_by_user_session_id uuid references user_session (id);

          alter table operator_location
              add column "booking_enabled" boolean default false;


          create table boat_departure
          (
              id              uuid primary key not null,
              boat_id         uuid references boat (id),
              departure_time  timestamptz      not null,
              diving_location text             not null,
              captain_user_id uuid references "user" (id)
          );

          create table booking__boat_departure
          (
              id                uuid primary key not null,
              booking_id        uuid references booking (id),
              boat_departure_id uuid references boat_departure (id)
          );

          alter table booking__boat_departure
              add constraint unique_booking__boat_departure unique (booking_id, boat_departure_id);

          alter table booking_addon
              drop constraint booking_addon_booking_id_fkey;

          alter table booking_addon
              add foreign key (booking_id) references booking
                  on delete cascade;


          create table booking_registration
          (
              id                        uuid primary key                  not null default gen_random_uuid(),
              created_at                timestamptz                       not null default now(),
              for_user_session_id       uuid references user_session (id) not null,
              on_behalf_of              boolean                           not null default false,

              booking_id                uuid references booking (id)      not null,
              first_name                text                              not null,
              last_name                 text                              not null,
              phone                     text                              not null,
              emergency_contact_name    text                              not null,
              emergency_contact_phone   text                              not null,
              country_id                uuid references country (id)      not null,
              address                   text                              not null,
              passport_number           text                              not null,
              birth_date                date                              not null,
              diet                      text,

              height_in_cm              int,
              shoe_size                 text,

              diving_certificate_level  text,
              diving_certificate_number text,
              number_of_dives           int,
              last_dived_at             timestamptz,
              wetsuit_size              text,
              weight_in_kg              int
          );

          alter table booking_registration
              rename column on_behalf_of to on_my_behalf;

          alter table booking
              add column message text;

          alter table booking
              add column number_of_participants int not null default 1;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
