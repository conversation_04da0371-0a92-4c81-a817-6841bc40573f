import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table user_event
              add column session_id uuid references session;
          alter table user_event
              add check ( user_session_id is null or session_id is null);

alter table user_session alter column currency_switched drop not null ;
alter table user_session alter column currency_switched drop default ;
update user_session set currency_switched = null where currency_switched = false;
update entity_action set action_name = 'update' where action_name = 'soft_delete';
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
