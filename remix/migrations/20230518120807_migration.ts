import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table boat_departure add column crew__operator_location__user_ids uuid[];
alter table boat_departure rename to boat_schedule;
alter table boat_schedule add column departure_location text;
`
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
