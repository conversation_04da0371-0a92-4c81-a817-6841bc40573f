import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          delete from  participant
          where participant.booking_id_old is not null
            and participant.id not in (select participant_waiver.participant_id from participant_waiver)
--             and (participant.id not in (select participant_waiver.participant_id from participant_waiver) or
--                  (select count(*) < 2
--                   from customer
--                            inner join participant p2 on customer.id = p2.customer_id
--                   where customer.id = participant.customer_id))
            and not exists(select participation.id
                           from participation
                           where participation.participant_id = participant.id);



          select operator.name,
                 participant.id                                                     as participant_id,
                 participant.booking_id_old                                         as booking_id,
                 person.first_name,
                 person.last_name,
                 "user".email,
                 'https://traveltruster.com/participant/' || participant.id         as participant_url,
                 'https://traveltruster.com/booking/' || participant.booking_id_old as booking_url
          from participant
                   inner join customer on participant.customer_id = customer.id
                   inner join person on customer.person_id = person.id
                   inner join "user" on person.user_id = "user".id
                   inner join establishment on customer.establishment_id = establishment.id
                   inner join operator on establishment.operator_id = operator.id
          where participant.booking_id_old is not null
--             and (select count(*) > 1
--                  from customer
--                           inner join participant p2 on customer.id = p2.customer_id
--                  where customer.id = participant.customer_id)
            and participant.id in (select participant_waiver.participant_id from participant_waiver)
            and not exists
              (select participation.id from participation where participation.participant_id = participant.id);


      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
