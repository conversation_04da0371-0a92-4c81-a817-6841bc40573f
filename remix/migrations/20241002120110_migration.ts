import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      alter table payment
          add column intuit_payment_id text;

select * from payment;
update payment set deleted_at = 'infinity' where deleted_at is null;
alter table payment alter column deleted_at set not null ;
  `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
