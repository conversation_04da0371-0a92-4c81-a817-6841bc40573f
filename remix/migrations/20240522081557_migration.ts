import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table participant_waiver alter column approved set default false;
update participant_waiver set approved = false where medical_evaluation_required = false;
`,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
