import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          update waiver_translation
          set name = 'Diver Medical'
          where waiver_translation.language_code = 'en'
            and waiver_translation.waiver_id in (select id from waiver where waiver.medical = true);

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
