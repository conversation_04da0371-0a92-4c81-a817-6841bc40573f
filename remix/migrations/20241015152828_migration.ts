import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table participant_waiver
              add column input jsonb;

          alter table currency
              add column decimals int2 not null default 2;

          UPDATE currency
          SET decimals = CASE id
                             WHEN 'BIF' THEN 0
                             WHEN 'CLP' THEN 0
                             WHEN 'DJF' THEN 0
                             WHEN 'GNF' THEN 0
                             WHEN 'IDR' THEN 0
                             WHEN 'ISK' THEN 0
                             WHEN 'JPY' THEN 0
                             WHEN 'KMF' THEN 0
                             WHEN 'KRW' THEN 0
                             WHEN 'PYG' THEN 0
                             WHEN 'RWF' THEN 0
                             WHEN 'UGX' THEN 0
                             WHEN 'VND' THEN 0
                             WHEN 'VUV' THEN 0
                             WHEN 'XAF' THEN 0
                             WHEN 'XOF' THEN 0
                             WHEN 'XPF' THEN 0
              END
          WHERE id IN ('BIF', 'CLP', 'DJF', 'GNF', 'IDR', 'ISK', 'JPY', 'KMF', 'KRW', 'PYG', 'RWF', 'UGX', 'VND', 'VUV',
                       'XAF', 'XOF', 'XPF');


-- update currency set decimals = 0 where id = 'IDR';

-- select round(49988::numeric * 0.03::numeric) * 10, round((49988::numeric * 0.03::numeric) * 10), 49988::numeric * 0.03::numeric;
-- select round(49988::numeric * 0.10::numeric) * 10, round((49988::numeric * 0.10::numeric) * 10), 49988::numeric * 0.10::numeric;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
