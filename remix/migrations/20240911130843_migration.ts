import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

          alter table activity
              alter column product_id drop not null;

          alter table establishment
              add column workflow int not null default 0;
          update establishment
          set workflow = 2
          where booking_enabled = true;

          alter table establishment
              rename column booking_enabled to booking_enabled_old;
          alter table establishment
              rename column planning_enabled to planning_enabled_old;
          -- update establishment set workflow = 2 where planning_enabled = true;
-- select count(*) from establishment where workflow = 2;

          alter table activity
              alter column form_id drop not null;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
