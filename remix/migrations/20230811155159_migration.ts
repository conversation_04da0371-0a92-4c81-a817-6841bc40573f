import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table booking
              add column cancelled_at                 timestamptz,
              add column cancelled_by_user_session_id uuid references user_session;

          alter table user_session
              add column verification_token uuid unique;

-- select * from booking where booking.cancelled_at is not null;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
