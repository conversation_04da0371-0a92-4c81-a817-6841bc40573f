import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          select *
          from establishment;

          alter table establishment
              add column direct_booking int not null default 0;

          update establishment
          set direct_booking = 1
          where direct_booking_enabled = true;

          alter table establishment
              drop direct_booking_enabled;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
