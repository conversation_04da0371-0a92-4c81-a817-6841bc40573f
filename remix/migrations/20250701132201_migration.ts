import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Your SQL statements here
      select 'bla';
-- execute later
-- UPDATE activity 
-- SET cached_total_tax_amount = (
--   (
--     (activity.quantity * ROUND(
--       (activity.price_pp * (1 - (activity.discount_percentage::numeric / 100))),
--       COALESCE((SELECT currency.decimals FROM currency JOIN booking ON booking.currency_id = currency.id WHERE booking.id = activity.booking_id), 0)
--     ))
--     +
--     COALESCE((
--       SELECT SUM(addon_prices.price)
--       FROM (
--         SELECT 
--           (activity_addon.quantity * ROUND(
--             (activity_addon.price_amount * (1 - (
--               CASE 
--                 WHEN activity.exclude_discount_for_addons = true THEN 0
--                 ELSE activity.discount_percentage
--               END::numeric / 100))),
--             COALESCE((SELECT currency.decimals FROM currency JOIN booking ON booking.currency_id = currency.id WHERE booking.id = activity.booking_id), 0)
--           )) as price
--         FROM activity_addon 
--         WHERE activity_addon.activity_id = activity.id
--         
--         UNION ALL
--         SELECT 
--           (participation_addon.quantity * ROUND(
--             (activity_addon.price_amount * (1 - (
--               CASE 
--                 WHEN activity.exclude_discount_for_addons = true THEN 0
--                 ELSE activity.discount_percentage
--               END::numeric / 100))),
--             COALESCE((SELECT currency.decimals FROM currency JOIN booking ON booking.currency_id = currency.id WHERE booking.id = activity.booking_id), 0)
--           )) as price
--         FROM activity_addon
--         INNER JOIN participation ON participation.activity_id = activity_addon.activity_id
--         INNER JOIN participation_addon ON (
--           participation_addon.participation_id = participation.id 
--           AND participation_addon.addon_id = activity_addon.addon_id
--         )
--         WHERE activity_addon.activity_id = activity.id
--       ) as addon_prices
--     ), 0)
--   )
--   * (COALESCE((SELECT booking.vat_rate FROM booking WHERE booking.id = activity.booking_id), 0) / 100)
-- );

      
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
