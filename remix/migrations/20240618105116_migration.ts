import { Kysely } from "kysely";

const defaultTabs = [
  {
    id: "essentials",
    name: "Essentials",
    sorts: [
      {
        key: "instructor_name",
        direction: "asc",
      },
      {
        key: "participant_filled",
        direction: "desc",
      },
      { key: "activity", direction: "asc" },
      { key: "booking_reference", direction: "asc" },
      {
        key: "participant_name",
        direction: "asc",
      },
    ],
    columns: [
      "name",
      "booking_reference",
      "country",
      "age",
      "dives",
      "level",
      "registration",
      "waivers",
      "activity",
      "comment",
      "payment_status",
      "contact_number",
      "instructor",
      "meet",
      "time",
      "location",
      "diet",
      "allergies",
      "remarks",
    ],
  },
  {
    id: "dive_specific",
    name: "Dive specific",
    sorts: [
      {
        key: "instructor_name",
        direction: "asc",
      },
      {
        key: "participant_filled",
        direction: "desc",
      },
      { key: "activity", direction: "asc" },
      { key: "booking_reference", direction: "asc" },
      {
        key: "participant_name",
        direction: "asc",
      },
    ],
    columns: ["name", "booking_reference", "country", "age", "dives", "level", "height", "weight", "wetsuit", "boots"],
  },
];

export async function up(db: Kysely<any>): Promise<void> {
  const operators = await db.selectFrom("operator").select("operator.id").execute();

  const inserts = operators
    .map((operator) =>
      defaultTabs.map((tab, index) => ({
        id: db.fn("gen_random_uuid", []),
        name: tab.name,
        sort_order: index,
        sorts: JSON.stringify(tab.sorts),
        columns: tab.columns,
        operator_id: operator.id,
      })),
    )
    .reduce((acc, inserts) => [...acc, ...inserts], []);

  const insertResult = await db.insertInto("view").values(inserts).execute();
  console.log("view inserts: ", insertResult.length);
}

export async function down(): Promise<void> {
  // Migration code
}
