import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          select *
          from activity;
          alter table activity
              add column discount_percentage int default 0;
          update activity
          set discount_percentage = (select booking.discount from booking where booking.id = activity.booking_id)
          where activity.discount_percentage = 0;

          alter table booking
              alter column exclude_discount_for_addons set not null;
          alter table activity
              add column exclude_discount_for_addons bool default false;

          update activity
          set exclude_discount_for_addons = (select booking.exclude_discount_for_addons
                                             from booking
                                             where booking.id = activity.booking_id);

alter table booking rename column discount to discount_old;
alter table booking rename column exclude_discount_for_addons to exclude_discount_for_addons_old;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
