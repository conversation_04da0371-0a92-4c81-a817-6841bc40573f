import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
          delete
          from product__diving_location
          where product_id in (select product.id
                               from product
                                        inner join activity on activity.id = product.activity_id
                               where activity.slug = 'snorkeling');

          insert into product__diving_location (id, diving_location_id, product_id, created_by, updated_by, updated_at,
                                                created_at)
          select id,
                 diving_location_id,
                 product_id,
                 created_by,
                 updated_by,
                 updated_at,
                 created_at
          from product__snorkel_location;

          insert into product__diving_site (id, diving_site_id, product_id, created_by, updated_by, updated_at,
                                                created_at)
          select id, diving_site_id, product_id, created_by, updated_by, updated_at,
                 created_at
          from product__snorkel_site;


      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
