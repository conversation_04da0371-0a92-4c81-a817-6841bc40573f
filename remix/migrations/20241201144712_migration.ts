import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table participation_waiver
    drop constraint participation_waiver_participant_id_fkey;

alter table participation_waiver
    add foreign key (participant_id) references participant
        on delete cascade;

alter table participation_waiver
    drop constraint participation_waiver_activity_id_fkey;

alter table participation_waiver
    add foreign key (activity_id) references activity
        on delete cascade;

      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
