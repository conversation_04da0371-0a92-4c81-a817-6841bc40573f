import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table booking add column meeting_type text not null default 'DIVE_CENTER';
alter table booking alter column meeting_type drop default ;
alter table booking add column meeting_address text;

alter table trip_assignment add column product_id uuid;

update trip_assignment set product_id = group_name::uuid where group_name ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$';
alter table trip_assignment drop column group_name;
`
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
