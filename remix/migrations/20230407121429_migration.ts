import type { Kysely } from "kysely";
import { sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table booking
              drop column activity_slug;

          alter table booking__boat_departure
              drop constraint booking__boat_departure_booking_id_fkey;

          alter table booking__boat_departure
              add foreign key (booking_id) references booking
                  on delete cascade;


          delete
          from booking_registration;
          delete
          from booking;

          alter table booking
              add column price_pp numeric not null;
          alter table booking
              add column price_currency text references currency (id) not null;

          alter table booking
              add column discount int not null default 0;
          alter table booking
              add column exclude_discount_for_addons boolean default false;

          alter table booking
              rename column price_currency to currency_id;

          alter table booking
              add column product_title text not null;

          create table booking_registration_addon
          (
              id                      uuid                                                        not null primary key default gen_random_uuid(),
              addon_id                uuid                                                        not null,
              booking_registration_id uuid references booking_registration (id) on delete cascade not null,
              amount                  int                                                         not null,
              constraint unique_addon_id__booking_registration_id unique (addon_id, booking_registration_id)
          )
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
