import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      update payment_method
      set default_surcharge_percentage = 4
      where key = 'paypal';

-- select * from payment_method where key = 'paypal'
  `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
