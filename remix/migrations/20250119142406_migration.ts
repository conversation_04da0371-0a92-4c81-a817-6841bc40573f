import { <PERSON>ys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table one_time_password
              add column user_id uuid references "user";
          update one_time_password
          set user_id = (select "user".id
                         from "user"
                         where "user".email = one_time_password.email and "user".deleted_at = 'infinity');
          alter table one_time_password
              alter column user_id set not null;

          alter table one_time_password
              drop column email;
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
