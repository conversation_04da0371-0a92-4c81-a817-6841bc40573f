import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table establishment
              add column default_weight_unit text;
          alter table establishment
              add column default_shoe_size_unit text;
          alter table establishment
              add column default_weightbelt_unit text;
          alter table establishment
              add column default_height_unit text;

          alter table participant
              alter column weight_value type numeric using weight_value::numeric;
          alter table participant
              alter column shoe_size_value type numeric using shoe_size_value::numeric;
          alter table participant
              alter column weightbelt_value type numeric using weightbelt_value::numeric;
          alter table participant
              alter column height_value type numeric using height_value::numeric;



      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
