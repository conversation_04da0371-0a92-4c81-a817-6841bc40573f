import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

update participant set booking_id = null where booking_id not in (select id from booking);

          ALTER TABLE participant
              ADD CONSTRAINT fk_booking_id
                  FOREIGN KEY (booking_id) REFERENCES booking (id);

-- select * from entity_action inner join user_event on entity_action.user_event_id = user_event.id where entity_id = '2e642bba-0b63-4b29-bed3-bf5dcc14be86'
`,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
