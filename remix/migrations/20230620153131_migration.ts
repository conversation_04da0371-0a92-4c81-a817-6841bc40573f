import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

      alter table participant_assignment
          add column trip_id   uuid references trip on delete cascade,
          add column member_id uuid references member on delete set null;

      alter table trip_assignment
          alter column member_id drop not null;
      alter table trip_assignment
          add column participant_id uuid references participant on delete cascade;
      alter table trip_assignment
          alter column id set default gen_random_uuid();

      insert into trip_assignment (trip_id, participant_id, member_id, role)
      select trip_booking.trip_id as trip_id,
             participant.id       as participant_id,
             (select member_id
              from trip_assignment
              where trip_assignment.trip_id = trip_booking.trip_id
                and trip_assignment.product_id = booking.product_id
                and trip_assignment.role = 'instructor'
              limit 1)            as member_id,
             'instructor'         as role
      from trip_booking
               inner join trip on trip.id = trip_booking.trip_id
               inner join booking on booking.id = trip_booking.booking_id
               inner join participant on participant.booking_id = trip_booking.booking_id;

      alter table member
          add unique (establishment_id, user_id);
      create unique index on member (establishment_id, first_name, last_name) where user_id is null;
      alter table member
          add unique (establishment_id, user_id, first_name, last_name);

      --           create table trip_assignment
--           (
--               id         uuid not null
--                   constraint location_user_schedule_pkey
--                       primary key default gen_random_uuid(),
--               trip_id    uuid not null
--                   constraint location_user_schedule_location_schedule_id_fkey
--                       references trip
--                       on delete cascade,
--               member_id  uuid,
--               created_at timestamp with time zone default now(),
--               role       text not null,
--               product_id uuid
--           );
      drop table participant_assignment;

      --           update participant_assignment
--           set trip_id   = (select trip_assignment.trip_id
--                            from trip_assignment
--                            where trip_assignment.id = participant_assignment.trip_assignment_id),
--               member_id = (select trip_assignment.member_id
--                            from trip_assignment
--                            where trip_assignment.id = participant_assignment.trip_assignment_id);
-- 
--           select *
--           from participant_assignment;

      -- update participant set user_session_id = null where first_name is null;

--           select u.email as us_email, u.id as u_id, us.user_id, participant.user_id
--           from participant
--                    inner join user_session us on participant.created_by_user_session_id = us.id
--                    inner join "user" u on u.id = us.user_id
`
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
