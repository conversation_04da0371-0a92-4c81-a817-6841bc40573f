import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          create table form
          (
              id                         uuid primary key,
              created_at                 timestamptz not null,
              created_by_user_session_id uuid        not null references user_session,
              target_id                  uuid        not null,
              type                       text        not null,
              name                       text        not null,
              unique (target_id, type, name)
          );

          create table field
          (
              id                         uuid primary key,
              created_at                 timestamptz          not null,
              created_by_user_session_id uuid                 not null references user_session,
              form_id                    uuid references form not null,
              name                       text                 not null,
              required                   bool                 not null,
              show                       bool                 not null,
              unique (form_id, name)
          );

          alter table session
              add column anonymous bool not null default false;

          update session
          set anonymous        = true,
              selected_user_id = null
          where selected_user_id = 'no-user';

          alter table session
              alter selected_user_id type uuid using selected_user_id::uuid;

          alter table session
              add foreign key (selected_user_id) references "user";

          update session
          set selected_user_id = (select user_session.user_id
                                  from user_session
                                           inner join "user" u on u.id = user_session.user_id
                                  where user_session.destroyed_at = 'infinity'
                                    and u.deleted_at = 'infinity'
                                    and user_session.session_id = session.id
                                  order by user_session.created_at asc
                                  limit 1)
          where session.selected_user_id is null
            and session.anonymous = false
            and session.destroyed_at = 'infinity';

          alter table session
              drop anonymous;

          alter table form
              alter created_at set default now();
          alter table field
              alter created_at set default now();

          alter table field
              drop constraint field_form_id_fkey;
          alter table field
              add constraint field_form_id_fk
                  foreign key (form_id) references form
                      on delete cascade;

          alter table product
              add column form_id uuid references form on delete set null;

          --           select *
--           from session
--                    inner join user_session on session.id = user_session.session_id
--                    inner join "user" on user_session.user_id = "user".id
--           order by session.
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
