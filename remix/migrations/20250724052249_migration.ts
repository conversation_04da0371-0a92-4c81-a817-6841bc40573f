import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Your SQL statements here
      alter table tank_assignment alter column liters type varchar(6);
update tank_assignment set liters = '12L' where liters = '12';
update tank_assignment set liters = '15L' where liters = '15';

alter table rentable add column service_interval interval;
alter table rentable add column service_use_count integer;
      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `
    )
    .execute(db);
}
