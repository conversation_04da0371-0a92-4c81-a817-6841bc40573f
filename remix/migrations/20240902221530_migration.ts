import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      alter table establishment
          add column direct_booking_enabled bool default false;

      alter table booking
          add column direct_booking bool default false;


select distinct meeting_type from booking;
  `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
