import type { Kys<PERSON> } from "kysely";
import { sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          create table medical
          (
              id             uuid primary key,
              participant_id uuid references participant not null unique,
              q_1            bool,
              q_2            bool
          );

          alter table medical
              add column answers bool[][];


      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
