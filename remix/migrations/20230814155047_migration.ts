import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table trip
              add column created_at                 timestamptz,
              add column created_by_user_session_id uuid references user_session;

          update trip
          set created_at                 = (select user_event.created_at
                                            from entity_action
                                                     inner join user_event on entity_action.user_event_id = user_event.id
                                            where entity_action.entity_name = 'trip'
                                              and entity_action.action_name = 'insert'
                                              and entity_action.entity_id = trip.id),
              created_by_user_session_id = (select user_event.user_session_id
                                            from entity_action
                                                     inner join user_event on entity_action.user_event_id = user_event.id
                                            where entity_action.entity_name = 'trip'
                                              and entity_action.action_name = 'insert'
                                              and entity_action.entity_id = trip.id)
          where trip.created_at is null
             or trip.created_by_user_session_id is null;

          alter table trip
              alter column created_at set default now();
          select *
          from trip;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
