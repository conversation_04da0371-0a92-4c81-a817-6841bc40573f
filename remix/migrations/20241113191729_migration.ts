import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

delete
from signature
where signature.participant_waiver_id in (select participant_waiver.id
                                          from participant
                                                   inner join participant_waiver
                                                              on participant.id = participant_waiver.participant_id
--                                                      inner join waiver on participant_waiver.waiver_id = waiver.id
                                          where (select count(distinct p2.id) = 1
                                                 from participant p2
                                                          inner join customer on p2.customer_id = customer.id
                                                 where customer.id = participant.customer_id)
                                            and participant.id not in (select participation.participant_id
                                                                       from participation
                                                                       where participation.participant_id is not null)
                                            and participant.booking_id_old is not null);


delete
from participant_waiver
where participant_waiver.participant_id in (select participant.id
                                            from participant
                                            --                                                      inner join participant_waiver
--                                                                 on participant.id = participant_waiver.participant_id
--                                                      inner join waiver on participant_waiver.waiver_id = waiver.id
                                            where (select count(distinct p2.id) = 1
                                                   from participant p2
                                                            inner join customer on p2.customer_id = customer.id
                                                   where customer.id = participant.customer_id)
                                              and participant.id not in (select participation.participant_id
                                                                         from participation
                                                                         where participation.participant_id is not null)
                                              and participant.booking_id_old is not null);

          delete from  participant
          where participant.booking_id_old is not null
            and participant.id not in (select participant_waiver.participant_id from participant_waiver)
--             and (participant.id not in (select participant_waiver.participant_id from participant_waiver) or
--                  (select count(*) < 2
--                   from customer
--                            inner join participant p2 on customer.id = p2.customer_id
--                   where customer.id = participant.customer_id))
            and not exists(select participation.id
                           from participation
                           where participation.participant_id = participant.id);

      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
