import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
          drop function if exists get_session_id();
          drop function if exists get_session();
          drop view if exists session_active;
          alter table "user"
              drop column terms_of_conditions_accepted,
              drop column privacy_accepted;

          alter table product
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by,
              drop column if exists old_duration_in_hours,
              drop column if exists category_g,
              drop column if exists files_json;

          alter table activity
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by,
              drop column if exists files_json;

          alter table country
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by;

          alter table diving_certificate_level
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by;

          alter table diving_certificate_organization
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by;

          alter table diving_course
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by;

          alter table diving_location
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by;

          alter table diving_site
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by;

          alter table operator
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by;

          alter table operator_location
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by;

          alter table owner
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by;

          alter table product__diving_course
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by;

          alter table product__diving_location
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by;

          alter table product__diving_site
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by;

          alter table region
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by,
              drop column if exists files_json;

          alter table spot
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by,
              drop column if exists files_json;

          drop table old_product__diving_type;
          drop table old_diving_type;
          drop table node_user;
          drop table knex_migrations_lock;
          drop table knex_migrations;
--           drop table old_tag;

          select *
          from "user";
          alter table "user"
              add column old_id text;
          update "user"
          set old_id = id;

          drop table transport.user_place;
          drop table transport.user_estimate;
          drop table vote;
          drop table transport.transport_area_info;


          drop table transport."estimate";
          drop table transport."offer";
          drop table transport."transport";
          drop table transport."calculation_type";
          drop table transport."content";

          drop table transport."search";
          drop table transport."place";
          drop table transport."location";
          drop table transport."area_info";
          drop function area_size;
          drop function bbox_areas;
          drop table transport."area";
          drop table transport."area_type";

          drop function transport.set_current_timestamp_updated_at;

          drop schema transport;

          drop table review;

          alter table user_event
              drop constraint if exists mutation_event_created_by_fkey;
          alter table user_event
              drop constraint if exists mutation_event_user_id_fkey;

          alter table user_event
              add constraint mutation_event_created_by_fkey
                  foreign key (user_id) references "user"
                      on update cascade on delete restrict;

          alter table user_session
              drop constraint session_user_id_fk;

          alter table user_session
              add constraint session_user_id_fk
                  foreign key (user_id) references "user"
                      on update cascade on delete restrict;

          alter table "user"
              drop constraint user_created_by_fkey;

          alter table "user"
              add foreign key (created_by) references "user"
                  on update cascade on delete restrict;

          alter table "user"
              drop constraint user_updated_by_fkey;

          alter table "user"
              add foreign key (updated_by) references "user"
                  on update cascade on delete restrict;

          alter table "user"
              drop constraint user_deleted_by_fkey;

          alter table "user"
              add foreign key (deleted_by) references "user"
                  on update cascade on delete restrict;

          alter table owner
              drop constraint owner_user_id_fkey;

          alter table owner
              add foreign key (user_id) references "user"
                  on update cascade on delete restrict;

          alter table old_tag
              drop column if exists created_at,
              drop column if exists created_by,
              drop column if exists updated_at,
              drop column if exists updated_by;

          drop trigger update_user_session on "user_session";
          drop function update_user_session;

          update "user"
          set id = gen_random_uuid()
          where length(id) != 36;



          alter table user_event
              drop constraint mutation_event_created_by_fkey;


          alter table user_session
              drop constraint session_user_id_fk;


          alter table "user"
              drop constraint user_created_by_fkey;

          alter table "user"
              drop constraint user_updated_by_fkey;

          alter table "user"
              drop constraint user_deleted_by_fkey;


          alter table owner
              drop constraint owner_user_id_fkey;

          alter table "user"
              alter column "id" type uuid using (id::uuid);

-- add constraints again 
          alter table user_event
              alter column user_id type uuid using (user_id::uuid),
              add constraint mutation_event_created_by_fkey
                  foreign key (user_id) references "user"
                      on update restrict on delete restrict;

          alter table user_session
              alter column user_id type uuid using (user_id::uuid),
              add constraint session_user_id_fk
                  foreign key (user_id) references "user"
                      on update restrict on delete restrict;

          alter table "user"
              alter column created_by type uuid using (created_by::uuid),
              add foreign key (created_by) references "user"
                  on update restrict on delete restrict;

          alter table "user"
              alter column updated_by type uuid using (updated_by::uuid),
              add foreign key (updated_by) references "user"
                  on update restrict on delete restrict;

          alter table "user"
              alter column deleted_by type uuid using (deleted_by::uuid),
              add foreign key (deleted_by) references "user"
                  on update restrict on delete restrict;

          alter table owner
              alter column user_id type uuid using (user_id::uuid),
              add foreign key (user_id) references "user"
                  on update restrict on delete restrict;

          alter table "user"
              alter column id drop default;



          select *
          from "user"
          where created_at is null;

          select *
          from table_action
          where table_name = 'user';

          with userEventInserts as (
              insert into user_event (created_at, user_id, target_id, payload)
                  select "user".created_at, coalesce("user".created_by, "user".id), "user".id, to_jsonb("user")
                  from "user" returning *)
          insert
          into table_action (target_id, name, user_event_id, data, table_name)
          select userEventInserts.target_id, 'create', userEventInserts.id, userEventInserts.payload, 'user'
          from userEventInserts;

          alter table "user"
              alter column deleted_at set not null;

          alter table "user"
              drop column "updated_at",
              drop column "updated_by",
              drop column "created_by",
              drop column "created_at";


          with userEventInserts as (
              insert into user_event (created_at, user_id, target_id, payload)
                  select "user".deleted_at, coalesce("user".deleted_by, "user".id), "user".id, to_jsonb("user")
                  from "user"
                  where "user".deleted_at != 'infinity' returning *)
          insert
          into table_action (target_id, name, user_event_id, data, table_name)
          select userEventInserts.target_id, 'update', userEventInserts.id, userEventInserts.payload, 'user'
          from userEventInserts;

          alter table "user"
              drop column "deleted_by";


-- 
--           select *
--           from table_action
--                    inner join user_event ue on table_action.user_event_id = ue.id
--           where table_action.table_name = 'user'
-- order by ue.created_at desc ;
-- select * from "user" where email = '<EMAIL>';
-- 
-- 
--           select *
--           from old_tag;
-- 
-- 
--           select table_name, count(table_name)
--           from table_action
--           group by table_name;
      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
