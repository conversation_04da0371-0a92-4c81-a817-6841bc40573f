import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table participant add column diving_certificate_organization text;
alter table participant rename column wetsuit_size to tshirt_size;
alter table participant add column comment text;

alter table participant add column emergency_contact_relationship text;
alter table participant add column food_allergies text;
alter table participant add column insurance text;
alter table participant add column years_of_experience int;
alter table participant add column weightbelt_in_kg int;

alter table participant add column user_session_created_at timestamptz;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
