import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          create table category
          (
              id                 uuid primary key     default gen_random_uuid(),
              created_at         timestamptz not null default now(),
              establishment_id   uuid references establishment,
              key                text        not null,
              name               text        not null,
              parent_category_id uuid references category,
--               default_activity_slug text,
--               retail              boolean     not null default false,
--               activity            boolean     not null default false,
              unique (establishment_id, key)
          );

          --           alter table product
--               drop column retail;

          alter table product
              add column category_id uuid references category;

          alter table product
              alter column gear_included set default false;

--               add column retail bool not null default false;
      `,
    )
    .execute(db);
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
--           
      `,
    )
    .execute(db);
}
