import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
      create table addon
      (
          id                   uuid primary key default gen_random_uuid() not null,
          name                 text                                       not null,
          price                numeric                                    not null,
          price_currency       text references currency (id),
          operator_location_id uuid references operator_location (id),
          duration_in_hours    decimal
      );

      alter table product
          rename column addons to addons_description;

      alter table product
          add column addons uuid[];


      alter table addon
          add column unit text;
      update addon
      set unit = 'DAY'
      WHERE unit is null;
      alter table addon
          alter column unit set not null;
  `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
