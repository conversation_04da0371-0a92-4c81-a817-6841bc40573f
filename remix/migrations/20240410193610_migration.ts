import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table waiver
              add column sort_order int;
          alter table waiver_translation
              add column sort_order int;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
