import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Your SQL statements here
      alter table activity rename to sale_item;

      alter table activity_addon rename column activity_id to sale_item_id;
      alter table participation rename column activity_id to sale_item_id;
      alter table participation_waiver rename column activity_id to sale_item_id;
      alter table mail rename column activity_id to sale_item_id;
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      alter table sale_item rename to activity;
      `,
    )
    .execute(db);
}
