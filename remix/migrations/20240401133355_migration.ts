import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table payment
              add column payment_currency text references currency;
          alter table payment
              add column payment_amount numeric;
          alter table payment
              add column error text;

          alter table waiver
              add column diving_certificate_organization_key text;

          alter table waiver
              drop constraint waiver_root_id_language_code_key;
          alter table waiver
              add unique nulls not distinct (slug, language_code, establishment_id, diving_certificate_organization_key) 

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
