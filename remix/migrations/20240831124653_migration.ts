import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          create table payment_method
          (
              id                           uuid primary key default gen_random_uuid(),
              created_at                   timestamptz      default now() not null,
              created_by_user_session_id   uuid references user_session,
              key                          text                           not null,
              name                         text                           not null,
              short                        text,
              fixed                        bool             default false not null,
              xendit                       bool             default false not null,
              establishment_id             uuid references establishment  not null,
              default_surcharge_percentage int              default 0     not null,
              unique (establishment_id, key),
              unique (establishment_id, name)
          );

-- Inserting the payment methods
          INSERT INTO payment_method (key, name, fixed, default_surcharge_percentage, establishment_id)
          SELECT 'xendit', 'Xendit', true, 3, establishment.id
          FROM establishment;

          INSERT INTO payment_method (key, name, fixed, default_surcharge_percentage, establishment_id)
          SELECT 'wise', 'Wise', true, 0, establishment.id
          FROM establishment;

          INSERT INTO payment_method (key, name, fixed, default_surcharge_percentage, establishment_id)
          SELECT 'cash', 'Cash', true, 0, establishment.id
          FROM establishment;

          INSERT INTO payment_method (key, name, fixed, default_surcharge_percentage, establishment_id)
          SELECT 'paypal', 'Paypal', true, 2, establishment.id
          FROM establishment;

          INSERT INTO payment_method (key, name, fixed, default_surcharge_percentage, establishment_id)
          SELECT 'banktransfer', 'Bank Transfer', true, 0, establishment.id
          FROM establishment;

          INSERT INTO payment_method (key, name, fixed, default_surcharge_percentage, establishment_id)
          SELECT 'creditcard', 'Credit Card', true, 3, establishment.id
          FROM establishment;

          INSERT INTO payment_method (key, name, fixed, default_surcharge_percentage, establishment_id)
          SELECT 'other', 'Other', true, 0, establishment.id
          FROM establishment;

          update payment_method
          set xendit = true
          where key = 'xendit';

          update payment_method
          set short = 'CC'
          where key = 'creditcard';

          alter table payment
              add column payment_method_id uuid references payment_method;


          update payment
          set payment_method_id = (select payment_method.id
                                   from payment_method
                                            inner join booking on booking.establishment_id = payment_method.establishment_id
                                   where booking.id = payment.booking_id
                                     and payment.method = payment_method.key)
          where payment.payment_method_id is null;
          select distinct method
          from payment;

          alter table payment
              alter column payment_method_id set not null,
              alter column method drop not null;
          alter table payment
              rename column method to method_old;

--           select distinct payment_method.name, payment_method.key, payment_method.short, payment_method.xendit
--           from payment_method;

--           delete
--           from payment
--           where payment.id in (select payment.id
--                                from payment
--                                         inner join payment_method on payment_method.id = payment.payment_method_id
--                                where payment.xendit_invoice_id is not null
--                                  and payment_method.xendit = false);
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
