import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table callback
              add column msg text;
          select *
          from callback;

          UPDATE entity_action
          SET data = data -> 'auditInput'
          WHERE data ? 'diff';
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
