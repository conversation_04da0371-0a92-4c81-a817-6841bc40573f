import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table diving_location
              add column region_id uuid references region;

          update diving_location
          set region_id = (select region.id from region where region.country_code = 'ID')
          where diving_location.region_id is null;

          alter table diving_location
              alter column region_id set not null;

          alter table region
              alter column geom drop not null;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
