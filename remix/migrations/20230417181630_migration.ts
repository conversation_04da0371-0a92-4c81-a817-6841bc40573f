import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          delete
          from booking_registration;
          alter table booking_registration
              drop column for_user_session_id,
              add column user_id     uuid references "user" (id) not null,
              add column verified_at timestamptz;

select * from booking_registration inner join "user" on "user".id = booking_registration.user_id;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
