import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table booking alter column duration set not null ;

alter table session alter column selected_user_id type varchar(36);

create table callback (
    id uuid primary key default gen_random_uuid(),
--     google_task_id text not null ,
    created_at timestamptz not null default now(),
    created_by_user_session_id uuid,
    client_id uuid,
    name text,
    target_id uuid,
    handled_at timestamptz
);

alter table callback add column status text;

-- alter table booking add column duration_in_hours int4range;
-- alter table booking add column start_datetime timestamptz;
-- alter table booking add column days daterange;
-- 
-- 
-- update booking set duration_in_hours = int4range((EXTRACT(EPOCH FROM upper(duration) - lower(duration)) / 3600)::int4, (EXTRACT(EPOCH FROM upper(duration) - lower(duration)) / 3600)::int4, '[]');
-- 
-- update booking set start_datetime = lower(duration);
-- 
-- alter table booking alter column start_datetime set not null ;

-- select * from booking;
-- 
-- select * from booking;
-- select booking.id, booking.duration, int4range((EXTRACT(EPOCH FROM upper(duration) - lower(duration)) / 3600)::int4, (EXTRACT(EPOCH FROM upper(duration) - lower(duration)) / 3600)::int4, '[]') AS duration_in_hours from booking;
-- 
-- 
-- select int4range((EXTRACT(EPOCH FROM upper(tstzrange('2023-06-01 10:00', '2023-06-01 11:00')) - lower(tstzrange('2023-06-01 10:00', '2023-06-01 11:00'))) / 3600)::int4, (EXTRACT(EPOCH FROM upper(tstzrange('2023-06-01 10:00', '2023-06-01 11:00')) - lower(tstzrange('2023-06-01 10:00', '2023-06-01 11:00'))) / 3600)::int4, '[]') AS duration_in_hours;
-- 
-- SELECT *
-- FROM booking
-- WHERE lower(daterange('2023-01-01', '2023-06-01', '[]')) > booking.start_datetime;
-- 
-- select '2023-11-01 10:00'::timestamptz > lower(daterange('2023-10-01', '2023-11-01'));
-- select '2023-10-30'::date <@ daterange('2023-10-01', '2023-11-01');
-- 
-- CREATE OR REPLACE FUNCTION set_duration_from_start_and_hours()
--     RETURNS TRIGGER AS $$
-- BEGIN
--     IF NEW is not null then
--             NEW.duration := tstzrange(NEW.start_datetime, NEW.start_datetime + INTERVAL '1 hour' * (coalesce(upper(new.duration_in_hours), lower(new.duration_in_hours))));
--     END IF;
-- 
--     RETURN NEW;
-- END;
-- $$ LANGUAGE plpgsql;

`
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
