import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table diving_location
              add column name_generated text generated always as ( lower(trim(name)) ) stored not null,
              add unique (region_id, name_generated),
              add unique (region_id, name);
alter table diving_location drop constraint location_name_key;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
