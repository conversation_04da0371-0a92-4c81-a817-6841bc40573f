import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table participant
              add column last_dive_within_months int;

          update participant
          set last_dive_within_months = extract(month from age(participant.created_at, participant.last_dived_at))
          where participant.last_dived_at is not null;

          alter table participant
              rename column last_dived_at to last_dived_at_old;

          update field
          set name = 'last_dive_within_months'
          where field.name = 'last_dived_at';

update participant set last_dive_within_months = null where participant.id = '7f21ad74-81df-4b82-84c9-e5f58c49179c'

          --           select extract(month from age(participant.created_at, participant.last_dived_at)),
--                  participant.created_at,
--                  participant.last_dived_at
--           from participant
--           order by extract(month from
--                            age(participant.created_at, participant.last_dived_at)) asc nulls last where participant.last_dived_at is not null
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
