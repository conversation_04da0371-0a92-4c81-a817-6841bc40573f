import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table waiver
              alter column root_id type text;
          alter table waiver
              rename column root_id to slug;
          alter table waiver
              add column establishment_id uuid references establishment;
          alter table file_target
              alter column target_id drop not null;


          update waiver set markdoc = '# DIVER ACTIVITIES

Please carefully review and complete all sections before affixing your signature.

This document outlines the risks associated with engaging in scuba diving activities, whether as a certified diver or under the guidance of a certified scuba instructor. By signing, you acknowledge your understanding of these risks. If you have any uncertainties, consult your instructor or dive professional. If you are a minor, this form must be co-signed by a parent or legal guardian.

**NON-AGENCY DISCLOSURE AND ACKNOWLEDGMENT AGREEMENT**

I understand and agree that **{% $operator %}** and individuals associated with the program are authorized to conduct diver training but are not agents, employees, or franchisees of the organization establishing training standards. The businesses operated by **{% $operator %}**  are independent. In case of injury or death during this activity, I will not hold the said organization liable for actions, inactions, or negligence of **{% $operator %}** and associated staff.

**LIABILITY RELEASE AND ASSUMPTION OF RISK**

I, {% input value=$participant /%}, am either a certified diver knowledgeable in safe dive practices or a student diver under certified instructor supervision. I acknowledge inherent risks in scuba diving, including those linked to boat travel (the "Excursion"), which could result in severe injury or loss of life. I recognize that scuba diving carries inherent risks, including decompression sickness and other injuries. I understand risks related to enriched air ("Enriched Air") or other gas blends. I am aware that this Excursion encompasses risks like slipping, encountering cuts, injuries during embarkation/disembarkation, and maritime dangers. Additionally, I comprehend that the Excursion will be in a remote dive site without immediate access to a recompression chamber. Despite these factors, I elect to proceed with the Excursion.

I understand and agree that neither **{% $operator %}** nor the diving professional(s) nor the organization responsible for training standards can be held liable for injuries, fatalities, or damages sustained during the Excursion.

**EXCLUSION OF LIABILITY**

 understand and agree that neither **{% $operator %}**, the divemasters, crew members, captain, nor any other organization or entity bear responsibility for any demise, injury, or losses experienced by me due to my actions or circumstances under my control. My participation in this scuba diving expedition is at my personal risk. I affirm my fitness for diving and that I will avoid alcohol or contraindicated drugs. I understand the physical demands of diving. In case of injuries arising from various causes, I assume associated risks and agree not to hold parties accountable.

---

**I HAVE READ AND UNDERSTOOD THIS RELEASE OF LIABILITY, ASSUMPTION OF RISK, AND NON-AGENCY ACKNOWLEDGMENT FORM. I SIGN THIS ON MY BEHALF AND THAT OF MY HEIRS.**
'
          where waiver.id = '62e83a65-ec1c-4e48-9853-8808bfed7f10';

select * from waiver;

update waiver set markdoc = '![ssi logo](/images/ssi-logo.png)  dsfas

# SSI Recreational Scuba Training Assumption of Risk, Liability Release & Hold Harmless Agreement {% input value=$participant  /%}
{% $participant %}{% input value=$operator /%}
{% input value=$participant /%}

{% u %}dfadsfsdfas fsadfasf {% /u %}{% input value=$participant /%}


**This is a legal contract termin input value=ating your rights to file a lawsuit. Read carefully before signing. Warning – Scuba diving uses life-support equipment and techniques that have inherent *risks* which may cause serious injury, illness or death.** 
$sadfasdf sad 
# header

In consideration of being allowed to participate in scuba training,
*****sadfsd*****
I, {% input value=$participant /%}, {% $participant %} expressly {% u %}agree to be bound{% /u %} by this Agreement and comply with the SSI Responsible Scuba Diver Code. I understand this Agreement is between me, my family, estate, heirs and or anyone who may have a claim on my behalf; and Test ruben, including all instructors, facilities, boats, and training sites I receive training with or at; Scuba Schools International (“SSI”); and each of their respective owners, officers, employees, representatives, volunteers, agents, contractors and any others on their behalves, whether specifically named or not (herein referred to as “Released Parties”).

I voluntarily assume all risks of injury, illness and death, caused by scuba diving and all related activities, whether foreseeable or not, including but not limited to risks associated with: swimming, entering and exiting the water, falling on, struck by or abandoned by a boat, separation or lost underwater, holding my breath, pre-existing health conditions, heart failure, over-exertion, panic, drowning, pressure related injuries, decompression illness, environmental and marine life injuries, unknown causes, equipment malfunctions, improper dive planning, or improper action of other divers or support personnel (including failure to rescue, recover, resuscitate, or provide emergency assistance).


## subheading
1. bla
2. set


sadfs
- sfasdf
- asfdasf
- asdfsdf

sdfasd
- asdfasdf

blaasdfsfdfasd fa
sadfsdf

balasdf

| Syntax | Description |
| ----------- | ----------- |
| Header | Title |
| Paragraph | Text |

{% callout type="check" %}
Markdoc is open-source—check out its [source](http://github.com/markdoc/markdoc) to see how it works.
{% /callout %}

That is so funny! :joy:


- [x] Write the press release
- [ ] Update the website
- [ ] Contact the media'
where language_code = 'en' and slug = 'test'

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
