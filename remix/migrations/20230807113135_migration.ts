import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          create table file_target
          (
              id                         uuid primary key,
              file_id                    uuid references file not null,
              sort_order                 int                  not null default 0,
              target                     text                 not null,
              target_id                  uuid                 not null,
              created_at                 timestamptz                   default now(),
              created_by_user_session_id uuid references user_session
          );

          insert into file_target (id, file_id, sort_order, target, target_id, created_at, created_by_user_session_id)
          select gen_random_uuid(),
                 file.id,
                 file.sort_order,
                 file.target,
                 file.target_id,
                 file.created_at,
                 file.created_by_user_session_id
          from file;

          alter table file
              alter column target drop not null;
          alter table file
              alter column target_id drop not null;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
