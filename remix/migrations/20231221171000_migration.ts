import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
--           update participant
--           set establishment_id = (select booking.id from booking where participant.booking_id = booking.id)
--           where participant.establishment_id is null;
-- 
--           create table participation
--           (
--               id             uuid primary key default gen_random_uuid(),
--               booking_id     uuid references booking not null,
--               participant_id uuid references participant
--           );
-- 
--           insert into participation (booking_id, participant_id)
--           select case when participant.user_id is null then null else participant.id end as participant_id,
--                  participant.booking_id
--           from participant;
-- 
--           delete
--           from participant
--           where user_id is null;
-- 
--           alter table participant
--               rename column booking_id to booking_id_old;
--           alter table participant
--               alter column booking_id drop not null;
--           alter table participant
--               alter column establishment_id set not null;
--           alter table participant
--               alter column user_id set not null;
-- 
--           alter table participant
--               alter column created_by_user_session_id drop not null;


      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
