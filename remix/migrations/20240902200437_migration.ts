import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          create table invoice_count
          (
              id               uuid primary key                       default gen_random_uuid(),
              establishment_id uuid references establishment not null,
              year             int                           not null,
              count            int                           not null default 0,
              unique (establishment_id, year)
          );

          alter table invoice
              add column invoice_nr text;

          with numbered_invoices as (select invoice.id,
                                            invoice.created_at,
                                            row_number()
                                            over (partition by booking.establishment_id order by invoice.created_at, invoice.id) as invoice_count
                                     from invoice
                                              inner join booking on booking.id = invoice.booking_id
                                     order by invoice.created_at, invoice.id)
          update invoice
          set invoice_nr = concat('INV-', to_char(numbered_invoices.created_at, 'YYYY'),
                                  lpad(numbered_invoices.invoice_count::text, 5, '0'))
          from numbered_invoices
          where invoice.id = numbered_invoices.id;

          alter table invoice
              alter column invoice_nr set not null;

          insert into invoice_count (establishment_id, year, count)
          select booking.establishment_id, EXTRACT(YEAR FROM invoice.created_at), count(invoice.id)
          from invoice
                   inner join booking on booking.id = invoice.booking_id
          group by booking.establishment_id, EXTRACT(YEAR FROM invoice.created_at);

--           select *
--           from invoice_count;
--           select *
--           from invoice;
--           select *
--           from master.public.invoice;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
