import { <PERSON>ys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
          alter table session
              add column currency_switched boolean default false,
              add column env_verified      bool    default false;

          alter table session
              alter column created_at set not null;
          alter table session
              alter column created_at set default now();

          update session
          set currency_switched = coalesce((session.raw -> 'currency_switched')::boolean, false),
              env_verified      = coalesce((session.raw -> 'env_verified')::boolean, false)
            where id is not null;

      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
