import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          update trip_assignment
          set member_id = null
          where member_id is not null
            and member_id not in (select member.id from member);

          ALTER TABLE trip_assignment
              ADD CONSTRAINT fk_trip_assignment_member
                  FOREIGN KEY (member_id)
                      REFERENCES member (id);

      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
