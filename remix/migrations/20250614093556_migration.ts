import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
        -- Your SQL statements here
        ALTER TABLE participant
          ADD COLUMN bcd_size varchar(4);

        drop table if exists waiver_establishment;
        create table waiver_establishment
        (
          id                uuid primary key default gen_random_uuid() not null,
          establishment_id  uuid references establishment              not null,
          waiver_id         uuid references waiver                     not null,
          enabled           bool             default true              not null,
          validity_duration interval,
          unique (establishment_id, waiver_id)
        );

        alter table file
          add column description text;

        drop table if exists rentable;
        drop table if exists rental_assignment;
        create table rentable
        (
          id                         uuid primary key default gen_random_uuid() not null,
          establishment_id           uuid references establishment              not null,

          type                       text                                       not null,
          size                       varchar(4)                                 not null,
          reference_id               text                                       not null,
          deleted_at                 timestamptz                                not null,
          deleted_by_user_session_id uuid references user_session,
          storage_location           text,
          unique (establishment_id, reference_id)
        );

        create table rental_assignment
        (

          id             uuid primary key default gen_random_uuid() not null,
          date           date                                       not null,
          participant_id uuid references participant                not null,
          rentable_id    uuid references rentable                   not null,
          unique (date, participant_id, rentable_id)
        );

      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
