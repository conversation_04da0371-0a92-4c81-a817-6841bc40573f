import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table payment
              add column xendit_invoice_id text;

          alter table establishment
              add column xendit_user_id text unique;

alter table establishment add column xendit_invoice_callback_token text;

-- alter table establishment add column 
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
