import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

          alter table product
              add column deleted_at                 timestamptz,
              add column deleted_by_user_session_id uuid references user_session;


          -- drop table payment;
          create table payment
          (
              id                         uuid primary key,
              booking_id                 uuid references booking      not null,
              deposit                    bool        default false    not null,
              method                     text                         not null,
              amount                     numeric                      not null,
              currency                   text                         not null,
              payed_at                   timestamptz,
              created_at                 timestamptz default now()    not null,
              created_by_user_session_id uuid references user_session not null
          );

          -- for the existing payments:
-- what should be the payment method? other/unkown?
-- wat should be the payment type? deposit=false i guess
--           select gen_random_uuid(),
--                  booking.id,
--                  true,
--                  booking.paid_amount,
--                  establishment.default_currency,
--                  booking.created_at,
--                  booking.created_by_user_session_id
--           from booking
--                    inner join establishment on booking.establishment_id = establishment.id
--           where paid_amount is not null;

          alter table booking
              drop column if exists payed_amount;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
