import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table booking_dayschedule
              alter column booking_id set not null,
              drop start_datetime,
              drop boat_id,
              add column boat_departure_id uuid references boat_departure;

          alter table booking_dayschedule
              rename to booking_schedule;

          alter table booking_schedule
              drop constraint booking_dayschedule_boat_departure_id_fkey;

          alter table booking_schedule
              add constraint booking_dayschedule_boat_departure_id_fkey
                  foreign key (boat_departure_id) references boat_departure
                      on delete set null;

          alter table booking_schedule
              drop column diving_location,
              drop column shore;
          alter table booking_schedule
              rename to booking__boat_departure;

          create table booking_location
          (
              id              uuid primary key,
              booking_id      uuid not null,
              diving_location uuid not null,
              shore           bool not null,
              instructor      uuid
          );


          delete
          from booking__boat_departure
          where boat_departure_id is null;
          alter table booking__boat_departure
              alter column boat_departure_id set not null;

          alter table booking__boat_departure
              rename to booking_schedule;
          alter table booking_schedule
              rename column boat_departure_id to boat_departure_id__location_schedule_id;

          drop table booking_location;

          drop table if exists location_schedule;
          create table location_schedule
          (
              id                   uuid primary key,
              operator_location_id uuid not null references operator_location,
              diving_location      text not null,
              shore                bool not null,
              departure_time timestamptz not null
          );


          alter table booking_schedule
              drop constraint booking_dayschedule_instructor_id_fkey;

          -- alter table booking_schedule drop 

-- alter table 
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
