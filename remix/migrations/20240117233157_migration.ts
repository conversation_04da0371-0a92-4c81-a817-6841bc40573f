import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      create index trip_date_establishment_id_index
          on trip (date, establishment_id);

      create index booking_duration_establishment_id_index
          on booking (duration, establishment_id);


      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
