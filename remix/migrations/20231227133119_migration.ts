import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table diving_course
              add column diving_certificate_organization_key text;
          alter table diving_course
              add column diving_certificate_level_key text;

          update diving_course
          set diving_certificate_organization_key = (select replace(lower(diving_certificate_organization.name), ' ', '_')
                                                     from diving_certificate_organization
                                                     where diving_course.diving_certificate_organization_id =
                                                           diving_certificate_organization.id)
          where diving_course.diving_certificate_organization_key is null;

          update diving_course
          set diving_certificate_level_key = (select replace(lower(diving_certificate_level.name), ' ', '_')
                                              from diving_certificate_level
                                              where diving_course.diving_certificate_level_id =
                                                    diving_certificate_level.id)
          where diving_course.diving_certificate_level_key is null;

          update diving_course
          set diving_certificate_organization_key = upper(diving_certificate_organization_key)
          where diving_certificate_organization_key is not  null;

          alter table diving_course
              alter column diving_certificate_organization_key set not null;
          alter table diving_course
              alter column diving_certificate_level_key set not null;

          alter table diving_certificate_level
              rename to diving_certificate_level_old;
          alter table diving_certificate_organization
              rename to diving_certificate_organization_old;

          update diving_course
          set diving_certificate_level_key = 'professional'
          where diving_certificate_level_key = 'professionals';
          update diving_course
          set diving_certificate_level_key = 'refresher'
          where diving_certificate_level_key = 'refreshment';

          alter table diving_course
              alter column diving_certificate_organization_id drop not null;
          alter table diving_course
              alter column diving_certificate_level_id drop not null;

          --           select *, replace(lower(diving_certificate_organization.name), ' ', '_')
--           from diving_certificate_organization;
-- alter table diving_certificate_level 
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
