import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

--         alter table price
--           rename to product_price;


-- alter table product_price 

        create table price
        (
          id          uuid primary key default gen_random_uuid(),
          amount      numeric                  not null,
          currency_id text references currency not null,
          amount_usd  numeric                  not null,
          unique (amount, currency_id)
        );

        insert into price (amount, currency_id, amount_usd)
        select distinct on (product_price.amount, product_price.currency_id) product_price.amount,
                                                                             product_price.currency_id,
                                                                             (coalesce(
                                                                               nullif(product_price.amount, 0) /
                                                                               (select currency.conversion_rate_usd
                                                                                from currency
                                                                                where currency.id = product_price.currency_id
                                                                                limit 1),
                                                                               0
                                                                              ))
        from product_price;
        -- Your SQL statements here

        alter table product_price
          add column price_id uuid references price;

        update product_price
        set price_id = (select price.id
                        from price
                        where price.amount = product_price.amount
                          and price.currency_id = product_price.currency_id);

        alter table product_price
          alter column price_id set not null;
        alter table product_price
          drop column amount,
          drop column currency_id,
          drop column amount_usd;

        alter table price
          add column created_at                 timestamptz default now(),
          add column created_by_user_session_id uuid references user_session;
          
          alter table price alter column created_at set not null ;
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
