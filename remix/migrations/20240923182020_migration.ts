import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          select *
          from payment_method;
          alter table payment_method
              add column intuit_connection_id uuid references intuit_connection;

          alter table invoice
              add column intuit_connection_id    uuid references intuit_connection,
              add column intuit_invoice_id       text,
              add column intuit_invoice_response jsonb;

          select *
          from intuit_invoice;

          update invoice
          set intuit_invoice_id       = (select intuit_invoice.intuit_invoice_id
                                         from intuit_invoice
                                         where intuit_invoice.invoice_id = invoice.id),
              intuit_invoice_response = (select intuit_invoice.response
                                         from intuit_invoice
                                         where intuit_invoice.invoice_id = invoice.id),
              intuit_connection_id    = (select intuit_connection.id
                                         from intuit_invoice
                                                  inner join intuit_connection
                                                             on intuit_connection.realm_id = intuit_invoice.realm_id
                                         where intuit_invoice.invoice_id = invoice.id)
          where invoice.id in (select intuit_invoice.invoice_id from intuit_invoice);

          alter table intuit_invoice
              rename to intuit_invoice_old;



          alter table payment_method
              add column deleted_at                 timestamptz,
              add column deleted_by_user_session_id uuid references user_session;
          update payment_method
          set deleted_at = 'infinity'
          where payment_method.deleted_at is null;
          alter table payment_method
              alter column deleted_at set not null;


          alter table intuit_connection
              add column deleted_at                 timestamptz,
              add column deleted_by_user_session_id uuid references user_session;
          update intuit_connection
          set deleted_at = 'infinity'
          where intuit_connection.deleted_at is null;
          alter table intuit_connection
              alter column deleted_at set not null;

          alter table intuit_connection
              drop constraint intuit_connection_realm_id_key;

          alter table intuit_connection
              add unique (deleted_at, realm_id);

          alter table establishment
              rename column intuit_connection_id to intuit_connection_id_old;


          alter table payment_method
              drop constraint payment_method_establishment_id_key_key;

          alter table payment_method
              drop constraint payment_method_establishment_id_name_key;

          alter table payment_method
              add unique (deleted_at, establishment_id, name);

          alter table payment_method
              add unique (deleted_at, establishment_id, key);


          alter table establishment
              add column review_enabled bool not null default true;

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
