import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          -- Your SQL statements here
--           select product.id, product.root_id
--           from product
--           where id = '9d498638-2242-43b4-94d7-af48ef00f61d';
--           select *
--           from product
--           where product.root_id = 'bc1046e2-6532-4d52-a6e6-7fedd5974095';

          create table product_price
          (
              id          uuid primary key default gen_random_uuid(),
              product_id  uuid references product  not null,
              name        text,
--     duration text not null,
              amount      numeric                  not null,
              currency_id text references currency not null,
              amount_usd  numeric                  not null,
--               amount_usd  numeric generated always as (
--                   coalesce(
--                           nullif(amount, 0) / (select currency.conversion_rate_usd
--                                                from currency
--                                                where currency.id = currency_id
--                                                limit 1),
--                           0
--                   )
--                   ) stored                         not null,
              unique nulls not distinct (product_id, name)
          );

          insert into product_price (product_id, amount, currency_id, amount_usd)
          select product.id,
                 product.price,
                 product.price_currency,
                 (coalesce(
                         nullif(product.price, 0) / (select currency.conversion_rate_usd
                                                     from currency
                                                     where currency.id = product.price_currency
                                                     limit 1),
                         0
                  ))
          from product;

          alter table product
              alter column price drop not null,
              alter column price_currency drop not null,
              alter column price_usd drop not null;
          alter table product
              rename column price to price_old;
          alter table product
              rename column price_currency to price_currency_old;
          alter table product
              rename column price_usd to price_usd_old;
          drop trigger set_price_usd on product;
          drop function set_price_usd;

--           select count(*)
--           from event;

      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
