import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          --           alter table booking
--               add column invoice_generated_at timestamptz;
-- 
--           delete
--           from file_target
--           where file_target.target = 'booking_invoice';
--           alter table booking
--               drop column invoice_generated_at;

          alter table establishment
              add column vat_number text;

          alter table booking
              add column vat_rate int not null default 0;


          create table invoice
          (
              id                    uuid        default gen_random_uuid(),
              created_at            timestamptz default now(),
              booking_id            uuid references booking,
              customer_name         text not null,
              customer_address      text not null,
              customer_address2     text,
              customer_country_code char(2)
          );

          alter table invoice
              add unique (booking_id);

-- alter table booking add column 
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
