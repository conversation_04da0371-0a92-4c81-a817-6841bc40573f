import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table diving_course
              drop column diving_certificate_level_id;
          alter table diving_course
              drop column diving_certificate_organization_id;
          drop table diving_certificate_level_old;
          drop table diving_certificate_organization_old;

          alter table product
              drop column old_main_tag_id,
              drop column old_sub_tag_id,
              drop column old_price,
              drop column old_currency,
              drop column old_description,
              drop column old_title;
          drop table old_tag;

          alter table product
              drop column safety_diving_form_enabled_old,
              drop column medical_form_enabled_old,
              drop column liability_form_enabled_old;


          alter table booking
              drop column product_id_old,
              drop column duration_old,
              drop column number_of_participants_old,
              drop column price_pp_old,
              drop column duration_old_old;

          alter table participant
              drop column old_user_session_created_at,
              drop column old_user_session_id;

          drop table booking_addon_old;

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
