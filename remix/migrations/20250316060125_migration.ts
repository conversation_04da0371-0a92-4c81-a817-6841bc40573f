import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          -- Your SQL statements here
          alter table xendit_split_rule
              add column xendit_api_key_id uuid references xendit_api_key;
          update xendit_split_rule
          set xendit_api_key_id = (select xendit_api_key.id
                                   from xendit_api_key
                                   where
                                       xendit_api_key.xendit_environment_id = xendit_split_rule.xendit_environment_id);
          alter table xendit_split_rule
              alter column xendit_api_key_id set not null;
          alter table xendit_split_rule
              alter column xendit_environment_id drop not null;
          alter table xendit_split_rule
              rename column xendit_environment_id to xendit_environment_id_old;


      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
