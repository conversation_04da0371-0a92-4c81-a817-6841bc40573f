import { Kysely, sql } from "kysely";
import { <PERSON> } from "~/kysely/db";

export async function up(db: Kysely<DB>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

          alter table booking
              add column cached_final_amount numeric,
              add column cached_final_paid   numeric;

-- select booking.id, booking.cached_final_amount, booking.cached_final_paid, booking.cached_final_amount - booking.cached_final_paid from booking where booking.id = '2c0b3ed2-6175-49f9-8e34-ac500f313528'; 
          --           alter table booking
--               add column derived_price_total_final numeric;

--           select *
--           from booking;

          -- update booking set de


-- alter table payment alter column amount type numeric(30, 2);
-- select * from payment
-- where amount % 1 = 0;  

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
