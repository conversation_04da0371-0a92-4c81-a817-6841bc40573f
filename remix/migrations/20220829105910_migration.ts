import { <PERSON>ys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `alter table "event"
          alter column session_id drop default,
          alter column target_id drop not null,
          alter column role drop default;

      delete
      from "session"
      where id not in (select session_id from user_session)
        and id not in (select session_id from "event");

      alter table "session"
          drop column currency_switched;

      `
    )
    .execute(db);
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
}
