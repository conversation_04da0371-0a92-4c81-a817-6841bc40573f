import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table trip_assignment
              add column participant_id uuid;
          alter table trip_assignment
              drop column participant_id;
          create table participant_assignment
          (
              id                 uuid primary key default gen_random_uuid(),
              trip_assignment_id uuid not null,
              participant_id     uuid not null
          );
          alter table participant
              alter column user_id drop not null,
              add column user_session_id uuid references user_session,
              alter column first_name drop not null,
              alter column last_name drop not null,
              alter column phone drop not null,
              alter column emergency_contact_name drop not null,
              alter column emergency_contact_phone drop not null,
              alter column country_code drop not null,
              alter column address drop not null,
              alter column passport_number drop not null,
              alter column birth_date drop not null;

          alter table participant
              drop column user_id;

          update participant
          set user_session_id = created_by_user_session_id
          where participant.user_session_id is null;

          alter table booking
              alter column number_of_participants drop default,
              alter column number_of_participants drop not null;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
