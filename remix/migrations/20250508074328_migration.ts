import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
        -- Your SQL statements here
        alter table member
          add column permissions text[];

        update member
        set permissions = ARRAY ['metrics']
        where member.admin > 0 or member.owner = true;
      `,
    )
    .execute(db);
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
