import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table review
              add column created_by_user_session_id uuid references user_session;
          alter table review
              add column created_at timestamptz;

          update review
          set created_by_user_session_id = (select user_event.user_session_id
                                            from entity_action
                                                     inner join user_event on user_event.id = entity_action.user_event_id
                                            where entity_action.entity_name = 'review'
                                              and entity_action.entity_id = review.id
                                              and (entity_action.action_name = 'insert' or
                                                   entity_action.action_name = 'create'));

          update review
          set created_at = (select user_event.created_at
                            from entity_action
                                     inner join user_event on user_event.id = entity_action.user_event_id
                            where entity_action.entity_name = 'review'
                              and entity_action.entity_id = review.id
                              and (entity_action.action_name = 'insert' or
                                   entity_action.action_name = 'create'));
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
