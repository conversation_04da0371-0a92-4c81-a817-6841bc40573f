import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  await sql
    .raw(
      `
    
      alter table addon add column default_amount integer not null default 0;
      alter table addon add column selectable boolean not null default false;
      alter table addon rename column default_amount to amount;
      alter table addon rename column selectable to allow_change;
      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `
    )
    .execute(db);
}
