import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table booking
              add column paid_amount numeric,
              drop column paid;
          alter table booking_registration
              drop column paid;
alter table booking drop column pickup_location;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
