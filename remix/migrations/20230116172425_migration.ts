import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
          alter table "user"
              add column features text[];


          drop table "logging"."table_history";
          drop schema logging;

          drop trigger trigger_log_on_change on "user";
          drop function log_on_change_trigger;

          create table "booking"
          (
              id                   uuid primary key default gen_random_uuid() not null,
              product_id           uuid,
              message              text                                       not null,
              operator_location_id uuid references operator_location (id)     not null
          );

          create table "message"
          (
              id        uuid primary key default gen_random_uuid() not null,
              message   text                                       not null,
              target_id uuid                                       not null
          );

          alter table "booking" add column addons jsonb[];

      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
