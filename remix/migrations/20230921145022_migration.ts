import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table trip
              alter column activity_location drop not null;
          alter table trip
              alter column start_time drop not null;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
