import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
alter table product__diving_course alter column created_at drop not null, alter column created_by drop not null ;
`
    )
    .execute(db);
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
}
