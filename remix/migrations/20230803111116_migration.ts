import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

          update review
          set created_at = (select user_event.created_at
                            from entity_action
                                     inner join user_event on user_event.id = entity_action.user_event_id
                            where entity_id = review.id
                              and entity_action.action_name = 'insert'
                            limit 1)
          where review.created_at is null;

          update review
          set created_by_user_session_id = (select user_event.user_session_id
                                            from entity_action
                                                     inner join user_event on user_event.id = entity_action.user_event_id
                                            where entity_id = review.id
                                              and entity_action.action_name = 'insert'
                                            limit 1)
          where review.created_by_user_session_id is null;

          alter table review
              alter column created_at set default now();
          alter table review
              alter column created_at set not null;

--           alter table review
--               alter column created_by_user_session_id set not null;

--           select *,
--                  (select user_event.created_at
--                   from entity_action
--                            inner join user_event on user_event.id = entity_action.user_event_id
--                   where entity_id = review.id
--                     and entity_action.action_name = 'insert'
--                   limit 1)
--           from review
--           where created_at is null;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
