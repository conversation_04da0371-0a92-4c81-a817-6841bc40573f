import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      update product
      set forms = '{}'::text[]
      where forms is null;
      alter table product
          alter column forms set not null;
`,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
