import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
          alter table event
              add column user_id uuid;

          update event
          set user_id = (select us.user_id
                         from user_session us
                         where us.session_id = event.session_id
                           and us.verified = true
                           and us.created_at < event.created_at
                           and us.destroyed_at > event.created_at
                         limit 1);

          alter table event
              add constraint event_user_id_fkey foreign key (user_id) references "user" (id);

-- select extract(month from now())

          -- select event.type, event.tag, u.email, event.created_at from event inner join public."user" u on u.id = event.user_id order by created_at desc ;
-- select count(*) from event where user_id isnull;
--           select count(*)
--           from event
--                    inner join user_session us on us.session_id = event.session_id
--           where us.verified = true
--             and us.created_at < event.created_at
--             and us.destroyed_at > event.created_at;

      `
    )
    .execute(db);
}

export async function down(db: <PERSON><PERSON>ely<any>): Promise<void> {
  // Migration code
}
