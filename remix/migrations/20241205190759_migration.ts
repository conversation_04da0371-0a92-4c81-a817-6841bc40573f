import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `



          alter table callback
              add column started_at timestamptz;

          --           update callback
--           set started_at = '-infinity'
--           where handled_at is null
--             and
--           select callback.created_at,
--                  created_at + ((callback.delay_in_seconds + 60) * interval '1 seconds'),
--                  now() - ((callback.delay_in_seconds + 60) * interval '1 seconds')               as now_minus_interval,
--                  callback.created_at > now() -
--                                        ((callback.delay_in_seconds + 60) * interval '1 seconds') as now_minus_interval,
-- --                  callback.created_at < (now() - (created_at + ((callback.delay_in_seconds + 60) * interval '1 seconds'))::timestamptz),
--                  callback.*
--           from callback
--           where handled_at is null;


          update callback
          set started_at = '-infinity',
              handled_at = coalesce(callback.handled_at, now()),
              success    = coalesce(callback.success, false),
              msg        = case
                               when callback.handled_at is null and callback.msg is null then 'timeout, cancelled'
                               else callback.msg end
          where callback.created_at + ((callback.delay_in_seconds + 60) * interval '1 seconds') < now();

          alter table payment
              rename column intuit_payment_id to intuit_payment_id_old;

          -- delete from callback where callback.started_at is null;
-- select count(*) from callback where callback.started_at is null;


--           select callback.created_at,
--                  callback.name,
--                  callback.target_id,
--                  callback.msg,
--                  callback.success,
--                  callback.created_at +
--                  ((callback.delay_in_seconds + 60) * interval '1 seconds')                       as created_plus_interval,
--                  now()                                                                           as now,
--                  created_at + ((callback.delay_in_seconds + 60) * interval '1 seconds'),
--                  now() - ((callback.delay_in_seconds + 60) * interval '1 seconds')               as now_minus_interval,
--                  callback.created_at > now() -
--                                        ((callback.delay_in_seconds + 60) * interval '1 seconds') as now_minus_interval
--           from callback
--           where callback.created_at + ((callback.delay_in_seconds + 60) * interval '1 seconds') < now()
--             and callback.handled_at is null;

      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
