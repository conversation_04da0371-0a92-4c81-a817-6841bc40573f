import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          create table tag
          (
              id               uuid primary key     default gen_random_uuid(),
              created_at       timestamptz not null default now(),
              establishment_id uuid references establishment,
              key              text        not null,
              name             text        not null,
              unique (establishment_id, key)
          );

          create table product__tag
          (
              id         uuid primary key default gen_random_uuid(),
              tag_id     uuid references tag,
              product_id uuid references product,
              unique (tag_id, product_id)
          );
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
