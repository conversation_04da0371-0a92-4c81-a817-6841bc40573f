import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table participant
              rename column booking_id_old to booking_id;

          alter table participant
              add column booking_id_migration_2024_11_14 uuid;
          update participant
          set booking_id_migration_2024_11_14 = participant.booking_id;

          update participant
          set booking_id = (select activity.booking_id
                            from participation
                                     inner join activity on participation.activity_id = activity.id
                            where participation.participant_id = participant.id
                            limit 1)
          where booking_id is null;

          select participant.customer_id, participant.booking_id, count(participant.id)
          from participant
          group by participant.customer_id, participant.booking_id
          having count(participant.id) > 1;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
