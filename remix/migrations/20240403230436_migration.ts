import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

          update region
          set timezone = 'Africa/Cairo'
          where timezone is null
            and country_code = 'EG';

          update region
          set timezone = 'Asia/Makassar'
          where timezone is null;

          alter table region
              alter column timezone set not null;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
