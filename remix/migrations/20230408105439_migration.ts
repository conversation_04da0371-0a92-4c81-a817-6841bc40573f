import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

          alter table booking_registration
              add column addons uuid[] not null default '{}';
          drop table if exists booking_registration_addon;
          alter table booking_addon
              drop column price_currency;
          alter table booking_addon
              rename column show to allow_customer_change;
          alter table booking_addon
              rename column allow_customer_change to allow_change;



-- get from and to for booking.duration
--           select jsonb_build_object('from', lower(duration), 'to', upper(duration)), duration, *
--           from booking
--           where id = '292e2f04-2d7e-4a2a-9b06-dfe20101d98e';
--             and duration @> tstzrange(null, null, '[)');

--           update booking
--           set duration = tstzrange((upper(duration) - interval '1 hours' ), upper(duration), '[)')
--           where id = '292e2f04-2d7e-4a2a-9b06-dfe20101d98e';
-- 
--           update booking
--           set duration = tstzrange('-infinity', upper(duration), '[)')
--           where id = '292e2f04-2d7e-4a2a-9b06-dfe20101d98e';
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
