import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      alter table "user"
          drop column email_lower;

update "user" set email = lower(email) where email != lower(email);
--       select * from "user" where lower("user".email) != "user".email;
  `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
