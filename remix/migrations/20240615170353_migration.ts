import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          create table view
          (
              id          uuid primary key,
--               created_at                 timestamptz                   not null,
--               created_by_user_session_id uuid                          not null references master.public.user_session,
              name        text                     not null,
              sort_order  int                      not null,
              sorts       jsonb                    not null,
              columns     text[]                   not null,
              operator_id uuid references operator not null,
              unique (operator_id, name)
          );

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
