import { Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          delete
          from participant
          where establishment_id is null
            and establishment_id not in (select coalesce(entity_action.data ->> 'establishment_id',
                                                         entity_action.data ->> 'operator_location_id')::uuid
                                         from entity_action
                                         where entity_action.entity_id = participant.booking_id
                                           and entity_action.entity_name = 'booking'
                                           and entity_action.action_name = 'insert');


          update participant
          set establishment_id = (select coalesce(entity_action.data ->> 'establishment_id',
                                                  entity_action.data ->> 'operator_location_id')::uuid
                                  from entity_action
                                  where entity_action.entity_id = participant.booking_id
                                    and entity_action.entity_name = 'booking'
                                    and entity_action.action_name = 'insert')
          where establishment_id is null;

          alter table participant
              alter column establishment_id set not null;

          alter table booking
              rename column duration_old to duration_old_old;
          alter table booking
              rename column duration to duration_old;
          alter table booking
              rename column product_id to product_id_old;
          alter table booking
              alter column price_pp drop not null;
          alter table booking
              rename column price_pp to price_pp_old;

          alter table booking
              rename column number_of_participants to number_of_participants_old;

          create table activity
          (
              id                         uuid primary key                      default gen_random_uuid(),
              booking_id                 uuid references booking      not null,
              product_id                 uuid references product      not null,
              duration                   daterange                    not null,
              price_pp                   numeric                      not null,
              created_at                 timestamptz                  not null default now(),
              created_by_user_session_id uuid references user_session not null
          );

          create table participation
          (
              id                         uuid primary key                      default gen_random_uuid(),
              activity_id                uuid references activity     not null,
              participant_id             uuid references participant,
              created_at                 timestamptz                  not null default now(),
              created_by_user_session_id uuid references user_session not null
          );

          insert into activity (booking_id, product_id, duration, price_pp, created_at, created_by_user_session_id)
          select booking.id,
                 booking.product_id_old,
                 booking.duration_old,
                 booking.price_pp_old,
                 booking.created_at,
                 booking.created_by_user_session_id
          from booking;

          insert into participation (activity_id, participant_id, created_at, created_by_user_session_id)
          select activity.id,
                 case when participant.user_id is null then null else participant.id end,
                 booking.created_at,
                 booking.created_by_user_session_id
          from participant
                   inner join booking on booking.id = participant.booking_id
                   inner join activity on activity.booking_id = booking.id;

          create table activity_addon
          (
              id           uuid primary key                                    default gen_random_uuid(),
              activity_id  uuid references activity on delete cascade not null,
              addon_id     uuid                                       not null,
              allow_change bool                                       not null default false,
              amount       integer                                             default 0 not null
          );

          create table participation_addon
          (
              id               uuid primary key default gen_random_uuid(),
              addon_id         uuid                       not null,
              participation_id uuid                       not null references participation on delete cascade,
              amount           integer          default 0 not null
          );

          insert into activity_addon (id, activity_id, addon_id, allow_change, amount)
          select booking_addon.id, activity.id, booking_addon.addon_id, booking_addon.allow_change, booking_addon.amount
          from booking_addon
                   inner join activity on activity.booking_id = booking_addon.booking_id;

          insert into participation_addon (id, addon_id, participation_id, amount)
          select participant_addon.id, participant_addon.addon_id, participation.id, participant_addon.amount
          from participant_addon
                   inner join participation on participation.participant_id = participant_addon.participant_id;

          select count(*) as noUserIdParticipats
          from participant
          where user_id is null;
          select count(*) as bookignCountOld
          from booking;
          select count(*) as activityCount
          from activity;
          select count(*) as participantCount
          from participant;
          select count(*) as participationCount
          from participation;
          select count(*) as bookingAddonCountOld
          from booking_addon;
          select count(*) as activityAddonCount
          from activity_addon;
          select count(*) as participantAddonCountOld
          from participant_addon;
          select count(*) as participationAddonCount
          from participant_addon;


          alter table booking_addon
              rename to booking_addon_old;
          alter table participant_addon
              rename to participant_addon_old;



          delete
          from participant
          where user_id is null;



          --           alter table participant
--               alter column booking_id drop not null;
          alter table participant
              alter column user_id set not null;

          --           alter table participant
--               alter column created_by_user_session_id drop not null;


          alter table trip_assignment
              rename column participant_id to participant_id_old;
          alter table trip_assignment
              add participation_id uuid references participation;

          update trip_assignment
          set participation_id = (select participation.id
                                  from participation
                                  where participation.participant_id = trip_assignment.participant_id_old)
          where trip_assignment.participant_id_old is not null;

          alter table booking
              alter column product_id_old drop not null;


          alter table activity_addon
              add column price numeric,
              add column unit  text,
              add column name  text;
          update activity_addon
          set price = (select booking_addon_old.price
                       from booking_addon_old
                       where booking_addon_old.id = activity_addon.id)
            , unit  = (select booking_addon_old.unit
                       from booking_addon_old
                       where booking_addon_old.id = activity_addon.id)
            , name  = (select booking_addon_old.name
                       from booking_addon_old
                       where booking_addon_old.id = activity_addon.id)
          where price is null;

          alter table activity_addon
              alter column price set not null,
              alter column unit set not null,
              alter column name set not null;

          --           select *
--           from trip_assignment
--                    inner join trip on trip_assignment.trip_id = trip.id
--                    inner join establishment on trip.establishment_id = establishment.id
--           where establishment.operator_id = 'c4a9a9df-1c64-4845-8df9-8dcb16baab40';
-- 
--           select *
--           from trip_assignment
--           where participation_id is not null;

-- select booking.cancelled_at, * from activity inner join booking on booking.id = activity.booking_id where activity.duration && '[2024-01-17,2024-01-17]';
-- select * from trip where trip.date = '2024-01-17'::date;

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
