import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
          alter table diving_site
              alter column highlights type text;
          alter table diving_location
              alter column highlights type text;
-- 
--           create table marine_life
--           (
--               id    uuid primary key,
--               name  text not null unique,
--               type  text not null,
--               files text[]
--           )
      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
