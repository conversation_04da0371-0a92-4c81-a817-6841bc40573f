import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table form
              add column deleted_at timestamptz not null default 'infinity',
              add column deleted_by_user_session_id uuid references user_session,
              add column root_id    uuid,
              add unique (root_id, deleted_at);
          update form
          set root_id = id;
          alter table form
              alter column root_id set not null;

          alter table form
              drop constraint form_target_id_type_name_key;

          alter table form
              add unique (target_id, deleted_at, type, name);
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
