import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table participant
              add column cache_read_waivers_valid bool;
          update participant
          set cache_stale = true
          where cache_stale = false;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
