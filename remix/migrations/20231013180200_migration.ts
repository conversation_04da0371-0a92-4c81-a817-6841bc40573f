import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table signature
              add column signed_by_representative bool default false not null ;

          alter table signature
              drop constraint signature_target_id_form_name_key;

          alter table signature
              add unique (target_id, form_name, signed_by_representative);

          update signature
          set signed_by_representative = true, form_name = 'liability'
          where form_name = 'liability_guardian';

          update signature
          set signed_by_representative = true, form_name = 'safety_diving'
          where form_name = 'safety_diving_guardian';

          update signature
          set form_name = 'safety_diving'
          where form_name = 'safety_diving_participant';

          update signature
          set form_name = 'liability'
          where form_name = 'liability_participant';


-- update signature 
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
