import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `alter table diving_course
          add constraint fk_diving_certificate_level foreign key (diving_certificate_level_id) references diving_certificate_level (id);
      alter table diving_course
          add constraint fk_diving_certificate_organization foreign key (diving_certificate_organization_id) references diving_certificate_organization (id)
      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
