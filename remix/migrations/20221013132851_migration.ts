import { Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
          alter table mutation_event
              rename to user_event;
          create table action
          (
              id            serial primary key              not null,
              target_id     uuid,
              name          text                            not null,
              table_name    text,
              user_event_id uuid references user_event (id) not null,
              data          jsonb
          );

          alter table operator_location
              alter column id drop default,
              alter column published drop default;

-- 
--           alter table product
--               rename column description to old_description;


          with user_event_inserts as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         operator_location.created_at,
                         operator_location.created_by,
                         operator_location.id
                  from operator_location returning user_event.*)
             , insert_actions as (insert
              into "action" (target_id, name, table_name, user_event_id, data)
                  select operator_location.id,
                         'create',
                         'operator_location',
                         user_event_inserts.id,
                         to_jsonb(operator_location)
                  from user_event_inserts
                           inner join operator_location on operator_location.id = user_event_inserts.target_id
                  returning "action".*)
             , user_event_updates as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         operator_location.updated_at,
                         operator_location.updated_by,
                         operator_location.id
                  from operator_location
                  where operator_location.updated_at is not null
                    and operator_location.updated_by is not null returning user_event.*)
          insert
          into "action" (target_id, name, table_name, user_event_id)
          select user_event_updates.target_id, 'update', 'operator_location', user_event_updates.id
          from user_event_updates;

          drop policy if exists product_editor on product;
          drop policy if exists product_me on product;
          drop policy if exists product_user on product;

          drop policy if exists visit_insert on event;
          drop policy if exists visit_select on event;

          drop policy if exists operator_editor on operator;
          drop policy if exists operator_me on operator;
          drop policy if exists operator_user on operator;

          drop policy if exists operator_location_editor on operator_location;
          drop policy if exists operator_location_me on operator_location;
          drop policy if exists operator_location_user on operator_location;


          drop policy if exists product__diving_course_editor on product__diving_course;
          drop policy if exists product__diving_course_me on product__diving_course;
          drop policy if exists product__diving_course_user on product__diving_course;

          drop policy if exists product__diving_site_editor on product__diving_site;
          drop policy if exists product__diving_site_me on product__diving_site;
          drop policy if exists product__diving_site_user on product__diving_site;

          drop policy if exists product__diving_location_editor on product__diving_location;
          drop policy if exists product__diving_location_me on product__diving_location;
          drop policy if exists product__diving_location_user on product__diving_location;

          drop policy if exists product__diving_type_editor on product__diving_type;
          drop policy if exists product__diving_type_me on product__diving_type;
          drop policy if exists product__diving_type_user on product__diving_type;


          drop table if exists product__snorkel_site;
          drop table if exists product__snorkel_location;


          alter table product
              drop constraint product_activity_id_fk;

          alter table review
              drop constraint review_activity_id_fkey2;

          alter table activity
              alter column id type uuid using id::uuid;
          alter table product
              alter column activity_id type uuid using activity_id::uuid;
          alter table review
              alter column activity_id type uuid using activity_id::uuid;

          alter table review
              add constraint review_activity_id_fkey
                  foreign key (activity_id) references activity
                      on update restrict on delete restrict;

          alter table product
              add constraint product_activity_id_fkey
                  foreign key (activity_id) references activity
                      on update restrict on delete restrict;

          alter table activity
              alter column id drop default,
              alter column published drop default,
              alter column files_json drop default,
              alter column files set not null,
              alter column files drop default,
              drop column fields,
              drop column domain;

          select *
          from activity;
          with user_event_inserts as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         activity.created_at,
                         activity.created_by,
                         activity.id
                  from activity returning user_event.*)
             , insert_actions as (insert
              into "action" (target_id, name, table_name, user_event_id, data)
                  select activity.id,
                         'create',
                         'activity',
                         user_event_inserts.id,
                         to_jsonb(activity)
                  from user_event_inserts
                           inner join activity on activity.id = user_event_inserts.target_id
                  returning "action".*)
             , user_event_updates as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         activity.updated_at,
                         activity.updated_by,
                         activity.id
                  from activity
                  where activity.updated_at is not null
                    and activity.updated_by is not null returning user_event.*)
          insert
          into "action" (target_id, name, table_name, user_event_id)
          select user_event_updates.target_id, 'update', 'activity', user_event_updates.id
          from user_event_updates;

          select *
          from country;
          with user_event_inserts as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         country.created_at,
                         country.created_by,
                         country.id
                  from country returning user_event.*)
             , insert_actions as (insert
              into "action" (target_id, name, table_name, user_event_id, data)
                  select country.id,
                         'create',
                         'country',
                         user_event_inserts.id,
                         to_jsonb(country)
                  from user_event_inserts
                           inner join country on country.id = user_event_inserts.target_id
                  returning "action".*)
             , user_event_updates as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         country.updated_at,
                         country.updated_by,
                         country.id
                  from country
                  where country.updated_at is not null
                    and country.updated_at != country.created_at
                    and country.updated_by is not null returning user_event.*)
             , update_actions as (insert
              into "action" (target_id, name, table_name, user_event_id)
                  select user_event_updates.target_id, 'update', 'country', user_event_updates.id
                  from user_event_updates returning action.*)
          select (select count(*) from insert_actions) as insert_count,
                 (select count(*) from update_actions) as update_count;

          select *
          from diving_course;
          with user_event_inserts as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         diving_course.created_at,
                         diving_course.created_by,
                         diving_course.id
                  from diving_course
                  where diving_course.created_at is not null returning user_event.*)
             , insert_actions as (insert
              into "action" (target_id, name, table_name, user_event_id, data)
                  select diving_course.id,
                         'create',
                         'diving_course',
                         user_event_inserts.id,
                         to_jsonb(diving_course)
                  from user_event_inserts
                           inner join diving_course on diving_course.id = user_event_inserts.target_id
                  returning "action".*)
             , user_event_updates as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         diving_course.updated_at,
                         diving_course.updated_by,
                         diving_course.id
                  from diving_course
                  where diving_course.updated_at is not null
                    and diving_course.updated_at != diving_course.created_at
                    and diving_course.updated_by is not null returning user_event.*)
             , update_actions as (insert
              into "action" (target_id, name, table_name, user_event_id)
                  select user_event_updates.target_id, 'update', 'diving_course', user_event_updates.id
                  from user_event_updates returning action.*)
          select (select count(*) from insert_actions) as insert_count,
                 (select count(*) from update_actions) as update_count;



          select *
          from operator;
          with user_event_inserts as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         operator.created_at,
                         operator.created_by,
                         operator.id
                  from operator
                  where operator.created_at is not null returning user_event.*)
             , insert_actions as (insert
              into "action" (target_id, name, table_name, user_event_id, data)
                  select operator.id,
                         'create',
                         'operator',
                         user_event_inserts.id,
                         to_jsonb(operator)
                  from user_event_inserts
                           inner join operator on operator.id = user_event_inserts.target_id
                  returning "action".*)
             , user_event_updates as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         operator.updated_at,
                         operator.updated_by,
                         operator.id
                  from operator
                  where operator.updated_at is not null
                    and operator.updated_at != operator.created_at
                    and operator.updated_by is not null returning user_event.*)
             , update_actions as (insert
              into "action" (target_id, name, table_name, user_event_id)
                  select user_event_updates.target_id, 'update', 'operator', user_event_updates.id
                  from user_event_updates returning action.*)
          select (select count(*) from insert_actions) as insert_count,
                 (select count(*) from update_actions) as update_count;


          select *
          from owner;
          with user_event_inserts as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         owner.created_at,
                         owner.created_by,
                         owner.id
                  from owner
                  where owner.created_at is not null returning user_event.*)
             , insert_actions as (insert
              into "action" (target_id, name, table_name, user_event_id, data)
                  select owner.id,
                         'create',
                         'owner',
                         user_event_inserts.id,
                         to_jsonb(owner)
                  from user_event_inserts
                           inner join owner on owner.id = user_event_inserts.target_id
                  returning "action".*)
             , user_event_updates as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         owner.updated_at,
                         owner.updated_by,
                         owner.id
                  from owner
                  where owner.updated_at is not null
                    and owner.updated_at != owner.created_at
                    and owner.updated_by is not null returning user_event.*)
             , update_actions as (insert
              into "action" (target_id, name, table_name, user_event_id)
                  select user_event_updates.target_id, 'update', 'owner', user_event_updates.id
                  from user_event_updates returning action.*)
          select (select count(*) from insert_actions) as insert_count,
                 (select count(*) from update_actions) as update_count;


          alter table product
              add column boat_dive boolean;
          alter table product
              add column night_dive boolean;
          alter table product
              add column shore_dive boolean;


          update product
          set boat_dive = true
          where id in (select pdt.product_id
                       from product__diving_type pdt
                                inner join diving_type dt on dt.id = pdt.diving_type_id
                       where dt.name = 'boat');

          update product
          set shore_dive = true
          where id in (select pdt.product_id
                       from product__diving_type pdt
                                inner join diving_type dt on dt.id = pdt.diving_type_id
                       where dt.name = 'shore');

          update product
          set night_dive = true
          where id in (select pdt.product_id
                       from product__diving_type pdt
                                inner join diving_type dt on dt.id = pdt.diving_type_id
                       where dt.name = 'night');


          alter table product
              alter column elearning_included drop default,
              alter column id drop default,
              alter gear_included drop default;

          select category_g
          from product
          where category_g is not null;

          alter table action
              rename to table_action;
          alter table table_action
              alter column table_name set not null;
          alter table table_action
              alter column target_id set not null;

          alter table diving_type
              rename to old_diving_type;
          alter table product__diving_type
              rename to old_product__diving_type;


          with p as (select product.*,
                            (select array_agg(product__diving_course.diving_course_id)
                             from product__diving_course
                             where product__diving_course.product_id = product.id)   as diving_course_ids,
                            (select array_agg(product__diving_location.diving_location_id)
                             from product__diving_location
                             where product__diving_location.product_id = product.id) as diving_location_ids,
                            (select array_agg(product__diving_site.diving_site_id)
                             from product__diving_site
                             where product__diving_site.product_id = product.id)     as diving_site_ids
                     from product)
             , user_event_inserts as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         p.created_at,
                         p.created_by,
                         p.id
                  from p
                  where p.created_at is not null returning user_event.*)
             , insert_actions as (insert
              into table_action (target_id, name, table_name, user_event_id, data)
                  select p.id,
                         'create',
                         'product',
                         user_event_inserts.id,
                         to_jsonb(p)
                  from user_event_inserts
                           inner join p on p.id = user_event_inserts.target_id
                  returning table_action.*)
             , user_event_updates as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         p.updated_at,
                         p.updated_by,
                         p.id
                  from p
                  where p.updated_at is not null
                    and p.updated_at != p.created_at
                    and p.updated_by is not null returning user_event.*)
             , update_actions as (insert
              into table_action (target_id, name, table_name, user_event_id)
                  select user_event_updates.target_id, 'update', 'product', user_event_updates.id
                  from user_event_updates returning table_action.*)
          select (select count(*) from insert_actions) as insert_count,
                 (select count(*) from update_actions) as update_count;


          with user_event_inserts as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         region.created_at,
                         region.created_by,
                         region.id
                  from region
                  where region.created_at is not null returning user_event.*)
             , insert_actions as (insert
              into table_action (target_id, name, table_name, user_event_id, data)
                  select region.id,
                         'create',
                         'region',
                         user_event_inserts.id,
                         to_jsonb(region)
                  from user_event_inserts
                           inner join region on region.id = user_event_inserts.target_id
                  returning table_action.*)
             , user_event_updates as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         region.updated_at,
                         region.updated_by,
                         region.id
                  from region
                  where region.updated_at is not null
                    and region.updated_at != region.created_at
                    and region.updated_by is not null returning user_event.*)
             , update_actions as (insert
              into table_action (target_id, name, table_name, user_event_id)
                  select user_event_updates.target_id, 'update', 'region', user_event_updates.id
                  from user_event_updates returning table_action.*)
          select (select count(*) from insert_actions) as insert_count,
                 (select count(*) from update_actions) as update_count;



          with user_event_inserts as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         spot.created_at,
                         spot.created_by,
                         spot.id
                  from spot
                  where spot.created_at is not null returning user_event.*)
             , insert_actions as (insert
              into table_action (target_id, name, table_name, user_event_id, data)
                  select spot.id,
                         'create',
                         'spot',
                         user_event_inserts.id,
                         to_jsonb(spot)
                  from user_event_inserts
                           inner join spot on spot.id = user_event_inserts.target_id
                  returning table_action.*)
             , user_event_updates as (
              insert into user_event (id, created_at, user_id, target_id)
                  select gen_random_uuid(),
                         spot.updated_at,
                         spot.updated_by,
                         spot.id
                  from spot
                  where spot.updated_at is not null
                    and spot.updated_at != spot.created_at
                    and spot.updated_by is not null returning user_event.*)
             , update_actions as (insert
              into table_action (target_id, name, table_name, user_event_id)
                  select user_event_updates.target_id, 'update', 'spot', user_event_updates.id
                  from user_event_updates returning table_action.*)
          select (select count(*) from insert_actions) as insert_count,
                 (select count(*) from update_actions) as update_count;


          alter table tag
              rename to old_tag;

          alter table diving_course
              drop constraint fk_diving_certificate_level;

          alter table diving_course
              add constraint fk_diving_certificate_level
                  foreign key (diving_certificate_level_id) references diving_certificate_level
                      on update restrict on delete restrict;

          alter table diving_course
              drop constraint fk_diving_certificate_organization;

          alter table diving_course
              add constraint fk_diving_certificate_organization
                  foreign key (diving_certificate_organization_id) references diving_certificate_organization
                      on update restrict on delete restrict;

      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
