import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table waiver
              add column signature_required bool;
          update waiver
          set signature_required = true
          where signature_required is null;
          alter table waiver
              alter column signature_required set not null;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
