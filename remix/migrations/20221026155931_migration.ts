import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
          alter table diving_site
              drop column if exists files_json,
              add column if not exists highlights                   text[],

              add column if not exists short                        text,

              add column if not exists difficulty_level             int check ( difficulty_level between 0 and 3),
              add column if not exists difficulty_info              text,
              add column if not exists access_via_boat              bool,
              add column if not exists access_via_shore             bool,

              add column if not exists reef                         bool,
              add column if not exists wall                         bool,
              add column if not exists cave                         bool,
              add column if not exists wreck                        bool,
              add column if not exists muck                         bool,

              add column if not exists open_water                   bool,
              add column if not exists night                        bool,
              add column if not exists drift                        bool,
              add column if not exists macro                        bool,
              add column if not exists exploration                  bool,
              add column if not exists slope                        bool,
              add column if not exists snorkeling                   bool,

              add column if not exists ice                          bool,
              add column if not exists technical                    bool,
              add column if not exists altitude                     bool,

              add column if not exists depth_range_in_meters        int4range,
              add column if not exists current_range_in_level       int4range,
              add column if not exists visibility_range_in_meters   int4range,
              add column if not exists temperature_range_in_celsius int4range,
              add column if not exists sources                      text;

          alter table diving_location
              add column if not exists files      text[],
              add column if not exists highlights text[];

          alter table diving_location
              add column short text;
      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
