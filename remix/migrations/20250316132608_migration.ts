import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          -- Your SQL statements here
          alter table xendit_api_key
              rename to xendit_environment;
          alter table xendit_split_rule
              rename column xendit_api_key_id to xendit_environment_id;
              alter table xendit_environment rename column xendit_environment_id to xendit_platform_id;
alter table xendit_account rename column xendit_environment_id to xendit_platform_id;
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
