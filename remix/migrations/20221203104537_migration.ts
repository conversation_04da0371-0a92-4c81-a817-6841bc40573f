import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
          alter table session
              drop column raw;

          alter table user_session
              drop column raw,
              drop column selected_role;

          alter table user_session
              add column display_name text;

          create table review
          (
              id                   uuid primary key,
              operator_location_id uuid references operator_location (id)         not null,
              activity_id          uuid references activity (id)                  not null,
              experience_rating    int check ( experience_rating between 1 and 5) not null,
              positives            text[],
              negatives            text[],
              experience           text
          );

          alter table user_session
              alter column session_id set not null;

          alter table user_event
              add column user_session_id uuid references user_session (id);

          delete
          from user_event
          where user_event.id not in (select user_event_id from entity_action);

          select distinct u.email
          from user_event
                   inner join "user" u on u.id = user_event.user_id;

          update user_event
          set user_session_id = (select id
                                 from user_session
                                 where user_event.user_id = user_session.user_id
                                   and user_event.created_at between user_session.created_at and user_session.destroyed_at
                                   and user_session.verified = true
                                 limit 1)
          where user_id is not null;



          insert into session (created_at)
          values ('-infinity');


          with users as (select distinct user_event.user_id as user_id
                         from user_event
                         where user_session_id is null
                           and user_id is not null)
          insert
          into user_session (user_id, created_at, session_id)
          select users.user_id, '-infinity', (select id from session where created_at = '-infinity' limit 1)
          from users
          returning user_session.id;

          update user_event
          set user_session_id = (select id
                                 from user_session
                                 where user_event.user_id = user_session.user_id
                                   and user_event.created_at between user_session.created_at and user_session.destroyed_at
                                   and user_session.verified = false
                                 limit 1)
          where user_id is not null
            and user_session_id is null;

          alter table user_event
              drop column user_id;
      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
