import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table participant
              drop column verified_at;
          alter table participant
              drop column on_my_behalf;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
