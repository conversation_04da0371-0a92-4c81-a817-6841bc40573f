import type { Kysely } from "kysely";
import { sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table booking
              drop column "from",
              drop column "to";
          alter table boat_departure
              alter column diving_location drop not null;
          alter table boat_departure
              rename column diving_location to diving_location_info;
          alter table boat_departure
              add column diving_location_id uuid references diving_location (id);
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
