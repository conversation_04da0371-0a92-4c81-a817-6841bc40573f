import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table member
              rename column first_name to old_first_name;
          alter table member
              rename column last_name to old_last_name;
          alter table member
              add column name text;

          create unique index member_establishment_id_name_idx
              on member (establishment_id, name)
              where (user_id IS NULL);

          drop index member_establishment_id_first_name_last_name_idx;

          update member
          set name = member.old_first_name || ' ' || member.old_last_name
          where name is null;
          alter table member
              alter column name set not null;

--           select establishment_id, first_name, last_name, count(*)
--           from member
--           group by establishment_id, first_name, last_name
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
