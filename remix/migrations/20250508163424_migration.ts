import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
        -- Your SQL statements here
        alter table activity
          add column quantity int not null default 0;

        update activity
        set quantity = (select count(*)
                        from participation
                        where participation.activity_id = activity.id);


        delete
        from participation
        where participation.participant_id is null
          and participation.activity_id in (select activity.id from activity where activity.duration is null);


--         select count(*)
--         from activity
--         where activity.duration is null;
-- 
-- select distinct activity.quantity from activity;
-- 
-- 
--         select activity.duration is null, count(activity.id)
--         from activity
--         where activity.id in
--               (select participation.activity_id from participation where participation.participant_id is null)
--         group by activity.duration is null;
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
