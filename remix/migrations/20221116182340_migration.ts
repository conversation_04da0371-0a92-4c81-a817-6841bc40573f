import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
          create table operator_location__language
          (
              id                   uuid primary key                       not null,
              operator_location_id uuid references operator_location (id) not null,
              language_code        char(2)                                not null,
              unique (language_code, operator_location_id)
          );


          --           alter table operator_location
--               add column languages char(2)[] not null default array []::char(2)[];

      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
