import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          drop table if exists signup_submission;
          create table signup_submission
          (
              id            uuid primary key     default gen_random_uuid(),
              created_at    timestamptz not null default now(),
              email_send_at timestamptz,
              email         text        not null,
              full_name     text        not null,
              phone         text,
              job_title     text,
              vat_number    text,
              company       text,
              country       text,
              comment       text,
              website       text
          );

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
