import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          select *
          from invoice;
          alter table invoice
              alter column customer_address drop not null,
              alter column customer_country_code drop not null;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
