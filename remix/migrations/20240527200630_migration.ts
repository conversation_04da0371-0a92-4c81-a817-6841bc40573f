import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          create table xendit_account
          (
              id                               uuid primary key default gen_random_uuid(),
              xendit_user_id                   text unique not null,
              xendit_invoice_callback_token    text        not null,
              xendit_account_response          jsonb,
              xendit_invoice_callback_response jsonb
--               xendit_account_name              text        not null,
          );


          insert into xendit_account (xendit_user_id, xendit_invoice_callback_token)
          select distinct on (establishment.xendit_user_id) establishment.xendit_user_id,
                                                            establishment.xendit_invoice_callback_token
          from establishment
          where establishment.xendit_user_id is not null;

          alter table establishment
              rename column xendit_user_id to xendit_user_id_old;
          alter table establishment
              rename column xendit_invoice_callback_token to xendit_invoice_callback_token_old;

          alter table establishment
              add column xendit_account_id uuid references xendit_account(id);

          update establishment
          set xendit_account_id = (select xendit_account.id
                                   from xendit_account
                                   where xendit_account.xendit_user_id = establishment.xendit_user_id_old)
          where establishment.xendit_user_id_old is not null;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
