import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table waiver
              add column medical bool default false not null;

          insert into waiver (slug, establishment_id, diving_certificate_organization_key, medical)
          values ('medical', null, null, true);

          insert into waiver_translation (waiver_id, name, markdoc, language_code)
          select waiver.id, 'medical', '', 'en'
          from waiver
          where waiver.medical = true;

          create table form_waiver
          (
              id        uuid primary key default gen_random_uuid(),
              form_id   uuid references form   not null,
              waiver_id uuid references waiver not null,
              unique (form_id, waiver_id)
          );

          insert into form_waiver (form_id, waiver_id)
          select form.id, waiver.id
          from waiver
                   inner join form on form.forms @> array [waiver.slug]
          where waiver.establishment_id is null;

          create table participant_waiver
          (
              id                   uuid primary key default gen_random_uuid(),
              participant_id       uuid references participant not null,
              waiver_id            uuid references waiver      not null,
              signed_language_code text,
              unique (participant_id, waiver_id)
          );

          insert into participant_waiver (participant_id, waiver_id, signed_language_code)
          select distinct on (p.participantId, p.waiver_id) p.participantId, p.waiver_id, p.lang
          from (select participant.id as participantId, waiver.id as waiver_id, null as lang
                from answer
                         inner join participant on answer.target_id = participant.id
                         inner join waiver on answer.form_name = waiver.slug
                union all
                select participant.id as participantId, waiver.id as waiver_id, 'EN' as lang
                from signature
                         inner join participant on signature.target_id = participant.id
                         inner join waiver on signature.form_name = waiver.slug) as p;


          alter table signature
              rename column form_name to form_name_old;
          alter table signature
              rename column target_id to target_id_old;

          alter table signature
              add column participant_waiver_id uuid references participant_waiver;
          --           alter table signature
--               add column target text;

          update signature
          set participant_waiver_id = (select participant_waiver.id
                                       from participant_waiver
                                                inner join waiver on waiver.id = participant_waiver.waiver_id
                                       where participant_waiver.participant_id = signature.target_id_old
                                         and waiver.slug = signature.form_name_old)
--               target    = 'participant_waiver'
          where signature.participant_waiver_id is null;

          delete
          from signature
          where signature.participant_waiver_id is null;

          --           select *
--           from signature
--                    inner join entity_action on entity_action.entity_id = signature.target_id_old
--           where signature.participant_waiver_id is null
--             and action_name = 'delete';

          alter table signature
              alter column participant_waiver_id set not null;
          --           alter table signature
--               alter column target_id set not null;

          alter table signature
              alter column target_id_old drop not null;
          alter table signature
              alter column form_name_old drop not null;


          alter table participant_waiver
              add column medical_yes_answers         text[],
              add column medical_evaluation_required bool default false;



          update participant_waiver
          set medical_yes_answers = (select array_agg(answer.question)
                                     from answer
                                              inner join participant on participant.id = answer.target_id
                                              inner join waiver on waiver.id = participant_waiver.waiver_id
                                     where participant_waiver.participant_id = participant.id
                                       and answer.form_name = waiver.slug
                                       and answer.answer = 'yes')
          where participant_waiver.medical_yes_answers is null;

          alter table answer
              rename to answer_old;


-- select * from waiver where id = 'ccb77b4a-e40f-4702-a1f3-3f8c38283c05';

--           select *
--           from participant_waiver
--           where array_length(participant_waiver.medical_yes_answers, 1) > 0;
--           select participant_waiver.id,
--                  (select array_agg(answer.question)
--                   from answer
--                            inner join participant on participant.id = answer.target_id
--                            inner join waiver on waiver.id = participant_waiver.waiver_id
--                   where participant_waiver.participant_id = participant.id
--                     and answer.form_name = waiver.slug
--                     and answer.answer = 'yes') as yesses
--           from participant_waiver;
-- 
--           select distinct answer.form_name
--           from answer;
--           select distinct answer.target_id
--           from answer
--                    inner join participant on participant.id = answer.target_id
--           where answer.answer = 'yes';


--  set medical_evaluation_required migration


          update "participant_waiver"
          set "medical_evaluation_required" = exists (select "arr"."question"
                                                      from json_populate_recordset(null::record,
                                                                                   '[{"question":"q_0_0","parent":"q_0","childs":0},{"question":"q_0_1","parent":"q_0","childs":0},{"question":"q_0_2","parent":"q_0","childs":0},{"question":"q_0_3","parent":"q_0","childs":0},{"question":"q_0_4","parent":"q_0","childs":0},{"question":"q_1_0","parent":"q_1","childs":0},{"question":"q_1_1","parent":"q_1","childs":0},{"question":"q_1_2","parent":"q_1","childs":0},{"question":"q_1_3","parent":"q_1","childs":0},{"question":"q_2","parent":null,"childs":0},{"question":"q_3_0","parent":"q_3","childs":0},{"question":"q_3_1","parent":"q_3","childs":0},{"question":"q_3_2","parent":"q_3","childs":0},{"question":"q_3_3","parent":"q_3","childs":0},{"question":"q_4","parent":null,"childs":0},{"question":"q_5_0","parent":"q_5","childs":0},{"question":"q_5_1","parent":"q_5","childs":0},{"question":"q_5_2","parent":"q_5","childs":0},{"question":"q_5_3","parent":"q_5","childs":0},{"question":"q_5_4","parent":"q_5","childs":0},{"question":"q_6_0","parent":"q_6","childs":0},{"question":"q_6_1","parent":"q_6","childs":0},{"question":"q_6_2","parent":"q_6","childs":0},{"question":"q_6_3","parent":"q_6","childs":0},{"question":"q_7_0","parent":"q_7","childs":0},{"question":"q_7_1","parent":"q_7","childs":0},{"question":"q_7_2","parent":"q_7","childs":0},{"question":"q_7_3","parent":"q_7","childs":0},{"question":"q_7_4","parent":"q_7","childs":0},{"question":"q_8_0","parent":"q_8","childs":0},{"question":"q_8_1","parent":"q_8","childs":0},{"question":"q_8_2","parent":"q_8","childs":0},{"question":"q_8_3","parent":"q_8","childs":0},{"question":"q_8_4","parent":"q_8","childs":0},{"question":"q_8_5","parent":"q_8","childs":0},{"question":"q_9","parent":null,"childs":0}]') as arr(question text, parent text, childs integer)
                                                      where ("arr"."parent" is null or not exists (select
                                                                                                   from "answer_old"
                                                                                                   where "answer_old"."form_name" = 'medical'
                                                                                                     and "answer_old"."target_id" = "participant_waiver"."participant_id"
                                                                                                     and "answer_old"."answer" = 'no'
                                                                                                     and "answer_old"."question" = "arr"."parent"))
                                                      except
                                                      select "answer_old"."question"
                                                      from "answer_old"
                                                      where "answer_old"."form_name" = 'medical'
                                                        and "answer_old"."target_id" = "participant_waiver"."participant_id"
                                                        and "answer_old"."answer" = 'no'
                                                      limit 1)
          where "participant_waiver".waiver_id in (select waiver.id from waiver where waiver.medical = true);

          select *
          from participant_waiver
          where participant_waiver.medical_evaluation_required = true;
          select *
          from participant_waiver
          where participant_waiver.medical_evaluation_required = false;



          --------- first thought about change answer table get moved it to the participant_waiver table as text[] column medical_yes_answers

--           alter table answer
--               add column participant_waiver_id uuid references participant_waiver;
-- 
--           update answer
--           set participant_waiver_id = (select participant_waiver.id
--                                        from participant_waiver
--                                                 inner join waiver on waiver.id = participant_waiver.waiver_id
--                                        where waiver.medical = true
--                                          and participant_waiver.participant_id = answer.target_id)
--           where answer.participant_waiver_id is null;
-- 
--           alter table answer
--               drop constraint answer_target_id_form_name_question_key;
-- 
--           alter table answer
--               add unique (participant_waiver_id, question);
-- 
--           alter table answer
--               rename column target_id to target_id_old;
--           alter table answer
--               rename column form_name to form_name_old;
--           alter table answer
--               alter column target_id_old drop not null;
--           alter table answer
--               alter column form_name_old drop not null;
--           alter table answer
--               alter column participant_waiver_id set not null;


          -- -------- though about having soft delete on waiver, but we can get it from the audit if data needs to be retrieved ------

--           alter table waiver
--               add column deleted_at                 timestamptz default 'infinity' not null,
--               add column deleted_by_user_session_id uuid references user_session;
--           alter table waiver
--               drop constraint waiver_establishment_id_slug_key;
-- 
--           alter table waiver
--               add unique (deleted_at, establishment_id, slug);
-- 
--           alter table waiver_translation
--               add column deleted_at                 timestamptz default 'infinity' not null,
--               add column deleted_by_user_session_id uuid references user_session;
-- 
--           alter table waiver_translation
--               drop constraint waiver_translation_waiver_id_language_code_key;
-- 
--           alter table waiver_translation
--               add unique (deleted_at, waiver_id, language_code);


--       alter table form
--           rename to journey;


--       changed again ----
--           alter table form
--               add column medical boolean default false;
-- 
--           update form
--           set medical = true
--           where form.forms @> array ['medical'];
-- 
--           create table form_waiver
--           (
--               id        uuid primary key default gen_random_uuid(),
--               form_id   uuid references form   not null,
--               waiver_id uuid references waiver not null,
--               unique (form_id, waiver_id)
--           );
-- 
--           insert into form_waiver (form_id, waiver_id)
--           select form.id, waiver.id
--           from waiver
--                    inner join form on form.forms @> array [waiver.slug]
--           where waiver.establishment_id is null;
-- 
--           create table participant_waiver
--           (
--               id                   uuid primary key default gen_random_uuid(),
--               participant_id       uuid references participant not null,
--               waiver_id            uuid references waiver      not null,
--               signed_language_code text,
--               unique (participant_id, waiver_id)
--           );
-- 
--           insert into participant_waiver (participant_id, waiver_id, signed_language_code)
--           select participant.id, waiver.id, 'EN'
--           from signature
--                    inner join participant on signature.target_id = participant.id
--                    inner join waiver on signature.form_name = waiver.slug
--           where signature.signed_by_representative = false;
-- 
-- 
-- 
--           alter table signature
--               rename column form_name to form_name_old;
--           alter table signature
--               rename column target_id to target_id_old;
-- 
--           alter table signature
--               add column target_id uuid;
--           alter table signature
--               add column target text;
-- 
--           update signature
--           set target_id = signature.target_id_old,
--               target    = 'participant_medical'
--           where signature.target_old = 'medical';
-- 
--           update signature
--           set target_id = (select participant_waiver.id
--                            from participant_waiver
--                                     inner join waiver on waiver.id = participant_waiver.waiver_id
--                            where participant_waiver.participant_id = signature.target_id_old
--                              and waiver.slug = signature.form_name_old),
--               target    = 'participant_waiver'
--           where signature.target is null;
-- 
--           alter table signature
--               alter column target set not null;
--           alter table signature
--               alter column target_id set not null;
-- 
--           alter table signature
--               alter column target_id_old drop not null;
--           alter table signature
--               alter column form_name_old drop not null;



      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
