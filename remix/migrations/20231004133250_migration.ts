import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table operator add column enterprise bool default false;
alter table establishment add column short varchar(2), add unique (operator_id, short);
-- alter table 

`,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
