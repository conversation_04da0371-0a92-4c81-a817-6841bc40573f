import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      ALTER TABLE invoice ADD COLUMN invoice_local_date DATE;
      
      UPDATE invoice
      SET invoice_local_date = (
        SELECT (invoice.created_at AT TIME ZONE region.timezone)::date
        FROM booking
        INNER JOIN establishment ON establishment.id = booking.establishment_id
        INNER JOIN spot ON spot.id = establishment.spot_id
        INNER JOIN region ON region.id = spot.region_id
        WHERE booking.id = invoice.booking_id
      )
      WHERE invoice.invoice_local_date IS NULL;

      ALTER TABLE invoice ALTER COLUMN invoice_local_date SET NOT NULL;
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
