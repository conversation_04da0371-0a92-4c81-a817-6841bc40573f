import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          -- if not exist because the table AND one row with the api key should be filled on production before the migration  is executed
          create table if not exists xendit_environment
          (
              id             uuid primary key not null default gen_random_uuid(),
              name           text             not null unique,
              production     bool             not null default false,
              xendit_api_key text             not null,
              created_at     timestamptz      not null default now(),
              unique (name, production)
          );

          create table xendit_split_rule
          (
              id                    uuid primary key not null default gen_random_uuid(),
              xendit_split_rule_id  text             not null unique,
              xendit_response       jsonb            not null,
              xendit_environment_id uuid             not null references xendit_environment,
              created_at            timestamptz      not null default now()
          );

          alter table xendit_account
              add column xendit_environment_id uuid references xendit_environment,
              drop column xendit_api_key_id;

          update xendit_account
          set xendit_environment_id = (select xendit_environment.id
                                       from xendit_environment
                                       order by xendit_environment.created_at asc
                                       limit 1);

          alter table xendit_account
              alter column xendit_environment_id set not null;


          alter table xendit_split_rule
              add column active bool;
          alter table xendit_split_rule
              add unique (xendit_environment_id, active);
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
