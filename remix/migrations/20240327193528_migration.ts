import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table waiver alter column markdoc set not null ;
alter table waiver add column name text not null ;
alter table waiver add unique (root_id, language_code);

`,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
