import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
          alter table product
              alter column created_at drop not null,
              alter column created_by drop not null;

          alter table product__diving_course
              alter column created_at drop not null,
              alter column created_by drop not null;

          alter table activity
              alter column created_at drop not null,
              alter column created_by drop not null;

          alter table country
              alter column created_at drop not null,
              alter column created_by drop not null;

          alter table region
              alter column created_at drop not null,
              alter column created_by drop not null;

          alter table spot
              alter column created_at drop not null,
              alter column created_by drop not null;

          alter table operator_location
              alter column created_at drop not null,
              alter column created_by drop not null;

          alter table operator
              alter column created_at drop not null,
              alter column created_by drop not null;

          alter table owner
              alter column created_at drop not null,
              alter column created_by drop not null;

          create or replace function set_price_usd() returns trigger
              language plpgsql
          as
          $$
          begin
              select currency.conversion_rate_usd / new.price
              from currency
              where currency.id = new.price_currency
              limit 1
              into new.price_usd;
              return new;
          end;
          $$;

          create or replace trigger set_price_usd
              before insert or update
              on product
              for each row
          execute function set_price_usd()
      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
