import { Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
          alter table mutation_event
              alter column target_id type text;
          alter table product
              drop constraint product_activity_id_fk;

          alter table product
              add constraint product_activity_id_fk
                  foreign key (activity_id) references activity
                      on update cascade on delete restrict;

          alter table activity
              add column slug text;
          update activity
          set slug = id
          where id is not null;
          alter table activity
              alter column slug set not null;
          update activity
          set id = gen_random_uuid();
          drop trigger set_node on activity;
          update activity
          set slug = 'diving-course'
          where slug = 'diving_course_product';
          update activity
          set slug = 'fun-diving'
          where slug = 'diving_product';
          update activity
          set slug = 'snorkeling'
          where slug = 'snorkeling_product';
          alter table activity
              add constraint slug_unique unique (slug);
          delete
          from mutation_event
          where table_name = 'activity';
          alter table mutation_event
              alter column target_id type uuid using target_id::uuid;
          alter table activity
              rename column files to files_json;
          alter table activity
              add column files text[];
          update activity
          set files = array(select jsonb_array_elements_text(files_json));

          drop trigger if exists set_node on diving_certificate_organization;
          drop trigger if exists set_node on diving_course;
          drop trigger if exists set_node on diving_location;
          drop trigger if exists set_node on diving_site;
          drop trigger if exists set_node on diving_type;
          drop trigger if exists set_node on event;
          drop trigger if exists set_node on node_user;
          drop trigger if exists set_node on operator;
          drop trigger if exists set_node on operator_location;
          drop trigger if exists set_node on owner;
          drop trigger if exists set_node on product;
          drop trigger if exists set_node on product__diving_course;
          drop trigger if exists set_node on product__diving_location;
          drop trigger if exists set_node on product__diving_site;
          drop trigger if exists set_node on product__diving_type;
          drop trigger if exists set_node on product__snorkel_location;
          drop trigger if exists set_node on product__snorkel_site;
          drop trigger if exists set_node on region;
          drop trigger if exists set_node on review;
          drop trigger if exists set_node on session;
          drop trigger if exists set_node on spatial_ref_sys;
          drop trigger if exists set_node on spot;
          drop trigger if exists set_node on tag;
          drop trigger if exists set_node on "user";
          drop trigger if exists set_node on user_session;
          drop trigger if exists set_node on vote;

          alter table diving_site
              rename column files to files_json;
          alter table diving_site
              add column files text[];
          update diving_site
          set files = array(select jsonb_array_elements_text(files_json));

          alter table operator_location
              rename column files to files_json;
          alter table operator_location
              add column files text[];
          update operator_location
          set files = array(select jsonb_array_elements_text(files_json));

          alter table product
              rename column files to files_json;
          alter table product
              add column files text[];
          update product
          set files = array(select jsonb_array_elements_text(files_json));

          alter table region
              rename column files to files_json;
          alter table region
              add column files text[];
          update region
          set files = array(select jsonb_array_elements_text(files_json));

          alter table review
              rename column files to files_json;
          alter table review
              add column files text[];
          update review
          set files = array(select jsonb_array_elements_text(files_json));

          alter table spot
              rename column files to files_json;
          alter table spot
              add column files text[];
          update spot
          set files = array(select jsonb_array_elements_text(files_json));



      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
