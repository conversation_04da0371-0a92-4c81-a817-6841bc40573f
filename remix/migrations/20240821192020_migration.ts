import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          delete
          from intuit_connection;
          alter table intuit_connection
              add column intuit_company_info jsonb not null,
              add column intuit_user_info    jsonb not null,
              drop column user_email,
              drop column company_name;

          alter table intuit_connection
              rename column intuit_company_info to intuit_company;
          alter table intuit_connection
              rename column intuit_user_info to intuit_user;

          alter table intuit_connection
              add column sandbox bool not null default true;

          --           alter table booking
--               add column intuit_invoice_id text;

          alter table invoice
              add primary key (id);

          create table intuit_invoice
          (
              id                uuid primary key default gen_random_uuid(),
              created_at        timestamptz      default now() not null,
              realm_id          text                           not null,
              intuit_invoice_id text                           not null,
              invoice_id        uuid references invoice unique not null,
              response          jsonb                          not null,
              unique (realm_id, intuit_invoice_id),
              unique (invoice_id)
          );
          --           create table intuit_customer
--           (
--               id                 uuid primary key default gen_random_uuid(),
--               created_at         timestamptz      default now(),
--               realm_id           text  not null,
--               intuit_customer_id text  not null,
--               customer_id        uuid references customer unique,
--               response           jsonb not null,
--               unique (realm_id, customer_id),
--               unique (customer_id)
--           );

          alter table product
              add column sku text,
              add unique (deleted_at, establishment_id, sku);

          --           create table connection
--           (
--               id          uuid primary key default gen_random_uuid(),
--               created_at  timestamptz      default now(),
--               type        text  not null,
--               internal_id uuid  not null,
--               external_id text  not null,
--               response    jsonb not null,
--               meta        jsonb not null,
--           )
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
