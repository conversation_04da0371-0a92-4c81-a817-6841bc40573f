import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          select booking.id, booking.cached_duration, lower(booking.cached_duration)
          from booking
          where (select count(*) from activity where activity.booking_id = booking.id) > 1
          order by lower(booking.cached_duration)
          limit 10;

          create index booking_establishment_id_cached_duration_lower_idx on booking (establishment_id, lower(cached_duration));
          select lower(datemultirange('[2024-02-01, 2024-03-01)', '[2024-01-01, 2024-04-15)'));
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
