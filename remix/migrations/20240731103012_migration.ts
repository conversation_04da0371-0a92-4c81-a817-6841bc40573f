import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table participant_waiver
              add column signature_required bool;

          update participant_waiver
          set signature_required = (select participant_waiver.signature_required
                                    from waiver
                                    where waiver.id = participant_waiver.waiver_id)
          where signature_required is null;

          alter table participant_waiver
              alter column signature_required drop not null;

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
