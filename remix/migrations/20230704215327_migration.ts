import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table participant
              add column stay text;

          alter table participant
              rename column weight_in_kg to weight_value;
          alter table participant
              add column weight_metric text;
          update participant
          set weight_metric = 'kg'
          where weight_value is not null;

          alter table participant
              rename column height_in_cm to height_value;
          alter table participant
              add column height_metric text;
          update participant
          set height_metric = 'cm'
          where height_value is not null;

          alter table participant
              rename column shoe_size to shoe_size_value;
          alter table participant
              add column shoe_size_metric text;
          update participant
          set shoe_size_metric = 'eu'
          where shoe_size_value is not null;


          alter table participant
              rename column weightbelt_in_kg to weightbelt_value;
          alter table participant
              add column weightbelt_metric text;
          update participant
          set weightbelt_metric = 'kg'
          where weightbelt_value is not null;

          alter table participant
              rename column weightbelt_metric to weightbelt_unit;
          alter table participant
              rename column weight_metric to weight_unit;
          alter table participant
              rename column height_metric to height_unit;
          alter table participant
              rename column shoe_size_metric to shoe_size_unit;

          alter table participant
              rename column number_of_dives to number_of_dives_old;
          alter table participant
              add column number_of_dives int4range;
          update participant
          set number_of_dives = int4range(participant.number_of_dives_old, participant.number_of_dives_old, '[]')
          where participant.number_of_dives_old is not null;
          alter table participant
              drop column number_of_dives_old;

          select *
          from participant
          where number_of_dives is not null;

          select '[1,)'::int4range, int4range(1, 9, '[]');
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
