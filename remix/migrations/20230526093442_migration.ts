import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table location_schedule rename to trip;

alter table booking_location_schedule rename to trip_booking;
alter table trip_booking rename column location_schedule_id to trip_id;
alter table location_user_schedule rename to trip_assignment;
alter table trip_assignment rename column location_schedule_id to trip_id;
alter table operator_location__user rename to member;
alter table trip_assignment rename column operator_location__user_id to member_id;
alter table user_schedule rename column operator_location__user_id to member_id;
-- alter table trip rename 
`
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
