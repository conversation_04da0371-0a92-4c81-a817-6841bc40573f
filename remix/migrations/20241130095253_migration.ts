import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table participant_waiver
              alter column participant_id drop not null;

          alter table participant_waiver
              drop constraint participant_waiver_participant_id_fkey;

          alter table participant_waiver
              add foreign key (participant_id) references participant
                  on delete set null;
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
