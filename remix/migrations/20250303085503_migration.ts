import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

alter table form
    drop constraint form_target_id_deleted_at_type_name_key;

alter table form
    add constraint form_target_id_deleted_at_type_name_key
        unique (establishment_id, deleted_at, type, name);

alter table form
    drop constraint form_deleted_at_target_id_name_key;

alter table form
    add constraint form_deleted_at_target_id_name_key
        unique (deleted_at, establishment_id, name);

alter table form drop column type;
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
