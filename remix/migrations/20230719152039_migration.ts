import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table participant
              rename column tshirt_size to wetsuit_size;
          alter table participant
              add column experience_in_years          int,
              add column own_gear                     bool,
              add column referral_source              text,
              add column allow_contact_for_experience bool;
alter table participant drop own_gear;
alter table participant add column my_gear text[];
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
