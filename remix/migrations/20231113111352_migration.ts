import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table file
              drop column sort_order,
              drop column target_id,
              drop column target;
          alter table file
              add column public boolean default false;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
