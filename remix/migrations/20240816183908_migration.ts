import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      create table intuit_connection
      (
          id                         uuid primary key                      default gen_random_uuid(),
          created_at                 timestamptz                  not null default now(),
          created_by_user_session_id uuid references user_session not null,
          realm_id                   text unique                  not null,
          refresh_token              text                         not null,
          access_token               text                         not null,
          company_name               text                         not null,
          user_email                 text                         not null
      );

      alter table establishment
          add column intuit_connection_id uuid references intuit_connection;
  `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
