import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `

          create or replace function set_price_usd() returns trigger
              language plpgsql
          as
          $$
          begin
              select coalesce(nullif(new.price, 0) / currency.conversion_rate_usd, 0)
              from currency
              where currency.id = new.price_currency
              limit 1
              into new.price_usd;
              return new;
          end;
          $$;


          alter table product
              add column activity_slug text;

          create index product_activity_slug_idx on product (activity_slug);

          alter table review
              add column activity_slug text;

          create index review_activity_slug_idx on review (activity_slug);

          update product
          set activity_slug = (select activity.slug from activity where product.activity_id = activity.id)
          where activity_slug is null;

          update review
          set activity_slug = (select activity.slug from activity where review.activity_id = activity.id)
          where activity_slug is null;

          alter table product
              alter column activity_slug set not null,
              drop column activity_id;

          alter table review
              alter column activity_slug set not null,
              drop column activity_id;

          drop table activity;

          --       select *
--       from currency;
--       select product.id,
--              product.price,
--              product.price_usd,
--              product.price_currency,
--              nullif(product.price, 0),
--              (select coalesce(nullif(product.price, 0) / currency.conversion_rate_usd, 0)
--               from currency
--               where currency.id = product.price_currency
--               limit 1) as calculated_price
--       from product
--       limit 10;

          create table boatshare
          (
              id                 uuid primary key default gen_random_uuid() not null,
              description        text                                       not null,
              operator_location_id uuid references operator_location (id)       not null,
              duration           tstzrange                                  not null,
              has_boat           bool                                       not null
          );

          create table boatshare_match
          (
              id          uuid primary key default gen_random_uuid() not null,
              operator_location_id uuid references operator_location (id)       not null,
              boatshare_id   uuid references boatshare (id)             not null,
              accepted    bool,
              description text
          );

          alter table boatshare_match
              drop constraint boatshare_match_boatshare_id_fkey;

          alter table boatshare_match
              add foreign key (boatshare_id) references boatshare
                  on delete cascade;



          --       create table boat
--       (
--           id                   uuid primary key default gen_random_uuid() not null,
--           name                 text                                       not null,
--           files                text[],
--           operator_location_id uuid references operator_location (id)     not null
--       );
-- 
--       create table boatshare_offering
--       (
--           id                 uuid primary key default gen_random_uuid() not null,
--           description        text                                       not null,
--           boat_id            uuid references boat (id)                  not null,
--           diving_location_id uuid references diving_location (id)       not null,
--           duration           tstzrange                                  not null
--       );
-- 
--       create table boatshare_match
--       (
--           boatshare_offering_id uuid references boatshare_offering (id) not null,
--           boatshare_request_id  uuid references boatshare_offering (id) not null,
--           accepted              bool default false
--       );
-- 
--       create table boatshare_request
--       (
--           id                   uuid primary key default gen_random_uuid() not null,
--           description          text,
--           operator_location_id uuid references operator_location (id),
--           duration           tstzrange                                  not null
--       );
-- 
--       create table comment
--       (
--           id        uuid primary key default gen_random_uuid() not null,
--           target_id uuid                                       not null,
-- 
--       )
      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
