import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
-- al<PERSON><PERSON> ran on production and test, only left here for git

--           update participant_waiver
--           set created_at = user_event.created_at
--           from entity_action
--                    inner join user_event on entity_action.user_event_id = user_event.id
--           where entity_action.entity_id = participant_waiver.id
--             and user_event.created_at != participant_waiver.created_at
--             and entity_action.action_name = 'insert';
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
