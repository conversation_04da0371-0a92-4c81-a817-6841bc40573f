import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table activity
              alter column duration drop not null,
              add column description text;

      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
