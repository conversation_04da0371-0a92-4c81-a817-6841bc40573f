import { Kysely } from "kysely";
import type { ActivitySlug } from "~/domain/activity/activity";
import { v4 } from "uuid";

type FieldsConfig = Record<string, [show: boolean, required: boolean, check: boolean]>;

type DefaultTemplate = {
  name: string;
  fields: FieldsConfig;
  activity_slug?: ActivitySlug;
  beginner_diver?: boolean | undefined;
};

export const baseFieldsConfig: FieldsConfig = {
  first_name: [true, true, true],
  last_name: [true, true, true],
  address: [true, false, true],
  country_code: [true, false, true],
  passport_number: [false, false, true],
  stay: [true, true, true],
  birth_date: [true, true, true],
  phone: [true, true, true],
  diet: [true, false, true],
  food_allergies: [true, false, true],
  insurance: [true, false, true],
  emergency_contact_name: [true, true, true],
  emergency_contact_phone: [true, true, true],
  emergency_contact_relationship: [true, false, true],
  comment: [true, false, true],
};

const certificateFields: FieldsConfig = {
  diving_certificate_level: [true, false, true],
  diving_certificate_number: [true, false, true],
  diving_certificate_organization: [true, false, true],
  years_of_experience: [true, false, true],
  last_dived_at: [true, true, true],
  number_of_dives: [true, true, true],
};

const feedbackFields: FieldsConfig = {
  referral_source: [true, false, false],
  allow_contact_for_experience: [true, false, false],
};

const sizingFields: FieldsConfig = {
  weightbelt_value: [true, false, true],
  height_value: [true, false, true],
  weight_value: [true, false, true],
  wetsuit_size: [true, false, true],
  shoe_size_value: [true, false, true],
  my_gear: [true, false, false],
};

export const templates: DefaultTemplate[] = [
  {
    name: "Fun diving",
    fields: { ...baseFieldsConfig, ...certificateFields, ...sizingFields, ...feedbackFields },
    activity_slug: "fun-diving",
  },
  {
    name: "Diving course, beginners",
    fields: { ...baseFieldsConfig, ...sizingFields, ...feedbackFields },
    activity_slug: "diving-course",
    beginner_diver: true,
  },
  {
    name: "Diving course, advanced",
    fields: { ...baseFieldsConfig, ...certificateFields, ...sizingFields, ...feedbackFields },
    activity_slug: "diving-course",
  },
  {
    name: "Snorkeling",
    fields: { ...baseFieldsConfig, ...feedbackFields },
    activity_slug: "snorkeling",
  },
  {
    name: "Freediving",
    fields: { ...baseFieldsConfig, ...feedbackFields },
    activity_slug: "freediving",
  },
  {
    name: "Basic",
    fields: { ...baseFieldsConfig, ...feedbackFields },
  },
];

export async function up(db: Kysely<any>): Promise<void> {
  for (const template of templates) {
    const formId = v4();
    const result = await db
      .insertInto("form")
      .values({
        id: formId,
        root_id: formId,
        type: "registration",
        name: template.name,
        filter_activity_slug: template.activity_slug,
        filter_beginner_diver: template.beginner_diver || false,
        selectable: false,
      })
      .returningAll()
      .executeTakeFirstOrThrow();

    const fields = Object.entries(template.fields);
    await db
      .insertInto("field")
      .values(
        fields.map(([fieldName, [show, required, check]]) => ({
          id: v4(),
          name: fieldName,
          required: required,
          show: show,
          form_id: formId,
        })),
      )
      .executeTakeFirstOrThrow();
  }
}

export async function down(): Promise<void> {
  // Migration code
}
