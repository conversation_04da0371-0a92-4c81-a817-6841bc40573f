import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table product add column published boolean;
update product set published = true;
alter table product alter column published set not null ;

alter table booking drop column product_title, drop column diving_location;
delete from booking where product_id is null;
alter table booking alter column product_id set not null ;

-- alter table booking drop column di
alter table booking add column meeting_location text;
`
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
