import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      alter table member
          alter column old_first_name drop not null,
          alter column old_last_name drop not null;`,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
