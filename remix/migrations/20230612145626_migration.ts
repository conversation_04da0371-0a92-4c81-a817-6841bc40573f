import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table user_schedule
              alter column range set default tstzrange(null, null, '[)');

alter table user_schedule drop constraint user_schedule_member_id_fkey;
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
