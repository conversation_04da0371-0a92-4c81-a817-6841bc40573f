import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table participant
              add column cache_stale                   bool default true not null,
              add column cache_signature_waivers_valid bool default false;

-- select * from participant where participant.cache_stale = true;
-- select participant.cache_signature_waivers_valid, participant.cache_stale from participant where id = '78afe004-f882-4bca-abf5-01071cd67e83'
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
