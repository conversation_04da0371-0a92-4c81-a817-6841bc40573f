import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table booking
              add column reviewed_at timestamptz default 'infinity',
              add column reviewed_by uuid references user_session (id),
              add column accepted    bool        default true;

          alter table establishment
              add column direct_booking_mode int2 not null default 0;


          update establishment
          set direct_booking_mode = 2
          where establishment.direct_booking_enabled = true;

          alter table establishment
              drop column direct_booking_enabled;

          create table device_token
          (

              id         uuid primary key default gen_random_uuid(),
              device_id  text        not null unique,
              token      text        not null unique,
              created_at timestamptz not null,
              updated_at timestamptz not null
          );

          create table user__device_token
          (

              id              uuid primary key default gen_random_uuid(),
              user_id         uuid references "user"       not null,
              device_token_id uuid references device_token not null
          );


      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
