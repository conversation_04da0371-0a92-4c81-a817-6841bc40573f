import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
-- select floor(EXTRACT(EPOCH FROM (participant_token.created_at - now())) / 60) + 30 as remaining_minutes, * from participant_token;
-- select encode(gen_random_bytes(32), 'base64');
          drop table if exists participant_token;
   create table participant_token (
       id uuid primary key default gen_random_uuid(),
       created_at timestamptz not null default now(),
       created_by_user_session_id uuid references user_session not null,
       participant_id uuid references participant not null,
       token char(64) not null unique default encode(gen_random_bytes(32), 'hex')
   )
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
