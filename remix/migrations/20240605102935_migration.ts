import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

          CREATE TABLE address
          (
              id                          uuid PRIMARY KEY default gen_random_uuid(), -- Auto-incrementing integer for unique IDs
              place_id                    VARCHAR(255) unique,                        -- Google Places ID
              place_details_response      jsonb,
              formatted_address           TEXT,                                       -- Full address
              street_number               VARCHAR(50),                                -- Optional
              route                       VARCHAR(255),                               -- Optional
              sublocality                 VARCHAR(255),                               -- Optional
              locality                    VARCHAR(255),                               -- Optional (city)
              administrative_area_level_1 VARCHAR(255),                               -- Optional (state/province)
              administrative_area_level_2 VARCHAR(255),                               -- Optional (county)
              postal_code                 VARCHAR(20),                                -- Optional (ZIP/postal code)
              country_code                char(2) NOT NULL,                           -- ISO 3166-1 alpha-2 country code
              latitude                    NUMERIC(10, 7),                             -- Latitude with precision
              longitude                   NUMERIC(10, 7),                             -- Longitude with precision
              types                       TEXT[]                                      -- Array for storing place types (e.g., 'street_address')
          );

          create unique index on address (postal_code, street_number, route, country_code) nulls not distinct where (place_id is null);
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
