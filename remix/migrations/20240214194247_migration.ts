import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
-- alter table callback
update callback set host = '' where callback.host is null;
alter table callback alter host set not null ;
`,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
