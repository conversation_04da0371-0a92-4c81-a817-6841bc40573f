import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
drop table if exists one_time_password;
          create table one_time_password
          (
              id         uuid primary key     default gen_random_uuid(),
              created_at timestamptz not null default now(),
              user_session_id uuid references user_session not null ,
              password   char(6)     not null
          );
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
