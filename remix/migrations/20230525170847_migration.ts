import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table location_schedule add column max_participants int4;
alter table location_schedule rename column max_participants to max_no_participants;
`
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
