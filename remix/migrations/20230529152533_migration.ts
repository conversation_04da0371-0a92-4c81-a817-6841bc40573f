import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table booking add column booking_reference text;
alter table booking_registration rename column last_dived_at to last_dived_at_old;
alter table booking_registration add column last_dived_at date;
update booking_registration set last_dived_at = last_dived_at_old::date where last_dived_at_old is not null;
alter table booking_registration drop column last_dived_at_old;
alter table booking_registration add column paid bool default false;
alter table booking add column paid bool default false;

-- SELECT date_part('year', age('2021-01-01', '1990-05-15')) AS age_in_years;
`
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
