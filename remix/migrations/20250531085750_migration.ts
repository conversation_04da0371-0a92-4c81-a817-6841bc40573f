import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
        -- Your SQL statements here
        alter table establishment
          add column activity_reminder_in_days_before_start int2;
        --         alter table establishment
--           add column incomplete_registration_reminder_in_hours_before_start int2;

        drop table if exists mail;
        create table mail
        (
          id                         uuid primary key                          default gen_random_uuid(),
          created_at                 timestamptz                      not null default now(),
          created_by_user_session_id uuid references user_session (id),
          participant_id             uuid references participant (id) not null,
          activity_id                uuid references activity (id)    not null,
          to_email                   text                             not null,
          to_name                    text                             not null,
          from_name                  text                             not null,
--           template_name              text                             not null,
          success                    boolean,
          msg                        text
        );

        update "user"
        set admin = true
        where "user".email = '<EMAIL>'
          and "user".deleted_at = 'infinity';

        alter table booking
          add column host text;

--         select lower(activity.duration),
--                lower(activity.duration) + coalesce(booking.meeting_time, '00:00'),
--                ((lower(activity.duration) + coalesce(booking.meeting_time, '00:00')) at time zone
--                 region.timezone)::timestamptz,
--                ((lower(activity.duration) + coalesce(booking.meeting_time, '00:00')) at time zone
--                 region.timezone)::timestamp at time zone 'utc',
--                activity.duration,
--                region.timezone
--         from activity
--                inner join booking on activity.booking_id = booking.id
--                inner join establishment on booking.establishment_id = establishment.id
--                inner join spot on establishment.spot_id = spot.id
--                inner join region on spot.region_id = region.id
--         where activity.duration is not null
--           and booking.meeting_time is null;

      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
