import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
      alter table diving_site
          drop constraint diving_site_difficulty_level_check;

      alter table diving_site
          add constraint diving_site_difficulty_level_check
              check ((difficulty_level >= 1) AND (difficulty_level <= 4));


  `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
}
