import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `

        alter table mail
          add column establishment_id uuid references establishment;

        update mail
        set establishment_id = (select booking.establishment_id
                                from booking
                                       inner join sale_item on sale_item.booking_id = booking.id
                                where sale_item.id = mail.sale_item_id);

        alter table mail
          alter column establishment_id set not null;

        alter table mail
          alter column sale_item_id drop not null;

        alter table mail
          drop constraint mail_participant_id_fkey;

        alter table mail
          add foreign key (participant_id) references participant
            on delete set null;

        alter table mail
          drop constraint mail_activity_id_fkey;

        alter table mail
          add constraint mail_activity_id_fkey
            foreign key (sale_item_id) references sale_item
              on delete set null;

      `
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `
    )
    .execute(db);
}
