import type { Kysely } from "kysely";
import { sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          drop table booking__boat_departure;
          create table booking_dayschedule
          (
              id              uuid        not null primary key,
              booking_id      uuid references booking,
              start_datetime  timestamptz not null,
              boat_id         uuid references boat,
              shore           bool,
              diving_location text,
              instructor_id   uuid references operator_location__user
          );
          alter table booking
              add column diving_location text;

          alter table operator_location__language
              add column id uuid primary key default gen_random_uuid();

          alter table boat_departure
              add column diving_location text,
              drop column diving_location_id,
              alter column boat_id set not null;


      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
