import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table location_user_schedule
              add column group_name text;
alter table location_user_schedule add column created_at timestamptz;
alter table location_user_schedule alter column created_at set default now();
      `
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
