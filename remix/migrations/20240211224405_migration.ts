import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table operator
              add column slug text unique;

          alter table session
              add column features text[];

          alter table form
              add column selectable bool default false not null;

          alter table participant
              alter column booking_id drop not null;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
