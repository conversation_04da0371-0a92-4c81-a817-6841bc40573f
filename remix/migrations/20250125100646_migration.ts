import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table session
              add flash_success_message text;

alter table field add column status int2 not null default 0;
update field set status = 1 where field.show = true;
update field set status = 2 where field.show = true and field.required = true;

alter table field rename column show to show_old;
alter table field alter column show_old drop not null ;

alter table field rename column required to required_old;
alter table field alter column required_old drop not null ;

      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
