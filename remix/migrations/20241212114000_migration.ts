import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
alter table establishment alter column default_booking_meeting_time set default '08:00';
alter table establishment alter column default_trip_start_time set default '10:00';
   update establishment set default_booking_meeting_time = '08:00' where default_booking_meeting_time is null;
update establishment set default_trip_start_time = '10:00' where establishment.default_trip_start_time is null;
      `,
    )
    .execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
