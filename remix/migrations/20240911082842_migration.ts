import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          alter table establishment
              add column direct_booking_form_root_id uuid references form;

alter table establishment add column direct_booking_enabled bool default false;
update establishment set direct_booking_enabled = true where direct_booking > 0;
alter table establishment drop column direct_booking;
alter table establishment alter column direct_booking_enabled set not null ;
alter table establishment add column planning_enabled bool not null default true;
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
