import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
  //language=postgresql
  await sql
    .raw(
      `
          create index on participant (form_id);
          create index on activity (form_id);
          create index on participation (participant_id);
      `,
    )
    .execute(db);
}

export async function down(): Promise<void> {
  // Migration code
}
