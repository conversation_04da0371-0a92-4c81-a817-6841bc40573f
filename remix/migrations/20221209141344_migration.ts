import { Kys<PERSON>, sql } from "kysely";

export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
  // Migration code
  //language=sql
  await sql
    .raw(
      `
          alter table user_session
              add column verified_at timestamptz;

          update user_session
          set verified_at = '-infinity'
          where verified = true;

          alter table user_session
              drop column verified;

          alter table review
              add column files text[];
      `
    )
    .execute(db);
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  // Migration code
}
