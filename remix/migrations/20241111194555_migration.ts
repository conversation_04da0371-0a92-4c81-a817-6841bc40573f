import { Kysely, sql } from "kysely";

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  await sql
    .raw(
      `
     alter table participant rename column booking_id to booking_id_old;
      `,
    )
    .execute(db);
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  await sql
    .raw(
      `
      -- Reversion SQL statements here
      `,
    )
    .execute(db);
}
