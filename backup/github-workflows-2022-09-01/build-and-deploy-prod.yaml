name: Build and deploy

on:
  push:
    branches: [ master, test ]

env:
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_KEY }}
  DOMAIN: traveltruster.com

defaults:
  run:
    shell: bash

jobs:

  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '14'

      - uses: mdgreenwald/mozilla-sops-action@v1.1.0
        with:
          version: 'v3.7.1'
      - run: bash ${GITHUB_WORKSPACE}/cicd/scripts/setup-env.sh

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ env.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: start deployment
        run: doctl app create-deployment ${{ env.DIGITALOCEAN_APP_ID }} --wait
