name: Setup infra

on:
  push:
    branches: [ master, test ]
    paths:
      - 'infrastructure/**'

env:
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_KEY }}

defaults:
  run:
    shell: bash

jobs:
  infra:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - uses: mdgreenwald/mozilla-sops-action@v1
      - run: bash ${GITHUB_WORKSPACE}/cicd/scripts/setup-env.sh

      - name: Install Pulumi CLI
        uses: pulumi/action-install-pulumi-cli@v1.0.1

      - name: deploy infra
        working-directory: 'infrastructure'
        if: ${{ env.BRANCH == env.ENVIRONMENT}}
        run: |
          npm install
          pulumi login
          pulumi stack select $ENVIRONMENT
          pulumi up --yes
