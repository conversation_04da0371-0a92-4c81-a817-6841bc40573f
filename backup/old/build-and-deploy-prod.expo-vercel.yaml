name: Build and deploy prod

on:
  push:
    branches: [ ]

env:
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_KEY }}
  VERCEL_ORG_ID: team_BchgmbQy31ONW3ssmuKhUX0p
  VERCEL_PROJECT_ID: prj_58Say7XRG5hlZVhV6b0jgzrJhXVO
  DOMAIN: traveltruster.com

defaults:
  run:
    shell: bash

jobs:

  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '14'

      - uses: mdgreenwald/mozilla-sops-action@v1.1.0
        with:
          version: 'v3.7.1'
      - run: bash ${GITHUB_WORKSPACE}/cicd/scripts/setup-env.sh

      - name: install vercel cli
        run: yarn global add vercel

      - id: vercel_deployment
        name: build en deploy to vercel
        working-directory: 'remix'
        run: |
          DEPLOYMENT_URL=$(vercel --token ${{ env.VERCEL_TOKEN }} \
            -e HASURA_GRAPHQL_ADMIN_SECRET=${{ env.HASURA_GRAPHQL_ADMIN_SECRET }} \
            -e ENVIRONMENT=${{ env.ENVIRONMENT }} \
            -e SESSION_SECRET=${{ env.SESSION_SECRET }} \
            -e API_KEY=${{ env.API_KEY }})
          echo "::set-output name=url::$DEPLOYMENT_URL"

      - name: alias to api
        run: |
          vercel --token ${{ env.VERCEL_TOKEN }} --scope dinkelworks alias ${{ steps.vercel_deployment.outputs.url }} api-${{ env.ENVIRONMENT }}.${{ env.DOMAIN }}

      - name: run hasura migrations
        working-directory: 'hasura'
        env:
          HASURA_GRAPHQL_ENDPOINT: https://hasura-${{ env.ENVIRONMENT }}.${{ env.DOMAIN }}
        run: |
          npm install
          npm run apply:metadata
          npm run apply:migrations
          npm run reload:metadata

      - name: alias to environment
        run: vercel --token ${{ env.VERCEL_TOKEN }} --scope dinkelworks alias ${{ steps.vercel_deployment.outputs.url }} ${{ env.ENVIRONMENT }}.${{ env.DOMAIN }}

      - name: alias to root domain if environment is master
        if: ${{ env.ENVIRONMENT == 'master' }}
        run: vercel --token ${{ env.VERCEL_TOKEN }} --scope dinkelworks alias ${{ steps.vercel_deployment.outputs.url }} ${{ env.DOMAIN }}

  build-and-publish-app:
    if: ${{ false }}  # disable for now
    runs-on: ubuntu-latest
    needs: [ build-and-deploy ]
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: 14.x
      #          cache: 'yarn'

      - uses: mdgreenwald/mozilla-sops-action@v1
      - run: bash ${GITHUB_WORKSPACE}/cicd/scripts/setup-env.sh

      - uses: expo/expo-github-action@v6
        with:
          expo-version: 5.x
          expo-cache: true
          token: ${{ env.EXPO_TOKEN }}

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        working-directory: 'expo'
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - uses: actions/cache@v2
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - working-directory: 'expo'
        env:
          NODE_OPTIONS: '--max_old_space_size=4096'
        run: |
          yarn install
          expo publish
