# fly.toml app configuration file generated for dinkelbrowserless-rough-cloud-5663-bitter-leaf-3663 on 2024-01-09T16:09:42+01:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = "dinkelbrowserless-rough-cloud-5663-bitter-leaf-3663"
primary_region = "sin"

[build]

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[[services]]
  protocol = "tcp"
  internal_port = 8080

  [[services.ports]]
    port = 80
    handlers = ["http"]

  [[services.ports]]
    port = 443
    handlers = ["tls", "http"]
  [services.concurrency]
    hard_limit = 25
    soft_limit = 20

  [[services.tcp_checks]]
    interval = "10s"
    timeout = "2s"

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 1024
