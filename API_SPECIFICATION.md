# Form-Based API Specification

## OpenAPI 3.0 Specification

```yaml
openapi: 3.0.0
info:
  title: Form-Based API
  description: A custom form-based API system for database operations
  version: 1.0.0
  contact:
    name: Development Team
servers:
  - url: /
    description: Default server

paths:
  /resource:
    post:
      summary: Submit form data for database operations
      description: |
        Accepts form-encoded data and performs database operations based on the form structure.
        Supports complex nested operations, references, and type transformations.
      operationId: submitFormData
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                # Special fields
                redirect:
                  type: string
                  description: Success redirect URL
                redirect_error:
                  type: string
                  description: Error redirect URL
                identifier:
                  type: string
                  description: Form identifier for state management
                response_identifier:
                  type: string
                  description: Response identifier for tracking
                captcha_token:
                  type: string
                  description: Google reCAPTCHA token for bot prevention
                start:
                  type: string
                  format: date-time
                  description: Form start timestamp for bot prevention
                website:
                  type: string
                  description: Honeypot field for bot prevention
              additionalProperties:
                type: string
                description: |
                  Dynamic form fields following the pattern:
                  {table}.{index}.{field}.{subfield} = {value}
                  {table}.{index}.operation = {insert|update|delete|ignore}
                  {table}.{index}.data.{field} = {__field_type__}
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                    items:
                      $ref: "#/components/schemas/ResourceResult"
                  init:
                    $ref: "#/components/schemas/SessionInit"
        "302":
          description: Redirect response
          headers:
            Location:
              schema:
                type: string
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

components:
  schemas:
    ResourceResult:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Entity ID
        entity_name:
          type: string
          description: Database table name
        operation:
          type: string
          enum: [insert, update, delete, ignore]
          description: Operation performed
        result:
          oneOf:
            - type: string
              format: uuid
            - type: boolean
          description: Operation result (UUID for success, false for failure)
        auditInsert:
          $ref: "#/components/schemas/AuditInsert"
      required:
        - id
        - entity_name
        - operation
        - result

    AuditInsert:
      type: object
      properties:
        data:
          type: object
          description: Audit data
        entity_name:
          type: string
          description: Entity name
        entity_id:
          type: string
          format: uuid
          description: Entity ID
        action_name:
          type: string
          enum: [insert, update, delete]
          description: Action performed

    SessionInit:
      type: object
      properties:
        session_id:
          type: string
          format: uuid
          description: Session ID

    ErrorResponse:
      type: object
      properties:
        response_error:
          type: string
          description: Error message
        response_identifier:
          type: string
          description: Response identifier
        response_form_id:
          type: string
          description: Form ID

    # Database table schemas
    Participant:
      type: object
      properties:
        id:
          type: string
          format: uuid
        data:
          type: object
          properties:
            first_name:
              type: string
              maxLength: 255
            last_name:
              type: string
              maxLength: 255
            phone:
              type: string
            email:
              type: string
              format: email
            birth_date:
              type: string
              format: date
            country_code:
              type: string
              maxLength: 2
            address:
              type: string
            emergency_contact_name:
              type: string
            emergency_contact_phone:
              type: string
            emergency_contact_relationship:
              type: string
            stay:
              type: string
            room:
              type: string
            passport_number:
              type: string
            diet:
              type: string
            food_allergies:
              type: string
            insurance:
              type: string
            height_value:
              type: number
            height_unit:
              type: string
            weight_value:
              type: number
            weight_unit:
              type: string
            shoe_size_value:
              type: number
            shoe_size_unit:
              type: string
            bcd_size:
              type: string
            years_of_experience:
              type: string
            diving_certificate_organization:
              type: string
            diving_certificate_level:
              type: string
            diving_certificate_number:
              type: string
            number_of_dives:
              type: string
            last_dive_within_months:
              type: number
            wetsuit_size:
              type: string
            weightbelt_value:
              type: number
            weightbelt_unit:
              type: string
            my_gear:
              type: array
              items:
                type: string
            referral_source:
              type: string
            allow_contact_for_experience:
              type: boolean
            instagram:
              type: string
            comment:
              type: string
        operation:
          type: string
          enum: [insert, update, delete, ignore]

    Booking:
      type: object
      properties:
        id:
          type: string
          format: uuid
        data:
          type: object
          properties:
            date:
              type: string
              format: date
            establishment_id:
              type: string
              format: uuid
            currency_id:
              type: string
            message:
              type: string
            meeting_type:
              type: string
            meeting_address:
              type: string
            meeting_time:
              type: string
            cart_for_session_id:
              type: string
              format: uuid
            internal_note:
              type: string
            booking_source:
              type: string
            hide_price_for_customer:
              type: boolean
        operation:
          type: string
          enum: [insert, update, delete, ignore]

    Product:
      type: object
      properties:
        id:
          type: string
          format: uuid
        data:
          type: object
          properties:
            name:
              type: string
            description:
              type: string
            external_identifier:
              type: string
            sku:
              type: string
            stock:
              type: number
            published:
              type: boolean
            diving_course_level:
              type: string
            diving_count:
              type: number
            inclusions:
              type: string
            exclusions:
              type: string
            diving_type:
              type: string
            guide_pax_max:
              type: number
            guide_pax_min:
              type: number
            itinerary:
              type: string
            pickup:
              type: boolean
            pickup_info:
              type: string
            boat_dive:
              type: boolean
            shore_dive:
              type: boolean
            night_dive:
              type: boolean
            gear_included:
              type: boolean
            stay:
              type: boolean
            stay_info:
              type: string
            required_diving_certificate:
              type: string
            elearning_included:
              type: boolean
            required_experience:
              type: string
            duration_in_hours:
              type: string
            n_sessions:
              type: number
            color:
              type: string
            size:
              type: string
        operation:
          type: string
          enum: [insert, update, delete, ignore]

    Establishment:
      type: object
      properties:
        id:
          type: string
          format: uuid
        data:
          type: object
          properties:
            short:
              type: string
            review_enabled:
              type: boolean
            direct_booking_mode:
              type: number
            direct_booking_form_root_id:
              type: string
              format: uuid
            workflow:
              type: number
            vat_number:
              type: string
            geom:
              type: string
            address:
              type: string
            internal_notes:
              type: string
            telephone:
              type: string
            email:
              type: string
              format: email
            notification_email:
              type: string
              format: email
            whatsapp:
              type: string
            website:
              type: string
            location_name:
              type: string
            default_currency:
              type: string
            spot_id:
              type: string
              format: uuid
            order:
              type: number
            bio:
              type: string
            published:
              type: boolean
            about:
              type: string
            require_email_verification_for_signing:
              type: boolean
            default_weight_unit:
              type: string
            default_shoe_size_unit:
              type: string
            default_height_unit:
              type: string
            booking_message_templates:
              type: array
              items:
                type: string
            whatsapp_message_template:
              type: string
            default_trip_start_time:
              type: string
            default_booking_meeting_time:
              type: string
            default_booking_meeting_type:
              type: string
            locale:
              type: string
            activity_reminder_in_days_before_start:
              type: number
        operation:
          type: string
          enum: [insert, update, delete, ignore]

    # Field types
    FieldTypes:
      type: object
      properties:
        __boolean__:
          type: string
          description: Converts string values to boolean
        __pg_int_range__:
          type: string
          description: Creates PostgreSQL integer ranges
        __pg_coordinates__:
          type: string
          description: Creates PostGIS Point objects
        __pg_daterange__:
          type: string
          description: Creates PostgreSQL date ranges
        __sum__:
          type: string
          description: Sums array values
        __join__:
          type: string
          description: Joins array values into a string
        __to_string__:
          type: string
          description: Converts objects to JSON strings
        __empty_array__:
          type: string
          description: Ensures empty arrays are properly handled

  securitySchemes:
    sessionAuth:
      type: apiKey
      in: cookie
      name: session_id
      description: Session-based authentication
    captchaAuth:
      type: apiKey
      in: formData
      name: captcha_token
      description: Google reCAPTCHA token for bot prevention

security:
  - sessionAuth: []
  - captchaAuth: []
```

## Field Naming Patterns

### Basic Pattern

```
{table}.{index}.{field}.{subfield} = {value}
```

### Examples

```
participant.0.data.first_name = "John"
participant.0.data.last_name = "Doe"
participant.0.operation = "insert"
booking.1.id = "uuid-123"
booking.1.data.date = "2024-01-01"
booking.1.operation = "update"
```

### Reference Pattern

```
{table}.{index}.data.{foreign_key_field} = "ref-{table}-{index}"
```

### Examples

```
participant.0.id = "ref-participant-0"
booking.0.data.participant_id = "ref-participant-0"
```

## Field Type Transformations

### Boolean Fields

```html
<input type="checkbox" name="participant.0.data.active" value="true" />
<input type="hidden" name="participant.0.data.active" value="__boolean__" />
```

### Date Ranges

```html
<input type="date" name="booking.0.data.date_range.from" value="2024-01-01" />
<input type="date" name="booking.0.data.date_range.to" value="2024-01-07" />
<input type="hidden" name="booking.0.data.date_range" value="__pg_daterange__" />
```

### Integer Ranges

```html
<input type="number" name="diving_site.0.data.depth_range.from" value="5" />
<input type="number" name="diving_site.0.data.depth_range.to" value="30" />
<input type="hidden" name="diving_site.0.data.depth_range" value="__pg_int_range__" />
```

### Coordinates

```html
<input type="text" name="address.0.data.coordinates" value="40.7128,-74.0060" />
<input type="hidden" name="address.0.data.coordinates" value="__pg_coordinates__" />
```

### Arrays

```html
<input type="text" name="participant.0.data.my_gear.0" value="BCD" />
<input type="text" name="participant.0.data.my_gear.1" value="Regulator" />
<input type="hidden" name="participant.0.data.my_gear" value="__empty_array__" />
```

## Authorization Rules

### Table Authorization Matrix

| Table                                 | Insert | Update | Delete | Authorization Rule         |
| ------------------------------------- | ------ | ------ | ------ | -------------------------- |
| `participant`                         | ✅     | ✅     | ✅     | Establishment admin        |
| `booking`                             | ✅     | ✅     | ✅     | Establishment admin        |
| `product`                             | ✅     | ✅     | ✅     | Establishment admin        |
| `establishment`                       | ✅     | ✅     | ✅     | Editor                     |
| `user`                                | ✅     | ✅     | ✅     | Editor                     |
| `operator`                            | ✅     | ✅     | ✅     | Editor                     |
| `region`                              | ✅     | ✅     | ✅     | Editor                     |
| `spot`                                | ✅     | ✅     | ✅     | Editor                     |
| `diving_location`                     | ✅     | ✅     | ✅     | Editor                     |
| `diving_site`                         | ✅     | ✅     | ✅     | Editor                     |
| `diving_course`                       | ✅     | ✅     | ✅     | Editor                     |
| `tag`                                 | ✅     | ✅     | ✅     | Establishment admin        |
| `category`                            | ✅     | ✅     | ✅     | Establishment admin        |
| `price`                               | ✅     | ✅     | ✅     | Establishment admin        |
| `addon`                               | ✅     | ✅     | ✅     | Establishment admin        |
| `member`                              | ✅     | ✅     | ✅     | Establishment admin        |
| `activity`                            | ✅     | ✅     | ✅     | Establishment admin        |
| `activity_addon`                      | ✅     | ✅     | ✅     | Establishment admin        |
| `person`                              | ✅     | ✅     | ✅     | Establishment admin        |
| `customer`                            | ✅     | ✅     | ✅     | Establishment admin        |
| `participation`                       | ✅     | ✅     | ✅     | Establishment admin        |
| `participation_addon`                 | ✅     | ✅     | ✅     | Establishment admin        |
| `waiver`                              | ✅     | ✅     | ✅     | Establishment admin        |
| `waiver_translation`                  | ✅     | ✅     | ✅     | Establishment admin        |
| `participant_waiver`                  | ✅     | ✅     | ✅     | Establishment admin        |
| `participation_waiver`                | ✅     | ✅     | ✅     | Establishment admin        |
| `signature`                           | ✅     | ✅     | ✅     | Establishment admin        |
| `comment`                             | ✅     | ✅     | ✅     | Establishment admin        |
| `review`                              | ✅     | ✅     | ✅     | Public (with restrictions) |
| `inquiry`                             | ✅     | ✅     | ✅     | Public                     |
| `signup_submission`                   | ✅     | ❌     | ❌     | Public                     |
| `callback`                            | ✅     | ❌     | ❌     | System                     |
| `file`                                | ✅     | ✅     | ✅     | Establishment admin        |
| `file_target`                         | ✅     | ✅     | ✅     | Establishment admin        |
| `payment_method`                      | ✅     | ✅     | ✅     | Establishment admin        |
| `payment`                             | ✅     | ✅     | ✅     | Establishment admin        |
| `trip`                                | ✅     | ✅     | ✅     | Establishment admin        |
| `trip_assignment`                     | ✅     | ✅     | ✅     | Establishment admin        |
| `tank_assignment`                     | ✅     | ✅     | ✅     | Establishment admin        |
| `boat`                                | ✅     | ✅     | ✅     | Establishment admin        |
| `schedule`                            | ✅     | ✅     | ✅     | Establishment admin        |
| `rentable`                            | ✅     | ✅     | ✅     | Establishment admin        |
| `rental_assignment`                   | ✅     | ✅     | ✅     | Establishment admin        |
| `view`                                | ✅     | ✅     | ✅     | Establishment admin        |
| `form`                                | ✅     | ✅     | ✅     | Establishment admin        |
| `field`                               | ✅     | ✅     | ✅     | Establishment admin        |
| `form_waiver`                         | ✅     | ✅     | ✅     | Establishment admin        |
| `session_link`                        | ✅     | ✅     | ✅     | System                     |
| `mail`                                | ✅     | ❌     | ✅     | System                     |
| `user_session`                        | ✅     | ✅     | ✅     | System                     |
| `one_time_password`                   | ✅     | ✅     | ✅     | System                     |
| `intuit_connection`                   | ✅     | ✅     | ✅     | Establishment admin        |
| `xendit_platform`                     | ❌     | ❌     | ❌     | Not allowed                |
| `xendit_environment`                  | ✅     | ✅     | ✅     | System                     |
| `xendit_account`                      | ✅     | ✅     | ✅     | System                     |
| `xendit_split_rule`                   | ✅     | ✅     | ✅     | System                     |
| `xendit_virtual_bank_account`         | ✅     | ✅     | ✅     | System                     |
| `xendit_virtual_bank_account_payment` | ✅     | ✅     | ✅     | System                     |

## Error Codes

### Authorization Errors

- `unauthorized for {operation} on {table}` - User lacks permission
- `no_changes` - No operations were performed

### Validation Errors

- `could not parse {field}` - Field parsing failed
- `invalid {field_type}` - Invalid field type

### Business Logic Errors

- `{custom_error_message}` - Custom business rule violations

## Rate Limiting

The API includes basic bot prevention:

- Forms must be submitted at least 3 seconds after start
- Honeypot field (`website`) must remain empty
- reCAPTCHA validation for public endpoints

## Audit Trail

All operations are automatically audited with:

- User session information
- IP address and headers
- Before/after data snapshots
- Operation type and timestamp

## Transaction Safety

All operations in a single request are wrapped in a database transaction, ensuring:

- Atomicity: All operations succeed or fail together
- Consistency: Database remains in a valid state
- Isolation: Concurrent requests don't interfere
- Durability: Changes are permanently saved
